<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotChatHistory">
    <option name="conversations">
      <list>
        <Conversation>
          <option name="createTime" value="1753060302093" />
          <option name="id" value="01982a895d0d762e90b09a8f3cfbf983" />
          <option name="title" value="新对话 2025年7月21日 09:11:42" />
          <option name="updateTime" value="1753060302093" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1752973433704" />
          <option name="id" value="0198255bdb68737eac0258fcd5319af9" />
          <option name="title" value="新对话 2025年7月20日 09:03:53" />
          <option name="updateTime" value="1752973433704" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1752902190216" />
          <option name="id" value="0198211cc4887f0abdfe14cb425b66a0" />
          <option name="title" value="新对话 2025年7月19日 13:16:30" />
          <option name="updateTime" value="1752902190216" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1752338610257" />
          <option name="id" value="0197ff8538517b589e224d0ceaa47378" />
          <option name="title" value="新对话 2025年7月13日 00:43:30" />
          <option name="updateTime" value="1752338610257" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1752066423642" />
          <option name="id" value="0197ef4bfb5a7f30a77d250dec823f62" />
          <option name="title" value="新对话 2025年7月09日 21:07:03" />
          <option name="updateTime" value="1752066423642" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1751269489673" />
          <option name="id" value="0197bfcbbc01777e84ce6625693938bc" />
          <option name="title" value="@workspace /fix /fix 修复报错:&#10;java.lang.NoClassDefFou" />
          <option name="turns">
            <list>
              <Turn>
                <option name="agent" value="workspace" />
                <option name="answer" value="" />
                <option name="chatResource" value="user" />
                <option name="codeReferences">
                  <list>
                    <CodeReferencesBean>
                      <option name="content" value="&#9;&#9;&#9;&#9;&#9;&#9;FmTArk&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;fmTArk = mvd FmTArk();&#10;&#9;&#9;&#9;&#9;&#9;&#9;rhFmArkuozt = fmTArk.fmTArkDrgsYfu(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;mvd Kzgs(hlfixvUrov.glHgirmt()), mvd Kzgs(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;wzgzUrov.glHgirmt()).tvgMznv());&#10;&#9;&#9;&#9;&#9;&#9;} vohv {&#10;&#9;&#9;&#9;&#9;&#9;&#9;rhFmArkuozt = gifv;&#10;&#9;&#9;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;&#9;&#9;ru (rhFmArkuozt) {&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;NxGzyovGy  hlfixvGzyov  = nxGzyovWzl.tvgGzyovYbRwWzl(Rmgvtvi.kzihvRmg(qlyKzizn&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.tvgHlfixv_gzyov_rw()));&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;Hgirmt hlfixvGzyovMznv = hlfixvGzyov.tvgGzyovMznv();&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;wzgzHlfixv = wzgzHlfixvWZL.urmwYbRw(Rmgvtvi.ezofvLu(hlfixvGzyov.tvgWhRw()));&#10;&#9;&#9;&#9;&#9;&#9;&#9;ru(wzgzHlfixv == mfoo){&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;该任务没有找到对应的数据源配置信息&quot;);&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;ivgfim mvd IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV, QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv(),&quot;根据表RW没有找到对应的数据源配置信息&quot;);&#10;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;yllovzm gzyovVcrhg = ywh.jfvibGzyovVcrhg(hlfixvGzyovMznv);&#10;" />
                      <option name="end_line" value="153" />
                      <option name="filename" value="DataImpService.java" />
                      <option name="language" value="JAVA" />
                      <option name="range">
                        <Range>
                          <option name="end">
                            <Position>
                              <option name="line" value="153" />
                            </Position>
                          </option>
                          <option name="start">
                            <Position>
                              <option name="line" value="129" />
                            </Position>
                          </option>
                        </Range>
                      </option>
                      <option name="start_line" value="129" />
                      <option name="type" value="editor" />
                      <option name="url" value="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/ETL/SunETLExecute/src/main/java/com/sunyard/etl/tms/DataImpService.java" />
                    </CodeReferencesBean>
                  </list>
                </option>
                <option name="command" value="fix" />
                <option name="conversationId" value="0197bfcbbc01777e84ce6625693938bc" />
                <option name="historyDocumentMap">
                  <map>
                    <entry key="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/ETL/SunETLExecute/src/main/java/com/sunyard/etl/tms/DataImpService.java" value="package com.sunyard.etl.tms;&#10;&#10;&#10;import com.sun.java.swing.plaf.windows.WindowsInternalFrameTitlePane.ScalableIconUIResource;&#10;import com.sunyard.etl.system.common.Constants;&#10;import com.sunyard.etl.system.dao.*;&#10;import com.sunyard.etl.system.dao.impl.*;&#10;import com.sunyard.etl.system.model.DataSource;&#10;import com.sunyard.etl.system.model.JobParam;&#10;import com.sunyard.etl.system.model.McTableTb;&#10;import com.sunyard.etl.system.util.EscapeChar;&#10;import com.sunyard.etl.tms.service.TableDataOper;&#10;import com.xxl.job.core.biz.model.ReturnT;&#10;import com.xxl.job.core.enums.JobStartEnum;&#10;import com.xxl.job.core.handler.IJobHandler;&#10;import com.xxl.job.core.handler.annotation.JobHandler;&#10;import com.xxl.job.core.log.XxlJobLogger;&#10;&#10;import org.springframework.stereotype.Service;&#10;import org.sunyard.model.Path;&#10;import org.sunyard.util.dbutil.DBHandler;&#10;import org.sunyard.util.string.StringUtil;&#10;import org.sunyard.util.zip.UnGZip;&#10;&#10;import java.io.BufferedReader;&#10;import java.io.BufferedWriter;&#10;import java.io.File;&#10;import java.io.FileInputStream;&#10;import java.io.FileNotFoundException;&#10;import java.io.FileOutputStream;&#10;import java.io.IOException;&#10;import java.sql.SQLException;&#10;import java.util.ArrayList;&#10;import java.util.List;&#10;import java.util.zip.GZIPOutputStream;&#10;&#10;&#10;/**&#10; * 文本数据导入&#10; * <AUTHOR> *&#10; */&#10;&#10;@JobHandler(value = &quot;DataImpService&quot;,name=&quot;文本数据导处理&quot;)&#10;@Service&#10;public class DataImpService extends IJobHandler {&#10;&#9;/**&#10;&#9; *&#10;&#9; */&#10;&#9;private static final long serialVersionUID = 1L;&#10;&#10;&#10;&#9;private static JobParamDao jobParamDao = new JobParamDaoImpl();&#10;&#9;private static McTableDao mcTableDao = new McTableDaoImpl();&#10;&#9;private static BusiDateService bds = new BusiDateServiceImpl();&#10;&#9;private static DataDateDAO dateDao = new DataDateDAOImpl();&#10;&#9;private static  TableDefDAO tableDefDAO = new TableDefDAOImpl();&#10;&#9;private static DataSourceDAO dataSourceDAO = new DataSourceDAOImpl();&#10;&#10;&#10;&#9;public ReturnT&lt;String&gt; execute(String taskid,String... params) throws Exception {&#10;&#10;//&#9;&#9;int flag = AgileConstants.TASK_STATE_HAS_HANDLEOUT;&#10;&#10;&#9;&#9;JobParam jobParam = jobParamDao.JobParam(Integer.parseInt(taskid));&#10;&#10;&#9;&#9;if (null == jobParam) {&#10;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;任务:&quot;+taskid+&quot;找不到对应的参数表信息,无法执行&quot;);&#10;&#9;&#9;&#9;return new  ReturnT&lt;String&gt;(ReturnT.FAIL_CODE,JobStartEnum.TASK_CONFIG_ERROR.getCode(),&quot;找不到对应的参数表信息,无法执行&quot;);&#10;&#9;&#9;} else {&#10;&#9;&#9;&#9;String jobDate = dateDao.getDataDate();&#10;&#9;&#9;&#9;File flagFile = null;&#10;&#9;&#9;&#9;File dataFile = null;&#10;&#9;&#9;&#9;File sourceFile = null;&#10;&#9;&#9;&#9;try {&#10;&#9;&#9;&#9;&#9;flagFile = new File((jobParam.getTxt_load_file_path().trim()&#10;&#9;&#9;&#9;&#9;&#9;&#9;+ File.separator&#10;&#9;&#9;&#9;&#9;&#9;&#9;+ jobParam.getTxt_load_flag_file_name().toString().trim())&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.replaceAll(&quot;@&quot;, jobDate));&#10;&#9;&#9;&#9;&#9;dataFile = new File((jobParam.getTxt_load_file_path().trim()&#10;&#9;&#9;&#9;&#9;&#9;&#9;+ File.separator&#10;&#9;&#9;&#9;&#9;&#9;&#9;+ jobParam.getTxt_data_load_file_name().toString().trim())&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.replaceAll(&quot;@&quot;, jobDate));&#10;&#9;&#9;&#9;&#9;sourceFile = new File((jobParam.getTxt_load_file_path().trim()&#10;&#9;&#9;&#9;&#9;&#9;&#9;+ File.separator&#10;&#9;&#9;&#9;&#9;&#9;&#9;+ jobParam.getTxt_load_file_name().toString().trim())&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.replaceAll(&quot;@&quot;, jobDate));&#10;&#9;&#9;&#9;} catch (Exception e) {&#10;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;获取任务文件配置出现问题,请检查该任务的参数配置&quot;, taskid);&#10;&#9;&#9;&#9;&#9;return new  ReturnT&lt;String&gt;(ReturnT.FAIL_CODE,JobStartEnum.TASK_CONFIG_ERROR.getCode(),&quot;找不到对应的参数表信息,无法执行&quot;);&#10;&#10;&#9;&#9;&#9;}&#10;&#10;&#10;&#10;&#9;&#9;&#9;//对参数进行验证&#10;&#9;&#9;&#9;DataSource dataSource = null;&#10;&#9;&#9;&#9;try {&#10;&#9;&#9;&#9;&#9;if (StringUtil.checkNull(jobParam.getSource_table_id())) {&#10;&#9;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;该任务没有找到对应的数据源配置信息&quot;);&#10;&#9;&#9;&#9;&#9;&#9;return new ReturnT&lt;String&gt;(ReturnT.FAIL_CODE, JobStartEnum.TASK_CONFIG_ERROR.getCode(), JobStartEnum&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.getName(JobStartEnum.TASK_CONFIG_ERROR.getCode()));&#10;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;}catch (Exception e){&#10;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;解析数据源配置信息发生异常&quot;+e);&#10;&#9;&#9;&#9;&#9;e.printStackTrace();&#10;&#9;&#9;&#9;&#9;return new ReturnT&lt;String&gt;(ReturnT.FAIL_CODE, JobStartEnum.TASK_CONFIG_ERROR.getCode(), JobStartEnum&#10;&#9;&#9;&#9;&#9;&#9;&#9;.getName(JobStartEnum.TASK_CONFIG_ERROR.getCode()));&#10;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;//导数之前先清除历史文件信息&#10;//&#9;&#9;&#9;FileSystem fs = new FileSystem();&#10;//&#9;&#9;&#9;fs.setMaxdays(new Integer(Constants.localdays));&#10;//&#9;&#9;&#9;fs.setMaxSize(Constants.localsize);&#10;//&#9;&#9;&#9;fs.setRoot(Constants.FTP_DOWNLOAD_ROOT_PATH);&#10;//&#9;&#9;&#9;fs.clearHis(); //wangkuiyang&#10;&#10;&#10;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;标志文件FlagFile:---&quot; + flagFile.getPath(), taskid);&#10;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;数据文件dataFile:---&quot; + dataFile.getPath(), taskid);&#10;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;源数据文件File:---&quot; + sourceFile.getPath(), taskid);&#10;&#10;&#9;&#9;&#9;&#9;if (flagFile.exists()) {&#10;&#9;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;【标志文件存在，准备进行数据导入操作】&quot;, taskid);&#10;&#9;&#9;&#9;&#9;&#9;boolean isUnZipflag = true;&#10;&#9;&#9;&#9;&#9;&#9;// TODO 文件存在判断是否需要解压&#10;&#9;&#9;&#9;&#9;&#9;if (&quot;1&quot;.equals(jobParam.getIs_unzip())) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;INFO: 开始解压文件&quot; + sourceFile.toString()&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;+ &quot;到&quot; + dataFile.toString());&#10;&#9;&#9;&#9;&#9;&#9;&#9;UnGZip&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;unGZip = new UnGZip();&#10;&#9;&#9;&#9;&#9;&#9;&#9;isUnZipflag = unGZip.unGZipWithBuf(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;new Path(sourceFile.toString()), new Path(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;dataFile.toString()).getName());&#10;&#9;&#9;&#9;&#9;&#9;} else {&#10;&#9;&#9;&#9;&#9;&#9;&#9;isUnZipflag = true;&#10;&#9;&#9;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;&#9;&#9;if (isUnZipflag) {&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;McTableTb  sourceTable  = mcTableDao.getTableByIdDao(Integer.parseInt(jobParam&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.getSource_table_id()));&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;String sourceTableName = sourceTable.getTableName();&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;dataSource = dataSourceDAO.findById(Integer.valueOf(sourceTable.getDsId()));&#10;&#9;&#9;&#9;&#9;&#9;&#9;if(dataSource == null){&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;该任务没有找到对应的数据源配置信息&quot;);&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;return new ReturnT&lt;String&gt;(ReturnT.FAIL_CODE, JobStartEnum.TASK_CONFIG_ERROR.getCode(),&quot;根据表ID没有找到对应的数据源配置信息&quot;);&#10;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;boolean tableExist = bds.queryTableExist(sourceTableName);&#10;&#9;&#9;&#9;&#9;&#9;&#9;// 不能找到文件对应的表&#10;&#9;&#9;&#9;&#9;&#9;&#9;if (!tableExist) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;找不到任务对应的表&quot; + sourceTableName,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;taskid + &quot;&quot;);&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;return new ReturnT&lt;String&gt;(ReturnT.FAIL_CODE, JobStartEnum.TASK_CONFIG_ERROR.getCode(), &quot;找不到任务对应的表&quot; + sourceTableName);&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;&#9;&#9;&#9;// 将压缩数据导入表中&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;bds.truncateTable(sourceTableName);&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;TableDataOper to = new TableDataOper();&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;/*** 导数据分隔符转化 start ***/&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;String splitChar = jobParam.getSplit_mark();&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;String splitStr = &quot;&quot;;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;String splitStrCHar = &quot;&quot;;&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;if (splitChar.length() &gt; 1) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;for (int i = 0; i &lt; splitChar.length(); i++) {&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;char[] sc = splitChar.toCharArray();&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;splitStrCHar += &quot;\\&quot; + sc[i];&#10;&#10;&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;} else if (splitChar.length() == 1) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;if (EscapeChar.ESCAPE_CHAR_SET&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.contains(splitChar)) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;splitStrCHar = &quot;\\&quot; + splitChar;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;} else {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;splitStrCHar = splitChar;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;//splitStr = splitStrCHar.toString().trim();&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;splitStr = splitStrCHar.toString();&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;/** 导数据分隔符转化 end **/&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;//获取数据导入开始行&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;int startLine = jobParam.getStart_line() == null?1:Integer.parseInt(jobParam.getStart_line().trim());&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;// 导入数据操作&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;to.impFromFlatFile(sourceTableName.toUpperCase()&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.trim(), new Path(dataFile.getPath()), startLine,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;splitStr, dataSource.getUser());&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;int loadedDataCount = 0;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;loadedDataCount = tableDefDAO.getRowCount(sourceTableName);&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;INFO: 导入记录条数：&quot; + loadedDataCount,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;taskid + &quot;&quot;);&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;if (&quot;BANK_CORD0050&quot;.equals(sourceTableName.toUpperCase().trim()) &amp;&amp; loadedDataCount&gt;0) {//该表导入完以后，需要执行修改虚拟柜员语句20221202hwj&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;this.updateCord0050Teller_temp(jobDate, taskid);//根据左老师的需求，修改CORD0050的自动柜员为实体柜员，关联ACAD0020_temp表。hwj20240327&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;this.updateCord0050Teller(jobDate, taskid);&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;}else if (&quot;BANK_DEPD0020&quot;.equals(sourceTableName.toUpperCase().trim()) &amp;&amp; loadedDataCount&gt;0) {//20240105hwj该表导入完以后，需要 修改DEPD0020的机构号为telm中所属机构号，关联bank_telm表&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;this.updateDEPD0020Teller(jobDate, taskid);&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;&#9;&#9;} else {&#10;&#9;&#9;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;INFO: 解压文件失败&quot;, taskid + &quot;&quot;);&#10;&#9;&#9;&#9;&#9;&#9;&#9;return new ReturnT&lt;String&gt;(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_EXECUTE_ERROR.getCode(), &quot;解压文件失败\&quot;&quot;);&#10;&#9;&#9;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;&#9;} else {&#10;&#9;&#9;&#9;&#9;&#9;XxlJobLogger.log(&quot;INFO: 资源不足：&quot; + flagFile.getPath(), taskid + &quot;&quot;);&#10;&#9;&#9;&#9;&#9;&#9;return new ReturnT&lt;String&gt;(ReturnT.FAIL_CODE, JobStartEnum.TASK_STATE_NO_RESOURCE.getCode(), &quot;标志文件&quot;+flagFile.getPath()+&quot;不存在&quot;);&#10;&#9;&#9;&#9;&#9;}&#10;&#10;&#10;&#9;&#9;}&#10;&#9;&#9;return ReturnT.SUCCESS;&#10;&#9;}&#10;&#10;&#10;&#9;/**&#10;&#9; * 修改CORD0050的自动柜员为实体柜员，关联ACAD0020表&#10;&#9; * @param curDate&#10;&#9; */&#10;&#9;public void updateCord0050Teller(String curDate,String taskid) {&#10;&#9;&#9;DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);&#10;&#9;&#9;List&lt;String&gt; sqlList = new ArrayList&lt;String&gt;() ;&#10;&#9;&#9;sqlList.add(&quot;UPDATE BANK_CORD0050 a SET a.TELLERNO = (select DISTINCT  b.TELLER from BANK_ACAD0020 b where a.FLOWID = b.FLOW_ID and b.GXSIGN = '挂账')  &quot;+&#10;                 &#9;&quot;WHERE EXISTS(SELECT 1 FROM BANK_ACAD0020 c WHERE a.FLOWID = c.FLOW_ID and c.GXSIGN = '挂账' ) &quot;) ;&#10;&#10;&#9;&#9;try {&#10;&#9;&#9;&#9;dbHandler.executeAsBatch(sqlList);&#10;&#9;&#9;&#9;XxlJobLogger.log(&quot;INFO: 关联ACAD0020表,修改CORD0050的自动柜员为实体柜员完毕&quot;, taskid + &quot;&quot;);&#10;&#9;&#9;} catch (SQLException e) {&#10;&#9;&#9;&#9;XxlJobLogger.log(&quot;ERROR: 修改CORD0050的自动柜员为实体柜员语句更新失败&quot;+e, taskid + &quot;&quot;);&#10;&#9;&#9;}&#10;&#9;}&#10;&#10;&#9;/**&#10;&#9; * 根据左老师的需求，修改CORD0050的自动柜员为实体柜员，关联ACAD0020_temp表。hwj20240327&#10;&#9; * @param curDate&#10;&#9; */&#10;&#9;public void updateCord0050Teller_temp(String curDate,String taskid) {&#10;&#9;&#9;DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);&#10;&#9;&#9;List&lt;String&gt; sqlList = new ArrayList&lt;String&gt;() ;&#10;&#9;&#9;sqlList.add(&quot;UPDATE BANK_CORD0050 a SET a.TELLERNO = (select DISTINCT  b.TELLER from BANK_ACAD0020_TEMP b where a.FLOWID = b.FLOW_ID and b.GXSIGN = '票据' and b.workdate='&quot;+curDate+&quot;')  &quot;+&#10;                 &#9;&quot;WHERE EXISTS(SELECT 1 FROM BANK_ACAD0020_TEMP c WHERE a.FLOWID = c.FLOW_ID and c.GXSIGN = '票据' and c.workdate='&quot;+curDate+&quot;') &quot;) ;&#10;&#10;&#9;&#9;try {&#10;&#9;&#9;&#9;dbHandler.executeAsBatch(sqlList);&#10;&#9;&#9;&#9;XxlJobLogger.log(&quot;INFO: 关联BANK_ACAD0020_TEMP表,修改CORD0050的自动柜员为票据实体柜员完毕&quot;, taskid + &quot;&quot;);&#10;&#9;&#9;} catch (SQLException e) {&#10;&#9;&#9;&#9;XxlJobLogger.log(&quot;ERROR: 修改CORD0050的自动柜员为票据实体柜员语句更新失败&quot;+e, taskid + &quot;&quot;);&#10;&#9;&#9;}&#10;&#9;}&#10;&#10;&#9;/**&#10;&#9; * 修改DEPD0020的机构号为telm中所属机构号，关联bank_telm表&#10;&#9; * @param curDate&#10;&#9; */&#10;&#9;public void updateDEPD0020Teller(String curDate,String taskid) {&#10;&#9;&#9;DBHandler dbHandler = new DBHandler(Constants.CONN_POOL_NAME);&#10;&#9;&#9;List&lt;String&gt; sqlList = new ArrayList&lt;String&gt;() ;&#10;&#9;&#9;sqlList.add(&quot;UPDATE BANK_DEPD0020 a set a.brno =(select DISTINCT  b.CW08_BRCH_NO from BANK_TELM b where a.teller1 =(reverse(substr(reverse(b.cw08_teller_no), 1, 7)))) &quot;&#10;&#9;&#9;&#9;&#9;&#9;+&quot; WHERE EXISTS(SELECT 1 FROM BANK_TELM b where a.teller1 =(reverse(substr(reverse(b.cw08_teller_no), 1, 7)) ))&quot;) ;&#10;&#10;&#9;&#9;try {&#10;&#9;&#9;&#9;dbHandler.executeAsBatch(sqlList);&#10;&#9;&#9;&#9;XxlJobLogger.log(&quot;INFO: 关联bank_telm表,修改DEPD0020的机构号为bank_telm中所属机构号完毕&quot;, taskid + &quot;&quot;);&#10;&#9;&#9;} catch (SQLException e) {&#10;&#9;&#9;&#9;XxlJobLogger.log(&quot;ERROR: 修改DEPD0020的机构号为bank_telm中所属机构号更新失败&quot;+e, taskid + &quot;&quot;);&#10;&#9;&#9;}&#10;&#9;}&#10;&#10;&#9;/**&#10;&#9; * 压缩gz包。hwj20220324&#10;&#9; * @param inFileName&#10;&#9; */&#10;&#9;private static void doCompressFile(String inFileName) {&#10;&#10;        try {&#10;&#10;            System.out.println(&quot;Creating the GZIP output stream.&quot;);&#10;            String outFileName = inFileName + &quot;.gz&quot;;&#10;            GZIPOutputStream out = null;&#10;            try {&#10;                out = new GZIPOutputStream(new FileOutputStream(outFileName));&#10;            } catch(FileNotFoundException e) {&#10;                System.err.println(&quot;Could not create file: &quot; + outFileName);&#10;                System.exit(1);&#10;            }&#10;&#10;&#10;            System.out.println(&quot;Opening the input file.&quot;);&#10;            FileInputStream in = null;&#10;            try {&#10;                in = new FileInputStream(inFileName);&#10;            } catch (FileNotFoundException e) {&#10;            System.err.println(&quot;File not found. &quot; + inFileName);&#10;                System.exit(1);&#10;            }&#10;&#10;            System.out.println(&quot;Transfering bytes from input file to GZIP Format.&quot;);&#10;            byte[] buf = new byte[1024];&#10;            int len;&#10;            while((len = in.read(buf)) &gt; 0) {&#10;                out.write(buf, 0, len);&#10;            }&#10;            in.close();&#10;&#10;            System.out.println(&quot;Completing the GZIP file&quot;);&#10;            out.finish();&#10;            out.close();&#10;&#10;        } catch (IOException e) {&#10;            e.printStackTrace();&#10;            System.exit(1);&#10;        }&#10;&#10;    }&#10;&#10;&#10;&#10;&#9;public static void main(String[] args) throws Exception {&#10;&#9;&#9;DataImpService da  = new DataImpService();&#10;&#9;&#9;da.execute(&quot;2&quot;, &quot;1&quot;);&#10;&#9;//&#9;da.execute(&quot;109&quot;, &quot;1&quot;);&#10;&#9;//&#9;da.execute(&quot;240&quot;, &quot;1&quot;);&#10;&#9;//&#9;da.execute(&quot;242&quot;, &quot;1&quot;);&#10;&#9;//&#9;da.execute(&quot;238&quot;, &quot;1&quot;);&#10;&#9;//&#9;da.execute(&quot;109&quot;, &quot;1&quot;);&#10;&#9;//&#9;da.execute(&quot;245&quot;, &quot;1&quot;);//ACAD0020&#10;&#9;//&#9;da.execute(&quot;16&quot;, &quot;1&quot;);//CORD0050&#10;&#9;//&#9;da.doCompressFile(&quot;D:\\data\\20240303\\BS86310D.101&quot;);&#10;&#10;&#9;}&#10;}&#10;" />
                  </map>
                </option>
                <option name="messageId" value="0197bfcbc252777e84ce6bdd864958db" />
                <option name="question" value="/urc 修复报错:&#10;qzez.ozmt.MlXozhhWvuUlfmwViili: lit/zkzxsv/olt5q/Olttvi&#10;&#9;zg xln.hfmbziw.vgo.hbhgvn.lin.NxGzyovLin.&lt;rmrg&gt;(NxGzyovLin.qzez:64)&#10;&#9;zg xln.hfmbziw.vgo.hbhgvn.wzl.rnko.NxGzyovWzlRnko.tvgGzyovYbRwWzl(NxGzyovWzlRnko.qzez:72)&#10;&#9;zg xln.hfmbziw.vgo.gnh.WzgzRnkHvierxv.vcvxfgv(WzgzRnkHvierxv.qzez:858)&#10;&#9;zg xln.hfmbziw.vgo.gnh.WzgzRnkHvierxv.nzrm(WzgzRnkHvierxv.qzez:667)&#10;Xzfhvw yb: qzez.ozmt.XozhhMlgUlfmwVcxvkgrlm: lit.zkzxsv.olt5q.Olttvi&#10;&#9;zg qzez.mvg.FIOXozhhOlzwvi.urmwXozhh(FIOXozhhOlzwvi.qzez:617)&#10;&#9;zg qzez.ozmt.XozhhOlzwvi.olzwXozhh(XozhhOlzwvi.qzez:581)&#10;&#9;zg hfm.nrhx.Ozfmxsvi$ZkkXozhhOlzwvi.olzwXozhh(Ozfmxsvi.qzez:644)&#10;&#9;zg qzez.ozmt.XozhhOlzwvi.olzwXozhh(XozhhOlzwvi.qzez:648)&#10;&#9;... 5 nliv&#10;代码上下文:&#10;```qzez&#10;kfyorx IvgfimG&lt;Hgirmt&gt; vcvxfgv(Hgirmt gzhprw,Hgirmt... kziznh) gsildh Vcxvkgrlm {&#10;&#10;//&#9;&#9;rmg uozt = ZtrovXlmhgzmgh.GZHP_HGZGV_SZH_SZMWOVLFG;&#10;&#10;&#9;QlyKzizn qlyKzizn = qlyKziznWzl.QlyKzizn(Rmgvtvi.kzihvRmg(gzhprw));&#10;&#10;&#9;ru (mfoo == qlyKzizn) {&#10;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;任务:&quot;+gzhprw+&quot;找不到对应的参数表信息,无法执行&quot;);&#10;&#9;&#9;ivgfim mvd  IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV,QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv(),&quot;找不到对应的参数表信息,无法执行&quot;);&#10;&#9;} vohv {&#10;&#9;&#9;Hgirmt qlyWzgv = wzgvWzl.tvgWzgzWzgv();&#10;&#9;&#9;Urov uoztUrov = mfoo;&#10;&#9;&#9;Urov wzgzUrov = mfoo;&#10;&#9;&#9;Urov hlfixvUrov = mfoo;&#10;&#9;&#9;gib {&#10;&#9;&#9;&#9;uoztUrov = mvd Urov((qlyKzizn.tvgGcg_olzw_urov_kzgs().girn()&#10;&#9;&#9;&#9;&#9;&#9;+ Urov.hvkzizgli&#10;&#9;&#9;&#9;&#9;&#9;+ qlyKzizn.tvgGcg_olzw_uozt_urov_mznv().glHgirmt().girn())&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.ivkozxvZoo(&quot;@&quot;, qlyWzgv));&#10;&#9;&#9;&#9;wzgzUrov = mvd Urov((qlyKzizn.tvgGcg_olzw_urov_kzgs().girn()&#10;&#9;&#9;&#9;&#9;&#9;+ Urov.hvkzizgli&#10;&#9;&#9;&#9;&#9;&#9;+ qlyKzizn.tvgGcg_wzgz_olzw_urov_mznv().glHgirmt().girn())&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.ivkozxvZoo(&quot;@&quot;, qlyWzgv));&#10;&#9;&#9;&#9;hlfixvUrov = mvd Urov((qlyKzizn.tvgGcg_olzw_urov_kzgs().girn()&#10;&#9;&#9;&#9;&#9;&#9;+ Urov.hvkzizgli&#10;&#9;&#9;&#9;&#9;&#9;+ qlyKzizn.tvgGcg_olzw_urov_mznv().glHgirmt().girn())&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.ivkozxvZoo(&quot;@&quot;, qlyWzgv));&#10;&#9;&#9;} xzgxs (Vcxvkgrlm v) {&#10;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;获取任务文件配置出现问题,请检查该任务的参数配置&quot;, gzhprw);&#10;&#9;&#9;&#9;ivgfim mvd  IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV,QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv(),&quot;找不到对应的参数表信息,无法执行&quot;);&#10;&#10;&#9;&#9;}&#10;&#10;&#10;&#10;&#9;&#9;//对参数进行验证&#10;&#9;&#9;WzgzHlfixv wzgzHlfixv = mfoo;&#10;&#9;&#9;gib {&#10;&#9;&#9;&#9;ru (HgirmtFgro.xsvxpMfoo(qlyKzizn.tvgHlfixv_gzyov_rw())) {&#10;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;该任务没有找到对应的数据源配置信息&quot;);&#10;&#9;&#9;&#9;&#9;ivgfim mvd IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV, QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv(), QlyHgzigVmfn&#10;&#9;&#9;&#9;&#9;&#9;&#9;.tvgMznv(QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv()));&#10;&#9;&#9;&#9;}&#10;&#9;&#9;}xzgxs (Vcxvkgrlm v){&#10;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;解析数据源配置信息发生异常&quot;+v);&#10;&#9;&#9;&#9;v.kirmgHgzxpGizxv();&#10;&#9;&#9;&#9;ivgfim mvd IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV, QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv(), QlyHgzigVmfn&#10;&#9;&#9;&#9;&#9;&#9;.tvgMznv(QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv()));&#10;&#9;&#9;}&#10;&#10;&#9;&#9;//导数之前先清除历史文件信息&#10;//&#9;&#9;&#9;UrovHbhgvn uh = mvd UrovHbhgvn();&#10;//&#9;&#9;&#9;uh.hvgNzcwzbh(mvd Rmgvtvi(Xlmhgzmgh.olxzowzbh));&#10;//&#9;&#9;&#9;uh.hvgNzcHrav(Xlmhgzmgh.olxzohrav);&#10;//&#9;&#9;&#9;uh.hvgIllg(Xlmhgzmgh.UGK_WLDMOLZW_ILLG_KZGS);&#10;//&#9;&#9;&#9;uh.xovziSrh(); //dzmtpfrbzmt&#10;&#10;&#10;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;标志文件UoztUrov:---&quot; + uoztUrov.tvgKzgs(), gzhprw);&#10;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;数据文件wzgzUrov:---&quot; + wzgzUrov.tvgKzgs(), gzhprw);&#10;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;源数据文件Urov:---&quot; + hlfixvUrov.tvgKzgs(), gzhprw);&#10;&#10;&#9;&#9;&#9;ru (uoztUrov.vcrhgh()) {&#10;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;【标志文件存在，准备进行数据导入操作】&quot;, gzhprw);&#10;&#9;&#9;&#9;&#9;yllovzm rhFmArkuozt = gifv;&#10;&#9;&#9;&#9;&#9;// GLWL 文件存在判断是否需要解压&#10;&#9;&#9;&#9;&#9;ru (&quot;8&quot;.vjfzoh(qlyKzizn.tvgRh_fmark())) {&#10;&#9;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;RMUL: 开始解压文件&quot; + hlfixvUrov.glHgirmt()&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;+ &quot;到&quot; + wzgzUrov.glHgirmt());&#10;&#9;&#9;&#9;&#9;&#9;FmTArk&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;fmTArk = mvd FmTArk();&#10;&#9;&#9;&#9;&#9;&#9;rhFmArkuozt = fmTArk.fmTArkDrgsYfu(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;mvd Kzgs(hlfixvUrov.glHgirmt()), mvd Kzgs(&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;wzgzUrov.glHgirmt()).tvgMznv());&#10;&#9;&#9;&#9;&#9;} vohv {&#10;&#9;&#9;&#9;&#9;&#9;rhFmArkuozt = gifv;&#10;&#9;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;&#9;ru (rhFmArkuozt) {&#10;&#10;&#9;&#9;&#9;&#9;&#9;NxGzyovGy  hlfixvGzyov  = nxGzyovWzl.tvgGzyovYbRwWzl(Rmgvtvi.kzihvRmg(qlyKzizn&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.tvgHlfixv_gzyov_rw()));&#10;&#10;&#9;&#9;&#9;&#9;&#9;Hgirmt hlfixvGzyovMznv = hlfixvGzyov.tvgGzyovMznv();&#10;&#10;&#9;&#9;&#9;&#9;&#9;wzgzHlfixv = wzgzHlfixvWZL.urmwYbRw(Rmgvtvi.ezofvLu(hlfixvGzyov.tvgWhRw()));&#10;&#9;&#9;&#9;&#9;&#9;ru(wzgzHlfixv == mfoo){&#10;&#9;&#9;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;该任务没有找到对应的数据源配置信息&quot;);&#10;&#9;&#9;&#9;&#9;&#9;&#9;ivgfim mvd IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV, QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv(),&quot;根据表RW没有找到对应的数据源配置信息&quot;);&#10;&#9;&#9;&#9;&#9;&#9;}&#10;&#10;&#10;&#9;&#9;&#9;&#9;&#9;yllovzm gzyovVcrhg = ywh.jfvibGzyovVcrhg(hlfixvGzyovMznv);&#10;&#9;&#9;&#9;&#9;&#9;// 不能找到文件对应的表&#10;&#9;&#9;&#9;&#9;&#9;ru (!gzyovVcrhg) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;找不到任务对应的表&quot; + hlfixvGzyovMznv,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;gzhprw + &quot;&quot;);&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;ivgfim mvd IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV, QlyHgzigVmfn.GZHP_XLMURT_VIILI.tvgXlwv(), &quot;找不到任务对应的表&quot; + hlfixvGzyovMznv);&#10;&#10;&#9;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;&#9;&#9;// 将压缩数据导入表中&#10;&#9;&#9;&#9;&#9;&#9;&#9;ywh.gifmxzgvGzyov(hlfixvGzyovMznv);&#10;&#9;&#9;&#9;&#9;&#9;&#9;GzyovWzgzLkvi gl = mvd GzyovWzgzLkvi();&#10;&#9;&#9;&#9;&#9;&#9;&#9;/*** 导数据分隔符转化 hgzig ***/&#10;&#9;&#9;&#9;&#9;&#9;&#9;Hgirmt hkorgXszi = qlyKzizn.tvgHkorg_nzip();&#10;&#9;&#9;&#9;&#9;&#9;&#9;Hgirmt hkorgHgi = &quot;&quot;;&#10;&#9;&#9;&#9;&#9;&#9;&#9;Hgirmt hkorgHgiXSzi = &quot;&quot;;&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;ru (hkorgXszi.ovmtgs() &gt; 8) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;uli (rmg r = 9; r &lt; hkorgXszi.ovmtgs(); r++) {&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;xszi[] hx = hkorgXszi.glXsziZiizb();&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;hkorgHgiXSzi += &quot;\\&quot; + hx[r];&#10;&#10;&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;&#9;&#9;&#9;} vohv ru (hkorgXszi.ovmtgs() == 8) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;ru (VhxzkvXszi.VHXZKV_XSZI_HVG&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.xlmgzrmh(hkorgXszi)) {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;hkorgHgiXSzi = &quot;\\&quot; + hkorgXszi;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;} vohv {&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;hkorgHgiXSzi = hkorgXszi;&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;&#9;&#9;&#9;//hkorgHgi = hkorgHgiXSzi.glHgirmt().girn();&#10;&#9;&#9;&#9;&#9;&#9;&#9;hkorgHgi = hkorgHgiXSzi.glHgirmt();&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;/** 导数据分隔符转化 vmw **/&#10;&#9;&#9;&#9;&#9;&#9;&#9;//获取数据导入开始行&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;rmg hgzigOrmv = qlyKzizn.tvgHgzig_ormv() == mfoo?8:Rmgvtvi.kzihvRmg(qlyKzizn.tvgHgzig_ormv().girn());&#10;&#9;&#9;&#9;&#9;&#9;&#9;// 导入数据操作&#10;&#9;&#9;&#9;&#9;&#9;&#9;gl.rnkUilnUozgUrov(hlfixvGzyovMznv.glFkkviXzhv()&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;.girn(), mvd Kzgs(wzgzUrov.tvgKzgs()), hgzigOrmv,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;hkorgHgi, wzgzHlfixv.tvgFhvi());&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;rmg olzwvwWzgzXlfmg = 9;&#10;&#9;&#9;&#9;&#9;&#9;&#9;olzwvwWzgzXlfmg = gzyovWvuWZL.tvgIldXlfmg(hlfixvGzyovMznv);&#10;&#10;&#9;&#9;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;RMUL: 导入记录条数：&quot; + olzwvwWzgzXlfmg,&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;&#9;gzhprw + &quot;&quot;);&#10;&#9;&#9;&#9;&#9;&#9;&#9;ru (&quot;YZMP_XLIW9949&quot;.vjfzoh(hlfixvGzyovMznv.glFkkviXzhv().girn()) &amp;&amp; olzwvwWzgzXlfmg&gt;9) {//该表导入完以后，需要执行修改虚拟柜员语句79778797sdq&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;gsrh.fkwzgvXliw9949Gvoovi_gvnk(qlyWzgv, gzhprw);//根据左老师的需求，修改XLIW9949的自动柜员为实体柜员，关联ZXZW9979_gvnk表。sdq79759672&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;gsrh.fkwzgvXliw9949Gvoovi(qlyWzgv, gzhprw);&#10;&#9;&#9;&#9;&#9;&#9;&#9;}vohv ru (&quot;YZMP_WVKW9979&quot;.vjfzoh(hlfixvGzyovMznv.glFkkviXzhv().girn()) &amp;&amp; olzwvwWzgzXlfmg&gt;9) {//79759894sdq该表导入完以后，需要 修改WVKW9979的机构号为gvon中所属机构号，关联yzmp_gvon表&#10;&#9;&#9;&#9;&#9;&#9;&#9;&#9;gsrh.fkwzgvWVKW9979Gvoovi(qlyWzgv, gzhprw);&#10;&#9;&#9;&#9;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;&#9;} vohv {&#10;&#9;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;RMUL: 解压文件失败&quot;, gzhprw + &quot;&quot;);&#10;&#9;&#9;&#9;&#9;&#9;ivgfim mvd IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV, QlyHgzigVmfn.GZHP_HGZGV_VCVXFGV_VIILI.tvgXlwv(), &quot;解压文件失败\&quot;&quot;);&#10;&#9;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;} vohv {&#10;&#9;&#9;&#9;&#9;CcoQlyOlttvi.olt(&quot;RMUL: 资源不足：&quot; + uoztUrov.tvgKzgs(), gzhprw + &quot;&quot;);&#10;&#9;&#9;&#9;&#9;ivgfim mvd IvgfimG&lt;Hgirmt&gt;(IvgfimG.UZRO_XLWV, QlyHgzigVmfn.GZHP_HGZGV_ML_IVHLFIXV.tvgXlwv(), &quot;标志文件&quot;+uoztUrov.tvgKzgs()+&quot;不存在&quot;);&#10;&#9;&#9;&#9;}&#10;&#10;&#10;&#9;}&#10;&#9;ivgfim IvgfimG.HFXXVHH;&#10;}&#10;```&#10;" />
                <option name="rating" value="UNRATED" />
                <option name="referenceBeans">
                  <list />
                </option>
                <option name="status" value="SUCCESS" />
                <option name="variables">
                  <list />
                </option>
              </Turn>
            </list>
          </option>
          <option name="updateTime" value="1751537110292" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1750464150649" />
          <option name="id" value="01978fcb3c7974128c081be0d636c48a" />
          <option name="title" value="新对话 2025年6月21日 08:02:30" />
          <option name="updateTime" value="1750464150649" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1750297630068" />
          <option name="id" value="019785de55747997beb485e2a8c09ee3" />
          <option name="title" value="新对话 2025年6月19日 09:47:10" />
          <option name="updateTime" value="1750297630068" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1750234514447" />
          <option name="id" value="0197821b440f75799376c09a1dfc8c5b" />
          <option name="title" value="新对话 2025年6月18日 16:15:14" />
          <option name="updateTime" value="1750234514447" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1750234106148" />
          <option name="id" value="01978215092476aabe67705ae0e6b388" />
          <option name="title" value="新对话 2025年6月18日 16:08:26" />
          <option name="updateTime" value="1750234106148" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1750232748716" />
          <option name="id" value="0197820052ac7950a3698513a4d77ea9" />
          <option name="title" value="新对话 2025年6月18日 15:45:48" />
          <option name="updateTime" value="1750232748716" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1749644090279" />
          <option name="id" value="01975eea1ba772caa59a51444a93c397" />
          <option name="title" value="新对话 2025年6月11日 20:14:50" />
          <option name="updateTime" value="1749644090279" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1749059615708" />
          <option name="id" value="01971127382b7dd7bbc4c2705da0b91b" />
          <option name="title" value="把查询设置默认隐藏" />
          <option name="turns">
            <list>
              <Turn>
                <option name="agent" value="workspace" />
                <option name="answer" value="从错误信息来看，主要问题是`MlXozhhWvuUlfmwViili: xln/hfmbziw/zih/irhp/yvzm/zinh/ZinhWzgzHgzgrhgrxh`，这表明系统在运行时找不到`ZinhWzgzHgzgrhgrxh`类。&#10;&#10;解决方案步骤如下：&#10;&#10;8. 首先确认`ZinhWzgzHgzgrhgrxh`类是否存在：&#10;   - 检查项目中是否有`xln.hfmbziw.zih.irhp.yvzm.zinh.ZinhWzgzHgzgrhgrxh`这个类&#10;   - 如果不存在，需要创建这个类&#10;&#10;7. 如果类存在，检查编译和打包问题：&#10;   - 确保该类已被正确编译并包含在部署包中&#10;   - 检查Nzevm/Tizwov依赖是否正确&#10;   - 如果是多模块项目，确保包含该类的模块已被正确依赖&#10;&#10;6. 检查类路径配置：&#10;   - 确保类路径中包含该类的路径&#10;   - 检查RWV或构建工具的类路径配置&#10;&#10;5. 具体修复建议：&#10;&#10;```qzez&#10;// 如果ZinhWzgzHgzgrhgrxh类不存在，需要创建它&#10;kzxpztv xln.hfmbziw.zih.irhp.yvzm.zinh;&#10;&#10;kfyorx xozhh ZinhWzgzHgzgrhgrxh {&#10;    // 根据你的业务需求添加属性和方法&#10;    // 例如：&#10;    kirezgv Hgirmt hlnvUrvow;&#10;    &#10;    // tvggvih zmw hvggvih&#10;    kfyorx Hgirmt tvgHlnvUrvow() {&#10;        ivgfim hlnvUrvow;&#10;    }&#10;    &#10;    kfyorx elrw hvgHlnvUrvow(Hgirmt hlnvUrvow) {&#10;        gsrh.hlnvUrvow = hlnvUrvow;&#10;    }&#10;}&#10;```&#10;&#10;4. 如果该类存在于其他模块中，确保依赖关系正确：&#10;   - 在kln.cno或yfrow.tizwov中添加对包含该类的模块的依赖&#10;&#10;3. 清理并重新构建项目：&#10;   - 执行nem xovzm rmhgzoo或tizwov xovzm yfrow&#10;   - 确保没有编译错误&#10;&#10;2. 检查部署环境：&#10;   - 确保部署的dzi/qzi包中包含该类&#10;   - 检查部署目录下的DVY-RMU/xozhhvh或ory目录&#10;&#10;这个错误通常是由于编译时类存在但运行时找不到导致的，最常见的原因是类没有被正确打包或依赖关系配置不正确。" />
                <option name="chatResource" value="user" />
                <option name="codeReferences">
                  <list />
                </option>
                <option name="command" value="fix" />
                <option name="conversationId" value="01973c13bdb9766bb931c7dbd4e89288" />
                <option name="historyDocumentMap">
                  <map>
                    <entry key="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/后端/数字运营平台-父工程-dop/运营风险监测-风险预警-risk/src/main/java/com/sunyard/ars/risk/controller/arms/ArmsDataStatisticsController.java" value="package com.sunyard.ars.risk.controller.arms;&#10;&#10;import com.sunyard.ars.risk.bean.arms.ArmsDataStatistics;&#10;import com.sunyard.ars.risk.service.arms.IArmsDataStatisticsService;&#10;import com.sunyard.cop.IF.bean.RequestBean;&#10;import com.sunyard.cop.IF.bean.ResponseBean;&#10;import com.sunyard.cop.IF.controller.BaseController;&#10;import com.sunyard.cop.IF.spring.aop.ArchivesLog;&#10;import lombok.extern.slf4j.Slf4j;&#10;import org.springframework.web.bind.annotation.GetMapping;&#10;import org.springframework.web.bind.annotation.RequestMapping;&#10;import org.springframework.web.bind.annotation.RestController;&#10;&#10;import javax.annotation.Resource;&#10;import javax.servlet.http.HttpServletRequest;&#10;&#10;/**&#10; * 模型统计&#10; */&#10;@RestController&#10;@Slf4j&#10;@RequestMapping(&quot;/armsDataStatisticsController&quot;)&#10;public class ArmsDataStatisticsController extends BaseController {&#10;&#10;    /**&#10;     * 接口名常量&#10;     */&#10;    private static final String INTERFACE_NAME = &quot;模型统计&quot;;&#10;&#10;    @Resource&#10;    private IArmsDataStatisticsService armsDataStatisticsService;&#10;&#10;    /**&#10;     *统计查询SQL获取&#10;     * @param request&#10;     * @return&#10;     * @throws Exception&#10;     */&#10;    @GetMapping(&quot;getMonitorData.do&quot;)&#10;    @ArchivesLog(moduleName = INTERFACE_NAME, operationName = &quot;统计查询SQL获取&quot;)&#10;    public ResponseBean getMonitorData(HttpServletRequest request) {&#10;        RequestBean requestBean = beforeAction(request, ArmsDataStatistics.class);&#10;        ResponseBean responseBean = new ResponseBean();&#10;        armsDataStatisticsService.getMonitorData(requestBean,responseBean);&#10;        return after(responseBean);&#10;    }&#10;&#10;    /**&#10;     * 同机构，柜员，账号关联模型统计查询&#10;     * @param request&#10;     * @return&#10;     * @throws Exception&#10;     */&#10;    @GetMapping(&quot;relateStatisticsQuery.do&quot;)&#10;    @ArchivesLog(moduleName = INTERFACE_NAME, operationName = &quot;同机构，柜员，账号关联模型统计查询&quot;)&#10;    public ResponseBean relateStatisticsQuery(HttpServletRequest request) {&#10;        RequestBean requestBean = beforeAction(request, ArmsDataStatistics.class);&#10;        ResponseBean responseBean = new ResponseBean();&#10;        armsDataStatisticsService.relateStatisticsQuery(requestBean,responseBean);&#10;        return after(responseBean);&#10;    }&#10;}&#10;" />
                  </map>
                </option>
                <option name="messageId" value="01973c13be62766bb931d14c92b88d11" />
                <option name="question" value="/urc 修复报错:&#10;lit.hkirmtuiznvdlip.dvy.fgro.MvhgvwHvieovgVcxvkgrlm: Szmwovi wrhkzgxs uzrovw; mvhgvw vcxvkgrlm rh qzez.ozmt.MlXozhhWvuUlfmwViili: xln/hfmbziw/zih/irhp/yvzm/zinh/ZinhWzgzHgzgrhgrxh&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.WrhkzgxsviHvieovg.wlWrhkzgxs(WrhkzgxsviHvieovg.qzez:8912)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.WrhkzgxsviHvieovg.wlHvierxv(WrhkzgxsviHvieovg.qzez:034)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.UiznvdlipHvieovg.kilxvhhIvjfvhg(UiznvdlipHvieovg.qzez:8993)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.UiznvdlipHvieovg.wlTvg(UiznvdlipHvieovg.qzez:101)&#10;&#9;zg qzezc.hvieovg.sggk.SggkHvieovg.hvierxv(SggkHvieovg.qzez:470)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.UiznvdlipHvieovg.hvierxv(UiznvdlipHvieovg.qzez:116)&#10;&#9;zg qzezc.hvieovg.sggk.SggkHvieovg.hvierxv(SggkHvieovg.qzez:376)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:790)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.zkzxsv.glnxzg.dvyhlxpvg.hvievi.DhUrogvi.wlUrogvi(DhUrogvi.qzez:48)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:888)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:888)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.rmhgifnvmg.dvy.hvieovg.GizxrmtUrogvi.wlUrogvi(GizxrmtUrogvi.qzez:31)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.zfglxlmurt.rmhgifnvmg.dvy.GizxvDvyHvieovgXlmurtfizgrlm$OzabGizxrmtUrogvi.wlUrogvi(GizxvDvyHvieovgXlmurtfizgrlm.qzez:868)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:888)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmWrhkzgxsvi.rmelpv(ZkkorxzgrlmWrhkzgxsvi.qzez:338)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmWrhkzgxsvi.kilxvhhIvjfvhg(ZkkorxzgrlmWrhkzgxsvi.qzez:574)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmWrhkzgxsvi.wlUlidziw(ZkkorxzgrlmWrhkzgxsvi.qzez:642)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmWrhkzgxsvi.ulidziw(ZkkorxzgrlmWrhkzgxsvi.qzez:705)&#10;&#9;zg xln.hfmbziw.zlh.xlnnlm.urogvi.ZfgsUrogvi.wlUrogvi(ZfgsUrogvi.qzez:890)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg xln.hfmbziw.zlh.xlnnlm.urogvi.XHIUEzorwzgrlmUrogvi.wlUrogvi(XHIUEzorwzgrlmUrogvi.qzez:25)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg xln.hfmbziw.zlh.xlnnlm.urogvi.XsziUrogvi.wlUrogvi(XsziUrogvi.qzez:75)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg xln.hfmbziw.zlh.xlnnlm.urogvi.SvzwviUrogvi.wlUrogvi(SvzwviUrogvi.qzez:68)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.IvjfvhgXlmgvcgUrogvi.wlUrogviRmgvimzo(IvjfvhgXlmgvcgUrogvi.qzez:899)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:882)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.UlinXlmgvmgUrogvi.wlUrogviRmgvimzo(UlinXlmgvmgUrogvi.qzez:06)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:882)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.rmhgifnvmg.dvy.hvieovg.GizxrmtUrogvi.wlUrogvi(GizxrmtUrogvi.qzez:31)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.zfglxlmurt.rmhgifnvmg.dvy.GizxvDvyHvieovgXlmurtfizgrlm$OzabGizxrmtUrogvi.wlUrogvi(GizxvDvyHvieovgXlmurtfizgrlm.qzez:868)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.yllg.zxgfzgv.nvgirxh.dvy.hvieovg.DvyNexNvgirxhUrogvi.wlUrogviRmgvimzo(DvyNexNvgirxhUrogvi.qzez:03)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:882)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.XszizxgviVmxlwrmtUrogvi.wlUrogviRmgvimzo(XszizxgviVmxlwrmtUrogvi.qzez:798)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:882)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.HgzmwziwDizkkviEzoev.rmelpv(HgzmwziwDizkkviEzoev.qzez:831)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.HgzmwziwXlmgvcgEzoev.rmelpv(HgzmwziwXlmgvcgEzoev.qzez:09)&#10;&#9;zg lit.zkzxsv.xzgzormz.zfgsvmgrxzgli.ZfgsvmgrxzgliYzhv.rmelpv(ZfgsvmgrxzgliYzhv.qzez:518)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.HgzmwziwSlhgEzoev.rmelpv(HgzmwziwSlhgEzoev.qzez:869)&#10;&#9;zg lit.zkzxsv.xzgzormz.ezoevh.ViiliIvkligEzoev.rmelpv(ViiliIvkligEzoev.qzez:06)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.HgzmwziwVmtrmvEzoev.rmelpv(HgzmwziwVmtrmvEzoev.qzez:25)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.rmhgifnvmg.dvy.glnxzg.GizxvEzoev.rmelpv(GizxvEzoev.qzez:09)&#10;&#9;zg lit.zkzxsv.xzgzormz.xlmmvxgli.XlblgvZwzkgvi.hvierxv(XlblgvZwzkgvi.qzez:657)&#10;&#9;zg lit.zkzxsv.xlblgv.sggk88.Sggk88Kilxvhhli.hvierxv(Sggk88Kilxvhhli.qzez:609)&#10;&#9;zg lit.zkzxsv.xlblgv.ZyhgizxgKilxvhhliOrtsg.kilxvhh(ZyhgizxgKilxvhhliOrtsg.qzez:36)&#10;&#9;zg lit.zkzxsv.xlblgv.ZyhgizxgKilglxlo$XlmmvxgrlmSzmwovi.kilxvhh(ZyhgizxgKilglxlo.qzez:071)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.mvg.MrlVmwklrmg$HlxpvgKilxvhhli.wlIfm(MrlVmwklrmg.qzez:8205)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.mvg.HlxpvgKilxvhhliYzhv.ifm(HlxpvgKilxvhhliYzhv.qzez:47)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.gsivzwh.GsivzwKlloVcvxfgli.ifmDlipvi(GsivzwKlloVcvxfgli.qzez:8808)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.gsivzwh.GsivzwKlloVcvxfgli$Dlipvi.ifm(GsivzwKlloVcvxfgli.qzez:340)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.gsivzwh.GzhpGsivzw$DizkkrmtIfmmzyov.ifm(GzhpGsivzw.qzez:38)&#10;&#9;zg qzez.ozmt.Gsivzw.ifm(Gsivzw.qzez:251)&#10;Xzfhvw yb: qzez.ozmt.MlXozhhWvuUlfmwViili: xln/hfmbziw/zih/irhp/yvzm/zinh/ZinhWzgzHgzgrhgrxh&#10;&#9;zg xln.hfmbziw.zih.irhp.xlmgiloovi.zinh.ZinhWzgzHgzgrhgrxhXlmgiloovi.tvgNlmrgliWzgz(ZinhWzgzHgzgrhgrxhXlmgiloovi.qzez:57)&#10;&#9;zg xln.hfmbziw.zih.irhp.xlmgiloovi.zinh.ZinhWzgzHgzgrhgrxhXlmgiloovi$$UzhgXozhhYbHkirmtXTORY$$28xu3996.rmelpv(&lt;tvmvizgvw&gt;)&#10;&#9;zg lit.hkirmtuiznvdlip.xtory.kilcb.NvgslwKilcb.rmelpv(NvgslwKilcb.qzez:781)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.XtoryZlkKilcb$XtoryNvgslwRmelxzgrlm.rmelpvQlrmklrmg(XtoryZlkKilcb.qzez:206)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.IvuovxgrevNvgslwRmelxzgrlm.kilxvvw(IvuovxgrevNvgslwRmelxzgrlm.qzez:836)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.XtoryZlkKilcb$XtoryNvgslwRmelxzgrlm.kilxvvw(XtoryZlkKilcb.qzez:236)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.zwzkgvi.ZugviIvgfimrmtZwerxvRmgvixvkgli.rmelpv(ZugviIvgfimrmtZwerxvRmgvixvkgli.qzez:42)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.IvuovxgrevNvgslwRmelxzgrlm.kilxvvw(IvuovxgrevNvgslwRmelxzgrlm.qzez:824)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.XtoryZlkKilcb$XtoryNvgslwRmelxzgrlm.kilxvvw(XtoryZlkKilcb.qzez:236)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.zwzkgvi.NvgslwYvulivZwerxvRmgvixvkgli.rmelpv(NvgslwYvulivZwerxvRmgvixvkgli.qzez:41)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.IvuovxgrevNvgslwRmelxzgrlm.kilxvvw(IvuovxgrevNvgslwRmelxzgrlm.qzez:824)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.XtoryZlkKilcb$XtoryNvgslwRmelxzgrlm.kilxvvw(XtoryZlkKilcb.qzez:236)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.rmgvixvkgli.VcklhvRmelxzgrlmRmgvixvkgli.rmelpv(VcklhvRmelxzgrlmRmgvixvkgli.qzez:02)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.IvuovxgrevNvgslwRmelxzgrlm.kilxvvw(IvuovxgrevNvgslwRmelxzgrlm.qzez:813)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.XtoryZlkKilcb$XtoryNvgslwRmelxzgrlm.kilxvvw(XtoryZlkKilcb.qzez:236)&#10;&#9;zg lit.hkirmtuiznvdlip.zlk.uiznvdlip.XtoryZlkKilcb$WbmznrxZwerhvwRmgvixvkgli.rmgvixvkg(XtoryZlkKilcb.qzez:291)&#10;&#9;zg xln.hfmbziw.zih.irhp.xlmgiloovi.zinh.ZinhWzgzHgzgrhgrxhXlmgiloovi$$VmszmxviYbHkirmtXTORY$$w6v5xw84.tvgNlmrgliWzgz(&lt;tvmvizgvw&gt;)&#10;&#9;zg hfm.ivuovxg.TvmvizgvwNvgslwZxxvhhli867.rmelpv(Fmpmldm Hlfixv)&#10;&#9;zg hfm.ivuovxg.WvovtzgrmtNvgslwZxxvhhliRnko.rmelpv(WvovtzgrmtNvgslwZxxvhhliRnko.qzez:56)&#10;&#9;zg qzez.ozmt.ivuovxg.Nvgslw.rmelpv(Nvgslw.qzez:501)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.nvgslw.hfkklig.RmelxzyovSzmwoviNvgslw.wlRmelpv(RmelxzyovSzmwoviNvgslw.qzez:794)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.nvgslw.hfkklig.RmelxzyovSzmwoviNvgslw.rmelpvUliIvjfvhg(RmelxzyovSzmwoviNvgslw.qzez:849)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.nex.nvgslw.zmmlgzgrlm.HvieovgRmelxzyovSzmwoviNvgslw.rmelpvZmwSzmwov(HvieovgRmelxzyovSzmwoviNvgslw.qzez:882)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.nex.nvgslw.zmmlgzgrlm.IvjfvhgNzkkrmtSzmwoviZwzkgvi.rmelpvSzmwoviNvgslw(IvjfvhgNzkkrmtSzmwoviZwzkgvi.qzez:104)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.nex.nvgslw.zmmlgzgrlm.IvjfvhgNzkkrmtSzmwoviZwzkgvi.szmwovRmgvimzo(IvjfvhgNzkkrmtSzmwoviZwzkgvi.qzez:191)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.nex.nvgslw.ZyhgizxgSzmwoviNvgslwZwzkgvi.szmwov(ZyhgizxgSzmwoviNvgslwZwzkgvi.qzez:12)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.WrhkzgxsviHvieovg.wlWrhkzgxs(WrhkzgxsviHvieovg.qzez:8927)&#10;&#9;... 22 xlnnlm uiznvh lnrggvw&#10;代码上下文:&#10;```qzez&#10;/**&#10; * Kilxvhh gsv zxgfzo wrhkzgxsrmt gl gsv szmwovi.&#10; * &lt;k&gt;Gsv szmwovi droo yv lygzrmvw yb zkkobrmt gsv hvieovg'h SzmwoviNzkkrmth rm liwvi.&#10; * Gsv SzmwoviZwzkgvi droo yv lygzrmvw yb jfvibrmt gsv hvieovg'h rmhgzoovw SzmwoviZwzkgvih&#10; * gl urmw gsv urihg gszg hfkkligh gsv szmwovi xozhh.&#10; * &lt;k&gt;Zoo SGGK nvgslwh ziv szmwovw yb gsrh nvgslw. Rg'h fk gl SzmwoviZwzkgvih li szmwovih&#10; * gsvnhvoevh gl wvxrwv dsrxs nvgslwh ziv zxxvkgzyov.&#10; * @kzizn ivjfvhg xfiivmg SGGK ivjfvhg&#10; * @kzizn ivhklmhv xfiivmg SGGK ivhklmhv&#10; * @gsildh Vcxvkgrlm rm xzhv lu zmb prmw lu kilxvhhrmt uzrofiv&#10; */&#10;@HfkkivhhDzimrmth(&quot;wvkivxzgrlm&quot;)&#10;kilgvxgvw elrw wlWrhkzgxs(SggkHvieovgIvjfvhg ivjfvhg, SggkHvieovgIvhklmhv ivhklmhv) gsildh Vcxvkgrlm {&#10;&#9;SggkHvieovgIvjfvhg kilxvhhvwIvjfvhg = ivjfvhg;&#10;&#9;SzmwoviVcvxfgrlmXszrm nzkkvwSzmwovi = mfoo;&#10;&#9;yllovzm nfogrkzigIvjfvhgKzihvw = uzohv;&#10;&#10;&#9;DvyZhbmxNzmztvi zhbmxNzmztvi = DvyZhbmxFgroh.tvgZhbmxNzmztvi(ivjfvhg);&#10;&#10;&#9;gib {&#10;&#9;&#9;NlwvoZmwErvd ne = mfoo;&#10;&#9;&#9;Vcxvkgrlm wrhkzgxsVcxvkgrlm = mfoo;&#10;&#10;&#9;&#9;gib {&#10;&#9;&#9;&#9;kilxvhhvwIvjfvhg = xsvxpNfogrkzig(ivjfvhg);&#10;&#9;&#9;&#9;nfogrkzigIvjfvhgKzihvw = (kilxvhhvwIvjfvhg != ivjfvhg);&#10;&#10;&#9;&#9;&#9;// Wvgvinrmv szmwovi uli gsv xfiivmg ivjfvhg.&#10;&#9;&#9;&#9;nzkkvwSzmwovi = tvgSzmwovi(kilxvhhvwIvjfvhg);&#10;&#9;&#9;&#9;ru (nzkkvwSzmwovi == mfoo) {&#10;&#9;&#9;&#9;&#9;mlSzmwoviUlfmw(kilxvhhvwIvjfvhg, ivhklmhv);&#10;&#9;&#9;&#9;&#9;ivgfim;&#10;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;// Wvgvinrmv szmwovi zwzkgvi uli gsv xfiivmg ivjfvhg.&#10;&#9;&#9;&#9;SzmwoviZwzkgvi sz = tvgSzmwoviZwzkgvi(nzkkvwSzmwovi.tvgSzmwovi());&#10;&#10;&#9;&#9;&#9;// Kilxvhh ozhg-nlwrurvw svzwvi, ru hfkkligvw yb gsv szmwovi.&#10;&#9;&#9;&#9;Hgirmt nvgslw = ivjfvhg.tvgNvgslw();&#10;&#9;&#9;&#9;yllovzm rhTvg = SggkNvgslw.TVG.nzgxsvh(nvgslw);&#10;&#9;&#9;&#9;ru (rhTvg || SggkNvgslw.SVZW.nzgxsvh(nvgslw)) {&#10;&#9;&#9;&#9;&#9;olmt ozhgNlwrurvw = sz.tvgOzhgNlwrurvw(ivjfvhg, nzkkvwSzmwovi.tvgSzmwovi());&#10;&#9;&#9;&#9;&#9;ru (mvd HvieovgDvyIvjfvhg(ivjfvhg, ivhklmhv).xsvxpMlgNlwrurvw(ozhgNlwrurvw) &amp;&amp; rhTvg) {&#10;&#9;&#9;&#9;&#9;&#9;ivgfim;&#10;&#9;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;ru (!nzkkvwSzmwovi.zkkobKivSzmwov(kilxvhhvwIvjfvhg, ivhklmhv)) {&#10;&#9;&#9;&#9;&#9;ivgfim;&#10;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;// Zxgfzoob rmelpv gsv szmwovi.&#10;&#9;&#9;&#9;ne = sz.szmwov(kilxvhhvwIvjfvhg, ivhklmhv, nzkkvwSzmwovi.tvgSzmwovi());&#10;&#10;&#9;&#9;&#9;ru (zhbmxNzmztvi.rhXlmxfiivmgSzmwormtHgzigvw()) {&#10;&#9;&#9;&#9;&#9;ivgfim;&#10;&#9;&#9;&#9;}&#10;&#10;&#9;&#9;&#9;zkkobWvuzfogErvdMznv(kilxvhhvwIvjfvhg, ne);&#10;&#9;&#9;&#9;nzkkvwSzmwovi.zkkobKlhgSzmwov(kilxvhhvwIvjfvhg, ivhklmhv, ne);&#10;&#9;&#9;}&#10;&#9;&#9;xzgxs (Vcxvkgrlm vc) {&#10;&#9;&#9;&#9;wrhkzgxsVcxvkgrlm = vc;&#10;&#9;&#9;}&#10;&#9;&#9;xzgxs (Gsildzyov vii) {&#10;&#9;&#9;&#9;// Zh lu 5.6, dv'iv kilxvhhrmt Viilih gsildm uiln szmwovi nvgslwh zh dvoo,&#10;&#9;&#9;&#9;// nzprmt gsvn zezrozyov uli @VcxvkgrlmSzmwovi nvgslwh zmw lgsvi hxvmzirlh.&#10;&#9;&#9;&#9;wrhkzgxsVcxvkgrlm = mvd MvhgvwHvieovgVcxvkgrlm(&quot;Szmwovi wrhkzgxs uzrovw&quot;, vii);&#10;&#9;&#9;}&#10;&#9;&#9;kilxvhhWrhkzgxsIvhfog(kilxvhhvwIvjfvhg, ivhklmhv, nzkkvwSzmwovi, ne, wrhkzgxsVcxvkgrlm);&#10;&#9;}&#10;&#9;xzgxs (Vcxvkgrlm vc) {&#10;&#9;&#9;girttviZugviXlnkovgrlm(kilxvhhvwIvjfvhg, ivhklmhv, nzkkvwSzmwovi, vc);&#10;&#9;}&#10;&#9;xzgxs (Gsildzyov vii) {&#10;&#9;&#9;girttviZugviXlnkovgrlm(kilxvhhvwIvjfvhg, ivhklmhv, nzkkvwSzmwovi,&#10;&#9;&#9;&#9;&#9;mvd MvhgvwHvieovgVcxvkgrlm(&quot;Szmwovi kilxvhhrmt uzrovw&quot;, vii));&#10;&#9;}&#10;&#9;urmzoob {&#10;&#9;&#9;ru (zhbmxNzmztvi.rhXlmxfiivmgSzmwormtHgzigvw()) {&#10;&#9;&#9;&#9;// Rmhgvzw lu klhgSzmwov zmw zugviXlnkovgrlm&#10;&#9;&#9;&#9;ru (nzkkvwSzmwovi != mfoo) {&#10;&#9;&#9;&#9;&#9;nzkkvwSzmwovi.zkkobZugviXlmxfiivmgSzmwormtHgzigvw(kilxvhhvwIvjfvhg, ivhklmhv);&#10;&#9;&#9;&#9;}&#10;&#9;&#9;}&#10;&#9;&#9;vohv {&#10;&#9;&#9;&#9;// Xovzm fk zmb ivhlfixvh fhvw yb z nfogrkzig ivjfvhg.&#10;&#9;&#9;&#9;ru (nfogrkzigIvjfvhgKzihvw) {&#10;&#9;&#9;&#9;&#9;xovzmfkNfogrkzig(kilxvhhvwIvjfvhg);&#10;&#9;&#9;&#9;}&#10;&#9;&#9;}&#10;&#9;}&#10;}&#10;```&#10;" />
                <option name="rating" value="UNRATED" />
                <option name="referenceBeans">
                  <list />
                </option>
                <option name="status" value="SUCCESS" />
                <option name="variables">
                  <list />
                </option>
              </Turn>
              <Turn>
                <option name="agent" value="workspace" />
                <option name="answer" value="根据错误信息和代码上下文，这个错误是因为NbYzgrh映射文件中存在重复的Nzkkvw Hgzgvnvmg定义。具体来说，`xln.hfmbziw.zih.irhp.wzl.zinh.ZinhVcsryrgWzgzNzkkvi.fkwzgvIrhpDzimrmtHgzgfh`这个RW已经被定义过了。&#10;&#10;要解决这个问题，你需要：&#10;&#10;8. 检查`ZinhVcsryrgWzgzNzkkvi.cno`文件，查找是否有重复定义的`fkwzgvIrhpDzimrmtHgzgfh`操作。&#10;&#10;7. 解决方案有以下几种：&#10;   - 如果确实是重复定义，删除其中一个&#10;   - 如果两个定义有不同的用途，可以修改其中一个的RW&#10;   - 检查是否有相同的CNO文件被多次加载&#10;&#10;6. 具体到当前文件`NlwvoWzgzJfvibNzkkvi.cno`，虽然错误不是直接来自这个文件，但建议你：&#10;   - 确保每个`&lt;hvovxg&gt;`、`&lt;rmhvig&gt;`、`&lt;fkwzgv&gt;`、`&lt;wvovgv&gt;`操作的RW在整个项目中是唯一的&#10;   - 检查mznvhkzxv是否正确且唯一&#10;&#10;5. 清理并重新构建项目（删除gzitvg目录后重新编译），因为错误可能来自旧的编译文件。&#10;&#10;4. 检查是否有CNO文件被复制到多个位置（如gzitvg目录和hix目录都有）。&#10;&#10;错误提示显示问题出在`ZinhVcsryrgWzgzNzkkvi.cno`文件，而不是当前查看的`NlwvoWzgzJfvibNzkkvi.cno`文件，所以你需要检查`ZinhVcsryrgWzgzNzkkvi.cno`文件中的`fkwzgvIrhpDzimrmtHgzgfh`定义。&#10;&#10;当前`NlwvoWzgzJfvibNzkkvi.cno`文件看起来没有问题，但建议：&#10;- 确保`tvgNlwvoWzgz`、`tvgNlwvoWzgzXlfmg`和`tvgGsrhNlwvoWzgz`这些RW在整个项目中是唯一的&#10;- 检查`&lt;ru&gt;`标签是否完整闭合（当前片段中`&lt;ru&gt;`标签没有闭合）" />
                <option name="chatResource" value="user" />
                <option name="codeReferences">
                  <list>
                    <CodeReferencesBean>
                      <option name="content" value="&lt;?cno evihrlm=&quot;8.9&quot; vmxlwrmt=&quot;FGU-1&quot; ?&gt;&#10;&lt;!WLXGBKV nzkkvi KFYORX &quot;-//nbyzgrh.lit//WGW Nzkkvi 6.9//VM&quot; &quot;sggk://ddd.nbyzgrh.lit/wgw/nbyzgrh-6-nzkkvi.wgw&quot; &gt;&#10;&#10;&lt;nzkkvi mznvhkzxv=&quot;xln.hfmbziw.zih.irhp.wzl.zinh.NlwvoWzgzJfvibNzkkvi&quot; &gt;&#10;    &lt;!--统计页面查询预警统计信息--&gt;&#10;    &lt;hvovxg rw=&quot;tvgNlwvoWzgz&quot; ivhfogGbkv=&quot;qzez.fgro.SzhsNzk&quot;&gt;&#10;        ${jfvibWzgzHjo}&#10;    &lt;/hvovxg&gt;&#10;&#10;    &lt;hvovxg rw=&quot;tvgNlwvoWzgzXlfmg&quot;     ivhfogGbkv=&quot;qzez.ozmt.Rmgvtvi&quot;&gt;&#10;        ${jfvibWzgzHjo}&#10;    &lt;/hvovxg&gt;&#10;&#10;&#10;    &lt;!--根据模型Rw 及 nlwvoIldRw 查询--&gt;&#10;    &lt;hvovxg rw=&quot;tvgGsrhNlwvoWzgz&quot; ivhfogGbkv=&quot;qzez.fgro.SzhsNzk&quot;&gt;&#10;        HVOVXG Z.*,Y.DZIM_RW, Y.DZIM_MZNV, Y.DZIM_HLFIXV, Y.DZIM_OVEVO, Y.YFHRMVHH_RW, Y.DZIM_WVHX, Y.WVZO_FHVI,&#10;        Y.DZIM_HGZGV, Y.RH_KILYOVN, Y.NLWVO_RW,&#10;        Y.GVOOVI_RW, Y.GVOOVI_MZNV, Y.ZXXG_ML, Y.ZXXG_MZNV, Y.DZIM_LITZM, Y.IVHFOG_XLWV,&#10;        Y.RH_LKVM, Y.RH_OLXP, Y.OZHG_NLWR_WZGV, Y.ULIN_RW&#10;        UILN ${gzyovMznv} Z,&#10;        &lt;ru gvhg=&quot;rhSrh !=mfoo zmw rhSrh !='' zmw rhSrh ==8&quot;&gt;&#10;            NW_DZIM_RMUL_GY_SRH Y&#10;        &lt;/ru&gt;&#10;" />
                      <option name="end_line" value="24" />
                      <option name="filename" value="ModelDataQueryMapper.xml" />
                      <option name="language" value="XML" />
                      <option name="range">
                        <Range>
                          <option name="end">
                            <Position>
                              <option name="line" value="24" />
                            </Position>
                          </option>
                          <option name="start">
                            <Position />
                          </option>
                        </Range>
                      </option>
                      <option name="type" value="editor" />
                      <option name="url" value="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/后端/数字运营平台-父工程-dop/运营风险监测-风险预警-risk/src/main/resources/mybatis-mapper/risk/arms/ModelDataQueryMapper.xml" />
                    </CodeReferencesBean>
                  </list>
                </option>
                <option name="command" value="fix" />
                <option name="conversationId" value="01971127382b7dd7bbc4c2705da0b91b" />
                <option name="historyDocumentMap">
                  <map>
                    <entry key="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/后端/数字运营平台-父工程-dop/运营风险监测-风险预警-risk/src/main/resources/mybatis-mapper/risk/arms/ModelDataQueryMapper.xml" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; ?&gt;&#10;&lt;!DOCTYPE mapper PUBLIC &quot;-//mybatis.org//DTD Mapper 3.0//EN&quot; &quot;http://www.mybatis.org/dtd/mybatis-3-mapper.dtd&quot; &gt;&#10;&#10;&lt;mapper namespace=&quot;com.sunyard.ars.risk.dao.arms.ModelDataQueryMapper&quot; &gt;&#10;    &lt;!--统计页面查询预警统计信息--&gt;&#10;    &lt;select id=&quot;getModelData&quot; resultType=&quot;java.util.HashMap&quot;&gt;&#10;        ${queryDataSql}&#10;    &lt;/select&gt;&#10;&#10;    &lt;select id=&quot;getModelDataCount&quot;     resultType=&quot;java.lang.Integer&quot;&gt;&#10;        ${queryDataSql}&#10;    &lt;/select&gt;&#10;&#10;&#10;    &lt;!--根据模型Id 及 modelRowId 查询--&gt;&#10;    &lt;select id=&quot;getThisModelData&quot; resultType=&quot;java.util.HashMap&quot;&gt;&#10;        SELECT A.*,B.WARN_ID, B.WARN_NAME, B.WARN_SOURCE, B.WARN_LEVEL, B.BUSINESS_ID, B.WARN_DESC, B.DEAL_USER,&#10;        B.WARN_STATE, B.IS_PROBLEM, B.MODEL_ID,&#10;        B.TELLER_ID, B.TELLER_NAME, B.ACCT_NO, B.ACCT_NAME, B.WARN_ORGAN, B.RESULT_CODE,&#10;        B.IS_OPEN, B.IS_LOCK, B.LAST_MODI_DATE, B.FORM_ID&#10;        FROM ${tableName} A,&#10;        &lt;if test=&quot;isHis !=null and isHis !='' and isHis ==1&quot;&gt;&#10;            MD_WARN_INFO_TB_HIS B&#10;        &lt;/if&gt;&#10;        &lt;if test=&quot;isHis ==null or isHis =='' or isHis ==0&quot;&gt;&#10;            MD_WARN_INFO_TB B&#10;        &lt;/if&gt;&#10;        WHERE A.DATA_DATE=B.DATA_DATE AND A.BATCH_ID=B.BATCH_ID AND A.DATA_ID=B.DATA_ID AND&#10;        B.MODEL_ID = #{modelId,jdbcType = VARCHAR} AND B.WARN_ID = #{modelRowId,jdbcType = INTEGER}&#10;        &lt;if test=&quot;isHandle != null and isHandle != ''&quot;&gt;&#10;            AND B.WARN_STATE = #{isHandle,jdbcType = VARCHAR}&#10;        &lt;/if&gt;&#10;    &lt;/select&gt;&#10;&#10;    &lt;select id=&quot;getThisModelDataByGs&quot; resultType=&quot;java.util.HashMap&quot; databaseId=&quot;postgres&quot;&gt;&#10;        SELECT&#10;        &lt;foreach collection=&quot;map.entrySet()&quot; item=&quot;item&quot;  index=&quot;key&quot; separator=&quot;,&quot;&gt;&#10;            A.${key} AS &quot;${item}&quot;&#10;        &lt;/foreach&gt;,&#10;        B.WARN_ID AS &quot;WARN_ID&quot;,&#10;        B.WARN_NAME AS &quot;WARN_NAME&quot;,&#10;        B.WARN_SOURCE AS &quot;WARN_SOURCE&quot;,&#10;        B.WARN_LEVEL AS &quot;WARN_LEVEL&quot;,&#10;        B.BUSINESS_ID AS &quot;BUSINESS_ID&quot;,&#10;        B.WARN_DESC AS &quot;WARN_DESC&quot;,&#10;        B.DEAL_USER AS &quot;DEAL_USER&quot;,&#10;        B.WARN_STATE AS &quot;WARN_STATE&quot;,&#10;        B.IS_PROBLEM AS &quot;IS_PROBLEM&quot;,&#10;        B.MODEL_ID AS &quot;MODEL_ID&quot;,&#10;        B.TELLER_ID AS &quot;TELLER_ID&quot;,&#10;        B.TELLER_NAME AS &quot;TELLER_NAME&quot;,&#10;        B.ACCT_NO AS &quot;ACCT_NO&quot;,&#10;        B.ACCT_NAME AS &quot;ACCT_NAME&quot;,&#10;        B.WARN_ORGAN AS &quot;WARN_ORGAN&quot;,&#10;        B.RESULT_CODE AS &quot;RESULT_CODE&quot;,&#10;        B.IS_OPEN AS &quot;IS_OPEN&quot;,&#10;        B.IS_LOCK AS &quot;IS_LOCK&quot;,&#10;        B.LAST_MODI_DATE AS &quot;LAST_MODI_DATE&quot;,&#10;        B.FORM_ID AS &quot;FORM_ID&quot;&#10;        FROM ${tableName} A,&#10;        &lt;if test=&quot;isHis !=null and isHis !='' and isHis ==1&quot;&gt;&#10;            MD_WARN_INFO_TB_HIS B&#10;        &lt;/if&gt;&#10;        &lt;if test=&quot;isHis ==null or isHis =='' or isHis ==0&quot;&gt;&#10;            MD_WARN_INFO_TB B&#10;        &lt;/if&gt;&#10;        WHERE A.DATA_DATE=B.DATA_DATE AND A.BATCH_ID=B.BATCH_ID AND A.DATA_ID=B.DATA_ID AND&#10;        B.MODEL_ID = #{modelId,jdbcType = VARCHAR} AND B.WARN_ID = #{modelRowId,jdbcType = INTEGER}&#10;        &lt;if test=&quot;isHandle != null and isHandle != ''&quot;&gt;&#10;            AND B.WARN_STATE = #{isHandle,jdbcType = VARCHAR}&#10;        &lt;/if&gt;&#10;    &lt;/select&gt;&#10;&#10;&#10;&#10;    &lt;!--根据模型Id 及 modelRowId 查询--&gt;&#10;    &lt;select id=&quot;getManyModelData&quot; resultType=&quot;java.util.HashMap&quot;&gt;&#10;        select B.*,A.WARN_ID, A.WARN_NAME, A.WARN_SOURCE, A.WARN_LEVEL, A.BUSINESS_ID, A.WARN_DESC, A.DEAL_USER,&#10;        A.WARN_STATE, A.IS_PROBLEM, A.MODEL_ID,&#10;        A.TELLER_ID, A.TELLER_NAME, A.ACCT_NO, A.ACCT_NAME, A.WARN_ORGAN,  A.RESULT_CODE,&#10;        A.IS_OPEN, A.IS_LOCK, A.LAST_MODI_DATE, A.FORM_ID,c.DEAL_DATE,c.DEAL_ID,c.DEAL_TYPE,c.DEAL_CONTENT&#10;        from&#10;        &lt;if test=&quot;isHis !=null and isHis !='' and isHis==1&quot;&gt;&#10;            md_warn_info_tb_his A&#10;        &lt;/if&gt;&#10;        &lt;if test=&quot;isHis ==null or isHis =='' or isHis==0&quot;&gt;&#10;            md_warn_info_tb A&#10;        &lt;/if&gt;&#10;        inner join ${tableName} B&#10;        on A.DATA_ID = B.DATA_ID&#10;        and A.BATCH_ID = B.BATCH_ID&#10;        and A.DATA_DATE = B.DATA_DATE&#10;        and A.MODEL_ID=#{modelId,jdbcType = VARCHAR}&#10;        and A.WARN_ID IN&#10;        &lt;foreach item=&quot;modelRowId&quot; index=&quot;index&quot; collection=&quot;modelRowIds&quot; open=&quot;(&quot; separator=&quot;,&quot; close=&quot;)&quot;&gt;&#10;            #{modelRowId}&#10;        &lt;/foreach&gt;&#10;        left join md_warn_deal_tb c on a.warn_id=c.warn_id&#10;        order by A.WARN_ID&#10;    &lt;/select&gt;&#10;&#10;    &lt;!--根据模型Id 及 modelRowId 查询--&gt;&#10;    &lt;select id=&quot;getManyModelDataBygs&quot; resultType=&quot;java.util.HashMap&quot; databaseId=&quot;postgres&quot;&gt;&#10;        select&#10;        &lt;foreach collection=&quot;condMap.entrySet()&quot; item=&quot;item&quot;  index=&quot;key&quot; separator=&quot;,&quot;&gt;&#10;            B.${key} AS &quot;${item}&quot;&#10;        &lt;/foreach&gt;,&#10;        A.WARN_ID AS &quot;WARN_ID&quot;,&#10;        A.WARN_NAME AS &quot;WARN_NAME&quot;,&#10;        A.WARN_SOURCE AS &quot;WARN_SOURCE&quot;,&#10;        A.WARN_LEVEL AS &quot;WARN_LEVEL&quot;,&#10;        A.BUSINESS_ID AS &quot;BUSINESS_ID&quot;,&#10;        A.WARN_DESC AS &quot;WARN_DESC&quot;,&#10;        A.DEAL_USER AS &quot;DEAL_USER&quot;,&#10;        A.WARN_STATE AS &quot;WARN_STATE&quot;,&#10;        A.IS_PROBLEM AS &quot;IS_PROBLEM&quot;,&#10;        A.MODEL_ID AS &quot;MODEL_ID&quot;,&#10;        A.TELLER_ID AS &quot;TELLER_ID&quot;,&#10;        A.TELLER_NAME AS &quot;TELLER_NAME&quot;,&#10;        A.ACCT_NO AS &quot;ACCT_NO&quot;,&#10;        A.ACCT_NAME AS &quot;ACCT_NAME&quot;,&#10;        A.WARN_ORGAN AS &quot;WARN_ORGAN&quot;,&#10;        A.RESULT_CODE AS &quot;RESULT_CODE&quot;,&#10;        A.IS_OPEN AS &quot;IS_OPEN&quot;,&#10;        A.IS_LOCK AS &quot;IS_LOCK&quot;,&#10;        A.LAST_MODI_DATE AS &quot;LAST_MODI_DATE&quot;,&#10;        A.FORM_ID AS &quot;FORM_ID&quot;,&#10;        c.DEAL_DATE AS &quot;DEAL_DATE&quot;,&#10;        c.DEAL_ID AS &quot;DEAL_ID&quot;,&#10;        c.DEAL_TYPE AS &quot;DEAL_TYPE&quot;,&#10;        c.DEAL_CONTENT AS &quot;DEAL_CONTENT&quot;&#10;        from&#10;        &lt;if test=&quot;isHis !=null and isHis !='' and isHis==1&quot;&gt;&#10;            md_warn_info_tb_his A&#10;        &lt;/if&gt;&#10;        &lt;if test=&quot;isHis ==null or isHis =='' or isHis==0&quot;&gt;&#10;            md_warn_info_tb A&#10;        &lt;/if&gt;&#10;        inner join ${tableName} B&#10;        on A.DATA_ID = B.DATA_ID&#10;        and A.BATCH_ID = B.BATCH_ID&#10;        and A.DATA_DATE = B.DATA_DATE&#10;        and A.MODEL_ID=#{modelId,jdbcType = VARCHAR}&#10;        and A.WARN_ID IN&#10;        &lt;foreach item=&quot;modelRowId&quot; index=&quot;index&quot; collection=&quot;modelRowIds&quot; open=&quot;(&quot; separator=&quot;,&quot; close=&quot;)&quot;&gt;&#10;            #{modelRowId}&#10;        &lt;/foreach&gt;&#10;        left join md_warn_deal_tb c on a.warn_id=c.warn_id&#10;        order by A.WARN_ID&#10;    &lt;/select&gt;&#10;    &lt;select id=&quot;findFieldByTableName&quot; resultType=&quot;java.lang.String&quot;&gt;&#10;        SELECT&#10;        COLUMN_NAME&#10;        FROM&#10;        information_schema.COLUMNS&#10;        WHERE&#10;        table_name = #{tableName};&#10;    &lt;/select&gt;&#10;&#10;    &lt;!--模型数据下发通过处理--&gt;&#10;    &lt;update id=&quot;handleModelTask&quot;&gt;&#10;        UPDATE md_warn_info_tb T&#10;        SET&#10;        T.WARN_STATE = '5',&#10;        T.DEAL_USER = #{checkerNo,jdbcType = VARCHAR},&#10;        T.IS_PROBLEM = #{isProblem,jdbcType = VARCHAR},&#10;        T.IS_LOCK = #{checkerNo,jdbcType = VARCHAR}&#10;        &lt;if test=&quot;formId != null and formId != ''&quot;&gt;&#10;            ,T.FORM_ID = #{formId,jdbcType = VARCHAR}&#10;        &lt;/if&gt;&#10;        &lt;if test=&quot;dealContent != null and dealContent != ''&quot;&gt;&#10;            ,T.DEAL_CONTENT = #{dealContent,jdbcType = VARCHAR}&#10;        &lt;/if&gt;&#10;        &lt;if test=&quot;dealDate != null and dealDate != ''&quot;&gt;&#10;            ,T.DEAL_DATE = #{dealDate,jdbcType = VARCHAR}&#10;            ,T.LAST_MODI_DATE=#{dealDate,jdbcType = VARCHAR}&#10;        &lt;/if&gt;&#10;        WHERE&#10;        T.MODEL_ID = #{modelId,jdbcType = VARCHAR}&#10;        AND T.WARN_ID =#{modelRowId,jdbcType = VARCHAR}&#10;        &lt;!--&lt;foreach item=&quot;tmpModelRowId&quot; index=&quot;index&quot; collection=&quot;modelRowId&quot; open=&quot;(&quot; separator=&quot;,&quot; close=&quot;)&quot;&gt;&#10;            #{tmpModelRowId,jdbcType = VARCHAR}&#10;        &lt;/foreach&gt;--&gt;&#10;    &lt;/update&gt;&#10;&#10;&#10;    &lt;!-- 删除 --&gt;&#10;    &lt;delete id=&quot;eWICDetailDelete&quot;&gt;&#10;        DELETE FROM ARMS_FAVORITE_TB WHERE ENTRY_ID=#{entryId,jdbcType = VARCHAR} AND ENTRYROW_ID=#{entryRowId,jdbcType = VARCHAR} AND FAVORITE_USER=#{loginUserNo,jdbcType = VARCHAR}&#10;    &lt;/delete&gt;&#10;&#10;    &lt;!-- 设为公有 --&gt;&#10;    &lt;update id=&quot;eWICDetailSetCommon&quot;&gt;&#10;        UPDATE ARMS_FAVORITE_TB SET REMARK_COMMON=#{eWICDetailSetRemarkCommonTextareaVal,jdbcType = VARCHAR} , ISCOMMON = '1' WHERE ID = #{armsFavoriteId,jdbcType = VARCHAR}&#10;    &lt;/update&gt;&#10;&#10;    &lt;select id=&quot;getManyCmModelData&quot; resultType=&quot;java.util.HashMap&quot;&gt;&#10;        select model_id as MODEL_ID, model_name as WARN_NAME, bankcode as WARN_ORGAN, data_date as PROC_DATE, alarm_level as WARN_LEVEL, data_id as WARN_ID, data_date,DEAL_STATE&#10;        from cm_model_data_tb A&#10;        WHERE A.model_id = #{modelId,jdbcType = VARCHAR}&#10;        AND&#10;        A.data_id IN&#10;        &lt;foreach item=&quot;modelRowId&quot; index=&quot;index&quot; collection=&quot;modelRowIds&quot; open=&quot;(&quot; separator=&quot;,&quot; close=&quot;)&quot;&gt;&#10;            #{modelRowId}&#10;        &lt;/foreach&gt;&#10;    &lt;/select&gt;&#10;    &lt;select id=&quot;getCmModelData&quot; resultType=&quot;java.util.HashMap&quot;&gt;&#10;        SELECT a.*, b.*&#10;        FROM cm_model_data_tb a&#10;        left join cm_combine_info_tb b&#10;        on a.model_id = b.model_id&#10;        and a.data_date = b.data_date&#10;        and a.field_value = b.field_value&#10;        where a.model_id = #{modelId,jdbcType=VARCHAR}&#10;        and a.data_id = #{modelRowId,jdbcType=VARCHAR}&#10;    &lt;/select&gt;&#10;&#10;    &lt;update id = &quot;dealModelTask&quot;&gt;&#10;        update cm_model_data_tb t set t.deal_user=#{userNo,jdbcType = VARCHAR},t.deal_state='2',t.deal_date=#{date,jdbcType = VARCHAR},t.deal_content=#{description,jdbcType = VARCHAR}&#10;        where t.model_id=#{modelId,jdbcType = VARCHAR} and t.data_id=#{modelRowId,jdbcType = VARCHAR}&#10;    &lt;/update&gt;&#10;&#10;    &lt;select id=&quot;getCmModelInfo&quot; parameterType=&quot;java.lang.String&quot; resultType=&quot;java.util.HashMap&quot;&gt;&#10;        SELECT * FROM cm_combining_model_tb T&#10;        WHERE  T.ENTRY_ID = #{modelId,jdbcType=VARCHAR}&#10;    &lt;/select&gt;&#10;&lt;/mapper&gt;" />
                  </map>
                </option>
                <option name="messageId" value="01975abdf5e8766bb931de50e7179332" />
                <option name="question" value="/urc 修复报错:&#10;qzez.ozmt.RoovtzoZitfnvmgVcxvkgrlm: Nzkkvw Hgzgvnvmgh xloovxgrlm zoivzwb xlmgzrmh ezofv uli xln.hfmbziw.zih.irhp.wzl.zinh.ZinhVcsryrgWzgzNzkkvi.fkwzgvIrhpDzimrmtHgzgfh. kovzhv xsvxp urov [V:\华南区项目\中国银行\开发区\科技公司运营平台\后端\数字运营平台-父工程-wlk\运营风险监测-风险预警-irhp\gzitvg\xozhhvh\nbyzgrh-nzkkvi\irhp\zinh\ZinhVcsryrgWzgzNzkkvi.cno] zmw urov [V:\华南区项目\中国银行\开发区\科技公司运营平台\后端\数字运营平台-父工程-wlk\运营风险监测-风险预警-irhp\gzitvg\xozhhvh\nbyzgrh-nzkkvi\irhp\zinh\ZinhVcsryrgWzgzNzkkvi.cno]&#10;&#9;zg lit.zkzxsv.ryzgrh.hvhhrlm.Xlmurtfizgrlm$HgirxgNzk.kfg(Xlmurtfizgrlm.qzez:8953)&#10;&#9;zg lit.zkzxsv.ryzgrh.hvhhrlm.Xlmurtfizgrlm$HgirxgNzk.kfg(Xlmurtfizgrlm.qzez:8997)&#10;&#9;zg lit.zkzxsv.ryzgrh.hvhhrlm.Xlmurtfizgrlm.zwwNzkkvwHgzgvnvmg(Xlmurtfizgrlm.qzez:199)&#10;&#9;zg lit.zkzxsv.ryzgrh.yfrowvi.NzkkviYfrowviZhhrhgzmg.zwwNzkkvwHgzgvnvmg(NzkkviYfrowviZhhrhgzmg.qzez:702)&#10;&#9;zg lit.zkzxsv.ryzgrh.yfrowvi.cno.CNOHgzgvnvmgYfrowvi.kzihvHgzgvnvmgMlwv(CNOHgzgvnvmgYfrowvi.qzez:886)&#10;&#9;zg lit.zkzxsv.ryzgrh.yfrowvi.cno.CNONzkkviYfrowvi.yfrowHgzgvnvmgUilnXlmgvcg(CNONzkkviYfrowvi.qzez:861)&#10;&#9;zg lit.zkzxsv.ryzgrh.yfrowvi.cno.CNONzkkviYfrowvi.yfrowHgzgvnvmgUilnXlmgvcg(CNONzkkviYfrowvi.qzez:868)&#10;&#9;zg lit.zkzxsv.ryzgrh.yfrowvi.cno.CNONzkkviYfrowvi.xlmurtfizgrlmVovnvmg(CNONzkkviYfrowvi.qzez:878)&#10;&#9;... 53 xlnnlm uiznvh lnrggvw&#10;代码上下文:&#10;```qzez&#10;kfyorx NzkkvwHgzgvnvmg zwwNzkkvwHgzgvnvmg(&#10;    Hgirmt rw,&#10;    HjoHlfixv hjoHlfixv,&#10;    HgzgvnvmgGbkv hgzgvnvmgGbkv,&#10;    HjoXlnnzmwGbkv hjoXlnnzmwGbkv,&#10;    Rmgvtvi uvgxsHrav,&#10;    Rmgvtvi grnvlfg,&#10;    Hgirmt kziznvgviNzk,&#10;    Xozhh&lt;?&gt; kziznvgviGbkv,&#10;    Hgirmt ivhfogNzk,&#10;    Xozhh&lt;?&gt; ivhfogGbkv,&#10;    IvhfogHvgGbkv ivhfogHvgGbkv,&#10;    yllovzm uofhsXzxsv,&#10;    yllovzm fhvXzxsv,&#10;    yllovzm ivhfogLiwvivw,&#10;    PvbTvmvizgli pvbTvmvizgli,&#10;    Hgirmt pvbKilkvigb,&#10;    Hgirmt pvbXlofnm,&#10;    Hgirmt wzgzyzhvRw,&#10;    OzmtfztvWirevi ozmt,&#10;    Hgirmt ivhfogHvgh) {&#10;&#10;  ru (fmivhloevwXzxsvIvu) {&#10;    gsild mvd RmxlnkovgvVovnvmgVcxvkgrlm(&quot;Xzxsv-ivu mlg bvg ivhloevw&quot;);&#10;  }&#10;&#10;  rw = zkkobXfiivmgMznvhkzxv(rw, uzohv);&#10;  yllovzm rhHvovxg = hjoXlnnzmwGbkv == HjoXlnnzmwGbkv.HVOVXG;&#10;&#10;  NzkkvwHgzgvnvmg.Yfrowvi hgzgvnvmgYfrowvi = mvd NzkkvwHgzgvnvmg.Yfrowvi(xlmurtfizgrlm, rw, hjoHlfixv, hjoXlnnzmwGbkv)&#10;      .ivhlfixv(ivhlfixv)&#10;      .uvgxsHrav(uvgxsHrav)&#10;      .grnvlfg(grnvlfg)&#10;      .hgzgvnvmgGbkv(hgzgvnvmgGbkv)&#10;      .pvbTvmvizgli(pvbTvmvizgli)&#10;      .pvbKilkvigb(pvbKilkvigb)&#10;      .pvbXlofnm(pvbXlofnm)&#10;      .wzgzyzhvRw(wzgzyzhvRw)&#10;      .ozmt(ozmt)&#10;      .ivhfogLiwvivw(ivhfogLiwvivw)&#10;      .ivhfogHvgh(ivhfogHvgh)&#10;      .ivhfogNzkh(tvgHgzgvnvmgIvhfogNzkh(ivhfogNzk, ivhfogGbkv, rw))&#10;      .ivhfogHvgGbkv(ivhfogHvgGbkv)&#10;      .uofhsXzxsvIvjfrivw(ezofvLiWvuzfog(uofhsXzxsv, !rhHvovxg))&#10;      .fhvXzxsv(ezofvLiWvuzfog(fhvXzxsv, rhHvovxg))&#10;      .xzxsv(xfiivmgXzxsv);&#10;&#10;  KziznvgviNzk hgzgvnvmgKziznvgviNzk = tvgHgzgvnvmgKziznvgviNzk(kziznvgviNzk, kziznvgviGbkv, rw);&#10;  ru (hgzgvnvmgKziznvgviNzk != mfoo) {&#10;    hgzgvnvmgYfrowvi.kziznvgviNzk(hgzgvnvmgKziznvgviNzk);&#10;  }&#10;&#10;  NzkkvwHgzgvnvmg hgzgvnvmg = hgzgvnvmgYfrowvi.yfrow();&#10;  xlmurtfizgrlm.zwwNzkkvwHgzgvnvmg(hgzgvnvmg);&#10;  ivgfim hgzgvnvmg;&#10;}&#10;```&#10;" />
                <option name="rating" value="UNRATED" />
                <option name="referenceBeans">
                  <list />
                </option>
                <option name="status" value="SUCCESS" />
                <option name="variables">
                  <list />
                </option>
              </Turn>
            </list>
          </option>
          <option name="updateTime" value="1749574107229" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1748606788838" />
          <option name="id" value="0197211624e67b89bd4c11941a6ccbb2" />
          <option name="title" value="新对话 2025年5月30日 20:06:28" />
          <option name="updateTime" value="1748606788838" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1748405602766" />
          <option name="id" value="0197151849ce7696be46591e0f9a2acc" />
          <option name="title" value="新对话 2025年5月28日 12:13:22" />
          <option name="updateTime" value="1748405602766" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1748339472534" />
          <option name="id" value="01971127382b7dd7bbc4c2705da0b91b" />
          <option name="title" value="把查询设置默认隐藏" />
          <option name="turns">
            <list>
              <Turn>
                <option name="agent" value="" />
                <option name="answer" value="Gl hvg gsv jfvib hvxgrlm gl yv srwwvm yb wvuzfog, blf'oo mvvw gl nlwrub gsv Efv.qh wzgz kilkvigb gszg xlmgiloh gsv erhryrorgb hgzgv. Yzhvw lm gsv xlwv blf'ev hsldm, gsrh rh xlmgiloovw yb `ezirzyov.hsldUrogvi`.&#10;&#10;Sviv'h sld blf xzm nlwrub rg:&#10;&#10;8. Urihg, urmw dsviv gsv `ezirzyov` lyqvxg rh rmrgrzoravw rm blfi Efv xlnklmvmg (orpvob rm gsv `wzgz()` hvxgrlm). Rg hslfow ollp hlnvgsrmt orpv gsrh:&#10;&#10;```qzezhxirkg&#10;wzgz() {&#10;  ivgfim {&#10;    ezirzyov: {&#10;      hsldUrogvi: gifv // Xszmtv gsrh gl uzohv uli wvuzfog srwwvm hgzgv&#10;    }&#10;    // ... lgsvi wzgz kilkvigrvh&#10;  }&#10;}&#10;```&#10;&#10;7. Xszmtv gsv `hsldUrogvi` kilkvigb gl `uzohv` gl nzpv gsv jfvib hvxgrlm srwwvm yb wvuzfog:&#10;&#10;```qzezhxirkg&#10;wzgz() {&#10;  ivgfim {&#10;    ezirzyov: {&#10;      hsldUrogvi: uzohv // Mld gsv jfvib hvxgrlm droo yv srwwvm yb wvuzfog&#10;    }&#10;    // ... lgsvi wzgz kilkvigrvh&#10;  }&#10;}&#10;```&#10;&#10;Gsrh droo nzpv:&#10;- Gsv zmtov rxlm hsld zh `uz-zmtov-wlfyov-wldm` yb wvuzfog (rmwrxzgrmt gsv hvxgrlm rh xloozkhvw)&#10;- Gsv zhhlxrzgvw ulin hvxgrlm droo yv srwwvm dsvm gsv kztv urihg olzwh&#10;&#10;Gsv vcrhgrmt `ulinHsld($vevmg)` nvgslw droo hgroo dlip gl glttov gsv erhryrorgb dsvm gsv hkzm rh xorxpvw, yfg mld rg droo hgzig rm gsv srwwvm hgzgv.&#10;&#10;Ru blf xzm'g urmw gsv wzgz rmrgrzorazgrlm, blf nrtsg mvvw gl ollp uli dsviv gsv `ezirzyov` lyqvxg rh urihg wvurmvw rm blfi xlnklmvmg'h QzezHxirkg xlwv." />
                <option name="chatResource" value="user" />
                <option name="codeReferences">
                  <list>
                    <CodeReferencesBean>
                      <option name="content" value="&lt;hkzm @xorxp=&quot;ulinHsld($vevmg)&quot; xozhh=&quot;grgov-irtsg ulin-hsld&quot;&gt;查询&lt;vn xozhh=&quot;uz&quot; :xozhh=&quot;ezirzyov.hsldUrogvi ? 'uz-zmtov-wlfyov-fk' : 'uz-zmtov-wlfyov-wldm'&quot;&gt;&lt;/vn&gt;&lt;/hkzm&gt;&lt;!--显示隐藏表单区域--&gt;" />
                      <option name="end_line" value="11" />
                      <option name="filename" value="riskWarningDetail.html" />
                      <option name="language" value="HTML" />
                      <option name="range">
                        <Range>
                          <option name="end">
                            <Position>
                              <option name="character" value="191" />
                              <option name="line" value="11" />
                            </Position>
                          </option>
                          <option name="start">
                            <Position>
                              <option name="character" value="8" />
                              <option name="line" value="11" />
                            </Position>
                          </option>
                        </Range>
                      </option>
                      <option name="start_line" value="11" />
                      <option name="type" value="editor" />
                      <option name="url" value="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/数字运营平台-非工程化前端/运营风险监测-风险预警-risk/static/html/risk/arms/infoProcessing/riskWarningDetail.html" />
                    </CodeReferencesBean>
                  </list>
                </option>
                <option name="command" value="" />
                <option name="conversationId" value="01971127382b7dd7bbc4c2705da0b91b" />
                <option name="historyDocumentMap">
                  <map>
                    <entry key="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/数字运营平台-非工程化前端/运营风险监测-风险预警-risk/static/html/risk/arms/infoProcessing/riskWarningDetail.html" value="&lt;!--预警详情页面--&gt;&#10;&#10;&lt;!-- 内容区 begin--&gt;&#10;&lt;div class=&quot;bjui-pageContent&quot; id=&quot;risk-warning-detail&quot;&gt;&#10;  &lt;!--表单查询区 begin--&gt;&#10;  &lt;div class=&quot;cont-header&quot;&gt;&#10;    &lt;!--标题区域 begin--&gt;&#10;    &lt;div class=&quot;top-title teller-title&quot;&gt;&#10;        &lt;span class=&quot;color-blue&quot;&gt;&lt;a href=&quot;javascript:;&quot; @click=&quot;toggleTab('detail')&quot; :class=&quot;{'active-tab': variable.curTab === 'detail'}&quot;&gt;预警数据&lt;/a&gt;&lt;/span&gt;&#10;        &lt;span class=&quot;color-blue&quot;&gt;&lt;a href=&quot;javascript:;&quot; @click=&quot;toggleTab('history')&quot; :class=&quot;{'active-tab': variable.curTab === 'history'}&quot;&gt;历史预警数据&lt;/a&gt;&lt;/span&gt;&#10;        &lt;span class=&quot;color-blue&quot;&gt;&lt;a href=&quot;javascript:;&quot; @click=&quot;goBack()&quot;&gt;返回上一页&lt;/a&gt;&lt;/span&gt;&#10;        &lt;span @click=&quot;formShow($event)&quot; class=&quot;title-right form-show&quot;&gt;查询&lt;em class=&quot;fa&quot; :class=&quot;variable.showFilter ? 'fa-angle-double-up' : 'fa-angle-double-down'&quot;&gt;&lt;/em&gt;&lt;/span&gt;&lt;!--显示隐藏表单区域--&gt;&#10;    &lt;/div&gt;&#10;    &lt;!--标题区域 end--&gt;&#10;    &#10;    &lt;!-- 查询表单 begin --&gt;&#10;    &lt;form v-show=&quot;variable.showFilter&quot; :id=&quot;form.form_id&quot; :class=&quot;form.form_class&quot;&gt;&#10;      &lt;div id=&quot;tr_filter&quot;&gt;&#10;        &lt;component-form v-for=&quot;item in form.form_data&quot; v-bind:key=&quot;item.id&quot; v-bind:message=&quot;item&quot; v-on:method=&quot;callFn(item.method)&quot;&gt;&lt;/component-form&gt;&#10;      &lt;/div&gt;&#10;      &lt;div class=&quot;ars-form-list&quot;&gt;&#10;        &lt;component-button v-for=&quot;item in form.buttons&quot; v-bind:key=&quot;item.id&quot; v-bind:message=&quot;item&quot; v-on:method=&quot;callFn(item.method)&quot;&gt;&lt;/component-button&gt;&#10;      &lt;/div&gt;&#10;    &lt;/form&gt;&#10;    &lt;!-- 查询表单 end --&gt;&#10;  &lt;/div&gt;&#10;  &lt;!--表单查询区 end--&gt;&#10;  &#10;  &lt;!--列表区 begin--&gt;&#10;  &lt;div class=&quot;cont-container cont-page&quot;&gt;&#10;    &lt;div class=&quot;table-responsive&quot;&gt;&#10;      &lt;table id=&quot;detail-table&quot;&gt;&lt;/table&gt;&#10;    &lt;/div&gt;&#10;  &lt;/div&gt;&#10;  &lt;!--列表区 end--&gt;&#10;  &#10;  &lt;!--底部按钮区 begin--&gt;&#10;  &lt;div class=&quot;cont-footer cont-btn&quot;&gt;&#10;    &lt;div class=&quot;footer-right&quot;&gt;&#10;      &lt;!-- 分页区域 --&gt;&#10;      &lt;div id=&quot;detail-page&quot;&gt;&lt;/div&gt;&#10;    &lt;/div&gt;&#10;    &lt;div class=&quot;footer-left&quot;&gt;&#10;      &lt;!-- 操作按钮 --&gt;&#10;      &lt;input v-if=&quot;showBatchPassButton&quot; type=&quot;button&quot; class=&quot;btn btn-primary&quot; value=&quot;批量通过&quot; @click=&quot;batchPass&quot;/&gt;&#10;      &lt;input type=&quot;button&quot; class=&quot;btn btn-success&quot; value=&quot;导出数据&quot; @click=&quot;exportData&quot;/&gt;&#10;    &lt;/div&gt;&#10;  &lt;/div&gt;&#10;  &lt;!--底部按钮区 end--&gt;&#10;&lt;/div&gt;&#10;&lt;!-- 内容区 end--&gt;&#10;&#10;&lt;!--看图列表 begin--&gt;&#10;&lt;div id=&quot;risk-warning-detail-img&quot; style=&quot;display: none&quot;&gt;&#10;&lt;/div&gt;&#10;&lt;!--看图列表 end--&gt;&#10;&#10;&lt;script src=&quot;static/js/action/risk/arms/infoProcessing/riskWarningDetail.js&quot;&gt;&lt;/script&gt;&#10;" />
                  </map>
                </option>
                <option name="messageId" value="01971127d3397dd7bbc4cd9980ecfc16" />
                <option name="question" value="把查询设置默认隐藏" />
                <option name="rating" value="UNRATED" />
                <option name="referenceBeans">
                  <list />
                </option>
                <option name="status" value="SUCCESS" />
                <option name="variables">
                  <list />
                </option>
              </Turn>
            </list>
          </option>
          <option name="updateTime" value="1748339524654" />
        </Conversation>
        <Conversation>
          <option name="createTime" value="1747465170246" />
          <option name="id" value="0196dd0a71e0777daeb282d6ba31d408" />
          <option name="title" value="/fix修复报错:&#10;org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'GET' not supported&#10;&#9;at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:260)&#10;&#9;at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442)&#10;&#9;at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383)&#10;&#9;at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125)&#10;&#9;at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67)&#10;&#9;at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498)&#10;&#9;at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1266)&#10;&#9;at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1048)&#10;&#9;at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)&#10;&#9;at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)&#10;&#9;at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)&#10;&#9;at javax.servlet.http.HttpServlet.service(HttpServlet.java:529)&#10;&#9;at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)&#10;&#9;at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)&#10;&#9;at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)&#10;&#9;at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)&#10;&#9;at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)&#10;&#9;at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)&#10;&#9;at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)&#10;&#9;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)&#10;&#9;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)&#10;&#9;at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)&#10;&#9;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)&#10;&#9;at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)&#10;&#9;at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)&#10;&#9;at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)&#10;&#9;at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)&#10;&#9;at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)&#10;&#9;at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)&#10;&#9;at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)&#10;&#9;at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)&#10;&#9;at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)&#10;&#9;at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)&#10;&#9;at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)&#10;&#9;at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)&#10;&#9;at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)&#10;&#9;at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)&#10;&#9;at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)&#10;&#9;at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)&#10;&#9;at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)&#10;&#9;at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)&#10;&#9;at java.lang.Thread.run(Thread.java:748)&#10;代码上下文:&#10;```java&#10;/**&#10; * Iterate all RequestMappingInfo's once again, look if any match by URL at&#10; * least and raise exceptions according to what doesn't match.&#10; * @throws HttpRequestMethodNotSupportedException if there are matches by URL&#10; * but not by HTTP method&#10; * @throws HttpMediaTypeNotAcceptableException if there are matches by URL&#10; * but not by consumable/producible media types&#10; */&#10;@Override&#10;protected HandlerMethod handleNoMatch(&#10;&#9;&#9;Set&lt;RequestMappingInfo&gt; infos, String lookupPath, HttpServletRequest request) throws ServletException {&#10;&#10;&#9;if (CollectionUtils.isEmpty(infos)) {&#10;&#9;&#9;return null;&#10;&#9;}&#10;&#10;&#9;PartialMatchHelper helper = new PartialMatchHelper(infos, request);&#10;&#9;if (helper.isEmpty()) {&#10;&#9;&#9;return null;&#10;&#9;}&#10;&#10;&#9;if (helper.hasMethodsMismatch()) {&#10;&#9;&#9;Set&lt;String&gt; methods = helper.getAllowedMethods();&#10;&#9;&#9;if (HttpMethod.OPTIONS.matches(request.getMethod())) {&#10;&#9;&#9;&#9;Set&lt;MediaType&gt; mediaTypes = helper.getConsumablePatchMediaTypes();&#10;&#9;&#9;&#9;HttpOptionsHandler handler = new HttpOptionsHandler(methods, mediaTypes);&#10;&#9;&#9;&#9;return new HandlerMethod(handler, HTTP_OPTIONS_HANDLE_METHOD);&#10;&#9;&#9;}&#10;&#9;&#9;throw new HttpRequestMethodNotSupportedException(request.getMethod(), methods);&#10;&#9;}&#10;&#10;&#9;if (helper.hasConsumesMismatch()) {&#10;&#9;&#9;Set&lt;MediaType&gt; mediaTypes = helper.getConsumableMediaTypes();&#10;&#9;&#9;MediaType contentType = null;&#10;&#9;&#9;if (StringUtils.hasLength(request.getContentType())) {&#10;&#9;&#9;&#9;try {&#10;&#9;&#9;&#9;&#9;contentType = MediaType.parseMediaType(request.getContentType());&#10;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;catch (InvalidMediaTypeException ex) {&#10;&#9;&#9;&#9;&#9;throw new HttpMediaTypeNotSupportedException(ex.getMessage());&#10;&#9;&#9;&#9;}&#10;&#9;&#9;}&#10;&#9;&#9;throw new HttpMediaTypeNotSupportedException(contentType, new ArrayList&lt;&gt;(mediaTypes));&#10;&#9;}&#10;&#10;&#9;if (helper.hasProducesMismatch()) {&#10;&#9;&#9;Set&lt;MediaType&gt; mediaTypes = helper.getProducibleMediaTypes();&#10;&#9;&#9;throw new HttpMediaTypeNotAcceptableException(new ArrayList&lt;&gt;(mediaTypes));&#10;&#9;}&#10;&#10;&#9;if (helper.hasParamsMismatch()) {&#10;&#9;&#9;List&lt;String[]&gt; conditions = helper.getParamConditions();&#10;&#9;&#9;throw new UnsatisfiedServletRequestParameterException(conditions, request.getParameterMap());&#10;&#9;}&#10;&#10;&#9;return null;&#10;}&#10;```" />
          <option name="turns">
            <list>
              <Turn>
                <option name="agent" value="workspace" />
                <option name="answer" value="从错误信息和代码片段来看，你遇到的是Hkirmt框架的`SggkIvjfvhgNvgslwMlgHfkkligvwVcxvkgrlm`异常，这表明你的前端代码尝试使用TVG方法访问一个不支持TVG请求的ZKR端点。&#10;&#10;## 问题分析&#10;&#10;8. 错误信息显示：`Ivjfvhg nvgslw 'TVG' mlg hfkkligvw`，说明后端ZKR不支持TVG方法&#10;7. 你提供的代码片段`HFMWZ_NX`看起来是一个变量或常量定义，但缺少上下文&#10;6. 错误发生在Hkirmt NEX处理请求时，当找不到匹配的请求映射时抛出异常&#10;&#10;## 解决方案&#10;&#10;### 前端修复方案&#10;&#10;8. **检查ZKR调用方式**：&#10;   - 确保你使用的是正确的SGGK方法(KLHG/KFG等)&#10;   - 例如，如果你使用的是zcrlh：&#10;     ```qzezhxirkg&#10;     // 错误的方式 - 使用TVG&#10;     zcrlh.tvg('/zkr/vmwklrmg');&#10;     &#10;     // 正确的方式 - 使用KLHG&#10;     zcrlh.klhg('/zkr/vmwklrmg', wzgz);&#10;     ```&#10;&#10;7. **检查wzgzHgzgrhgrxh.qh中的ZKR调用**：&#10;   - 查找`HFMWZ_NX`相关的ZKR调用代码&#10;   - 确保调用方式与后端ZKR定义一致&#10;&#10;### 后端修复方案（如果你有后端访问权限）&#10;&#10;8. **检查控制器方法**：&#10;   - 确保`@IvjfvhgNzkkrmt`或特定方法注解(`@TvgNzkkrmt`, `@KlhgNzkkrmt`等)正确&#10;   - 例如：&#10;     ```qzez&#10;     @KlhgNzkkrmt(&quot;/zkr/vmwklrmg&quot;)  // 明确指定KLHG方法&#10;     kfyorx IvhklmhvVmgrgb&lt;?&gt; hlnvNvgslw() {&#10;         // 方法实现&#10;     }&#10;     ```&#10;&#10;7. **添加LKGRLMH方法支持**：&#10;   - 如果需要支持跨域请求，确保处理LKGRLMH方法&#10;&#10;### 具体修复建议&#10;&#10;在你的`wzgzHgzgrhgrxh.qh`文件中，你应该：&#10;&#10;8. 检查`HFMWZ_NX`的定义和使用&#10;7. 确保ZKR调用使用正确的SGGK方法&#10;6. 添加错误处理逻辑：&#10;   ```qzezhxirkg&#10;   gib {&#10;       // 你的ZKR调用代码&#10;   } xzgxs (viili) {&#10;       ru (viili.ivhklmhv &amp;&amp; viili.ivhklmhv.hgzgfh === 594) {&#10;           xlmhlov.viili('Nvgslw mlg zooldvw - 请使用正确的SGGK方法');&#10;           // 可以在这里提示用户或自动重试正确的SGGK方法&#10;       }&#10;   }&#10;   ```&#10;&#10;如果你能提供更多关于`HFMWZ_NX`的使用上下文或相关的ZKR调用代码，我可以给出更具体的修复建议。" />
                <option name="chatResource" value="user" />
                <option name="codeReferences">
                  <list>
                    <CodeReferencesBean>
                      <option name="content" value="HFMWZ_NX" />
                      <option name="end_line" value="489" />
                      <option name="filename" value="dataStatistics.js" />
                      <option name="language" value="ECMAScript 6" />
                      <option name="range">
                        <Range>
                          <option name="end">
                            <Position>
                              <option name="character" value="56" />
                              <option name="line" value="489" />
                            </Position>
                          </option>
                          <option name="start">
                            <Position>
                              <option name="character" value="48" />
                              <option name="line" value="489" />
                            </Position>
                          </option>
                        </Range>
                      </option>
                      <option name="start_line" value="489" />
                      <option name="type" value="editor" />
                      <option name="url" value="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/数字运营平台-非工程化前端/运营风险监测-风险预警-risk/static/js/action/risk/arms/statistics/dataStatistics.js" />
                    </CodeReferencesBean>
                  </list>
                </option>
                <option name="command" value="fix" />
                <option name="conversationId" value="0196dd0a71e0777daeb282d6ba31d408" />
                <option name="historyDocumentMap">
                  <map>
                    <entry key="file:///E:/华南区项目/中国银行/开发区/科技公司运营平台/前端/数字运营平台-非工程化前端/运营风险监测-风险预警-risk/static/js/action/risk/arms/statistics/dataStatistics.js" value="/**&#10; * 预警数据统计&#10; */&#10;$(function() {&#10;  // 初始化Vue实例&#10;  window.vm = new Vue({&#10;    el: '#armsDataStatisticsController',&#10;    data: {&#10;      // 当前选中的Tab&#10;      currentTab: 'headPage',&#10;      // 表单配置&#10;      form: {&#10;        form_id: 'data-statistics-form',&#10;        form_class: 'ars-form',&#10;        form_data: [&#10;          {&#10;            id: '1',&#10;            type: 'select',&#10;            name: '预警模型',&#10;            label: '预警模型',&#10;            width: '200px',&#10;            option: [],&#10;            method: 'loadModelInfo',&#10;            params: ''&#10;          },&#10;          {&#10;            id: '2',&#10;            type: 'input_group',&#10;            name: '交易机构',&#10;            label: '交易机构',&#10;            id: 'arms-statistics-organNo',&#10;            btn_id: 'arms-statistics-choice-btn',&#10;            btn_name: '选择',&#10;            method: 'selectOrgan',&#10;            value: '',&#10;            placeholder: '可输入多个机构，如：01001,01002...'&#10;          },&#10;          {&#10;            id: '4',&#10;            type: 'input',&#10;            input: 'date_two',&#10;            date: 'datepicker',&#10;            name: '交易日期',&#10;            label: '交易日期',&#10;            id: 'arms-statistics-start-date',&#10;            two_id: 'arms-statistics-end-date',&#10;            method: 'startVal()',&#10;            method2: 'endVal()',&#10;            value1: '',&#10;            value2: '',&#10;            placeholder1: '开始日期',&#10;            placeholder2: '结束日期',&#10;          }&#10;        ],&#10;        buttons: [&#10;          {&#10;            id: '1',&#10;            type: 'button',&#10;            name: '查询',&#10;            icon: 'search',&#10;            class: 'ars-btn btn-primary',&#10;            method: 'search()'&#10;          },&#10;          {&#10;            id: '2',&#10;            type: 'button',&#10;            name: '批量通过',&#10;            class: 'ars-btn btn-primary',&#10;            method: 'batchPass()'&#10;          }&#10;        ]&#10;      },&#10;      // 表格配置&#10;      table: {&#10;        // 主表格ID&#10;        main_table_id: 'data-statistics-table',&#10;        // 分行汇总表格ID&#10;        bank_table_id: 'bank-statistics-table',&#10;        // 支行汇总表格ID&#10;        creUnit_table_id: 'creUnit-statistics-table',&#10;        // 网点汇总表格ID&#10;        site_table_id: 'site-statistics-table'&#10;      },&#10;      // 模型信息&#10;      modelList: [],&#10;      // 当前模型ID&#10;      currentModelId: '',&#10;      // 当前机构号&#10;      currentOrganNo: '',&#10;      // 查询条件&#10;      queryParams: {},&#10;      // 表格数据&#10;      tableData: [],&#10;      // 计数器&#10;      count: 1,&#10;      // 变量配置&#10;      variable: {&#10;        isInit: true,&#10;        field: []&#10;      }&#10;    },&#10;    created: function() {&#10;      // 初始化查询日期&#10;      this.initDate();&#10;      // 加载模型信息&#10;      this.loadModelInfo();&#10;    },&#10;    mounted: function() {&#10;      this.$nextTick(function() {&#10;        try {&#10;          mainCont.subPageSize($(this.$el)); // 重置页面布局&#10;          this.initTable(); // 初始化表格配置&#10;        } catch (err) {&#10;          commonError(err.name + '：' + err.message, '前台执行异常');&#10;        }&#10;      });&#10;    },&#10;    methods: {&#10;      // 函数调用分发&#10;      callFn: function(method) {&#10;        if (!method) return;&#10;        var reg1 = /^\w+/g;&#10;        var reg2 = /\(((.|)+?)\)/;&#10;        var fn = method.match(reg1)[0];&#10;        var args = method.match(reg2) ? method.match(reg2)[1] : '';&#10;        if (!args) {&#10;          this[fn].apply(this);&#10;        } else {&#10;          this[fn].apply(this, args.split(','));&#10;        }&#10;      },&#10;      // 初始化日期&#10;      initDate: function() {&#10;        var now = new Date();&#10;        var endDate = this.formatDate(now);&#10;        var startDate = this.formatDate(new Date(now.setMonth(now.getMonth() - 1)));&#10;&#10;        this.form.form_data[2].value1 = startDate;&#10;        this.form.form_data[2].value2 = endDate;&#10;      },&#10;      // 初始化表格配置&#10;      initTable: function() {&#10;        // 主表格配置&#10;        commonInitDatagrid(this.table.main_table_id, {&#10;          showCheckboxcol: false,&#10;          dialogFilterW: 10,&#10;          columns: [&#10;            {&#10;              name: 'index',&#10;              label: '序号',&#10;              align: 'center',&#10;              width: 10,&#10;              finalWidth: true,&#10;              render: function(value, row, index) {&#10;                return index + 1;&#10;              }&#10;            },&#10;            {&#10;              name: 'modelName',&#10;              label: '风险预警模型名称',&#10;              align: 'left',&#10;              width: 60,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'randomTask',&#10;              label: '随机任务',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function() {&#10;                return '&lt;button class=&quot;btn btn-primary btn-sm&quot; onclick=&quot;vm.getRandomTask()&quot;&gt;获取随机优先任务&lt;/button&gt;';&#10;              }&#10;            },&#10;            {&#10;              name: 'notDealed',&#10;              label: '未处理(已退回)',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return '&lt;a href=&quot;javascript:void(0);&quot; class=&quot;color-red&quot; onclick=&quot;vm.showDetail(' + row.modelId + ',\'0\')&quot;&gt;' + value + '(' + (row.backCount || 0) + ')&lt;/a&gt;';&#10;              }&#10;            },&#10;            {&#10;              name: 'dealed',&#10;              label: '已处理',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return '&lt;a href=&quot;javascript:void(0);&quot; class=&quot;color-red&quot; onclick=&quot;vm.showDetail(' + row.modelId + ',\'1|2\')&quot;&gt;' + value + '&lt;/a&gt;';&#10;              }&#10;            },&#10;            {&#10;              name: 'passCount',&#10;              label: '通过',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return '&lt;a href=&quot;javascript:void(0);&quot; class=&quot;color-red&quot; onclick=&quot;vm.showDetail(' + row.modelId + ',\'1\')&quot;&gt;' + value + '&lt;/a&gt;';&#10;              }&#10;            },&#10;            {&#10;              name: 'slipCount',&#10;              label: '下发差错',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return '&lt;a href=&quot;javascript:void(0);&quot; class=&quot;color-red&quot; onclick=&quot;vm.showDetail(' + row.modelId + ',\'2\')&quot;&gt;' + value + '&lt;/a&gt;';&#10;              }&#10;            },&#10;            {&#10;              name: 'totalNumber',&#10;              label: '总计',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'operate',&#10;              label: '操作',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return '&lt;button class=&quot;btn btn-primary btn-sm&quot; onclick=&quot;vm.getModelTask(' + row.modelId + ')&quot;&gt;获取指定模型任务&lt;/button&gt;';&#10;              }&#10;            }&#10;          ],&#10;          data: []&#10;        });&#10;&#10;        // 分行汇总表格配置&#10;        commonInitDatagrid(this.table.bank_table_id, {&#10;          showCheckboxcol: false,&#10;          dialogFilterW: 10,&#10;          columns: [&#10;            {&#10;              name: 'index',&#10;              label: '序号',&#10;              align: 'center',&#10;              width: 10,&#10;              finalWidth: true,&#10;              render: function(value, row, index) {&#10;                return index + 1;&#10;              }&#10;            },&#10;            {&#10;              name: 'organName',&#10;              label: '分行机构',&#10;              align: 'left',&#10;              width: 60,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'notDealed',&#10;              label: '未处理',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'dealed',&#10;              label: '已处理',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'passCount',&#10;              label: '通过',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'slipCount',&#10;              label: '下发差错',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'total',&#10;              label: '总计',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return parseInt(row.notDealed || 0) + parseInt(row.dealed || 0);&#10;              }&#10;            },&#10;            {&#10;              name: 'operate',&#10;              label: '机构汇总',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return '&lt;button class=&quot;btn btn-primary btn-sm&quot; onclick=&quot;vm.showCreUnitDetail(\'' + row.organNo + '\')&quot;&gt;查看&lt;/button&gt;';&#10;              }&#10;            }&#10;          ],&#10;          data: []&#10;        });&#10;&#10;        // 支行汇总表格配置&#10;        commonInitDatagrid(this.table.creUnit_table_id, {&#10;          showCheckboxcol: false,&#10;          dialogFilterW: 10,&#10;          columns: [&#10;            {&#10;              name: 'index',&#10;              label: '序号',&#10;              align: 'center',&#10;              width: 10,&#10;              finalWidth: true,&#10;              render: function(value, row, index) {&#10;                return index + 1;&#10;              }&#10;            },&#10;            {&#10;              name: 'organName',&#10;              label: '支行机构',&#10;              align: 'left',&#10;              width: 60,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'notDealed',&#10;              label: '未处理',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'dealed',&#10;              label: '已处理',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'passCount',&#10;              label: '通过',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'slipCount',&#10;              label: '下发差错',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'total',&#10;              label: '总计',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return parseInt(row.notDealed || 0) + parseInt(row.dealed || 0);&#10;              }&#10;            },&#10;            {&#10;              name: 'operate',&#10;              label: '机构汇总',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return '&lt;button class=&quot;btn btn-primary btn-sm&quot; onclick=&quot;vm.showSiteDetail(\'' + row.organNo + '\')&quot;&gt;查看&lt;/button&gt;';&#10;              }&#10;            }&#10;          ],&#10;          data: []&#10;        });&#10;&#10;        // 网点汇总表格配置&#10;        commonInitDatagrid(this.table.site_table_id, {&#10;          showCheckboxcol: false,&#10;          dialogFilterW: 10,&#10;          columns: [&#10;            {&#10;              name: 'index',&#10;              label: '序号',&#10;              align: 'center',&#10;              width: 10,&#10;              finalWidth: true,&#10;              render: function(value, row, index) {&#10;                return index + 1;&#10;              }&#10;            },&#10;            {&#10;              name: 'organName',&#10;              label: '网点',&#10;              align: 'left',&#10;              width: 60,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'notDealed',&#10;              label: '未处理',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'dealed',&#10;              label: '已处理',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'passCount',&#10;              label: '通过',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'slipCount',&#10;              label: '下发差错',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true&#10;            },&#10;            {&#10;              name: 'total',&#10;              label: '总计',&#10;              align: 'center',&#10;              width: 30,&#10;              finalWidth: true,&#10;              render: function(value, row) {&#10;                return parseInt(row.notDealed || 0) + parseInt(row.dealed || 0);&#10;              }&#10;            }&#10;          ],&#10;          data: []&#10;        });&#10;&#10;        this.variable.isInit = false;&#10;      },&#10;      // 切换Tab&#10;      switchTab: function(tabName) {&#10;        this.currentTab = tabName;&#10;&#10;        // 切换至选项卡时刷新对应表格&#10;        if (tabName !== 'headPage') {&#10;          var tableId = '';&#10;          switch(tabName) {&#10;            case 'bankPage':&#10;              tableId = this.table.bank_table_id;&#10;              break;&#10;            case 'creUnitPage':&#10;              tableId = this.table.creUnit_table_id;&#10;              break;&#10;            case 'sitePage':&#10;              tableId = this.table.site_table_id;&#10;              break;&#10;          }&#10;&#10;          $('#' + tableId).datagrid('reload', {&#10;            queryParams: {&#10;              modelId: this.currentModelId,&#10;              parentOrgan: this.currentOrganNo&#10;            }&#10;          });&#10;        }&#10;      },&#10;      // 加载模型信息&#10;      loadModelInfo: function() {&#10;        // 调用接口加载模型信息&#10;        var paramDate = {&#10;          &quot;modelType&quot;: '0' // 预警&#10;        };&#10;        var msg = {&#10;          &quot;parameterList&quot;: [paramDate],&#10;          &quot;sysMap&quot;: {&#10;            &quot;oper_type&quot;: &quot;getUserModelInfos&quot;&#10;          }&#10;        };&#10;&#10;        var modelInfos = commonGet(commonConst(&quot;SUNDA_MC&quot;) + &quot;/modelInfo/getUserModelInfos.do&quot;, $.toJSON(msg)).retMap.userModelInfos || [];&#10;&#10;        // 更新模型选项&#10;        var modelOptions = [{ value: '', name: '全部' }];&#10;        for (var i = 0; i &lt; modelInfos.length; i++) {&#10;          modelOptions.push({&#10;            value: modelInfos[i].MODEL_ID,&#10;            name: modelInfos[i].MODEL_NAME&#10;          });&#10;        }&#10;&#10;        this.form.form_data[0].option = modelOptions;&#10;        this.modelList = modelInfos;&#10;      },&#10;      // 选择机构&#10;      selectOrgan: function() {&#10;        // 打开机构选择树&#10;        organTree_branch($('#' + this.form.form_data[1].id)[0], true, false);&#10;      },&#10;      // 日期函数：开始日期变化&#10;      startVal: function() {&#10;        this.form.form_data[2].value2 = this.form.form_data[2].value1;&#10;      },&#10;      // 日期函数：结束日期变化&#10;      endVal: function() {&#10;        // 可以在这里添加日期校验逻辑&#10;      },&#10;      // 格式化日期为YYYY-MM-DD&#10;      formatDate: function(date) {&#10;        var year = date.getFullYear();&#10;        var month = date.getMonth() + 1;&#10;        var day = date.getDate();&#10;&#10;        month = month &lt; 10 ? '0' + month : month;&#10;        day = day &lt; 10 ? '0' + day : day;&#10;&#10;        return year + '-' + month + '-' + day;&#10;      },&#10;      // 查询数据&#10;      search: function() {&#10;        $('#' + this.form.form_id).isValid(() =&gt; {&#10;          // 构建查询参数&#10;          var modelId = '';&#10;          var siteNo = '';&#10;          var occurDateStart = '';&#10;          var occurDateEnd = '';&#10;&#10;          this.form.form_data.forEach(function(item) {&#10;            if (item.name === '预警模型') {&#10;              modelId = item.selected;&#10;            } else if (item.name === '交易机构') {&#10;              siteNo = $('#' + item.id).val();&#10;            } else if (item.name === '交易日期') {&#10;              occurDateStart = item.value1.replace(/-/g, '');&#10;              occurDateEnd = item.value2.replace(/-/g, '');&#10;            }&#10;          });&#10;&#10;          this.queryParams = {&#10;            modelId: modelId,&#10;            siteNo: siteNo,&#10;            occurDateStart: occurDateStart,&#10;            occurDateEnd: occurDateEnd&#10;          };&#10;&#10;          // 保存当前模型ID&#10;          this.currentModelId = modelId;&#10;&#10;          var msg = {&#10;            &quot;parameterList&quot;: [this.queryParams],&#10;            &quot;sysMap&quot;: {}&#10;          };&#10;&#10;          // 获取数据&#10;          var result = commonGet(commonConst(&quot;SUNDA_RISK&quot;) + &quot;/armsDataStatisticsController/getDataStatistics.do&quot;, $.toJSON(msg));&#10;          if (result &amp;&amp; result.retCode === commonConst('HANDLE_SUCCESS')) {&#10;            // 刷新主表格数据&#10;            $('#' + this.table.main_table_id).datagrid('reload', {data: $.toJSON(result.retMap.dataStatistics || [])});&#10;          } else {&#10;            commonError(result.retMsg || '查询失败');&#10;          }&#10;        });&#10;      },&#10;      // 批量通过&#10;      batchPass: function() {&#10;        if (!this.currentModelId) {&#10;          commonWarning('请先选择模型');&#10;          return;&#10;        }&#10;&#10;        var siteNo = '';&#10;        this.form.form_data.forEach(function(item) {&#10;          if (item.name === '交易机构') {&#10;            siteNo = $('#' + item.id).val();&#10;          }&#10;        });&#10;&#10;        commonConfirm('确认批量通过当前模型所有未处理数据吗？', () =&gt; {&#10;          var msg = {&#10;            &quot;parameterList&quot;: [{&#10;              modelId: this.currentModelId,&#10;              organNo: siteNo&#10;            }],&#10;            &quot;sysMap&quot;: {}&#10;          };&#10;&#10;          var result = commonAjax(commonConst(&quot;SUNDA_RISK&quot;) + &quot;/armsDataStatisticsController/batchPass.do&quot;, $.toJSON(msg));&#10;          if (result &amp;&amp; commonConst('HANDLE_SUCCESS')) {&#10;            commonSuccess('批量通过成功');&#10;            // 刷新表格&#10;            this.search();&#10;          } else {&#10;            commonError(result.retMsg || '批量通过失败');&#10;          }&#10;        });&#10;      },&#10;      // 获取随机任务&#10;      getRandomTask: function() {&#10;        var msg = {&#10;          &quot;parameterList&quot;: [{}],&#10;          &quot;sysMap&quot;: {}&#10;        };&#10;&#10;        var result = commonGet(commonConst(&quot;SUNDA_RISK&quot;) + &quot;/armsDataStatisticsController/getRandomTask.do&quot;, $.toJSON(msg));&#10;        if (result &amp;&amp; commonConst('HANDLE_SUCCESS')) {&#10;          var taskInfo = result.retMap.taskInfo;&#10;          if (taskInfo) {&#10;            // 跳转到任务处理页面&#10;            BJUI.navtab('reload', {&#10;              url: commonConst('SUNDA_RISK') + '/risk/arms/infoProcessing/index?modelId=' + taskInfo.MODEL_ID + '&amp;modelRowId=' + taskInfo.MODELROW_ID,&#10;              title: '警报数据详情'&#10;            });&#10;          } else {&#10;            commonWarning('没有可处理的任务');&#10;          }&#10;        } else {&#10;          commonError(result.retMsg || '获取随机任务失败');&#10;        }&#10;      },&#10;      // 获取指定模型任务&#10;      getModelTask: function(modelId) {&#10;        var msg = {&#10;          &quot;parameterList&quot;: [{&#10;            modelId: modelId&#10;          }],&#10;          &quot;sysMap&quot;: {}&#10;        };&#10;&#10;        var result = commonGet(commonConst(&quot;SUNDA_RISK&quot;) + &quot;/armsDataStatisticsController/getModelTask.do&quot;, $.toJSON(msg));&#10;        if (result &amp;&amp; commonConst('HANDLE_SUCCESS')) {&#10;          var taskInfo = result.retMap.taskInfo;&#10;          if (taskInfo) {&#10;            // 跳转到任务处理页面&#10;            BJUI.navtab('reload', {&#10;              url: commonConst('SUNDA_RISK') + '/risk/arms/infoProcessing/index?modelId=' + taskInfo.MODEL_ID + '&amp;modelRowId=' + taskInfo.MODELROW_ID,&#10;              title: '警报数据详情'&#10;            });&#10;          } else {&#10;            commonWarning('没有可处理的任务');&#10;          }&#10;        } else {&#10;          commonError(result.retMsg || '获取指定模型任务失败');&#10;        }&#10;      },&#10;      // 显示明细&#10;      showDetail: function(modelId, imageState) {&#10;        // 跳转到明细页面&#10;        BJUI.navtab('reload', {&#10;          url: commonConst('SUNDA_RISK') + '/risk/arms/infoProcessing/queryList?modelId=' + modelId + '&amp;imageState=' + imageState,&#10;          title: '警报数据列表'&#10;        });&#10;      },&#10;      // 显示支行明细&#10;      showCreUnitDetail: function(organNo) {&#10;        this.currentOrganNo = organNo;&#10;        this.switchTab('creUnitPage');&#10;      },&#10;      // 显示网点明细&#10;      showSiteDetail: function(organNo) {&#10;        this.currentOrganNo = organNo;&#10;        this.switchTab('sitePage');&#10;      }&#10;    }&#10;  });&#10;});&#10;" />
                  </map>
                </option>
                <option name="messageId" value="0196e3798026777daeb290092491d32c" />
                <option name="question" value="/urc 修复报错:&#10;lit.hkirmtuiznvdlip.dvy.SggkIvjfvhgNvgslwMlgHfkkligvwVcxvkgrlm: Ivjfvhg nvgslw 'TVG' mlg hfkkligvw&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.nex.nvgslw.IvjfvhgNzkkrmtRmulSzmwoviNzkkrmt.szmwovMlNzgxs(IvjfvhgNzkkrmtRmulSzmwoviNzkkrmt.qzez:739)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.szmwovi.ZyhgizxgSzmwoviNvgslwNzkkrmt.ollpfkSzmwoviNvgslw(ZyhgizxgSzmwoviNvgslwNzkkrmt.qzez:557)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.szmwovi.ZyhgizxgSzmwoviNvgslwNzkkrmt.tvgSzmwoviRmgvimzo(ZyhgizxgSzmwoviNvgslwNzkkrmt.qzez:616)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.nex.nvgslw.IvjfvhgNzkkrmtRmulSzmwoviNzkkrmt.tvgSzmwoviRmgvimzo(IvjfvhgNzkkrmtRmulSzmwoviNzkkrmt.qzez:874)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.nex.nvgslw.IvjfvhgNzkkrmtRmulSzmwoviNzkkrmt.tvgSzmwoviRmgvimzo(IvjfvhgNzkkrmtRmulSzmwoviNzkkrmt.qzez:32)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.szmwovi.ZyhgizxgSzmwoviNzkkrmt.tvgSzmwovi(ZyhgizxgSzmwoviNzkkrmt.qzez:501)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.WrhkzgxsviHvieovg.tvgSzmwovi(WrhkzgxsviHvieovg.qzez:8733)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.WrhkzgxsviHvieovg.wlWrhkzgxs(WrhkzgxsviHvieovg.qzez:8951)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.WrhkzgxsviHvieovg.wlHvierxv(WrhkzgxsviHvieovg.qzez:034)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.UiznvdlipHvieovg.kilxvhhIvjfvhg(UiznvdlipHvieovg.qzez:8993)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.UiznvdlipHvieovg.wlTvg(UiznvdlipHvieovg.qzez:101)&#10;&#9;zg qzezc.hvieovg.sggk.SggkHvieovg.hvierxv(SggkHvieovg.qzez:470)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.hvieovg.UiznvdlipHvieovg.hvierxv(UiznvdlipHvieovg.qzez:116)&#10;&#9;zg qzezc.hvieovg.sggk.SggkHvieovg.hvierxv(SggkHvieovg.qzez:376)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:790)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.zkzxsv.glnxzg.dvyhlxpvg.hvievi.DhUrogvi.wlUrogvi(DhUrogvi.qzez:48)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:888)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:888)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.rmhgifnvmg.dvy.hvieovg.GizxrmtUrogvi.wlUrogvi(GizxrmtUrogvi.qzez:31)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.zfglxlmurt.rmhgifnvmg.dvy.GizxvDvyHvieovgXlmurtfizgrlm$OzabGizxrmtUrogvi.wlUrogvi(GizxvDvyHvieovgXlmurtfizgrlm.qzez:868)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:888)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmWrhkzgxsvi.rmelpv(ZkkorxzgrlmWrhkzgxsvi.qzez:338)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmWrhkzgxsvi.kilxvhhIvjfvhg(ZkkorxzgrlmWrhkzgxsvi.qzez:574)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmWrhkzgxsvi.wlUlidziw(ZkkorxzgrlmWrhkzgxsvi.qzez:642)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmWrhkzgxsvi.ulidziw(ZkkorxzgrlmWrhkzgxsvi.qzez:705)&#10;&#9;zg xln.hfmbziw.zlh.xlnnlm.urogvi.ZfgsUrogvi.wlUrogvi(ZfgsUrogvi.qzez:890)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg xln.hfmbziw.zlh.xlnnlm.urogvi.XHIUEzorwzgrlmUrogvi.wlUrogvi(XHIUEzorwzgrlmUrogvi.qzez:25)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg xln.hfmbziw.zlh.xlnnlm.urogvi.XsziUrogvi.wlUrogvi(XsziUrogvi.qzez:75)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg xln.hfmbziw.zlh.xlnnlm.urogvi.SvzwviUrogvi.wlUrogvi(SvzwviUrogvi.qzez:68)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.IvjfvhgXlmgvcgUrogvi.wlUrogviRmgvimzo(IvjfvhgXlmgvcgUrogvi.qzez:899)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:882)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.UlinXlmgvmgUrogvi.wlUrogviRmgvimzo(UlinXlmgvmgUrogvi.qzez:06)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:882)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.rmhgifnvmg.dvy.hvieovg.GizxrmtUrogvi.wlUrogvi(GizxrmtUrogvi.qzez:31)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.zfglxlmurt.rmhgifnvmg.dvy.GizxvDvyHvieovgXlmurtfizgrlm$OzabGizxrmtUrogvi.wlUrogvi(GizxvDvyHvieovgXlmurtfizgrlm.qzez:868)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.yllg.zxgfzgv.nvgirxh.dvy.hvieovg.DvyNexNvgirxhUrogvi.wlUrogviRmgvimzo(DvyNexNvgirxhUrogvi.qzez:03)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:882)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.XszizxgviVmxlwrmtUrogvi.wlUrogviRmgvimzo(XszizxgviVmxlwrmtUrogvi.qzez:798)&#10;&#9;zg lit.hkirmtuiznvdlip.dvy.urogvi.LmxvKviIvjfvhgUrogvi.wlUrogvi(LmxvKviIvjfvhgUrogvi.qzez:882)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.rmgvimzoWlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:821)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.ZkkorxzgrlmUrogviXszrm.wlUrogvi(ZkkorxzgrlmUrogviXszrm.qzez:846)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.HgzmwziwDizkkviEzoev.rmelpv(HgzmwziwDizkkviEzoev.qzez:831)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.HgzmwziwXlmgvcgEzoev.rmelpv(HgzmwziwXlmgvcgEzoev.qzez:09)&#10;&#9;zg lit.zkzxsv.xzgzormz.zfgsvmgrxzgli.ZfgsvmgrxzgliYzhv.rmelpv(ZfgsvmgrxzgliYzhv.qzez:518)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.HgzmwziwSlhgEzoev.rmelpv(HgzmwziwSlhgEzoev.qzez:869)&#10;&#9;zg lit.zkzxsv.xzgzormz.ezoevh.ViiliIvkligEzoev.rmelpv(ViiliIvkligEzoev.qzez:06)&#10;&#9;zg lit.zkzxsv.xzgzormz.xliv.HgzmwziwVmtrmvEzoev.rmelpv(HgzmwziwVmtrmvEzoev.qzez:25)&#10;&#9;zg lit.hkirmtuiznvdlip.xolfw.hovfgs.rmhgifnvmg.dvy.glnxzg.GizxvEzoev.rmelpv(GizxvEzoev.qzez:09)&#10;&#9;zg lit.zkzxsv.xzgzormz.xlmmvxgli.XlblgvZwzkgvi.hvierxv(XlblgvZwzkgvi.qzez:657)&#10;&#9;zg lit.zkzxsv.xlblgv.sggk88.Sggk88Kilxvhhli.hvierxv(Sggk88Kilxvhhli.qzez:609)&#10;&#9;zg lit.zkzxsv.xlblgv.ZyhgizxgKilxvhhliOrtsg.kilxvhh(ZyhgizxgKilxvhhliOrtsg.qzez:36)&#10;&#9;zg lit.zkzxsv.xlblgv.ZyhgizxgKilglxlo$XlmmvxgrlmSzmwovi.kilxvhh(ZyhgizxgKilglxlo.qzez:071)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.mvg.MrlVmwklrmg$HlxpvgKilxvhhli.wlIfm(MrlVmwklrmg.qzez:8205)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.mvg.HlxpvgKilxvhhliYzhv.ifm(HlxpvgKilxvhhliYzhv.qzez:47)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.gsivzwh.GsivzwKlloVcvxfgli.ifmDlipvi(GsivzwKlloVcvxfgli.qzez:8808)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.gsivzwh.GsivzwKlloVcvxfgli$Dlipvi.ifm(GsivzwKlloVcvxfgli.qzez:340)&#10;&#9;zg lit.zkzxsv.glnxzg.fgro.gsivzwh.GzhpGsivzw$DizkkrmtIfmmzyov.ifm(GzhpGsivzw.qzez:38)&#10;&#9;zg qzez.ozmt.Gsivzw.ifm(Gsivzw.qzez:251)&#10;代码上下文:&#10;```qzez&#10;/**&#10; * Rgvizgv zoo IvjfvhgNzkkrmtRmul'h lmxv ztzrm, ollp ru zmb nzgxs yb FIO zg&#10; * ovzhg zmw izrhv vcxvkgrlmh zxxliwrmt gl dszg wlvhm'g nzgxs.&#10; * @gsildh SggkIvjfvhgNvgslwMlgHfkkligvwVcxvkgrlm ru gsviv ziv nzgxsvh yb FIO&#10; * yfg mlg yb SGGK nvgslw&#10; * @gsildh SggkNvwrzGbkvMlgZxxvkgzyovVcxvkgrlm ru gsviv ziv nzgxsvh yb FIO&#10; * yfg mlg yb xlmhfnzyov/kilwfxryov nvwrz gbkvh&#10; */&#10;@Leviirwv&#10;kilgvxgvw SzmwoviNvgslw szmwovMlNzgxs(&#10;&#9;&#9;Hvg&lt;IvjfvhgNzkkrmtRmul&gt; rmulh, Hgirmt ollpfkKzgs, SggkHvieovgIvjfvhg ivjfvhg) gsildh HvieovgVcxvkgrlm {&#10;&#10;&#9;ru (XloovxgrlmFgroh.rhVnkgb(rmulh)) {&#10;&#9;&#9;ivgfim mfoo;&#10;&#9;}&#10;&#10;&#9;KzigrzoNzgxsSvokvi svokvi = mvd KzigrzoNzgxsSvokvi(rmulh, ivjfvhg);&#10;&#9;ru (svokvi.rhVnkgb()) {&#10;&#9;&#9;ivgfim mfoo;&#10;&#9;}&#10;&#10;&#9;ru (svokvi.szhNvgslwhNrhnzgxs()) {&#10;&#9;&#9;Hvg&lt;Hgirmt&gt; nvgslwh = svokvi.tvgZooldvwNvgslwh();&#10;&#9;&#9;ru (SggkNvgslw.LKGRLMH.nzgxsvh(ivjfvhg.tvgNvgslw())) {&#10;&#9;&#9;&#9;Hvg&lt;NvwrzGbkv&gt; nvwrzGbkvh = svokvi.tvgXlmhfnzyovKzgxsNvwrzGbkvh();&#10;&#9;&#9;&#9;SggkLkgrlmhSzmwovi szmwovi = mvd SggkLkgrlmhSzmwovi(nvgslwh, nvwrzGbkvh);&#10;&#9;&#9;&#9;ivgfim mvd SzmwoviNvgslw(szmwovi, SGGK_LKGRLMH_SZMWOV_NVGSLW);&#10;&#9;&#9;}&#10;&#9;&#9;gsild mvd SggkIvjfvhgNvgslwMlgHfkkligvwVcxvkgrlm(ivjfvhg.tvgNvgslw(), nvgslwh);&#10;&#9;}&#10;&#10;&#9;ru (svokvi.szhXlmhfnvhNrhnzgxs()) {&#10;&#9;&#9;Hvg&lt;NvwrzGbkv&gt; nvwrzGbkvh = svokvi.tvgXlmhfnzyovNvwrzGbkvh();&#10;&#9;&#9;NvwrzGbkv xlmgvmgGbkv = mfoo;&#10;&#9;&#9;ru (HgirmtFgroh.szhOvmtgs(ivjfvhg.tvgXlmgvmgGbkv())) {&#10;&#9;&#9;&#9;gib {&#10;&#9;&#9;&#9;&#9;xlmgvmgGbkv = NvwrzGbkv.kzihvNvwrzGbkv(ivjfvhg.tvgXlmgvmgGbkv());&#10;&#9;&#9;&#9;}&#10;&#9;&#9;&#9;xzgxs (RmezorwNvwrzGbkvVcxvkgrlm vc) {&#10;&#9;&#9;&#9;&#9;gsild mvd SggkNvwrzGbkvMlgHfkkligvwVcxvkgrlm(vc.tvgNvhhztv());&#10;&#9;&#9;&#9;}&#10;&#9;&#9;}&#10;&#9;&#9;gsild mvd SggkNvwrzGbkvMlgHfkkligvwVcxvkgrlm(xlmgvmgGbkv, mvd ZiizbOrhg&lt;&gt;(nvwrzGbkvh));&#10;&#9;}&#10;&#10;&#9;ru (svokvi.szhKilwfxvhNrhnzgxs()) {&#10;&#9;&#9;Hvg&lt;NvwrzGbkv&gt; nvwrzGbkvh = svokvi.tvgKilwfxryovNvwrzGbkvh();&#10;&#9;&#9;gsild mvd SggkNvwrzGbkvMlgZxxvkgzyovVcxvkgrlm(mvd ZiizbOrhg&lt;&gt;(nvwrzGbkvh));&#10;&#9;}&#10;&#10;&#9;ru (svokvi.szhKziznhNrhnzgxs()) {&#10;&#9;&#9;Orhg&lt;Hgirmt[]&gt; xlmwrgrlmh = svokvi.tvgKziznXlmwrgrlmh();&#10;&#9;&#9;gsild mvd FmhzgrhurvwHvieovgIvjfvhgKziznvgviVcxvkgrlm(xlmwrgrlmh, ivjfvhg.tvgKziznvgviNzk());&#10;&#9;}&#10;&#10;&#9;ivgfim mfoo;&#10;}&#10;```&#10;" />
                <option name="rating" value="UNRATED" />
                <option name="referenceBeans">
                  <list />
                </option>
                <option name="status" value="SUCCESS" />
                <option name="variables">
                  <list />
                </option>
              </Turn>
            </list>
          </option>
          <option name="updateTime" value="1747573138684" />
        </Conversation>
      </list>
    </option>
  </component>
</project>