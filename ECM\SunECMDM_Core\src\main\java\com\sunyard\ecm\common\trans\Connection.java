package com.sunyard.ecm.common.trans;

import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.exception.SunECMException;

import java.io.IOException;
import java.io.OutputStream;

/**
 * 表示跟客户端的通信的一次连接。<p>
 * 提供接受信息，处理信息，发送信息，接受文件，获得客户端IP接口。<p>
 * 此接口屏蔽了底层通信时基于socket或者是http。<p>
 * 
 * <AUTHOR>
 *
 */
public interface Connection {

	/**
	 * 一行一行的读取全部信息
	 * 
	 * @return
	 * @throws IOException
	 * @throws SunECMException
	 */
	public abstract String getMessages() throws IOException;
	public abstract String getHttpMessages() throws IOException;
	public void process();
	public void httpUploadProcess();//用于http上传改造，用同一个连接对象上传批次及文件
	public void httpUpdateProcess();
    
	/**
	 * 发送信息
	 * 
	 * @param str
	 * @return
	 * @throws IOException
	 */
	public abstract boolean sendMessages(String msg) throws IOException;
	
	/**
	 * 发送信息
	 * 
	 * @param str
	 * @return
	 * @throws IOException
	 */
	public abstract boolean sendErrorMessages(int code,String msg) throws IOException;

	public abstract String getClientIp();
	
	public String getConnectionType();


	/**
	 * 接收文件，指定大小的文件接收到输出流中
	 * @param filesize 文件大小
	 * @param out 接收文件的流
	 * @param remain 
	 * @throws IOException 
	 */
	public abstract void reciveFile(long filesize, OutputStream out, FileBean received)
			throws IOException;
	
	public abstract void reciveFile(long filesize, OutputStream out, FileBean received, String encodeLength)
			throws IOException, SunECMException;
	
	public void close();
	public abstract void httpReciveFile(long fileSize, OutputStream out, FileBean fileBean) throws IOException;
	public abstract void httpReciveFile(long fileSize, OutputStream out, FileBean fileBean, String encodeLength) throws IOException, SunECMException;
	
	
}