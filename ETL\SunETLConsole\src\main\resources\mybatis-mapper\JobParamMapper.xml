<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.JobParamDao">
	
	<resultMap id="JobParam" type="com.xxl.job.admin.core.model.JobParam" >
		<result column="id" property="id" />
		<result column="JOBID" property="jobId" />
	    <result column="PARAM_FIELD" property="param_field" />
	    <result column="PARAM_FIELD_CH" property="param_field_ch" />
	    <result column="PARAM_VALUE" property="param_value" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.jobId,
		t.param_field,
		t.param_field_ch,
		t.param_value
	</sql>
	
	<select id="pageList" parameterType="java.util.HashMap" resultMap="JobParam">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_JOB_PARAM t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobId gt 0">
				AND t.JOBID = #{jobId}
			</if>
			<if test="param_field != null and param_field != ''">
				AND t.PARAM_FIELD = #{param_field}
			</if>
		</trim>
		ORDER BY JOBID
	</select>

	<select id="getAllJobParams" parameterType="java.util.HashMap" resultMap="JobParam">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_JOB_PARAM t
		ORDER BY id
	</select>

	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM QRTZ_JOB_PARAM t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobId gt 0">
				AND t.JOBID = #{jobId}
			</if>
			<if test="param_field != null and param_field != ''">
				AND t.PARAM_FIELD = #{param_field}
			</if>
		</trim>
	</select>

	<insert id="save" parameterType="com.xxl.job.admin.core.model.JobParam" useGeneratedKeys="false" keyProperty="id" >
		<selectKey keyProperty = "count"  order = "BEFORE"  resultType = "int">
			SELECT COUNT(*) AS COUNT FROM QRTZ_JOB_PARAM T 
			 WHERE T.JOBID = #{jobId,jdbcType=INTEGER} 
			   AND T.PARAM_FIELD = #{param_field,jdbcType=VARCHAR}
		</selectKey>
		
		<if test = " count > 0">
			UPDATE QRTZ_JOB_PARAM SET 
			ID = #{id,jdbcType=INTEGER}, 
			JOBID = #{jobId,jdbcType=INTEGER},
			PARAM_FIELD = #{param_field,jdbcType=VARCHAR},
			PARAM_VALUE = #{param_value,jdbcType=VARCHAR},
			PARAM_FIELD_CH = #{param_field_ch,jdbcType=VARCHAR}
			WHERE JOBID = #{jobId,jdbcType=INTEGER}  AND PARAM_FIELD = #{param_field,jdbcType=VARCHAR}
		</if>
		
		<if test = " count == 0">
			INSERT INTO QRTZ_JOB_PARAM (
				ID,JOBID, PARAM_FIELD, PARAM_VALUE, PARAM_FIELD_CH
			) VALUES (
				#{id,jdbcType=INTEGER},
				#{jobId,jdbcType=INTEGER},
				#{param_field,jdbcType=VARCHAR},
				#{param_value,jdbcType=VARCHAR},
				#{param_field_ch,jdbcType=VARCHAR}
			)
		</if>
	</insert>

	
	<update id="update" parameterType="com.xxl.job.admin.core.model.JobParam" >
		UPDATE QRTZ_JOB_PARAM
		SET 
			jobId = #{jobId,jdbcType=INTEGER},
			param_field = #{param_field,jdbcType=INTEGER},
			param_field_ch = #{param_field_ch,jdbcType=INTEGER},
			PARAM_VALUE = #{param_value,jdbcType=INTEGER}
		WHERE id = #{id,jdbcType=INTEGER}
	</update>
	
	<delete id="delete" parameterType="java.util.HashMap">
		DELETE
		FROM QRTZ_JOB_PARAM
		WHERE id = #{id}
	</delete>



	<delete id="deleteParam" parameterType="java.util.HashMap">
		DELETE
		FROM QRTZ_JOB_PARAM
		WHERE JOBID = #{jobId}
	</delete>

	
	<select id="getMaxId" resultType="Integer">
		SELECT  nvl(MAX(id),0) 
		FROM QRTZ_JOB_PARAM
	</select>

</mapper>