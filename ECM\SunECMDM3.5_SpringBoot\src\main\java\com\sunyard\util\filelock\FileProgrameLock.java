package com.sunyard.util.filelock;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.nio.channels.OverlappingFileLockException;

import com.sunyard.ecm.server.bean.StoreObjectBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.ecm.server.util.VolumnUtils;
import com.sunyard.exception.SunECMException;
import com.sunyard.util.ecm3_1.StoreObjectUtil;

/**
 * 
 * 文件锁的实现类
 * 
 * <AUTHOR>
 * 
 */
public class FileProgrameLock implements Lock {
	private final static Logger log = LoggerFactory.getLogger(FileProgrameLock.class);
	private String lockFileName = null; // 锁定的文件
	private FileChannel channel = null; // 文件通道
	private FileLock lock = null; // 文件锁

	/**
	 * 构造器，输入需要锁定的文件名和该文件的模型名称
	 * 
	 * @param lockFileName
	 *            即批次号
	 * @param modeCode
	 *            内容模型英文名
	 * @throws SunECMException
	 */
	public FileProgrameLock(String lockFileName, String modeCode)
			throws SunECMException {
		this.lockFileName = getLockFileFullPath(lockFileName, modeCode);
		// this.lockFileName="D://SUNECM//TMP//2014_45_C01ECBC4-1D62-C2B5-E28A-C931912FEB2C-1";

	}

	/**
	 * 获取锁资源
	 * 
	 * @return true成功锁定目标资源 ,false锁定操作失败
	 * */
	public boolean obtain() throws IOException {
		log.debug("obtain-->获取锁文件，新建文件为 [" + lockFileName + "]");
		File tf = new File(lockFileName);

		// if(tf.exists()){
		// return false; //这里要加这个觉得,要不锁这个机制完全没用
		// }
		// 创建需要锁定的文件
		createFile();
		try {
			channel = new RandomAccessFile(tf, "rw").getChannel();
			lock = channel.lock();
			if (lock == null) {
				log.debug(" lock 的一个bug  此时lock文件 为空");
				return false;
			}
			log.debug(
					"--FileProgrameLock-->obtain-->获取锁文件成功，文件 [" + lockFileName
							+ "] 已独占使用");
			return true;
		} catch (OverlappingFileLockException e) {
			// log.debug("--FileProgrameLock-->obtain-->获取锁文件失败，文件 ["
			// + lockFileName + "] 已被别的进程使用");
			log.warn("--FileProgrameLock-->obtain-->获取锁文件失败，文件 ["
					+ lockFileName + "] 已被别的进程使用");
			return false;
		} catch (Exception e) {
			// Log4JUtil.logWarning(log,
			// "--FileProgrameLock-->obtain-->获取锁文件失败，文件 [" + lockFileName +
			// "] 发生异常状况,异常信息为: " + e.toString());
			log.error("--FileProgrameLock-->obtain-->获取锁文件失败，文件 ["
					+ lockFileName + "] 发生异常状况,异常信息为: ", e);
			return false;
		}finally{
			if (channel != null && channel.isOpen()) {
				channel.close();
			}
			channel = null;
		}
	}

	/**
	 * 释放锁
	 * */
	public void unlock() {
		log.debug("--FileProgrameLock-->unlock-->释放锁文件，文件为 [" + lockFileName
				+ "]");
		try {
			if (lock != null) {
				lock.release();
			}
			if (channel != null && channel.isOpen()) {
				channel.close();
			}
			lock = null;
			channel = null;
			this.deleteFile();
		} catch (IOException e) {
			log.warn(
					"--FileProgrameLock-->unlock-->释放锁文件异常，文件为 ["
							+ lockFileName + "], 异常信息为:" + e.toString());
		}
	}

	/**
	 * 获取锁文件的全路径,在内容模型保存的卷下建立一个临时文件夹
	 * 
	 * @return
	 * @throws SunECMException
	 */
	private static String getLockFileFullPath(String fileName,
			String modeCodeName) throws SunECMException {
		// log.debug( "getLockFileFullPath-->获取锁文件的全路径");

		// 获取内容模型所配置的卷信息
		StoreObjectBean storeObjectBean = StoreObjectUtil
				.getStoreObj(modeCodeName);
		// 文件路径为根路径+存储路径
		// 示例如：E:\sunECM\(storeObjectBean中获取)TMP（自定义）\+lockFileName
//		String filePath = storeObjectBean.getRoot_path()+ storeObjectBean.getSave_path();
		String filePath = VolumnUtils.getsavePath(storeObjectBean.getRoot_path(), storeObjectBean.getSave_path());
		String file = filePath + "TMP" + File.separator + fileName;
		log.debug( "getLockFileFullPath-->内容模型:[" + modeCodeName
				+ "] ,批次ID:[" + fileName + "] ,锁文件路径:[" + file + "]");
		//storeObjectBean = null;
		//filePath = null;
		return file;
	}

	/**
	 * 创建文件，先判断file名是否路径,若为路径则删除后再创建
	 * 
	 * @throws IOException
	 */
	private void createFile() throws IOException {
		// log.debug(
		// "--FileProgrameLock-->createFile-->创建锁文件,创建路径路径为 :" + lockFileName );
		try {
			File tf = new File(lockFileName);
			File parent = tf.getParentFile();
			if (!parent.exists()) {
				// log.debug( "createFile-->锁文件的临时路径不存在,创建路径路径为 ["
				// + parent.getCanonicalPath() + "]");
				parent.mkdirs();
			}
			if (!tf.exists()) {
				tf.createNewFile();
			} else {
				// 如果该文件名为一个路径则删除该路径
				if (tf.isDirectory()) {
					tf.delete();
				}
				// 创建文件
				tf.createNewFile();
			}
		//	tf = null;
		} catch (IOException e) {
			log.error( "createFile-->创建锁文件异常，文件为 [" + lockFileName
					+ "]:" , e);
			throw e;
		}
	}

	/**
	 * 
	 * 释放锁文件后删除文件
	 * 
	 */
	private void deleteFile() {
		log.debug("--FileProgrameLock-->deleteFile-->删除锁文件,文件为 :"
				+ lockFileName);
		File tf = new File(lockFileName);
		if (tf.exists()) {
			tf.delete();
		}
	//	tf = null;
	}

}