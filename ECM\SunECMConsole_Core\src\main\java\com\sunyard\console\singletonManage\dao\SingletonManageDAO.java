package com.sunyard.console.singletonManage.dao;

import com.sunyard.console.singletonManage.bean.LazySingletonBean;
import com.sunyard.exception.SunECMException;

public interface SingletonManageDAO {

	public LazySingletonBean getDMInfo(String ip, String port) throws SunECMException;

	public LazySingletonBean regetDMInfo(String ip, String port) throws SunECMException;
	
	public LazySingletonBean getUAInfo(String ip, String port) throws SunECMException;

	public LazySingletonBean regetUAInfo(String ip, String port) throws SunECMException;
}
