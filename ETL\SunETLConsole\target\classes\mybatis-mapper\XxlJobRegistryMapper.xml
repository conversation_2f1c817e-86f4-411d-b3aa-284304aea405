<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobRegistryDao">
	
	<resultMap id="XxlJobRegistry" type="com.xxl.job.admin.core.model.XxlJobRegistry" >
		<result column="id" property="id" />
	    <result column="registry_group" property="registryGroup" />
	    <result column="registry_key" property="registryKey" />
	    <result column="registry_value" property="registryValue" />
		<result column="update_time" property="updateTime" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.registry_group,
		t.registry_key,
		t.registry_value,
		t.update_time
	</sql>
	
	<delete id="removeDead" parameterType="java.lang.Integer" >
		DELETE FROM QRTZ_TRIGGER_REGISTRY t
		WHERE ROUND(TO_NUMBER(sysdate - t.update_time) * 24 * 60 * 60) <![CDATA[ > ]]> #{timeout}
	</delete>

	<select id="findAll" parameterType="java.lang.Integer" resultMap="XxlJobRegistry">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_REGISTRY t
		WHERE ROUND(TO_NUMBER(sysdate - t.update_time) * 24 * 60 * 60) <![CDATA[ < ]]> #{timeout}

		
	</select>

    <update id="registryUpdate" >
        UPDATE QRTZ_TRIGGER_REGISTRY
        SET update_time = sysdate
        WHERE registry_group = #{registryGroup}
          AND registry_key = #{registryKey}
          AND registry_value = #{registryValue}
    </update>

    <insert id="registrySave" >
        INSERT INTO QRTZ_TRIGGER_REGISTRY( registry_group , registry_key , registry_value, update_time)
        VALUES( #{registryGroup}  , #{registryKey} , #{registryValue}, sysdate)
    </insert>

</mapper>