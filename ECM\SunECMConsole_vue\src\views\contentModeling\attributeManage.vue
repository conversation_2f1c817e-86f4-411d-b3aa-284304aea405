<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.attributeCode"
        placeholder="属性代码"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.attributeName"
        placeholder="属性名称"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />

      <el-button
        v-if="this.hasPerm('attributeSearch')"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain
        round
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain
        round
        @click="handleclear"
      >
        清空
      </el-button>
      <el-button
        v-if="this.hasPerm('addAttribute')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="属性代码" min-width="16%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.attribute_code }}</span>
        </template>
      </el-table-column>

      <el-table-column label="属性名称" min-width="16%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.attribute_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="属性类型" min-width="14%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.attribute_type_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="属性长度" min-width="12%" align="center" >
        <template slot-scope="{ row }">
          <span>{{ row.attribute_length }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否为空" min-width="10%" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.attribute_isNull == 0">否</span>
          <span v-if="row.attribute_isNull == 1">是</span>
        </template>
      </el-table-column>

      <el-table-column label="默认值" min-width="10%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.attribute_default }}</span>
        </template>
      </el-table-column>
      
      <el-table-column label="脱敏规则" min-width="10%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.desensitive_rule }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        min-width="22%"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <el-button v-if="hasPerm('modifyAttribute')"  type="primary"  icon="el-icon-edit" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
          <el-button
            v-if=" hasPerm('delAttribute') && row.status != 'deleted'"
            size="mini"
            icon="el-icon-delete" type="danger"
            @click="handleDelete(row, $index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="属性代码" prop="attribute_code">
          <el-input
            v-model="temp.attribute_code"
            :disabled="attribute_code_status ? true : false"
            prop="attribute_code"
            onkeyup="value=value.replace(/[\u4e00-\u9fa5]/g,'')"
          />
        </el-form-item>
        <el-form-item label="属性名称" prop="attribute_name">
          <el-input v-model="temp.attribute_name" />
        </el-form-item>
        <el-form-item label="字段类型" prop="attribute_type">
          <el-select
            v-model="temp.attribute_type"
            placeholder="请选择类型"
            @change="getCategoryOptions"
          >
            <el-option
              v-for="at in loadAttributeTypes"
              :key="at.app_type_id"
              :label="at.app_type_name"
              :value="at.app_type_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="属性长度" v-if="attrTypesVisible">
          <el-input v-model="temp.attribute_length" type="number" min="0" step="1" />

          <!-- <el-input value =0 onkeyup="value=value.replace(/[^\d]/g,'')"   v-model="temp.attribute_length" placeholder="只能是数字" /> -->
        </el-form-item>
        <el-form-item label="精度" v-if="attrTypesVisible1">
          <el-input-number v-model.number="temp.precision"/>


        </el-form-item>
        <el-form-item label="小数长度" v-if="attrTypesVisible1">
          <el-input-number v-model.number="temp.decimals"/>


        </el-form-item>
        <el-form-item label="是否为空" prop="attribute_isNull">
          <el-select v-model="temp.attribute_isNull" placeholder="请选择类型">
            <el-option
              v-for="at in canNull"
              :key="at.key"
              :label="at.display_name"
              :value="at.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="默认值">
          <el-input v-model="temp.attribute_default" />
        </el-form-item>
        <el-form-item label="脱敏规则" prop="desensitive_rule">
        <el-select v-model="temp.desensitive_rule" placeholder="请选择脱敏规则">
           <el-option
              v-for="at in loadDesensitiveRules"
              :key="at.desrule_id"
              :label="at.desrule_name"
              :value="at.desrule_id"
            />
        </el-select>
      </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAttrList,
  addAttribute,
  updateAttribte,
  deleteAttr,
  getAttrTypeList,
  getDesensitiveRuleList
} from "@/api/attribute";

import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import {checkEnglish } from '@/utils/validate.js'

import global from "../../store/global.js";
const canNull = [
  { key: "1", display_name: "是" },
  { key: "0", display_name: "否" },
];

export default {
  name: "attributeManage",
  components: { Pagination },
  directives: { waves,elDragDialog },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        attributeCode:'',
        attributeName:''
      },
      temp: {
        attribute_type: "12",
        attribute_default: "",
        attribute_length: "10",
        attribute_isNull: "1",
        desensitive_rule: "99",
        precision:"",
        decimals:""
      },
      loadAttributeTypes: [],
      canNull,
      dialogFormVisible: false,
      dialogStatus: "",
      textMap: {
        update: "修改属性",
        create: "新增属性",
      },
      rawData:[],
      dialogPvVisible: false,
      rules: {
        attribute_code: [
          { required: true, message: global.regexCodeText, pattern:global.regexCode,trigger: "change" },
        ],
        attribute_name: [
          { required: true, message: global.regexNameText, pattern:global.regexName,trigger: "change" },
        ],
        attribute_type: [
          { required: true, message: "属性类型必输", trigger: "blur" },
        ],
        attribute_length: [
          { required: true, message: "属性长度必输", trigger: "blur" },
        ],
      },
      downloadLoading: false,
      attribute_code_status: false,
      attrTypesVisible: true,
      attrTypesVisible1: false,
    };
  },

  created() {
    this.loadFieldType();
    this.loadDesenRule();
    this.getList();
  },

  methods: {
    getCategoryOptions(id) {
      // alert(id);
      if (id == 4 ) {
        this.attrTypesVisible = false;
        this.attrTypesVisible1 = false;
      } else if(id ==3) {
        this.attrTypesVisible1 = true;
        this.attrTypesVisible = false;
      }else{
       this.attrTypesVisible = true;
       this.attrTypesVisible1 = false;
      }
    },

    loadFieldType() {
      getAttrTypeList().then((response) => {
        this.loadAttributeTypes = response.root;
        let globalAttributeMapping = {};
        for (var index = 0; index < this.loadAttributeTypes.length; index++) {
          globalAttributeMapping[
            this.loadAttributeTypes[index]["app_type_id"]
          ] = this.loadAttributeTypes[index]["app_type_name"];
        }
        global.attributeMapping = globalAttributeMapping;
      });
    },

    loadDesenRule() {
      getDesensitiveRuleList().then((response) => {
        this.loadDesensitiveRules = response.root;
        let globalDesensitiveMapping = {};
        for (var index = 0; index < this.loadDesensitiveRules.length; index++) {
          globalDesensitiveMapping[
            this.loadDesensitiveRules[index]["desrule_id"]
          ] = this.loadDesensitiveRules[index]["desrule_name"];
        }
        global.desensitiveMapping = globalDesensitiveMapping;
      });
    },


    getList() {
      this.listLoading = true;
      this.attrTypesVisible = true;
      this.attrTypesVisible1 = false;
      getAttrList(this.listQuery).then((response) => {
        this.list = response.root;
        for (var i = 0; i < this.list.length; i++) {
          this.list[i].attribute_type_name =
            global.attributeMapping[this.list[i].attribute_type];
          this.list[i].desensitive_rule = 
            global.desensitiveMapping[this.list[i].attribute_desenrule]
        }
        this.total = Number(response.totalProperty);

        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },
    handleclear() {
      this.listQuery.attributeCode = "";
      this.listQuery.attributeName = "";
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },

    resetTemp() {
      this.temp = {
        attribute_type: "12",
        attribute_default: "",
        attribute_length: "10",
        attribute_isNull: "1",
        desensitive_rule: "99"
      };
    },
    handleCreate() {
      this.resetTemp();
      this.attrTypesVisible = true;
      this.attrTypesVisible1 = false;
      this.attribute_code_status = false;
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    //提交
    createData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          if (!this.checkAttributeType(this.temp)) {
            return;
          }
          if(!checkEnglish(this.temp.attribute_code)){
            alert("属性名称应以字母开头,且不能包含中文");
            return;
          }
          if(this.temp.decimals>this.temp.precision){
            alert("小数位长度必须小于精度");
            return;
          }
          addAttribute(this.temp).then(() => {
            // this.list.unshift(this.temp)
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Created Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },
    handleUpdate(row) {
      if(row.attribute_type==4){
        this.attrTypesVisible = false;
        this.attrTypesVisible1 = false;
      }else if(row.attribute_type==3) {
        this.attrTypesVisible1 = true;
        this.attrTypesVisible = false;
        var result = row.attribute_length.split(",");
        row.precision = result[0];
        row.decimals = result[1];
      }else{
        this.attrTypesVisible = true;
        this.attrTypesVisible1 = false;
      }
      this.attribute_code_status = true;
      this.temp = Object.assign({}, row); // copy obj
      this.temp.timestamp = new Date(this.temp.timestamp);
      this.dialogStatus = "update";
      this.rawData = {...this.temp};
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    //更新
    updateData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp);
          tempData.onlyDesensitive = true;
          if (!this.checkAttributeType(tempData)) {
            return;
          }
          if(tempData.attribute_type==3 && tempData.precision < tempData.decimals){
            this.$message.warning("小数位长度必须小于精度");
            return;
          }
          Object.keys(this.rawData).forEach(item => {
            if(this.rawData[item] !== this.temp[item]){
                if(item !=  "desensitive_rule"){
                  tempData.onlyDesensitive = false;
                }
            }
          })
          updateAttribte(tempData).then(() => {
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Update Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },
    checkAttributeType(tempData) {
      let type = tempData.attribute_type;
      let length = tempData.attribute_length;
      let dd = tempData.attribute_default;
      switch (type) {
        case "12":
          if (length == null || length == 0) {
            this.$message.warning("字段类型是字符串，请输入字段长度");
            return false;
          } else {
            return true;
          }
        // break;
        case "1":
          if (length == null || length == 0) {
            this.$message.warning("字段类型是字符串，请输入字段长度");
            return false;
          } else {
            return true;
          }
        // break;
        case "3":
          if (length == null || length == 0) {
            this.$message.warning("字段类型是浮点数，小数位长度必须小于精度");
            return false;
          } else {
            return true;
          }
        // break;
        case "4":
          if (dd == null || dd.length == 0) {
            tempData.attribute_default = "";
            return true;
          } else if (!this.checkInt(dd)) {
            this.$message.warning("字段类型是整形，默认值只能输入数字");
            return false;
          } else {
            return true;
          }
        // break;
        default:
          if (length == null || length == 0) {
            tempData.attribute_length = 0;
            return false;
          } else {
            return true;
          }
      }
    },
    checkInt(a) {
      let reg = /^[0-9]+$/;
      if (!reg.test(a)) {
        return false;
      } else {
        return true;
      }
    },
    handleDelete(row) {
      this.openDelConfirm().then(() => {
        deleteAttr(row).then(() => {
          this.getList();
          this.$notify({
              title: "Success",
              message: "Delete Successfully",
              type: "success",
              duration: 2000,
            });
        });
      })
    },
    openDelConfirm(){
      return this.$confirm(`是否确定删除？`,'提示',{
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      })
    },
  },
};
</script>
