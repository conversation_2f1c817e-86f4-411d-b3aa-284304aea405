<template>
  <div>
       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="关联内容对象" :visible.sync="RelContentObjPage1Visible" width="1200px" >
           <div class="edit_dev"> 
            <el-transfer
            style="text-align: left; display: inline-block"
            v-model="RelContentObjTree"
            filterable
            :left-default-checked="[1]"
            :right-default-checked="[2]"
            :titles="['未关联的内容对象', '已关联的内容对象']"
            :button-texts="['删除', '添加']"
            :format="{
               noChecked: '${total}',
               hasChecked: '${checked}/${total}',
            }"
            :data="UnRelContentObjTree"
          >
          </el-transfer>
        </div>  
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="RelContentObjPage1Visible = false"> 取消 </el-button>
             <el-button size="mini" type="primary" @click="handlePostModel()">提交 </el-button>
          </div>
       </el-dialog>
  </div>
</template>


<style>
.transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
}
</style>

<script>
import {getUnRelContentObjectTree,getRelContentObjectTree,updateTagModels} from "@/api/tagManage";
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "tag-model-rel",
  directives: { elDragDialog },
  props: {
    tagCode: {
      required: false,
      type: String,
    },
    tagName: {
      required: false,
      type: String,
    }
  },
  data: function () {
    return {
      UnRelContentObjTree: [],
      RelContentObjTree: [],
      AlreadyChoice: [],
      RelContentObjPage1Visible: false,
      listLoading: true,
      tCode:'',
      tName:''
    };
  },
  created() {},
  methods: {
    show() {
      this.RelContentObjPage1Visible = true;
    },
    hide() {
      this.RelContentObjPage1Visible = false;
    },
    handlePostModel() {
      var choiceKeys = "";
      for (var i = 0; i < this.RelContentObjTree.length; i++) {
        if(this.AlreadyChoice.indexOf(this.RelContentObjTree[i])<0){
          if (choiceKeys.length > 0) {
            choiceKeys += ",";
          }
          choiceKeys += this.RelContentObjTree[i];
        }
      }

      this.$message.info("提交中...");
        updateTagModels({
          tag_code: this.tCode,
          model_ids: choiceKeys
        }).then(() => {
          this.hide();
          this.$notify({
            title: "Success",
            message: "Created Successfully",
            type: "success",
            duration: 1000,
          });
          this.$emit("refreshModel",this.tCode,this.tName);
        });

    },
    getAllModelsTree(tagCode,tagName) {   
      this.UnRelContentObjTree = [];
      this.RelContentObjTree = [];
      this.AlreadyChoice = [];
      this.tCode = tagCode;
      this.tName = tagName;
        getUnRelContentObjectTree(tagCode).then((response) => {
          let unRelContentObjectTreeResult = response.root;
          unRelContentObjectTreeResult.map((item) => {
            this.UnRelContentObjTree.push({
              key: item.model_code,
              label: item.model_name
            });
          });
        });
        getRelContentObjectTree(tagCode).then((response) => {
          let RelContentObjectTreeResult = response.root;
          RelContentObjectTreeResult.map((item) => {
            this.UnRelContentObjTree.push({
              key: item.model_code,
              label: item.model_name,
              disabled :true
            });
            this.AlreadyChoice.push(item.model_code);
            this.RelContentObjTree.push(item.model_code);
          });
        });
      }
  }
};
</script>
<style scoped>
.edit_dev >>> .el-transfer-panel {
     width:350px;
   }
</style>