<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sunyard.ars.risk.dao.arms.ArmsExhibitDataMapper">

    <!-- 获取所有模型信息 -->
    <select id="getAllModelInfoDao" resultType="java.util.HashMap">
        SELECT
            a.id as MODEL_ID,
            a.show_name as MODEL_NAME,
            a.privname as MODEL_PRIV,
            b.table_name as TABLE_NAME,
            a.model_check_way as MODEL_CHECK_WAY,
            a.model_data_check_way as MODEL_DATA_CHECK_WAY,
            a.relating_id as RELATING_MODEL_ID,
            a.model_type as MODEL_TYPE,
            CASE WHEN EXISTS (
                SELECT 1 FROM MC_MODEL_TB m
                WHERE m.relating_id = a.id
            ) THEN 1 ELSE 0 END as IS_RELATE_MODEL
        FROM
            MC_MODEL_TB a,
            MC_TABLE_TB b
        WHERE
            a.table_id = b.id
            AND a.status = 1
            AND (a.model_type = 0 OR a.model_type = 1)
        ORDER BY
            a.name
    </select>

    <!-- 获取预警统计数据 -->
    <select id="getRiskWarningStatistics" parameterType="java.util.Map" resultType="java.util.HashMap">
        SELECT
            COALESCE(main.NOT_DEALED_COUNT, 0) as NOT_DEALED_COUNT,
            COALESCE(main.HAVE_DEALED_COUNT, 0) as HAVE_DEALED_COUNT,
            COALESCE(main.SUPERVISE_PASS_COUNT, 0) as SUPERVISE_PASS_COUNT,
            COALESCE(main.SUPERVISE_SLIP_COUNT, 0) as SUPERVISE_SLIP_COUNT,
            COALESCE(back.BACKCOUNT, 0) as BACKCOUNT,
            main.MODEL_ID
        FROM (
            SELECT
                sum(a.NOT_DEALED_COUNT) as NOT_DEALED_COUNT,
                sum(a.HAVE_DEALED_COUNT) as HAVE_DEALED_COUNT,
                sum(a.SUPERVISE_PASS_COUNT) as SUPERVISE_PASS_COUNT,
                sum(a.SUPERVISE_SLIP_COUNT) as SUPERVISE_SLIP_COUNT,
                a.MODEL_ID
            FROM
                SUPERVISE_STATISTIC_TB a,
                SM_USER_ORGAN_TB b
            WHERE
                1=1
                <if test="model_id != null and model_id != ''">
                    AND a.MODEL_ID = #{model_id}
                </if>
                <if test="start_date != null and start_date != ''">
                    AND a.OCCUR_DATE &gt;= #{start_date}
                </if>
                <if test="end_date != null and end_date != ''">
                    AND a.OCCUR_DATE &lt;= #{end_date}
                </if>
                <if test="query_busi_organ != null and query_busi_organ.size() > 0">
                    AND a.SITE_NO IN
                    <foreach item="item" collection="query_busi_organ" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="site_no != null and site_no.size() > 0">
                    AND a.SITE_NO IN
                    <foreach item="item" collection="site_no" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                AND a.site_no = b.organ_no
                AND b.user_no = #{userNo}
                AND a.user_no = b.user_no
                <if test="hasPrivOrganFlag != null and hasPrivOrganFlag == '0'">
                    AND a.SITE_NO LIKE CONCAT(#{organNoStr}, '%')
                </if>
            GROUP BY a.MODEL_ID
        ) main
        LEFT JOIN (
            SELECT
                COUNT(*) as BACKCOUNT,
                CAST(model_id AS NUMERIC) as MODEL_ID
            FROM mc_model_zjd
            WHERE state = '3'
                AND user_no_s = #{userNo}
                <if test="model_id != null and model_id != ''">
                    AND CAST(model_id AS NUMERIC) = #{model_id}
                </if>
                <if test="start_date != null and start_date != ''">
                    AND business_date &gt;= #{start_date}
                </if>
                <if test="end_date != null and end_date != ''">
                    AND business_date &lt;= #{end_date}
                </if>
            GROUP BY model_id
        ) back ON main.MODEL_ID = back.MODEL_ID
        ORDER BY main.MODEL_ID
    </select>

    <!-- 获取历史预警统计数据 -->
    <select id="getHistoryRiskWarningStatistics" parameterType="java.util.Map" resultType="java.util.HashMap">
        <if test="modelInfos != null and modelInfos.size() > 0">
            <foreach item="modelInfo" collection="modelInfos" open="" separator="UNION ALL" close="">
                <if test="modelInfo != null and modelInfo.modelId != null and modelInfo.modelName != null and modelInfo.tableName != null">
                    SELECT
                        ${modelInfo.modelId} as MODEL_ID,
                        '${modelInfo.modelName}' as MODEL_NAME,
                        COUNT(*) as TOTAL
                    FROM
                        ${modelInfo.tableName}
                    WHERE
                        list_flag = 0
                        AND model_id = ${modelInfo.modelId}
                         <if test="start_date != null and start_date != ''">
                            AND OCCUR_DATE &gt;= #{start_date}
                        </if>
                        <if test="end_date != null and end_date != ''">
                            AND OCCUR_DATE &lt;= #{end_date}
                        </if>
                        <if test="site_no != null and site_no.size() > 0">
                            AND SITE_NO IN
                            <foreach item="item" collection="site_no" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                        <if test="condition != null and condition != ''">
                            ${condition}
                        </if>
                    <!--<if test="privOrgans != null and privOrgans[0] != null and privOrgans[0] != ''">
                        AND site_no in(${privOrgans[0]})
                    </if>
                    <if test="privOrgans == null or privOrgans[0] == null or privOrgans[0] == ''">
                        AND 1=2
                    </if>-->
                </if>
            </foreach>
        </if>
        <if test="modelInfos == null or modelInfos.size() == 0">
            SELECT 0 as MODEL_ID, 'No Models' as MODEL_NAME, 0 as TOTAL WHERE 1=0
        </if>
    </select>

    <!-- 获取预警明细数据 -->
    <select id="getRiskWarningDetailData" parameterType="java.util.Map" resultType="java.util.HashMap">
        SELECT
            <if test="exhibitFieldInfos != null and exhibitFieldInfos.size() > 0">
                <foreach item="field" collection="exhibitFieldInfos" separator=",">
                    <choose>
                        <when test="field.type == 'CHAR' or field.type == 'VARCHAR'">
                            cast(${field.name} as varchar(30)) as ${field.name}
                        </when>
                        <when test="field.type == 'DATE'">
                            cast(${field.name} as varchar(8)) as ${field.name}
                        </when>
                        <when test="field.type == 'TIME'">
                            cast(${field.name} as varchar(6)) as ${field.name}
                        </when>
                        <otherwise>
                            ${field.name}
                        </otherwise>
                    </choose>
                </foreach>,flow_id,ishandle,modelrow_id
            </if>
            <if test="exhibitFieldInfos == null or exhibitFieldInfos.size() == 0">
                *
            </if>
        FROM
            ${modelInfo.tableName}
        WHERE
            1=1
            <if test="model_id != null and model_id != ''">
                AND MODEL_ID = #{model_id}
            </if>
            <if test="model_row_id != null and model_row_id != ''">
                AND MODELROW_ID = #{model_row_id}
            </if>
            <if test="ishandleList != null and ishandleList.size() > 0">
                AND ISHANDLE IN
                <foreach collection="ishandleList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ishandleList == null and ishandle != null and ishandle != ''">
                AND ISHANDLE = #{ishandle}
            </if>
            <if test="userNo != null and userNo != ''">
                AND trim(USER_NO) = #{userNo}
            </if>
            <if test="start_date != null and start_date != ''">
                AND OCCUR_DATE &gt;= #{start_date}
            </if>
            <if test="end_date != null and end_date != ''">
                AND OCCUR_DATE &lt;= #{end_date}
            </if>
            <if test="site_no != null and site_no.size() > 0">
                AND SITE_NO IN
                <foreach item="item" collection="site_no" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="modelInfo.modelType == 1">
                AND IMAGE_STATE = '1'
            </if>
            <if test="privOrgans != null and privOrgans.length > 0 and privOrgans[0] != null and privOrgans[0] != ''">
                AND site_no in(${privOrgans[0]})
            </if>
            <!--<if test="privOrgans == null or privOrgans.length == 0 or privOrgans[0] == null or privOrgans[0] == ''">
                AND 1=2
            </if>-->
        ORDER BY
            occur_date, site_no, OPERATOR_NO
    </select>

    <!-- 获取历史预警明细数据 -->
    <select id="getHistoryRiskWarningDetailData" parameterType="java.util.Map" resultType="java.util.HashMap">
        SELECT
            <if test="exhibitFieldInfos != null and exhibitFieldInfos.size() > 0">
                <foreach item="field" collection="exhibitFieldInfos" separator=",">
                    <choose>
                        <when test="field.type == 'CHAR' or field.type == 'VARCHAR'">
                            cast(${field.name} as varchar(30)) as ${field.name}
                        </when>
                        <when test="field.type == 'DATE'">
                            cast(${field.name} as varchar(8)) as ${field.name}
                        </when>
                        <when test="field.type == 'TIME'">
                            cast(${field.name} as varchar(6)) as ${field.name}
                        </when>
                        <otherwise>
                            ${field.name}
                        </otherwise>
                    </choose>
                </foreach>,flow_id,ishandle,modelrow_id
            </if>
            <if test="exhibitFieldInfos == null or exhibitFieldInfos.size() == 0">
                *
            </if>
        FROM
            ${modelInfo.tableName}
        WHERE
            1=1
            <if test="model_id != null and model_id != ''">
                AND MODEL_ID = #{model_id}
            </if>
            <if test="model_row_id != null and model_row_id != ''">
                AND MODELROW_ID = #{model_row_id}
            </if>
            <if test="ishandleList != null and ishandleList.size() > 0">
                AND ISHANDLE IN
                <foreach collection="ishandleList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ishandleList == null and ishandle != null and ishandle != ''">
                AND ISHANDLE = #{ishandle}
            </if>
            <if test="userNo != null and userNo != ''">
                AND trim(USER_NO) = #{userNo}
            </if>
            <if test="start_date != null and start_date != ''">
                AND OCCUR_DATE &gt;= #{start_date}
            </if>
            <if test="end_date != null and end_date != ''">
                AND OCCUR_DATE &lt;= #{end_date}
            </if>
            <if test="site_no != null and site_no.size() > 0">
                AND SITE_NO IN
                <foreach item="item" collection="site_no" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="modelInfo.modelType == 1">
                AND IMAGE_STATE = '1'
            </if>
            <if test="privOrgans != null and privOrgans.length > 0 and privOrgans[0] != null and privOrgans[0] != ''">
                AND site_no in(${privOrgans[0]})
            </if>
            <!--<if test="privOrgans == null or privOrgans.length == 0 or privOrgans[0] == null or privOrgans[0] == ''">
                AND 1=2
            </if>-->
        ORDER BY
            ALERT_TIME DESC, OCCUR_DATE DESC
    </select>

    <!-- 批量更新预警状态 -->
    <update id="batchUpdateRiskWarningStatus" parameterType="java.util.Map">
        UPDATE
            ${tableName}
        SET
            ISHANDLE = #{ishandle},
            ALERT_USER = #{alertUser},
            ALERT_USERNAME = #{alertUsername},
            ALERT_DATE = #{alertDate},
            ALERT_TIME = #{alertTime}
        WHERE
            MODEL_ID = #{model_id}
            AND MODELROW_ID IN
            <foreach collection="modelrow_ids" item="modelrow_id" open="(" separator="," close=")">
                #{modelrow_id}
            </foreach>
            <if test="site_no != null and site_no != ''">
                AND SITE_NO = #{site_no}
            </if>
            <if test="start_date != null and start_date != ''">
                AND BUSI_DATA_DATE &gt;= #{start_date}
            </if>
            <if test="end_date != null and end_date != ''">
                AND BUSI_DATA_DATE &lt;= #{end_date}
            </if>
    </update>

    <!-- 插入预警差错记录 -->
    <insert id="insertRiskWarningError" parameterType="java.util.Map">
        INSERT INTO MC_MODEL_ERROR_TB (
            MODEL_ID, MODELROW_ID, OCCUR_DATE,
            SITE_NO, OPERATOR_NO, FLOW_ID,
            BUSI_DATA_DATE, CREATE_USER, CREATE_TIME
        ) VALUES (
            #{model_id}, #{modelrow_id}, #{occur_date},
            #{site_no}, #{operator_no}, #{flow_id},
            #{busi_data_date}, #{create_user}, #{create_time}
        )
    </insert>

    <!-- 获取单条模型数据 -->
    <select id="getModelDataToOne" parameterType="java.util.Map" resultType="java.util.HashMap">
        SELECT
            *
        FROM
            ${tableName}
        WHERE
            MODEL_ID = #{model_id}
            AND MODELROW_ID = #{modelrow_id}
    </select>

    <!-- 解锁预警记录 -->
    <delete id="unlockRiskWarningRecord" parameterType="java.util.Map">
        DELETE FROM
            ARMS_TASK_LOCK_TB
        WHERE
            MODEL_ID = #{model_id}
            AND MODELROW_ID = #{modelrow_id}
            AND USER_NO = #{user_no}
    </delete>

    <!-- 根据模型ID获取模型信息 -->
    <select id="getModelInfoById" parameterType="string" resultType="java.util.HashMap">
        SELECT
            a.id as MODEL_ID,
            a.show_name as MODEL_NAME,
            a.privname as MODEL_PRIV,
            b.table_name as TABLE_NAME,
            a.model_check_way as MODEL_CHECK_WAY,
            a.model_data_check_way as MODEL_DATA_CHECK_WAY,
            a.relating_id as RELATING_MODEL_ID,
            a.model_type as MODEL_TYPE
        FROM
            MC_MODEL_TB a,
            MC_TABLE_TB b
        WHERE
            a.table_id = b.id
            AND a.id = #{modelId}
    </select>

    <!-- 获取模型展示字段 -->
    <select id="getExhibitFields" parameterType="java.util.Map" resultType="java.util.HashMap">
        SELECT
            a.ID,
            a.NAME,
            a.CH_NAME,
            a.TYPE,
            c.ROWNO,
            c.FORMAT,
            c.ISFIND,
            c.ISDROPDOWN,
            c.ISIMPORTANT,
            c.RELATE_ID,
            a.TABLE_TYPE
        FROM
            MC_FIELD_TB a,
            MC_EXHIBIT_FIELD_TB c
        WHERE
            a.ID = c.TABLE_FIELD_ID
            <if test="modelId != null">
                AND c.MODEL_ID = #{modelId}
                <!-- 根据模型类型判断 -->
                <if test="modelInfo != null and modelInfo.modelType == 1">
                    AND a.TABLE_TYPE IN(3,5)
                </if>
                <if test="modelInfo == null or modelInfo.modelType != 1">
                    AND a.TABLE_TYPE IN(2,5)
                </if>
            </if>
        ORDER BY
            c.ROWNO
    </select>

    <!-- 根据条件查询统计数量 -->
    <select id="selectCountSame" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            ${tableName}
        WHERE
            1=1
            <if test="model_id != null and model_id != ''">
                AND MODEL_ID = #{model_id}
            </if>
            <if test="modelrow_id != null and modelrow_id != ''">
                AND MODELROW_ID = #{modelrow_id}
            </if>
            <if test="site_no != null and site_no != ''">
                AND SITE_NO = #{site_no}
            </if>
            <if test="busi_data_date != null and busi_data_date != ''">
                AND BUSI_DATA_DATE = #{busi_data_date}
            </if>
            <if test="flow_id != null and flow_id != ''">
                AND FLOW_ID = #{flow_id}
            </if>
    </select>

    <!-- 查询关联模型 -->
    <select id="selectRelateModel" parameterType="string" resultType="java.util.HashMap">
        SELECT
            a.id as MODEL_ID,
            a.show_name as MODEL_NAME,
            a.privname as MODEL_PRIV,
            b.table_name as TABLE_NAME,
            a.model_check_way as MODEL_CHECK_WAY,
            a.model_data_check_way as MODEL_DATA_CHECK_WAY,
            a.relating_id as RELATING_MODEL_ID,
            a.model_type as MODEL_TYPE
        FROM
            MC_MODEL_TB a,
            MC_TABLE_TB b
        WHERE
            a.table_id = b.id
            AND a.status = 1
            AND a.relating_id = #{modelId}
        ORDER BY
            a.name
    </select>

    <!-- 获取预警数据用于下发差错 -->
    <select id="getRiskWarningDataForSlip" parameterType="java.util.Map" resultType="java.util.HashMap">
        SELECT
            *
        FROM
            ${tableName}
        WHERE
            MODEL_ID = #{model_id}
            AND MODELROW_ID = #{modelrow_id}
    </select>

    <!-- 更新预警状态 -->
    <update id="updateRiskWarningStatus" parameterType="java.util.Map">
        UPDATE
            ${tableName}
        SET
            ISHANDLE = #{isHandle},
            ALERT_USER = #{alertUser},
            ALERT_USERNAME = #{alertUsername},
            ALERT_DATE = #{alertDate},
            ALERT_TIME = #{alertTime},
            FORM_TYPE = #{formType},
            FORM_ID = #{formId}
        WHERE
            MODEL_ID = #{modelId}
            AND MODELROW_ID = #{modelRowId}
    </update>

    <!-- 获取质检信息 -->
    <select id="getModelZjdInfo" parameterType="string" resultType="java.util.HashMap">
        SELECT
            user_name as userName,
            jd_time as jdTime,
            reason
        FROM mc_model_zjd
        WHERE model_row_id = #{modelRowId}
          AND state = '3'
    </select>

    <!-- 获取用户权限信息 -->
    <select id="getUserPermission" parameterType="string" resultType="java.util.HashMap">
        SELECT
            role_no as roleNo,
            user_type as userType
        FROM sm_users_tb
        WHERE user_no = #{userNo}
    </select>

    <!-- 获取挂起状态信息 -->
    <select id="getSuspendInfo" parameterType="string" resultType="java.util.HashMap">
        SELECT
            s.suspend_reason as suspendReason,
            s.suspend_time as suspendTime,
            u.user_name as suspendUser,
            s.status
        FROM arms_suspend_tb s
        LEFT JOIN sm_users_tb u ON s.user_no = u.user_no
        WHERE s.model_row_id = #{modelRowId}
          AND s.status = '1'
    </select>

    <!-- 创建task_mount表（如果不存在）- 与老系统完全一致 -->
    <update id="createTaskMountTableIfNotExists">
        CREATE TABLE IF NOT EXISTS task_mount (
            modelrow_id INTEGER NOT NULL,
            model_flag VARCHAR(1) NOT NULL DEFAULT '1',
            PRIMARY KEY (modelrow_id)
        )
    </update>

    <!-- 查询挂起状态 -->
    <select id="queryByModelRowId" parameterType="String" resultType="java.util.Map">
        SELECT modelrow_id, model_flag
        FROM task_mount
        WHERE modelrow_id = #{modelRowId}
    </select>

    <!-- 预警挂起操作 -->
    <insert id="suspendRiskWarning" parameterType="java.util.Map">
        INSERT INTO task_mount (modelrow_id, model_flag)
        VALUES (#{model_row_id}, '1')
    </insert>

    <!-- 更新挂起状态为挂起 -->
    <update id="updateSuspendStatus" parameterType="java.util.Map">
        UPDATE task_mount
        SET model_flag = '1'
        WHERE modelrow_id = #{model_row_id}
    </update>

    <!-- 取消预警挂起 -->
    <update id="cancelSuspendRiskWarning" parameterType="java.util.Map">
        UPDATE task_mount
        SET model_flag = '2'
        WHERE modelrow_id = #{model_row_id}
    </update>

    <!-- 插入中间表记录（按照老系统mc_model_zjd表逻辑） -->
    <insert id="insertModelZjdRecord" parameterType="java.util.Map">
        INSERT INTO mc_model_zjd (
            model_id, model_name, model_row_id, site_no, operator_no,
            business_date, user_no_s, state, end_time, table_name
        )
        SELECT
            model_id, model_name, modelrow_id, site_no, operator_no,
            occur_date, user_no, 1, #{currentTime}, #{tableName}
        FROM ${tableName}
        WHERE modelrow_id = #{modelRowId} AND model_id = #{modelId}
    </insert>

    <!-- 更新预警状态（包含备注字段，与老系统完全一致） -->
    <update id="updateRiskWarningStatusWithReason" parameterType="java.util.Map">
        UPDATE ${tableName} SET
            ISHANDLE = #{isHandle},
            ALERT_USER = #{alertUser},
            ALERT_USERNAME = #{alertUsername},
            ALERT_DATE = #{alertDate},
            ALERT_TIME = #{alertTime},
            ALERT_CONTENT = #{alertContent}
        WHERE MODEL_ID = #{modelId} AND MODELROW_ID = #{modelRowId}
    </update>

</mapper>
