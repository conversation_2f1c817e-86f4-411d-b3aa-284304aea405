package com.sunyard.util;

import java.util.Map;
import java.util.Set;

public class URLAssembleUtil {

	/**
	 * 拼装URL问号后面的参数，格式为key1=value1&key2=value2&key3=value3...
	 * @param map
	 * @return
	 */
	public static String assembleURL(Map<String, String> map){
	//	log.info(er.getLogger(URLAssembleUtil.class), "--URLAssembleUtil-->assembleURL-->拼装URL问号后面的参数，格式为key1=value1&key2=value2&key3=value3...");
		StringBuffer urlSB = new StringBuffer();
		Set<String> strSet = map.keySet();
		for (String key : strSet) {
			if(urlSB.length()>0){
				urlSB.append("&");
			}
			urlSB.append(key).append("=").append(map.get(key));
			
		}
		return urlSB.toString();
	}
}