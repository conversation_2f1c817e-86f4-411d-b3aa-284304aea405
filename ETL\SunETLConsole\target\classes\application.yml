server:
  port: 8097
  servlet:
    context-path: /SunETLConsole
    session:
      timeout: 3600

spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: org.postgresql.Driver
      url: ********************************************
      username: sunyard
      password: YSsXK89Inx/wsq943AHGD70s2EnPVWNsUS7qgYS/81dA2NStM/glMLQ7JEdSOgjIpByL6bSYBn+Zo8WcxOD9dg==
      initial-size: 5
      max-active: 20
      min-idle: 5
      max-wait: 60000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 100
      validation-query: SELECT 1
      test-while-idle: true
      connection-error-retry-attempts: 0
      break-after-acquire-failure: true
      filters: config
      connection-properties: config.decrypt=true;config.decrypt.key=MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKXxlgTzAnxj+7aJKKTNqGKhvQaqnaVNktGKoqDgD2tJPxLZ2by/Le8Bg46XjKmNyBOFdFlw92cYIG2qP1WlMQUCAwEAAQ==

  mvc:
    static-path-pattern: /static/**
  web:
    resources:
      static-locations: classpath:/static/
  freemarker:
    template-loader-path: classpath:/templates/
    suffix: .ftl
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########
      classic_compatible: true
  mail:
    host: smtp.qq.com
    port: 465
    username: <EMAIL>
    password: rrgdgfkxeggmbiee
    properties:
      from: <EMAIL>
      nick: SunETL

mybatis:
  mapper-locations: classpath:/mybatis-mapper/*.xml
  configuration:
#    database-id: oracle
    database-id: postgres

#配置分页插件Oracle、MySQL、DB2数据库
pagehelper:
#  helperDialect: PostgreSQL
#  helperDialect: oracle
#  helperDialect: gbasedbt-sqli
  reasonable: true
  supportMethodsArguments: true
#  auto-runtime-dialect: false
  params: count=countSql
#  dialectAlias: gbasedbt-sqli=com.github.pagehelper.dialect.helper.OracleDialect

xxl:
  job:
    accessToken:
    login:
      username: admin
      password: 123456
    system:
      isOenpReactive: 1

logging:
  config: classpath:logback.xml
