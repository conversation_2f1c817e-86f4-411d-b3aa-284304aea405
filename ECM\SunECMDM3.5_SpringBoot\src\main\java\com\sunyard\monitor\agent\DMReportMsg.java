package com.sunyard.monitor.agent;

import com.sunyard.ecm.server.Report;
import com.sunyard.ecm.server.bean.AllModelMsgBean;
import com.sunyard.ecm.server.cache.LazySingleton;
import com.sunyard.util.DateUtil;

import java.util.Map;

/**
 * <p>Title: 获取DM统计信息代理</p>
 * <p>Description: 获取DM统计信息供监控服务端调用</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: Sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */

public class DMReportMsg {
	
	/**
	 *  获取统计信息
	 * @return 统计信息xml文件
	 */
	public static String getReportMsg(){
		StringBuffer sb = new StringBuffer("<?xml version='1.0' encoding='UTF-8' ?><root>");
				
		Map<String, AllModelMsgBean> map = LazySingleton.getInstance().modelObjectSet.getMetadataObjectSetBean(); //获取内容模型名称
		
		String server_id = LazySingleton.getInstance().load.getNodeInfoBean().getServer_id();  //获取服务器ID
		
		String group_id = LazySingleton.getInstance().load.getNodeInfoBean().getGroup_id();  //获取服务器组ID
		
		String countDate = DateUtil.get10bitDateStr();  //获取 统计时间  年月日时
		
		for(String m:map.keySet()){
			sb.append("<report>");
				sb.append("<server_id>").append(server_id).append("</server_id>");
				sb.append("<group_id>").append(group_id).append("</group_id>");
				sb.append("<obj_name>").append(m).append("</obj_name>");
				sb.append("<receive_batch>").append(Report.getReceiveBatch(m)).append("</receive_batch>");
				sb.append("<receive_file>").append(Report.getFileSum(m)).append("</receive_file>");
				sb.append("<send_batch>").append(Report.getSendBatch(m)).append("</send_batch>");
				sb.append("<send_file>").append(Report.getSendFileSumMap(m)).append("</send_file>");
				sb.append("<count_time>").append(countDate).append("</count_time>");
			sb.append("</report>");
			//清空数据
			Report.deleteReceiveBatch(m);
			Report.deleteFileSum(m);
			Report.deleteSendBatch(m);
			Report.deleteSendFileSumMap(m);
		}
		
		sb.append("</root>");
		

		return sb.toString();
	}
//	public static void main(String[] args) {
//		System.out.println(getReportMsg());
//	}
}