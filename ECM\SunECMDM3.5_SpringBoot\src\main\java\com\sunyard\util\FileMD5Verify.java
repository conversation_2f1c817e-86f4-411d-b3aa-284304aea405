package com.sunyard.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import com.sunyard.ecm.server.service.EncrypterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * MD5校验类
 * <AUTHOR>
 *
 */
public class FileMD5Verify {
	private final static Logger log = LoggerFactory.getLogger(FileMD5Verify.class);
	/**
	 * 默认的密码字符串组合，apache校验下载的文件的正确性用的就是默认的这个组合
	 */
	protected static char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6',
			'7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f' };
	protected  MessageDigest messagedigest = null;
	
	public FileMD5Verify(){
		try {
			messagedigest = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException nsaex) {
			log.error(FileMD5Verify.class.getName()
					+ "初始化失败，MessageDigest不支持MD5Util?");
		}
	}

	/**
	 * 传入文件计算其MD5校验码
	 * @param file
	 * @return
	 */
	public String getFileMD5String(File file) {
		FileInputStream fis = null;
		try{
			fis = new FileInputStream(file);
			byte[] buffer = new byte[8192];
            int length = -1;
            while ((length = fis.read(buffer)) != -1) {
            	messagedigest.update(buffer, 0, length);
            }
            return bufferToHex(messagedigest.digest()).toUpperCase();
		} catch(IOException e) {
			log.error("出错",e);
		} finally{
			try{
				if(fis != null){
					fis.close();
				}
			}catch (IOException e) {
				log.error("出错");
			}
		}
		return "";
	}
	
	/**
	 * 传入加密文件计算其解密文件的MD5校验码
	 * @param file
	 * @param afterEncodeSize 
	 * @param beforeEncodeSize 
	 * @return
	 * @throws Exception 
	 */
	public String getEncryMD5String(File file, int beforeEncodeSize, int afterEncodeSize) {
		InputStream oldin = null;
		EncrypterService encrypterService = (EncrypterService) SpringUtil.getSpringBean("encrypterService");
		try{
			oldin = new FileInputStream(file);
			byte[] desencryText = new byte[beforeEncodeSize];
			byte[] encrybuffer = new byte[afterEncodeSize];
			byte[] lastbuffer = new byte[8192 - beforeEncodeSize];
            int length = -1;
            boolean flag = true;
            while (flag && (oldin.read(encrybuffer)) != -1) {
            	desencryText = encrypterService.desencryptData_ECB(encrybuffer);
            	messagedigest.update(desencryText, 0, desencryText.length);
            	flag = false;
            }
            while ((length = oldin.read(lastbuffer)) != -1) {
            	byte[] newbyte = byteMerger(desencryText, lastbuffer);
            	messagedigest.update(newbyte, desencryText.length, length);
            }
            return bufferToHex(messagedigest.digest()).toUpperCase();
		} catch(Exception e) {
			log.error("生成解密文件的MD5码出错",e);
		} finally{
			try{
				if(oldin != null){
					oldin.close();
				}
			}catch (IOException e) {
				log.error("出错");
			}
		}
		return "";
	}

	private  String bufferToHex(byte bytes[]) {
		return bufferToHex(bytes, 0, bytes.length);
	}

	private  String bufferToHex(byte bytes[], int m, int n) {
		StringBuffer stringbuffer = new StringBuffer(2 * n);
		int k = m + n;
		for (int l = m; l < k; l++) {
			appendHexPair(bytes[l], stringbuffer);
		}
		return stringbuffer.toString();
	}

	private  void appendHexPair(byte bt, StringBuffer stringbuffer) {
		char c0 = hexDigits[(bt & 0xf0) >> 4];
		char c1 = hexDigits[bt & 0xf];
		stringbuffer.append(c0);
		stringbuffer.append(c1);
	}
	public String aa(File file){
		String md5=getFileMD5String(file);
		return md5;
	}

	/**
	 * 数组拼接
	 * @param byte[]
	 * @return byte[]
	 */
    public byte[] byteMerger(byte[] byte_1, byte[] byte_2){  
        byte[] byte_3 = new byte[byte_1.length+byte_2.length];  
        System.arraycopy(byte_1, 0, byte_3, 0, byte_1.length);  
        System.arraycopy(byte_2, 0, byte_3, byte_1.length, byte_2.length);  
        return byte_3;  
    }
    
    /**
   	 * 截取byte数组   不改变原数组
   	 * @param b 原数组
   	 * @param off 偏差值（索引）
   	 * @param length 长度
   	 * @return 截取后的数组
   	 */
   	public byte[] subByte(byte[] b,int off,int length){
   		byte[] b1 = new byte[length];
   		System.arraycopy(b, off, b1, 0, length);
   		return b1;
   	}
   	
	/**
	 * 测试main函数
	 * @param args
	 */
//	public static void main(String[] args) {
//		File file1 = new File("D://123/1.jpg");
//		File file2 = new File("D://123/2.jpg");
//		File file3 = new File("D://123/3.jpg");
//		File file4 = new File("D://123/4.jpg");
//		File file5 = new File("D://123/5.jpg");
//		for(int i=0;i<10;i++){
//			new Thread(new FileMD5Verify().aa(file1)).start();
//			new Thread(new FileMD5Verify().aa(file2)).start();
//			new Thread(new FileMD5Verify().aa(file3)).start();
//			new Thread(new FileMD5Verify().aa(file4)).start();
//			new Thread(new FileMD5Verify().aa(file5)).start();
//		}
//	}

}