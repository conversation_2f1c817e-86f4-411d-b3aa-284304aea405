package com.sunyard.console.insNoManager.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.insNoManager.bean.ItemtypeInsoDmsNameInfoBean;
import com.sunyard.console.insNoManager.dao.InsNoDmsManagerDao;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>
 * Title: 机构管理action
 * </p>
 * <p>
 * Description: 用于管理机构信息获取,以及增删改等操作的action访问类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class insNoDmsManagerAction extends BaseAction {

	private String insNo;
	private String dmsId;
	private String dmsName;
	private String itemType;
	private String itemName;
	private String groupId;
	private String modelCode;
	private String nowPID;
	/**
	 * 操作类型：update：更新，create：新增
	 */
	private String optionFlag;
	@Autowired
	private InsNoDmsManagerDao insNoDmsDao;


	public String getNowPID() {
		return nowPID;
	}

	public void setNowPID(String nowPID) {
		this.nowPID = nowPID;
	}

	public InsNoDmsManagerDao getInsNoDmsDao() {
		return insNoDmsDao;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getModelCode() {
		return modelCode;
	}

	public void setModelCode(String modelCode) {
		this.modelCode = modelCode;
	}

	public void setInsNoDmsDao(InsNoDmsManagerDao insNoDmsDao) {
		this.insNoDmsDao = insNoDmsDao;
	}

	public String getInsNo() {
		return insNo;
	}

	public void setInsNo(String insNo) {
		this.insNo = insNo;
	}

	public String getDmsId() {
		return dmsId;
	}

	public void setDmsId(String dmsId) {
		this.dmsId = dmsId;
	}

	public String getDmsName() {
		return dmsName;
	}

	public void setDmsName(String dmsName) {
		this.dmsName = dmsName;
	}

	public String getItemType() {
		return itemType;
	}

	public void setItemType(String itemType) {
		this.itemType = itemType;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}
	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	/**
	 * 分页记录开始位置
	 */
	private int start;
	/**
	 * 分页记录条数
	 */
	private int limit;
	private String value;
	/**
	 * 日志对象
	 */
	private final static  Logger log = LoggerFactory.getLogger(insNoDmsManagerAction.class);

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	/**
	 * 查询action 将结果集组合成jsonStr，返回给页面
	 * 
	 * @return null
	 */
	@ResponseBody
	@RequestMapping(value = "/insnoDmsManager/getInsnoDmsList.action",produces = {"application/json;charset=UTF-8"}, method = RequestMethod.POST)
	public String getInsnoDmsList(String data) {
		 JSONObject modelJson = JSONObject.fromObject(data);
	     String insNo = modelJson.getString("ins_No");
	     int page = modelJson.getInt("page");
		 int limit = modelJson.getInt("limit");
		 start = (page-1) * limit;
		 try {
				insNo = URLDecoder.decode(insNo, "utf-8");
			} catch (UnsupportedEncodingException e1) {
				log.error("decode insNoDmsManagerAction fields error, insNo=" + insNo
						+ ", remark=" + e1);
			}
	     String modelCode = modelJson.getString("model_code");
		log.info(
				"--insNoDmsManagerAction(start)-->getInsnoDmsList:");
		String jsonStr = null;

		try {
			List<ItemtypeInsoDmsNameInfoBean> infoList = insNoDmsDao
					.getInsNoDmsList(modelCode,insNo,start + 1, limit);

			List<ItemtypeInsoDmsNameInfoBean> AllAttrInfoList = insNoDmsDao
					.getInsNoDmsList(modelCode,insNo);

			int size = 0;
			if (AllAttrInfoList != null && AllAttrInfoList.size() > 0) {
				size = AllAttrInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(infoList, size,
					new ItemtypeInsoDmsNameInfoBean());
			log.debug( "--getInsnoDmsList-->查询成功");

		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取机构和服务器组名信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "机构管理->获取机构和服务器组名信息失败!!" + e.toString());
			log.error( "Exception:",e);
		}
		log.info( "--getInsnoDmsList(over)");
		return jsonStr;
	}

	/**
	 * 删除属性action
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/insnoDmsManager/delInsnoDms.action",produces = {"application/json;charset=UTF-8"} ,method = RequestMethod.POST)
	public String delInsnoDms(String nowPID) {
		log.info( "--delInsnoDms(start)-->pId:" + nowPID);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean result = insNoDmsDao.delInsnoDms(nowPID);
			jsonResp.put("success", result);
			if (result) {
				log.debug( "--delInsnoDms-->删除成功!!-->pId:" + nowPID);
				jsonResp.put("message", "删除成功!!");
			} else {
				log.debug( "--delInsnoDms-->删除失败!!-->pId:" + nowPID);
				jsonResp.put("message", "删除失败!!");
			}
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "删除失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "->删除失败！" + e.toString());
			log.error( "Exception:",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--delInsnoDms(over)");
		return null;
	}

	/**
	 * 新增或者更新机构信息action
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/insnoDmsManager/alterInsNoDms.action",produces = {"application/json;charset=UTF-8"} ,method = RequestMethod.POST)
	public String alterInsNoDms(String data,String optionFlag1) {

		log.info("--addInsNoDms(start)-->");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		// 取得session中的用户信息
//		HttpSession session = getSession();
//
//		UserInfoBean userBean = (UserInfoBean) session
//				.getAttribute("loginUser");
//
//		if (userBean == null) {
//			jsonResp.put("success", false);	
//			jsonResp.put("message", "配置失败!!用户登陆已超时,请重新登陆!");
//			jsonStr = jsonResp.toString();
//			this.outJsonString(jsonStr);
//			return null;
//		}
		JSONObject modelJson = JSONObject.fromObject(data);
		optionFlag=modelJson.getString("optionFlag");
		ItemtypeInsoDmsNameInfoBean bean = new ItemtypeInsoDmsNameInfoBean();
		if(optionFlag1.equals("update1")) {	
			bean.setpId(modelJson.getString("pId"));
		}else {
			bean.setpId(modelJson.getString("nowPID"));
		}
		bean.setGroup_id(modelJson.getString("group_id"));
		bean.setModel_code(modelJson.getString("model_code"));
		String group_name = modelJson.getString("group_name");
		String insNo = modelJson.getString("insNo");
		try {
			group_name = URLDecoder.decode(group_name, "utf-8");
			insNo = URLDecoder.decode(insNo, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode insNoDmsManagerAction fields error, group_name=" + group_name
					+ ", remark=" + e1,"decode insNoDmsManagerAction fields error, insNo=" + insNo
					+ ", remark=" + e1);
		}
        bean.setGroup_name(group_name);
        bean.setInsNo(insNo);
		try {
			boolean result = false;
			int exisGroupId = insNoDmsDao.getDmsId(bean);
			if (exisGroupId != 0 && exisGroupId == Integer.parseInt(modelJson.getString("group_id"))) {
				// 说明该机构对应的服务器组已经配置存在
				// 新增失败
				jsonResp.put("success", false);
				jsonResp.put("message", "该机构和项类型已经配置过该服务器组信息!!!");
				jsonStr = jsonResp.toString();
				log.debug( "--alterInsNoDms配置机构失败，已经配置过该机构信息-->");
			} else {
				if (optionFlag != null && optionFlag.equals("create1")) {
					// 新增
					result = insNoDmsDao.addInsNo(bean);
				} else if (optionFlag != null && optionFlag.equals("update1")) {
					// 修改
					result = insNoDmsDao.updateInsNo(bean);

				}
				if (result) {
					jsonResp.put("message", "配置机构成功!!");
					jsonResp.put("success", true);
					jsonResp.put("code", 20000);//TODO mock
					jsonStr = jsonResp.toString();
				} else {// 失败
					jsonResp.put("success", false);
					jsonResp.put("message", "配置机构失败!!");
//					jsonStr = jsonResp.toString();
					log.debug( "--alterInsNoDms配置机构失败!-->");
				}

			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置机构失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "配置机构失败!" + e.toString(), e);
		}
		log.info( "--alterInsNoDms(over)");
		this.outJsonString(jsonStr);
		return null;

	}

}
