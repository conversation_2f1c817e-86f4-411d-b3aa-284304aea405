package com.sunyard.console.logmanage.action;

import java.util.List;
import java.util.Set;


import com.sunyard.console.configmanager.wsserviceutil.WsBeanInterface;
import com.sunyard.console.monitor.monitorCenter.singleton.LazySingleton;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONObject;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.configmanager.bean.LogInfoBean;
import com.sunyard.console.configmanager.bean.NodeInfo;
import com.sunyard.console.configmanager.wsserviceutil.DMAdressConstuct;
import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.contentservermanage.dao.ContentServerManageDAO;
import com.sunyard.console.logmanage.bean.LogListStatic;
import com.sunyard.ws.client.WSAccessClient;
import com.sunyard.ws.internalapi.SunEcmAccess;
import com.sunyard.ws.utils.XMLUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>Title: 日志下载Action</p>
 * <p>Description: 查询指定服务器上日志列表、获取日志URL</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class LogDownloadManageAction extends BaseAction {
    private String server_id;    //服务器ID
    private String server_name;    //服务器name
    private String log_name;    //日志名称
    private String log_path;    //日志路径
    private String url;            //日志URL
    private String start;    //分页起始参数
    private String limit;    //分页结束参数
    WsBeanInterface wsClient;
    SunEcmAccess wsInterface = null;
    private final static Logger log = LoggerFactory.getLogger(LogDownloadManageAction.class);
    @Autowired
    private ContentServerManageDAO contentServerManageDao;


    public String getServer_name() {
        return server_name;
    }

    public void setServer_name(String server_name) {
        this.server_name = server_name;
    }

    public String getServer_id() {
        return server_id;
    }

    public void setServer_id(String server_id) {
        this.server_id = server_id;
    }

    public String getLog_name() {
        return log_name;
    }

    public void setLog_name(String log_name) {
        this.log_name = log_name;
    }

    public String getLog_path() {
        return log_path;
    }

    public void setLog_path(String log_path) {
        this.log_path = log_path;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public ContentServerManageDAO getCsmdao() {
        return contentServerManageDao;
    }

    public void setCsmdao(ContentServerManageDAO csmdao) {
        this.contentServerManageDao = csmdao;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        this.start = start;
    }

    public String getLimit() {
        return limit;
    }

    public void setLimit(String limit) {
        this.limit = limit;
    }

    /**
     * 获取指定内容存储服务器下所有日志信息列表
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/logManage/logListByServerIdAction.action", method = RequestMethod.POST)
    public String logListByServerId(String data) {
        JSONObject modelJson = JSONObject.fromObject(data);
        int page = modelJson.getInt("page");
        int limit = modelJson.getInt("limit");
        String server_id = modelJson.getString("server_id");
        String server_name = modelJson.getString("server_name");
        log.info("--logListByServerId(start)-->server_id:" + server_id + ";server_name:" + server_name);
        String jsonStr = null;
        String xmlStr = null;
        //查询内容存储服务器列表
        List<ContentServerInfoBean> list = contentServerManageDao.getContentServerList(Integer.parseInt(server_id), server_name);
        try {
            ContentServerInfoBean bean = list.get(0);
            String socket_port = bean.getSocket_port() + "";
            String ip = bean.getServer_ip();
            List<LogInfoBean> logList = null;
            List<LogInfoBean> totalLogList = null;
            if (bean != null) {
                log.debug("--logListByServerId-->bean:" + bean);
                if (page == 1) {
                    page = page - 1;
                    NodeInfo serverInfo = new NodeInfo();
                    serverInfo.setServer_id(server_id);
                    serverInfo.setServer_name(server_name);
                    //将服务器信息Bean转化成XML
                    xmlStr = XMLUtil.addHeadRootNode(XMLUtil.bean2XML(serverInfo));
                    //获取统一接入客户端
                    WSAccessClient wsclient = new WSAccessClient();
                    //连接指定服务器
                    String dmName = DMAdressConstuct.getDMAdress(bean.getServer_ip() + ":" + bean.getHttp_port());
                    log.debug("#################日志查询的url链接：" + dmName + "##;###############");
                    String result;
                    SunEcmAccess access = wsclient.getAccessClient(dmName, 300000);
                    //调用查询指定服务器下日志列表信息
                    result = access.logListByServerId(xmlStr);
                    totalLogList = XMLUtil.xml2list(XMLUtil.removeHeadRoot(result), LogInfoBean.class);
                    LogListStatic.getInstance().addBatch(server_id, totalLogList);
                } else {
                    totalLogList = LogListStatic.getInstance().getLogList(server_id);
                }
                logList = getSubList(totalLogList, page + "", limit + "");
                jsonStr = new JSONUtil().createJsonDataByColl(logList, totalLogList.size(), new LogInfoBean());
            }
        } catch (Exception e) {
            JSONObject jsonResp = new JSONObject();
            jsonResp.put("success", false);
            jsonResp.put("message", "获取日志信息失败!!");
            jsonStr = jsonResp.toString();
            //错误提示信息日志
            log.error("日志下载管理->获取日志列表失败->" + e.toString(), e);
        }
        this.outJsonString(jsonStr);
        log.info("--logListByServerId(over)-->server_id:" + server_id);
        return null;
    }

    /**
     * 完成日志列表分页功能
     *
     * @param list  日志列表
     * @param start 开始位置
     * @param limit 结束位置
     * @return
     */
    private List<LogInfoBean> getSubList(List<LogInfoBean> list, String start, String limit) {
        log.info("--getSubList(start)-->list:" + list);
        int iStart = Integer.valueOf(start);
        int iLimit = Integer.valueOf(limit) + Integer.valueOf(start);
        log.debug("--getSubList-->iStart:" + iStart + ";iLimit:" + iLimit + ";list.size:" + list.size());
        if (iStart > list.size()) {
            iStart = list.size();
        }
        if (iLimit > list.size() || iLimit < iStart) {
            iLimit = list.size();
        }
        log.info("--getSubList(over)");
        return list.subList(iStart, iLimit);
    }

    private SunEcmAccess getEcmAccess(String url) {
        log.info("--getEcmAccess(start)-->url:" + url);
        long timeout = 300000;
        Set<String> urls = LazySingleton.getInstance().getAccesMap().keySet();
        boolean flag = true;
        for (String urlTemp : urls) {
            if (url.equals(urlTemp)) {
                wsInterface = LazySingleton.getInstance().getAccesMap().get(urlTemp);
                flag = false;
            }
        }
        if (flag) {
            wsInterface = new WSAccessClient().getAccessClient(url, timeout);
            log.debug("WEBSERVICE创建的URL为:" + url);
            LazySingleton.getInstance().addAccesMap(url, wsInterface);
        }
        log.info("--getEcmAccess(over)");
        return wsInterface;
    }
}
