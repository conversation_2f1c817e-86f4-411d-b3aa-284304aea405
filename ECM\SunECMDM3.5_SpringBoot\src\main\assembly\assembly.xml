<?xml version="1.0" encoding="utf-8"?>
<assembly
        xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">

    <!-- id 标识符，添加到生成文件名称的后缀符。如果指定 id 的话（这里指定的是项目的版本），目标文件则是 ${artifactId}-${id}.jar。【如terminal-dispatch-*******.jar】 -->
    <id>${project.version}</id>

    <!-- 指定打包格式。maven-assembly-plugin插件支持的打包格式有zip、tar、tar.gz (or tgz)、tar.bz2 (or tbz2)、jar、dir、war，可以同时指定多个打包格式 -->
    <formats>
        <format>jar</format>
    </formats>

    <!-- 指定打的包是否包含打包层目录（比如finalName是terminal-dispatch，当值为true，所有文件被放在包内的terminal-dispatch目录下，否则直接放在包的根目录下）-->
    <includeBaseDirectory>false</includeBaseDirectory>

    <!-- 指定将工程依赖的包打到包里的指定目录下 -->
<!--    <dependencySets>-->
<!--        <dependencySet>-->
<!--            <useProjectArtifact>true</useProjectArtifact> &lt;!&ndash; 指定打包时是否包含工程自身生成的jar包 &ndash;&gt;-->
<!--            <outputDirectory>lib</outputDirectory> &lt;!&ndash; 指定将这些依赖包打到包里lib目录下 &ndash;&gt;-->
<!--            <scope>runtime</scope> &lt;!&ndash; 用于管理依赖的部署，runtime表示只在运行时使用 &ndash;&gt;-->
<!--        </dependencySet>-->
<!--    </dependencySets>-->
    <fileSets>
        <fileSet>
            <directory>${project.basedir}/target/classes/</directory>
            <outputDirectory>/</outputDirectory>
            <includes>
                <include>com/sunyard/client/**/*</include>
                <include>com/sunyard/exception/**/*</include>
                <include>com/sunyard/util/**/*</include>
                <include>com/sunyard/ecm/server/bean/**/*</include>
                <include>com/sunyard/ecm/server/dm/batchdown/bean/**/*</include>
                <include>com/sunyard/ecm/server/dm/batchcopy/bean/**/*</include>
                <include>com/sunyard/ecm/util/net/**/*</include>
                <include>com/sunyard/ecm/server/dm/batchEs/bean/*</include>
            </includes>
        </fileSet>
    </fileSets>

    <!-- 指定要包含的文件集，可以定义多个fileSet -->
<!--    <fileSets>-->
<!--        <fileSet>-->
<!--            <directory>src/main/script/linux/bin</directory> &lt;!&ndash; 指定归档文件（要打的jar包）要包含的目录（下的文件及文件夹） &ndash;&gt;-->
<!--            <outputDirectory>bin</outputDirectory> &lt;!&ndash; 指定要将当前目录（<directory>标签中的目录放在归档文件（要打的jar包）bin目录下） &ndash;&gt;-->
<!--            <includes>-->
<!--                <include>terminal-dispatch</include> &lt;!&ndash; 精确控制要包含的文件，<exclude>用于精确控制要排除的文件  &ndash;&gt;-->
<!--                <include>server</include>-->
<!--            </includes>-->
<!--            <fileMode>0755</fileMode> &lt;!&ndash; 设置文件 UNIX 属性，是一种读写权限 &ndash;&gt;-->
<!--        </fileSet>-->
<!--        <fileSet>-->
<!--            <directory>src/main/resources</directory>-->
<!--            <outputDirectory>conf</outputDirectory>-->
<!--            <includes>-->
<!--                <include>config.properties</include>-->
<!--                <include>logback.xml</include>-->
<!--            </includes>-->
<!--            <fileMode>0644</fileMode>-->
<!--        </fileSet>-->
<!--        <fileSet>-->
<!--            <directory>src/main/script/conf</directory>-->
<!--            <outputDirectory>conf</outputDirectory>-->
<!--            <includes>-->
<!--                <include>wrapper.conf</include>-->
<!--            </includes>-->
<!--            <fileMode>0644</fileMode>-->
<!--        </fileSet>-->
<!--        <fileSet>-->
<!--            <directory>src/main/script/linux/lib</directory>-->
<!--            <outputDirectory>lib</outputDirectory>-->
<!--            <includes>-->
<!--                <include>libwrapper.so</include>-->
<!--                <include>wrapper.jar</include>-->
<!--            </includes>-->
<!--            <fileMode>0755</fileMode>-->
<!--        </fileSet>-->
<!--    </fileSets>-->

</assembly>