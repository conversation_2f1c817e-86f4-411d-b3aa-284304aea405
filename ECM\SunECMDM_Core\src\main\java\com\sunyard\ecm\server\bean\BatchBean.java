package com.sunyard.ecm.server.bean;

import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientBatchIndexBean;
import com.sunyard.util.CodeUtil;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

import java.util.ArrayList;
import java.util.List;

/**
 * 上传、更新、查询的批次信息bean对象
 * 
 * <AUTHOR>
 */
@XStreamAlias("BatchBean")
public class BatchBean {
	@XStreamAsAttribute
	private String MODEL_CODE; // 内容模型代码
	@XStreamAsAttribute
	private boolean IS_UNITED_ACCESS; // 是否由统一接入服务器转发
	@XStreamAsAttribute
	private boolean IS_SUNECM_COSOLE; // 是否由控制台服务器转发,绕过不必要的内部校验
	@XStreamAsAttribute
	private String TOKEN_CODE;// 令牌数值
	@XStreamAsAttribute
	private String TOKEN_CHECK_VALUE;// 令牌校验标识，申请令牌时由客户端提供的数值
	@XStreamAsAttribute
	private String USER;// 上传用户名
	@XStreamAsAttribute
	private String PASSWD;//密码
	@XStreamAsAttribute
	private String CHECK_TOKEN;// 检入检出随机数
	@XStreamAsAttribute
	private boolean IS_BREAK_POINT;// 是否断点续传
	/**
	 * 客户端IP
	 */
	@XStreamAsAttribute
	private String CLIENT_IP;
	@XStreamAsAttribute
	private boolean IS_DOWNLOAD;// 是否设置下拉
	
	private BatchIndexBean index_Object; // 索引信息
	
	private List<BatchFileBean> document_Objects;// 文档部件信息
	
	private String TABLE_NAME;//表名,服务端为提高性能设置的表名字段
	private String DMS_NAME; //服务器组名
	@XStreamAsAttribute
	private String PASSWORD;// 密码
	
	/**
	 * 令牌
	 */
	@XStreamOmitField
	private String token;


	public String getToken() {
		return token;
	}


	public void setToken(String token) {
		this.token = token;
	}
	public String getPassWord() {
		return PASSWORD;
	}

	public void setPassWord(String passWord) {
		PASSWORD = passWord;
	}
	/**
	 * 相对路径，从卷目录到批次目录的相对路径，即时间目录+随机目录
	 */
	@XStreamAsAttribute
	private String relatePath;
	
	public String getModelCode() {
		return MODEL_CODE;
	}

	public void setModelCode(String modelCode) {
		this.MODEL_CODE = modelCode;
	}

	public String getToken_code() {
		return TOKEN_CODE;
	}

	public void setToken_code(String tokenCode) {
		TOKEN_CODE = tokenCode;
	}

	public String getUser() {
		return USER;
	}

	public void setUser(String user) {
		this.USER = user;
	}

	public BatchIndexBean getIndex_Object() {
		if(index_Object == null) {
			index_Object = new BatchIndexBean();
		}
		return index_Object;
	}

	public void setIndex_Object(BatchIndexBean indexObject) {
		index_Object = indexObject;
	}

	public List<BatchFileBean> getDocument_Objects() {
		return document_Objects;
	}

	public void setDocument_Objects(List<BatchFileBean> documentObjects) {
		document_Objects = documentObjects;
	}

	public void addDocument_Object(BatchFileBean documentObject) {
		if(document_Objects == null){
			document_Objects = new ArrayList<BatchFileBean>();
		}
		this.document_Objects.add(documentObject);
	}

	public String getToken_check_value() {
		return TOKEN_CHECK_VALUE;
	}

	public void setToken_check_value(String tokenCheckValue) {
		TOKEN_CHECK_VALUE = tokenCheckValue;
	}

	public String getCheckToken() {
		return CHECK_TOKEN;
	}

	public void setCheckToken(String checkToken) {
		this.CHECK_TOKEN = checkToken;
	}

	public boolean isBreakPoint() {
		return IS_BREAK_POINT;
	}

	public void setBreakPoint(boolean isBreakPoint) {
		this.IS_BREAK_POINT = isBreakPoint;
	}

	public boolean isUnitedAccess() {
		return IS_UNITED_ACCESS;
	}

	public void setUnitedAccess(boolean isUnitedAccess) {
		this.IS_UNITED_ACCESS = isUnitedAccess;
	}

	public boolean isSunECMCosole() {
		return IS_SUNECM_COSOLE;
	}

	public void setSunECMCosole(boolean isSunECMCosole) {
		this.IS_SUNECM_COSOLE = isSunECMCosole;
	}

	public boolean isDownLoad() {
		return IS_DOWNLOAD;
	}

	public void setDownLoad(boolean isDownLoad) {
		this.IS_DOWNLOAD = isDownLoad;
	}

	@Override
	public String toString() {
		return "BatchBean [CHECK_TOKEN=" + CHECK_TOKEN + ", document_Objects="
				+ document_Objects + ", index_Object=" + index_Object
				+ ", IS_BREAK_POINT=" + IS_BREAK_POINT + ", IS_DOWNLOAD="
				+ IS_DOWNLOAD + ", IS_SUNECM_COSOLE=" + IS_SUNECM_COSOLE
				+ ", IS_UNITED_ACCESS=" + IS_UNITED_ACCESS + ", MODEL_CODE="
				+ MODEL_CODE + ", TOKEN_CHECK_VALUE=" + TOKEN_CHECK_VALUE
				+ ", TOKEN_CODE=" + TOKEN_CODE + ", USER=" + USER + "]";
	}

	public String getCLIENT_IP() {
		return CLIENT_IP;
	}

	public void setCLIENT_IP(String client_ip) {
		CLIENT_IP = client_ip;
	}

	public String getTABLE_NAME() {
		return TABLE_NAME;
	}

	public void setTABLE_NAME(String tABLENAME) {
		TABLE_NAME = tABLENAME;
	}
	public String getDmsName() {
		return DMS_NAME;
	}

	public void setDmsName(String dmsName) {
		this.DMS_NAME = dmsName;
	}

	public String getPASSWD() {
		return PASSWD;
	}

	public void setPASSWD(String pASSWD) {
		PASSWD = pASSWD;
	}

	public ClientBatchBean batchBean2ClientBatchBean(){
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(this.MODEL_CODE);
		clientBatchBean.setUser(this.USER);
		clientBatchBean.setBreakPoint(this.IS_BREAK_POINT);
		clientBatchBean.setToken_code(this.TOKEN_CODE);
		clientBatchBean.setToken_check_value(this.TOKEN_CHECK_VALUE);
		clientBatchBean.setCheckToken(this.CHECK_TOKEN);
		clientBatchBean.setDownLoad(this.isDownLoad());
			if(PASSWD==null||"null".equals(PASSWD)||"".equals(PASSWD)){
			clientBatchBean.setPassWord(CodeUtil.decode(this.PASSWORD));
		}else {
		clientBatchBean.setPassWord(CodeUtil.decode(this.PASSWD));
		}
		ClientBatchIndexBean clientindexbean = new ClientBatchIndexBean();
		clientindexbean.setContentID(this.getIndex_Object().getContentID());
		clientindexbean.setAmount(this.getIndex_Object().getAmount());
		clientindexbean.setVersion(this.getIndex_Object().getVersion());
		clientindexbean.setCustomMap(this.getIndex_Object().getCustomMap());
		clientBatchBean.setIndex_Object(clientindexbean);
		if(null == this.document_Objects || this.document_Objects.size() == 0){
			return clientBatchBean;
		}
		for(BatchFileBean batchfilebean : this.document_Objects){
			ClientBatchFileBean clientbatchfilebean = new ClientBatchFileBean();
			clientbatchfilebean.setFilePartName(batchfilebean.getFilePartName());
			clientbatchfilebean.setFilters(batchfilebean.getFilters());
			clientBatchBean.addDocument_Object(clientbatchfilebean);
		}
		return clientBatchBean;
	}

	public String getRelatePath() {
		return relatePath;
	}

	public void setRelatePath(String relatePath) {
		this.relatePath = relatePath;
	}
	
}