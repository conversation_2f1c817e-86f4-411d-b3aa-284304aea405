package com.sunyard.client.impl;

import java.io.File;
import java.io.IOException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.util.FileMD5Verify;

/**
 * 客户端MD5码的工具类
 * <AUTHOR>
 *
 */
public class MD5Util {
	private final static  Logger log = LoggerFactory.getLogger(MD5Util.class);

	/**
	 * 为批次下的文件添加MD5码
	 * @param clientBatchBean
	 * @throws SunECMException 
	 */
	public static void addBatchMD5Code(ClientBatchBean clientBatchBean) throws SunECMException{
		if(clientBatchBean.isOwnMD5()){
			//log.info(er.getLogger(MD5Util.class), "--MD5Util-->addBatchMD5Code-->为批次下的文件添加MD5码");
			List<ClientBatchFileBean> fileobjs = clientBatchBean.getDocument_Objects();
			for (ClientBatchFileBean clientBatchFileBean : fileobjs) {
				List<ClientFileBean> files = clientBatchFileBean.getFiles();
				for (ClientFileBean clientFileBean : files) {
					if(clientFileBean.getFileName() != null){
						File file = new File(clientFileBean.getFileName());
						clientFileBean.setMd5Str(getMD5Code(file)); // 为文件添加MD5码
					}
				}
			}
		}
	}
	
	/**
	 * 生成MD5码
	 * @param file 文件对象
	 * @return
	 * @throws SunECMException 
	 */
	private static String getMD5Code(File file) throws SunECMException{
		if(file.exists()){
			FileMD5Verify md5Verify = new FileMD5Verify();
			String md5Code = md5Verify.getFileMD5String(file);
			return md5Code;
		} else {
			throw new SunECMException(SunECMExceptionStatus.FILE_NOT_FOUND, "MD5码生成时找不到文件["+ file.getAbsolutePath() +"]");
		}
	}
	
	/**
	 * 为批次下的文件添加MD5码 ByStream
	 * @param clientBatchBean
	 * @return 
	 * @throws SunECMException 
	 * @throws IOException 
	 */
	public static boolean addBatchMD5CodeByStream(ClientBatchBean clientBatchBean) throws SunECMException, IOException{
		if(clientBatchBean.isOwnMD5()){
			//log.info(er.getLogger(MD5Util.class), "--MD5Util-->addBatchMD5Code-->为批次下的文件添加MD5码");
			List<ClientBatchFileBean> fileobjs = clientBatchBean.getDocument_Objects();	
			for (ClientBatchFileBean clientBatchFileBean : fileobjs) {
				List<ClientFileBean> files = clientBatchFileBean.getFiles();
				for (ClientFileBean clientFileBean : files) {
					if(clientFileBean.getMd5Str() != null){//如果客户端传入了MD5
						log.debug("--uploadByStream-->客户端已经传入了MD5");
                        return true;
					}else {//预留是否可以解决客户端生成MD5
						return false;
						//clientFileBean.setMd5Str("createByECMClient");
					}
				}
			}
		}
		return true;	
	}
}