package com.sunyard.ecm.consoleclient;

import com.sunyard.initialization.LoadConfigFile;
import com.sunyard.ws.client.WSConsoleClient;
import com.sunyard.ws.internalapi.SunEcmConsole;

/**
 * 管理控制中心的webservice客户端工具
 * <AUTHOR>
 *
 */
public class WSConsoleUtil {

	/**
	 * 
	 * 获取console控制台的webservice
	 * 
	 * @return
	 */
	public static SunEcmConsole getConsoleClient(){
		String url = LoadConfigFile.getInstance().getConfigBean().getConsoleServer();
		return new WSConsoleClient().getEcmConsoleClient(url, 300000);
	}
}