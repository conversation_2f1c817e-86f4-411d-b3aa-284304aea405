package com.sunyard.client.connQueueControl;

import java.io.IOException;
import java.net.Socket;

import javax.net.SocketFactory;

import com.sunyard.util.TransOptionKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.conn.SocketConn;
import com.sunyard.common.Configuration;
import com.sunyard.ecm.util.net.JSSEClientSocketFactory;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.util.CodeUtil;

/**
 * Socket连接对象
 * 
 * @version1.0
 * <AUTHOR>
 *
 */
public class SocketConnObject{
	private final static  Logger log = LoggerFactory.getLogger(SocketConnObject.class);
	private String ip = ""; 			//连接IP
	private String port = "";			//连接端口
	private SocketConn connObject = null;	//传输连接
	
	public SocketConnObject(String ip, String port) throws SunECMException{
		this(ip,port,Configuration.getBoolean("ssl.client.enable", false));
	}
	
	public SocketConnObject(String ip, String port,boolean sslEnable) throws SunECMException{
		ip=CodeUtil.changeIp(ip);
		this.ip = ip;
		this.port = port;
		log.debug( this.ip + "======" + this.port);
		SocketFactory scf;
		int iport = 0;
		if(sslEnable){
//			iport  = Configuration.getInt("ssl.client.port", 8025);
			iport = Integer.parseInt(port); //2019/12/2  修改ssl 端口获取
			scf=new JSSEClientSocketFactory();
		}else {
			iport = Integer.parseInt(port);
			scf=SocketFactory.getDefault();
		}
		//创建socket连接对象
		try {
			Socket socket =scf.createSocket(ip, iport);
			connObject = new SocketConn(socket);
		} catch (Exception e) {
			log.error("连接服务器"+ip+":"+port+"发生问题",e);
			throw new SunECMException(SunECMExceptionStatus.SERVER_CONNECTION_FAIL, "IO exception ip=" + ip + ";port = " + port, e);
		}
		//……
	}
   /**
	 * 关闭连接
	 * @throws IOException 
	 */
	public void closeConn(){
		try{
			//关闭socket连接
			try {
				connObject.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			} catch (Exception e) {
				log.error( "socket ip=" + ip +"; port = "+port + " close fail , error info-->" + e.getMessage());
			}
		}finally{
			connObject.destroy();
		}
	}

	/**
	 * 获取连接的IP
	 * @return
	 */
	public String getIp() {
		return ip;
	}
	/**
	 * 获取Socket连接对象
	 * @return
	 */
	public SocketConn getClientTrans(){
		return connObject;
	}
}