<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

	<bean id="sunECMServerIni" class="com.sunyard.initialization.SunECMServerIni"
		destroy-method="stop">
	</bean>
	<bean id="heartBeatOfDM"
		class="com.sunyard.client.conn.HeartBeatOfDM"  destroy-method="stop">
	</bean>
	<bean id="service" class="com.syd.common.serviceinit.ServiceLoader"
		scope="singleton" init-method="loading">
		<property name="interruptLoad" value="false" />
		<property name="serviceList">
			<list>
				<ref bean="sunECMServerIni" />
				<ref bean="socketInitial" />
				<ref bean="heartBeatOfDM" />
			</list>
		</property>
	</bean>

	<bean id="baseServer" class="com.sunyard.ecm.server.service.BaseMessageServer"
		abstract="true">
		<property name="checkDao" ref="checkDao" />
		<property name="dMDBDao" ref="dMDBDao" />
		<property name="loginDao" ref="loginDao" />
		<property name="offlineService" ref="offlineService" />
		<property name="extStoreService" ref="extStoreService" />
		<property name="sunECMDao" ref="sunECMDao" />
		<property name="tokenRelUserUtil" ref="tokenRelUserUtil"/>
		<property name="eventManageCenter" ref="eventManageCenter"/>
	</bean>

	<bean id="server" class="com.sunyard.ecm.server.service.UAMessageServer"
		parent="baseServer">
	</bean>

	<bean id="offlineService" class="com.sunyard.ecm.server.service.OfflineService">
		<property name="eventManageCenter" ref="eventManageCenter"></property>
		<property name="sunECMDao" ref="sunECMDao" />
	</bean>
	<bean id="extStoreService" class="com.sunyard.ecm.server.service.ExtStoreService">
	</bean>
	<bean id="conParamService" class="com.sunyard.ecm.server.service.ConParamService">
	</bean>

	<bean id="fileServer" class="com.sunyard.ecm.server.ua.UAFileServer" />
	
	<bean id="encrypterService" class="com.sunyard.ecm.server.service.impl.SM4Encrypter">
	</bean>
	
	<!-- DESEncrypter SM4Encrypter -->
</beans>