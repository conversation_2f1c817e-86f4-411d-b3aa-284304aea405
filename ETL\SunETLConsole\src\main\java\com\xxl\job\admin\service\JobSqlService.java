package com.xxl.job.admin.service;

import com.xxl.job.admin.core.model.JobParam;
import com.xxl.job.admin.core.model.JobSql;
import com.xxl.job.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 任务SQL
 */
public interface JobSqlService {
    //获取参数列表
    public Map<String,Object> paramPageList(int start, int length, Integer jobId);


    /**
     * SQL信息
     * @param jobSql
     * @return
     */
    public ReturnT<String> add(JobSql jobSql);

    /**
     * 修改SQL配置
     * @param jobSql
     * @return
     */
    public  ReturnT<String> reschedule(JobSql jobSql);

    /**
     * 删除SQL
     * @param id
     * @return
     */
    public ReturnT<String> remove(int id);

    /**
     * 信息
     * @param jobSqls
     * @return
     */
	ReturnT<String> add(List<JobSql> jobSqls);
}
