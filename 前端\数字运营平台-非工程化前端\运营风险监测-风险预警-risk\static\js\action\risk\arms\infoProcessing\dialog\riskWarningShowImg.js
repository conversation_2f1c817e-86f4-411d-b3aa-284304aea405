/**
 * 预警看图处理对话框
 */

var riskWarningShowImg = new Vue({
    el: ".ars-show-img",//页面整体 ID
    data: {
        'tab':{ //选项卡
            'data':[
                {'class':'active','href':'#ars-img-voucher','title':'凭证信息'},
            ]
        },
        'table_voucher':{//凭证信息
            'table_id': 'ars-img-voucher-table', //当前页表格ID
            'table_column': common_dialog.dialog_img.table_voucher.table_column || [
                {name: 'displayIndex', label: '图像序号', align: 'center', width: 80},
                {name: 'batchId', label: '批次号', align: 'center', width: 180},
                {name: 'flowId', label: '业务流水', align: 'center', width: 100},
                {name: 'formName', label: '版面名称', align: 'center', width: 100},
                {name: 'inccodeinBatch', label: '批内码', align: 'center', width: 80},
                {name: 'errorFlag', label: '差错标志', align: 'center', width: 80},
                {name: 'insertDate', label: '录入日期', align: 'center', width: 100},
                {name: 'fileName', label: '文件名', align: 'center', width: 200}
            ], //表格字段名
            'table_data': common_dialog.dialog_img.table_voucher.table_data || [] //表格数据
        },
        'table_flow':{//流水信息
            'table_id':'ars-img-overPlus-table',//当前页表格ID
            'table_column': common_dialog.dialog_img.table_flow.table_column || "[]", //表格字段名
            'table_data': common_dialog.dialog_img.table_flow.table_data || [] //表格数据
        },
        'title':'预警信息',
        'form':[//补录信息
            {
                'type':'info',
                'name':'预警模型',
                'value': common_dialog.dialog_img.variable.model_info ? common_dialog.dialog_img.variable.model_info.modelName : '',
                'alias':'modelName'
            },{
                'type':'info',
                'name':'预警日期',
                'value': common_dialog.dialog_img.variable.warningData ? common_dialog.dialog_img.variable.warningData.busi_data_date : '',
                'alias':'busiDataDate'
            },{
                'type':'info',
                'name':'机构号',
                'value': common_dialog.dialog_img.variable.warningData ? common_dialog.dialog_img.variable.warningData.organ_no : '',
                'alias':'organNo'
            },{
                'type':'info',
                'name':'柜员号',
                'value': common_dialog.dialog_img.variable.warningData ? common_dialog.dialog_img.variable.warningData.teller_no : '',
                'alias':'tellerNo'
            }
        ],//信息列表
        'variable':{
            'batch_data':common_dialog.dialog_img.variable.batch_data || [],
            'flow_data':common_dialog.dialog_img.variable.flow_data || [],
            'image_data':common_dialog.dialog_img.variable.image_data || [],
            'img_batchId':'',
            'img_serialNo':'',
            'img_siteNo':'',
            'img_contentId':'',
            'img_inputDate':'',
            'img_fileName':'',
            'img_backFileName':'',
            'img_imgType':'0',
            'imgChangeBackBut':0,
            // 预警处理相关变量
            'showOperations': true,  // 是否显示操作按钮
            'warningStatus': '0',    // 预警状态：0-未处理，1-已通过，2-下发差错
            'modelId': '',           // 模型ID
            'modelRowId': '',        // 预警记录ID
            'tableName': '',         // 表名
            'busiDataDate': '',      // 业务日期
            'passReason': '',        // 通过原因
            'revokeReason': '',      // 撤销原因
            'currentTime': '',       // 当前时间
            'currentUser': {         // 当前用户信息
                'userNo': '',
                'userName': ''
            },
            'suspendInfo': {         // 挂起信息
                'isSuspended': false,
                'suspendUser': '',
                'suspendTime': '',
                'suspendReason': ''
            },
            'businessData': {},      // 业务数据
            'warningData': {}        // 预警数据
        },
        // 备注弹框配置
        'dialog_form_title': {}
    },
    methods:{
        /**
         * 将字符串转换为函数
         * @param item:函数字符串*/
        callFn:function(item) {
            var reg1 = /^\w+/g;
            var reg2 = /\(((.|)+?)\)/; //取小括号中的参数
            var fn = item.match(reg1)[0];
            var args = item.match(reg2)[1];
            if(commonBlank(args)){ //函数无参数
                this[fn].apply(this); //调用函数
            }else { //函数有参数
                this[fn].apply(this,args.split(',')); //调用函数
            }
        }, //callFn
        /**
         * init: 凭证信息-表格初始化*/
        initTable:function () {
            var self = this;

            /*表格初始化 begin*/
            commonInitDatagrid(this.table_voucher.table_id,{
                dialogFilterW:10,//当表格宽度小于设定值时，将表头的快速筛选更换为dialog模式。当值设为0时，表头将固定为dialog模式
                showCheckboxcol:false,
                columns:this.table_voucher.table_column,
                data:this.table_voucher.table_data
            });

            $("#"+this.table_voucher.table_id).on("click.bjui.datagrid.tr",function(e,checkbox){
                self.switchData();
                self.variable.imgChangeBackBut=0;
            });
            $('#'+this.table_voucher.table_id).on("completed.bjui.datagrid",function(){
                //默认选中第一张
                $('#'+self.table_voucher.table_id+' tr').eq(0).click();
            })
        },//initTable
        /**
         * init: 剩余流水-表格初始化*/
        initTableInfo:function () {
            /*表格初始化 begin*/
            commonInitDatagrid(this.table_flow.table_id,{
                dialogFilterW:10,//当表格宽度小于设定值时，将表头的快速筛选更换为dialog模式。当值设为0时，表头将固定为dialog模式
                showCheckboxcol:false,
                columns:this.table_flow.table_column,
                data:this.table_flow.table_data
            });
        },//initTableInfo
        switchData:function(){
            //获取当前选中的图像信息
            var selectedDatas = $('#' + this.table_voucher.table_id).data('selectedDatas');//取得选中行数据
            if (!selectedDatas || selectedDatas.length === 0) {
                return;
            }
            var selectedData = selectedDatas[0];

            // 设置图像显示参数（使用后端返回的字段名）
            this.variable.img_batchId = selectedData.batchId || '';
            this.variable.img_inputDate = selectedData.insertDate || selectedData.inputDate || '';
            this.variable.img_fileName = selectedData.fileName || '';
            this.variable.img_backFileName = selectedData.backFileName || '';
            this.variable.img_siteNo = selectedData.siteNo || '';
            this.variable.img_contentId = selectedData.contentId || '';
            this.variable.img_serialNo = selectedData.serialNo || '';

            // 重载流水数据（如果有的话）
            if (this.variable.flow_data && this.variable.flow_data.length > 0) {
                var flowDatas = [];
                var serialNo = this.variable.img_serialNo;
                $.each(this.variable.flow_data, function(index, item) {
                    if(item.LSERIAL_NO == serialNo || item.SERIAL_NO == serialNo){
                        flowDatas.push(item);
                    }
                });
                commonReloadDatagrid(this.table_flow.table_id, flowDatas);
            }

            //显示图像
            doShowImage(this.variable.img_batchId, this.variable.img_inputDate, this.variable.img_fileName, this.variable.img_siteNo,
                this.variable.img_contentId, this.variable.img_imgType, "ars-img");

            var self = this;
            setTimeout(function () {
                $.CurrentDialog.find(".viewer-toolbar ul").find('.viewer-back').remove()//先把viewer-back删掉,防止在差错管理中,出现两个正反面切换按钮
                $.CurrentDialog.find(".viewer-toolbar ul").append('<li role="button" class="viewer-back" title="正反面切换" onclick="riskWarningShowImg.changeBack()"></li>');

                // 确保图像居中显示
                self.ensureImageCentered();
            },200); // 增加延迟，确保图像完全加载
            this.variable.imgChangeBackBut = 0;

        },
        /**
         * img: 正反面切换*/
        changeBack:function (){
            if(this.variable.imgChangeBackBut==0){
                doShowImage(this.variable.img_batchId, this.variable.img_inputDate, this.variable.img_backFileName,this.variable.img_siteNo,
                    this.variable.img_contentId,this.variable.img_imgType,"ars-img");
                setTimeout(function () {
                    $.CurrentDialog.find(".viewer-toolbar ul").find('.viewer-back').remove()//先把viewer-back删掉,防止在差错管理中,出现两个正反面切换按钮
                    $.CurrentDialog.find(".viewer-toolbar ul").append('<li role="button" class="viewer-back" title="正反面切换" onclick="riskWarningShowImg.changeBack()"></li>');
                },100);
                this.variable.imgChangeBackBut=1;
            }else{
                doShowImage(this.variable.img_batchId, this.variable.img_inputDate, this.variable.img_fileName,this.variable.img_siteNo,
                    this.variable.img_contentId,this.variable.img_imgType,"ars-img");
                setTimeout(function () {
                    $.CurrentDialog.find(".viewer-toolbar ul").find('.viewer-back').remove()//先把viewer-back删掉,防止在差错管理中,出现两个正反面切换按钮
                    $.CurrentDialog.find(".viewer-toolbar ul").append('<li role="button" class="viewer-back" title="正反面切换" onclick="riskWarningShowImg.changeBack()"></li>');
                },100);
                this.variable.imgChangeBackBut=0;
            }
        },//changeBack
        /**
         * 页面整体布局*/
        maResize:function (){
            var self = this;
            setTimeout(function () {
                /*各区域块间大小设置 begin*/
                $('#ars-show-img').jqxSplitter({ height: '100%', width: '100%', orientation: 'vertical', panels: [{ size: '30%' }, { size: '70%'}] });//vertical设置左右区域，恢复原来的比例
                $('#ars-left-splitter').jqxSplitter({ height: '100%', width: '100%', orientation: 'horizontal',  panels: [{ size: '60%' }, { size: '40%'}] });//左侧上下分割：恢复原来的比例
                $('#ars-content-splitter').jqxSplitter({ height: '100%', width: '100%', orientation: 'horizontal',  panels: [{ size: '60%' }, { size: '40%'}] });//horizontal设置上下区域
                /*各区域块间大小设置 end*/

                // 延迟触发表格重绘，确保表格适应新的容器大小
                setTimeout(function() {
                    $(window).trigger(BJUI.eventType.resizeGrid);
                    // 布局调整后，确保图像重新居中
                    self.ensureImageCentered();
                }, 100);
            }, 200) // 确保DOM完全渲染
        },

        /**
         * 查询挂起状态
         */
        loadSuspendStatus: function() {
            var self = this;
            if (!this.variable.modelRowId) {
                return;
            }

            var msg = {
                "sysMap": {
                    "modelRowId": this.variable.modelRowId
                }
            };

            var response = commonPost(commonConst("SUNDA_RISK") + "/armsExhibitDataController/getSuspendInfo.do", $.toJSON(msg));
            if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                var suspendInfo = response.retMap.suspendInfo;
                if (suspendInfo && suspendInfo.isSuspended) {
                    // 设置挂起状态
                    self.variable.suspendInfo.isSuspended = true;
                    self.variable.suspendInfo.suspendTime = suspendInfo.suspendTime;
                    self.variable.suspendInfo.suspendUser = suspendInfo.suspendUser;
                } else {
                    // 未挂起状态
                    self.variable.suspendInfo.isSuspended = false;
                    self.variable.suspendInfo.suspendTime = '';
                    self.variable.suspendInfo.suspendUser = '';
                }
            }
        },

        /**
         * 标注通过（需要填写备注，与老系统pass方法一致）
         */
        pass: function() {
            // 打开备注填写弹框
            this.openPassReasonDialog();
        },

        /**
         * 打开标注通过备注填写弹框
         */
        openPassReasonDialog: function() {
            // 设置弹框配置
            this.dialog_form_title = {
                'title': '标注通过',
                'dialog_id': 'risk-warning-pass-reason-dialog',
                'url': 'static/html/common/dialog/dialogFormTitle.html',
                'dialog_title': '标注通过 --> 填写通过原因',
                'form_id': 'risk-warning-pass-reason-fm',
                'form_data': [
                    {
                        'type': 'textarea',
                        'name': '通过原因',
                        'id': 'risk-warning-pass-reason-textarea',
                        'rows': '4',
                        'cols': '30',
                        'value': '',
                        'required': 'required',
                        'placeholder': '请输入通过原因...'
                    }
                ],
                'buttons': [
                    {'type': 'button', 'name': '确定', 'icon': 'save', 'class': 'btn-primary', 'method': 'confirmPass()'},
                    {'type': 'button', 'name': '取消', 'icon': 'close', 'class': 'btn-danger', 'method': 'dialogClose()'}
                ]
            };

            // 设置全局变量供弹框使用
            common_dialog = this;

            // 打开弹框
            BJUI.dialog({
                id: this.dialog_form_title.dialog_id,
                title: this.dialog_form_title.title,
                url: this.dialog_form_title.url,
                width: 500,
                height: 300,
                loadingmask: true,
                onLoad: function() {
                    // 弹框加载完成后的回调，设置焦点到通过原因输入框
                    // 使用系统标准的焦点管理函数
                    commonInfoFocusClose('risk-warning-pass-reason-textarea');
                }
            });
        },

        /**
         * 确认标注通过
         */
        confirmPass: function() {
            var reason = $("#risk-warning-pass-reason-textarea").val();
            if (!reason || reason.trim() === "") {
                BJUI.alertmsg('error', "通过原因不能为空");
                return;
            }

            // 关闭备注弹框（通过ID关闭指定弹框）
            BJUI.dialog('close', 'risk-warning-pass-reason-dialog');

            // 确认弹窗
            BJUI.alertmsg('confirm', '确定要标注通过该预警信息吗?', {
                okCall: function() {
                    var msg = {
                        "sysMap": {
                            "modelId": riskWarningShowImg.variable.modelId,
                            "modelRowId": riskWarningShowImg.variable.modelRowId,
                            "tableName": riskWarningShowImg.variable.tableName,
                            "reason": reason.trim()
                        }
                    };

                    var response = commonPost(commonConst("SUNDA_RISK") + "/armsExhibitDataController/passRiskWarningWithReason.do", $.toJSON(msg));
                    if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                        BJUI.alertmsg('ok', '标注通过成功!');
                        riskWarningShowImg.variable.warningStatus = '1'; // 更新为已通过状态
                        // 安全地关闭看图页面，返回列表
                        riskWarningShowImg.safeCloseDialog();
                    } else {
                        BJUI.alertmsg('error', response.retMsg || "标注通过失败");
                    }
                }
            });
        },

        /**
         * 弹框关闭方法（只关闭备注弹框，不关闭看图页面）
         */
        dialogClose: function() {
            // 通过弹框ID关闭指定的备注弹框，避免误关父弹框
            BJUI.dialog('close', 'risk-warning-pass-reason-dialog');
        },

        /**
         * 安全地关闭看图页面
         */
        safeCloseDialog: function() {
            try {
                if (parent && parent.BJUI && parent.BJUI.dialog) {
                    parent.BJUI.dialog('closeCurrent');
                } else if (window.BJUI && window.BJUI.dialog) {
                    window.BJUI.dialog('closeCurrent');
                } else {
                    // 如果都不可用，尝试关闭当前窗口
                    window.close();
                }
            } catch (e) {
                console.warn('关闭看图页面失败:', e);
                // 即使关闭失败，也不影响用户操作
            }
        },

        /**
         * 更新预警状态（下发成功后调用）
         * @param {Object} data 包含modelId, modelRowId, formId, formType等
         * @param {Function} callback 回调函数
         */
        updateRiskWarningStatus: function(data, callback) {
            if (!data || !data.modelId || !data.modelRowId || !data.formId || !data.formType) {
                console.error("更新预警状态参数不完整");
                if (typeof callback === 'function') {
                    callback(false, "参数不完整");
                }
                return;
            }

            var msg = {
                "parameterList": [{
                    "model_id": data.modelId,
                    "model_row_id": data.modelRowId
                }],
                "sysMap": {
                    "form_id": data.formId,
                    "form_type": data.formType,
                    "tableName": data.tableName
                }
            };

            var response = commonAjax(commonConst("SUNDA_RISK") + "/armsExhibitDataController/updateRiskWarningStatus.do", $.toJSON(msg));

            if (response.retCode == commonConst('HANDLE_SUCCESS')) {
                console.log("预警状态更新成功: ", data);
                if (typeof callback === 'function') {
                    callback(true, "更新成功");
                }
            } else {
                console.error("预警状态更新失败: ", response.retMsg);
                if (typeof callback === 'function') {
                    callback(false, response.retMsg);
                }
            }
        },

        /**
         * 挂起预警任务（与老系统方法名一致）
         */
        suspendTask: function() {
            var self = this;

            var msg = {
                "sysMap": {
                    "modelRowId": this.variable.modelRowId
                }
            };

            var response = commonPost(commonConst("SUNDA_RISK") + "/armsExhibitDataController/suspendTask.do", $.toJSON(msg));
            if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                BJUI.alertmsg('ok', response.retMsg || '预警挂起成功!');
                self.variable.suspendInfo.isSuspended = true;
                self.variable.suspendInfo.suspendTime = new Date().toLocaleString();
                self.variable.suspendInfo.suspendUser = self.variable.currentUser.userName;
            } else {
                BJUI.alertmsg('error', response.retMsg || "挂起失败");
            }
        },

        /**
         * 解挂预警任务（与老系统方法名一致）
         */
        cancleSuspendTask: function() {
            var self = this;
            var msg = {
                "sysMap": {
                    "modelRowId": this.variable.modelRowId
                }
            };

            var response = commonPost(commonConst("SUNDA_RISK") + "/armsExhibitDataController/resumeTask.do", $.toJSON(msg));
            if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                BJUI.alertmsg('ok', response.retMsg || '取消挂起成功!');
                self.variable.suspendInfo.isSuspended = false;
                self.variable.suspendInfo.suspendTime = '';
                self.variable.suspendInfo.suspendUser = '';
            } else {
                BJUI.alertmsg('error', response.retMsg || "解挂失败");
            }
        },



        /**
         * 直接通过（与老系统方法名一致）
         */
        savePassByNoAction: function() {
            var self = this;

            // 确认弹窗
            BJUI.alertmsg('confirm', '确定要直接通过该预警信息吗?', {
                okCall: function() {
                    var msg = {
                        "sysMap": {
                            "modelId": self.variable.modelId,
                            "modelRowId": self.variable.modelRowId,
                            "tableName": self.variable.tableName
                        }
                    };

                    var response = commonPost(commonConst("SUNDA_RISK") + "/armsExhibitDataController/passRiskWarning.do", $.toJSON(msg));
                    if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                        if (response.retMap && response.retMap.result === '-1') {
                            BJUI.alertmsg('warn', '该预警信息已经被处理!');
                        } else if (response.retMap && response.retMap.result === '0') {
                            BJUI.alertmsg('warn', '锁定失败，该预警信息可能已被其他人锁定!');
                        } else {
                            BJUI.alertmsg('ok', '直接通过成功!');
                            self.variable.warningStatus = '1'; // 更新为已通过状态
                        }
                    } else {
                        BJUI.alertmsg('error', response.retMsg || "处理失败");
                    }
                }
            });
        },

        /**
         * 下发差错（与预警详情页保持一致）
         */
        error: function() {
            var self = this;

            // 创建请求参数（与预警详情页格式一致）
            var msg = {
                "parameterList": [{
                    "model_id": self.variable.modelId,
                    "modelrow_ids": [self.variable.modelRowId],
                    "formType": "3",                   // 单据类型：3表示预警单
                    "isBatch": "0"                     // 是否批量：0表示单笔
                }],
                "sysMap": {}
            };

            // 获取业务信息（复用预警详情页的接口）
            var data = commonGet(commonConst("SUNDA_RISK") + "/armsExhibitDataController/getRiskWarningDataForSlip.do", $.toJSON(msg));
            if (data.retCode == commonConst('HANDLE_SUCCESS')) {
                // 调用arms_dialog_et显示差错单弹窗
                BJUI.dialog({
                    id: 'arms-dialog-et',
                    title: '下发差错单',
                    url: 'static/html/risk/arms/dialog/dialogEt.html',
                    width: 700,
                    height: 420,
                    loadingmask: true,
                    max: true,
                    onLoad: function() {
                        // 确保modelData存在
                        if (!data.retMap.modelData || data.retMap.modelData.length === 0) {
                            commonError("预警数据为空");
                            BJUI.dialog('closeCurrent', '');
                            return;
                        }

                        // 将获取到的数据传递给arms_dialog_et
                        arms_dialog_et.dialog.left[1].table.table_data = data.retMap.modelData;

                        // 设置基础信息表单（完整复制预警详情页逻辑）
                        arms_dialog_et.dialog.left[0].form_data.forEach(function (item) {
                            if(item.alias=='formTypeName'){
                                // 修改表单类型为下拉列表
                                item.type = 'select';
                                item.value = '2'; // 默认值设为处理单
                                item.data = [
                                    {value: '2', name: '处理单'},
                                    {value: '7', name: '金库流程单'},
                                    {value: '8', name: '网点处理单'}
                                ];
                                item.show = true;
                                item.disabled = false;
                                item.readonly = false;
                                item.required = 'required'; // 修复：使用字符串格式
                                item.allowEmpty = false;
                                item.emptyText = null;

                                // 设置下拉框值
                                arms_dialog_et.$nextTick(function() {
                                    var selectElem = $("#arms-dialog-et-formTypeName");
                                    if (selectElem.length > 0) {
                                        selectElem.val('2').trigger('change');
                                        selectElem.parent().find('.dropdown-menu li[data-original-index="0"]').remove();
                                    }
                                });
                            } else if(item.alias=='operationDate'){
                                item.value = commonFormatDate(data.retMap.modelData[0].occur_date);
                            } else if(item.alias=='procDealTime'){
                                item.value = commonCurrentDateFormatStr('yyyy-MM-dd HH:mm:ss');
                            } else if(item.alias=='checkerNo'){
                                item.value = sessionObject.user_no;
                            } else if(item.alias=='netNo'){
                                item.value = commonFormatOrgan(data.retMap.modelData[0].site_no, data.retMap.modelData[0].site_name);
                            } else {
                                item.value = '';
                            }
                            item.show = true;
                        });

                        // 显示业务信息表单
                        arms_dialog_et.dialog.left[1].form = true;

                        // 使用fieldsDefines构建表单数据
                        var customFormData = [];
                        var modelData = data.retMap.modelData[0];
                        var fieldDefs = data.retMap.modelFieldResulets;

                        // 遍历所有字段定义
                        for (var i = 0; i < fieldDefs.length; i++) {
                            var field = fieldDefs[i];
                            var fieldName = field.name || field.NAME;
                            var displayName = field.ch_name || field.CH_NAME;

                            if (!fieldName || !displayName) continue;

                            // 尝试多种可能的键名格式
                            var value = "";
                            if (modelData[fieldName] !== undefined) {
                                value = modelData[fieldName];
                            } else if (modelData[fieldName.toUpperCase()] !== undefined) {
                                value = modelData[fieldName.toUpperCase()];
                            } else if (modelData[fieldName.toLowerCase()] !== undefined) {
                                value = modelData[fieldName.toLowerCase()];
                            }

                            // 确保value不是undefined或null
                            value = value === undefined || value === null ? "" : value;

                            customFormData.push({
                                'type': 'info',
                                'name': displayName,
                                'value': value,
                                'alias': fieldName
                            });
                        }

                        // 使用构建的表单数据
                        arms_dialog_et.dialog.left[1].form_data = customFormData;

                        // 隐藏警报信息栏
                        arms_dialog_et.dialog.left[2].show = false;

                        // 设置相关预警和关联预警模型
                        if (data.retMap.sameSiteCount || data.retMap.sameTellCount || data.retMap.sameAcctCount) {
                            arms_dialog_et.etSameEntry(data.retMap.sameSiteCount, data.retMap.sameTellCount, data.retMap.sameAcctCount);
                        }
                        if (data.retMap.relateList) {
                            arms_dialog_et.etRelateEntry(data.retMap.relateList, self.variable.modelId);
                        }

                        // 处理表单配置
                        var sendSlipBean = data.retMap.sendSlipBean;
                        if (sendSlipBean) {
                            arms_dialog_et.variable.workDate = sendSlipBean.workDate;
                            arms_dialog_et.dialog.left[0].form_data[1].value = commonFormatDate(sendSlipBean.backDate); // 应反馈日期
                            arms_dialog_et.variable.workTime = sendSlipBean.workTime;
                        }

                        // 处理差错单/预警单表单配置
                        arms_dialog_et.dialog.left[3].show = true;  // 显示差错信息
                        arms_dialog_et.dialog.left[6].show = false; // 隐藏撤销信息
                        arms_dialog_et.dialog.left[7].show = false; // 隐藏补充信息
                        arms_dialog_et.dialog.left[4].show = false; // 隐藏流程处理

                        // 获取差错类型和问题类型列表
                        var sendMsg = {
                            "parameterList": [{"formId":""}],
                            "sysMap": {
                                "other_operation": "saveOrUpdateBusiformTb",
                                "entryId": self.variable.modelId
                            }
                        };
                        var recordData = commonAjax(commonConst("SUNDA_ET")+"/busiFormController/otherOperation.do", $.toJSON(sendMsg));
                        if (recordData.retCode == commonConst("HANDLE_SUCCESS")) {
                            // 设置差错归类列表
                            var slipTypeList = recordData.retMap.slipTypeList;
                            arms_dialog_et.variable.arms_slipTypeList = [];
                            for(var i=0; i<slipTypeList.length; i++) {
                                arms_dialog_et.variable.arms_slipTypeList.push({value:slipTypeList[i], name:slipTypeList[i]});
                            }

                            // 设置问题类型列表（修复：使用正确的数据结构）
                            var sourceTypeList = recordData.retMap.typeList;
                            arms_dialog_et.variable.arms_sourceList = [];
                            for(var j=0; j<sourceTypeList.length; j++) {
                                arms_dialog_et.variable.arms_sourceList.push({value:sourceTypeList[j].SOURCE_TYPE, name:sourceTypeList[j].SOURCE_TYPE});
                            }

                            // 获取管理条线列表
                            var gltxList = recordData.retMap.gltxList;
                            arms_dialog_et.variable.arms_gltxList = [];
                            if (gltxList && gltxList.length > 0) {
                                for(var k=0; k<gltxList.length; k++) {
                                    arms_dialog_et.variable.arms_gltxList.push({
                                        value: gltxList[k].substring(0, gltxList[k].indexOf("-")),
                                        name: gltxList[k]
                                    });
                                }
                            }

                            // 设置差错级别列表（如果存在）
                            if (recordData.retMap.slipLevelList) {
                                var slipLevelList = recordData.retMap.slipLevelList;
                                arms_dialog_et.variable.arms_slipLevelList = [];
                                for(var l=0; l<slipLevelList.length; l++) {
                                    arms_dialog_et.variable.arms_slipLevelList.push({value:slipLevelList[l], name:slipLevelList[l]});
                                }
                            }

                            // 将数据分配给具体的表单字段
                            arms_dialog_et.dialog.left[3].form_data[0].data = arms_dialog_et.variable.arms_slipTypeList; // 差错归类
                            if (arms_dialog_et.variable.arms_slipLevelList) {
                                arms_dialog_et.dialog.left[3].form_data[2].data = arms_dialog_et.variable.arms_slipLevelList; // 差错级别
                            }
                            arms_dialog_et.dialog.left[5].form_data[0].data = arms_dialog_et.variable.arms_sourceList; // 问题类型

                            // 设置管理条线下拉列表
                            if (arms_dialog_et.dialog.left[3].form_data[5]) {
                                arms_dialog_et.dialog.left[3].form_data[5].data = arms_dialog_et.variable.arms_gltxList;
                            }

                            // 设置其他必要的变量
                            arms_dialog_et.variable.arms_sourceMap = recordData.retMap.riskSourceMap;
                            arms_dialog_et.variable.arms_riskmarkisrequired = "0";
                        }

                        // 设置提交按钮处理函数（完整复制预警详情页逻辑）
                        $("#arms-dialog-et-submit").off("click").on("click", function() {
                            // 直接从select元素获取选择的表单类型
                            var selectedFormType = $("#arms-dialog-et-formTypeName").val();

                            // 如果没有找到，使用默认值
                            if (!selectedFormType) {
                                selectedFormType = "2"; // 默认使用"2"(处理单)
                            }

                            // 监听AJAX成功事件，只执行一次
                            $(document).one('ajaxSuccess', function(_, xhr, settings) {
                                if (settings.url.indexOf("/busiFormController/add.do") > -1) {
                                    var response = $.parseJSON(xhr.responseText);
                                    if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                                        // 获取差错单ID
                                        var formIdList = response.retMap.fromIdList;
                                        if (formIdList && formIdList.length > 0) {
                                            // 更新预警状态
                                            self.updateRiskWarningStatus({
                                                modelId: self.variable.modelId,
                                                modelRowId: self.variable.modelRowId,
                                                formId: formIdList[0],
                                                formType: selectedFormType,
                                                tableName: self.variable.tableName
                                            }, function(success, msg) {
                                                if (success) {
                                                    console.log("预警状态更新成功");
                                                    self.variable.warningStatus = '2'; // 更新为已下发状态
                                                    BJUI.alertmsg('ok', '下发成功!');
                                                    // 关闭看图页面，返回列表
                                                    self.safeCloseDialog();
                                                } else {
                                                    console.error("预警状态更新失败: " + msg);
                                                    BJUI.alertmsg('error', '预警状态更新失败: ' + msg);
                                                }
                                            });
                                        }
                                    }
                                }
                            });

                            // 使用选择的表单类型
                            arms_dialog_et.addSlipForm(self.variable.modelId, self.variable.modelRowId, selectedFormType, 0, 0, 0, function(success) {
                                if (success) {
                                    console.log("差错单提交成功");
                                } else {
                                    console.error("差错单提交失败");
                                }
                            });
                        });
                    }
                });
            } else {
                commonError(data.retMsg || "获取预警数据失败");
            }
        },

        /**
         * 撤销通过
         */
        revokePass: function() {
            var self = this;

            BJUI.alertmsg('confirm', '确定要撤销通过该预警信息吗?', {
                okCall: function() {
                    // 按照后端期望的格式构造参数
                    var msg = {
                        "parameterList": [{
                            "modelRowId": [String(self.variable.modelRowId)] // 确保是字符串类型的数组
                        }],
                        "sysMap": {
                            "modelId": self.variable.modelId,
                            "arms_deal_mark": "0", // 撤销标记
                            "isBatch": "false" // 非批量操作
                        }
                    };

                    var response = commonPost(commonConst("SUNDA_RISK") + "/arms-model-dispose/dealExhibit.do", $.toJSON(msg));
                    if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                        BJUI.alertmsg('ok', '撤销成功!');
                        self.variable.warningStatus = '0'; // 更新为未处理状态
                    } else {
                        BJUI.alertmsg('error', response.retMsg || "撤销失败");
                    }
                }
            });
        },

        /**
         * 撤销下发
         */
        revokeIssue: function() {
            var self = this;

            BJUI.alertmsg('confirm', '确定要撤销下发该预警信息吗?', {
                okCall: function() {
                    // 按照后端期望的格式构造参数
                    var msg = {
                        "parameterList": [{
                            "modelRowId": [String(self.variable.modelRowId)] // 确保是字符串类型的数组
                        }],
                        "sysMap": {
                            "modelId": self.variable.modelId,
                            "arms_deal_mark": "0", // 撤销标记
                            "isBatch": "false" // 非批量操作
                        }
                    };

                    var response = commonPost(commonConst("SUNDA_RISK") + "/arms-model-dispose/dealExhibit.do", $.toJSON(msg));
                    if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                        BJUI.alertmsg('ok', '撤销成功!');
                        self.variable.warningStatus = '0'; // 更新为未处理状态
                    } else {
                        BJUI.alertmsg('error', response.retMsg || "撤销失败");
                    }
                }
            });
        },

        /**
         * 确保图像居中显示
         */
        ensureImageCentered: function() {
            try {
                var $viewer = $('#ars-img').next('.viewer-container');
                if ($viewer.length > 0) {
                    var $canvas = $viewer.find('.viewer-canvas');
                    if ($canvas.length > 0) {
                        // 只触发viewer的resize事件，重新计算居中位置，不改变缩放比例
                        $('#ars-img').viewer('resize');
                    }
                }
            } catch (e) {
                console.log("图像居中处理异常：", e);
            }
        },

        /**
         * 初始化预警处理相关数据
         */
        initWarningData: function() {
            // 从外部传入的数据中获取预警信息
            if (common_dialog && common_dialog.dialog_img && common_dialog.dialog_img.variable) {
                var externalData = common_dialog.dialog_img.variable;

                // 设置预警相关数据
                this.variable.modelId = externalData.modelId || '';
                this.variable.modelRowId = externalData.modelRowId || '';
                this.variable.tableName = externalData.tableName || '';
                this.variable.warningStatus = externalData.warningStatus || '0';
                this.variable.busiDataDate = externalData.busiDataDate || '';

                // 设置用户信息
                if (externalData.currentUser) {
                    this.variable.currentUser = externalData.currentUser;
                }

                // 设置挂起信息
                if (externalData.suspendInfo) {
                    this.variable.suspendInfo = externalData.suspendInfo;
                }

                // 设置业务数据和预警数据
                this.variable.businessData = externalData.businessData || {};
                this.variable.warningData = externalData.warningData || {};
            }

        }
    },
    mounted: function () {//页面元素加载完成后执行
        this.$nextTick(function () {//整个视图都已渲染完毕
            try {
                // 初始化预警处理相关数据
                this.initWarningData();

                // 查询挂起状态
                this.loadSuspendStatus();

                this.maResize();
                $('window').resize(function(){
                    riskWarningShowImg.maResize();
                });
                this.initTable();
                this.initTableInfo();

                $.CurrentDialog.find('.restore').click(function () {
                    riskWarningShowImg.maResize();
                });
                $.CurrentDialog.find('.maximize').click(function () {
                    riskWarningShowImg.maResize();
                });

            } catch (err) {
                commonAosError(err.name + '：' + err.message, '前台执行异常');
            }
        })
    }
});
