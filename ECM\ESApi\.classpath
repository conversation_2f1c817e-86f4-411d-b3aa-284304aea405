<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8"/>
	<classpathentry kind="src" path="src"/>
	<classpathentry kind="lib" path="lib/elasticsearch-7.14.2.jar"/>
	<classpathentry kind="lib" path="lib/elasticsearch-core-7.14.2.jar"/>
	<classpathentry kind="lib" path="lib/elasticsearch-rest-client-7.15.2.jar"/>
	<classpathentry kind="lib" path="lib/elasticsearch-rest-high-level-client-7.14.2.jar"/>
	<classpathentry kind="lib" path="lib/elasticsearch-x-content-7.14.2.jar"/>
	<classpathentry kind="lib" path="lib/httpasyncclient-4.1.5.jar"/>
	<classpathentry kind="lib" path="lib/httpclient-4.5.13.jar"/>
	<classpathentry kind="lib" path="lib/httpcore-4.4.15.jar"/>
	<classpathentry kind="lib" path="lib/Jace.jar"/>
	<classpathentry kind="lib" path="lib/jackson-core-2.13.1.jar"/>
	<classpathentry kind="lib" path="lib/jackson-databind-2.13.1.jar"/>
	<classpathentry kind="lib" path="lib/log4j-1.2-api-2.17.0.jar"/>
	<classpathentry kind="lib" path="lib/log4j-api-2.17.0.jar"/>
	<classpathentry kind="lib" path="lib/log4j-core-2.17.0.jar"/>
	<classpathentry kind="lib" path="lib/lucene-core-8.9.0.jar"/>
	<classpathentry kind="output" path="bin"/>
</classpath>
