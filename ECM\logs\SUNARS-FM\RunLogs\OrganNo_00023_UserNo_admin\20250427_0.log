2025-04-27 09:35:34.431 [OrganNo_00023_UserNo_admin] [8e30730d5a14f4a3/2ac2701f26370e20] [http-nio-9009-exec-16] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-27 09:35:34.469 [OrganNo_00023_UserNo_admin] [8e30730d5a14f4a3/2fc7590c4786eced] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-27 09:35:34.470 [OrganNo_00023_UserNo_admin] [8e30730d5a14f4a3/2fc7590c4786eced] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-27 09:35:34.500 [OrganNo_00023_UserNo_admin] [8e30730d5a14f4a3/2fc7590c4786eced] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-27 09:35:34.501 [OrganNo_00023_UserNo_admin] [8e30730d5a14f4a3/2fc7590c4786eced] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-27 09:35:34.501 [OrganNo_00023_UserNo_admin] [8e30730d5a14f4a3/2fc7590c4786eced] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-27 09:35:34.531 [OrganNo_00023_UserNo_admin] [8e30730d5a14f4a3/2fc7590c4786eced] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-27 09:35:34.612 [OrganNo_00023_UserNo_admin] [8e30730d5a14f4a3/2ac2701f26370e20] [http-nio-9009-exec-16] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-27 09:35:34.686 [OrganNo_00023_UserNo_admin] [0a7e540e016dd10d/36691bff0193411c] [http-nio-9009-exec-17] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-27 09:35:34.706 [OrganNo_00023_UserNo_admin] [0a7e540e016dd10d/18075e243e950014] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-04-27 09:35:34.707 [OrganNo_00023_UserNo_admin] [0a7e540e016dd10d/18075e243e950014] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String)
2025-04-27 09:35:34.737 [OrganNo_00023_UserNo_admin] [0a7e540e016dd10d/18075e243e950014] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-04-27 09:35:34.738 [OrganNo_00023_UserNo_admin] [0a7e540e016dd10d/18075e243e950014] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-04-27 09:35:34.738 [OrganNo_00023_UserNo_admin] [0a7e540e016dd10d/18075e243e950014] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 15(Integer)
2025-04-27 09:35:34.772 [OrganNo_00023_UserNo_admin] [0a7e540e016dd10d/18075e243e950014] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 4
2025-04-27 09:35:34.842 [OrganNo_00023_UserNo_admin] [0a7e540e016dd10d/36691bff0193411c] [http-nio-9009-exec-17] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"46e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"15370-中国银行四川省分行支付清算部",
				"oganNo":"15370",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行支付清算部",
				"siteNo":"15370",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":4,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-27 09:35:35.528 [OrganNo_00023_UserNo_admin] [854f344c6d87b1ba/584a079022c41570] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-27 09:35:35.535 [OrganNo_00023_UserNo_admin] [854f344c6d87b1ba/b5774853777f7240] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-27 09:35:35.537 [OrganNo_00023_UserNo_admin] [854f344c6d87b1ba/b5774853777f7240] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-27 09:35:35.567 [OrganNo_00023_UserNo_admin] [854f344c6d87b1ba/b5774853777f7240] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-27 09:35:35.568 [OrganNo_00023_UserNo_admin] [854f344c6d87b1ba/b5774853777f7240] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-27 09:35:35.569 [OrganNo_00023_UserNo_admin] [854f344c6d87b1ba/b5774853777f7240] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-27 09:35:35.600 [OrganNo_00023_UserNo_admin] [854f344c6d87b1ba/b5774853777f7240] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-27 09:35:35.670 [OrganNo_00023_UserNo_admin] [854f344c6d87b1ba/584a079022c41570] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-27 09:35:35.732 [OrganNo_00023_UserNo_admin] [c4a3397de4997acd/93f27c193d382a1f] [http-nio-9009-exec-19] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-04-27 09:35:35.750 [OrganNo_00023_UserNo_admin] [c4a3397de4997acd/cbe681afcab0a2cf] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-04-27 09:35:35.750 [OrganNo_00023_UserNo_admin] [c4a3397de4997acd/cbe681afcab0a2cf] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String), 23(String)
2025-04-27 09:35:35.782 [OrganNo_00023_UserNo_admin] [c4a3397de4997acd/cbe681afcab0a2cf] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-04-27 09:35:35.783 [OrganNo_00023_UserNo_admin] [c4a3397de4997acd/cbe681afcab0a2cf] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE USER_NO = ? and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-04-27 09:35:35.783 [OrganNo_00023_UserNo_admin] [c4a3397de4997acd/cbe681afcab0a2cf] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 23(String), 15(Integer)
2025-04-27 09:35:35.814 [OrganNo_00023_UserNo_admin] [c4a3397de4997acd/cbe681afcab0a2cf] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-04-27 09:35:35.886 [OrganNo_00023_UserNo_admin] [c4a3397de4997acd/93f27c193d382a1f] [http-nio-9009-exec-19] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"46e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"15370-中国银行四川省分行支付清算部",
				"oganNo":"15370",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行支付清算部",
				"siteNo":"15370",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-27 09:35:47.351 [OrganNo_00023_UserNo_admin] [c348b52437770220/0508b699b35e2ce2] [http-nio-9009-exec-21] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"appType":"1",
			"businessDate":"20250427",
			"codeName":"保存15年 (默认)",
			"codeNo":"0",
			"endBusiDate":"20250427",
			"remark":"",
			"siteName":"中国银行四川省分行",
			"siteNo":"00023",
			"tellerName":"系统超级管理员",
			"tellerNo":"admin",
			"warrantAmount":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP001"
	}
}
2025-04-27 09:35:47.431 [OrganNo_00023_UserNo_admin] [c348b52437770220/0508b699b35e2ce2] [http-nio-9009-exec-21] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"500",
	"retMap":{
		
	},
	"retMsg":"第1条数据的柜员系统超级管理员不属于当前机构;"
}
2025-04-27 09:35:55.750 [OrganNo_00023_UserNo_admin] [97760bc04215b58c/5147398ea89f437e] [http-nio-9009-exec-23] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"appType":"1",
			"businessDate":"20250427",
			"codeName":"保存15年 (默认)",
			"codeNo":"0",
			"endBusiDate":"20250427",
			"remark":"",
			"siteName":"中国银行四川省分行",
			"siteNo":"00023",
			"tellerName":"系统超级管理员",
			"tellerNo":"admin",
			"warrantAmount":"2"
		}
	],
	"sysMap":{
		"oper_type":"OP001"
	}
}
2025-04-27 09:35:55.827 [OrganNo_00023_UserNo_admin] [97760bc04215b58c/5147398ea89f437e] [http-nio-9009-exec-23] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"500",
	"retMap":{
		
	},
	"retMsg":"第1条数据的柜员系统超级管理员不属于当前机构;"
}
2025-04-27 09:36:05.474 [OrganNo_00023_UserNo_admin] [764c1a045f53e3fa/47a9bd30ff21bd02] [http-nio-9009-exec-26] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"appType":"1",
			"businessDate":"20250427",
			"codeName":"保存30年",
			"codeNo":"1",
			"endBusiDate":"20250427",
			"remark":"",
			"siteName":"中国银行四川省分行",
			"siteNo":"00023",
			"tellerName":"系统超级管理员",
			"tellerNo":"admin",
			"warrantAmount":"2"
		}
	],
	"sysMap":{
		"oper_type":"OP001"
	}
}
2025-04-27 09:36:05.563 [OrganNo_00023_UserNo_admin] [764c1a045f53e3fa/47a9bd30ff21bd02] [http-nio-9009-exec-26] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"500",
	"retMap":{
		
	},
	"retMsg":"第1条数据的柜员系统超级管理员不属于当前机构;"
}
2025-04-27 09:36:23.157 [OrganNo_00023_UserNo_admin] [2a72eaac3c19531f/9550e0d209a332b9] [http-nio-9009-exec-27] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-27 09:36:23.164 [OrganNo_00023_UserNo_admin] [2a72eaac3c19531f/b00c676a8a748c04] [http-nio-9009-exec-27] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-27 09:36:23.164 [OrganNo_00023_UserNo_admin] [2a72eaac3c19531f/b00c676a8a748c04] [http-nio-9009-exec-27] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-27 09:36:23.193 [OrganNo_00023_UserNo_admin] [2a72eaac3c19531f/b00c676a8a748c04] [http-nio-9009-exec-27] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-27 09:36:23.194 [OrganNo_00023_UserNo_admin] [2a72eaac3c19531f/b00c676a8a748c04] [http-nio-9009-exec-27] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-27 09:36:23.194 [OrganNo_00023_UserNo_admin] [2a72eaac3c19531f/b00c676a8a748c04] [http-nio-9009-exec-27] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-27 09:36:23.224 [OrganNo_00023_UserNo_admin] [2a72eaac3c19531f/b00c676a8a748c04] [http-nio-9009-exec-27] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-27 09:36:23.298 [OrganNo_00023_UserNo_admin] [2a72eaac3c19531f/9550e0d209a332b9] [http-nio-9009-exec-27] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-27 09:36:23.362 [OrganNo_00023_UserNo_admin] [efe6a46c7a10cafe/f1251066d44b9c47] [http-nio-9009-exec-28] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-27 09:36:23.381 [OrganNo_00023_UserNo_admin] [efe6a46c7a10cafe/07ea20c7f481da8c] [http-nio-9009-exec-28] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-04-27 09:36:23.381 [OrganNo_00023_UserNo_admin] [efe6a46c7a10cafe/07ea20c7f481da8c] [http-nio-9009-exec-28] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String)
2025-04-27 09:36:23.411 [OrganNo_00023_UserNo_admin] [efe6a46c7a10cafe/07ea20c7f481da8c] [http-nio-9009-exec-28] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-04-27 09:36:23.412 [OrganNo_00023_UserNo_admin] [efe6a46c7a10cafe/07ea20c7f481da8c] [http-nio-9009-exec-28] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-04-27 09:36:23.412 [OrganNo_00023_UserNo_admin] [efe6a46c7a10cafe/07ea20c7f481da8c] [http-nio-9009-exec-28] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 15(Integer)
2025-04-27 09:36:23.449 [OrganNo_00023_UserNo_admin] [efe6a46c7a10cafe/07ea20c7f481da8c] [http-nio-9009-exec-28] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 4
2025-04-27 09:36:23.520 [OrganNo_00023_UserNo_admin] [efe6a46c7a10cafe/f1251066d44b9c47] [http-nio-9009-exec-28] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"46e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"15370-中国银行四川省分行支付清算部",
				"oganNo":"15370",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行支付清算部",
				"siteNo":"15370",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":4,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-27 09:36:24.064 [OrganNo_00023_UserNo_admin] [41cf03450455235c/e2b7f6b6702f4dd3] [http-nio-9009-exec-29] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-27 09:36:24.069 [OrganNo_00023_UserNo_admin] [41cf03450455235c/c7387fa61917cf04] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-27 09:36:24.069 [OrganNo_00023_UserNo_admin] [41cf03450455235c/c7387fa61917cf04] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-27 09:36:24.099 [OrganNo_00023_UserNo_admin] [41cf03450455235c/c7387fa61917cf04] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-27 09:36:24.100 [OrganNo_00023_UserNo_admin] [41cf03450455235c/c7387fa61917cf04] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-27 09:36:24.100 [OrganNo_00023_UserNo_admin] [41cf03450455235c/c7387fa61917cf04] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-27 09:36:24.131 [OrganNo_00023_UserNo_admin] [41cf03450455235c/c7387fa61917cf04] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-27 09:36:24.203 [OrganNo_00023_UserNo_admin] [41cf03450455235c/e2b7f6b6702f4dd3] [http-nio-9009-exec-29] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-27 09:36:24.266 [OrganNo_00023_UserNo_admin] [d3108e5f45da7d6f/1e810764a07034b3] [http-nio-9009-exec-30] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-27 09:36:24.284 [OrganNo_00023_UserNo_admin] [d3108e5f45da7d6f/1af0a2b447355b1c] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-04-27 09:36:24.284 [OrganNo_00023_UserNo_admin] [d3108e5f45da7d6f/1af0a2b447355b1c] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String)
2025-04-27 09:36:24.315 [OrganNo_00023_UserNo_admin] [d3108e5f45da7d6f/1af0a2b447355b1c] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-04-27 09:36:24.316 [OrganNo_00023_UserNo_admin] [d3108e5f45da7d6f/1af0a2b447355b1c] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-04-27 09:36:24.317 [OrganNo_00023_UserNo_admin] [d3108e5f45da7d6f/1af0a2b447355b1c] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 15(Integer)
2025-04-27 09:36:24.348 [OrganNo_00023_UserNo_admin] [d3108e5f45da7d6f/1af0a2b447355b1c] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 4
2025-04-27 09:36:24.423 [OrganNo_00023_UserNo_admin] [d3108e5f45da7d6f/1e810764a07034b3] [http-nio-9009-exec-30] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"46e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"15370-中国银行四川省分行支付清算部",
				"oganNo":"15370",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行支付清算部",
				"siteNo":"15370",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":4,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
