<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.sunyard"></context:component-scan>
    <!-- 消费方应用名，用于计算依赖关系，不是匹配条件，不要与提供方一样 -->
    <dubbo:application name="SunECMDM"  />

    <!-- 使用multicast广播注册中心暴露发现服务地址 -->
    <dubbo:registry address="nacos://***********:8848" />

<!--    &lt;!&ndash; 监控中心监控设置，直连监控中心服务器地址或者从注册中心发现 &ndash;&gt;-->
<!--    <dubbo:monitor protocol="registry"></dubbo:monitor>-->
    <!-- <dubbo:monitor address="127.0.0.1:7070"></dubbo:monitor> -->

    <!-- 生成远程服务代理，可以和本地bean一样使用demoService -->
    <dubbo:reference id="SunEcmDubboConsole" interface="com.sunyard.ws.internalapi.SunEcmConsole" timeout="5000"/>

</beans>
