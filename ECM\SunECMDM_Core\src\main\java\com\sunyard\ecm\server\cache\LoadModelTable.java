package com.sunyard.ecm.server.cache;

import com.sunyard.ecm.server.bean.ModelTableBean;
import com.sunyard.ecm.server.bean.TableBean;
import com.sunyard.ecm.server.cache.wsclient.GetServiceConfig;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import org.apache.cxf.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Title:内容模型表信息管理类
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class LoadModelTable {

	private Map<String, ModelTableBean> modelTable;
	// 获取配置信息类
	private GetServiceConfig getConfig = new GetServiceConfig();
	private final static  Logger log = LoggerFactory.getLogger(LoadModelTable.class);

	/**
	 * DM初始化时获取内容模型表
	 * */
	public LoadModelTable() {
		setModelTable();

	}

	/**
	 * 获得内容模型表信息
	 * */
	private void setModelTable() {

		try {
			List<ModelTableBean> list = getConfig.getModelTable();
			modelTable = new HashMap<String, ModelTableBean>();
			for (ModelTableBean modelTableBean : list) {
				modelTable.put(modelTableBean.getModel_code(), modelTableBean);
			}
			log.debug( "DM主动获取内容模型表成功-->" + modelTable);
		} catch (SunECMException e) {
			// webservice异常时，每隔20秒钟重新获取信息 直到获取成功
			log.error("Failed to setModelTable",e);
			try {
				Thread.sleep(20 * 1000);
			} catch (InterruptedException e1) {
			}
			if (LazySingleton.run) {
				setModelTable();
				log.error("Failed to setModelTable",e);
			}else {
				log.info("serverStop");
			}
			
		}

	}

	/**
	 * 更新内容模型表信息
	 * 
	 * @throws SunECMException
	 * */
	public void updateMetaTable(ModelTableBean bean) throws SunECMException {
		try {
			if (modelTable == null) {
				modelTable = new HashMap<String, ModelTableBean>();
			}
			ModelTableBean localModelTableBean = modelTable.get(bean
					.getModel_code());
			if (localModelTableBean == null) {
				throw new SunECMException(
						SunECMExceptionStatus.CONSOLE_CONFIG_FAIL,
						"更新内容模型表信息失败,内容模型[" + bean.getModel_code()
								+ "]的表列表信息不存在");
			}
			List<TableBean> localTables = localModelTableBean.getTable();
			List<TableBean> tables = bean.getTable();
			List<TableBean> reaultTables = new ArrayList<TableBean>();
			reaultTables.addAll(localTables);
			for (TableBean tableBean : tables) {
				boolean flag = true;
				for (TableBean localTable : localTables) {
					if (localTable.getTable_name().equals(
							tableBean.getTable_name())) {
						flag = false;
					}
				}
				if (flag) {
					reaultTables.add(tableBean);
				}
			}
			localModelTableBean.setTable(reaultTables);
			modelTable.put(bean.getModel_code(), localModelTableBean);
		} catch (Exception e) {
			throw new SunECMException(
					SunECMExceptionStatus.CONSOLE_CONFIG_FAIL, "更新内容模型表信息失败");
		}
	}

	public Map<String, ModelTableBean> getModelTable() {
		return modelTable;
	}

	/**
	 * 根据模型名获取分表对象
	 * @param modelName
	 * @return
	 */
	public ModelTableBean getModelBeanByModelName(String modelName){
		ModelTableBean bean = null;
		if(modelTable != null){
			bean = modelTable.get(modelName);
		}
		return bean;
	}
	public void setModelTable(Map<String, ModelTableBean> modelTable) {
		this.modelTable = modelTable;
	}

	/**
	 * 获取指定模型名和时间段内的分表表名
	 * 
	 * @param modelName
	 *            模型英文名
	 * @param startTime
	 *            时间段
	 * @return 分表表名
	 * @throws SunECMException
	 */
	public String getSubTable(String modelName, String startTime)
			throws SunECMException {
		ModelTableBean modelTableBean = modelTable.get(modelName); // 获取表名
		List<TableBean> tableBeans = modelTableBean.getTable();
		String tableName = "";
		for (TableBean tableBean : tableBeans) {
			log.debug( "---getSubTable======"
					+ tableBean.toString());
			if (startTime.compareTo(tableBean.getBegin_time()) >= 0) {
				if (!StringUtils.isEmpty(tableBean.getEnd_time()) ) {
					if (startTime.compareTo(tableBean.getEnd_time()) <= 0) {
						tableName = tableBean.getTable_name();
					}
				} else {
					tableName = tableBean.getTable_name();
				}
			}
		}
		if (StringUtils.isEmpty(tableName)) {
			throw new SunECMException(
					SunECMExceptionStatus.CANNOT_FIND_TABLE,
					"LoadModelTable-->getSubTable: can not find table, please check the table_time's column in the xml!");
		}
		return tableName;
	}

}