package com.sunyard.console.tagmanage.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <p>
 * Title: 标签关联属性信息bean
 * </p>
 * <p>
 * Description: 标签关联属性信息
 * </p>
 * <p>
 * Copyright: Copyright (c) 2022
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@XStreamAlias("tagRelAttributeBean")
public class TagRelAttributeBean {

	private String attribute_code; // 属性代码
	private String attribute_name; // 属性名称
	private String attribute_type; // 属性类型
	private String attribute_upUser; // 增加人
	private int attribute_motel_type; // 属性类型（1文档，2部件，3：文档和部件，4：其他）
	private String attribute_isindex; //是否为索引
	private String state;//状态 1未同步，2已同步，3同步后修改
	private int attribute_above;//默认为0
	public String getAttribute_code() {
		return attribute_code;
	}
	public void setAttribute_code(String attribute_code) {
		this.attribute_code = attribute_code;
	}
	public String getAttribute_name() {
		return attribute_name;
	}
	public void setAttribute_name(String attribute_name) {
		this.attribute_name = attribute_name;
	}
	public String getAttribute_type() {
		return attribute_type;
	}
	public void setAttribute_type(String attribute_type) {
		this.attribute_type = attribute_type;
	}
	public String getAttribute_upUser() {
		return attribute_upUser;
	}
	public void setAttribute_upUser(String attribute_upUser) {
		this.attribute_upUser = attribute_upUser;
	}
	public int getAttribute_motel_type() {
		return attribute_motel_type;
	}
	public void setAttribute_motel_type(int attribute_motel_type) {
		this.attribute_motel_type = attribute_motel_type;
	}
	public String getAttribute_isindex() {
		return attribute_isindex;
	}
	public void setAttribute_isindex(String attribute_isindex) {
		this.attribute_isindex = attribute_isindex;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public int getAttribute_above() {
		return attribute_above;
	}
	public void setAttribute_above(int attribute_above) {
		this.attribute_above = attribute_above;
	}
	
}
