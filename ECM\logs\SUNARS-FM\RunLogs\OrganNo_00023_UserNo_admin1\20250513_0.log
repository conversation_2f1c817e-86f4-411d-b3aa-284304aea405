2025-05-13 08:50:20.465 [OrganNo_00023_UserNo_admin1] [68fb3dcce42d988d/58f4b140ad170a8d] [http-nio-9009-exec-35] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:50:21.093 [OrganNo_00023_UserNo_admin1] [68fb3dcce42d988d/0126f2fba0cef9a5] [http-nio-9009-exec-35] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:50:21.100 [OrganNo_00023_UserNo_admin1] [68fb3dcce42d988d/0126f2fba0cef9a5] [http-nio-9009-exec-35] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:50:21.130 [OrganNo_00023_UserNo_admin1] [68fb3dcce42d988d/0126f2fba0cef9a5] [http-nio-9009-exec-35] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:50:21.131 [OrganNo_00023_UserNo_admin1] [68fb3dcce42d988d/0126f2fba0cef9a5] [http-nio-9009-exec-35] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:50:21.132 [OrganNo_00023_UserNo_admin1] [68fb3dcce42d988d/0126f2fba0cef9a5] [http-nio-9009-exec-35] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:50:21.159 [OrganNo_00023_UserNo_admin1] [68fb3dcce42d988d/0126f2fba0cef9a5] [http-nio-9009-exec-35] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:50:21.233 [OrganNo_00023_UserNo_admin1] [68fb3dcce42d988d/58f4b140ad170a8d] [http-nio-9009-exec-35] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:50:21.324 [OrganNo_00023_UserNo_admin1] [d2a98c9fe6f17e14/c3bd1b5939e60369] [http-nio-9009-exec-36] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-05-13 08:50:21.346 [OrganNo_00023_UserNo_admin1] [d2a98c9fe6f17e14/fe35b267de7540a6] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-05-13 08:50:21.347 [OrganNo_00023_UserNo_admin1] [d2a98c9fe6f17e14/fe35b267de7540a6] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-05-13 08:50:21.374 [OrganNo_00023_UserNo_admin1] [d2a98c9fe6f17e14/fe35b267de7540a6] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:50:21.446 [OrganNo_00023_UserNo_admin1] [d2a98c9fe6f17e14/c3bd1b5939e60369] [http-nio-9009-exec-36] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:50:21.536 [OrganNo_00023_UserNo_admin1] [549439c2d1e0927a/c33163c2f1f84cad] [http-nio-9009-exec-37] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:50:21.542 [OrganNo_00023_UserNo_admin1] [549439c2d1e0927a/e404e8ce09819126] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:50:21.543 [OrganNo_00023_UserNo_admin1] [549439c2d1e0927a/e404e8ce09819126] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:50:21.568 [OrganNo_00023_UserNo_admin1] [549439c2d1e0927a/e404e8ce09819126] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:50:21.568 [OrganNo_00023_UserNo_admin1] [549439c2d1e0927a/e404e8ce09819126] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:50:21.569 [OrganNo_00023_UserNo_admin1] [549439c2d1e0927a/e404e8ce09819126] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:50:21.595 [OrganNo_00023_UserNo_admin1] [549439c2d1e0927a/e404e8ce09819126] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:50:21.681 [OrganNo_00023_UserNo_admin1] [549439c2d1e0927a/c33163c2f1f84cad] [http-nio-9009-exec-37] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:50:21.761 [OrganNo_00023_UserNo_admin1] [0f1f9c70e214fd71/1a7f5a0cd077e82c] [http-nio-9009-exec-38] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 08:50:21.780 [OrganNo_00023_UserNo_admin1] [0f1f9c70e214fd71/3bd882732594d70d] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 08:50:21.781 [OrganNo_00023_UserNo_admin1] [0f1f9c70e214fd71/3bd882732594d70d] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 08:50:21.809 [OrganNo_00023_UserNo_admin1] [0f1f9c70e214fd71/3bd882732594d70d] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:50:21.810 [OrganNo_00023_UserNo_admin1] [0f1f9c70e214fd71/3bd882732594d70d] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 08:50:21.811 [OrganNo_00023_UserNo_admin1] [0f1f9c70e214fd71/3bd882732594d70d] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 08:50:21.837 [OrganNo_00023_UserNo_admin1] [0f1f9c70e214fd71/3bd882732594d70d] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 08:50:21.911 [OrganNo_00023_UserNo_admin1] [0f1f9c70e214fd71/1a7f5a0cd077e82c] [http-nio-9009-exec-38] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:53:27.025 [OrganNo_00023_UserNo_admin1] [c4b789c078832abd/4287f22b6ea82445] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:53:27.086 [OrganNo_00023_UserNo_admin1] [c4b789c078832abd/21089792404bdd9f] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:53:27.087 [OrganNo_00023_UserNo_admin1] [c4b789c078832abd/21089792404bdd9f] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:53:27.113 [OrganNo_00023_UserNo_admin1] [c4b789c078832abd/21089792404bdd9f] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:53:27.113 [OrganNo_00023_UserNo_admin1] [c4b789c078832abd/21089792404bdd9f] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:53:27.114 [OrganNo_00023_UserNo_admin1] [c4b789c078832abd/21089792404bdd9f] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:53:27.140 [OrganNo_00023_UserNo_admin1] [c4b789c078832abd/21089792404bdd9f] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:53:27.204 [OrganNo_00023_UserNo_admin1] [c4b789c078832abd/4287f22b6ea82445] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:53:27.282 [OrganNo_00023_UserNo_admin1] [2362096df6aef6d9/400cf647a9673bc0] [http-nio-9009-exec-41] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-05-13 08:53:27.296 [OrganNo_00023_UserNo_admin1] [2362096df6aef6d9/da03a9da985eec48] [http-nio-9009-exec-41] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-05-13 08:53:27.298 [OrganNo_00023_UserNo_admin1] [2362096df6aef6d9/da03a9da985eec48] [http-nio-9009-exec-41] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-05-13 08:53:27.323 [OrganNo_00023_UserNo_admin1] [2362096df6aef6d9/da03a9da985eec48] [http-nio-9009-exec-41] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:53:27.389 [OrganNo_00023_UserNo_admin1] [2362096df6aef6d9/400cf647a9673bc0] [http-nio-9009-exec-41] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:53:29.339 [OrganNo_00023_UserNo_admin1] [5d2f326e91617ad1/0f94ac92bc2a74f4] [http-nio-9009-exec-42] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:53:29.344 [OrganNo_00023_UserNo_admin1] [5d2f326e91617ad1/069a0aef94ad17bc] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:53:29.345 [OrganNo_00023_UserNo_admin1] [5d2f326e91617ad1/069a0aef94ad17bc] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:53:29.370 [OrganNo_00023_UserNo_admin1] [5d2f326e91617ad1/069a0aef94ad17bc] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:53:29.371 [OrganNo_00023_UserNo_admin1] [5d2f326e91617ad1/069a0aef94ad17bc] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:53:29.371 [OrganNo_00023_UserNo_admin1] [5d2f326e91617ad1/069a0aef94ad17bc] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:53:29.397 [OrganNo_00023_UserNo_admin1] [5d2f326e91617ad1/069a0aef94ad17bc] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:53:29.462 [OrganNo_00023_UserNo_admin1] [5d2f326e91617ad1/0f94ac92bc2a74f4] [http-nio-9009-exec-42] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:53:29.525 [OrganNo_00023_UserNo_admin1] [ac009aad387d6429/fc5d8e6975ae9c86] [http-nio-9009-exec-43] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 08:53:29.539 [OrganNo_00023_UserNo_admin1] [ac009aad387d6429/4a64c39e43617a07] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 08:53:29.540 [OrganNo_00023_UserNo_admin1] [ac009aad387d6429/4a64c39e43617a07] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 08:53:29.567 [OrganNo_00023_UserNo_admin1] [ac009aad387d6429/4a64c39e43617a07] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:53:29.568 [OrganNo_00023_UserNo_admin1] [ac009aad387d6429/4a64c39e43617a07] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 08:53:29.568 [OrganNo_00023_UserNo_admin1] [ac009aad387d6429/4a64c39e43617a07] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 08:53:29.599 [OrganNo_00023_UserNo_admin1] [ac009aad387d6429/4a64c39e43617a07] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 08:53:29.667 [OrganNo_00023_UserNo_admin1] [ac009aad387d6429/fc5d8e6975ae9c86] [http-nio-9009-exec-43] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:53:31.267 [OrganNo_00023_UserNo_admin1] [29e9829851d1e344/3bf554874788c7d9] [http-nio-9009-exec-44] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:53:31.272 [OrganNo_00023_UserNo_admin1] [29e9829851d1e344/ab885813734e2cff] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:53:31.273 [OrganNo_00023_UserNo_admin1] [29e9829851d1e344/ab885813734e2cff] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:53:31.299 [OrganNo_00023_UserNo_admin1] [29e9829851d1e344/ab885813734e2cff] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:53:31.299 [OrganNo_00023_UserNo_admin1] [29e9829851d1e344/ab885813734e2cff] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:53:31.300 [OrganNo_00023_UserNo_admin1] [29e9829851d1e344/ab885813734e2cff] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:53:31.325 [OrganNo_00023_UserNo_admin1] [29e9829851d1e344/ab885813734e2cff] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:53:31.392 [OrganNo_00023_UserNo_admin1] [29e9829851d1e344/3bf554874788c7d9] [http-nio-9009-exec-44] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:53:31.454 [OrganNo_00023_UserNo_admin1] [1a7f6ec81120bbba/f7888ebc58062e6c] [http-nio-9009-exec-45] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-05-13 08:53:31.468 [OrganNo_00023_UserNo_admin1] [1a7f6ec81120bbba/bc50f9fdbe968f87] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-05-13 08:53:31.468 [OrganNo_00023_UserNo_admin1] [1a7f6ec81120bbba/bc50f9fdbe968f87] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-05-13 08:53:31.493 [OrganNo_00023_UserNo_admin1] [1a7f6ec81120bbba/bc50f9fdbe968f87] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:53:31.564 [OrganNo_00023_UserNo_admin1] [1a7f6ec81120bbba/f7888ebc58062e6c] [http-nio-9009-exec-45] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:53:35.368 [OrganNo_00023_UserNo_admin1] [abe52b22a94cb82d/6aedce2b25684011] [http-nio-9009-exec-47] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:53:35.373 [OrganNo_00023_UserNo_admin1] [abe52b22a94cb82d/8480f2c1f1531661] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:53:35.374 [OrganNo_00023_UserNo_admin1] [abe52b22a94cb82d/8480f2c1f1531661] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:53:35.399 [OrganNo_00023_UserNo_admin1] [abe52b22a94cb82d/8480f2c1f1531661] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:53:35.400 [OrganNo_00023_UserNo_admin1] [abe52b22a94cb82d/8480f2c1f1531661] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:53:35.400 [OrganNo_00023_UserNo_admin1] [abe52b22a94cb82d/8480f2c1f1531661] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:53:35.425 [OrganNo_00023_UserNo_admin1] [abe52b22a94cb82d/8480f2c1f1531661] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:53:35.489 [OrganNo_00023_UserNo_admin1] [abe52b22a94cb82d/6aedce2b25684011] [http-nio-9009-exec-47] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:53:35.552 [OrganNo_00023_UserNo_admin1] [d9129db1595c98ad/fb7b3d48636a4c3c] [http-nio-9009-exec-48] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 08:53:35.566 [OrganNo_00023_UserNo_admin1] [d9129db1595c98ad/663d0781f6f60431] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 08:53:35.567 [OrganNo_00023_UserNo_admin1] [d9129db1595c98ad/663d0781f6f60431] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 08:53:35.593 [OrganNo_00023_UserNo_admin1] [d9129db1595c98ad/663d0781f6f60431] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:53:35.593 [OrganNo_00023_UserNo_admin1] [d9129db1595c98ad/663d0781f6f60431] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 08:53:35.594 [OrganNo_00023_UserNo_admin1] [d9129db1595c98ad/663d0781f6f60431] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 08:53:35.621 [OrganNo_00023_UserNo_admin1] [d9129db1595c98ad/663d0781f6f60431] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 08:53:35.693 [OrganNo_00023_UserNo_admin1] [d9129db1595c98ad/fb7b3d48636a4c3c] [http-nio-9009-exec-48] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:53:40.930 [OrganNo_00023_UserNo_admin1] [f83a61c05cc5fe1c/fce464372c6901d4] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:53:40.934 [OrganNo_00023_UserNo_admin1] [f83a61c05cc5fe1c/a362b695c474d74f] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:53:40.935 [OrganNo_00023_UserNo_admin1] [f83a61c05cc5fe1c/a362b695c474d74f] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:53:40.961 [OrganNo_00023_UserNo_admin1] [f83a61c05cc5fe1c/a362b695c474d74f] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:53:40.962 [OrganNo_00023_UserNo_admin1] [f83a61c05cc5fe1c/a362b695c474d74f] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:53:40.962 [OrganNo_00023_UserNo_admin1] [f83a61c05cc5fe1c/a362b695c474d74f] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:53:40.987 [OrganNo_00023_UserNo_admin1] [f83a61c05cc5fe1c/a362b695c474d74f] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:53:41.050 [OrganNo_00023_UserNo_admin1] [f83a61c05cc5fe1c/fce464372c6901d4] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:53:41.111 [OrganNo_00023_UserNo_admin1] [9f64e33f160745c4/990e2594864ff027] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-05-13 08:53:41.125 [OrganNo_00023_UserNo_admin1] [9f64e33f160745c4/85098659b6a20a3c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-05-13 08:53:41.126 [OrganNo_00023_UserNo_admin1] [9f64e33f160745c4/85098659b6a20a3c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-05-13 08:53:41.151 [OrganNo_00023_UserNo_admin1] [9f64e33f160745c4/85098659b6a20a3c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:53:41.220 [OrganNo_00023_UserNo_admin1] [9f64e33f160745c4/990e2594864ff027] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:53:49.051 [OrganNo_00023_UserNo_admin1] [29f0a2f4a9f10f63/1584dd42f9786774] [http-nio-9009-exec-53] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"appType":"1",
			"businessDate":"20250513",
			"codeName":"保存15年 (默认)",
			"codeNo":"0",
			"endBusiDate":"20250513",
			"remark":"",
			"siteName":"中国银行四川省分行",
			"siteNo":"00023",
			"tellerName":"系统超级管理员",
			"tellerNo":"admin1",
			"warrantAmount":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP001"
	}
}
2025-05-13 08:53:49.325 [OrganNo_00023_UserNo_admin1] [29f0a2f4a9f10f63/1584dd42f9786774] [http-nio-9009-exec-53] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"500",
	"retMap":{
		
	},
	"retMsg":"第1条数据的柜员系统超级管理员不属于当前机构;"
}
2025-05-13 08:53:54.678 [OrganNo_00023_UserNo_admin1] [3883813eb9d50f56/195e6d0fe14cea74] [http-nio-9009-exec-56] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:53:54.683 [OrganNo_00023_UserNo_admin1] [3883813eb9d50f56/00f8f69735e6ad62] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:53:54.683 [OrganNo_00023_UserNo_admin1] [3883813eb9d50f56/00f8f69735e6ad62] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:53:54.708 [OrganNo_00023_UserNo_admin1] [3883813eb9d50f56/00f8f69735e6ad62] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:53:54.709 [OrganNo_00023_UserNo_admin1] [3883813eb9d50f56/00f8f69735e6ad62] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:53:54.709 [OrganNo_00023_UserNo_admin1] [3883813eb9d50f56/00f8f69735e6ad62] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:53:54.734 [OrganNo_00023_UserNo_admin1] [3883813eb9d50f56/00f8f69735e6ad62] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:53:54.808 [OrganNo_00023_UserNo_admin1] [3883813eb9d50f56/195e6d0fe14cea74] [http-nio-9009-exec-56] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:53:54.871 [OrganNo_00023_UserNo_admin1] [45ada1ae933029c5/c54b7bd698607e5c] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 08:53:54.886 [OrganNo_00023_UserNo_admin1] [45ada1ae933029c5/ea2f91c41dfe0ab7] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 08:53:54.886 [OrganNo_00023_UserNo_admin1] [45ada1ae933029c5/ea2f91c41dfe0ab7] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 08:53:54.912 [OrganNo_00023_UserNo_admin1] [45ada1ae933029c5/ea2f91c41dfe0ab7] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:53:54.913 [OrganNo_00023_UserNo_admin1] [45ada1ae933029c5/ea2f91c41dfe0ab7] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 08:53:54.914 [OrganNo_00023_UserNo_admin1] [45ada1ae933029c5/ea2f91c41dfe0ab7] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 08:53:54.942 [OrganNo_00023_UserNo_admin1] [45ada1ae933029c5/ea2f91c41dfe0ab7] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 08:53:55.010 [OrganNo_00023_UserNo_admin1] [45ada1ae933029c5/c54b7bd698607e5c] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:54:02.043 [OrganNo_00023_UserNo_admin1] [74f96b1f3ed5f5e8/da79278ec26b5102] [http-nio-9009-exec-58] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:54:02.049 [OrganNo_00023_UserNo_admin1] [74f96b1f3ed5f5e8/a0cb43ae645674fc] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:54:02.049 [OrganNo_00023_UserNo_admin1] [74f96b1f3ed5f5e8/a0cb43ae645674fc] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:54:02.075 [OrganNo_00023_UserNo_admin1] [74f96b1f3ed5f5e8/a0cb43ae645674fc] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:54:02.075 [OrganNo_00023_UserNo_admin1] [74f96b1f3ed5f5e8/a0cb43ae645674fc] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:54:02.075 [OrganNo_00023_UserNo_admin1] [74f96b1f3ed5f5e8/a0cb43ae645674fc] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:54:02.101 [OrganNo_00023_UserNo_admin1] [74f96b1f3ed5f5e8/a0cb43ae645674fc] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:54:02.169 [OrganNo_00023_UserNo_admin1] [74f96b1f3ed5f5e8/da79278ec26b5102] [http-nio-9009-exec-58] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:54:02.233 [OrganNo_00023_UserNo_admin1] [04d1a6ee292f450f/87cd9fbe8d935662] [http-nio-9009-exec-59] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-05-13 08:54:02.245 [OrganNo_00023_UserNo_admin1] [04d1a6ee292f450f/09afb094758253c5] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-05-13 08:54:02.245 [OrganNo_00023_UserNo_admin1] [04d1a6ee292f450f/09afb094758253c5] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-05-13 08:54:02.272 [OrganNo_00023_UserNo_admin1] [04d1a6ee292f450f/09afb094758253c5] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:54:02.342 [OrganNo_00023_UserNo_admin1] [04d1a6ee292f450f/87cd9fbe8d935662] [http-nio-9009-exec-59] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:54:02.885 [OrganNo_00023_UserNo_admin1] [dc69cdcf4b10577d/e4c46fd9fef8f2f7] [http-nio-9009-exec-60] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:54:02.890 [OrganNo_00023_UserNo_admin1] [dc69cdcf4b10577d/17dbdb4de5fde42c] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:54:02.890 [OrganNo_00023_UserNo_admin1] [dc69cdcf4b10577d/17dbdb4de5fde42c] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:54:02.915 [OrganNo_00023_UserNo_admin1] [dc69cdcf4b10577d/17dbdb4de5fde42c] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:54:02.916 [OrganNo_00023_UserNo_admin1] [dc69cdcf4b10577d/17dbdb4de5fde42c] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:54:02.916 [OrganNo_00023_UserNo_admin1] [dc69cdcf4b10577d/17dbdb4de5fde42c] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:54:02.942 [OrganNo_00023_UserNo_admin1] [dc69cdcf4b10577d/17dbdb4de5fde42c] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:54:03.009 [OrganNo_00023_UserNo_admin1] [dc69cdcf4b10577d/e4c46fd9fef8f2f7] [http-nio-9009-exec-60] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:54:03.072 [OrganNo_00023_UserNo_admin1] [54389d2b1675536a/7aa890e2e5c0a50d] [http-nio-9009-exec-61] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-05-13 08:54:03.086 [OrganNo_00023_UserNo_admin1] [54389d2b1675536a/37b94d94345540e4] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE = ?
2025-05-13 08:54:03.086 [OrganNo_00023_UserNo_admin1] [54389d2b1675536a/37b94d94345540e4] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-05-13 08:54:03.112 [OrganNo_00023_UserNo_admin1] [54389d2b1675536a/37b94d94345540e4] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:54:03.113 [OrganNo_00023_UserNo_admin1] [54389d2b1675536a/37b94d94345540e4] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 08:54:03.113 [OrganNo_00023_UserNo_admin1] [54389d2b1675536a/37b94d94345540e4] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 23(String), 15(Integer)
2025-05-13 08:54:03.139 [OrganNo_00023_UserNo_admin1] [54389d2b1675536a/37b94d94345540e4] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 1
2025-05-13 08:54:03.212 [OrganNo_00023_UserNo_admin1] [54389d2b1675536a/7aa890e2e5c0a50d] [http-nio-9009-exec-61] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:54:03.272 [OrganNo_00023_UserNo_admin1] [a3b1686bd86d1204/7ddd73ef457df630] [http-nio-9009-exec-62] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-05-13 08:54:03.284 [OrganNo_00023_UserNo_admin1] [a3b1686bd86d1204/b04f06ec1975ec69] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-05-13 08:54:03.285 [OrganNo_00023_UserNo_admin1] [a3b1686bd86d1204/b04f06ec1975ec69] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin1(String)
2025-05-13 08:54:03.310 [OrganNo_00023_UserNo_admin1] [a3b1686bd86d1204/b04f06ec1975ec69] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-05-13 08:54:03.311 [OrganNo_00023_UserNo_admin1] [a3b1686bd86d1204/b04f06ec1975ec69] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-05-13 08:54:03.312 [OrganNo_00023_UserNo_admin1] [a3b1686bd86d1204/b04f06ec1975ec69] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin1(String), 15(Integer)
2025-05-13 08:54:03.338 [OrganNo_00023_UserNo_admin1] [a3b1686bd86d1204/b04f06ec1975ec69] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 2
2025-05-13 08:54:03.398 [OrganNo_00023_UserNo_admin1] [a3b1686bd86d1204/7ddd73ef457df630] [http-nio-9009-exec-62] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025042400023001",
				"baleState":"1",
				"id":"5289ce87003d45fc8202a33e783ad8f2",
				"sendDate":"20250424",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:54:05.599 [OrganNo_00023_UserNo_admin1] [8e190bea8fd4c7cc/368179e5c5971d0e] [http-nio-9009-exec-63] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:54:05.605 [OrganNo_00023_UserNo_admin1] [8e190bea8fd4c7cc/63995603eb0cf75b] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:54:05.605 [OrganNo_00023_UserNo_admin1] [8e190bea8fd4c7cc/63995603eb0cf75b] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:54:05.633 [OrganNo_00023_UserNo_admin1] [8e190bea8fd4c7cc/63995603eb0cf75b] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:54:05.634 [OrganNo_00023_UserNo_admin1] [8e190bea8fd4c7cc/63995603eb0cf75b] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:54:05.634 [OrganNo_00023_UserNo_admin1] [8e190bea8fd4c7cc/63995603eb0cf75b] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:54:05.659 [OrganNo_00023_UserNo_admin1] [8e190bea8fd4c7cc/63995603eb0cf75b] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:54:05.724 [OrganNo_00023_UserNo_admin1] [8e190bea8fd4c7cc/368179e5c5971d0e] [http-nio-9009-exec-63] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:54:05.785 [OrganNo_00023_UserNo_admin1] [a4fdb4fde9010e36/3c011e4f84ea5c4e] [http-nio-9009-exec-64] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-05-13 08:54:05.798 [OrganNo_00023_UserNo_admin1] [a4fdb4fde9010e36/181b79884d4dbcbc] [http-nio-9009-exec-64] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-05-13 08:54:05.798 [OrganNo_00023_UserNo_admin1] [a4fdb4fde9010e36/181b79884d4dbcbc] [http-nio-9009-exec-64] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-05-13 08:54:05.824 [OrganNo_00023_UserNo_admin1] [a4fdb4fde9010e36/181b79884d4dbcbc] [http-nio-9009-exec-64] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:54:05.895 [OrganNo_00023_UserNo_admin1] [a4fdb4fde9010e36/3c011e4f84ea5c4e] [http-nio-9009-exec-64] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:54:06.518 [OrganNo_00023_UserNo_admin1] [eb0868c0637c5252/81a9a6e992bbb763] [http-nio-9009-exec-65] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:54:06.523 [OrganNo_00023_UserNo_admin1] [eb0868c0637c5252/a679c9a51df53606] [http-nio-9009-exec-65] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:54:06.523 [OrganNo_00023_UserNo_admin1] [eb0868c0637c5252/a679c9a51df53606] [http-nio-9009-exec-65] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:54:06.549 [OrganNo_00023_UserNo_admin1] [eb0868c0637c5252/a679c9a51df53606] [http-nio-9009-exec-65] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:54:06.549 [OrganNo_00023_UserNo_admin1] [eb0868c0637c5252/a679c9a51df53606] [http-nio-9009-exec-65] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:54:06.549 [OrganNo_00023_UserNo_admin1] [eb0868c0637c5252/a679c9a51df53606] [http-nio-9009-exec-65] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:54:06.574 [OrganNo_00023_UserNo_admin1] [eb0868c0637c5252/a679c9a51df53606] [http-nio-9009-exec-65] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:54:06.642 [OrganNo_00023_UserNo_admin1] [eb0868c0637c5252/81a9a6e992bbb763] [http-nio-9009-exec-65] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:54:06.705 [OrganNo_00023_UserNo_admin1] [0de8a221b4c86e68/15a736b02442a928] [http-nio-9009-exec-66] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 08:54:06.719 [OrganNo_00023_UserNo_admin1] [0de8a221b4c86e68/f392aa43e56ad375] [http-nio-9009-exec-66] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 08:54:06.720 [OrganNo_00023_UserNo_admin1] [0de8a221b4c86e68/f392aa43e56ad375] [http-nio-9009-exec-66] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 08:54:06.746 [OrganNo_00023_UserNo_admin1] [0de8a221b4c86e68/f392aa43e56ad375] [http-nio-9009-exec-66] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 08:54:06.747 [OrganNo_00023_UserNo_admin1] [0de8a221b4c86e68/f392aa43e56ad375] [http-nio-9009-exec-66] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 08:54:06.748 [OrganNo_00023_UserNo_admin1] [0de8a221b4c86e68/f392aa43e56ad375] [http-nio-9009-exec-66] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 08:54:06.775 [OrganNo_00023_UserNo_admin1] [0de8a221b4c86e68/f392aa43e56ad375] [http-nio-9009-exec-66] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 08:54:06.845 [OrganNo_00023_UserNo_admin1] [0de8a221b4c86e68/15a736b02442a928] [http-nio-9009-exec-66] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 08:57:13.041 [OrganNo_00023_UserNo_admin1] [1e393db8498dbc84/ee929bce8c470d97] [http-nio-9009-exec-68] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 08:57:13.100 [OrganNo_00023_UserNo_admin1] [1e393db8498dbc84/9d2f28cdb8833802] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 08:57:13.101 [OrganNo_00023_UserNo_admin1] [1e393db8498dbc84/9d2f28cdb8833802] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 08:57:13.128 [OrganNo_00023_UserNo_admin1] [1e393db8498dbc84/9d2f28cdb8833802] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 08:57:13.129 [OrganNo_00023_UserNo_admin1] [1e393db8498dbc84/9d2f28cdb8833802] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 08:57:13.129 [OrganNo_00023_UserNo_admin1] [1e393db8498dbc84/9d2f28cdb8833802] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 08:57:13.154 [OrganNo_00023_UserNo_admin1] [1e393db8498dbc84/9d2f28cdb8833802] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 08:57:13.224 [OrganNo_00023_UserNo_admin1] [1e393db8498dbc84/ee929bce8c470d97] [http-nio-9009-exec-68] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 08:57:13.287 [OrganNo_00023_UserNo_admin1] [3979905b34783d09/f08148374ae3dd7a] [http-nio-9009-exec-69] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-05-13 08:57:13.296 [OrganNo_00023_UserNo_admin1] [3979905b34783d09/1ada62271af2c655] [http-nio-9009-exec-69] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-05-13 08:57:13.296 [OrganNo_00023_UserNo_admin1] [3979905b34783d09/1ada62271af2c655] [http-nio-9009-exec-69] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin1(String)
2025-05-13 08:57:13.322 [OrganNo_00023_UserNo_admin1] [3979905b34783d09/1ada62271af2c655] [http-nio-9009-exec-69] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-05-13 08:57:13.323 [OrganNo_00023_UserNo_admin1] [3979905b34783d09/1ada62271af2c655] [http-nio-9009-exec-69] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-05-13 08:57:13.323 [OrganNo_00023_UserNo_admin1] [3979905b34783d09/1ada62271af2c655] [http-nio-9009-exec-69] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin1(String), 15(Integer)
2025-05-13 08:57:13.348 [OrganNo_00023_UserNo_admin1] [3979905b34783d09/1ada62271af2c655] [http-nio-9009-exec-69] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 2
2025-05-13 08:57:13.410 [OrganNo_00023_UserNo_admin1] [3979905b34783d09/f08148374ae3dd7a] [http-nio-9009-exec-69] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025042400023001",
				"baleState":"1",
				"id":"5289ce87003d45fc8202a33e783ad8f2",
				"sendDate":"20250424",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-05-13 09:10:11.826 [OrganNo_00023_UserNo_admin1] [ba00a125f72d9298/42d5304c518bd134] [http-nio-9009-exec-71] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042100023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:10:12.128 [OrganNo_00023_UserNo_admin1] [ba00a125f72d9298/09f545f977fe682b] [http-nio-9009-exec-71] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:10:12.129 [OrganNo_00023_UserNo_admin1] [ba00a125f72d9298/09f545f977fe682b] [http-nio-9009-exec-71] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042100023001(String)
2025-05-13 09:10:12.163 [OrganNo_00023_UserNo_admin1] [ba00a125f72d9298/09f545f977fe682b] [http-nio-9009-exec-71] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:10:12.218 [OrganNo_00023_UserNo_admin1] [ba00a125f72d9298/42d5304c518bd134] [http-nio-9009-exec-71] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.sunyard.ars.fm.FmFunction
	at java.net.URLClassLoader.findClass(URLClassLoader.java:382)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:418)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:355)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:351)
	... 118 common frames omitted
2025-05-13 09:10:14.391 [OrganNo_00023_UserNo_admin1] [dd1d79ac88064c1c/bf371ae15d94ab18] [http-nio-9009-exec-72] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 09:10:14.665 [OrganNo_00023_UserNo_admin1] [dd1d79ac88064c1c/e5aabc65f0780517] [http-nio-9009-exec-72] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 09:10:14.666 [OrganNo_00023_UserNo_admin1] [dd1d79ac88064c1c/e5aabc65f0780517] [http-nio-9009-exec-72] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 09:10:14.697 [OrganNo_00023_UserNo_admin1] [dd1d79ac88064c1c/e5aabc65f0780517] [http-nio-9009-exec-72] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 09:10:14.698 [OrganNo_00023_UserNo_admin1] [dd1d79ac88064c1c/e5aabc65f0780517] [http-nio-9009-exec-72] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 09:10:14.699 [OrganNo_00023_UserNo_admin1] [dd1d79ac88064c1c/e5aabc65f0780517] [http-nio-9009-exec-72] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 09:10:14.730 [OrganNo_00023_UserNo_admin1] [dd1d79ac88064c1c/e5aabc65f0780517] [http-nio-9009-exec-72] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 09:10:14.810 [OrganNo_00023_UserNo_admin1] [dd1d79ac88064c1c/bf371ae15d94ab18] [http-nio-9009-exec-72] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 09:10:14.874 [OrganNo_00023_UserNo_admin1] [f004f90181c7574e/23dc6a6eb68daff3] [http-nio-9009-exec-73] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_4"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1",
		"isCenter":"1"
	}
}
2025-05-13 09:10:14.887 [OrganNo_00023_UserNo_admin1] [f004f90181c7574e/fef2af261687fce7] [http-nio-9009-exec-73] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE = ?
2025-05-13 09:10:14.887 [OrganNo_00023_UserNo_admin1] [f004f90181c7574e/fef2af261687fce7] [http-nio-9009-exec-73] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 14(String)
2025-05-13 09:10:14.917 [OrganNo_00023_UserNo_admin1] [f004f90181c7574e/fef2af261687fce7] [http-nio-9009-exec-73] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 09:10:14.918 [OrganNo_00023_UserNo_admin1] [f004f90181c7574e/fef2af261687fce7] [http-nio-9009-exec-73] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 09:10:14.919 [OrganNo_00023_UserNo_admin1] [f004f90181c7574e/fef2af261687fce7] [http-nio-9009-exec-73] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 14(String), 15(Integer)
2025-05-13 09:10:14.951 [OrganNo_00023_UserNo_admin1] [f004f90181c7574e/fef2af261687fce7] [http-nio-9009-exec-73] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 09:10:15.029 [OrganNo_00023_UserNo_admin1] [f004f90181c7574e/23dc6a6eb68daff3] [http-nio-9009-exec-73] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_4",
				"baleNo":"2025041800023001",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314183802129311",
				"applicationState":"FM_STATE_APP_4",
				"baleNo":"2025031400023002",
				"businessDate":"20250314",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400314",
				"endBusiDate":"20250314",
				"id":"8972b5df4f9a4606b5ce7ea1cf318625",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"19"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250429234215189911",
				"applicationState":"FM_STATE_APP_4",
				"baleNo":"2025042900023001",
				"batchId":"20250429234215189911",
				"businessDate":"20250429",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400429",
				"endBusiDate":"20250429",
				"id":"70b6a8d10b14417687c9c72257d7781e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250429",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"管理员",
				"tellerNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 09:10:15.124 [OrganNo_00023_UserNo_admin1] [3bf00d280d1dd8db/d534673999a7d0a8] [http-nio-9009-exec-74] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"packageState":"FM_STATE_PACK_1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-05-13 09:10:15.162 [OrganNo_00023_UserNo_admin1] [3bf00d280d1dd8db/01d7c0c6a8cb077e] [http-nio-9009-exec-74] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_PACKAGE WHERE PACKAGE_STATE = ? AND SITENO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-05-13 09:10:15.163 [OrganNo_00023_UserNo_admin1] [3bf00d280d1dd8db/01d7c0c6a8cb077e] [http-nio-9009-exec-74] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - ==> Parameters: 1(String), admin1(String)
2025-05-13 09:10:15.193 [OrganNo_00023_UserNo_admin1] [3bf00d280d1dd8db/01d7c0c6a8cb077e] [http-nio-9009-exec-74] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - <==      Total: 1
2025-05-13 09:10:15.194 [OrganNo_00023_UserNo_admin1] [3bf00d280d1dd8db/01d7c0c6a8cb077e] [http-nio-9009-exec-74] DEBUG c.s.a.f.d.P.selectPackagesByConfig - ==>  Preparing: select PACKAGE_NO "PACKAGE_NO", ID "ID", PACKAGE_STATE "PACKAGE_STATE", USER_NO "USER_NO", USER_NAME "USER_NAME", REGISTER_DATE "REGISTER_DATE", ORGAN_NO "ORGAN_NO", ORGAN_NAME "ORGAN_NAME",BELONG_YEAR "BELONG_YEAR", CODE_NO "CODE_NO", KEEP_YEAR "KEEP_YEAR", PACK_TYPE "PACK_TYPE", TODAY_NUM "TODAY_NUM", YEAR_NUM "YEAR_NUM", TRUNK_NO "TRUNK_NO", FRAMESEQ "FRAMESEQ",TOTAL_COUNT "TOTAL_COUNT", DESTROYDATE "DESTROYDATE", BEGINBUSIDATE "BEGINBUSIDATE", ENDBUSIDATE "ENDBUSIDATE", SITENO "SITENO", SITENAME "SITENAME", CODENAME "CODENAME", PLACEFILE "PLACEFILE", ALLTELLERS "ALLTELLERS", AREANO "AREANO", WAREHOUSENO "WAREHOUSENO", VOUCHER_COUNT "VOUCHER_COUNT" from FM_PACKAGE WHERE PACKAGE_STATE = ? and SITENO in ( select ORGAN_NO from SM_USER_ORGAN_TB WHERE USER_NO = ? ) order by SITENO,BEGINBUSIDATE,TODAY_NUM DESC LIMIT ?
2025-05-13 09:10:15.194 [OrganNo_00023_UserNo_admin1] [3bf00d280d1dd8db/01d7c0c6a8cb077e] [http-nio-9009-exec-74] DEBUG c.s.a.f.d.P.selectPackagesByConfig - ==> Parameters: 1(String), admin1(String), 15(Integer)
2025-05-13 09:10:15.228 [OrganNo_00023_UserNo_admin1] [3bf00d280d1dd8db/01d7c0c6a8cb077e] [http-nio-9009-exec-74] DEBUG c.s.a.f.d.P.selectPackagesByConfig - <==      Total: 1
2025-05-13 09:10:15.308 [OrganNo_00023_UserNo_admin1] [3bf00d280d1dd8db/d534673999a7d0a8] [http-nio-9009-exec-74] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"alltellers":"20250314:6045330",
				"beginbusidate":"20250314",
				"belongYear":"2025",
				"codeNo":"0",
				"codename":"凭证",
				"endbusidate":"20250314",
				"id":"9a9a376037cc4123b79e3670d7583da2",
				"keepYear":"15",
				"packageNo":"0202500023D1500001",
				"packageState":"FM_STATE_PACK_1",
				"placefile":"凭证",
				"registerDate":"20250314",
				"siteno":"00023",
				"todayNum":1,
				"totalCount":1,
				"userName":"系统超级管理员",
				"userNo":"admin",
				"yearNum":1
			}
		],
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 09:10:16.021 [OrganNo_00023_UserNo_admin1] [ca036e20d5103f3e/8280e2f1d6070e77] [http-nio-9009-exec-75] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 09:10:16.025 [OrganNo_00023_UserNo_admin1] [ca036e20d5103f3e/cc29be9b2ee7373e] [http-nio-9009-exec-75] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 09:10:16.025 [OrganNo_00023_UserNo_admin1] [ca036e20d5103f3e/cc29be9b2ee7373e] [http-nio-9009-exec-75] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 09:10:16.056 [OrganNo_00023_UserNo_admin1] [ca036e20d5103f3e/cc29be9b2ee7373e] [http-nio-9009-exec-75] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 09:10:16.057 [OrganNo_00023_UserNo_admin1] [ca036e20d5103f3e/cc29be9b2ee7373e] [http-nio-9009-exec-75] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 09:10:16.057 [OrganNo_00023_UserNo_admin1] [ca036e20d5103f3e/cc29be9b2ee7373e] [http-nio-9009-exec-75] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 09:10:16.088 [OrganNo_00023_UserNo_admin1] [ca036e20d5103f3e/cc29be9b2ee7373e] [http-nio-9009-exec-75] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 09:10:16.163 [OrganNo_00023_UserNo_admin1] [ca036e20d5103f3e/8280e2f1d6070e77] [http-nio-9009-exec-75] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 09:10:16.223 [OrganNo_00023_UserNo_admin1] [68831c1cd55cbace/680ef5a5f4efb044] [http-nio-9009-exec-76] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-05-13 09:10:16.232 [OrganNo_00023_UserNo_admin1] [68831c1cd55cbace/f5b3d32beadb6371] [http-nio-9009-exec-76] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-05-13 09:10:16.233 [OrganNo_00023_UserNo_admin1] [68831c1cd55cbace/f5b3d32beadb6371] [http-nio-9009-exec-76] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin1(String)
2025-05-13 09:10:16.263 [OrganNo_00023_UserNo_admin1] [68831c1cd55cbace/f5b3d32beadb6371] [http-nio-9009-exec-76] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-05-13 09:10:16.263 [OrganNo_00023_UserNo_admin1] [68831c1cd55cbace/f5b3d32beadb6371] [http-nio-9009-exec-76] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-05-13 09:10:16.264 [OrganNo_00023_UserNo_admin1] [68831c1cd55cbace/f5b3d32beadb6371] [http-nio-9009-exec-76] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin1(String), 15(Integer)
2025-05-13 09:10:16.294 [OrganNo_00023_UserNo_admin1] [68831c1cd55cbace/f5b3d32beadb6371] [http-nio-9009-exec-76] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 2
2025-05-13 09:10:16.361 [OrganNo_00023_UserNo_admin1] [68831c1cd55cbace/680ef5a5f4efb044] [http-nio-9009-exec-76] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025042400023001",
				"baleState":"1",
				"id":"5289ce87003d45fc8202a33e783ad8f2",
				"sendDate":"20250424",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-05-13 09:10:17.862 [OrganNo_00023_UserNo_admin1] [dd3609e8c312dc40/d5970d558479e58c] [http-nio-9009-exec-77] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042100023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:10:17.864 [OrganNo_00023_UserNo_admin1] [dd3609e8c312dc40/f459b43e50b8eced] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:10:17.864 [OrganNo_00023_UserNo_admin1] [dd3609e8c312dc40/f459b43e50b8eced] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042100023001(String)
2025-05-13 09:10:17.895 [OrganNo_00023_UserNo_admin1] [dd3609e8c312dc40/f459b43e50b8eced] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:10:17.927 [OrganNo_00023_UserNo_admin1] [dd3609e8c312dc40/d5970d558479e58c] [http-nio-9009-exec-77] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
2025-05-13 09:10:27.692 [OrganNo_00023_UserNo_admin1] [4d85e8ea84cbdf9a/975f4afed56a652f] [http-nio-9009-exec-79] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042400023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:10:27.695 [OrganNo_00023_UserNo_admin1] [4d85e8ea84cbdf9a/00b9257bd5b95921] [http-nio-9009-exec-79] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:10:27.696 [OrganNo_00023_UserNo_admin1] [4d85e8ea84cbdf9a/00b9257bd5b95921] [http-nio-9009-exec-79] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042400023001(String)
2025-05-13 09:10:27.726 [OrganNo_00023_UserNo_admin1] [4d85e8ea84cbdf9a/00b9257bd5b95921] [http-nio-9009-exec-79] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:10:27.758 [OrganNo_00023_UserNo_admin1] [4d85e8ea84cbdf9a/975f4afed56a652f] [http-nio-9009-exec-79] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
2025-05-13 09:11:06.308 [OrganNo_00023_UserNo_admin1] [8c4aa22f58987ed7/a0a82b7030f59d45] [http-nio-9009-exec-80] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042400023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:11:06.310 [OrganNo_00023_UserNo_admin1] [8c4aa22f58987ed7/698f8432bceab0c4] [http-nio-9009-exec-80] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:11:06.310 [OrganNo_00023_UserNo_admin1] [8c4aa22f58987ed7/698f8432bceab0c4] [http-nio-9009-exec-80] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042400023001(String)
2025-05-13 09:11:06.342 [OrganNo_00023_UserNo_admin1] [8c4aa22f58987ed7/698f8432bceab0c4] [http-nio-9009-exec-80] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:11:06.372 [OrganNo_00023_UserNo_admin1] [8c4aa22f58987ed7/a0a82b7030f59d45] [http-nio-9009-exec-80] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
2025-05-13 09:11:25.153 [OrganNo_00023_UserNo_admin1] [0d64afb80d5109d1/722fa161a00aad4b] [http-nio-9009-exec-83] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 09:11:25.185 [OrganNo_00023_UserNo_admin1] [0d64afb80d5109d1/85551bb5633c3621] [http-nio-9009-exec-83] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 09:11:25.185 [OrganNo_00023_UserNo_admin1] [0d64afb80d5109d1/85551bb5633c3621] [http-nio-9009-exec-83] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 09:11:25.216 [OrganNo_00023_UserNo_admin1] [0d64afb80d5109d1/85551bb5633c3621] [http-nio-9009-exec-83] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 09:11:25.216 [OrganNo_00023_UserNo_admin1] [0d64afb80d5109d1/85551bb5633c3621] [http-nio-9009-exec-83] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 09:11:25.216 [OrganNo_00023_UserNo_admin1] [0d64afb80d5109d1/85551bb5633c3621] [http-nio-9009-exec-83] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 09:11:25.247 [OrganNo_00023_UserNo_admin1] [0d64afb80d5109d1/85551bb5633c3621] [http-nio-9009-exec-83] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 09:11:25.316 [OrganNo_00023_UserNo_admin1] [0d64afb80d5109d1/722fa161a00aad4b] [http-nio-9009-exec-83] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 09:11:25.377 [OrganNo_00023_UserNo_admin1] [eefcd44a7678908d/a7c7f397635822e2] [http-nio-9009-exec-84] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 09:11:25.392 [OrganNo_00023_UserNo_admin1] [eefcd44a7678908d/92f7db4df302ce3e] [http-nio-9009-exec-84] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 09:11:25.393 [OrganNo_00023_UserNo_admin1] [eefcd44a7678908d/92f7db4df302ce3e] [http-nio-9009-exec-84] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 09:11:25.426 [OrganNo_00023_UserNo_admin1] [eefcd44a7678908d/92f7db4df302ce3e] [http-nio-9009-exec-84] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 09:11:25.426 [OrganNo_00023_UserNo_admin1] [eefcd44a7678908d/92f7db4df302ce3e] [http-nio-9009-exec-84] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 09:11:25.427 [OrganNo_00023_UserNo_admin1] [eefcd44a7678908d/92f7db4df302ce3e] [http-nio-9009-exec-84] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 09:11:25.459 [OrganNo_00023_UserNo_admin1] [eefcd44a7678908d/92f7db4df302ce3e] [http-nio-9009-exec-84] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 09:11:25.533 [OrganNo_00023_UserNo_admin1] [eefcd44a7678908d/a7c7f397635822e2] [http-nio-9009-exec-84] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 09:11:26.170 [OrganNo_00023_UserNo_admin1] [23436fbf3e54aa71/c3d77f53aad53f55] [http-nio-9009-exec-85] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 09:11:26.176 [OrganNo_00023_UserNo_admin1] [23436fbf3e54aa71/5db09883b5bf8a84] [http-nio-9009-exec-85] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 09:11:26.176 [OrganNo_00023_UserNo_admin1] [23436fbf3e54aa71/5db09883b5bf8a84] [http-nio-9009-exec-85] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 09:11:26.206 [OrganNo_00023_UserNo_admin1] [23436fbf3e54aa71/5db09883b5bf8a84] [http-nio-9009-exec-85] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 09:11:26.207 [OrganNo_00023_UserNo_admin1] [23436fbf3e54aa71/5db09883b5bf8a84] [http-nio-9009-exec-85] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 09:11:26.207 [OrganNo_00023_UserNo_admin1] [23436fbf3e54aa71/5db09883b5bf8a84] [http-nio-9009-exec-85] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 09:11:26.238 [OrganNo_00023_UserNo_admin1] [23436fbf3e54aa71/5db09883b5bf8a84] [http-nio-9009-exec-85] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 09:11:26.313 [OrganNo_00023_UserNo_admin1] [23436fbf3e54aa71/c3d77f53aad53f55] [http-nio-9009-exec-85] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 09:11:26.376 [OrganNo_00023_UserNo_admin1] [ff0d966ed00855f5/68d190a6e9a89aa5] [http-nio-9009-exec-86] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-05-13 09:11:26.392 [OrganNo_00023_UserNo_admin1] [ff0d966ed00855f5/8af22de702c80a25] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE = ?
2025-05-13 09:11:26.393 [OrganNo_00023_UserNo_admin1] [ff0d966ed00855f5/8af22de702c80a25] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-05-13 09:11:26.426 [OrganNo_00023_UserNo_admin1] [ff0d966ed00855f5/8af22de702c80a25] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 09:11:26.427 [OrganNo_00023_UserNo_admin1] [ff0d966ed00855f5/8af22de702c80a25] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 09:11:26.428 [OrganNo_00023_UserNo_admin1] [ff0d966ed00855f5/8af22de702c80a25] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 23(String), 15(Integer)
2025-05-13 09:11:26.459 [OrganNo_00023_UserNo_admin1] [ff0d966ed00855f5/8af22de702c80a25] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 1
2025-05-13 09:11:26.529 [OrganNo_00023_UserNo_admin1] [ff0d966ed00855f5/68d190a6e9a89aa5] [http-nio-9009-exec-86] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 09:11:26.591 [OrganNo_00023_UserNo_admin1] [b4614e70f53698ee/fee8057b501fad09] [http-nio-9009-exec-87] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-05-13 09:11:26.599 [OrganNo_00023_UserNo_admin1] [b4614e70f53698ee/b8be344a98ae7092] [http-nio-9009-exec-87] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-05-13 09:11:26.600 [OrganNo_00023_UserNo_admin1] [b4614e70f53698ee/b8be344a98ae7092] [http-nio-9009-exec-87] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin1(String)
2025-05-13 09:11:26.631 [OrganNo_00023_UserNo_admin1] [b4614e70f53698ee/b8be344a98ae7092] [http-nio-9009-exec-87] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-05-13 09:11:26.632 [OrganNo_00023_UserNo_admin1] [b4614e70f53698ee/b8be344a98ae7092] [http-nio-9009-exec-87] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-05-13 09:11:26.633 [OrganNo_00023_UserNo_admin1] [b4614e70f53698ee/b8be344a98ae7092] [http-nio-9009-exec-87] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin1(String), 15(Integer)
2025-05-13 09:11:26.666 [OrganNo_00023_UserNo_admin1] [b4614e70f53698ee/b8be344a98ae7092] [http-nio-9009-exec-87] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 2
2025-05-13 09:11:26.737 [OrganNo_00023_UserNo_admin1] [b4614e70f53698ee/fee8057b501fad09] [http-nio-9009-exec-87] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025042400023001",
				"baleState":"1",
				"id":"5289ce87003d45fc8202a33e783ad8f2",
				"sendDate":"20250424",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-05-13 09:11:29.365 [OrganNo_00023_UserNo_admin1] [2e24db1582023a89/074954d9b894c53d] [http-nio-9009-exec-88] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042400023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:11:29.367 [OrganNo_00023_UserNo_admin1] [2e24db1582023a89/3d49775f15720ef1] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:11:29.368 [OrganNo_00023_UserNo_admin1] [2e24db1582023a89/3d49775f15720ef1] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042400023001(String)
2025-05-13 09:11:29.399 [OrganNo_00023_UserNo_admin1] [2e24db1582023a89/3d49775f15720ef1] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:11:29.430 [OrganNo_00023_UserNo_admin1] [2e24db1582023a89/074954d9b894c53d] [http-nio-9009-exec-88] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
2025-05-13 09:11:32.080 [OrganNo_00023_UserNo_admin1] [f3bde2a1a4920482/957e8b1b6c34dc10] [http-nio-9009-exec-89] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042400023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:11:32.083 [OrganNo_00023_UserNo_admin1] [f3bde2a1a4920482/7c274ed4916ab418] [http-nio-9009-exec-89] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:11:32.083 [OrganNo_00023_UserNo_admin1] [f3bde2a1a4920482/7c274ed4916ab418] [http-nio-9009-exec-89] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042400023001(String)
2025-05-13 09:11:32.114 [OrganNo_00023_UserNo_admin1] [f3bde2a1a4920482/7c274ed4916ab418] [http-nio-9009-exec-89] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:11:32.145 [OrganNo_00023_UserNo_admin1] [f3bde2a1a4920482/957e8b1b6c34dc10] [http-nio-9009-exec-89] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
2025-05-13 09:17:43.803 [OrganNo_00023_UserNo_admin1] [f4389614f0dc79c4/e65e7561051515cb] [http-nio-9009-exec-91] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042400023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:17:43.835 [OrganNo_00023_UserNo_admin1] [f4389614f0dc79c4/232cb4a8f3fd4386] [http-nio-9009-exec-91] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:17:43.836 [OrganNo_00023_UserNo_admin1] [f4389614f0dc79c4/232cb4a8f3fd4386] [http-nio-9009-exec-91] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042400023001(String)
2025-05-13 09:17:43.868 [OrganNo_00023_UserNo_admin1] [f4389614f0dc79c4/232cb4a8f3fd4386] [http-nio-9009-exec-91] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:17:43.900 [OrganNo_00023_UserNo_admin1] [f4389614f0dc79c4/e65e7561051515cb] [http-nio-9009-exec-91] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
2025-05-13 09:17:46.915 [OrganNo_00023_UserNo_admin1] [b19fb7cb19cac4f0/09040a3f3fbdf7c6] [http-nio-9009-exec-92] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042400023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:17:46.918 [OrganNo_00023_UserNo_admin1] [b19fb7cb19cac4f0/3efc7e3eef7f4996] [http-nio-9009-exec-92] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:17:46.918 [OrganNo_00023_UserNo_admin1] [b19fb7cb19cac4f0/3efc7e3eef7f4996] [http-nio-9009-exec-92] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042400023001(String)
2025-05-13 09:17:46.949 [OrganNo_00023_UserNo_admin1] [b19fb7cb19cac4f0/3efc7e3eef7f4996] [http-nio-9009-exec-92] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:17:46.981 [OrganNo_00023_UserNo_admin1] [b19fb7cb19cac4f0/09040a3f3fbdf7c6] [http-nio-9009-exec-92] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
2025-05-13 09:17:56.096 [OrganNo_00023_UserNo_admin1] [1ddf11eaa8cb974e/e7992f1d151cee3b] [http-nio-9009-exec-94] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042400023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:17:56.097 [OrganNo_00023_UserNo_admin1] [1ddf11eaa8cb974e/ec7d37b48ca19330] [http-nio-9009-exec-94] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:17:56.098 [OrganNo_00023_UserNo_admin1] [1ddf11eaa8cb974e/ec7d37b48ca19330] [http-nio-9009-exec-94] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042400023001(String)
2025-05-13 09:17:56.128 [OrganNo_00023_UserNo_admin1] [1ddf11eaa8cb974e/ec7d37b48ca19330] [http-nio-9009-exec-94] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:18:07.007 [OrganNo_00023_UserNo_admin1] [1ddf11eaa8cb974e/e7992f1d151cee3b] [http-nio-9009-exec-94] ERROR GlobalException - 未知异常！原因是:
org.springframework.web.util.NestedServletException: Handler dispatch failed; nested exception is java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1087)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:209)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.ApplicationDispatcher.invoke(ApplicationDispatcher.java:661)
	at org.apache.catalina.core.ApplicationDispatcher.processRequest(ApplicationDispatcher.java:425)
	at org.apache.catalina.core.ApplicationDispatcher.doForward(ApplicationDispatcher.java:357)
	at org.apache.catalina.core.ApplicationDispatcher.forward(ApplicationDispatcher.java:294)
	at com.sunyard.aos.common.filter.AuthFilter.doFilter(AuthFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CSRFValidationFilter.doFilter(CSRFValidationFilter.java:74)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.CharFilter.doFilter(CharFilter.java:24)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at com.sunyard.aos.common.filter.HeaderFilter.doFilter(HeaderFilter.java:31)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.cloud.sleuth.instrument.web.servlet.TracingFilter.doFilter(TracingFilter.java:68)
	at org.springframework.cloud.sleuth.autoconfig.instrument.web.TraceWebServletConfiguration$LazyTracingFilter.doFilter(TraceWebServletConfiguration.java:131)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:178)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:153)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:481)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.springframework.cloud.sleuth.instrument.web.tomcat.TraceValve.invoke(TraceValve.java:90)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:342)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:390)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:928)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1794)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.lang.NoClassDefFoundError: com/sunyard/ars/fm/FmFunction
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl.printCodeHouseFrame(FmApplicationServiceImpl.java:689)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$FastClassBySpringCGLIB$$b78a013b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.service.impl.FmApplicationServiceImpl$$EnhancerBySpringCGLIB$$ce9fd038.printCodeHouseFrame(<generated>)
	at com.sunyard.ars.fm.controller.ApplicationController.printCodeHouseFrame(ApplicationController.java:134)
	at com.sunyard.ars.fm.controller.ApplicationController$$FastClassBySpringCGLIB$$b45ab200.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at com.sunyard.ars.fm.controller.ApplicationController$$EnhancerBySpringCGLIB$$cc353d6.printCodeHouseFrame(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	... 77 common frames omitted
2025-05-13 09:33:31.531 [OrganNo_00023_UserNo_admin1] [8638ba50b6f8588d/4c13400e2ac91957] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025042400023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-05-13 09:33:31.620 [OrganNo_00023_UserNo_admin1] [8638ba50b6f8588d/a05c7c4dadc753d7] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-05-13 09:33:31.621 [OrganNo_00023_UserNo_admin1] [8638ba50b6f8588d/a05c7c4dadc753d7] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025042400023001(String)
2025-05-13 09:33:31.656 [OrganNo_00023_UserNo_admin1] [8638ba50b6f8588d/a05c7c4dadc753d7] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-05-13 09:35:28.943 [OrganNo_00023_UserNo_admin1] [8638ba50b6f8588d/4c13400e2ac91957] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"fileName":"2025042400023001.png",
		"imgPath":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/2025042400023001/2025042400023001.png",
		"returnList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		]
	},
	"retMsg":"生成成功"
}
2025-05-13 10:22:51.239 [OrganNo_00023_UserNo_admin1] [0a2624724f01654f/b2533d593c4c90b6] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 10:22:51.840 [OrganNo_00023_UserNo_admin1] [0a2624724f01654f/bc2929b4e29e4aae] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 10:22:51.841 [OrganNo_00023_UserNo_admin1] [0a2624724f01654f/bc2929b4e29e4aae] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 10:22:51.872 [OrganNo_00023_UserNo_admin1] [0a2624724f01654f/bc2929b4e29e4aae] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 10:22:51.873 [OrganNo_00023_UserNo_admin1] [0a2624724f01654f/bc2929b4e29e4aae] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 10:22:51.873 [OrganNo_00023_UserNo_admin1] [0a2624724f01654f/bc2929b4e29e4aae] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 10:22:51.904 [OrganNo_00023_UserNo_admin1] [0a2624724f01654f/bc2929b4e29e4aae] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 10:22:51.982 [OrganNo_00023_UserNo_admin1] [0a2624724f01654f/b2533d593c4c90b6] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 10:22:52.060 [OrganNo_00023_UserNo_admin1] [45efa3d3e16a4ca5/99d9293c72c415c0] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 10:22:52.109 [OrganNo_00023_UserNo_admin1] [45efa3d3e16a4ca5/5ae0bd76a1a7c070] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 10:22:52.110 [OrganNo_00023_UserNo_admin1] [45efa3d3e16a4ca5/5ae0bd76a1a7c070] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 10:22:52.140 [OrganNo_00023_UserNo_admin1] [45efa3d3e16a4ca5/5ae0bd76a1a7c070] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 10:22:52.141 [OrganNo_00023_UserNo_admin1] [45efa3d3e16a4ca5/5ae0bd76a1a7c070] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 10:22:52.144 [OrganNo_00023_UserNo_admin1] [45efa3d3e16a4ca5/5ae0bd76a1a7c070] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 10:22:52.176 [OrganNo_00023_UserNo_admin1] [45efa3d3e16a4ca5/5ae0bd76a1a7c070] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 10:22:52.245 [OrganNo_00023_UserNo_admin1] [45efa3d3e16a4ca5/99d9293c72c415c0] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 11:06:56.995 [OrganNo_00023_UserNo_admin1] [5416d586c15180dd/c4fcb3bb567d9a0a] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 11:06:57.696 [OrganNo_00023_UserNo_admin1] [5416d586c15180dd/333aaefba7ee887d] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 11:06:57.697 [OrganNo_00023_UserNo_admin1] [5416d586c15180dd/333aaefba7ee887d] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 11:06:57.735 [OrganNo_00023_UserNo_admin1] [5416d586c15180dd/333aaefba7ee887d] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 11:06:57.736 [OrganNo_00023_UserNo_admin1] [5416d586c15180dd/333aaefba7ee887d] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 11:06:57.737 [OrganNo_00023_UserNo_admin1] [5416d586c15180dd/333aaefba7ee887d] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 11:06:57.773 [OrganNo_00023_UserNo_admin1] [5416d586c15180dd/333aaefba7ee887d] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 11:06:57.856 [OrganNo_00023_UserNo_admin1] [5416d586c15180dd/c4fcb3bb567d9a0a] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 11:06:57.995 [OrganNo_00023_UserNo_admin1] [464e54f2222feb49/ac54578544006dfa] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 11:06:58.016 [OrganNo_00023_UserNo_admin1] [464e54f2222feb49/fdd8ce46d29145a1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 11:06:58.017 [OrganNo_00023_UserNo_admin1] [464e54f2222feb49/fdd8ce46d29145a1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 11:06:58.051 [OrganNo_00023_UserNo_admin1] [464e54f2222feb49/fdd8ce46d29145a1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 11:06:58.051 [OrganNo_00023_UserNo_admin1] [464e54f2222feb49/fdd8ce46d29145a1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 11:06:58.052 [OrganNo_00023_UserNo_admin1] [464e54f2222feb49/fdd8ce46d29145a1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 11:06:58.087 [OrganNo_00023_UserNo_admin1] [464e54f2222feb49/fdd8ce46d29145a1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 11:06:58.173 [OrganNo_00023_UserNo_admin1] [464e54f2222feb49/ac54578544006dfa] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 15:04:01.673 [OrganNo_00023_UserNo_admin1] [d5e33acbc42a5970/ad6caf9e903d4db2] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 15:04:02.800 [OrganNo_00023_UserNo_admin1] [d5e33acbc42a5970/fa064453d04293fb] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 15:04:02.802 [OrganNo_00023_UserNo_admin1] [d5e33acbc42a5970/fa064453d04293fb] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 15:04:02.859 [OrganNo_00023_UserNo_admin1] [d5e33acbc42a5970/fa064453d04293fb] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 15:04:02.860 [OrganNo_00023_UserNo_admin1] [d5e33acbc42a5970/fa064453d04293fb] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 15:04:02.860 [OrganNo_00023_UserNo_admin1] [d5e33acbc42a5970/fa064453d04293fb] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 15:04:02.914 [OrganNo_00023_UserNo_admin1] [d5e33acbc42a5970/fa064453d04293fb] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 15:04:03.039 [OrganNo_00023_UserNo_admin1] [d5e33acbc42a5970/ad6caf9e903d4db2] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 15:04:03.149 [OrganNo_00023_UserNo_admin1] [a2c465a46542ecb9/cc2c38dbc2f7d56f] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-05-13 15:04:03.169 [OrganNo_00023_UserNo_admin1] [a2c465a46542ecb9/6863374517aad9eb] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-05-13 15:04:03.170 [OrganNo_00023_UserNo_admin1] [a2c465a46542ecb9/6863374517aad9eb] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin1(String)
2025-05-13 15:04:03.225 [OrganNo_00023_UserNo_admin1] [a2c465a46542ecb9/6863374517aad9eb] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-05-13 15:04:03.226 [OrganNo_00023_UserNo_admin1] [a2c465a46542ecb9/6863374517aad9eb] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-05-13 15:04:03.226 [OrganNo_00023_UserNo_admin1] [a2c465a46542ecb9/6863374517aad9eb] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin1(String), 15(Integer)
2025-05-13 15:04:03.282 [OrganNo_00023_UserNo_admin1] [a2c465a46542ecb9/6863374517aad9eb] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 2
2025-05-13 15:04:03.412 [OrganNo_00023_UserNo_admin1] [a2c465a46542ecb9/cc2c38dbc2f7d56f] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025042400023001",
				"baleState":"1",
				"id":"5289ce87003d45fc8202a33e783ad8f2",
				"sendDate":"20250424",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-05-13 15:13:50.681 [OrganNo_00023_UserNo_admin1] [566873fd050c6490/b9566e47936874bc] [http-nio-9009-exec-12] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 15:13:50.782 [OrganNo_00023_UserNo_admin1] [566873fd050c6490/570162901cd05890] [http-nio-9009-exec-12] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 15:13:50.783 [OrganNo_00023_UserNo_admin1] [566873fd050c6490/570162901cd05890] [http-nio-9009-exec-12] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 15:13:50.832 [OrganNo_00023_UserNo_admin1] [566873fd050c6490/570162901cd05890] [http-nio-9009-exec-12] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 15:13:50.834 [OrganNo_00023_UserNo_admin1] [566873fd050c6490/570162901cd05890] [http-nio-9009-exec-12] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 15:13:50.834 [OrganNo_00023_UserNo_admin1] [566873fd050c6490/570162901cd05890] [http-nio-9009-exec-12] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 15:13:50.881 [OrganNo_00023_UserNo_admin1] [566873fd050c6490/570162901cd05890] [http-nio-9009-exec-12] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 15:13:50.980 [OrganNo_00023_UserNo_admin1] [566873fd050c6490/b9566e47936874bc] [http-nio-9009-exec-12] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 15:13:51.057 [OrganNo_00023_UserNo_admin1] [565107799ed9aee0/cb184d3e8ddf861d] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_4"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1",
		"isCenter":"1"
	}
}
2025-05-13 15:13:51.075 [OrganNo_00023_UserNo_admin1] [565107799ed9aee0/941968bfe996ed4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE = ?
2025-05-13 15:13:51.076 [OrganNo_00023_UserNo_admin1] [565107799ed9aee0/941968bfe996ed4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 14(String)
2025-05-13 15:13:51.120 [OrganNo_00023_UserNo_admin1] [565107799ed9aee0/941968bfe996ed4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 15:13:51.121 [OrganNo_00023_UserNo_admin1] [565107799ed9aee0/941968bfe996ed4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 15:13:51.122 [OrganNo_00023_UserNo_admin1] [565107799ed9aee0/941968bfe996ed4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 14(String), 15(Integer)
2025-05-13 15:13:51.166 [OrganNo_00023_UserNo_admin1] [565107799ed9aee0/941968bfe996ed4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 15:13:51.274 [OrganNo_00023_UserNo_admin1] [565107799ed9aee0/cb184d3e8ddf861d] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_4",
				"baleNo":"2025041800023001",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314183802129311",
				"applicationState":"FM_STATE_APP_4",
				"baleNo":"2025031400023002",
				"businessDate":"20250314",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400314",
				"endBusiDate":"20250314",
				"id":"8972b5df4f9a4606b5ce7ea1cf318625",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"19"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250429234215189911",
				"applicationState":"FM_STATE_APP_4",
				"baleNo":"2025042900023001",
				"batchId":"20250429234215189911",
				"businessDate":"20250429",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400429",
				"endBusiDate":"20250429",
				"id":"70b6a8d10b14417687c9c72257d7781e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250429",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"管理员",
				"tellerNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 15:13:51.351 [OrganNo_00023_UserNo_admin1] [599e402ec1689f17/708671c3fc5c5c0f] [http-nio-9009-exec-14] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"packageState":"FM_STATE_PACK_1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-05-13 15:13:51.374 [OrganNo_00023_UserNo_admin1] [599e402ec1689f17/16347f7f0a92fc25] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_PACKAGE WHERE PACKAGE_STATE = ? AND SITENO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-05-13 15:13:51.375 [OrganNo_00023_UserNo_admin1] [599e402ec1689f17/16347f7f0a92fc25] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - ==> Parameters: 1(String), admin1(String)
2025-05-13 15:13:51.420 [OrganNo_00023_UserNo_admin1] [599e402ec1689f17/16347f7f0a92fc25] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - <==      Total: 1
2025-05-13 15:13:51.420 [OrganNo_00023_UserNo_admin1] [599e402ec1689f17/16347f7f0a92fc25] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.P.selectPackagesByConfig - ==>  Preparing: select PACKAGE_NO "PACKAGE_NO", ID "ID", PACKAGE_STATE "PACKAGE_STATE", USER_NO "USER_NO", USER_NAME "USER_NAME", REGISTER_DATE "REGISTER_DATE", ORGAN_NO "ORGAN_NO", ORGAN_NAME "ORGAN_NAME",BELONG_YEAR "BELONG_YEAR", CODE_NO "CODE_NO", KEEP_YEAR "KEEP_YEAR", PACK_TYPE "PACK_TYPE", TODAY_NUM "TODAY_NUM", YEAR_NUM "YEAR_NUM", TRUNK_NO "TRUNK_NO", FRAMESEQ "FRAMESEQ",TOTAL_COUNT "TOTAL_COUNT", DESTROYDATE "DESTROYDATE", BEGINBUSIDATE "BEGINBUSIDATE", ENDBUSIDATE "ENDBUSIDATE", SITENO "SITENO", SITENAME "SITENAME", CODENAME "CODENAME", PLACEFILE "PLACEFILE", ALLTELLERS "ALLTELLERS", AREANO "AREANO", WAREHOUSENO "WAREHOUSENO", VOUCHER_COUNT "VOUCHER_COUNT" from FM_PACKAGE WHERE PACKAGE_STATE = ? and SITENO in ( select ORGAN_NO from SM_USER_ORGAN_TB WHERE USER_NO = ? ) order by SITENO,BEGINBUSIDATE,TODAY_NUM DESC LIMIT ?
2025-05-13 15:13:51.421 [OrganNo_00023_UserNo_admin1] [599e402ec1689f17/16347f7f0a92fc25] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.P.selectPackagesByConfig - ==> Parameters: 1(String), admin1(String), 15(Integer)
2025-05-13 15:13:51.466 [OrganNo_00023_UserNo_admin1] [599e402ec1689f17/16347f7f0a92fc25] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.P.selectPackagesByConfig - <==      Total: 1
2025-05-13 15:13:51.566 [OrganNo_00023_UserNo_admin1] [599e402ec1689f17/708671c3fc5c5c0f] [http-nio-9009-exec-14] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"alltellers":"20250314:6045330",
				"beginbusidate":"20250314",
				"belongYear":"2025",
				"codeNo":"0",
				"codename":"凭证",
				"endbusidate":"20250314",
				"id":"9a9a376037cc4123b79e3670d7583da2",
				"keepYear":"15",
				"packageNo":"0202500023D1500001",
				"packageState":"FM_STATE_PACK_1",
				"placefile":"凭证",
				"registerDate":"20250314",
				"siteno":"00023",
				"todayNum":1,
				"totalCount":1,
				"userName":"系统超级管理员",
				"userNo":"admin",
				"yearNum":1
			}
		],
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 15:13:52.860 [OrganNo_00023_UserNo_admin1] [edc5a154ef01d8b1/8f19d5ca163eecec] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 15:13:52.866 [OrganNo_00023_UserNo_admin1] [edc5a154ef01d8b1/7e1304eb137f447b] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 15:13:52.866 [OrganNo_00023_UserNo_admin1] [edc5a154ef01d8b1/7e1304eb137f447b] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 15:13:52.911 [OrganNo_00023_UserNo_admin1] [edc5a154ef01d8b1/7e1304eb137f447b] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 15:13:52.911 [OrganNo_00023_UserNo_admin1] [edc5a154ef01d8b1/7e1304eb137f447b] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 15:13:52.912 [OrganNo_00023_UserNo_admin1] [edc5a154ef01d8b1/7e1304eb137f447b] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 15:13:52.956 [OrganNo_00023_UserNo_admin1] [edc5a154ef01d8b1/7e1304eb137f447b] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 15:13:53.060 [OrganNo_00023_UserNo_admin1] [edc5a154ef01d8b1/8f19d5ca163eecec] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 15:13:53.137 [OrganNo_00023_UserNo_admin1] [e1f922710e21ef04/64d48ffaa311d9e8] [http-nio-9009-exec-16] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-05-13 15:13:53.146 [OrganNo_00023_UserNo_admin1] [e1f922710e21ef04/c835ef229e80f828] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-05-13 15:13:53.147 [OrganNo_00023_UserNo_admin1] [e1f922710e21ef04/c835ef229e80f828] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin1(String)
2025-05-13 15:13:53.193 [OrganNo_00023_UserNo_admin1] [e1f922710e21ef04/c835ef229e80f828] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-05-13 15:13:53.194 [OrganNo_00023_UserNo_admin1] [e1f922710e21ef04/c835ef229e80f828] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-05-13 15:13:53.194 [OrganNo_00023_UserNo_admin1] [e1f922710e21ef04/c835ef229e80f828] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin1(String), 15(Integer)
2025-05-13 15:13:53.239 [OrganNo_00023_UserNo_admin1] [e1f922710e21ef04/c835ef229e80f828] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 2
2025-05-13 15:13:53.338 [OrganNo_00023_UserNo_admin1] [e1f922710e21ef04/64d48ffaa311d9e8] [http-nio-9009-exec-16] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025042400023001",
				"baleState":"1",
				"id":"5289ce87003d45fc8202a33e783ad8f2",
				"sendDate":"20250424",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-05-13 15:13:57.261 [OrganNo_00023_UserNo_admin1] [7cc2dbef0c762423/4a09cd5b285854e7] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 15:13:57.267 [OrganNo_00023_UserNo_admin1] [7cc2dbef0c762423/228da855e1141dd9] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 15:13:57.268 [OrganNo_00023_UserNo_admin1] [7cc2dbef0c762423/228da855e1141dd9] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 15:13:57.314 [OrganNo_00023_UserNo_admin1] [7cc2dbef0c762423/228da855e1141dd9] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 15:13:57.315 [OrganNo_00023_UserNo_admin1] [7cc2dbef0c762423/228da855e1141dd9] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 15:13:57.315 [OrganNo_00023_UserNo_admin1] [7cc2dbef0c762423/228da855e1141dd9] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 15:13:57.361 [OrganNo_00023_UserNo_admin1] [7cc2dbef0c762423/228da855e1141dd9] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 15:13:57.465 [OrganNo_00023_UserNo_admin1] [7cc2dbef0c762423/4a09cd5b285854e7] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 15:13:57.540 [OrganNo_00023_UserNo_admin1] [75302d3213c3cd4e/dc5a5db57bd221e2] [http-nio-9009-exec-19] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 15:13:57.556 [OrganNo_00023_UserNo_admin1] [75302d3213c3cd4e/f557354ebd184c2c] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 15:13:57.557 [OrganNo_00023_UserNo_admin1] [75302d3213c3cd4e/f557354ebd184c2c] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 15:13:57.602 [OrganNo_00023_UserNo_admin1] [75302d3213c3cd4e/f557354ebd184c2c] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 15:13:57.603 [OrganNo_00023_UserNo_admin1] [75302d3213c3cd4e/f557354ebd184c2c] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 15:13:57.604 [OrganNo_00023_UserNo_admin1] [75302d3213c3cd4e/f557354ebd184c2c] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 15:13:57.652 [OrganNo_00023_UserNo_admin1] [75302d3213c3cd4e/f557354ebd184c2c] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 15:13:57.762 [OrganNo_00023_UserNo_admin1] [75302d3213c3cd4e/dc5a5db57bd221e2] [http-nio-9009-exec-19] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 15:19:28.097 [OrganNo_00023_UserNo_admin1] [653cab5500c7e24a/33fed5ac8dfdd0cf] [http-nio-9009-exec-21] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 15:19:28.192 [OrganNo_00023_UserNo_admin1] [653cab5500c7e24a/93b16a2816b7b514] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 15:19:28.192 [OrganNo_00023_UserNo_admin1] [653cab5500c7e24a/93b16a2816b7b514] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 15:19:28.236 [OrganNo_00023_UserNo_admin1] [653cab5500c7e24a/93b16a2816b7b514] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 15:19:28.237 [OrganNo_00023_UserNo_admin1] [653cab5500c7e24a/93b16a2816b7b514] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 15:19:28.237 [OrganNo_00023_UserNo_admin1] [653cab5500c7e24a/93b16a2816b7b514] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 15:19:28.282 [OrganNo_00023_UserNo_admin1] [653cab5500c7e24a/93b16a2816b7b514] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 15:19:28.387 [OrganNo_00023_UserNo_admin1] [653cab5500c7e24a/33fed5ac8dfdd0cf] [http-nio-9009-exec-21] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 15:19:28.466 [OrganNo_00023_UserNo_admin1] [f9829db490964864/f7483c1e76b33636] [http-nio-9009-exec-22] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 15:19:28.484 [OrganNo_00023_UserNo_admin1] [f9829db490964864/c6ac639f8183d385] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 15:19:28.485 [OrganNo_00023_UserNo_admin1] [f9829db490964864/c6ac639f8183d385] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 15:19:28.533 [OrganNo_00023_UserNo_admin1] [f9829db490964864/c6ac639f8183d385] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 15:19:28.533 [OrganNo_00023_UserNo_admin1] [f9829db490964864/c6ac639f8183d385] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 15:19:28.534 [OrganNo_00023_UserNo_admin1] [f9829db490964864/c6ac639f8183d385] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 15:19:28.588 [OrganNo_00023_UserNo_admin1] [f9829db490964864/c6ac639f8183d385] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 15:19:28.702 [OrganNo_00023_UserNo_admin1] [f9829db490964864/f7483c1e76b33636] [http-nio-9009-exec-22] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-05-13 16:28:37.822 [OrganNo_00023_UserNo_admin1] [3ff82c83cfe4056b/6b118c84b95c8557] [http-nio-9009-exec-24] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-05-13 16:28:38.646 [OrganNo_00023_UserNo_admin1] [3ff82c83cfe4056b/d1f908cbd03313d4] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-05-13 16:28:38.647 [OrganNo_00023_UserNo_admin1] [3ff82c83cfe4056b/d1f908cbd03313d4] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-05-13 16:28:38.689 [OrganNo_00023_UserNo_admin1] [3ff82c83cfe4056b/d1f908cbd03313d4] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-05-13 16:28:38.690 [OrganNo_00023_UserNo_admin1] [3ff82c83cfe4056b/d1f908cbd03313d4] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-05-13 16:28:38.690 [OrganNo_00023_UserNo_admin1] [3ff82c83cfe4056b/d1f908cbd03313d4] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-05-13 16:28:38.731 [OrganNo_00023_UserNo_admin1] [3ff82c83cfe4056b/d1f908cbd03313d4] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-05-13 16:28:38.826 [OrganNo_00023_UserNo_admin1] [3ff82c83cfe4056b/6b118c84b95c8557] [http-nio-9009-exec-24] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-05-13 16:28:38.904 [OrganNo_00023_UserNo_admin1] [c7429a92f08b7122/37470888b89a8083] [http-nio-9009-exec-25] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-05-13 16:28:38.923 [OrganNo_00023_UserNo_admin1] [c7429a92f08b7122/dd065ec6e0e44f39] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-05-13 16:28:38.924 [OrganNo_00023_UserNo_admin1] [c7429a92f08b7122/dd065ec6e0e44f39] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-05-13 16:28:38.967 [OrganNo_00023_UserNo_admin1] [c7429a92f08b7122/dd065ec6e0e44f39] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-05-13 16:28:38.968 [OrganNo_00023_UserNo_admin1] [c7429a92f08b7122/dd065ec6e0e44f39] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-05-13 16:28:38.969 [OrganNo_00023_UserNo_admin1] [c7429a92f08b7122/dd065ec6e0e44f39] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-05-13 16:28:39.011 [OrganNo_00023_UserNo_admin1] [c7429a92f08b7122/dd065ec6e0e44f39] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 3
2025-05-13 16:28:39.111 [OrganNo_00023_UserNo_admin1] [c7429a92f08b7122/37470888b89a8083] [http-nio-9009-exec-25] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
