package com.sunyard.client;

import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientHeightQuery;
import com.sunyard.ecm.server.bean.MigrateBatchBean;
import com.sunyard.es.util.EsBean;

/**
 * 客户端api调用接口
 * 
 * <AUTHOR>
 *
 */
public interface SunEcmClientApi {
	
	/**
	 * 向统一接入问询内容存储服务器的地址
	 * @param userName 用户名
	 * @param groupName 内容存储服务器组名
	 * @return 返回信息
	 * @throws Exception
	 */
	public String inquireDMByGroup(String userName, String groupName) throws Exception;

	/**
	 * 登录接口
	 * @param userName 用户名
	 * @param password 密码
	 * @return 返回信息
	 * @throws Exception
	 */
	public String login(String userName, String password) throws Exception;
	
	/**
	 * 登出接口
	 * @param userName 用户名
	 * @return 返回信息
	 * @throws Exception
	 */
	public String logout(String userName) throws Exception;
	
	/**
	 * 上传接口
	 * @param clientBatchBean 上传的批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回contentID，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String upload(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	
	/**
	 * 更新接口
	 * @param clientBatchBean 需要更新的批次信息对象
	 * @param dmsName 服务器组名称
	 * @param isAutoCheck 是否自动检入检出
	 * @return 成功则返回contentID，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String update(ClientBatchBean clientBatchBean, String dmsName, boolean isAutoCheck) throws Exception;
	
	/**
	 * 删除接口
	 * @param clientBatchBean 批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回成功信息，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String delete(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	
	/**
	 * 检入接口
	 * @param clientBatchBean 批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回成功信息，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String checkIn(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	
	/**
	 * 检出接口
	 * @param clientBatchBean 批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回checkToken，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String checkOut(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	
	/**
	 * 申请批次号接口
	 * @param clientBatchBean 批次信息对象
	 * @param groupName 内容存储服务器组名
	 * @return 成功则返回contentID，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String createContentID(ClientBatchBean clientBatchBean, String groupName) throws Exception;
	
	/**
	 * 查询接口
	 * @param clientBatchBean 批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回ClientBatchBean信息的xml文档，失败则返回错误代码和信息
	 * @throws Exception
	 */
	
	
	public String queryBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	
	/**
	 * 查询接口
	 * @param heightQuery 批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回ClientBatchBean信息的xml文档，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String heightQuery(ClientHeightQuery heightQuery, String dmsName) throws Exception;
	
	/**
	 * 批次操作接口
	 * @param clientBatchBean 批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回成功信息，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String operAnnotation(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	
	/**
	 * 批注查询
	 * @param clientBatchBean 批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回批注信息ClientBatchBean的xml文档，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String queryAnnotation(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	
	/**
	 * 客户端获得内容模型权限获取
	 * @param userName 用户名
	 * @param passWord 密码
	 */
	public String getPermissions_Client(String userName,String passWord) throws Exception;
	
	/**
	 * 客户端获取所有服务器信息
	 * 
	 */
	public String getContentServerInfo_Client() throws Exception;
	/**
	 * 客户端获得获取内容模型列表信息
	 */
	public String getAllModelMsg_Client() throws Exception;

	/**
	 * 客户端获取内容模型模版
	 * @param docNames 内容模型列表
	 */
	public String getModelTemplate_Client (String[] modeCodes) throws Exception;
	
	/**
	 * 客户端获取动态令牌
	 * @param ipAddress console的IP地址和端口和工程名http://127.0.0.1:8080/SunECMConsole
	 * @param tokenCheckValue 唯一标识，申请令牌时 由客户端提供
	 * @param userName 用户名
	 * @param operationCode 操作权限类型OperPermission中的数值
	 * @return
	 * @throws Exception
	 */
	public String getToken (String ipAddress, String tokenCheckValue, String userName, String operationCode) throws Exception;
	
	/**
	 * 立即迁移接口
	 * @param migrateBatchBean 批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 
	 * @throws Exception
	 */
	public String immedMigrate(MigrateBatchBean migrateBatchBean, String dmsName) throws Exception;
	/**
	 * 机构管理接口
	 * @param itemType 模型代码
	 * @param insNo 机构编号
	 * @return
	 * @throws Exception
	 */
	
	public String queryNodeInfoByGroupIdAndInsNo(String itemType,String insNo) throws Exception;
   /**
    * 获取用户令牌
    * @param username
    * @param password
    * @return
    */
	public String createUserToken(String username,String password)throws Exception;

	/**
	 * 复制对应批次号下的批次信息和文件信息到新的模型中
	 * @param clientBatchBean
	 * @param dmsName
	 * @return 返回新的批次号
	 * @throws Exception
	 */
	public String copyBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	
	/**
	 * 立即复制对应批次号下的批次信息和文件信息到新的模型中
	 * @param clientBatchBean
	 * @param dmsName
	 * @return 返回新的批次号
	 * @throws Exception
	 */
	public String immedCopyBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	/**
	 * 第三方接口
	 * @param clientBatchBean
	 * @param 操作类型 “GETMD5”
	 * @return 返回xml
	 * @throws Exception
	 */
	public String otherRuquest(ClientBatchBean clientBatchBean,String requestName) throws Exception;
	/**
	 * 文件流上传接口
	 * @param clientBatchBean 上传的批次信息对象
	 * @param dmsName 服务器组名称
	 * @return 成功则返回contentID，失败则返回错误代码和信息
	 * @throws Exception
	 */
	public String uploadByStream(ClientBatchBean clientBatchBean, String groupName) throws Exception;
	/**
	 * 根据indexName和ECM_DOC_ID 查询es
	 * @param indexName 索引code
	 * @param ecm_doc_id 主键id
	 * @throws Exception
	 */
	public String queryESByECM_DOC_ID(String indexName,String ecm_doc_id) throws Exception ;
	/**
	 * 根据bean的其他map查询es
	 * @param json 
	 * @throws Exception
	 */
	public String queryESByBool(EsBean esBean) throws Exception ;
	public String uploadESTag(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
	public String updateESTag(ClientBatchBean clientBatchBean, String dmsName) throws Exception;
}