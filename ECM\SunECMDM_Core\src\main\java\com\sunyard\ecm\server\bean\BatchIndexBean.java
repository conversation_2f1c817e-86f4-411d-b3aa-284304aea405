package com.sunyard.ecm.server.bean;

import com.sunyard.ecm.server.bean.converter.StringCustomConverter;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamConverter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 索引信息
 * <AUTHOR>
 *
 */
@XStreamAlias("BatchIndexBean")
public class BatchIndexBean {
	/** 批次状态,0表示删除，1表示可用 **/
	@XStreamAsAttribute
	private String CONTENT_STATUS;
	/** 批次处理时间 **/
	@XStreamAsAttribute
	private String BUSINESS_TIME;
	/** 批次标识 **/
	@XStreamAsAttribute
	private String BATCH_FLAG;
	/** 所在服务器ID **/
	@XStreamAsAttribute
	private String SERVER_ID;
	/** 上传用户 **/
	@XStreamAsAttribute
	private String UPLOAD_USER;
	/** 内容ID **/
	@XStreamAsAttribute
	private String CONTENT_ID;
	/** 批次文件数 **/
	@XStreamAsAttribute
	private String AMOUNT;
	/** 批次所在服务器IP **/
	@XStreamAsAttribute
	private String SERVER_IP;
	/** 批次迁移状态 **/
	@XStreamAsAttribute
	private String MIGRATION_STATUS;
	/** 上传时间 **/
	@XStreamAsAttribute
	private String UPLOAD_TIME;
	/** 批次所在服务器socket端口号 **/
	@XStreamAsAttribute
	private String SOCKET_PORT;
	/** 批次所在服务器http端口号 **/
	@XStreamAsAttribute
	private String HTTP_PORT;
	/** 是否在线，1为在线 **/
	@XStreamAsAttribute
	private String ONLINE;
	/** 批次版本 **/
	@XStreamAsAttribute
	private String VERSION;
	/** 当前组最大批次版本 **/
	@XStreamAsAttribute
	private String MAX_VERSION;
	/** 所在服务器组ID **/
	@XStreamAsAttribute
	private String GROUP_ID;
	/** 所在服务器组name**/
	@XStreamAsAttribute
	private String GROUP_NAME;
	/** 大数据离线后存储的离线路径**/
	@XStreamAsAttribute
	private String OFFLINE_PATH;
	/** 是否最新版本**/
	@XStreamAsAttribute
	private String IS_LAST_VERSION;
	/** 自定义属性 **/
	@XStreamConverter(StringCustomConverter.class)
	private Map<String, String> customMap;
	/** 标签属性 **/
    private List<TagBean> tagBeanList;
    
	public List<TagBean> getTagBeanList() {
		return tagBeanList;
	}

	public void setTagBeanList(List<TagBean> tagBeanList) {
		this.tagBeanList = tagBeanList;
	}

	/**
	 *   添加标签
	 * 
	 * @param fangyue
	 *           
	 */
	public void addTagBeanList(TagBean tagBean) {
		if (tagBeanList == null) {
			tagBeanList = new ArrayList<TagBean>();
		}
		this.tagBeanList.add(tagBean);
	}

	public String getContentStatus() {
		return CONTENT_STATUS;
	}

	public void setContentStatus(String conentStatus) {
		this.CONTENT_STATUS = conentStatus;
	}

	public String getGroupName() {
		return GROUP_NAME;
	}

	public void setGroupName(String groupName) {
		this.GROUP_NAME = groupName;
	}

	public String getBusinessTime() {
		return BUSINESS_TIME;
	}

	public void setBusinessTime(String businessTime) {
		this.BUSINESS_TIME = businessTime;
	}

	public String getBatchFlag() {
		return BATCH_FLAG;
	}

	public void setBatchFlag(String batchFlag) {
		this.BATCH_FLAG = batchFlag;
	}

	public String getServerID() {
		return SERVER_ID;
	}

	public void setServerID(String serverID) {
		this.SERVER_ID = serverID;
	}

	public String getContentID() {
		return CONTENT_ID;
	}

	public void setContentID(String contentID) {
		this.CONTENT_ID = contentID;
	}

	public String getAmount() {
		return AMOUNT;
	}

	public void setAmount(String amount) {
		this.AMOUNT = amount;
	}

	public String getServerIp() {
		return SERVER_IP;
	}

	public void setServerIp(String serverIp) {
		this.SERVER_IP = serverIp;
	}

	public String getMigrationStatus() {
		return MIGRATION_STATUS;
	}

	public void setMigrationStatus(String migrationStatus) {
		this.MIGRATION_STATUS = migrationStatus;
	}

	public String getUploadTime() {
		return UPLOAD_TIME;
	}

	public void setUploadTime(String uploadTime) {
		this.UPLOAD_TIME = uploadTime;
	}

	public String getSocketPort() {
		return SOCKET_PORT;
	}

	public void setSocketPort(String socketPort) {
		this.SOCKET_PORT = socketPort;
	}

	public String getHttpPort() {
		return HTTP_PORT;
	}

	public void setHttpPort(String httpPort) {
		this.HTTP_PORT = httpPort;
	}

	public String getOnLine() {
		return ONLINE;
	}

	public void setOnLine(String onLine) {
		this.ONLINE = onLine;
	}

	public String getVersion() {
		return VERSION;
	}

	public void setVersion(String version) {
		this.VERSION = version;
	}

	public Map<String, String> getCustomMap() {
		return customMap;
	}

	public void setCustomMap(Map<String, String> customMap) {
		this.customMap = customMap;
	}
	
	public void addCustomMap(String key, String value) {
		if(customMap == null) {
			customMap = new HashMap<String, String>();
		}
		this.customMap.put(key, value);
	}

	public String getMaxVersion() {
		return MAX_VERSION;
	}

	public void setMaxVersion(String maxVersion) {
		this.MAX_VERSION = maxVersion;
	}

	public String getUploadUser() {
		return UPLOAD_USER;
	}

	public void setUploadUser(String uploadUser) {
		this.UPLOAD_USER = uploadUser;
	}

	public String getGroupID() {
		return GROUP_ID;
	}

	public void setGroupID(String groupID) {
		this.GROUP_ID = groupID;
	}
	public String getOFFLINE_PATH() {
		return OFFLINE_PATH;
	}

	public void setOFFLINE_PATH(String oFFLINE_PATH) {
		OFFLINE_PATH = oFFLINE_PATH;
	}

	public String getIS_LAST_VERSION() {
		return IS_LAST_VERSION;
	}

	public void setIS_LAST_VERSION(String iS_LAST_VERSION) {
		IS_LAST_VERSION = iS_LAST_VERSION;
	}
}