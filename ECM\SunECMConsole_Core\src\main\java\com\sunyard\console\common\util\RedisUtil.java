package com.sunyard.console.common.util;

import com.sunyard.redis.RedisConnPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.util.Map;

public class RedisUtil {
	private final static Logger log = LoggerFactory.getLogger(RedisUtil.class);

	public void pushHMap(String key, Map<String, String> map, int time) {
		Jedis jedis = null;
		try {
			jedis = RedisConnPool.getJedisConn();
			jedis.hset(key, map);
			jedis.expire(key, time*60);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("redis opt error ,key=" + key + ",value=" + map, e);
		} finally {
			RedisConnPool.closeJedisConn(jedis);
		}
	}

	public Map<String, String> getHashMap(String key) {
		Jedis jedis = null;
		Map<String, String> map = null;
		try {
			jedis = RedisConnPool.getJedisConn();
			map = jedis.hgetAll(key);
		} catch (Exception e) {
			log.error("redis opt error,key=" + key, e);
		} finally {
			RedisConnPool.closeJedisConn(jedis);
		}
		return map;
	}
	/**
	 * 返回redis剩余时间
	 * @param token
	 * @return
	 */
	public long  getRedisttl(String token) {
		Jedis jedis = null;
		try {
			jedis = RedisConnPool.getJedisConn();
			return jedis.ttl(token);
		} catch (Exception e) {
			log.error("redis设置超时失败", e);
			return 0;
		} finally {
			RedisConnPool.closeJedisConn(jedis);
		}
	}

	public void setRedisTimeout(String token, int redisTimeOut) {
		Jedis jedis = null;
		try {
			jedis = RedisConnPool.getJedisConn();
			jedis.expire(token, redisTimeOut*60);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("redis设置超时失败", e);
		} finally {
			RedisConnPool.closeJedisConn(jedis);
		}
	}

}
