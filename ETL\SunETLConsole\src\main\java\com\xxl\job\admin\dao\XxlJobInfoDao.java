package com.xxl.job.admin.dao;

import com.xxl.job.admin.core.model.XxlJobInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * job info
 * <AUTHOR> 2016-1-12 18:03:45
 */
public interface XxlJobInfoDao {

	public List<XxlJobInfo> pageList(@Param("offset") int offset, @Param("pagesize") int pagesize, @Param("jobGroup") int jobGroup, @Param("executorHandler") String executorHandler);
	public int pageListCount(@Param("offset") int offset, @Param("pagesize") int pagesize, @Param("jobGroup") int jobGroup, @Param("executorHandler") String executorHandler);
	
	public int save(XxlJobInfo info);

	public XxlJobInfo loadById(@Param("id") int id);
	
	public int update(XxlJobInfo item);
	
	public int delete(@Param("id") int id);

	public List<XxlJobInfo> getJobsByGroup(@Param("jobGroup") int jobGroup);

	public int findAllCount();

	public Integer getMaxId();

	public List<XxlJobInfo> getAllJobInfos();

	List<XxlJobInfo> ParentJobList(@Param("childrenJobId") int childrenJobId);
	//获取所有的日终任务
    List<XxlJobInfo> getReactiveJobInfos(@Param("jobType") String jobType);
	//更新任务的状态值
    int updateJobSatrt(@Param("id")int id, @Param("startCode")String startcode);
	//更新任务的状态值
    int updateJobSatrtING(@Param("id")int id, @Param("startCode")String startcode);
	//重置所有日终任务
	void updateAllJobSatrt();
	//重置所有失败任务
	void updateAllErrorJobSatrt();
}
