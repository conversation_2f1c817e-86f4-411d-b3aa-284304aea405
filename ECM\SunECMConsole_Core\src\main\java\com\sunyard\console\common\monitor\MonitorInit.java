package com.sunyard.console.common.monitor;

import com.sunyard.console.common.StopableRunnable;
import com.sunyard.console.common.config.LoadConfigFile;
import com.sunyard.console.monitor.monitorCenter.thread.DMEquInfoThread;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * Title: 监控管理初始化
 * </p>
 * <p>
 * Description: 初始化监控管理线程
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class MonitorInit implements ServletContextListener {
	 final static 	Logger log = LoggerFactory.getLogger(MonitorInit.class);
	StopableRunnable stopableRunnable ;

	public void contextInitialized(ServletContextEvent event) {
		
		int monitorTime=LoadConfigFile.getConfigBean().getMonitorTime();
		stopableRunnable= new StopableRunnable(
				new DMEquInfoThread(), monitorTime, TimeUnit.SECONDS);
		log.debug("=======开始初始化监控======频率:"+monitorTime+"秒");
		Thread t = new Thread(stopableRunnable);
		t.start();
		log.debug("=======监控初始化完成======");

	}

	public void contextDestroyed(ServletContextEvent event) {
		stopableRunnable.stop();
		log.info("Monitor stoped");
	}
}
