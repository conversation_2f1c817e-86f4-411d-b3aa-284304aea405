package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

import java.util.List;

@XStreamAlias("LazySingletonBean")
public class LazySingletonBean {
	@XStreamAsAttribute
	private String SERVER_IP;
	@XStreamAsAttribute
	private String SERVER_ID;
	@XStreamAsAttribute
	private String SERVER_NAME;
	@XStreamAsAttribute
	private String SERVER_STATUS; 
	@XStreamAsAttribute
	private String GROUP_NAME;
	@XStreamAsAttribute
	private String GROUP_ID;
	@XStreamAsAttribute
	private String GROUP_STATE;
	@XStreamAsAttribute
	private String GROUP_OS;
	@XStreamAsAttribute
	private String DEPLOY_MODE;
	@XStreamAsAttribute
	private String HTTP_PORT;
	@XStreamAsAttribute
	private String SOCKET_PORT;
	
	private List<LifeCycleStrategyBean> lifeCycle;// 策略

	public String getSERVER_IP() {
		return SERVER_IP;
	}

	public void setSERVER_IP(String sERVER_IP) {
		SERVER_IP = sERVER_IP;
	}

	public String getGROUP_NAME() {
		return GROUP_NAME;
	}

	public void setGROUP_NAME(String gROUP_NAME) {
		GROUP_NAME = gROUP_NAME;
	}

	public String getGROUP_ID() {
		return GROUP_ID;
	}

	public void setGROUP_ID(String gROUP_ID) {
		GROUP_ID = gROUP_ID;
	}

	public String getGROUP_STATE() {
		return GROUP_STATE;
	}

	public void setGROUP_STATE(String gROUP_STATE) {
		GROUP_STATE = gROUP_STATE;
	}

	public String getGROUP_OS() {
		return GROUP_OS;
	}

	public void setGROUP_OS(String gROUP_OS) {
		GROUP_OS = gROUP_OS;
	}

	public String getDEPLOY_MODE() {
		return DEPLOY_MODE;
	}

	public void setDEPLOY_MODE(String dEPLOY_MODE) {
		DEPLOY_MODE = dEPLOY_MODE;
	}

	public List<LifeCycleStrategyBean> getLifeCycle() {
		return lifeCycle;
	}

	public void setLifeCycle(List<LifeCycleStrategyBean> lifeCycle) {
		this.lifeCycle = lifeCycle;
	}

	public String getSERVER_ID() {
		return SERVER_ID;
	}

	public void setSERVER_ID(String sERVER_ID) {
		SERVER_ID = sERVER_ID;
	}
	
	public String getSERVER_NAME() {
		return SERVER_NAME;
	}

	public void setSERVER_NAME(String sERVER_NAME) {
		SERVER_NAME = sERVER_NAME;
	}

	public String getSERVER_STATUS() {
		return SERVER_STATUS;
	}

	public void setSERVER_STATUS(String sERVER_STATUS) {
		SERVER_STATUS = sERVER_STATUS;
	}

	public String getHTTP_PORT() {
		return HTTP_PORT;
	}

	public void setHTTP_PORT(String hTTP_PORT) {
		HTTP_PORT = hTTP_PORT;
	}

	public String getSOCKET_PORT() {
		return SOCKET_PORT;
	}

	public void setSOCKET_PORT(String sOCKET_PORT) {
		SOCKET_PORT = sOCKET_PORT;
	}

	@Override
	public String toString() {
		return "LazySingletonBean [SERVER_IP=" + SERVER_IP + ", SERVER_ID=" + SERVER_ID + ", SERVER_NAME=" + SERVER_NAME
				+ ", SERVER_STATUS=" + SERVER_STATUS + ", GROUP_NAME=" + GROUP_NAME + ", GROUP_ID=" + GROUP_ID
				+ ", GROUP_STATE=" + GROUP_STATE + ", GROUP_OS=" + GROUP_OS + ", DEPLOY_MODE=" + DEPLOY_MODE
				+ ", HTTP_PORT=" + HTTP_PORT + ", SOCKET_PORT=" + SOCKET_PORT + ", lifeCycle=" + lifeCycle + "]";
	}
	

	
}