import request from '@/utils/request'
import * as CryptoJS from 'crypto-js/crypto-js';
import EndUrl from './publicUrl.js'
//与后端约定的key
var key = '4c43c3654c43c3654c43c3654c43c365';

var sm4key = '0123456789abcdeffedcba9876543210';
const iv = '0123456789abcdeffedcba9876543210';


export function sm4_login_CBC(msg) {
  try {
    const { SM4 } = require('gm-crypto')
    const encryptedData = SM4.encrypt(msg, sm4key, {
      iv,
      mode: SM4.constants.CBC,
      inputEncoding: 'utf8',
      outputEncoding: 'hex'
    })

    console.log(`Encrypted: ${encryptedData}`);
    return encryptedData;
  } catch (error) {
    console.error('Encryption failed:', error);
    return null;
  }
}

export function login(data) {
  return request({
    url: '/safeManage/loginAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'login_id': encryptByDES(data.username), 'password':encryptByDES(data.password)}
  })
}

export function getInfo(token) {
  return request({
    url: '/safeManage/ssoUserLoginAction'+EndUrl.EndUrl,
    method: 'post',
    params: {token: token }
  })
}

export function logout(token) {

  return request({
    url: '/safeManage/loginOutAction'+EndUrl.EndUrl,
    method: 'post',
    params: { token:token }

  })
}
//加密
export function encryptByDES(message) {
  var deskey = CryptoJS.enc.Utf8.parse(key);
    var encrypted =  CryptoJS.TripleDES.encrypt(message,deskey, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });
      return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
}

