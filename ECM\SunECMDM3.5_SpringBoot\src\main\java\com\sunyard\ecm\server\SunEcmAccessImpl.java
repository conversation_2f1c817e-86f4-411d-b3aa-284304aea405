package com.sunyard.ecm.server;

import java.io.File;
import java.io.InputStream;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.jws.WebService;
import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.ws.WebServiceContext;
import javax.xml.ws.handler.MessageContext;

import com.alibaba.dubbo.config.annotation.Reference;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import com.sunyard.ecm.server.bean.BatchBean;
import com.sunyard.ecm.server.bean.BatchIndexBean;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.bean.LazySingletonBean;
import com.sunyard.ecm.server.bean.LifeCycleStrategyBean;
import com.sunyard.ecm.server.bean.LogInfoBean;
import com.sunyard.ecm.server.bean.MigrateFailDetailBean;
import com.sunyard.ecm.server.bean.MigrateStatusCode;
import com.sunyard.ecm.server.bean.ModelTableBean;
import com.sunyard.ecm.server.bean.ModelTemplateBean;
import com.sunyard.ecm.server.bean.NodeInfo;
import com.sunyard.ecm.server.bean.OfflineCountBean;
import com.sunyard.ecm.server.bean.SQLBean;
import com.sunyard.ecm.server.cache.LazySingleton;
import com.sunyard.ecm.server.cache.LoadLifeCycleStrategy;
import com.sunyard.ecm.server.cache.wsserver.ServiceInfoOption;
import com.sunyard.ecm.server.daoinf.IOfflineDao;
import com.sunyard.ecm.server.dm.batchdown.bean.BatchDownBean;
import com.sunyard.ecm.server.dm.batchdown.bean.BatchDownQueue;
import com.sunyard.ecm.server.dm.ws.WSReceiveFile;
import com.sunyard.ecm.server.service.IMessageServer;
import com.sunyard.ecm.server.util.ModelUtil;
import com.sunyard.ecm.server.util.VolumnUtils;
import com.sunyard.es.EsClient;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.initialization.LoadConfigFile;
import com.sunyard.initialization.bean.ConfigBean;
import com.sunyard.monitor.agent.DMReportMsg;
import com.sunyard.monitor.agent.DMTaskMsg;
import com.sunyard.scheduler.dao2.IndexCompressDao;
import com.sunyard.util.DateUtil;
import com.sunyard.util.FileUtil;
import com.sunyard.util.MapUtil;
import com.sunyard.es.util.EsMappingBean;
import com.sunyard.es.util.Result;
import com.sunyard.util.SpringUtil;
import com.sunyard.util.StringUtil;
import com.sunyard.util.TransOptionKey;
import com.sunyard.util.URLAssembleUtil;
import com.sunyard.util.URLEnCode;
import com.sunyard.ws.internalapi.SunEcmAccess;
import com.sunyard.ws.utils.XMLUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

/**
 * <p>
 * Title: webService接口实现
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 *
 * <AUTHOR>
 * @version 3.1
 */
@WebService
public class SunEcmAccessImpl implements SunEcmAccess {
	final static Logger log = LoggerFactory.getLogger(SunEcmAccessImpl.class);

	private WebServiceContext context;
	private IMessageServer server;
	private IOfflineDao offlinedao = (IOfflineDao) SpringUtil.getSpringBean("offlineDao"); // 数据库操作类
	private IndexCompressDao indexCompressDao = (IndexCompressDao) SpringUtil.getSpringBean("indexCompressDao"); // 数据库操作类
	private SunECMAccessUtil accessUtil = new SunECMAccessUtil();

	public void setServer(IMessageServer server) {
		this.server = server;
	}

	/**
	 * 接受内容模型模版
	 */
	public String setModelTemplate(String xml) {
		// 调用信息改变监听
		ServiceInfoOption.changeModelDoc(xml);
		return "success";
	}

	/**
	 * 内容模型列表
	 */
	public String setAllModelMsg(String xml) {
		ServiceInfoOption.changeAllModelMsg(xml);
		return "success";
	}

	/**
	 * 生命周期
	 */
	public String setLifeCycleStrategy(String xml) {
		ServiceInfoOption.changeScheduler(xml);
		return "success";
	}

	/**
	 * 内容模型表
	 */
	public String setModelTable(String xml) {
		ServiceInfoOption.changeMetaTable(xml);
		return "success";
	}

	/**
	 * 存储对象
	 */
	public String setStoreObject(String xml) {
		ServiceInfoOption.changeStoreObject(xml);
		return "success";
	}

	/**
	 * 改变内容服务器组和内容模型的对应集合
	 */
	public String setSgroupmodleSet(String xml) {
		ServiceInfoOption.changeSgroupmodleSet(xml);
		return "success";
	}

	/**
	 * 获取服务器同步信息和任务信息
	 *
	 * @return xml 信息
	 */
	public String getSchedulerAndSyn() {
		return DMTaskMsg.getDMTaskMsg();
	}

	public String getSchedulerAndSyn(String xml) {
		// 生命周期策略变更接口未使用,GJJ单独另写一方法
		return null;
	}

	public String logListByServerId(String xml) {
		try {
			//1、创建一个DocumentBuilderFactory的对象
			DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
			//创建DocumentBuilder对象
			DocumentBuilder db = dbf.newDocumentBuilder();
			log.info("查找日志路径");
			ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
			InputStream in = classLoader.getResourceAsStream("logback-spring.xml");
			Document doc = db.parse(in);
			//获取xml中Configuration节点
			String value = doc.getElementsByTagName("appender").item(1).getChildNodes().item(1).getTextContent();
			String log_home = doc.getElementsByTagName("property").item(0).getAttributes().item(1).getNodeValue().toString();
			String log_name = doc.getElementsByTagName("property").item(1).getAttributes().item(1).getNodeValue().toString();
			String[] path = value.split("/");
			if (value.startsWith("$")) {
				value = log_home + "/" + path[1] + "/" + log_name + ".log";
			}
			NodeInfo nodeInfo = LazySingleton.getInstance().load.getNodeInfoBean();
			File logFile = new File(value);
			log.info("logFile: " + logFile.getAbsolutePath());
			List<LogInfoBean> logInfoBeans = new ArrayList<LogInfoBean>();
			if (logFile.exists()) {
				String pathStr = logFile.getParent();
				File filePath = new File(pathStr);
				if (filePath.isDirectory()) {
					File[] subfiles = filePath.listFiles();
					for (File subfile : subfiles) {
						if (!subfile.isDirectory()) {
							LogInfoBean logInfoBean = new LogInfoBean();
							logInfoBean.setLog_path(filePath.getPath());
							logInfoBean.setLog_name(subfile.getName());
							logInfoBean.setServer_id(nodeInfo.getServer_id());
							logInfoBean.setServer_name(nodeInfo.getServer_name());

							// 拼装URL
							String end = subfile.getName().substring(subfile.getName().lastIndexOf(".")+1);
							Map<String, String> urlMap = new HashMap<String, String>(); // 创建url的map对象
							urlMap.put("fileType",end); // 文件后缀
							urlMap.put("fileID", subfile.getName()); // 文件ID即文件名
							String localDM = LoadConfigFile.getInstance().getConfigBean().getLocalName();
							String url = "http://" + nodeInfo.getServer_ip() + ":" + nodeInfo.getHttp_port() + "/"
									+ localDM + "/servlet/getFile?";
							// 文件路径为根路径+存储路径+randomPath(年/月/日/无+随机路径)
							urlMap.put("filePath", subfile.getParent() + File.separator);
							// 验证失效时间
							urlMap.put("effTime", DateUtil.get12bitDateStr());
							// 生成参数
							String paramsStr = URLAssembleUtil.assembleURL(urlMap);
							// 加密参数，并添加到logInfoBean中
							try {
								logInfoBean.setUrl(url + URLEnCode.ebotongEncrypto(paramsStr));
							} catch (Exception e) {
								log.error("", e);
							}

							logInfoBeans.add(logInfoBean);
						}
					}
				}
				return XMLUtil.list2Xml(logInfoBeans);
			} else {
				return "找不到日志文件" + logFile.getAbsolutePath();
			}
		} catch (Exception e) {
			return e.toString();
		}
	}

	public String logRuleInfoSend(String xml) {
		log.info("内容存储服务器收到的日志记录XML:" + xml);
		// 未使用,日志策略改变使用setLogRule
		return null;
	}

	public String setContentServerInfo(String xml) {
		ServiceInfoOption.changeNodeInfo(xml);
		return "success";
	}

	public String userLogin(String userName, String pwd) {
		String result = "";
		try {
			result = server.login(userName, pwd);
		} catch (SunECMException e) {
			log.error("出错", e);
		}
		return result;
	}

	public String volumeInfoSend(String xml) {
		return null;
	}

	/**
	 * 接受控制台下发的服务器组使用规则
	 */
	public String setContentGroupServerStatus(String xml) {
		ServiceInfoOption.setDMStat(xml);
		return "success";
	}

	/**
	 * 接受控制台下发的服务器使用规则
	 */
	public String setContentServerStatus(String xml) {
		ServiceInfoOption.setDMStat(xml);
		return "success";
	}

	/**
	 * 接受控制台下发的日志策略
	 */
	public String setLogRule(String xml) {
//		ServiceInfoOption.changeLogRule(xml);
		return "success";
	}

	/**
	 * 监控获取统计信息
	 */
	public String getReportMsg() {
		return DMReportMsg.getReportMsg();
	}

	/**
	 * 设置生命周期策略的启停
	 */
	public String setLifeCycleStrategyOnOrOff(String xml) {

		return "success";
	}

	public String receiveFileFromClient(String fileXml) {
		Map<String, String> map = MapUtil.parseMap(fileXml, null);
		setRemoteIp(map);
		WSReceiveFile.reveiveFile(map);
		return "NULL";
	}

	public String receiveMsgFromClient(String msgXml) {
		log.info("webservice接收到的报文信息:" + msgXml);
		Map<String, String> map = MapUtil.parseMap(msgXml, null);
		setRemoteIp(map);
		return WSDispatcher.handleRequest(map);
	}

	private void setRemoteIp(Map<String, String> map) {
		MessageContext ctx = context.getMessageContext();
		HttpServletRequest request = (HttpServletRequest) ctx.get(AbstractHTTPDestination.HTTP_REQUEST);
		String ip = request.getRemoteAddr();
		map.put("sunecm_system_remote_ip", ip);
	}

	public String getModelTable(String modelcode) {
		log.info("请求模型[" + modelcode + "]的表");
		ModelTableBean tableBean = LazySingleton.getInstance().ModelTable.getModelTable().get(modelcode);
		return XMLUtil.addHeadRootNode(XMLUtil.bean2XML(tableBean));
	}

	public String addDownloadTask(String xml) {
		BatchDownBean downBean = XMLUtil.xml2Bean(xml, BatchDownBean.class);
		// 添入下拉任务队列中
		BatchDownQueue.getInstance().addBatch(downBean);
		// 返回成功信息
		return TransOptionKey.SUCCESS;
	}

	public void abandonOfflineByContentId(String modelCode, String xml) throws SunECMException {
		if (xml == null) {
			log.warn("xml is null");
			return;
		}
		List<OfflineCountBean> beanList = XMLUtil.xml2list(xml, OfflineCountBean.class);
		if (beanList == null || beanList.size() == 0) {
			log.warn("bean is null[" + xml + "]");
			return;
		}
		// 放弃某些批次离线
		log.info("根据批次号放弃离线" + modelCode + ",xml:" + xml + "]");
		ModelTemplateBean modelTemplate = (ModelTemplateBean) LazySingleton.getInstance().ModelDoc.getModelDoc(modelCode);
		if (modelTemplate == null) {
			String[] objectNames = {modelCode};
			LazySingleton.getInstance().ModelDoc.setModelDoc(objectNames);
			modelTemplate = (ModelTemplateBean) LazySingleton.getInstance().ModelDoc.getModelDoc(modelCode);
		}
		if (modelTemplate == null) {
			throw new SunECMException(SunECMExceptionStatus.CONSOLE_CONFIG_FAIL, "createUpdateSQL： can not find modelTemplate");
		}
		String createDateColumn = modelTemplate.getCreation_date_column().toUpperCase();
		String contentID = "";
		for (OfflineCountBean bean : beanList) {
			contentID = bean.getContentId();
			log.info("begin to abandon contentid=" + bean.getContentId() + "]");
			BatchIndexBean indexObject = new BatchIndexBean();
			indexObject.addCustomMap(createDateColumn, bean.getStartDate());
			indexObject.setContentID(contentID);
			BatchBean batchBean = new BatchBean();
			batchBean.setModelCode(modelCode);
			batchBean.setIndex_Object(indexObject);
			// 修改状态为放弃离线
			offlinedao.updateBatchStatus(batchBean, MigrateStatusCode.ABANDONOFFLINECLEAR, null);
			log.info("abandon contentid=" + contentID + "] success");
		}
	}

	public void abandonOfflineByDate(String modelCode, String beginDates) throws SunECMException {
		log.info("放弃指定时间内离线失败的批次,ModelCode=" + modelCode + ",dates[" + beginDates + "]");
		if (beginDates == null) {
			log.info("abandonDate is null");
			return;
		}
		String[] date = beginDates.split(",");
		int size = date.length;
		if (size == 0) {
			return;
		}
		String dateFieldName = ModelUtil.getStartDateColumnByModelCode(modelCode);
		for (int i = 0; i < size; i++) {
			log.info("begin to adandon date:" + date[i] + "]");
			offlinedao.abandonOfflineByDate(modelCode, dateFieldName, date[i]);
		}
	}

	/**
	 * @param modelCode
	 * @param beginDate
	 * @param endDate
	 * @return List<Map < String, Object>>  格式的String
	 */
	public String getOfflineCount(String modelCode, String beginDate, String endDate, boolean queryAbandon) {
		log.info("查询离线失败的批次，beginDate" + beginDate + ",endDate=" + endDate + ",modelCode，" + modelCode + ",queryAbandon" + queryAbandon);
		List<OfflineCountBean> list = new ArrayList<OfflineCountBean>();
		List<String> dateList = new ArrayList<String>();
		try {
			dateList = DateUtil.getBetweenDayList(beginDate, endDate, true);
		} catch (ParseException e) {
			log.error("根据开始时间和结束时间  查询时间段出错,begindate=" + beginDate + ",enddate=" + endDate + "]", e);
			return null;
		}
		if (dateList == null) {
			log.warn("datelist is null");
			return XMLUtil.list2Xml(list);
		}
		try {
			String startDateColumn = ModelUtil.getStartDateColumnByModelCode(modelCode);
			if (queryAbandon) {
				// 需要查询放弃离线的
				list = getOfflineCountNeedAbandon(dateList, modelCode, startDateColumn);
			} else {
				list = getOfflineCountNoAbandon(dateList, modelCode, startDateColumn);
			}
		} catch (Exception e) {
			log.error("统计离线失败和成功批次失败", e);
		}

		return XMLUtil.list2Xml(list);
	}

	/**
	 * 首先查询indexcompress表，存在当天离线状态为000（正在离线），
	 * 则查询离线失败（在业务表中查询）的，如果离线失败存在，则查询当天放弃离线、离线失败、离线成功的批次数目 * @param dateList
	 *
	 * @param modelCode
	 * @param startDateColumn
	 * @throws SunECMException
	 */
	public List<OfflineCountBean> getOfflineCountNoAbandon(List<String> dateList, String modelCode, String startDateColumn) throws SunECMException {
		List<OfflineCountBean> list = new ArrayList<OfflineCountBean>();
		OfflineCountBean countBean = new OfflineCountBean();
		String groupId = LazySingleton.getInstance().load.getNodeInfoBean().getGroup_id();

		for (String date : dateList) {
			log.info("begin to getFailCount,date=" + date + "]");
			String successCount = "";
			String failCount;
			String abandonCount;
			String state = "";
			// 查询放弃离线
			Map<String, String> map = indexCompressDao.getModelDay(modelCode, date);
			if (map != null) {
				log.debug("查询离线状态:" + map.toString());
				state = map.get("LIFECYCLESTATE");
				successCount = map.get("BATCHCOUNT").toString();
			}
			if (!"000".equals(state)) {
				continue;
			}
			failCount = offlinedao.getOfflineCountByTpye(modelCode, startDateColumn, date, MigrateStatusCode.FAILOFFLINE);
			if (!StringUtil.stringIsNull(failCount)) {
				if ("0".equals(failCount)) {
					log.info("beginDate=" + date + "not has failCount");
					continue;
				} else {
					log.info("beginDate=" + date + ",failCount=" + failCount + "]");
					// 存在离线失败的批次，所以准备查询离线成功数
					abandonCount = offlinedao.getOfflineCountByTpye(modelCode, startDateColumn, date, MigrateStatusCode.ABANDONOFFLINECLEAR);
					int failCountInt = 0;
					int successCountInt = 0;
					int abandonCountInt = 0;
					int totalCountInt = 0;
					try {
						failCountInt = Integer.parseInt(failCount);
					} catch (Exception e) {
						log.error("转换failCount出错" + failCount + "]", e);
						failCountInt = 0;
					}
					try {
						successCountInt = Integer.parseInt(successCount);
					} catch (Exception e) {
						log.error("转换successCount出错" + successCount + "]", e);
						successCountInt = 0;
					}
					try {
						abandonCountInt = Integer.parseInt(abandonCount);
					} catch (Exception e) {
						log.error("转换abandonCountInt出错" + abandonCount + "]", e);
						abandonCountInt = 0;
					}
					log.info("beginDate=" + date + ",failCount=" + failCount + ",successCount=" + successCount + ",abandonCount=" + abandonCount);
					totalCountInt = failCountInt + successCountInt + abandonCountInt;

					countBean = new OfflineCountBean();
					countBean.setModelCode(modelCode);
					countBean.setGroupId(groupId);
					countBean.setStartDate(date);
					countBean.setOfflineFailCount(failCount);
					countBean.setOfflineSuccessCount(successCount);
					countBean.setOfflineabandonCount(abandonCount);
					countBean.setOfflineTotalCount(Integer.toString(totalCountInt));
					list.add(countBean);
				}
			}
		}
		return list;
	}

	/**
	 * 需要查询放弃离线的，首先查询indexcompress表，存在当天离线状态为000（正在离线），
	 * 则查询离线失败（在业务表中查询）的和放弃离线的批次，如果离线失败或者放弃离线存在其一，则查询当天放弃离线、离线失败、离线成功的批次数目
	 *
	 * @param modelCode
	 * @param startDateColumn
	 * @throws SunECMException
	 */
	public List<OfflineCountBean> getOfflineCountNeedAbandon(List<String> dateList, String modelCode, String startDateColumn) throws SunECMException {
		List<OfflineCountBean> list = new ArrayList<OfflineCountBean>();
		OfflineCountBean countBean = new OfflineCountBean();
		String groupId = LazySingleton.getInstance().load.getNodeInfoBean().getGroup_id();

		for (String date : dateList) {
			log.info("begin to getFailCount,date=" + date + "]");
			String failCount;
			String successCount = "";
			String abandonCount;
			String state = "";
			// 查询放弃离线
			Map<String, String> map = indexCompressDao.getModelDay(modelCode, date);
			if (map != null) {
				log.debug("查询离线状态:" + map.toString());
				state = map.get("LIFECYCLESTATE");
				successCount = map.get("BATCHCOUNT").toString();
			}

			log.info(date + " indexpress=" + state);
			if (!"000".equals(state)) {
				continue;
			}
			// 存在正在离线，说明可能有离线失败的
			failCount = offlinedao.getOfflineCountByTpye(modelCode, startDateColumn, date, MigrateStatusCode.FAILOFFLINE);
			abandonCount = offlinedao.getOfflineCountByTpye(modelCode, startDateColumn, date, MigrateStatusCode.ABANDONOFFLINECLEAR);

			if (StringUtil.stringIsNull(failCount) && StringUtil.stringIsNull(abandonCount)) {
				// 如果失败数和放弃数都为空,则不查询
				continue;
			}
			if ("0".equals(failCount) && "0".equals(abandonCount)) {
				// 如果失败数和放弃数都为0，则不查询
				continue;
			}

			int failCountInt = 0;
			int successCountInt = 0;
			int abandonCountInt = 0;
			int totalCountInt = 0;
			try {
				failCountInt = Integer.parseInt(failCount);
			} catch (Exception e) {
				log.error("转换failCount出错" + failCount + "]", e);
				failCountInt = 0;
			}
			try {
				successCountInt = Integer.parseInt(successCount);
			} catch (Exception e) {
				log.error("转换successCount出错" + successCount + "]", e);
				successCountInt = 0;
			}
			try {
				abandonCountInt = Integer.parseInt(abandonCount);
			} catch (Exception e) {
				log.error("转换abandonCountInt出错" + abandonCount + "]", e);
				abandonCountInt = 0;
			}
			log.info("beginDate=" + date + ",failCount=" + failCount + ",successCount=" + successCount + ",abandonCount=" + abandonCount);
			totalCountInt = failCountInt + successCountInt + abandonCountInt;

			countBean = new OfflineCountBean();
			countBean.setModelCode(modelCode);
			countBean.setGroupId(groupId);
			countBean.setStartDate(date);
			countBean.setOfflineFailCount(failCount);
			countBean.setOfflineSuccessCount(successCount);
			countBean.setOfflineabandonCount(abandonCount);
			countBean.setOfflineTotalCount(Integer.toString(totalCountInt));
			list.add(countBean);
		}
		return list;

	}

	/**
	 * List<List<String>> 格式的String
	 */
	public String getFailOfflineList(String modelCode, String beginDate, String endDate, String type, int start, int limit) {
		log.info("beginDate" + beginDate + ",endDate=" + endDate + ",modelCode，" + modelCode + "]");
		List<OfflineCountBean> list = new ArrayList<OfflineCountBean>();
		List<String> dateList = new ArrayList<String>();
		try {
			if (endDate == null || endDate.equals("null") || endDate.equals("null")) {
				dateList.add(beginDate);
			} else {
				log.info("endDate[" + endDate + "]");
				dateList = DateUtil.getBetweenDayList(beginDate, endDate, true);
			}

		} catch (ParseException e) {
			log.error("根据开始时间和结束时间  查询时间段出错,begindate=" + beginDate + ",enddate=" + endDate + "]", e);
			return null;
		}
		if (dateList == null) {
			log.warn("datelist is null");
			return null;
		}
		try {
			list = getOfflineContentByDate(modelCode, dateList, type, start, limit);
		} catch (Exception e) {
			log.error("获取离线批次失败", e);
			return null;
		}

		return XMLUtil.list2Xml(list);
	}

	/**
	 * 根据类型查询离线批次
	 *
	 * @param modelCode
	 * @param dateList
	 * @param type
	 * @throws SunECMException
	 */
	public List<OfflineCountBean> getOfflineContentByDate(String modelCode, List<String> dateList, String type, int start, int limit) throws SunECMException {
		List<OfflineCountBean> list = new ArrayList<OfflineCountBean>();
		OfflineCountBean offlineCountBean;
		for (String date : dateList) {
			log.info("begin to get date" + date + "]");
			ModelTemplateBean template = (ModelTemplateBean) LazySingleton.getInstance().ModelDoc.getModelDoc(modelCode);
			String dateFieldName = template.getCreation_date_column().toUpperCase();
			// 查询批次
			List<Map<String, String>> contentlist = offlinedao.getOfflineFailContentByDate(modelCode, dateFieldName, date, type, start, limit);
			if (contentlist == null) {
				log.info("contentList is null" + date);
				continue;
			}
			String totalCount = offlinedao.getOfflineCountByTpye(modelCode, dateFieldName, date, type);
			log.debug("date=" + date + ",批次数量是:" + totalCount + "]");

			for (Map<String, String> map : contentlist) {
				offlineCountBean = new OfflineCountBean();
				offlineCountBean.setContentId(map.get("CONTENT_ID"));
				offlineCountBean.setMigrationStatus(map.get("MIGRATION_STATUS"));
				offlineCountBean.setOfflineTotalCount(totalCount);
				list.add(offlineCountBean);
			}
		}
		return list;
	}

	public void clearWebserviceErrorDBData(String xml) throws SunECMException {
		log.info("clear webservice begin" + xml);
		if (xml == null || xml.equals("") || xml.equals("null") || xml.equals("NULL")) {
			return;
		}
		List<MigrateFailDetailBean> list = XMLUtil.xml2list(xml, MigrateFailDetailBean.class);
		if (list == null || list.size() == 0) {
			log.warn("list is null");
			return;
		}
		for (MigrateFailDetailBean bean : list) {
			getFileMsg(bean.getFile_table_name(), bean.getContent_id(), bean.getGroup_id(), bean.getVersion(), bean.getIndex_model_code());
		}
		log.info("clear webservice over");
	}

	public void getFileMsg(String tableName, String contentId, String groupId, String version, String indexModelCode) {
		// 从批次表tablename中查询业务开始时间，定位文件分表
		log.info("tableName=" + tableName + ",contentid=" + contentId + ",groupId=" + groupId + ",version=" + version);
		try {
			List<Map<String, String>> list;
			boolean veryControl = ModelUtil.isVersionControl(indexModelCode);
			log.debug("veryControl[" + veryControl + "]");
			if (veryControl) {
				list = accessUtil.getFile_V(contentId, version, groupId, tableName);
			} else {
				list = accessUtil.getFile_NV(contentId, version, groupId, tableName);
			}
			List<SQLBean> sqlList = new ArrayList<SQLBean>();
			FileBean fileBean = new FileBean();
			fileBean.setContentID(contentId);
			fileBean.setGroupID(groupId);
			fileBean.setVersion(version);
			accessUtil.createDelMigrateErrorDetailSQL(fileBean, tableName, sqlList);

			if (list != null) {
				for (Map<String, String> map : list) {
					fileBean.setFilePath(map.get("FILE_PATH"));
					fileBean.setSaveName(map.get("SAVE_NAME"));
					fileBean.setVolume_id(map.get("VOLUME_ID"));
					fileBean.setFileNO(map.get("FILE_NO"));
					fileBean.setGroupID(groupId);
					fileBean.setVersion(map.get("VERSION"));
					String realFilePath = VolumnUtils.getVolumnRootPath(fileBean.getVolume_id()) + fileBean.getFilePath() + fileBean.getSaveName();
					if (!FileUtil.existsFile(realFilePath)) {
						log.info("filepath=" + realFilePath + "]不存在，拼接删除该数据库记录的SQL");
						accessUtil.createDelFileNotExistSQL(fileBean, tableName, sqlList);
					}
				}
			}
			log.debug("begin to execute DB");
			accessUtil.executeDBUpdate(sqlList);
		} catch (SunECMException e) {
			log.error("tableName=" + tableName + "contentid=" + contentId + ",groupId=" + groupId + ",version=" + version + " has error", e);
		}
	}


	public boolean resetConf() {
		try {
			LazySingleton.getInstance().resetConfigInfo();
			return true;
		} catch (Exception e) {
			return false;
		}
	}

	public String getSingletonInfo() throws SunECMException {
		log.info("--getSingletonInfo(start)");
		List<LifeCycleStrategyBean> list = new ArrayList<LifeCycleStrategyBean>();
		LazySingletonBean lazy = new LazySingletonBean();
		ConfigBean configBean = LoadConfigFile.getInstance().getConfigBean();
		NodeInfo nodeInfo = LazySingleton.getInstance().load.getNodeInfoBean();
		lazy.setSERVER_IP(configBean.getServerIP());
		lazy.setSERVER_ID(configBean.getServerId());
		lazy.setSERVER_NAME(configBean.getServerName());
		lazy.setSERVER_STATUS(configBean.getStatus());
		lazy.setGROUP_NAME(nodeInfo.getGroup_name());
		lazy.setGROUP_ID(configBean.getGroupID());
		lazy.setGROUP_STATE(nodeInfo.getGroup_state());
		lazy.setGROUP_OS(nodeInfo.getGroup_os());
		lazy.setDEPLOY_MODE(nodeInfo.getDeploy_mode());
		lazy.setHTTP_PORT(nodeInfo.getHttp_port());
		lazy.setSOCKET_PORT(nodeInfo.getSocket_port());

		LoadLifeCycleStrategy lifecycle = LazySingleton.getInstance().lifeCycleStrategy;
		if (lifecycle != null) {
			Map<String, LifeCycleStrategyBean> lifemap = lifecycle.getLifeMap();
			Set<String> keyset = lifemap.keySet();
			for (String key : keyset) {
				LifeCycleStrategyBean life = new LifeCycleStrategyBean();
				life.setModel_code(lifemap.get(key).getModel_code());
				life.setTask_name(lifemap.get(key).getTask_name());
				life.setRun_type(lifemap.get(key).getRun_type());
				life.setBegin_time(lifemap.get(key).getBegin_time());
				life.setEnd_time(lifemap.get(key).getEnd_time());
				life.setTask_state(lifemap.get(key).getTask_state());
				list.add(life);
			}
			lazy.setLifeCycle(list);
		}
		log.info("--getSingletonInfo(over)");
		return XMLUtil.bean2XML(lazy);
	}

	public String regetSingletonInfo() throws SunECMException {
		log.info("--regetSingletonInfo(start)");
		LazySingleton.getInstance().resetConfigInfo();
		log.info("--regetSingletonInfo(over)");
		return getSingletonInfo();
	}

	public String getUASingletonInfo() throws SunECMException {
		log.info("--getUASingletonInfo(start)");
		LazySingletonBean lazy = new LazySingletonBean();
		ConfigBean configBean = LoadConfigFile.getInstance().getConfigBean();
		NodeInfo unityNode = LazySingleton.getInstance().load.getNodeInfoBean();
		lazy.setSERVER_IP(configBean.getServerIP());
		lazy.setSERVER_ID(configBean.getServerId());
		lazy.setSERVER_NAME(configBean.getServerName());
		lazy.setSERVER_STATUS(configBean.getStatus());
		lazy.setGROUP_NAME(unityNode.getGroup_name());
		lazy.setGROUP_ID(configBean.getGroupID());
		lazy.setHTTP_PORT(configBean.getHttpPort());
		lazy.setSOCKET_PORT(configBean.getSocketPort());
		log.info("--getUASingletonInfo(over)");
		return XMLUtil.bean2XML(lazy);
	}

	public String regetUASingletonInfo() throws SunECMException {
		log.info("--regetUASingletonInfo(start)");
		LazySingleton.getInstance().resetConfigInfo();
		log.info("--regetUASingletonInfo(over)");
		return getUASingletonInfo();
	}


	@Override
	public String createESIndex(String json) {
		log.info("createESIndex,json=" + json);
		EsClient client = null;
		try {
			List<EsMappingBean> esList = new ArrayList<EsMappingBean>();
			JSONObject aa = JSONObject.fromObject(json);
			Iterator it = aa.keys();
			String indexName = null;
			String tagState = null;
			while (it.hasNext()) {
				String key = (String) it.next();
				log.debug(key + "--" + aa.get(key));
				if (key.equals("indexName")) {
					indexName = aa.getString(key);
				} else if (key.equals("tagState")) {
					tagState = aa.getString(key);
				} else {
					JSONObject attr = JSONObject.fromObject(aa.get(key));
					EsMappingBean bean = new EsMappingBean();
					bean.setData(key);
					bean.setType(attr.getString("ATTR_TYPE"));
					bean.setIsIndex("1".equals(attr.getString("IS_INDEX")) ? "true" : "false");
					try {
						if ("keyword".equals(bean.getType())) {
							bean.setLength(Integer.valueOf(attr.getString("ATTR_LENGTH")));
						}
					} catch (Exception e) {
						log.info("", e);
					}
					esList.add(bean);
				}
			}
			client = new EsClient();
			Result<String> result = null;
			if ("1".equals(tagState)) {
				log.info("begin to createIndex");
				result = client.CreateIndex(indexName, esList);
			} else if ("3".equals(tagState)) {
				log.info("begin to updateIndex");
				result = client.updateIndexMapping(indexName, esList);
			}
			log.info("after createIndex");
			if ("false".equals(result.getMessage())) {
				return result.getData();
			} else {
				return result.getMessage();
			}
		} catch (Exception e) {
			log.error("", e);
			return Boolean.FALSE.toString();
		} finally {
			client.CloseClient();
		}
	}

	@Override
	public String deleteEsIndex(String indexName) {
		log.info("begin deleteEsIndex[" + indexName + "]");
		EsClient client = null;
		try {
			client = new EsClient();
			Result<String> result = client.DeleteIndex(indexName);
			log.info("after deleteEsIndex" + result.getData());
			if ("false".equals(result.getMessage())) {
				return result.getData();
			} else {
				return result.getMessage();
			}
		} catch (Exception e) {
			log.error("", e);
			return Boolean.FALSE.toString();
		} finally {
			client.CloseClient();
		}
	}

	@Override
	public String searchEsIndex(String indexName) {
		EsClient client = null;
		try {
			log.info("begin searchEsIndex[" + indexName + "]");
			client = new EsClient();
			String msg = client.SearchIndex(indexName);
			log.info("after searchEsIndex" + msg);
			return msg;
		} catch (Exception e) {
			log.error("", e);
			return Boolean.FALSE.toString();
		} finally {
			client.CloseClient();
		}

	}
}