package com.sunyard.console.monitor.statusManage.dao;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.common.util.DateUtil;
import com.sunyard.console.monitor.statusManage.bean.ConfigInfoSynchroBean;
import com.sunyard.console.process.exception.DBRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;

/**
 * <p>Title: 同步状态管理数据操作实现类</p>
 * <p>Description: 实现同步状态管理中数据操作的方法</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Repository("statusManageDao")
public class StatusManageDaoImp implements StatusManageDao{
	
	//分页工具
	@Autowired
	private PageTool pageTool;
	//记录序列
	@Resource(name = "configInfoSynchroID")
	private DataFieldMaxValueIncrementer incrementer;
	//日志记录
	private  final static Logger log = LoggerFactory.getLogger(StatusManageDaoImp.class);
	
	public PageTool getPageTool() {
		return pageTool;
	}
	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}
	public DataFieldMaxValueIncrementer getIncrementer() {
		return incrementer;
	}
	public void setIncrementer(DataFieldMaxValueIncrementer incrementer) {
		this.incrementer = incrementer;
	}
	
	
	@SuppressWarnings("unchecked")
	public List<ConfigInfoSynchroBean> getNodeStatusList(int start, int limit) {
		log.info( "--getNodeStatusList(start)");
		List<ConfigInfoSynchroBean> nodeStatusInfos = null;
		StringBuffer sql = new StringBuffer();
		
		sql.append("SELECT T1.CONFIG_ID, T1.CONFIG_TYPE, T1.CONFIG_TABLE, T1.SYNCHRO_STATUS, T1.CONFIG_CODE, T1.REMARK, T1.MODIFY_TYPE, T1.NODE_ID, T1.SYNCHRO_DATE, T2.SERVER_NAME, T2.SERVER_IP ");
		sql.append("FROM CONFIG_INFO_SYNCHRO T1, CONTENT_SERVER_INFO T2 ");
		sql.append("WHERE T1.NODE_ID = T2.SERVER_ID AND T1.TARGET_SERVER_TYPE != 2 ");
		sql.append("UNION ");
		sql.append("SELECT T1.CONFIG_ID, T1.CONFIG_TYPE, T1.CONFIG_TABLE, T1.SYNCHRO_STATUS, T1.CONFIG_CODE, T1.REMARK, T1.MODIFY_TYPE, T1.NODE_ID, T1.SYNCHRO_DATE, T2.SERVER_NAME, T2.SERVER_IP ");
		sql.append("FROM CONFIG_INFO_SYNCHRO T1, UNITY_ACCESS_SERVER T2 ");
		sql.append("WHERE T1.NODE_ID = T2.SERVER_ID AND T1.TARGET_SERVER_TYPE = 2 ");
		sql.append("ORDER BY SYNCHRO_DATE DESC");
		try {
			log.debug( "--getNodeStatusList-->sql:" + sql.toString());
			nodeStatusInfos = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(sql.toString(), start, limit),ConfigInfoSynchroBean.class );
		} catch (SQLException e) {
			//记录日志
			log.error( "状态管理->查询同步状态失败" + e.toString());
			throw new DBRuntimeException(
					"StatusManageDaoImp===>getNodeStatusList:"
							+ e.toString());
		}
		sql = null;
		log.info( "--getNodeStatusList(over)-->nodeStatusInfos:"+nodeStatusInfos);
		return nodeStatusInfos;
	}


	@SuppressWarnings("unchecked")
	public List<ConfigInfoSynchroBean> getAllNodeStatusList() {
		log.info( "--getAllNodeStatusList(start)");
		List<ConfigInfoSynchroBean> nodeStatusInfos = null;
		StringBuffer sql = new StringBuffer();

		sql.append("SELECT T1.CONFIG_ID, T1.CONFIG_TYPE, T1.CONFIG_TABLE, T1.SYNCHRO_STATUS, T1.CONFIG_CODE, T1.REMARK, T1.MODIFY_TYPE, T1.NODE_ID, T1.SYNCHRO_DATE, T2.SERVER_NAME, T2.SERVER_IP ");
		sql.append("FROM CONFIG_INFO_SYNCHRO T1, CONTENT_SERVER_INFO T2  ");
		sql.append("WHERE T1.NODE_ID = T2.SERVER_ID AND T1.TARGET_SERVER_TYPE !=2 ");
		sql.append("UNION ");
		sql.append("SELECT T1.CONFIG_ID, T1.CONFIG_TYPE, T1.CONFIG_TABLE, T1.SYNCHRO_STATUS, T1.CONFIG_CODE, T1.REMARK, T1.MODIFY_TYPE, T1.NODE_ID, T1.SYNCHRO_DATE, T2.SERVER_NAME, T2.SERVER_IP ");
		sql.append("FROM CONFIG_INFO_SYNCHRO T1, UNITY_ACCESS_SERVER T2 ");
		sql.append("WHERE T1.NODE_ID = T2.SERVER_ID AND T1.TARGET_SERVER_TYPE = 2 ");
		
		try {
			log.debug( "--getAllNodeStatusList-->sql:" + sql.toString());
			nodeStatusInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), ConfigInfoSynchroBean.class);
		} catch (SQLException e) {
			//记录日志
			log.error( "状态管理->查询所有同步状态失败" + e.toString());
			throw new DBRuntimeException(
					"StatusManageDaoImp===>getAllNodeStatusList:"
							+ e.toString());
		}
		sql = null;
		log.info( "--getAllNodeStatusList(over)-->nodeStatusInfos:"+nodeStatusInfos);
		return nodeStatusInfos;
	}

	
	public void delSyncStatusInfo(String configIDs) {
		log.info( "--delSyncStatusInfo(start)-->configIDs:" + configIDs);
		if("".equals(configIDs)||configIDs==null){
			return;
		}
		
		String[] ids = configIDs.split(",");
		
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM CONFIG_INFO_SYNCHRO WHERE CONFIG_ID IN(");
		for (int i = 0; i < ids.length; i++) {
			sql.append(Integer.parseInt(ids[i]));
			if (i < ids.length - 1) {
				sql.append(",");
			}
		}
		sql.append(")");
		try {
			log.debug( "--delSyncStatusInfo-->sql:" + sql.toString());
			DataBaseUtil.SUNECM.execute(sql.toString());
		} catch (SQLException e) {
			//记录日志
			log.error( "状态管理->删除同步状态失败" + e.toString());
			throw new DBRuntimeException(
					"StatusManageDaoImp===>delSyncStatusInfo:"
							+ e.toString());
		}
		sql = null;
		log.info( "--delSyncStatusInfo(over)-->configIDs:" + configIDs);
	}
	
	
	public void addSyncStatusInfo(ConfigInfoSynchroBean nodeStatusBean) {
		log.info( "--addSyncStatusInfo(start)-->nodeStatusBean:" + nodeStatusBean.toString());
		StringBuffer sql = new StringBuffer();
		
		sql.append("INSERT INTO CONFIG_INFO_SYNCHRO(CONFIG_ID,CONFIG_TABLE,CONFIG_CODE,CONFIG_TYPE,SYNCHRO_STATUS,NODE_ID,REMARK,MODIFY_TYPE,SYNCHRO_DATE,TARGET_SERVER_TYPE)");
		sql.append("VALUES");
		sql.append("(").append(incrementer.nextIntValue());
		sql.append(",'").append(nodeStatusBean.getConfig_table()).append("'");
		sql.append(",'").append(nodeStatusBean.getConfig_code()).append("'");
		sql.append(",'").append(nodeStatusBean.getConfig_type()).append("'");
		sql.append(",'").append(nodeStatusBean.getSynchro_status()).append("'");
		sql.append(",").append(nodeStatusBean.getNode_id());
		sql.append(",'").append(nodeStatusBean.getRemark()).append("'");
		sql.append(",'").append(nodeStatusBean.getModify_type()).append("'");
		sql.append(",'").append(DateUtil.getCurrentDateTime()).append("'");
		sql.append(",").append(nodeStatusBean.getTarget_server_type());
		sql.append(")");
		
		try{
			log.debug( "--addSyncStatusInfo-->sql:" + sql.toString());
			DataBaseUtil.SUNECM.update(sql.toString());
		}catch(Exception e){
			//记录日志
			log.error( "状态管理->新增同步状态信息失败" + e.toString());
			throw new DBRuntimeException(
					"StatusManageDaoImp===>addSyncStatusInfo:"
							+ e.toString());
		}
		sql = null;
		log.info( "--addSyncStatusInfo(over)");
	}
	
	public ConfigInfoSynchroBean getSingleServerStatus(String configID) {
		log.info( "--getSingleServerStatus(start)-->configID:" + configID);
		ConfigInfoSynchroBean configInfoSynchro = null;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT CONFIG_TYPE, CONFIG_TABLE, SYNCHRO_STATUS, CONFIG_CODE, REMARK, MODIFY_TYPE, NODE_ID,TARGET_SERVER_TYPE ");
		sql.append("FROM CONFIG_INFO_SYNCHRO ");
		sql.append("WHERE CONFIG_ID = ").append(configID);
		
		try {
			log.debug( "--getSingleServerStatus-->sql:" + sql.toString());
			configInfoSynchro = (ConfigInfoSynchroBean) DataBaseUtil.SUNECM.queryBean(sql.toString(),ConfigInfoSynchroBean.class);
		} catch (SQLException e) {
			//记录日志
			log.error( "状态管理->查询同步状态失败" + e.toString());
			throw new DBRuntimeException(
					"StatusManageDaoImp===>getSingleServerStatus:"
							+ e.toString());
		}
		sql = null;
		log.info( "--getSingleServerStatus(over)-->configInfoSynchro:" + configInfoSynchro);
		return configInfoSynchro;
	}
	
	public void updateSyncStatus(String configID) {
		log.info( "--updateSyncStatus(start)-->configID:" + configID);
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE CONFIG_INFO_SYNCHRO SET SYNCHRO_STATUS = '1' ");
		sql.append("WHERE CONFIG_ID = ").append(configID);
		
		try {
			log.debug( "--updateSyncStatus-->sql:" + sql.toString());
			DataBaseUtil.SUNECM.update(sql.toString());
		} catch (SQLException e) {
			//记录日志
			log.error( "状态管理->更新同步状态失败" + e.toString());
			throw new DBRuntimeException(
					"StatusManageDaoImp===>updateSyncStatus:"
							+ e.toString());
		}
		sql = null;
		log.info( "--updateSyncStatus(over)");
	}

}
