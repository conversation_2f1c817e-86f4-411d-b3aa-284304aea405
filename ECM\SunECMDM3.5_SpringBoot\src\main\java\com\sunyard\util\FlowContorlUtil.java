package com.sunyard.util;

import java.io.FilterInputStream;
import java.io.IOException;
import java.io.InputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 流量控制 
 * <AUTHOR>
 *
 */

public class FlowContorlUtil extends FilterInputStream {
	private static final Logger log = LoggerFactory
			.getLogger(FlowContorlUtil.class);
	private long timestamp; // 系统当前时间（单位ms）
    private int maxbps; // 限制的流量设定
    private int currentbps; // 当前的流量
    private int bytesread; // 读取的字节数

	/**
	 * 构造函数
	 * @param in
	 * @param maxbps 流量限制
	 */
	public FlowContorlUtil(InputStream in, int maxbps) {
		super(in);
		this.maxbps = maxbps;
		this.currentbps = 0;
		this.bytesread = 0;
		this.timestamp = System.currentTimeMillis();
	}
	
    /**
     * 检查单位时间可用速率
     * @return
     */
    public int check() { 
        long now = System.currentTimeMillis();
        if (now - timestamp >= 1000) { // 超过单位时间，则全速率
            timestamp = now;
            currentbps = bytesread;
            bytesread = 0;
           log.info("当前速率："+ (maxbps)/1000+"kbps");
            return maxbps;
        } else { // 速率控制在剩余的可用流量内
        	 log.info("当前速率："+ (maxbps - bytesread)/1000+"kbps");
            return maxbps - bytesread;
        } 
    } 

	 /* (non-Javadoc)
	 * @see java.io.FilterInputStream#read()
	 */
	public int read() throws IOException {
	        synchronized(in) {
	            int avaliable = check();
	            if (avaliable == 0) {
	                waitForAvailable();
	                avaliable = check();
	            }
	            int value = in.read();
	            update(1);
	            return value;
	        } 
	    } 

	    /* (non-Javadoc)
	     * @see java.io.FilterInputStream#read(byte[])
	     */
	    public int read(byte[] b) throws IOException {
	        return read(b, 0, b.length);
	    }

	    /* (non-Javadoc)
	     * @see java.io.FilterInputStream#read(byte[], int, int)
	     */
	    public int read(byte[] b, int off, int len) throws IOException {
	        synchronized(in) {
	            int avaliable = check();
	            if (avaliable == 0) { // 如果达到单位时间流量上限，则暂停并等待恢复可用的流量
	                waitForAvailable();
	                avaliable = check();
	            }
	            int n = in.read(b, off, Math.min(len, avaliable));
	            update(n);
	            return n;
	        }
	    }

	    /**
	     * 等待恢复可用的流量
	     */
	    public void waitForAvailable() {
	        long time = System.currentTimeMillis() - timestamp;
	        boolean isInterrupted = false;
	        while ( time < 1000 ) {
	            try {
	                Thread.sleep( 1000 - time );
	            } catch (InterruptedException e) {
	                isInterrupted = true;
	            }
	            time = System.currentTimeMillis() - timestamp;
	        }
	        if (isInterrupted) {
	            Thread.currentThread().interrupt(); // 中断，唤醒休眠线程
	        }
	        return;
	    }

	    /**
	     * 更新已经读取的文件字节数
	     * @param n
	     */
	    public void update(int n) {
	        bytesread += n;
	    }
	    
	    /**
	     * 获取当前的速率
	     * @return
	     */
	    public int getCurrentbps() {
	        return currentbps;
	    }
	    
	    
//	    public static void main(String[] args) {
//	        try {
//	            byte[] buffer = new byte[65532];
//	            long start = System.currentTimeMillis();
//	            FileInputStream in = new FileInputStream("D:\\D300S.pdf");
//	            //设定限制流量 10000=10kbps
//	            FlowContorlUtil fcu = new FlowContorlUtil(in, 10000);
//	            System.out.println("当前速率："+fcu.check()/1000+"kbps");
//	            System.out.println("当前文件大小:"+fcu.available()/1024+"kb");
//	            while( fcu.read(buffer) > 0) {
//	            	;
//	            }
//	            fcu.close();
//	            long end = System.currentTimeMillis();
//	            System.out.println("读取文件完成，共花掉"+(end-start)/600+"秒");
//	        } catch (IOException e) {
//	            e.printStackTrace();
//	        }
//	    }
	    
}