<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.server_name"
        placeholder="服务器名称"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-if="this.hasPerm('nodeSearch')"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
      <el-button
        v-if="this.hasPerm('addNode')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        添加服务器
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column label="服务器ID" v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.server_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="服务器名称" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="IP" width="110px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="HTTP端口" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.http_port }}</span>
        </template>
      </el-table-column>

      <el-table-column label="SOCKET端口" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.socket_port }}</span>
        </template>
      </el-table-column>
      <el-table-column label="HTTPS端口" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.https_port }}</span>
        </template>
      </el-table-column>

      <el-table-column label="传输协议" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.trans_protocol }}</span>
        </template>
      </el-table-column>

      <el-table-column label="连接方式" width="100px" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.isdb_conn == '1'">远程直连</span>
          <span v-if="row.isdb_conn == '2'">soap连接</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100px" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.state == '1'">启用</span>
          <span v-if="row.state == '0'">禁用</span>
        </template>
      </el-table-column>

      <el-table-column label="备注" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.remark }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="left"
        width="210"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <el-button
            v-if="hasPerm('editNode')"
            type="primary"
            icon="el-icon-edit"
            size="mini"
            @click="handleUpdate(row)"
            style="margin-bottom:5px;margin-left:10px"
          >
            编辑
          </el-button>
          <el-button
            v-if="row.state == '0' && hasPerm('ableNode')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="handleStart(row, $index)"
            style="margin-bottom:5px"
          >
            启用
          </el-button>

          <el-button
            v-if="row.state == '1' && hasPerm('disableNode')"
            size="mini"
            type="danger"
            icon="el-icon-turn-off"
            @click="handleStop(row, $index)"
            style="margin-bottom:5px"
          >
            禁用
          </el-button>
          <el-button v-if="hasPerm('addNode')"
            type="primary"
            size="mini"
            plain round
            icon="el-icon-view"
            @click="handleInit(row)"
            style="margin-bottom:5px"
          >
            内存信息展示
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="服务器名称" prop="server_name">
          <el-input v-model="temp.server_name" />
        </el-form-item>
        <el-form-item label="HTTP端口" prop="http_port">
          <el-input-number v-model.number="temp.http_port" />
        </el-form-item>
        <el-form-item label="SOCKET端口" prop="socket_port">
          <el-input-number v-model.number="temp.socket_port"/>
        </el-form-item>
        <el-form-item label="HTTPS端口" prop="https_port">
          <el-input-number v-model.number="temp.https_port" />
        </el-form-item>
        <el-form-item label="传输协议" prop="trans_protocol">
          <el-select v-model="temp.trans_protocol" placeholder="请选择传输协议">
            <el-option
              v-for="item in ProtocalTypes"
              :key="item.key"
              :label="item.display_name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="连接方式" prop="isdb_conn">
          <el-select v-model="temp.isdb_conn" placeholder="请选择连接方式">
            <el-option
              v-for="item in dbConns"
              :key="item.key"
              :label="item.display_name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-select v-model="temp.state" placeholder="请选择状态">
            <el-option
              v-for="item in states"
              :key="item.key"
              :label="item.display_name"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="IP" prop="server_ip">
          <el-input v-model="temp.server_ip" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="temp.remark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary" size="mini"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :visible.sync="dialogPvVisible" title="DM初始化页面信息">
      <el-form :inline="true">
        <el-form-item
          v-for="item in listData"
          :key="item.name"
          :label="item.title + ':'"
          style="margin-bottom: 0"
        >
          [<span style="color: blue">{{ pvData[item.name] }}</span
          >]
        </el-form-item>
      </el-form>
      <el-card class="box-card" style="margin-top: 15px">
        <div slot="header" lass="clearfix">
          <span style="font-weight: bolder">策略信息</span>
        </div>
        <el-table
          :data="lifemap"
          border
          fit
          highlight-current-row
          style="width: 100%"
          @sort-change="sortChange"
        >
          <el-table-column label="内容对象" width="110px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.model_code }}</span>
            </template>
          </el-table-column>

          <el-table-column label="任务名称" width="100px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.task_name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="运行类型" width="100px" align="center">
            <template slot-scope="{ row }">
              <span v-if="row.run_type == 'RT_01'">每天</span>
              <span v-if="row.run_type == 'RT_02'">每周</span>
              <span v-if="row.run_type == 'RT_03'">每月</span>
              <span v-if="row.run_type == 'RT_04'">每年</span>
            </template>
          </el-table-column>

          <el-table-column label="开始运行时间" width="130px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.begin_row }}</span>
            </template>
          </el-table-column>

          <el-table-column label="结束运行时间" width="130px" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.end_time }}</span>
            </template>
          </el-table-column>

          <el-table-column label="策略状态" width="100px" align="center">
            <template slot-scope="{ row }">
              <span v-if="row.task_state == '1'">启用</span>
              <span v-if="row.task_state == '0'">禁用</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="dialogPvVisible = false"
          >关闭</el-button
        >
        <el-button size="mini" type="primary" @click="resetLazySington()">
          发送重新初始化配置命令
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getContentServerList,
  addContentServer,
  updateContentServer,
  stopContentServer,
  startContentServer,
  singletonInfoSearch,
  resetSingletonInfoSearch,
} from "@/api/contentServer";

import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination

import global from "../../store/global.js";

export default {
  name: "ComplexTable",
  components: { Pagination },
  directives: { waves,elDragDialog },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
  },

  data() {
    return {
      tableKey: 0,
      list: null,
      lazylist: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+server_name",
        server_name:''
      },
      importanceOptions: [1, 2, 3],
      sortOptions: [
        { label: "ID Ascending", key: "+server_name" },
        { label: "ID Descending", key: "-server_name" },
      ],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
        http_port:'',
        socket_port:'',
        https_port:''
      },
      ProtocalTypes: [
        { key: "http", display_name: "http" },
        { key: "https", display_name: "https" },
      ],
      dbConns: [
        { key: "1", display_name: "远程直连" },
        { key: "2", display_name: "soap连接" },
      ],
      states: [
        { key: "1", display_name: "启用" },
        { key: "0", display_name: "禁用" },
      ],
      dialogFormVisible: false,
      dialogStatus: "",
      textMap: {
        update: "修改服务器",
        create: "添加服务器",
      },
      dialogPvVisible: false,
      listData: [
        { name: "server_id", title: "服务器ID" },
        { name: "server_ip", title: "服务器IP" },
        { name: "server_name", title: "服务器名称" },
        { name: "server_status", title: "服务器状态" },
        { name: "http_port", title: "HTTP端口" },
		{ name: "socket_port", title: "SOCKET端口" },
        { name: "group_id", title: "服务器组ID" },
        { name: "group_name", title: "服务器组名称" },
        { name: "group_state", title: "服务器组状态" },
        { name: "group_os", title: "操作系统" },
        { name: "deploy_mode", title: "部署方式" }
      ],
      pvData: [],
      lifemap: [],
      rules: {
        server_name: [
          { required: true, 
            pattern: global.regexName,
            message: global.regexNameText, 
            trigger: "blur" },
        ],
        http_port: [
          { required: true, message: "端口必输且为数字", trigger: "blur" },
        ],
        socket_port: [
          { required: true, message: "端口必输且为数字", trigger: "blur" },
        ],
        https_port: [
          { required: true, message: "端口必输且为数字", trigger: "blur" },
        ],
        trans_protocol: [
          { required: true, message: "传输协议必选", trigger: "blur" },
        ],
        isdb_conn: [
          { required: true, message: "连接方式必选", trigger: "blur" },
        ],
        state: [{ required: true, message: "服务器状态必选", trigger: "blur" }],
        server_ip: [
          { required: true, pattern: global.regNoSpecial, message: "服务器IP必输", trigger: "blur" },
        ],
      },
      downloadLoading: false,
    };
  },

  created() {
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      getContentServerList(this.listQuery).then((response) => {
        this.list = response.root;
        this.total = Number(response.totalProperty);
        // Just to simulate the time of the request
        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },
    handleclear() {
      this.listQuery.server_name = "";
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    sortChange(data) {
      const { prop, order } = data;
      alert(prop);
      if (prop === "server_name") {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === "ascending") {
        this.listQuery.sort = "+server_name";
      } else {
        this.listQuery.sort = "-server_name";
      }
      this.handleFilter();
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
        state:"1",
        isdb_conn:"1",
        trans_protocol:"http",
        http_port:'',
        socket_port:'',
        https_port:''
      };
    },
    resetLazy() {
      this.pvData = {
        id: undefined
      }
      this.lifemap = null;
    },
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    createData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.temp.id = parseInt(Math.random() * 100) + 1024; // mock a id
          this.temp.author = "vue-element-admin";
          addContentServer(this.temp).then(() => {
            // this.list.unshift(this.temp)
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Created Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row); // copy obj
      this.temp.timestamp = new Date(this.temp.timestamp);
      this.dialogStatus = "update";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    updateData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp);
          tempData.timestamp = +new Date(tempData.timestamp); // change Thu Nov 30 2017 16:41:05 GMT+0800 (CST) to 1512031311464
          updateContentServer(tempData).then(() => {
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Update Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },

    handleStart(row, index) {
      startContentServer(row).then((response) => {
        this.getList();
      });
    },

    handleStop(row, index) {
      stopContentServer(row).then((response) => {
        this.getList();
      });
    },

    handleInit(row){
      this.temp = Object.assign({}, row);
      this.getLazySington(this.temp);
      this.dialogPvVisible = true;
    },

    changeData(lazyData){
        if(lazyData.server_status == 1){
          lazyData.server_status = '运行'
        }else if(lazyData.server_status == 0){
          lazyData.server_status = '停用'
        }
        if(lazyData.group_state == 1){
          lazyData.group_state = '运行'
        }else if(lazyData.group_state == 0){
          lazyData.group_state = '停用'
        }
        if(lazyData.group_os == 1){
          lazyData.group_os = 'windows'
        }else if(lazyData.group_os == 2){
          lazyData.group_os = 'mac'
        }else if(lazyData.group_os == 3){
          lazyData.group_os = 'linux'
        }else if(lazyData.group_os == 4){
          lazyData.group_os = 'aix'
        }
        if(lazyData.deploy_mode == 1){
          lazyData.deploy_mode = '其他集群方式'
        }else if(lazyData.deploy_mode == 0){
          lazyData.deploy_mode = 'ECM负载均衡'
        }   
        this.lifemap = lazyData.life_map
        if( this.lifemap!=null){
          for(let item of this.lifemap){
              item.begin_row = this.checkBegin(item.run_type,item.begin_time)
          }
        }
    },

    getLazySington(data){
      this.resetLazy();
      singletonInfoSearch(data).then(response => {
        this.pvData = response.root;
        this.changeData(this.pvData)
      })
    },

    resetLazySington(){   
       this.$notify({
              title: 'Refresh',
              message: 'refresh success',
              type: 'success',
              duration: 2000
      })
      resetSingletonInfoSearch(this.temp).then(response => {
        this.pvData = response.root;
        this.changeData(this.pvData)
      })
    },

    checkBegin(rt, value) {
      let bg = value.split(":");
      if (rt == "RT_01") {
        //每天
        return bg[2] + ":" + bg[3];
      } else if (rt == "RT_02") {
        //每周
        return "周" + bg[4] + "  " + bg[2] + ":" + bg[3];
      } else if (rt == "RT_03") {
        //每月
        return bg[1] + "日   " + bg[2] + ":" + bg[3];
      } else if (rt == "RT_04") {
        //每年
        return bg[0] + "月" + bg[1] + "日    " + bg[2] + ":" + bg[3];
      }
    },

    formatJson(filterVal) {
      return this.list.map((v) =>
        filterVal.map((j) => {
          if (j === "timestamp") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort;
      return sort === `+${key}` ? "ascending" : "descending";
    },
  },
};
</script>
