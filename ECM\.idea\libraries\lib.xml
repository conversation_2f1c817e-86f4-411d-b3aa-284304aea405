<component name="libraryTable">
  <library name="lib">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/src/main/resources/lib/postgresql-42.2.19.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/src/main/resources/lib/SunECMClientV3.5.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/src/main/resources/lib/ESAPI.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/src/main/resources/lib/sunecmoffline.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/src/main/resources/lib/redisclien.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/src/main/resources/lib/DmJdbcDriver18.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/src/main/resources/lib/sun_comm_service_init.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>