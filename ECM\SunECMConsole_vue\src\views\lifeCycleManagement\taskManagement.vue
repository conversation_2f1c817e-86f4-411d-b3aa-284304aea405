<template>
  <div class="app-container">
    <div>
      <label-wrap>任务名称:</label-wrap>
      <el-input
        v-model="listQuery.task_name"
        style="width: 200px"
      />
    </div>
    <div align="center">
      <el-button v-if="this.hasPerm('scheduleSearch')"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round @click="getList">查询</el-button>
      <el-button type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round  @click="reset">重置</el-button>
    </div>


    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%;margin-top: 30px"
    >
      <el-table-column label="任务名称" width="300px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.task_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="任务路径" width="900px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.task_class }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination'
  import {getTaskListAction} from '@/api/taskManage'


  export default {
    components: { Pagination},
    data() {
      return {
        tableKey: 0,
        list: null,
        total: 0,
        listLoading: true,
        listQuery: {
          start:0,
          page: 1,
          limit: 20,
          task_name:'',
          model_code:'',
          task_no:''
        },
      }
    },
    created() {
      this.getList()
    },
    methods: {
      getList() {
        this.listLoading = true
        getTaskListAction(this.listQuery).then(response => {
          this.list = response.root
          this.total = Number(response.totalProperty)
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
      reset() {
        this.listQuery.task_name=""
      }
    }
  }
</script>
