<?xml version="1.0" encoding="UTF-8"?>
<beans
        xmlns="http://www.springframework.org/schema/beans"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:p="http://www.springframework.org/schema/p"
        xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">


        <import resource="database-postgresql-monitoring-config.xml"/>
    <!--    <import resource="database-mysql-monitoring-config.xml"/>-->
    <!--<import resource="database-oracle-monitoring-config.xml"/>-->
    <!--
    <bean id="test" class="com.syd.common.serviceinit.TestService" scope="prototype"></bean>
    <bean id="service" class="com.syd.common.serviceinit.ServiceLoader" scope="singleton" init-method="loading">
        <property name="interruptLoad" value="false" />
        <property name="serviceList">
        <list>
        <ref bean="test"/>
        </list>
        </property>
    </bean>
    <bean id="jdbcTemplate"
        class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource" />
    </bean>
    -->
    <!--    <bean id="jdbcTemplate"
              class="org.springframework.jdbc.core.JdbcTemplate">
            <property name="dataSource" ref="dataSource" />
        </bean>

        <bean id="transactionManager"
              class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
            <property name="dataSource" ref="dataSource" />
        </bean>-->
<!--    <import resource="provider.xml"/>-->

<!--    <import resource="consumer.xml"/>-->

</beans>