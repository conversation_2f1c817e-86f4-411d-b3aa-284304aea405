<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           ">

    <!-- 数据库配置 -->
    <!-- <import resource="database-oracle-config.xml"/>-->
<!--  <import resource="direct-conn-config.xml"/>-->
<!--    <import resource="dubbo-conn-config.xml"/>-->
    <import resource="database-postgresql-config.xml"/>
       <import resource="soap-conn-config.xml"/>
    <!-- 传输层配置 -->
    <import resource="spring-connection.xml"/>

    <!-- 业务层配置 -->
    <import resource="spring-service-dm.xml"/>

    <!-- DAO数据接口层配置 -->
    <import resource="spring-dao-dm.xml"/>

    <!-- 事件监听配置 -->
    <import resource="spring-event-listener.xml"/>

<!--    <import resource="consumer.xml"/>-->

<!--    <import resource="provider.xml"/>-->

</beans>