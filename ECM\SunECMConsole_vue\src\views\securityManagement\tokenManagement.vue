<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-if="this.hasPerm('addtoken')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        新增令牌
       </el-button>
        <!--    <el-button
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-edit"
        @click="handleApply"
      >
        申请令牌
      </el-button> -->
    </div>

     <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column label="机器IP"   min-width="20%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.ip }}</span>
        </template>
      </el-table-column>

      <el-table-column label="机器信息"  min-width="20%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_info }}</span>
        </template>
      </el-table-column>

      <el-table-column label="申请令牌方式" min-width="10%" align="center">
        <template slot-scope="{ row }" >
          <span v-if="row.isTrend == '1'">动态</span>
          <span v-if="row.isTrend == '0'">静态</span>
        </template>
      </el-table-column>

       <el-table-column label="令牌密码" min-width="30%" align="center">
        <template slot-scope="{ row }" >
           <span>{{ row.token_code }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        min-width="20%"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <el-button
            v-if="row.isTrend == '0'&& hasPerm('delToken')"
            size="mini"
            type="danger"
            icon="el-icon-delete-solid"
            @click="handleDelToken(row, $index)"
          >
            删除令牌
          </el-button>

          <el-button
            v-if="row.isTrend == '1'"
            size="mini"
            type="danger"
            icon="el-icon-delete-solid"
            @click="handleDelMachine(row, $index)"
          >
            删除机器
          </el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="titleper" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="机器IP" prop="ip">
          <el-input v-model="temp.ip"/>
        </el-form-item>
        <el-form-item label="令牌申请方式" prop="isTrend">
          <el-select v-model="temp.isTrend" placeholder="请选择令牌申请方式">
            <el-option
              v-for="item in TokenSelect"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="机器信息" prop="server_info">
          <el-input v-model="temp.server_info" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary" size="mini"
          @click="postTokenData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
   </div>
</template>

<script>
import {getTokenList, addTokenInfo,deleteToken,deleteMachine} from '@/api/tokenManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import global from '../../store/global.js'

export default {
  name: 'ComplexTable',
  components: { Pagination },
  directives: { waves,elDragDialog },
  filters: {
  },

  data() {
    return {
      titleper:'新增令牌',
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: '+ip'
      },
      importanceOptions: [1, 2, 3],
      sortOptions: [{ label: 'ID Ascending', key: '+ip' }, { label: 'ID Descending', key: '-ip' }],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        type: '',
        status: 'published'
      },

      TokenSelect : [
            { key: '1', display_name: '动态' },
            { key: '0', display_name: '静态' }
        ],

      dialogFormVisible: false,
      dialogStatus: '',

     rules: {
        ip: [{ required: true, message: '机器IP必输', pattern: global.regNoSpecial,trigger: 'blur' }],
        isTrend: [{ required: true, message: '申请方式必选', trigger: 'blur' }]
      },
      downloadLoading: false,
    }
  },

  created() {
    this.getList()
  },


  methods: {
    getList() {
      this.listLoading = true
      getTokenList(this.listQuery).then(response => {
        this.list = response.root
        this.total = Number(response.totalProperty)
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    sortChange(data) {
      const { prop, order } = data
      alert(prop)
      if (prop === 'ip') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+ip'
      } else {
        this.listQuery.sort = '-ip'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        status: 'published',
        type: '',
        name: ''
      }
    },
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    postTokenData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.id = parseInt(Math.random() * 100) + 1024 // mock a id
          this.temp.author = 'vue-element-admin'
          addTokenInfo(this.temp).then(() => {
            this.getList();
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Created Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },

    handleDelToken(row, index) {
      this.openDelConfirm().then(() => {
        deleteToken(row).then(response => {
          this.getList()
          this.$notify({
              title: "Success",
              message: "Delete Successfully",
              type: "success",
              duration: 2000,
            });
        })
      })
    },

    handleDelMachine(row, index) {
      this.openDelConfirm().then(() => {
        deleteMachine(row).then(response => {
          this.getList()
          this.$notify({
              title: "Success",
              message: "Delete Successfully",
              type: "success",
              duration: 2000,
            });
        })
      })
    },

    openDelConfirm(){
      return this.$confirm(`是否确定删除？`,'提示',{
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      })
    },

    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    }
  }
}
</script>
