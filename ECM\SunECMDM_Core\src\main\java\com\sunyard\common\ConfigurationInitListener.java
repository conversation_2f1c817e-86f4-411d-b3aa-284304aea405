/**
 * 
 */
package com.sunyard.common;

import com.sunyard.client.common.ClientConfiguration;
import com.sunyard.ecm.server.cache.ReloadConfigInfoJob;
import com.sunyard.ecm.server.dm.batchEs.service.EsMainThread;
import com.sunyard.msgqueue.RunTakeMsg;
import com.sunyard.msgqueue.TcpStatusRun;
import com.sunyard.msgqueue.msgreport.MigrateQueueUpdate;
import com.sunyard.msgqueue.msgreport.ReportUpdate;
import com.sunyard.msgqueue.msgreport.SocketNumReportUpdate;
import com.sunyard.msgqueue.msgreport.SocketStartUpdate;
import com.sunyard.text.SqlUtil;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

/**
 * 容器启动时首先加载配置文件
 * 
 * <AUTHOR> 2013-7-25
 * 
 * @version v1.0.0
 * 
 */
@WebListener
public class ConfigurationInitListener implements ServletContextListener {
	private static final Logger LOG = LoggerFactory
			.getLogger(ConfigurationInitListener.class);
	Scheduler scheduler = null;

	RunTakeMsg runTakeMsg = null;

	ReportUpdate reportUpdate = null;

	SocketNumReportUpdate socketNumReportUpdate = null;

	TcpStatusRun tcpStatusRun = null;
	
	SocketStartUpdate socketStartUpdate = null;
	
	MigrateQueueUpdate migrateQueueUpdate = null;
    
	EsMainThread es = null;
	/*
	 * (non-Javadoc)
	 * 
	 * @seejavax.servlet.ServletContextListener#contextDestroyed(javax.servlet.
	 * ServletContextEvent)
	 */
	public void contextDestroyed(ServletContextEvent arg0) {
		Configuration.clear();
		try {
			Configuration.clear();
			if (scheduler!=null&&scheduler.isStarted()) {
				scheduler.shutdown();
			}
			// 等待1秒钟，使线程池关闭。减少不必要的无法关闭线程的日志。
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				LOG.error("线程出错");
			}
		} catch (Exception e) {
			LOG.error("scheduler.shutdown error", e);
		}
		if (runTakeMsg != null) {
			runTakeMsg.stop();
		}
		if (reportUpdate != null) {
			reportUpdate.stop();
		}
		if (socketNumReportUpdate != null) {
			socketNumReportUpdate.stop();
		}
		if (tcpStatusRun != null) {
			tcpStatusRun.stop();
		}
		if(socketStartUpdate!=null){
			socketStartUpdate.stop();
		}
		if(migrateQueueUpdate!=null){
			migrateQueueUpdate.stop();
		}
		if(es!=null) {
		   es.stop();
		}
	}

	/**
	 * 启动创建重新向console获取配置信息任务
	 * 
	 * @param timmer
	 *            定时参数
	 */
	private void reloadConfigInfoJob(String timmer) {
		LOG.info("begin to reloadConfigInfoJob");
		try {
			scheduler = StdSchedulerFactory.getDefaultScheduler();
			scheduler.start();
			JobDetail jobDetail = JobBuilder.newJob(ReloadConfigInfoJob.class).withIdentity("reloadConfigInfoJob")
					.build();
			CronTrigger ctrigger = TriggerBuilder.newTrigger().withIdentity("reloadConfigInfoTriger")
					.withSchedule(CronScheduleBuilder.cronSchedule(timmer)).build();
			scheduler.scheduleJob(jobDetail, ctrigger);
		} catch (Exception e) {
			LOG.error("初始化dm每天主动获取配置信息出错,服务停止启动", e);
		}
		LOG.info("after reloadConfigInfoJob");
	}


	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * javax.servlet.ServletContextListener#contextInitialized(javax.servlet
	 * .ServletContextEvent)
	 */
	public void contextInitialized(ServletContextEvent arg0) {
		try {
			LOG.info("begin to start scheduler");
			// String timer="0 40 23 * * ?";

			Configuration.init();
			ClientConfiguration.init();

			String timer = Configuration.get("resetinfotimer", "false");
			if (!"false".equals(timer)) {
				reloadConfigInfoJob(timer);// 0 * 14 * * ?"
			}
			// reloadConfigInfoJob(timer);//0 * 14 * * ?"

			LOG.info("end to start scheduler,timer" + timer + "]");

			String path = System.getProperty("user.dir")+"src/main/webapp";
					//arg0.getServletContext().getRealPath("/");
			LOG.info("工程的绝对路径是：" + path);
			SqlUtil.setAbsPath(path);

			// 是否开启统计信息
			String writeReport = Configuration.get("writeReport", "false");
			if ("true".equals(writeReport)) {
				LOG.info("start writeReport to redis");
				runTakeMsg = new RunTakeMsg();
				new Thread(runTakeMsg).start();
				reportUpdate = new ReportUpdate();
				new Thread(reportUpdate).start();
				socketNumReportUpdate = new SocketNumReportUpdate();
				new Thread(socketNumReportUpdate).start();
				String localIp = Configuration.get("localIP");
				tcpStatusRun = new TcpStatusRun(localIp,
						Configuration.get("socketPort"));
				new Thread(tcpStatusRun).start();
				socketStartUpdate = new SocketStartUpdate();
				new Thread(socketStartUpdate).start();
				migrateQueueUpdate = new MigrateQueueUpdate(localIp);
				new Thread(migrateQueueUpdate).start();
			}
			//是否开启ES标签
			String IsOpenEsMainThread = Configuration.get("IsOpenEsMainThread", "false");
			if ("true".equals(IsOpenEsMainThread)) {
				LOG.info("start EsThread");
				es = new EsMainThread();
				Thread EsMainThread=new Thread(es);
				EsMainThread.setName("EsMainThread");
				EsMainThread.start();
			}
            
			LOG.info("Init the conf success");
		} catch (ConfInitFailedException e) {
			LOG.error("Init the conf get a Exception ", e);
			throw new RuntimeException("Init the conf get a Exception");
		} catch (Exception e) {
			LOG.error("error", e);
		}
	}

}