<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">


	<bean id="pageTool"
		class="com.sunyard.util.pageTool.MySQLPageTool">
	</bean>
<!--    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">-->
<!--        &lt;!&ndash; 基本配置，访问数据库的driver、url、user、password &ndash;&gt;-->
<!--        <property name="driverClassName" value="" />-->
<!--        <property name="url" value="" />-->
<!--        <property name="username" value="" />-->
<!--        <property name="password" value="" />-->
<!--        &lt;!&ndash; 配置初始化大小、最大、最小 &ndash;&gt;-->
<!--        <property name="initialSize" value="5" />-->
<!--        <property name="maxActive" value="5" />-->
<!--        <property name="minIdle" value="20" />-->
<!--        &lt;!&ndash; 配置获取连接等待超时的时间，单位是毫秒 &ndash;&gt;-->
<!--        <property name="maxWait" value="60000" />-->
<!--        &lt;!&ndash; 配置监控统计拦截的filters &ndash;&gt;-->
<!--        <property name="filters" value="stat,wall" />-->
<!--        &lt;!&ndash; 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 &ndash;&gt;-->
<!--        <property name="timeBetweenEvictionRunsMillis" value="60000" />-->
<!--        &lt;!&ndash; 配置一个连接在池中最小生存的时间，单位是毫秒 &ndash;&gt;-->
<!--        <property name="minEvictableIdleTimeMillis" value="300000" />-->
<!--        &lt;!&ndash; 用来检测连接是否有效的sql，要求是一个查询语句。如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用。 &ndash;&gt;-->
<!--        &lt;!&ndash; 查询语句需要根据不同的数据源进行调整设置 &ndash;&gt;-->
<!--        <property name="validationQuery" value="SELECT 1 FROM DUAL" />-->
<!--        &lt;!&ndash; 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。 &ndash;&gt;-->
<!--        <property name="testWhileIdle" value="true" />-->
<!--        &lt;!&ndash; 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。 &ndash;&gt;-->
<!--        <property name="testOnBorrow" value="false" />-->
<!--        &lt;!&ndash; 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能 &ndash;&gt;-->
<!--        <property name="testOnReturn" value="false" />-->
<!--        &lt;!&ndash; 对于长时间不使用的连接强制关闭 &ndash;&gt;-->
<!--        <property name="removeAbandoned" value="true" />-->
<!--        &lt;!&ndash; 超过30分钟开始关闭空闲连接 &ndash;&gt;-->
<!--        <property name="removeAbandonedTimeout" value="1800" />-->
<!--        &lt;!&ndash; 将当前关闭动作记录到日志 &ndash;&gt;-->
<!--        <property name="logAbandoned" value="false" />-->
<!--        &lt;!&ndash; 设置数据库事务是否自动提交，默认值为true &ndash;&gt;-->
<!--        <property name="defaultAutoCommit" value="false" />-->
<!--        &lt;!&ndash; 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大。&ndash;&gt;-->
<!--        <property name="poolPreparedStatements" value="true" />-->
<!--        &lt;!&ndash; 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。 &ndash;&gt;-->
<!--        <property name="maxOpenPreparedStatements" value="0" />-->
<!--        <property name="maxPoolPreparedStatementPerConnectionSize" value="20" />-->
<!--    </bean>-->
</beans> 