import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getNodeStatusInfo(data) {
  const url = '/statusManage/getNodeStatusAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function delSynStatusInfo(data) {
  return request({
    url : '/statusManage/delSynStatusInfoAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'config_ids': data.config_id}
  })
}

export function send2LowerLevels(data) {
  return request({
    url: '/statusManage/send2LowerLevelsAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'config_ids': data.config_id }
  })
}
