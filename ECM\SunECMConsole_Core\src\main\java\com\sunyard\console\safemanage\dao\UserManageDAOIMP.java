package com.sunyard.console.safemanage.dao;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.safemanage.bean.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Title: 用户管理数据库操作实现类
 * </p>
 * <p>
 * Description: 实现用户管理中数据库操作的方法
 * </p>
 * <p>
 * Copyright: Copyright (c) 2012
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Repository("umdao")
public class UserManageDAOIMP implements UserManageDAO {
	@Autowired
	private PageTool pageTool;
	private final static  Logger log = LoggerFactory.getLogger(UserManageDAOIMP.class);

	public PageTool getPageTool() {
		return pageTool;
	}

	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}

	/**
	 * 根据条件获取用户信息列表
	 * 
	 * @param userID
	 *            用户ID
	 * @param userName
	 *            用户名称
	 * @param userState
	 *            用户状态
	 * @param start
	 * @param limit
	 * @param roleID
	 *            角色ID
	 * @return 用户信息列表
	 */
	public List<UserInfoBean> searchUserInfoList(String userID, String userName, String userState, String roleID, int start, int limit) {
		log.info("--searchUserInfoList(start)-->userID:" + userID + ";userName:" + userName + ";userState:" + userState + ";roleID:" + roleID);
		List<UserInfoBean> user = null;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T1.LOGIN_ID,T1.USER_NAME,T1.USER_POST,T1.USER_DEPARTMENT,T1.USER_STATE,T1.USER_DOMAIN,T1.LDAP_CODE,T1.USER_CITY FROM USER_INFO T1 WHERE 1=1 ");
		// 过滤用户ID
		List list = new ArrayList();
		if (userID != null && !"".equals(userID)) {
			sql.append(" AND LOGIN_ID LIKE  '%").append(userID).append("%' ");
//			list.add(userID);
		}
		// 过滤用户名称
		if (userName != null && !"".equals(userName)) {
			sql.append("AND USER_NAME LIKE  '%").append(userName).append("%' ");
//			list.add(userName);
		}
		// 过滤用户状态
		if (userState != null && !"".equals(userState)) {
			sql.append("AND USER_STATE =? ");
			list.add(userState);
		}
		// 过滤所属角色
		if (roleID != null && !"".equals(roleID)) {
			sql.append("AND T1.LOGIN_ID IN (SELECT T2.LOGIN_ID FROM USER_ROLE_MAP T2 WHERE T2.ROLE_ID =?)");
			list.add(roleID);
		}
		log.debug("--searchUserInfoList-->sql:" + sql.toString());
		try {
			user = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(sql.toString(), start, limit), UserInfoBean.class, list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->根据条件获取用户信息列表失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>searchUserInfoList:" + e.toString());
		}
		log.info("--searchUserInfoList(over)-->user:" + user);
		return user;
	}

	/**
	 * 根据输入信息查询用户列表(取全部记录)
	 * 
	 * @param userID
	 *            用户编号
	 * @param userName
	 *            用户名称
	 * @param userState
	 *            用户状态
	 * @param roleID
	 *            角色编号
	 * @return 用户信息列表
	 */
	public List<UserInfoBean> searchAllUserInfoList(String userID, String userName, String userState, String roleID) {
		log.info("--searchAllUserInfoList(start)-->userID:" + userID + ";userName:" + userName + ";userState:" + userState + ";roleID:" + roleID);
		List<UserInfoBean> user = null;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T1.LOGIN_ID,T1.USER_NAME,T1.USER_POST,T1.USER_DEPARTMENT,T1.USER_STATE,T1.USER_DOMAIN,T1.LDAP_CODE,T1.USER_CITY FROM USER_INFO T1 WHERE 1=1 ");
		List list = new ArrayList();
		// 过滤用户ID
		if (userID != null && !"".equals(userID)) {
			sql.append("AND USER_NAME LIKE  '%").append(userName).append("%' ");
//			list.add(userName);
		}
		// 过滤用户名称
		if (userName != null && !"".equals(userName)) {
			sql.append("AND USER_NAME LIKE  '%").append(userName).append("%' ");
//			list.add(userName);
		}
		// 过滤用户状态
		if (userState != null && !"".equals(userState)) {
			sql.append("AND USER_STATE =? ");
			list.add(userState);
		}
		// 过滤所属角色
		if (roleID != null && !"".equals(roleID)) {
			sql.append("AND T1.LOGIN_ID IN (SELECT T2.LOGIN_ID FROM USER_ROLE_MAP T2 WHERE T2.ROLE_ID =? )");
			list.add(roleID);
		}
		log.debug("--searchAllUserInfoList-->sql:" + sql.toString());
		try {
			user = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), UserInfoBean.class, list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->根据输入信息查询用户列表(取全部记录)失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>searchAllUserInfoList:" + e.toString());
		}
		log.info("--searchAllUserInfoList(over)-->user:" + user);
		return user;
	}

	/**
	 * 根据输入的信息创建用户
	 * 
	 * @param user
	 */
	public boolean addUser(UserInfoBean user) {
		log.info("--addUser(start)-->user:" + user);
		SimpleDateFormat sm = new SimpleDateFormat("yyyyMMddHHmm");

		StringBuffer sql = new StringBuffer();

		sql.append("INSERT INTO USER_INFO (LOGIN_ID,PASSWORD,USER_NAME,USER_POST,USER_DEPARTMENT,LDAP_CODE,USER_CITY,USER_STATE,PSW_MDF_DATE )VALUES( " + "?,?,?,?,?,?,?,?,?)");
		log.debug("--addUser-->sql:" + sql.toString());
		List list = new ArrayList();
		list.add(user.getLogin_id());
		list.add(DigestUtils.md5Hex(user.getUser_password()));
		list.add(user.getUser_name());
		list.add(user.getUser_post());
		list.add(user.getUser_department());
		list.add(user.getLdap_code());
		list.add(user.getUser_city());
		list.add("1");
		list.add(sm.format(new Date()));
		try {
			DataBaseUtil.SUNECM.update(sql.toString(), list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->根据输入的信息创建用户失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>addUser:" + e.toString());
		}
		log.info("--addUser(over)");
		return true;
	}

	/**
	 * 根据用户id来修改用户信息
	 * 
	 * @param user
	 */
	public boolean modifyUser(UserInfoBean user) {
		log.info("--modifyUser(start)-->user:" + user);
		SimpleDateFormat sm = new SimpleDateFormat("yyyyMMddHHmm");

		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE USER_INFO SET USER_NAME = ?,USER_POST = ?,USER_DEPARTMENT = ?,LDAP_CODE = ?,USER_CITY = ?,PSW_MDF_DATE=? where LOGIN_ID =?");

		List list = new ArrayList();
		list.add(user.getUser_name());
		list.add(user.getUser_post());
		list.add(user.getUser_department());
		list.add(user.getLdap_code());
		list.add(user.getUser_city());
		list.add(sm.format(new Date()));
		list.add(user.getLogin_id());

		log.debug("--modifyUser-->sql:" + sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(), list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->修改用户信息失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>modifyUser:" + e.toString());
		}
		log.info("--modifyUser(over)");
		return true;
	}

	/**
	 * 启用、禁用用户
	 * 
	 * @param userIDs
	 * @param user_state
	 */
	public boolean modifyUserState(String userIDs, String userState) {
		log.info("--modifyUserState(start)-->userIDs:" + userIDs);
		StringBuffer user_id_string = new StringBuffer();
		String[] user_id = userIDs.split(",");
		String[] params = new String[user_id.length + 1];
		params[0] = userState;
		for (int i = 0; i < user_id.length; i++) {
			user_id_string.append("?,");
			if (i > 0) {
				params[i] = user_id[i - 1];
			}
		}
		params[user_id.length] = user_id[user_id.length - 1];
		if (user_id_string.length() > 0) {
			user_id_string.deleteCharAt(user_id_string.length() - 1);
		}
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE USER_INFO SET ").append("USER_STATE = ? ");
		sql.append(" where LOGIN_ID IN (");
		sql.append(user_id_string).append(")");

		log.debug("--modifyUserState-->sql:" + sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(), params);
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->修改用户状态失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>modifyUserState:" + e.toString());
		}
		log.info("--modifyUserState(over)");
		return true;
	}

	/**
	 * 重置密码
	 * 
	 * @param password
	 * @param userID
	 * @return 是否成功
	 */
	public boolean resetUserPwd(String userID, String password) {
		log.info("userID:" + userID + ";password:***");
		SimpleDateFormat sm = new SimpleDateFormat("yyyyMMddHHmm");
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE USER_INFO SET ").append("PASSWORD = ?, PSW_MDF_DATE=?  where LOGIN_ID =? ");
		List list = new ArrayList();
		list.add(DigestUtils.md5Hex(password));
		list.add(sm.format(new Date()));
		list.add(userID);
		log.debug("--resetUserPwd-->sql:" + sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(), list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->重置密码失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>resetUserPwd:" + e.toString());
		}
		log.info("--resetUserPwd(over)");
		return true;
	}

	/**
	 * 修改密码
	 * 
	 * @param old
	 * @param newm
	 * @param userID
	 * @return 是否成功
	 */
	public int mofifyUserPwd(String old, String newm, String userID) {
		// 是否开启密码最长使用时间,为0表示开启
		if ("old".equals(old)) {
			// 存在旧密码，旧密码不正确
			return UserInfoBean.OLD_PASSWORD_FAIL;
		} else if ("weak".equals(old)) {
			// 弱密码
			return UserInfoBean.WEAK_PASSWORD_FAIL;
		} else if ("usererror".equals(old)) {
			// 用户名错误或用户被警用
			return UserInfoBean.UPDATE_USER_FAIL;
		}
		// 重置用户密码
		resetUserPwd(userID, newm);
		return UserInfoBean.UPDATE_PASSWORD_SUCCESS;
	}

	/**
	 * 获取用户已有的权限
	 * 
	 * @param userIDs
	 * @return
	 */
	public List<NodeBean> getExistsComponents(String userIDs) {
		log.info("--getExistsComponents(start)-->userIDs:" + userIDs);
		if(StringUtil.stringIsNull(userIDs)){
			return new ArrayList<NodeBean>();
		}
		List<NodeBean> permissionInfos = null;
		StringBuffer user_id_string = new StringBuffer();
		String[] user_id = userIDs.split(",");
		for (int i = 0; i < user_id.length; i++) {
			user_id_string.append("?,");
		}
		if (user_id_string.length() > 0) {
			user_id_string.deleteCharAt(user_id_string.length() - 1);
		}
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T.PERMISSION_CODE,T.PERMISSION_NAME,T.PERMISSION_TYPE FROM SYS_PERMISSION T WHERE T.PERMISSION_CODE IN (SELECT T1.PERMISSION_CODE FROM USER_PERMISSION_MAP T1 "
				+ "WHERE T1.LOGIN_ID IN (");
		sql.append(user_id_string).append("))");
		log.debug("--getExistsComponents-->sql:" + sql.toString());
		List list = new ArrayList();
		list.add(userIDs);
		try {
			permissionInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), PermissionInfoBean.class, user_id);
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->获取用户已有的权限失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>getExistsComponents:" + e.toString());
		}
		log.info("--getExistsComponents(over)-->permissionInfos:" + permissionInfos);
		return permissionInfos;
	}

	/**
	 * 获取用户没有的权限
	 * 
	 * @param userIDs
	 * @return
	 */
	public List<NodeBean> getNotExistsComponents(String userIDs) {
		log.info("--getNotExistsComponents(start)-->userIDs:" + userIDs);
		if(StringUtil.stringIsNull(userIDs)){
			return new ArrayList<NodeBean>();
		}
		List<NodeBean> permissionInfos = null;
		StringBuffer user_id_string = new StringBuffer();
		String[] user_id = userIDs.split(",");
		String[] params = new String[user_id.length * 2];
		for (int i = 0; i < user_id.length; i++) {
			user_id_string.append("?,");
			params[i] = user_id[i];
		}
		for (int i = 0; i < user_id.length; i++) {
			params[i + user_id.length] = user_id[i];
		}
		if (user_id_string.length() > 0) {
			user_id_string.deleteCharAt(user_id_string.length() - 1);
		}

		StringBuffer sql = new StringBuffer();
		sql.append(
				"SELECT T.PERMISSION_CODE,T.PERMISSION_NAME,T.PERMISSION_TYPE FROM SYS_PERMISSION T WHERE T.PERMISSION_CODE NOT IN (SELECT T1.PERMISSION_CODE FROM USER_PERMISSION_MAP T1 WHERE T1.LOGIN_ID IN (")
				.append(user_id_string).append(")) ");
		sql.append(" UNION ");
		sql.append(
				"SELECT T.PERMISSION_CODE,T.PERMISSION_NAME,T.PERMISSION_TYPE FROM SYS_PERMISSION T WHERE T.PERMISSION_CODE IN(SELECT DISTINCT  T1.PERMISSION_TYPE FROM SYS_PERMISSION T1 WHERE T1.PERMISSION_CODE NOT IN (SELECT T2.PERMISSION_CODE FROM USER_PERMISSION_MAP T2 WHERE T2.LOGIN_ID IN (")
				.append(user_id_string).append(")) ").append(" AND  T1.PERMISSION_TYPE IS NOT NULL)");
		log.debug("--getNotExistsComponents-->sql:" + sql.toString() + ",params=" + params);
		try {
			permissionInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), PermissionInfoBean.class, params);
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->获取用户没有的权限失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>getNotExistsComponents:" + e.toString());
		}
		log.info("--getNotExistsComponents(over)-->permissionInfos:" + permissionInfos);
		return permissionInfos;
	}

	/**
	 * 修改用户权限
	 * 
	 * @param userIDs
	 * @param componentIDs
	 * @return
	 */
	public boolean updateUserComponents(String userIDs, String componentIDs) {
		log.info("--updateUserComponents(start)-->userIDs:" + userIDs + ";componentIDs" + componentIDs);
		String[] user_id = userIDs.split(",");
		StringBuffer sb = new StringBuffer();
		for (int i = 0; i < user_id.length; i++) {
			sb.append("?,");
		}
		if (sb.length() > 0) {
			sb.deleteCharAt(sb.length() - 1);
		}
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM USER_PERMISSION_MAP WHERE LOGIN_ID IN(").append(sb).append(")");

		log.debug("--updateUserComponents-->sql:" + sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(), user_id);
			if ("".equals(componentIDs.trim()))
				return true;
			sql = new StringBuffer();
			String[] componentID = componentIDs.split(",");
			List list;
			for (int i = 0; i < componentID.length; i++) {
				for (int j = 0; j < user_id.length; j++) {
					sql.append("INSERT INTO USER_PERMISSION_MAP(LOGIN_ID,PERMISSION_CODE)VALUES(?,?)");
					list=new ArrayList();
					list.add(user_id[j]);
					list.add(componentID[i]);
					log.debug("--updateUserComponents-->sql:" + sql.toString());
					DataBaseUtil.SUNECM.update(sql.toString(),list.toArray());
					sql = new StringBuffer("");
				}
			}
		} catch (Exception e) {
			log.error("用户管理->修改用户权限失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>updateUserComponents:" + e.toString());
		}
		log.info("--updateUserComponents(over)");
		return true;
	}

	/**
	 * 获取用户已有的角色
	 * 
	 * @param userIDs
	 * @return
	 */
	public List<NodeBean> getUserExistRoles(String userIDs) {
		log.info("--getUserExistRoles(start)-->userIDs:" + userIDs);
		if(StringUtil.stringIsNull(userIDs)){
			return new ArrayList<NodeBean>();
		}
		List<NodeBean> roleInfos = null;
		StringBuffer user_id_string = new StringBuffer();
		String[] user_id = userIDs.split(",");
		for (int i = 0; i < user_id.length; i++) {
			user_id_string.append("?,");
		}
		if(user_id_string.length()>0){
			user_id_string.deleteCharAt(user_id_string.length()-1);
		}
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T.ROLE_ID,T.ROLE_NAME,T.ROLE_STATE,T.ROLE_DES FROM ROLE_INFO T WHERE T.ROLE_ID IN (SELECT T1.ROLE_ID FROM USER_ROLE_MAP T1 WHERE T1.LOGIN_ID IN (").append(user_id_string).append("))");
		try {
			log.debug("--getUserExistRoles-->sql:" + sql.toString());
			roleInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), RoleInfoBean.class,user_id);
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->获取用户已有的角色失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>getUserExistRoles:" + e.toString());
		}
		log.info("--getUserExistRoles(start)-->roleInfos:" + roleInfos);
		return roleInfos;
	}

	/**
	 * 获取用户没有的角色
	 * 
	 * @param user_id
	 * @return
	 */
	public List<NodeBean> getUserNotExistRoles(String userIDs) {
		log.info("--getUserNotExistRoles(start)-->userIDs:" + userIDs);
		if(StringUtil.stringIsNull(userIDs)){
			return new ArrayList<NodeBean>();
		}
		
		List<NodeBean> roleInfos = null;
		StringBuffer user_id_string = new StringBuffer();
		String[] user_id = userIDs.split(",");
		for (int i = 0; i < user_id.length; i++) {
			user_id_string.append("?,");
		}
		if(user_id_string.length()>0){
			user_id_string.deleteCharAt(user_id_string.length()-1);
		}
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT T.ROLE_ID,T.ROLE_NAME,T.ROLE_STATE,T.ROLE_DES FROM ROLE_INFO T WHERE T.ROLE_ID NOT IN (SELECT T1.ROLE_ID FROM USER_ROLE_MAP T1 WHERE T1.LOGIN_ID IN (").append(user_id_string).append("))");
		try {
			log.debug("--getUserNotExistRoles-->sql:" + sql.toString());
			roleInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), RoleInfoBean.class, user_id);
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->获取用户没有的角色失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>getUserNotExistRoles:" + e.toString());
		}
		log.info("--getUserNotExistRoles(over)-->roleInfos:" + roleInfos);
		return roleInfos;
	}

	/**
	 * 修改用户的角色
	 * 
	 * @param userIDs
	 * @param roleIDs
	 */
	public boolean updateUserRoles(String userIDs, String roleIDs) {
		log.info("--updateUserRoles(start)-->userIDs:" + userIDs + ";roleIDs:" + roleIDs);
		String[] user_id = userIDs.split(",");
		StringBuffer sb=new StringBuffer();
		for (int i = 0; i < user_id.length; i++) {
			sb.append("?,");
		}
		if(sb.length()>0){
			sb.deleteCharAt(sb.length()-1);
		}
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM USER_ROLE_MAP WHERE LOGIN_ID IN(").append(sb).append(")");
		log.debug("--updateUserRoles-->sql:" + sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(),user_id);
			sql = new StringBuffer("");
			if (roleIDs == null || "".equals(roleIDs.trim()))
				return true;
			String[] role_id = roleIDs.split(",");
			List list;
			for (int i = 0; i < role_id.length; i++) {
				for (int j = 0; j < user_id.length; j++) {
					sql.append("INSERT INTO USER_ROLE_MAP(LOGIN_ID,ROLE_ID)VALUES(?,?)");
					list=new ArrayList();
					list.add(user_id[j]);
					list.add(role_id[i]);
					log.debug("--updateUserRoles-->sql:" + sql.toString());
					DataBaseUtil.SUNECM.update(sql.toString(),list.toArray());
					sql = new StringBuffer("");
				}
			}
		} catch (Exception e) {
			log.error("用户管理->修改用户的角色失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>updateUserRoles:" + e.toString());
		}
		log.info("--updateUserRoles(over)");
		return true;
	}

	/**
	 * 获取用户对内容对象操作权限
	 * 
	 * @param user_ids
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<UserCmodelRelBean> getConferList(String user_id, int start, int limit) {
		log.info("--getConferList(start)-->user_id:" + user_id);
		List<UserCmodelRelBean> beanList = null;
		StringBuffer sql = new StringBuffer();

		sql.append("SELECT C.MODEL_CODE,C.MODEL_NAME,U.PERMISSION_CODE,U.LOGIN_ID FROM CONTENT_MODEL_SET C LEFT OUTER JOIN USER_CMODEL_REL U ON(U.MODEL_CODE=C.MODEL_CODE) AND U.LOGIN_ID = ? WHERE C.MODEL_TYPE = '0' ");
		List list = new ArrayList();
		list.add(user_id);
		log.debug("--getConferList-->sql:" + sql.toString());
		try {
			beanList = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(sql.toString(), start, limit), UserCmodelRelBean.class, list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->获取用户对内容对象操作权限失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>getConferList:" + e.toString());
		}
		log.info("--getConferList(over)-->beanList:" + beanList);
		return beanList;
	}

	/**
	 * 获取用户对内容对象操作权限(取全部记录)
	 * 
	 * @param user_ids
	 * @return
	 */
	public List<UserCmodelRelBean> getConferAllList(String user_id) {
		log.info("--getConferAllList(start)-->user_id:" + user_id);
		List<UserCmodelRelBean> beanList = null;
		StringBuffer sql = new StringBuffer();

		sql.append("SELECT C.MODEL_CODE,C.MODEL_NAME,U.PERMISSION_CODE,U.LOGIN_ID FROM CONTENT_MODEL_SET C LEFT OUTER JOIN USER_CMODEL_REL U ON(U.MODEL_CODE=C.MODEL_CODE) AND U.LOGIN_ID = ? WHERE C.MODEL_TYPE = '0' ");
			List list = new ArrayList();
		list.add(user_id);
		log.debug("--getConferAllList-->sql:" + sql.toString());
		try {
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), UserCmodelRelBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("用户管理->获取用户对内容对象操作权限(取全部记录)失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>getConferAllList:" + e.toString());
		}
		log.info("--getConferAllList(over)-->beanList:" + beanList);
		return beanList;
	}

	/**
	 * 配置内容对象操作权限
	 * 
	 * @param user_ids
	 * @param model_codes
	 * @param permission_code
	 * @return
	 */
	public boolean configConfer(String user_id, String model_codes, String permission_code) {
		log.info("--configConfer(start)-->user_id:" + user_id);
		StringBuffer sql = new StringBuffer();
		StringBuffer model_code_string = new StringBuffer();
		String[] models = model_codes.trim().split(",");

		for (int i = 0; i < models.length; i++) {
			if (i > 0)
				model_code_string.append(",");
			model_code_string.append("'").append(models[i]).append("'");
		}
		Collection<String> sqls = new ArrayList<String>();
		sql.append("DELETE FROM USER_CMODEL_REL WHERE LOGIN_ID = '").append(user_id);
		sql.append("' AND MODEL_CODE IN (").append(model_code_string).append(")");
		log.debug("--configConfer-->sql:" + sql.toString());
		sqls.add(sql.toString());
		sql = new StringBuffer();

		for (int j = 0; j < models.length; j++) {
			sql = new StringBuffer();
			sql.append("INSERT INTO USER_CMODEL_REL (LOGIN_ID,MODEL_CODE,PERMISSION_CODE)VALUES('").append(user_id).append("','").append(models[j]).append("','").append(permission_code).append("')");
			sqls.add(sql.toString());
			log.debug("--configConfer-->sql:" + sql.toString());
		}
		try {
			DataBaseUtil.SUNECM.exceTrans(sqls);
		} catch (Exception e) {
			log.error("用户管理->配置内容对象操作权限失败->" + e.toString(), e);
			throw new DBRuntimeException("UserManageDAOIMP===>configConfer:" + e.toString());
		}
		log.info("--configConfer(over)");
		return true;
	}
}
