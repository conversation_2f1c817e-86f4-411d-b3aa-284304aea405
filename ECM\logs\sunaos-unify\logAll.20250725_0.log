2025-07-25 00:00:06.516 [] [/] [http-nio-9007-exec-79] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:00:17.521 [] [/] [http-nio-9007-exec-83] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:00:28.522 [] [/] [http-nio-9007-exec-78] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:00:36.365 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:00:39.512 [] [/] [http-nio-9007-exec-74] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:00:50.512 [] [/] [http-nio-9007-exec-92] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:01:01.511 [] [/] [http-nio-9007-exec-99] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:01:12.516 [] [/] [http-nio-9007-exec-90] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:01:23.524 [] [/] [http-nio-9007-exec-82] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:01:34.516 [] [/] [http-nio-9007-exec-72] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:01:45.515 [] [/] [http-nio-9007-exec-93] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:01:56.517 [] [/] [http-nio-9007-exec-77] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:02:07.509 [] [/] [http-nio-9007-exec-87] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:02:18.520 [] [/] [http-nio-9007-exec-98] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:02:29.524 [] [/] [http-nio-9007-exec-94] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:02:40.516 [] [/] [http-nio-9007-exec-96] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:02:51.518 [] [/] [http-nio-9007-exec-69] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:03:02.510 [] [/] [http-nio-9007-exec-100] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:03:13.525 [] [/] [http-nio-9007-exec-4] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:03:24.510 [] [/] [http-nio-9007-exec-84] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:03:35.512 [] [/] [http-nio-9007-exec-97] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:03:46.520 [] [/] [http-nio-9007-exec-2] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:03:57.511 [] [/] [http-nio-9007-exec-14] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:04:08.518 [] [/] [http-nio-9007-exec-91] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:04:19.524 [] [/] [http-nio-9007-exec-7] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:04:30.511 [] [/] [http-nio-9007-exec-11] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:04:41.510 [] [/] [http-nio-9007-exec-1] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:04:52.519 [] [/] [http-nio-9007-exec-86] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:05:03.511 [] [/] [http-nio-9007-exec-3] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:05:14.518 [] [/] [http-nio-9007-exec-8] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:05:25.509 [] [/] [http-nio-9007-exec-81] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:05:36.374 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:05:36.515 [] [/] [http-nio-9007-exec-10] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:05:47.523 [] [/] [http-nio-9007-exec-25] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:05:58.523 [] [/] [http-nio-9007-exec-6] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:06:09.515 [] [/] [http-nio-9007-exec-19] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:06:20.517 [] [/] [http-nio-9007-exec-5] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:06:31.512 [] [/] [http-nio-9007-exec-15] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:06:42.524 [] [/] [http-nio-9007-exec-18] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:06:53.515 [] [/] [http-nio-9007-exec-9] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:07:04.510 [] [/] [http-nio-9007-exec-31] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:07:15.512 [] [/] [http-nio-9007-exec-27] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:07:26.515 [] [/] [http-nio-9007-exec-16] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:07:37.522 [] [/] [http-nio-9007-exec-24] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:07:48.518 [] [/] [http-nio-9007-exec-95] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:07:59.516 [] [/] [http-nio-9007-exec-20] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:08:10.510 [] [/] [http-nio-9007-exec-12] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:08:21.516 [] [/] [http-nio-9007-exec-37] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:08:32.513 [] [/] [http-nio-9007-exec-23] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:08:43.521 [] [/] [http-nio-9007-exec-30] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:08:54.524 [] [/] [http-nio-9007-exec-29] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:09:05.514 [] [/] [http-nio-9007-exec-32] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:09:16.523 [] [/] [http-nio-9007-exec-13] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:09:27.511 [] [/] [http-nio-9007-exec-36] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:09:38.518 [] [/] [http-nio-9007-exec-33] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:09:49.526 [] [/] [http-nio-9007-exec-26] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:10:00.525 [] [/] [http-nio-9007-exec-35] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:10:11.522 [] [/] [http-nio-9007-exec-22] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:10:22.514 [] [/] [http-nio-9007-exec-47] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:10:33.523 [] [/] [http-nio-9007-exec-21] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:10:36.387 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:10:44.521 [] [/] [http-nio-9007-exec-38] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:10:55.522 [] [/] [http-nio-9007-exec-39] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:11:06.513 [] [/] [http-nio-9007-exec-17] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:11:17.525 [] [/] [http-nio-9007-exec-44] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:11:28.515 [] [/] [http-nio-9007-exec-57] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:11:39.513 [] [/] [http-nio-9007-exec-42] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:11:50.520 [] [/] [http-nio-9007-exec-34] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:12:01.512 [] [/] [http-nio-9007-exec-52] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:12:12.514 [] [/] [http-nio-9007-exec-60] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:12:23.511 [] [/] [http-nio-9007-exec-40] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:12:34.523 [] [/] [http-nio-9007-exec-53] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:12:45.517 [] [/] [http-nio-9007-exec-45] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:12:56.512 [] [/] [http-nio-9007-exec-48] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:13:07.524 [] [/] [http-nio-9007-exec-46] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:13:18.522 [] [/] [http-nio-9007-exec-43] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:13:29.523 [] [/] [http-nio-9007-exec-41] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:13:40.512 [] [/] [http-nio-9007-exec-58] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:13:51.517 [] [/] [http-nio-9007-exec-50] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:14:02.510 [] [/] [http-nio-9007-exec-49] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:14:13.516 [] [/] [http-nio-9007-exec-28] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:14:24.524 [] [/] [http-nio-9007-exec-61] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:14:35.523 [] [/] [http-nio-9007-exec-73] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:14:46.521 [] [/] [http-nio-9007-exec-63] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:14:57.517 [] [/] [http-nio-9007-exec-66] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:15:08.523 [] [/] [http-nio-9007-exec-67] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:15:19.522 [] [/] [http-nio-9007-exec-71] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:15:30.512 [] [/] [http-nio-9007-exec-70] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:15:36.399 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:15:41.517 [] [/] [http-nio-9007-exec-62] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:15:52.512 [] [/] [http-nio-9007-exec-56] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:16:03.518 [] [/] [http-nio-9007-exec-51] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:16:14.517 [] [/] [http-nio-9007-exec-68] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:16:25.521 [] [/] [http-nio-9007-exec-89] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:16:36.510 [] [/] [http-nio-9007-exec-54] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:16:47.522 [] [/] [http-nio-9007-exec-64] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:16:58.510 [] [/] [http-nio-9007-exec-75] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:17:09.515 [] [/] [http-nio-9007-exec-76] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:17:20.513 [] [/] [http-nio-9007-exec-65] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:17:31.512 [] [/] [http-nio-9007-exec-55] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:17:42.519 [] [/] [http-nio-9007-exec-88] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:17:53.519 [] [/] [http-nio-9007-exec-59] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:18:04.523 [] [/] [http-nio-9007-exec-80] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:18:15.510 [] [/] [http-nio-9007-exec-85] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:18:26.511 [] [/] [http-nio-9007-exec-79] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:18:37.512 [] [/] [http-nio-9007-exec-83] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:18:48.524 [] [/] [http-nio-9007-exec-78] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:18:59.512 [] [/] [http-nio-9007-exec-74] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:19:10.512 [] [/] [http-nio-9007-exec-92] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:19:21.524 [] [/] [http-nio-9007-exec-99] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:19:32.523 [] [/] [http-nio-9007-exec-90] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:19:43.511 [] [/] [http-nio-9007-exec-82] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:19:54.514 [] [/] [http-nio-9007-exec-72] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:20:05.513 [] [/] [http-nio-9007-exec-93] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:20:16.520 [] [/] [http-nio-9007-exec-77] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:20:27.524 [] [/] [http-nio-9007-exec-87] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:20:36.401 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:20:38.514 [] [/] [http-nio-9007-exec-98] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:20:49.516 [] [/] [http-nio-9007-exec-94] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:21:00.517 [] [/] [http-nio-9007-exec-96] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:21:11.511 [] [/] [http-nio-9007-exec-69] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:21:22.510 [] [/] [http-nio-9007-exec-100] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:21:33.521 [] [/] [http-nio-9007-exec-4] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:21:44.509 [] [/] [http-nio-9007-exec-84] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:21:55.519 [] [/] [http-nio-9007-exec-97] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:22:06.511 [] [/] [http-nio-9007-exec-2] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:22:17.513 [] [/] [http-nio-9007-exec-14] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:22:28.523 [] [/] [http-nio-9007-exec-91] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:22:39.515 [] [/] [http-nio-9007-exec-7] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:22:50.523 [] [/] [http-nio-9007-exec-11] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:23:01.514 [] [/] [http-nio-9007-exec-1] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:23:12.517 [] [/] [http-nio-9007-exec-86] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:23:23.514 [] [/] [http-nio-9007-exec-3] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:23:34.524 [] [/] [http-nio-9007-exec-8] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:23:45.517 [] [/] [http-nio-9007-exec-81] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:23:56.523 [] [/] [http-nio-9007-exec-10] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:24:07.521 [] [/] [http-nio-9007-exec-25] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:24:18.523 [] [/] [http-nio-9007-exec-6] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:24:29.516 [] [/] [http-nio-9007-exec-19] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:24:40.522 [] [/] [http-nio-9007-exec-5] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:24:51.523 [] [/] [http-nio-9007-exec-15] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:25:02.512 [] [/] [http-nio-9007-exec-18] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:25:13.523 [] [/] [http-nio-9007-exec-9] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:25:24.515 [] [/] [http-nio-9007-exec-31] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:25:35.524 [] [/] [http-nio-9007-exec-27] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:25:36.403 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:25:46.510 [] [/] [http-nio-9007-exec-16] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:25:57.515 [] [/] [http-nio-9007-exec-24] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:26:08.525 [] [/] [http-nio-9007-exec-95] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:26:19.522 [] [/] [http-nio-9007-exec-20] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:26:30.521 [] [/] [http-nio-9007-exec-12] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:26:41.524 [] [/] [http-nio-9007-exec-37] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:26:52.517 [] [/] [http-nio-9007-exec-23] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:27:03.525 [] [/] [http-nio-9007-exec-30] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:27:14.512 [] [/] [http-nio-9007-exec-29] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:27:25.521 [] [/] [http-nio-9007-exec-32] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:27:36.520 [] [/] [http-nio-9007-exec-13] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:27:47.514 [] [/] [http-nio-9007-exec-36] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:27:58.511 [] [/] [http-nio-9007-exec-33] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:28:09.516 [] [/] [http-nio-9007-exec-26] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:28:20.510 [] [/] [http-nio-9007-exec-35] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:28:31.520 [] [/] [http-nio-9007-exec-22] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:28:42.525 [] [/] [http-nio-9007-exec-47] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:28:53.512 [] [/] [http-nio-9007-exec-21] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:29:04.526 [] [/] [http-nio-9007-exec-38] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:29:15.515 [] [/] [http-nio-9007-exec-39] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:29:26.514 [] [/] [http-nio-9007-exec-17] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:29:37.521 [] [/] [http-nio-9007-exec-44] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:29:48.522 [] [/] [http-nio-9007-exec-57] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:29:59.514 [] [/] [http-nio-9007-exec-42] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:30:10.522 [] [/] [http-nio-9007-exec-34] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:30:21.525 [] [/] [http-nio-9007-exec-52] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:30:32.511 [] [/] [http-nio-9007-exec-60] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:30:36.412 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:30:43.523 [] [/] [http-nio-9007-exec-40] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:30:54.513 [] [/] [http-nio-9007-exec-53] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:31:05.517 [] [/] [http-nio-9007-exec-45] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:31:16.524 [] [/] [http-nio-9007-exec-48] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:31:27.515 [] [/] [http-nio-9007-exec-46] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:31:38.525 [] [/] [http-nio-9007-exec-43] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:31:49.512 [] [/] [http-nio-9007-exec-41] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:32:00.512 [] [/] [http-nio-9007-exec-58] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:32:11.516 [] [/] [http-nio-9007-exec-50] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:32:22.524 [] [/] [http-nio-9007-exec-49] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:32:33.515 [] [/] [http-nio-9007-exec-28] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:32:44.520 [] [/] [http-nio-9007-exec-61] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:32:55.517 [] [/] [http-nio-9007-exec-73] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:33:06.525 [] [/] [http-nio-9007-exec-63] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:33:17.521 [] [/] [http-nio-9007-exec-66] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:33:28.518 [] [/] [http-nio-9007-exec-67] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:33:39.517 [] [/] [http-nio-9007-exec-71] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:33:50.519 [] [/] [http-nio-9007-exec-70] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:34:01.522 [] [/] [http-nio-9007-exec-62] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:34:12.525 [] [/] [http-nio-9007-exec-56] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:34:23.516 [] [/] [http-nio-9007-exec-51] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:34:34.522 [] [/] [http-nio-9007-exec-68] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:34:45.522 [] [/] [http-nio-9007-exec-89] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:34:56.514 [] [/] [http-nio-9007-exec-54] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:35:07.516 [] [/] [http-nio-9007-exec-64] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:35:18.521 [] [/] [http-nio-9007-exec-75] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:35:29.512 [] [/] [http-nio-9007-exec-76] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:35:36.418 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:35:40.520 [] [/] [http-nio-9007-exec-65] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:35:51.516 [] [/] [http-nio-9007-exec-55] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:36:02.512 [] [/] [http-nio-9007-exec-88] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:36:13.517 [] [/] [http-nio-9007-exec-59] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:36:24.520 [] [/] [http-nio-9007-exec-80] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:36:35.515 [] [/] [http-nio-9007-exec-85] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:36:46.524 [] [/] [http-nio-9007-exec-79] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:36:57.518 [] [/] [http-nio-9007-exec-83] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:37:08.511 [] [/] [http-nio-9007-exec-78] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:37:19.523 [] [/] [http-nio-9007-exec-74] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:37:30.520 [] [/] [http-nio-9007-exec-92] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:37:41.521 [] [/] [http-nio-9007-exec-99] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:37:52.513 [] [/] [http-nio-9007-exec-90] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:38:03.521 [] [/] [http-nio-9007-exec-82] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:38:14.515 [] [/] [http-nio-9007-exec-72] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:38:25.510 [] [/] [http-nio-9007-exec-93] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:38:36.513 [] [/] [http-nio-9007-exec-77] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:38:47.520 [] [/] [http-nio-9007-exec-87] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:38:58.511 [] [/] [http-nio-9007-exec-98] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:39:09.511 [] [/] [http-nio-9007-exec-94] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:39:20.511 [] [/] [http-nio-9007-exec-96] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:39:31.521 [] [/] [http-nio-9007-exec-69] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:39:42.523 [] [/] [http-nio-9007-exec-100] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:39:53.512 [] [/] [http-nio-9007-exec-4] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:40:04.519 [] [/] [http-nio-9007-exec-84] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:40:15.514 [] [/] [http-nio-9007-exec-97] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:40:26.515 [] [/] [http-nio-9007-exec-2] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:40:36.426 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:40:37.521 [] [/] [http-nio-9007-exec-14] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:40:48.518 [] [/] [http-nio-9007-exec-91] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:40:59.521 [] [/] [http-nio-9007-exec-7] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:41:10.525 [] [/] [http-nio-9007-exec-11] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:41:21.519 [] [/] [http-nio-9007-exec-1] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:41:32.525 [] [/] [http-nio-9007-exec-86] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:41:43.525 [] [/] [http-nio-9007-exec-3] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:41:54.521 [] [/] [http-nio-9007-exec-8] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:42:05.510 [] [/] [http-nio-9007-exec-81] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:42:16.517 [] [/] [http-nio-9007-exec-10] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:42:27.525 [] [/] [http-nio-9007-exec-25] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:42:38.520 [] [/] [http-nio-9007-exec-6] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:42:49.516 [] [/] [http-nio-9007-exec-19] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:43:00.519 [] [/] [http-nio-9007-exec-5] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:43:11.516 [] [/] [http-nio-9007-exec-15] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:43:22.516 [] [/] [http-nio-9007-exec-18] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:43:33.517 [] [/] [http-nio-9007-exec-9] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:43:44.521 [] [/] [http-nio-9007-exec-31] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:43:55.514 [] [/] [http-nio-9007-exec-27] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:44:06.513 [] [/] [http-nio-9007-exec-16] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:44:17.521 [] [/] [http-nio-9007-exec-24] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:44:28.514 [] [/] [http-nio-9007-exec-95] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:44:39.522 [] [/] [http-nio-9007-exec-20] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:44:50.514 [] [/] [http-nio-9007-exec-12] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:45:01.521 [] [/] [http-nio-9007-exec-37] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:45:12.512 [] [/] [http-nio-9007-exec-23] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:45:23.513 [] [/] [http-nio-9007-exec-30] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:45:34.513 [] [/] [http-nio-9007-exec-29] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:45:36.440 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:45:45.517 [] [/] [http-nio-9007-exec-32] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:45:56.519 [] [/] [http-nio-9007-exec-13] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:46:07.517 [] [/] [http-nio-9007-exec-36] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:46:18.513 [] [/] [http-nio-9007-exec-33] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:46:29.519 [] [/] [http-nio-9007-exec-26] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:46:40.524 [] [/] [http-nio-9007-exec-35] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:46:51.516 [] [/] [http-nio-9007-exec-22] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:47:02.524 [] [/] [http-nio-9007-exec-47] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:47:13.512 [] [/] [http-nio-9007-exec-21] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:47:24.520 [] [/] [http-nio-9007-exec-38] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:47:35.517 [] [/] [http-nio-9007-exec-39] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:47:46.524 [] [/] [http-nio-9007-exec-17] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:47:57.517 [] [/] [http-nio-9007-exec-44] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:48:08.520 [] [/] [http-nio-9007-exec-57] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:48:19.521 [] [/] [http-nio-9007-exec-42] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:48:30.510 [] [/] [http-nio-9007-exec-34] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:48:41.517 [] [/] [http-nio-9007-exec-52] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:48:52.523 [] [/] [http-nio-9007-exec-60] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:49:03.523 [] [/] [http-nio-9007-exec-40] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:49:14.517 [] [/] [http-nio-9007-exec-53] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:49:25.524 [] [/] [http-nio-9007-exec-45] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:49:36.514 [] [/] [http-nio-9007-exec-48] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:49:47.521 [] [/] [http-nio-9007-exec-46] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:49:58.523 [] [/] [http-nio-9007-exec-43] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:50:09.522 [] [/] [http-nio-9007-exec-41] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:50:20.522 [] [/] [http-nio-9007-exec-58] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:50:31.521 [] [/] [http-nio-9007-exec-50] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:50:36.448 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:50:42.515 [] [/] [http-nio-9007-exec-49] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:50:53.525 [] [/] [http-nio-9007-exec-28] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:51:04.517 [] [/] [http-nio-9007-exec-61] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:51:15.521 [] [/] [http-nio-9007-exec-73] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:51:26.512 [] [/] [http-nio-9007-exec-63] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:51:37.518 [] [/] [http-nio-9007-exec-66] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:51:48.520 [] [/] [http-nio-9007-exec-67] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:51:59.518 [] [/] [http-nio-9007-exec-71] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:52:10.511 [] [/] [http-nio-9007-exec-70] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:52:21.513 [] [/] [http-nio-9007-exec-62] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:52:32.518 [] [/] [http-nio-9007-exec-56] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:52:43.513 [] [/] [http-nio-9007-exec-51] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:52:54.509 [] [/] [http-nio-9007-exec-68] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:53:05.517 [] [/] [http-nio-9007-exec-89] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:53:16.522 [] [/] [http-nio-9007-exec-54] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:53:27.511 [] [/] [http-nio-9007-exec-64] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:53:38.516 [] [/] [http-nio-9007-exec-75] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:53:49.516 [] [/] [http-nio-9007-exec-76] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:54:00.518 [] [/] [http-nio-9007-exec-65] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:54:11.511 [] [/] [http-nio-9007-exec-55] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:54:22.512 [] [/] [http-nio-9007-exec-88] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:54:33.521 [] [/] [http-nio-9007-exec-59] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:54:44.512 [] [/] [http-nio-9007-exec-80] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:54:55.524 [] [/] [http-nio-9007-exec-85] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:55:06.510 [] [/] [http-nio-9007-exec-79] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:55:17.517 [] [/] [http-nio-9007-exec-83] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:55:28.521 [] [/] [http-nio-9007-exec-78] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:55:36.455 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:55:39.523 [] [/] [http-nio-9007-exec-74] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:55:50.521 [] [/] [http-nio-9007-exec-92] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:56:01.523 [] [/] [http-nio-9007-exec-99] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:56:12.521 [] [/] [http-nio-9007-exec-90] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:56:23.524 [] [/] [http-nio-9007-exec-82] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:56:34.522 [] [/] [http-nio-9007-exec-72] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:56:45.521 [] [/] [http-nio-9007-exec-93] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:56:56.511 [] [/] [http-nio-9007-exec-77] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:57:07.521 [] [/] [http-nio-9007-exec-87] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:57:18.514 [] [/] [http-nio-9007-exec-98] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:57:29.525 [] [/] [http-nio-9007-exec-94] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:57:40.517 [] [/] [http-nio-9007-exec-96] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:57:51.519 [] [/] [http-nio-9007-exec-69] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:58:02.517 [] [/] [http-nio-9007-exec-100] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:58:13.524 [] [/] [http-nio-9007-exec-4] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:58:24.524 [] [/] [http-nio-9007-exec-84] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:58:35.518 [] [/] [http-nio-9007-exec-97] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:58:46.516 [] [/] [http-nio-9007-exec-2] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:58:57.520 [] [/] [http-nio-9007-exec-14] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:59:08.511 [] [/] [http-nio-9007-exec-91] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:59:19.526 [] [/] [http-nio-9007-exec-7] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:59:30.522 [] [/] [http-nio-9007-exec-11] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:59:41.515 [] [/] [http-nio-9007-exec-1] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 00:59:52.516 [] [/] [http-nio-9007-exec-86] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:00:03.509 [] [/] [http-nio-9007-exec-3] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:00:14.521 [] [/] [http-nio-9007-exec-8] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:00:25.524 [] [/] [http-nio-9007-exec-81] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:00:36.466 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 01:00:36.515 [] [/] [http-nio-9007-exec-10] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:00:47.516 [] [/] [http-nio-9007-exec-25] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:00:58.519 [] [/] [http-nio-9007-exec-6] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:01:09.521 [] [/] [http-nio-9007-exec-19] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:01:20.517 [] [/] [http-nio-9007-exec-5] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:01:31.522 [] [/] [http-nio-9007-exec-15] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:01:42.524 [] [/] [http-nio-9007-exec-18] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:01:53.519 [] [/] [http-nio-9007-exec-9] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:02:04.511 [] [/] [http-nio-9007-exec-31] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:02:15.514 [] [/] [http-nio-9007-exec-27] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:02:26.516 [] [/] [http-nio-9007-exec-16] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:02:37.518 [] [/] [http-nio-9007-exec-24] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:02:48.520 [] [/] [http-nio-9007-exec-95] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:02:59.514 [] [/] [http-nio-9007-exec-20] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:03:10.521 [] [/] [http-nio-9007-exec-12] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:03:21.522 [] [/] [http-nio-9007-exec-37] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:03:32.517 [] [/] [http-nio-9007-exec-23] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:03:43.525 [] [/] [http-nio-9007-exec-30] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:03:54.516 [] [/] [http-nio-9007-exec-29] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:04:05.515 [] [/] [http-nio-9007-exec-32] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:04:16.518 [] [/] [http-nio-9007-exec-13] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:04:27.519 [] [/] [http-nio-9007-exec-36] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:04:38.513 [] [/] [http-nio-9007-exec-33] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:04:49.513 [] [/] [http-nio-9007-exec-26] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:05:00.516 [] [/] [http-nio-9007-exec-35] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:05:11.523 [] [/] [http-nio-9007-exec-22] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:05:22.521 [] [/] [http-nio-9007-exec-47] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:05:33.509 [] [/] [http-nio-9007-exec-21] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:05:36.468 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 01:05:44.523 [] [/] [http-nio-9007-exec-38] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:05:55.517 [] [/] [http-nio-9007-exec-39] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:06:06.522 [] [/] [http-nio-9007-exec-17] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:06:17.515 [] [/] [http-nio-9007-exec-44] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:06:28.516 [] [/] [http-nio-9007-exec-57] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:06:39.522 [] [/] [http-nio-9007-exec-42] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:06:50.521 [] [/] [http-nio-9007-exec-34] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:07:01.516 [] [/] [http-nio-9007-exec-52] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:07:12.519 [] [/] [http-nio-9007-exec-60] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:07:23.516 [] [/] [http-nio-9007-exec-40] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:07:34.522 [] [/] [http-nio-9007-exec-53] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:07:45.524 [] [/] [http-nio-9007-exec-45] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:07:56.524 [] [/] [http-nio-9007-exec-48] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:08:07.514 [] [/] [http-nio-9007-exec-46] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:08:18.511 [] [/] [http-nio-9007-exec-43] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:08:29.516 [] [/] [http-nio-9007-exec-41] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:08:40.519 [] [/] [http-nio-9007-exec-58] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:08:51.522 [] [/] [http-nio-9007-exec-50] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:09:02.519 [] [/] [http-nio-9007-exec-49] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:09:13.522 [] [/] [http-nio-9007-exec-28] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:09:24.509 [] [/] [http-nio-9007-exec-61] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:09:35.640 [] [/] [http-nio-9007-exec-73] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1」webSocket发送消息：pong
2025-07-25 01:09:35.663 [] [/] [http-nio-9007-exec-63] INFO  c.s.c.I.s.w.WebSocketSessionUtils - admin_0b34efd8-9fd2-4986-abef-1e6cd9ceeef4_1 断开Websocket连接
2025-07-25 01:09:35.995 [] [8e9d85f1bebdfc78/3f6ce966ffa64fdf] [http-nio-9007-exec-67] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:35.996 [] [8e9d85f1bebdfc78/3f6ce966ffa64fdf] [http-nio-9007-exec-67] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: sysPassRegular(String)
2025-07-25 01:09:36.000 [] [8e9d85f1bebdfc78/3f6ce966ffa64fdf] [http-nio-9007-exec-67] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 0
2025-07-25 01:09:36.004 [] [8e9d85f1bebdfc78/719b7e57a18d6f48] [http-nio-9007-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:35 操作结束时间: 2025-07-25 01:09:36!总共花费时间: 23 毫秒！
2025-07-25 01:09:36.011 [] [8e9d85f1bebdfc78/340d856d2b4e49b1] [http-nio-9007-exec-71] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:36.012 [] [8e9d85f1bebdfc78/340d856d2b4e49b1] [http-nio-9007-exec-71] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: bankNo(String)
2025-07-25 01:09:36.014 [] [8e9d85f1bebdfc78/340d856d2b4e49b1] [http-nio-9007-exec-71] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:36.017 [] [8e9d85f1bebdfc78/4b599dee38473163] [http-nio-9007-exec-71] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:36 操作结束时间: 2025-07-25 01:09:36!总共花费时间: 6 毫秒！
2025-07-25 01:09:36.022 [] [8e9d85f1bebdfc78/2dfea17a3fe5352e] [http-nio-9007-exec-70] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:36.023 [] [8e9d85f1bebdfc78/2dfea17a3fe5352e] [http-nio-9007-exec-70] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: systemNo(String)
2025-07-25 01:09:36.026 [] [8e9d85f1bebdfc78/2dfea17a3fe5352e] [http-nio-9007-exec-70] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:36.028 [] [8e9d85f1bebdfc78/2fb43ed625d5774e] [http-nio-9007-exec-70] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:36 操作结束时间: 2025-07-25 01:09:36!总共花费时间: 6 毫秒！
2025-07-25 01:09:36.032 [] [8e9d85f1bebdfc78/f8bd3429d1ac46a4] [http-nio-9007-exec-62] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:36.032 [] [8e9d85f1bebdfc78/f8bd3429d1ac46a4] [http-nio-9007-exec-62] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: projectNo(String)
2025-07-25 01:09:36.036 [] [8e9d85f1bebdfc78/f8bd3429d1ac46a4] [http-nio-9007-exec-62] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:36.038 [] [8e9d85f1bebdfc78/74e9dc866a988f8f] [http-nio-9007-exec-62] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:36 操作结束时间: 2025-07-25 01:09:36!总共花费时间: 6 毫秒！
2025-07-25 01:09:36.046 [] [8e9d85f1bebdfc78/445ca17ef703d7af] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:36.046 [] [8e9d85f1bebdfc78/445ca17ef703d7af] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: LOGIN_SHOW_BANK(String)
2025-07-25 01:09:36.048 [] [8e9d85f1bebdfc78/445ca17ef703d7af] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:36.050 [] [8e9d85f1bebdfc78/c0a2b7ef628b7109] [http-nio-9007-exec-56] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:36 操作结束时间: 2025-07-25 01:09:36!总共花费时间: 4 毫秒！
2025-07-25 01:09:36.054 [] [8e9d85f1bebdfc78/b0a998f93bb9a3f6] [http-nio-9007-exec-51] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:36.054 [] [8e9d85f1bebdfc78/b0a998f93bb9a3f6] [http-nio-9007-exec-51] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: TIME_ERROE_RANGE(String)
2025-07-25 01:09:36.057 [] [8e9d85f1bebdfc78/b0a998f93bb9a3f6] [http-nio-9007-exec-51] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:36.059 [] [8e9d85f1bebdfc78/9a2f583ed02ea479] [http-nio-9007-exec-51] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:36 操作结束时间: 2025-07-25 01:09:36!总共花费时间: 5 毫秒！
2025-07-25 01:09:37.970 [OrganNo_null_UserNo_null] [94419d0b8645632f/fd05f2afd09a7dd2] [http-nio-9007-exec-68] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-25 01:09:37.997 [] [94419d0b8645632f/87a5818614eeaeb7] [http-nio-9007-exec-89] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:37.998 [] [94419d0b8645632f/87a5818614eeaeb7] [http-nio-9007-exec-89] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: IP_ADDRESS(String)
2025-07-25 01:09:38.002 [] [94419d0b8645632f/87a5818614eeaeb7] [http-nio-9007-exec-89] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:38.004 [] [94419d0b8645632f/dbecdd104929e5a0] [http-nio-9007-exec-89] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:37 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 22 毫秒！
2025-07-25 01:09:38.038 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-25 01:09:38.039 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-25 01:09:38.039 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-25 01:09:38.041 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-25 01:09:38.042 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-25 01:09:38.042 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-25 01:09:38.044 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-25 01:09:38.045 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-25 01:09:38.045 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-25 01:09:38.047 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-25 01:09:38.047 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-25 01:09:38.047 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-25 01:09:38.048 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-25 01:09:38.051 [] [94419d0b8645632f/71500d6c3f03f8c3] [http-nio-9007-exec-54] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:38.052 [] [94419d0b8645632f/71500d6c3f03f8c3] [http-nio-9007-exec-54] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: OVERDATE(String)
2025-07-25 01:09:38.054 [] [94419d0b8645632f/71500d6c3f03f8c3] [http-nio-9007-exec-54] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:38.056 [] [94419d0b8645632f/d4b67fcc2ba74877] [http-nio-9007-exec-54] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 5 毫秒！
2025-07-25 01:09:38.058 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-25 01:09:38.058 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-25 01:09:38.059 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-25 01:09:38.059 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-25 01:09:38.062 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-25 01:09:38.063 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-25 01:09:38.063 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-25 01:09:38.065 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-25 01:09:38.065 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-25 01:09:38.065 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-25 01:09:38.067 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-25 01:09:38.077 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-25 01:09:38.078 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-25 01:09:38.090 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-25 01:09:38.098 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-25 01:09:38.098 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-25 01:09:38.104 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-25 01:09:38.115 [OrganNo_null_UserNo_null] [94419d0b8645632f/fd05f2afd09a7dd2] [http-nio-9007-exec-68] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
2025-07-25 01:09:38.116 [] [94419d0b8645632f/fd05f2afd09a7dd2] [http-nio-9007-exec-68] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 登陆控制 操作方法: 登陆验证!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 65 毫秒！
2025-07-25 01:09:38.235 [] [54fe50a01f887bb8/df65ea5dd605da05] [http-nio-9007-exec-64] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作申请模块 操作方法: 查询菜单页面审核方式配置!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 61 毫秒！
2025-07-25 01:09:38.284 [] [2d5f4808100d850e/916bd0e976403a4a] [http-nio-9007-exec-75] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询菜单 操作方法: 查询菜单!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 110 毫秒！
2025-07-25 01:09:38.370 [] [f9ded421db60f477/bcfc2624bb580946] [http-nio-9007-exec-59] DEBUG c.s.a.u.d.E.queryExternalKeys - ==>  Preparing: select external_key, external_desc from sm_external_data_tb where bank_no = ?
2025-07-25 01:09:38.372 [] [f9ded421db60f477/bcfc2624bb580946] [http-nio-9007-exec-59] DEBUG c.s.a.u.d.E.queryExternalKeys - ==> Parameters: SUNYARD(String)
2025-07-25 01:09:38.379 [] [f9ded421db60f477/bcfc2624bb580946] [http-nio-9007-exec-59] DEBUG c.s.a.u.d.E.queryExternalKeys - <==      Total: 21
2025-07-25 01:09:38.401 [] [10198646b2596ffd/aa8ec9e498c9ba4a] [http-nio-9007-exec-85] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:38.406 [] [10198646b2596ffd/aa8ec9e498c9ba4a] [http-nio-9007-exec-85] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: limitUserNum(String)
2025-07-25 01:09:38.415 [] [10198646b2596ffd/aa8ec9e498c9ba4a] [http-nio-9007-exec-85] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:38.424 [] [10198646b2596ffd/28e68cb8039805ca] [http-nio-9007-exec-85] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 37 毫秒！
2025-07-25 01:09:38.431 [] [f9ded421db60f477/1e99a4b740efba8d] [http-nio-9007-exec-59] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"keys":[
			{
				"external_key":"ELEM_NO",
				"external_desc":"要素编号"
			},
			{
				"external_key":"USER_NO",
				"external_desc":"用户名"
			},
			{
				"external_key":"ROLE_NO",
				"external_desc":"岗位号"
			},
			{
				"external_key":"EXTERNAL_SYSTEM_NO",
				"external_desc":"外系统编号"
			},
			{
				"external_key":"MENU_ID",
				"external_desc":"监控报表菜单ID"
			},
			{
				"external_key":"SERVER_ID",
				"external_desc":"应用服务ID"
			},
			{
				"external_key":"ECM_ID",
				"external_desc":"ecm服务id"
			},
			{
				"external_key":"SYS_ID",
				"external_desc":"已启用对外系统"
			},
			{
				"external_key":"ALL_SYS_ID",
				"external_desc":"所有对外系统"
			},
			{
				"external_key":"TD_NO",
				"external_desc":"已启用对外接口"
			},
			{
				"external_key":"ALL_TD_NO",
				"external_desc":"所有对外接口"
			},
			{
				"external_key":"ERROR_CODE",
				"external_desc":"错误代码"
			},
			{
				"external_key":"CHAN_ID",
				"external_desc":"渠道号"
			},
			{
				"external_key":"MENU_ID_ALL",
				"external_desc":"菜单树"
			},
			{
				"external_key":"SCHEDULE_DATA",
				"external_desc":"定时服务列表"
			},
			{
				"external_key":"EVNET_TYPE_NO",
				"external_desc":"事件类型"
			},
			{
				"external_key":"SYSPARAM_SON",
				"external_desc":"系统参数子级"
			},
			{
				"external_key":"CUSTOM_ORGAN_NO",
				"external_desc":"自定义模块机构"
			},
			{
				"external_key":"FLOW_ID",
				"external_desc":"流程名称"
			},
			{
				"external_key":"MODULE_ID",
				"external_desc":"流程模板"
			},
			{
				"external_key":"organ_no",
				"external_desc":"机构号"
			}
		]
	},
	"retMsg":"外表key集合查询成功"
}
2025-07-25 01:09:38.432 [] [f9ded421db60f477/1e99a4b740efba8d] [http-nio-9007-exec-59] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 外表数据源配置 操作方法: 查询外表查询所有查询keys!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 47 毫秒！
2025-07-25 01:09:38.479 [] [65a02949177d5a1a/7605678bff36b284] [http-nio-9007-exec-76] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询操作 操作方法: 机构数据管理!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 15 毫秒！
2025-07-25 01:09:38.502 [] [d18afc5136f77373/ebf0ed8ab3c9d65a] [http-nio-9007-exec-55] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 角色管理 操作方法: 查询所有启用角色信息，包括机构级别!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 2 毫秒！
2025-07-25 01:09:38.547 [] [e9cffca75449d157/c0f2d6ee5d404e4d] [http-nio-9007-exec-79] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统参数管理 操作方法: 同步参数!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 46 毫秒！
2025-07-25 01:09:38.584 [] [989cd300c2f1fbf6/4fe2969c504a0feb] [http-nio-9007-exec-78] INFO  c.s.c.I.s.w.WebSocketHandshakeInterceptor - 用户号:admin 用户标识: 1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66 登录终端类型: 1 尝试Websocket连接:localhost:9007 单点登录标志：0
2025-07-25 01:09:38.585 [] [989cd300c2f1fbf6/4fe2969c504a0feb] [http-nio-9007-exec-78] DEBUG c.s.c.I.s.w.WebSocketHandshakeInterceptor - 握手后执行
2025-07-25 01:09:38.587 [] [/] [http-nio-9007-exec-78] DEBUG c.s.c.I.s.w.WebSocketSaveCache - 用户admin未启用单点登录
2025-07-25 01:09:38.588 [] [/] [http-nio-9007-exec-78] INFO  c.s.c.I.s.w.WebSocketSessionUtils - admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1 加入Websocket连接
2025-07-25 01:09:38.594 [] [8e600d79e491f112/a144c7333a5621d0] [http-nio-9007-exec-83] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 数据字典 操作方法: 数据字典查询!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 94 毫秒！
2025-07-25 01:09:38.676 [] [34c0506fa21315fe/8684eb8dbd32c294] [http-nio-9007-exec-88] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询权限机构 操作方法: 机构数据管理!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 176 毫秒！
2025-07-25 01:09:38.729 [] [0ddaecd1640633a5/ffcb5af6c182b9ec] [http-nio-9007-exec-80] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询菜单 操作方法: 查询菜单!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 229 毫秒！
2025-07-25 01:09:38.811 [] [f73d07217e043963/0b589641a992552e] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:38.813 [] [f73d07217e043963/0b589641a992552e] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: HOME_SHOW_USER(String)
2025-07-25 01:09:38.819 [] [f73d07217e043963/0b589641a992552e] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 0
2025-07-25 01:09:38.822 [] [f73d07217e043963/4588a28b4f23a19c] [http-nio-9007-exec-72] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 3 毫秒！
2025-07-25 01:09:38.824 [] [2588e0f81694b4ee/a112e5afa248d11b] [http-nio-9007-exec-93] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:38.825 [] [2588e0f81694b4ee/a112e5afa248d11b] [http-nio-9007-exec-93] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: SYSMSG_ENABLE_MODULE(String)
2025-07-25 01:09:38.827 [] [f73d07217e043963/8c7feed10fcd86e5] [http-nio-9007-exec-92] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统参数管理 操作方法: 从服务端内存获取系统参数对象!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 8 毫秒！
2025-07-25 01:09:38.827 [] [552eab5fca73728f/fb1d925ef6c3ccab] [http-nio-9007-exec-90] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 用户信息 操作方法: 查询用户信息!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 8 毫秒！
2025-07-25 01:09:38.827 [] [2588e0f81694b4ee/a112e5afa248d11b] [http-nio-9007-exec-93] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:38.840 [] [2588e0f81694b4ee/5f8e762b581b89b6] [http-nio-9007-exec-93] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 21 毫秒！
2025-07-25 01:09:38.877 [] [6868a4e2713dc592/532c94dc3d6bbd67] [http-nio-9007-exec-74] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作首页显示内容 操作方法: 查询当前登录用户主页信息!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 0 毫秒！
2025-07-25 01:09:38.882 [] [2588e0f81694b4ee/7d38667d11ed6433] [http-nio-9007-exec-99] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统消息 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 5 毫秒！
2025-07-25 01:09:38.888 [] [e1ff835d0d19dfe3/44fe32818f7ee7cb] [http-nio-9007-exec-82] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统链接 操作方法: 系统链接!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 11 毫秒！
2025-07-25 01:09:38.888 [] [38434528858236fd/ae5a01650b0ca7e6] [http-nio-9007-exec-77] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 用户信息 操作方法: 查询首页头像信息!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 11 毫秒！
2025-07-25 01:09:38.909 [] [a6e8c9069da5170e/9afccb7b6ce0cdbb] [http-nio-9007-exec-100] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:38.910 [] [a6e8c9069da5170e/9afccb7b6ce0cdbb] [http-nio-9007-exec-100] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: WATERMARK_PC(String)
2025-07-25 01:09:38.912 [] [a6e8c9069da5170e/9afccb7b6ce0cdbb] [http-nio-9007-exec-100] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:38.917 [] [a6e8c9069da5170e/3b965a3506cca267] [http-nio-9007-exec-100] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 7 毫秒！
2025-07-25 01:09:38.922 [] [d871e83985338944/8fac25235d54235e] [http-nio-9007-exec-87] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 外表数据源配置 操作方法: 查询对应外表数据!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 12 毫秒！
2025-07-25 01:09:38.925 [] [a6e8c9069da5170e/dbf33f0fca3b1eb9] [http-nio-9007-exec-94] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统参数管理 操作方法: 从服务端内存获取系统参数对象!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 15 毫秒！
2025-07-25 01:09:38.926 [] [96cab0830cd99219/f976a8e9177233c2] [http-nio-9007-exec-98] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 外表数据源配置 操作方法: 查询对应外表数据!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 16 毫秒！
2025-07-25 01:09:38.976 [] [10198646b2596ffd/c8cfcc313e963ecb] [http-nio-9007-exec-65] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 用户信息 操作方法: 查询所有用户信息!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:38 操作结束时间: 2025-07-25 01:09:38!总共花费时间: 66 毫秒！
2025-07-25 01:09:39.015 [] [11cd55278ab86c24/91bac355a943e417] [http-nio-9007-exec-7] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:39.016 [] [11cd55278ab86c24/91bac355a943e417] [http-nio-9007-exec-7] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: IMP_NDAY(String)
2025-07-25 01:09:39.017 [] [7894dde1163510b0/bdf9647d71935026] [http-nio-9007-exec-96] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统参数管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 1 毫秒！
2025-07-25 01:09:39.021 [] [11cd55278ab86c24/91bac355a943e417] [http-nio-9007-exec-7] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 0
2025-07-25 01:09:39.022 [] [9932ceb63e7053ff/4e27e98b5aaef339] [http-nio-9007-exec-11] DEBUG c.s.a.u.d.S.getParamValueByKey - ==>  Preparing: select a.param_item,a.param_value from sm_sysparameter_tb a where 1 = 1 and a.param_item = ?
2025-07-25 01:09:39.022 [] [9932ceb63e7053ff/4e27e98b5aaef339] [http-nio-9007-exec-11] DEBUG c.s.a.u.d.S.getParamValueByKey - ==> Parameters: SCROLL_TYPE(String)
2025-07-25 01:09:39.023 [] [11cd55278ab86c24/8ab40c1bf0e6a52e] [http-nio-9007-exec-7] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 7 毫秒！
2025-07-25 01:09:39.025 [] [9932ceb63e7053ff/4e27e98b5aaef339] [http-nio-9007-exec-11] DEBUG c.s.a.u.d.S.getParamValueByKey - <==      Total: 1
2025-07-25 01:09:39.025 [] [49fedf3d9d759ed3/6563c7fe323d54a9] [http-nio-9007-exec-91] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作首页显示内容 操作方法: 首页查询常用模块!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 9 毫秒！
2025-07-25 01:09:39.028 [] [9932ceb63e7053ff/a5a66040d3fd1f6d] [http-nio-9007-exec-11] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 参数设置 操作方法: 根据参数键名获取对应的参数值!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 12 毫秒！
2025-07-25 01:09:39.032 [] [9932ceb63e7053ff/e83c67ab1df503c8] [http-nio-9007-exec-97] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统参数管理 操作方法: 从服务端内存获取系统参数对象!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 16 毫秒！
2025-07-25 01:09:39.043 [] [11cd55278ab86c24/cc0900175a775d66] [http-nio-9007-exec-14] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统消息 操作方法: 查询重要消息!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 9 毫秒！
2025-07-25 01:09:39.062 [] [906eded6fe93d427/787ea46ef45fab84] [http-nio-9007-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 节假日信息 操作方法: 查询节假日!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 14 毫秒！
2025-07-25 01:09:39.077 [] [0f734b595fdacb7c/46948be4e3b1a28a] [http-nio-9007-exec-84] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统消息配置 操作方法: 查询首页展示!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 29 毫秒！
2025-07-25 01:09:39.113 [] [2538237e383aa665/84cc37108b6b9682] [http-nio-9007-exec-2] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 公告管理操作 操作方法: 首页展示公告信息列表查询!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 65 毫秒！
2025-07-25 01:09:39.197 [] [ce88cf38a88baf93/9cc331f10c9d25c7] [http-nio-9007-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 便签管理 操作方法: 查询指定时间区间内的便签状态!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 14 毫秒！
2025-07-25 01:09:39.200 [] [f884c117c5e67b8e/48f66bd77e3b11d4] [http-nio-9007-exec-8] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统消息 操作方法: 查询系统消息对应分类字段!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 17 毫秒！
2025-07-25 01:09:39.223 [] [89e7d83244a7f3e6/d88231533919abb1] [http-nio-9007-exec-81] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 用户信息 操作方法: 查询用户信息!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 21 毫秒！
2025-07-25 01:09:39.289 [] [7f04447ca99639f4/b659a89e03e11c32] [http-nio-9007-exec-25] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统消息配置 操作方法: 查询系统消息展示配置!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 17 毫秒！
2025-07-25 01:09:39.297 [] [3d0d34713cb66cde/f760c845e109d696] [http-nio-9007-exec-10] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统消息 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 25 毫秒！
2025-07-25 01:09:39.319 [] [e2f3699d803a250e/a8f74be517f7de15] [http-nio-9007-exec-6] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统消息 操作方法: 查询待办事项!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:39 操作结束时间: 2025-07-25 01:09:39!总共花费时间: 13 毫秒！
2025-07-25 01:09:42.852 [] [4f6d30e09ac9acde/b4893ffe535b5613] [http-nio-9007-exec-24] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:42 操作结束时间: 2025-07-25 01:09:42!总共花费时间: 3 毫秒！
2025-07-25 01:09:42.879 [] [4f6d30e09ac9acde/51ec61bf75018bc9] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==>  Preparing: select * from sm_organ_tb
2025-07-25 01:09:42.879 [] [4f6d30e09ac9acde/51ec61bf75018bc9] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==> Parameters: 
2025-07-25 01:09:42.903 [] [4f6d30e09ac9acde/51ec61bf75018bc9] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - <==      Total: 881
2025-07-25 01:09:42.920 [] [4f6d30e09ac9acde/f0a069e8ad1df392] [http-nio-9007-exec-95] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 门户外系统调用 操作方法: 根据指定条件获取指定机构信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:42 操作结束时间: 2025-07-25 01:09:42!总共花费时间: 42 毫秒！
2025-07-25 01:09:42.935 [] [4f6d30e09ac9acde/7a2c9cdf75cb89cc] [http-nio-9007-exec-20] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==>  Preparing: select * from sm_organ_tb
2025-07-25 01:09:42.936 [] [4f6d30e09ac9acde/7a2c9cdf75cb89cc] [http-nio-9007-exec-20] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==> Parameters: 
2025-07-25 01:09:42.960 [] [4f6d30e09ac9acde/7a2c9cdf75cb89cc] [http-nio-9007-exec-20] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - <==      Total: 881
2025-07-25 01:09:42.976 [] [4f6d30e09ac9acde/230735cc061a278f] [http-nio-9007-exec-20] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 门户外系统调用 操作方法: 根据指定条件获取指定机构信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:42 操作结束时间: 2025-07-25 01:09:42!总共花费时间: 41 毫秒！
2025-07-25 01:09:42.991 [] [4f6d30e09ac9acde/d7ff768b56ec155c] [http-nio-9007-exec-12] DEBUG c.s.a.u.d.U.selectRightOrganNoByUser - ==>  Preparing: select ORGAN_NO from SM_USER_ORGAN_TB where USER_NO = ?
2025-07-25 01:09:42.991 [] [4f6d30e09ac9acde/d7ff768b56ec155c] [http-nio-9007-exec-12] DEBUG c.s.a.u.d.U.selectRightOrganNoByUser - ==> Parameters: admin(String)
2025-07-25 01:09:42.996 [] [4f6d30e09ac9acde/d7ff768b56ec155c] [http-nio-9007-exec-12] DEBUG c.s.a.u.d.U.selectRightOrganNoByUser - <==      Total: 879
2025-07-25 01:09:42.998 [] [4f6d30e09ac9acde/29fc088b376876d5] [http-nio-9007-exec-12] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 门户外系统调用 操作方法: 获取指定用户权限机构!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:42 操作结束时间: 2025-07-25 01:09:42!总共花费时间: 7 毫秒！
2025-07-25 01:09:43.034 [] [4f6d30e09ac9acde/3fcde0b9739bd354] [http-nio-9007-exec-37] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 3 毫秒！
2025-07-25 01:09:43.103 [] [6055a84d5d734375/2bda62aab3050da7] [http-nio-9007-exec-23] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 3 毫秒！
2025-07-25 01:09:43.217 [] [6055a84d5d734375/57e366e66fe27621] [http-nio-9007-exec-30] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 1 毫秒！
2025-07-25 01:09:43.340 [] [d08a3c4595d31e5c/2966574534ad09c1] [http-nio-9007-exec-29] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 2 毫秒！
2025-07-25 01:09:43.353 [] [d08a3c4595d31e5c/da1cbb04c0edd441] [http-nio-9007-exec-32] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==>  Preparing: select * from sm_organ_tb
2025-07-25 01:09:43.354 [] [d08a3c4595d31e5c/da1cbb04c0edd441] [http-nio-9007-exec-32] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==> Parameters: 
2025-07-25 01:09:43.383 [] [d08a3c4595d31e5c/da1cbb04c0edd441] [http-nio-9007-exec-32] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - <==      Total: 881
2025-07-25 01:09:43.398 [] [d08a3c4595d31e5c/6b0ae89dbf95d87a] [http-nio-9007-exec-32] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 门户外系统调用 操作方法: 根据指定条件获取指定机构信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 45 毫秒！
2025-07-25 01:09:43.412 [] [d08a3c4595d31e5c/1685f184e069f820] [http-nio-9007-exec-13] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==>  Preparing: select * from sm_organ_tb
2025-07-25 01:09:43.412 [] [d08a3c4595d31e5c/1685f184e069f820] [http-nio-9007-exec-13] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==> Parameters: 
2025-07-25 01:09:43.435 [] [d08a3c4595d31e5c/1685f184e069f820] [http-nio-9007-exec-13] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - <==      Total: 881
2025-07-25 01:09:43.450 [] [d08a3c4595d31e5c/8db0201636f9358c] [http-nio-9007-exec-13] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 门户外系统调用 操作方法: 根据指定条件获取指定机构信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 39 毫秒！
2025-07-25 01:09:43.480 [] [d08a3c4595d31e5c/700ed83088ce9dfc] [http-nio-9007-exec-36] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 1 毫秒！
2025-07-25 01:09:43.603 [] [b3dd07e9479487c1/9351b6e9b9408818] [http-nio-9007-exec-33] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 3 毫秒！
2025-07-25 01:09:43.630 [] [b3dd07e9479487c1/4bbf98133dd99e10] [http-nio-9007-exec-26] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==>  Preparing: select * from sm_organ_tb
2025-07-25 01:09:43.631 [] [b3dd07e9479487c1/4bbf98133dd99e10] [http-nio-9007-exec-26] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - ==> Parameters: 
2025-07-25 01:09:43.659 [] [b3dd07e9479487c1/4bbf98133dd99e10] [http-nio-9007-exec-26] DEBUG c.s.a.u.d.U.selectOrganListByCriteria - <==      Total: 881
2025-07-25 01:09:43.682 [] [b3dd07e9479487c1/f92d4dd3c6cc5433] [http-nio-9007-exec-26] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 门户外系统调用 操作方法: 根据指定条件获取指定机构信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 52 毫秒！
2025-07-25 01:09:43.693 [] [b3dd07e9479487c1/9472e0ba0fa7d896] [http-nio-9007-exec-35] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 1 毫秒！
2025-07-25 01:09:43.725 [] [facc3c2742f3baaf/5a8bbc237ceb0b1f] [http-nio-9007-exec-22] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 1 毫秒！
2025-07-25 01:09:43.738 [] [facc3c2742f3baaf/afb4baa39e8fc9aa] [http-nio-9007-exec-47] DEBUG c.s.a.u.d.U.selectUserRolesList - ==>  Preparing: select * from SM_USER_ROLE_TB WHERE USER_NO = ?
2025-07-25 01:09:43.738 [] [facc3c2742f3baaf/afb4baa39e8fc9aa] [http-nio-9007-exec-47] DEBUG c.s.a.u.d.U.selectUserRolesList - ==> Parameters: admin(String)
2025-07-25 01:09:43.739 [] [facc3c2742f3baaf/afb4baa39e8fc9aa] [http-nio-9007-exec-47] DEBUG c.s.a.u.d.U.selectUserRolesList - <==      Total: 1
2025-07-25 01:09:43.741 [] [facc3c2742f3baaf/dcd6a4c4e5540779] [http-nio-9007-exec-47] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 门户外系统调用 操作方法: 根据指定条件获取指定角色信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 3 毫秒！
2025-07-25 01:09:43.770 [] [facc3c2742f3baaf/7dbaf05721714a0a] [http-nio-9007-exec-21] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 2 毫秒！
2025-07-25 01:09:44.791 [] [0fc533711bb57fb5/5285654a0c1b3867] [http-nio-9007-exec-38] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:44 操作结束时间: 2025-07-25 01:09:44!总共花费时间: 3 毫秒！
2025-07-25 01:09:44.828 [] [0fc533711bb57fb5/517740057729ec7d] [http-nio-9007-exec-39] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:44 操作结束时间: 2025-07-25 01:09:44!总共花费时间: 2 毫秒！
2025-07-25 01:09:48.944 [] [/] [http-nio-9007-exec-42] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:09:49.767 [] [ac32d7a8ed4507c5/02b5b074a540e243] [http-nio-9007-exec-34] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:49 操作结束时间: 2025-07-25 01:09:49!总共花费时间: 1 毫秒！
2025-07-25 01:09:49.778 [] [ac32d7a8ed4507c5/6b47013cec57b600] [http-nio-9007-exec-52] DEBUG c.s.a.u.d.U.selectUserRolesList - ==>  Preparing: select * from SM_USER_ROLE_TB WHERE USER_NO = ?
2025-07-25 01:09:49.778 [] [ac32d7a8ed4507c5/6b47013cec57b600] [http-nio-9007-exec-52] DEBUG c.s.a.u.d.U.selectUserRolesList - ==> Parameters: admin(String)
2025-07-25 01:09:49.783 [] [ac32d7a8ed4507c5/6b47013cec57b600] [http-nio-9007-exec-52] DEBUG c.s.a.u.d.U.selectUserRolesList - <==      Total: 1
2025-07-25 01:09:49.785 [] [ac32d7a8ed4507c5/39424bdcdd64fa97] [http-nio-9007-exec-52] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 门户外系统调用 操作方法: 根据指定条件获取指定角色信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:49 操作结束时间: 2025-07-25 01:09:49!总共花费时间: 7 毫秒！
2025-07-25 01:09:49.827 [] [ac32d7a8ed4507c5/ff4548f211ffab80] [http-nio-9007-exec-60] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 新增日志信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:49 操作结束时间: 2025-07-25 01:09:49!总共花费时间: 3 毫秒！
2025-07-25 01:09:49.841 [] [ac32d7a8ed4507c5/67afbe3f6b6746b6] [http-nio-9007-exec-40] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:49 操作结束时间: 2025-07-25 01:09:49!总共花费时间: 1 毫秒！
2025-07-25 01:09:51.354 [] [626b01c9cd5a0256/6ac2c2ffdc91d5a5] [http-nio-9007-exec-53] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作申请模块 操作方法: 查询页面的提交方式!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:51 操作结束时间: 2025-07-25 01:09:51!总共花费时间: 15 毫秒！
2025-07-25 01:09:51.472 [] [ecd6d1403fba65d9/e9053b1f7f843803] [http-nio-9007-exec-45] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:51 操作结束时间: 2025-07-25 01:09:51!总共花费时间: 3 毫秒！
2025-07-25 01:09:51.502 [] [ecd6d1403fba65d9/0f140af49888529e] [http-nio-9007-exec-48] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 新增日志信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:51 操作结束时间: 2025-07-25 01:09:51!总共花费时间: 3 毫秒！
2025-07-25 01:09:51.516 [] [ecd6d1403fba65d9/1cd3899028194dc0] [http-nio-9007-exec-46] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:51 操作结束时间: 2025-07-25 01:09:51!总共花费时间: 2 毫秒！
2025-07-25 01:09:51.548 [] [69a0f64ec175478a/a18ed83e8997a739] [http-nio-9007-exec-43] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:51 操作结束时间: 2025-07-25 01:09:51!总共花费时间: 2 毫秒！
2025-07-25 01:09:51.581 [] [69a0f64ec175478a/f27b61b346d2f253] [http-nio-9007-exec-41] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 新增日志信息!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:51 操作结束时间: 2025-07-25 01:09:51!总共花费时间: 3 毫秒！
2025-07-25 01:09:51.595 [] [69a0f64ec175478a/63b0c9970ad67732] [http-nio-9007-exec-58] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:51 操作结束时间: 2025-07-25 01:09:51!总共花费时间: 2 毫秒！
2025-07-25 01:09:52.945 [] [aef05474f20b443b/cbe3cd61953c576f] [http-nio-9007-exec-50] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:52 操作结束时间: 2025-07-25 01:09:52!总共花费时间: 6 毫秒！
2025-07-25 01:09:53.004 [] [aef05474f20b443b/141eb8e1f483b37f] [http-nio-9007-exec-49] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:53 操作结束时间: 2025-07-25 01:09:53!总共花费时间: 2 毫秒！
2025-07-25 01:09:53.067 [] [7213e21219fc9f62/68cb3cdf2dadbec9] [http-nio-9007-exec-28] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:53 操作结束时间: 2025-07-25 01:09:53!总共花费时间: 2 毫秒！
2025-07-25 01:09:53.082 [] [7213e21219fc9f62/88a3770e6b349296] [http-nio-9007-exec-61] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:53 操作结束时间: 2025-07-25 01:09:53!总共花费时间: 2 毫秒！
2025-07-25 01:09:53.185 [] [6b66beb9aa3cc2c3/996068f1f29bf325] [http-nio-9007-exec-73] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:53 操作结束时间: 2025-07-25 01:09:53!总共花费时间: 2 毫秒！
2025-07-25 01:09:53.366 [] [6b66beb9aa3cc2c3/50437264d58adb3a] [http-nio-9007-exec-63] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 系统Basedao 操作方法: 查询指定模块下的加密字段!请求IP地址: ************ 操作开始时间: 2025-07-25 01:09:53 操作结束时间: 2025-07-25 01:09:53!总共花费时间: 1 毫秒！
2025-07-25 01:09:58.962 [] [/] [http-nio-9007-exec-56] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:10:08.974 [] [/] [http-nio-9007-exec-51] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:10:18.990 [] [/] [http-nio-9007-exec-66] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:10:29.003 [] [/] [http-nio-9007-exec-89] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:10:36.481 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 01:10:39.020 [] [/] [http-nio-9007-exec-54] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:10:49.033 [] [/] [http-nio-9007-exec-68] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:10:59.050 [] [/] [http-nio-9007-exec-64] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:11:09.063 [] [/] [http-nio-9007-exec-75] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:11:19.080 [] [/] [http-nio-9007-exec-85] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:11:29.517 [] [/] [http-nio-9007-exec-59] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:11:40.525 [] [/] [http-nio-9007-exec-76] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:11:51.521 [] [/] [http-nio-9007-exec-55] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:12:02.511 [] [/] [http-nio-9007-exec-79] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:12:13.523 [] [/] [http-nio-9007-exec-78] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:12:24.518 [] [/] [http-nio-9007-exec-83] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:12:35.518 [] [/] [http-nio-9007-exec-88] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:12:46.523 [] [/] [http-nio-9007-exec-80] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 01:12:46.724 [] [/] [http-nio-9007-exec-72] INFO  c.s.c.I.s.w.WebSocketSessionUtils - admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1 断开Websocket连接
2025-07-25 08:01:42.041 [] [bcd52d3a0e7a490b/984fa24e36aabc20] [http-nio-9007-exec-92] INFO  c.s.c.I.s.w.WebSocketHandshakeInterceptor - 用户号:admin 用户标识: 1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66 登录终端类型: 1 尝试Websocket连接:localhost:9007 单点登录标志：0
2025-07-25 08:01:42.044 [] [bcd52d3a0e7a490b/984fa24e36aabc20] [http-nio-9007-exec-92] DEBUG c.s.c.I.s.w.WebSocketHandshakeInterceptor - 握手后执行
2025-07-25 08:01:42.045 [] [/] [http-nio-9007-exec-92] DEBUG c.s.c.I.s.w.WebSocketSaveCache - 用户admin未启用单点登录
2025-07-25 08:01:42.054 [] [/] [http-nio-9007-exec-92] INFO  c.s.c.I.s.w.WebSocketSessionUtils - admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1 加入Websocket连接
2025-07-25 08:01:55.215 [] [/] [http-nio-9007-exec-90] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
2025-07-25 08:02:06.301 [] [/] [http-nio-9007-exec-93] DEBUG c.s.c.I.s.w.WebSocketSessionUtils - 向sessionId「admin_1a2ab0e6-8cc2-4f0b-b645-b14e70d89d66_1」webSocket发送消息：pong
