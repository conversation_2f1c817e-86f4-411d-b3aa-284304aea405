<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-2.5.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
    <!-- 表主键自增配置-->


    <bean id="roleInfo_roleID"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="ROLE_INFO"/>
        <property name="columnName" value="ROLE_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>

    <bean id="logRule_logID"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="LOG_RULE"/>
        <property name="columnName" value="ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>

    <bean id="cachenodeInfo_nodeID"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="CONTENT_SERVER_INFO"/>
        <property name="columnName" value="SERVER_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>

    <bean id="contentServerGroup_groupID"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="CONTENT_SERVER_GROUP"/>
        <property name="columnName" value="GROUP_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>

    <bean id="configInfoSynchroID"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="CONFIG_INFO_SYNCHRO"/>
        <property name="columnName" value="CONFIG_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>

    <bean id="volumeID"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="VOLUME_INFO"/>
        <property name="columnName" value="VOLUME_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>

    <bean id="unityAccessServerId"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="UNITY_ACCESS_SERVER"/>
        <property name="columnName" value="SERVER_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>
    <bean id="unityAccessServerGroupId"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="UNITY_ACCESS_SERVER_GROUP"/>
        <property name="columnName" value="GROUP_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>
    <bean id="task_no_incrementer"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="DMTASK"/>
        <property name="columnName" value="TASK_NO"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>
    <bean id="schedule_id_incrementer"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="SCHEDULER"/>
        <property name="columnName" value="TASK_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>
    <bean id="group_cmodel_rel_id_incrementer"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="SGROUP_CMODEL_REL"/>
        <property name="columnName" value="ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>
    <bean id="insNo_dms_rel_id_incrementer"
          class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="ITEMTYPE_INSNO_DMSNAME"/>
        <property name="columnName" value="PID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"></property>
    </bean>
    <bean id="param_config_par_id_incrementer" class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="PARAM_CONFIG_INFO"/>
        <property name="columnName" value="PAR_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"/>
    </bean>
    <bean id="param_show_par_show_id_incrementer" class="org.springframework.jdbc.support.incrementer.MySQLMaxValueIncrementer">
        <property name="incrementerName" value="PARAM_SHOW_INFO"/>
        <property name="columnName" value="PAR_SHOW_ID"/>
        <property name="cacheSize" value="0"/>
        <property name="dataSource" ref="dataSource"/>
    </bean>
</beans>