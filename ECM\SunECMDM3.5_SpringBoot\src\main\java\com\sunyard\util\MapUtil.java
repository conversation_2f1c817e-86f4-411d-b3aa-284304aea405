package com.sunyard.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Map工具类
 * 
 * <AUTHOR>
 * 
 */
public class MapUtil {
	/**
	 * 通过传入的字符串来生成键值对并返回,字符串的格式为"a=b,c=d,e=f"
	 * 
	 * @param mapString
	 * @return
	 */
	public static Map<String, String> parseMap(String mapString, String clientIp) {
		String[] content = mapString.split(",");
		Map<String, String> map = new HashMap<String, String>();
		String answer = "";
		String value = "";
		for (int i = 0; i < content.length; i++) {
			if (content[i].indexOf("=") > 0) {
				answer = content[i].substring(0, content[i].indexOf("=")).trim();
				value = content[i].substring(content[i].indexOf("=") + 1,
						content[i].length()).trim();
				value = SymbolChange.severSocketSymbolChange(value);
				map.put(answer, value);
			}
		}
		map.put("CLIENT_IP", clientIp);
		return map;
	}

	/**
	 * 通过传入的字符串来生成键值对并返回,字符串的格式为"a=b&c=d&e=f"
	 * 
	 * @param mapString
	 * @return
	 */
	public static Map<String, String> parseMap(String mapString) {
		String[] content = mapString.split("&");
		Map<String, String> map = new HashMap<String, String>();
		String answer = "";
		String value = "";
		for (int i = 0; i < content.length; i++) {
			if (content[i].indexOf("=") > 0) {
				answer = content[i].substring(0, content[i].indexOf("=")).trim();
				value = content[i].substring(content[i].indexOf("=") + 1,
						content[i].length()).trim();
				value = SymbolChange.severSocketSymbolChange(value);
				map.put(answer, value);
			}
		}
		return map;
	}

	/**
	 * 解析集合中包含集合
	 * 
	 * @param str
	 *            [COMMON<<++>> [{a<<@@>>1<<++>> b<<@@>>2}]]
	 * @return
	 */
	public static List parseDoubleList(String str) {
		List allList = new ArrayList();
		String[] s = str.split("<<+DOU+>> \\[");
		allList.add(s[0].substring(1));
		allList.add(parseList(s[1]));
		return allList;
	}

	/**
	 * 将字符串转换成List
	 * 
	 * @param listStr
	 * @return
	 */
	public static List<Map> parseList(String listStr) {
		List<Map> list = new ArrayList<Map>();
	//	Map<String, String> map = null;
		String str = "";
		String[] content = listStr.split("}<<DOU>>");
		for (int i = 0; i < content.length; i++) {
			str = content[i];
			if (i + 1 == content.length) {
				str = str.substring(str.indexOf("{") + 1, str.lastIndexOf("}"));
			} else {
				str = str.substring(str.indexOf("{") + 1);
			}
			list.add(parseString2Map(str));
		}
		return list;
	}

	/**
	 * 将字符串转换成Map
	 * 
	 * @param string
	 * @return
	 */
	private static Map<String, String> parseString2Map(String string) {
		Map<String, String> map = new HashMap<String, String>();
		String[] content = string.split("<<DOU>>");
		String[] values = null;
		String str = "";
		for (int i = 0; i < content.length; i++) {
			str = content[i].trim();
			values = str.split("<<@@>>");
			map.put(values[0], values[1]);
		}
		return map;
	}

	/**
	 * 将map转换成string格式：X=X,X=X。
	 * 
	 * @param map
	 * @return
	 */
	public static String toString(Map<String, String> map) {
		return map.toString().replace("{", "").replace("}", "");
	}

//	public static void main(String[] args) {
//		String string = "F_BATCHID=20110617F34B65C0-5793-B5F1-181B-C9DE794CFA80-4, OBJNAME=GROUP, FILENO=4-97BF1423-DDFC-DA2A-1B37-BB8A3649BCB2, BATCHID=20110617F34B65C0-5793-B5F1-181B-C9DE794CFA80-4";
//		String nullString = "";
//		Map<String, String> map = MapUtil.parseMap(string);
//		System.out.println(toString(map).equals(""));
//		System.out.println(toString(map).equals(string));
//		List<Map<String, String>> maxVersion = new ArrayList<Map<String, String>>();
//		//maxVersion.add(map);
//		maxVersion.add(new HashMap<String, String>());
//		StringBuilder result = new StringBuilder();
//		for (Map<String, String> map1 : maxVersion) {
//			if (map1.size() != 0) {
//				result.append(MapUtil.toString(map1)).append(";");
//			}
//		}
//		if (result.length() == 0) {
//			System.out.println("");
//		} else
//			System.out.println("==" + result.substring(0, result.length() - 1));
//	}
}