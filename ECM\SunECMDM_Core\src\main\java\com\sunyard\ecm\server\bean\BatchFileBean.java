package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 批次信息
 * <AUTHOR>
 *
 */
@XStreamAlias("BatchFileBean")
public class BatchFileBean {
	/** 文档部件名 **/
	@XStreamAsAttribute
	private String FILE_PART_NAME;
	/** 文档部件版本信息 **/
	@XStreamAsAttribute
	private String VERSION;
	/** 文档部件过滤条件 **/
	private HashMap<String, String> FILTERS;

	/** 文件 **/
	private List<FileBean> files;
	private String FILE_PART_TABLE_NAME;
	
	public String getFILE_PART_TABLE_NAME() {
		return FILE_PART_TABLE_NAME;
	}

	public void setFILE_PART_TABLE_NAME(String fILE_PART_TABLE_NAME) {
		FILE_PART_TABLE_NAME = fILE_PART_TABLE_NAME;
	}

	public HashMap<String, String> getFilters() {
		return FILTERS;
	}

	public void setFilters(HashMap<String, String> filters) {
		FILTERS = filters;
	}
	
	/**
	 * 添加过滤条件
	 * 
	 * @param key 筛选条件
	 * @param value 筛选值
	 */
	public void addFilter(String key, String value) {
		if(FILTERS == null) {
			FILTERS = new HashMap<String, String>();
		}
		this.FILTERS.put(key, value);
	}

	public String getFilePartName() {
		return FILE_PART_NAME;
	}

	public void setFilePartName(String filePartName) {
		this.FILE_PART_NAME = filePartName;
	}

	public String getVersion() {
		return VERSION;
	}

	public void setVersion(String version) {
		this.VERSION = version;
	}

	public List<FileBean> getFiles() {
		return files;
	}

	public void setFiles(List<FileBean> files) {
		this.files = files;
	}
	
	public void addFile(FileBean file) {
		if(files == null) {
			files = new ArrayList<FileBean>();
		}
		this.files.add(file);
	}
	
	@Override
	public String toString() {
		return "BatchFileBean [FILE_PART_NAME=" + FILE_PART_NAME + "]";
	}
}