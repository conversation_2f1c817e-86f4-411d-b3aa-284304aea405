package com.xxl.job.admin.core;

import java.util.HashMap;
import java.util.Map;

public class Constants {

    public static  Map<String,HashMap<String,String>>  ALL_HANDLER = new  HashMap<String,HashMap<String,String>>();
    
    
    public static String IS_SUCCESS = "200";
    
    public static String IS_FAIL = "2";


    //任务状态
    public static final int TASK_STATE_NOT_VISITE = 0;  //未执行
    public static final int TASK_STATE_HAS_VISITED = 1; //已执行
    public static final int TASK_STATE_EXECUTE_ERROR = -1;//执行出错
    public static final int TASK_STATE_HAS_HANDLEOUT = 2;//正在执行
    public static final int TASK_STATE_WAIT_RESPONSE = 3;//发出执行请求等待回复
    public static final int TASK_STATE_NO_RESOURCE = -2;//资源不足
    public static final int TASK_CONFIG_ERROR = -3;//配置错误

    //任务重启间隔时间10分钟
    public static final long TASK_TIME = (long)(10*60*1000);

    //任务超时重发三个小时个小时
    public static final long TASK_TIME_OUT = (long)(3*60*60*1000);

    //任务等待回复超时2个小时
    public static final long TASK_RESPONSE_TIME_OUT = (long)(2*60*60*1000);

}
