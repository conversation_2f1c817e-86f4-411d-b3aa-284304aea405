	package com.sunyard.console.poolmanage.action;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.contentmodelmanage.bean.TreeBean;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sequoiadb.base.Sequoiadb;
import com.sequoiadb.base.SequoiadbDatasource;
import com.sequoiadb.datasource.ConnectStrategy;
import com.sequoiadb.datasource.DatasourceOptions;
import com.sequoiadb.net.ConfigOptions;
import com.sunyard.console.contentservermanage.bean.ContentServerGroupInfoBean;
import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.contentservermanage.dao.ContentServerGroupManageDAO;
import com.sunyard.console.poolmanage.bean.CollectionNameBean;
import com.sunyard.console.poolmanage.bean.CollectionSpaceNamesBean;
import com.sunyard.console.poolmanage.bean.ModelRelSDBConfigBean;
import com.sunyard.console.poolmanage.bean.PoolInfoBean;
import com.sunyard.console.poolmanage.dao.ContentObjParamConfDao;
import com.sunyard.console.poolmanage.dao.PoolManegerDAO;
import com.sunyard.console.threadpoool.IssueUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;

	/**
 * <p>
 * Title: 内容对象参数配置action
 * </p>
 * <p>
 * Description: 用于内容参数配置的action访问类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2017
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR> @version 1.0
 */
@Controller
public class ContentObjParamConfAction extends BaseAction {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String nodeId;
	private String modelcode;
	private int rel_id;// 修改配置表id
	private int sdb_connection_id;// SDB连接配置表id
	private String collectionSpaces;// 集合空间名称
	private String referenceCollections;// 索引对象集合
	private String documentCollections;// 文档对象集合
	private String fileCollections;// 文件集合
	private int split_cycle_auto;// 是否分表
	private String split_cycle;// 分表周期
	private int sdb_file_connection_id;//缓存SDB连接
	private String sdb_file_collectionspace;//缓存集合空间
	/**
	 * 分页记录开始位置
	 */
	private int start;
	/**
	 * 分页记录条数
	 */
	private int limit;

	@Autowired
	ContentServerGroupManageDAO contentServerGroupManageDao;

	@Autowired
	ContentObjParamConfDao contentObjParamConfDao;

	@Autowired
	PoolManegerDAO poolManegerDAO;
	private int group_id;// 服务器组id根据此id查询内容对象

	public String getNodeId() {
		return nodeId;
	}

	public void setNodeId(String nodeId) {
		this.nodeId = nodeId;
	}

	public String getReferenceCollections() {
		return referenceCollections;
	}

	public void setReferenceCollections(String referenceCollections) {
		this.referenceCollections = referenceCollections;
	}

	public String getDocumentCollections() {
		return documentCollections;
	}

	public void setDocumentCollections(String documentCollections) {
		this.documentCollections = documentCollections;
	}

	public String getFileCollections() {
		return fileCollections;
	}

	public void setFileCollections(String fileCollections) {
		this.fileCollections = fileCollections;
	}

	public int getRel_id() {
		return rel_id;
	}

	public void setRel_id(int rel_id) {
		this.rel_id = rel_id;
	}

	public int getSplit_cycle_auto() {
		return split_cycle_auto;
	}

	public void setSplit_cycle_auto(int split_cycle_auto) {
		this.split_cycle_auto = split_cycle_auto;
	}

	public String getSplit_cycle() {
		return split_cycle;
	}

	public void setSplit_cycle(String split_cycle) {
		this.split_cycle = split_cycle;
	}

	public String getCollectionSpaces() {
		return collectionSpaces;
	}

	public void setCollectionSpaces(String collectionSpaces) {
		this.collectionSpaces = collectionSpaces;
	}

	public String getModelcode() {
		return modelcode;
	}

	public void setModelcode(String modelcode) {
		this.modelcode = modelcode;
	}

	public ContentServerGroupManageDAO getContentServerGroupManageDao() {
		return contentServerGroupManageDao;
	}

	public void setContentServerGroupManageDao(
			ContentServerGroupManageDAO contentServerGroupManageDao) {
		this.contentServerGroupManageDao = contentServerGroupManageDao;
	}

	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int group_id) {
		this.group_id = group_id;
	}

	public ContentObjParamConfDao getContentObjParamConfDao() {
		return contentObjParamConfDao;
	}

	public void setContentObjParamConfDao(
			ContentObjParamConfDao contentObjParamConfDao) {
		this.contentObjParamConfDao = contentObjParamConfDao;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public PoolManegerDAO getPoolManegerDAO() {
		return poolManegerDAO;
	}

	public void setPoolManegerDAO(PoolManegerDAO poolManegerDAO) {
		this.poolManegerDAO = poolManegerDAO;
	}

	public int getSdb_connection_id() {
		return sdb_connection_id;
	}

	public void setSdb_connection_id(int sdb_connection_id) {
		this.sdb_connection_id = sdb_connection_id;
	}

	public int getSdb_file_connection_id() {
		return sdb_file_connection_id;
	}

	public void setSdb_file_connection_id(int sdb_file_connection_id) {
		this.sdb_file_connection_id = sdb_file_connection_id;
	}

	public String getSdb_file_collectionspace() {
		return sdb_file_collectionspace;
	}

	public void setSdb_file_collectionspace(String sdb_file_collectionspace) {
		this.sdb_file_collectionspace = sdb_file_collectionspace;
	}

	private final static  Logger log = LoggerFactory.getLogger(ContentObjParamConfAction.class);

	/**
	 * 获取服务器组
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/getServiceGroupListAction.action", method = RequestMethod.POST)
	public String getServiceGroup() {
		log.info("--getServiceGroup(start)-->");//日志打印
		StringBuffer nodeString = null;//存储服务器节点信息
		try {
			List<ContentServerGroupInfoBean> allInfoList = contentServerGroupManageDao
					.getContentServerGroupList(0, "");//获取服务器组信息
			log.debug( "--getServiceGroup-->AllInfoList:"+allInfoList);//日志打印
			nodeString = new StringBuffer("[");
			nodeString.append("{'id':'" + allInfoList.get(0).getGroup_id()//树id
					+ "',");
			nodeString.append("'text':'" + allInfoList.get(0).getGroup_name()//树显示内容
					+ "',");
			nodeString.append("'leaf':'true'}");
			for (int i = 1; i < allInfoList.size(); i++) {
				if (i > 0)
					nodeString.append(",{");
				nodeString.append("'id':'" + allInfoList.get(i).getGroup_id()
						+ "',");
				nodeString.append("'text':'"
						+ allInfoList.get(i).getGroup_name() + "',");
				nodeString.append("'leaf':'true'}");
			}
			nodeString.append("]");//组装服务器树
			log.debug( "--getServiceGroup-->nodeString:"+nodeString);
		} catch (Exception e) {
			// 记录日志
			log.error("内容存储服务器管理->获取服务器组列表失败" + e.toString(), e);
		}
		this.outJsonString(nodeString.toString());
		log.info("--getServiceGroup(over)-->");
		return null;
	}

	/**
	 * 內容对象查询
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/getContentObjectAction.action", method = RequestMethod.POST)
	public String contentObjectSearch(int group_id) {
		log.info("--contentObjectSearch(start)-->group_id:" + group_id);
		String jsonStr = "";
		List<TreeBean> attrList = null;
		StringBuffer nodeBuffer = new StringBuffer();
		try {
			attrList = contentServerGroupManageDao
					.getRelContentObjectTree(group_id);//查询内容对象信息
			if (attrList != null && attrList.size() > 0) {
				log.debug( "--contentObjectSearch-->attrList:"+attrList);
				for (int i = 0; i < attrList.size(); i++) {
					nodeBuffer.append("'");
					//组装内容对象字符串
					nodeBuffer.append(attrList.get(i).getId()).append("'").append(",").append("'").append(attrList.get(i).getText_text()).append("'");
					if(i!=attrList.size()-1){//判断是否追加最后一个"|"
						nodeBuffer.append("|");
					}
				}//组装内容对象字符串
				jsonStr = nodeBuffer.toString();
				log.debug( "--contentObjectSearch-->nodeBuffer:"+nodeBuffer);//日志打印
			} else {
				log.debug("--contentObjectSearch-->没有查询到相关记录-->group_id:"
						+ group_id);//日志打印
			}
		} catch (Exception e) {
			// 记录日志
			log.error("内容存储服务器组管理->查询关联的内容对象失败:" + e.toString(), e);//日志打印

		}
		this.outJsonString(jsonStr);
		log.info("--contentObjectSearch(over)-->group_id:" + group_id);//日志打印
		return null;
	}

	/**
	 * 根据内容对象查询参数配置
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/getContentObjConfAction.action", method = RequestMethod.POST)
	public String contentObjConfList(String modelcode,int start,int limit) {
		log.info("--contentObjConfList(start)-->MODEL_CODE:" + modelcode);//日志打印
		String jsonStr = "";
		try {

			List<ModelRelSDBConfigBean> modelRelSDBConfigBeanList = contentObjParamConfDao
					.contentObjConfList(modelcode, start + 1, limit);//查询内容对象配置信息
			List<ModelRelSDBConfigBean> allModelRelSDBConfigBeanList = contentObjParamConfDao
					.contentObjConfList(modelcode);//查询内容对象配置信息
			int size = 0;
			if (allModelRelSDBConfigBeanList != null
					&& allModelRelSDBConfigBeanList.size() > 0) {
				size = allModelRelSDBConfigBeanList.size();
				log.debug( "--contentObjConfList-->AllModelRelSDBConfigBeanList:"+allModelRelSDBConfigBeanList);//日志打印
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					modelRelSDBConfigBeanList, size,
					new ModelRelSDBConfigBean());
			log.debug("--contentObjConfList()-->查询成功;modelcode:" + modelcode);//日志打印
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取参数配置信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容对象参数配置->获取参数配置列表失败" + e.toString());
			log.error("Exception:",e);
		}
		this.outJsonString(jsonStr);
		log.info("--contentObjConfList(over)");//日志打印
		return null;
	}

	/**
	 * 业务参数配置(新增)
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/contentObjConfAddAction.action", method = RequestMethod.POST)
	public String contentObjConfAdd(int group_id,String modelcode,String collectionSpaces,int sdb_connection_id,String documentCollections,
									String fileCollections,String referenceCollections,String split_cycle,int split_cycle_auto,String sdb_file_collectionspace) {
		String jsonStr = "";
		int sdb_file_connection_id = 0;
		ModelRelSDBConfigBean modelRelSDBConfigBean = new ModelRelSDBConfigBean();
		modelRelSDBConfigBean.setGroup_id(group_id);//服务器组id
		modelRelSDBConfigBean.setModel_code(modelcode);//内容对象
		modelRelSDBConfigBean.setSdb_collectionspace(collectionSpaces);//集合空间
		modelRelSDBConfigBean.setSdb_connection_id(sdb_connection_id);//sdb连接
		modelRelSDBConfigBean.setSdb_doc_collection(documentCollections);//文档对象集合
		modelRelSDBConfigBean.setSdb_file_collection(fileCollections);//文件对象集合
		modelRelSDBConfigBean.setSdb_index_collection(referenceCollections);//索引对象集合
		modelRelSDBConfigBean.setSplit_cycle(split_cycle);//分表周期
		modelRelSDBConfigBean.setSplit_cycle_auto(split_cycle_auto);//是否分表
		modelRelSDBConfigBean.setSDB_FILE_CONNECTION_ID(sdb_file_connection_id);
		modelRelSDBConfigBean.setSDB_FILE_COLLECTIONSPACE(sdb_file_collectionspace);//文件集合
		log.debug("--contentObjConfAdd-->contentBean:" + modelRelSDBConfigBean);//日志打印
		JSONObject jsonResp = new JSONObject();
		try {
			boolean result = contentObjParamConfDao
					.contentObjConfAdd(modelRelSDBConfigBean);
			log.debug("--contentObjConfAdd()-->查询成功;result:" + result);
			if (result) {// 新增成功
				List<ContentServerInfoBean> serverBeanList = contentServerGroupManageDao
						.getContentServerByGroupId(group_id+"");
				if (serverBeanList != null && serverBeanList.size() > 0) {
					for (ContentServerInfoBean bean : serverBeanList){
						IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
						IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
						//new Thread(new SendContentObjParamConfThread(bean.getServer_ip()+":"+bean.getHttp_port())).start();
					}
				}
				jsonResp.put("message", "新增参数配置成功!!");
				jsonResp.put("success", true);
				jsonStr = jsonResp.toString();
				log.debug("--contentObjConfAdd-->新增参数配置成功");
			} else {// 新增失败
				jsonResp.put("success", false);
				jsonResp.put("message", "新增参数配置失败!!");
				jsonStr = jsonResp.toString();
				log.debug("--contentObjConfAdd-->新增参数配置失败");
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "新增参数配置失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容对象参数配置->新增参数配置失败!" + e.toString(), e);//日志打印
		}
		log.info("--contentObjConfAdd(over)-->model_code:");//日志打印
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 业务参数配置(修改)
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/contentObjConfUpdateAction.action", method = RequestMethod.POST)
	public String contentObjConfUpdate(int group_id,String modelcode,int rel_id,String collectionSpaces,int sdb_connection_id,
									   String documentCollections, String fileCollections,String referenceCollections,String split_cycle,int split_cycle_auto,
									   String sdb_file_collectionspace) {
		String jsonStr = "";
		int sdb_file_connection_id = 0;
		ModelRelSDBConfigBean modelRelSDBConfigBean = new ModelRelSDBConfigBean();
		modelRelSDBConfigBean.setGroup_id(group_id);//服务器组id
		modelRelSDBConfigBean.setModel_code(modelcode);//内容模型
		modelRelSDBConfigBean.setRel_id(rel_id);//参数配置表id
		modelRelSDBConfigBean.setSdb_collectionspace(collectionSpaces);//集合空间
		modelRelSDBConfigBean.setSdb_connection_id(sdb_connection_id);//sdb连接
		modelRelSDBConfigBean.setSdb_doc_collection(documentCollections);//文档对象集合
		modelRelSDBConfigBean.setSdb_file_collection(fileCollections);//文件对象集合
		modelRelSDBConfigBean.setSdb_index_collection(referenceCollections);//索引对象集合
		modelRelSDBConfigBean.setSplit_cycle(split_cycle);//分表周期
		modelRelSDBConfigBean.setSplit_cycle_auto(split_cycle_auto);//是否分表
		modelRelSDBConfigBean.setSDB_FILE_CONNECTION_ID(sdb_file_connection_id);
		modelRelSDBConfigBean.setSDB_FILE_COLLECTIONSPACE(sdb_file_collectionspace);//文件集合
		JSONObject jsonResp = new JSONObject();
		try {
			boolean result = contentObjParamConfDao
					.contentObjConfUpdate(modelRelSDBConfigBean);
			log.debug("--contentObjConfUpdate()-->查询成功;result:" + result);//日志打印
			if (result) {// 修改成功
				List<ContentServerInfoBean> serverBeanList = contentServerGroupManageDao
						.getContentServerByGroupId(group_id+"");
				if (serverBeanList != null && serverBeanList.size() > 0) {
					for (ContentServerInfoBean bean : serverBeanList){
						IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
						IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
						//new Thread(new SendContentObjParamConfThread(bean.getServer_ip()+":"+bean.getHttp_port())).start();
					}
				}
				jsonResp.put("message", "修改参数配置成功!!");//日志打印
				jsonResp.put("success", true);
				jsonStr = jsonResp.toString();
				log.debug("--contentObjConfUpdate-->修改参数配置成功");//日志打印
			} else {// 修改失败
				jsonResp.put("success", false);
				jsonResp.put("message", "修改参数配置失败!!");
				jsonStr = jsonResp.toString();
				log.debug("--contentObjConfUpdate-->修改参数配置失败");//日志打印
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "修改参数配置失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容对象参数配置->修改参数配置失败!" + e.toString(), e);//日志打印
		}
		log.info("--contentObjConfUpdate(over)--");//日志打印
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 查询集合空间名称
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/getSdbCollectionNamesAction.action", method = RequestMethod.POST)
	public String getSdbCollectionNames(int sdb_connection_id) {
		SequoiadbDatasource ds;
		Sequoiadb db;
		String jsonStr = null;
		int size = 0;
		JSONObject jsonResp = new JSONObject();
		PoolInfoBean poolInfoBean = null;// 定义连接池配置表bean
		List<CollectionSpaceNamesBean> collectionSpaceNameslist = new ArrayList<CollectionSpaceNamesBean>();
		List<PoolInfoBean> contentpoolList = poolManegerDAO
				.getContentpoolList(sdb_connection_id);//查询连接池配置信息
		if (contentpoolList == null || contentpoolList.size() == 0) {
			jsonResp.put("success", true);
			jsonResp.put("message", "未查询到集合空间名称");
			jsonStr = jsonResp.toString();
			log.info("--getSdbCollectionNames(over)--");//日志打印
			this.outJsonString(jsonStr.toString());
			return null;
		} else {
			log.debug("--getSdbCollectionNames-->contentpoolList" + contentpoolList);// 日志打印
		}
		poolInfoBean = contentpoolList.get(0);//连接池配置信息
		ds = getSequoiadbDatasource(poolInfoBean);//获取连接池
		try {
			if(ds!=null){
			db = ds.getConnection();//获取连接
			List<String> stringCollectionSpaceNameslist = db
					.getCollectionSpaceNames();//获取集合空间名称
			collectionSpaceNameslist = new ArrayList<CollectionSpaceNamesBean>();
			CollectionSpaceNamesBean collectionSpaceNamesBean = null;
			for (int i = 0; i < stringCollectionSpaceNameslist.size(); i++) {//组装集合空间名称list
				collectionSpaceNamesBean = new CollectionSpaceNamesBean();
				collectionSpaceNamesBean
						.setCollectionSpaceName(stringCollectionSpaceNameslist
								.get(i));
				collectionSpaceNameslist.add(collectionSpaceNamesBean);
			}
			if (collectionSpaceNameslist != null
					&& collectionSpaceNameslist.size() > 0) {
				size = collectionSpaceNameslist.size();
				log.debug("--getSdbCollectionNames-->collectionSpaceNameslist"+collectionSpaceNameslist);
			}
			if (ds != null) {
				ds.close();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					collectionSpaceNameslist, size,
					new CollectionSpaceNamesBean());
		}} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "查询集合名称失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容对象参数配置->查询集合空间名称失败!" + e.toString(), e);//日志打印
		}
		log.info("--getSdbCollectionNames(over)--");//日志打印
		if (jsonStr != null) {
			this.outJsonString(jsonStr.toString());
		}else {
			log.info("jsonStr is null");
		}
		return null;
	}

	/**
	 * 获取连接池id
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/getPoolNamesAction.action", method = RequestMethod.POST)
	public String getPoolNames() {
		log.info("--getPoolNames(start)-->");//日志打印
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try {
			List<PoolInfoBean> poolList = poolManegerDAO.getContentpoolList();//查询连接池id
			if (poolList != null && poolList.size() > 0) {
				log.debug("--getPoolNames-->poolList"+poolList);//日志打印
				jsonStr = new JSONUtil().createJsonDataByBean(poolList.get(0));
			}else{
				jsonResp.put("success", false);
				jsonResp.put("message", "查询连接池失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "查询连接池失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容对象参数配置->查询连接池名称失败!" + e.toString(), e);//日志打印
		}
		log.info("--getPoolNames(over)--");//日志打印
		this.outJsonString(jsonStr.toString());
		return null;
	}

	/**
	 * 查询集合名称
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/getCollectionNamesAction.action", method = RequestMethod.POST)
	public String getCollectionNames(HttpServletRequest servletRequest, String collectionSpaces) {
		SequoiadbDatasource ds;
		Sequoiadb db;
		String jsonStr = null;
		int size = 0;
		JSONObject jsonResp = new JSONObject();

		String sdb_connection_id_str = servletRequest.getParameter("sdb_connection_id");
		int sdb_connection_id =0;
		if (StringUtils.isNotEmpty(sdb_connection_id_str)){
			sdb_connection_id = Integer.parseInt(sdb_connection_id_str);
		}

		List<PoolInfoBean> contentpoolList = poolManegerDAO
				.getContentpoolList(sdb_connection_id);//获取连接池配置信息
		if (contentpoolList == null || contentpoolList.size() == 0) {
			jsonResp.put("success", true);
			jsonResp.put("message", "未查询到集合名称!!");
			jsonStr = jsonResp.toString();
			log.info("--getCollectionNames(over)--");//日志打印
			this.outJsonString(jsonStr.toString());
			return null;
		}else {
			log.debug("--getCollectionNames-->contentpoolList"+contentpoolList);//日志打印
		}
		ds = getSequoiadbDatasource(contentpoolList.get(0));//获取连接池
		try {
			if(ds!=null){
			db = ds.getConnection();//获取连接
			List<String> strCollectionNameLists = db.getCollectionSpace(
					collectionSpaces).getCollectionNames();//获取集合空间名称
			log.debug("--getCollectionNames-->strCollectionNameLists"+strCollectionNameLists);//日志打印
			List<String> strCollectionNameList = new ArrayList<String>();//对集合名称进行处理
			for (String str : strCollectionNameLists) {
				strCollectionNameList.add(str.substring(str.indexOf(".")+1, str.length()));//处理集合名称list
			}
			log.debug("--getCollectionNames-->strCollectionNameList"+strCollectionNameList);//日志打印
			List<CollectionNameBean> collectionNamelist = new ArrayList<CollectionNameBean>();
			CollectionNameBean collectionNameBean = null;
			for (int i = 0; i < strCollectionNameList.size(); i++) {//组装集合名称beanlist
				collectionNameBean = new CollectionNameBean();
				collectionNameBean.setCollectionName(strCollectionNameList
						.get(i));
				collectionNamelist.add(collectionNameBean);
			}
			log.debug("--getCollectionNames-->collectionNamelist"+collectionNamelist);//日志打印
			if (collectionNamelist != null && collectionNamelist.size() > 0) {
				size = collectionNamelist.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(collectionNamelist,
					size, new CollectionNameBean());
			if (ds != null) {
				ds.close();//关闭连接
			}}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "查询集合空间名称失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容对象参数配置->查询集合空间名称失败!" + e.toString(), e);//日志打印
		}
		log.info("--getCollectionNames(over)");//日志打印
		if (jsonStr != null) {
			this.outJsonString(jsonStr.toString());
		}else {
			log.info("jsonStr is null");
		}
		return null;
	}

	/**
	 * 获取SDB方法
	 * 
	 * @param poolInfoBean
	 * @return
	 */
	public SequoiadbDatasource getSequoiadbDatasource(PoolInfoBean poolInfoBean) {
		log.info("--getSequoiadbDatasource(start)--");
		ConfigOptions nwOpt = new ConfigOptions(); // 定义连接选项
		DatasourceOptions dsOpt  = new DatasourceOptions(); 
		// 设置连接池参数
		dsOpt.setMaxCount(Integer.parseInt(poolInfoBean.getSo_maxconnectionnum())); // 连接池最多能提供500个连接。
		dsOpt.setDeltaIncCount(Integer.parseInt(poolInfoBean.getSo_deltainccount())); // 每次增加20个连接。
		dsOpt.setMaxIdleCount(Integer.parseInt(poolInfoBean.getSo_maxidenum())); // 连接池空闲时，保留20个连接。
		dsOpt.setKeepAliveTimeout(Integer.parseInt(poolInfoBean.getSo_abandontime())); // 池中空闲连接存活时间。单位:毫秒。
										// 0表示不关心连接隔多长时间没有收发消息。
		dsOpt.setCheckInterval(Integer.parseInt(poolInfoBean.getSo_recheckcycleperiod())); // 每隔60秒将连接池中多于
											// MaxIdleCount限定的空闲连接关闭，
											// 并将存活时间过长（连接已停止收发
											// 超过keepAliveTimeout时间）的连接关闭。
		dsOpt.setSyncCoordInterval(Integer.parseInt(poolInfoBean.getSo_syncCoordInterval())); // 向catalog同步coord地址的周期。单位:毫秒。
										// 0表示不同步。
		dsOpt.setValidateConnection(poolInfoBean.getValidateConnection()==0? false : true); // 连接出池时，是否检测连接的可用性，默认不检测。
		dsOpt.setConnectStrategy(ConnectStrategy.BALANCE); // 默认使用coord地址负载均衡的策略获取连接。
		
		nwOpt.setMaxAutoConnectRetryTime(Integer.parseInt(poolInfoBean
				.getCo_maxautoconnectretrytime()));// 设置若连接失败，重试次数
		String[] urls1 = poolInfoBean.getUrls().split(",");
		List<String> urls2 = Arrays.asList(urls1);
		SequoiadbDatasource ds = new SequoiadbDatasource(
				urls2, poolInfoBean.getUsername(), poolInfoBean.getPassword(), nwOpt, dsOpt); // 创建连接池
		log.info("--getSequoiadbDatasource(over)");
		return ds;
	}
	
	

	
}
