<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/SunECMConsole3.5_SpringBoot/pom.xml" />
        <option value="$PROJECT_DIR$/SunECMDM3.5_SpringBoot/pom.xml" />
        <option value="$PROJECT_DIR$/SunECMDM_Core/pom.xml" />
        <option value="$PROJECT_DIR$/SunECMConsole_Core/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-SunARS-common/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-实时预警-trt/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-实物档案-fm/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-差错管理-et/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-模型运行-ModelRun/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-电子档案-SunARS-file/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-系统管理-SunARS-system/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-警报规则配置-mc/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-重点监督-supervise/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop/运营风险监测-风险预警-risk/pom.xml" />
        <option value="$PROJECT_DIR$/../后端/sunfa-parent/pom.xml" />
        <option value="$PROJECT_DIR$/../ETL/SunETLConsole/pom.xml" />
        <option value="$PROJECT_DIR$/../ETL/SunETLExecute/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" default="true" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
</project>