import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getInsnoDmsList(data) {
  const url = '/insnoDmsManager/getInsnoDmsList'+EndUrl.EndUrl
  data.model_code = encodeURI(data.model_code);
  data.ins_No = encodeURI(data.ins_No);
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function addInsNoDms(data) {
  const url = '/insnoDmsManager/alterInsNoDms'+EndUrl.EndUrl
  const obj =  {
    'insNo': encodeURI(data.insNo),
    'group_id': encodeURI(data.group_id),
    'model_code': encodeURI(data.model_code),
    'group_name': encodeURI(data.group_name),
    'optionFlag': encodeURI(data.optionFlag),
    'pId': encodeURI(data.pId),
    'nowPID': encodeURI(data.nowPID),
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj ,'optionFlag1':data.optionFlag}
  })
}

export function delInsnoDms(data) {
  return request({
    url: '/insnoDmsManager/delInsnoDms'+EndUrl.EndUrl,
    method: 'post',
    params: { 'nowPID': data.pId }
  })
}







