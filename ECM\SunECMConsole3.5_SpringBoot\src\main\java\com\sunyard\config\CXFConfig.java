
package com.sunyard.config;


import com.sunyard.ws.internalapi.SunEcmConsole;
import org.apache.cxf.Bus;
import org.apache.cxf.jaxws.EndpointImpl;
import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import javax.xml.ws.Endpoint;

@Configuration
@ConditionalOnExpression("#{'false'.equals(environment['dubboIsOn']) || 'both'.equals(environment['dubboIsOn']) || environment['dubboIsOn']==null}")
public class CXFConfig {

    @Autowired
    private Bus bus;

    @Resource(name="sunEcmConsoleImpl")
    private SunEcmConsole SunEcmConsole;


//     * 此方法被注释后:
//     * 去掉注释后：wsdl访问地址为：

    @Bean
    public ServletRegistrationBean disServlet() {
        return new ServletRegistrationBean(new CXFServlet(), "/webservices/*");
    }

//     * 发布服务
//     * 指定访问url
//     *
//     * @return


    @Bean
    public Endpoint userEndpoint() {
        EndpointImpl endpoint = new EndpointImpl(bus, SunEcmConsole);
        endpoint.publish("/WsInterface");
        return endpoint;
    }

}

