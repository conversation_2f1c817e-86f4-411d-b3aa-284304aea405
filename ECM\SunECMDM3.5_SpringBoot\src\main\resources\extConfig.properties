#是否为大数据环境 true 或者 false
is_Big_Data=false
#文件存储至hdfs的大小，默认5m
SAVEHDFS=2000000
#hbase表空间
hbase.namespace=SUNECM
#事务补偿清理几天之前的数据
zk.ago=1
#事务补偿定时(cron表达式)
quartz.zk.clear.undo =0 * 02 * * ?
#是否开启登录校验
hbaseIsLoginCheck=true
#hadoop登录校验文件存放路径
hadoop.security.authentication.dir=/home/<USER>
#hadoop登录校验用户
hadoop.security.user=ecm
#hbase用户
hadoop.user.name=ecm
#zkip和端口
zookeeper.quorum=***********\:24002,***********\:24002