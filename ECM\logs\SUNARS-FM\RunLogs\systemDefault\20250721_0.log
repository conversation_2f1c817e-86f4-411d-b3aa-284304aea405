2025-07-21 14:10:42.169 [] [/] [main] INFO  com.sunyard.aos.FmApplication - The following 1 profile is active: "dev"
2025-07-21 14:10:46.699 [] [/] [main] INFO  c.s.a.c.d.c.DynamicDataSourceLoading - 【加密类型】：AES
2025-07-21 14:10:46.717 [] [/] [main] INFO  c.s.a.c.d.c.DynamicDataSourceLoading - 【加密类型】：AES
2025-07-21 14:10:46.974 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2025-07-21 14:10:47.020 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,db1} inited
2025-07-21 14:10:47.021 [] [/] [main] INFO  c.b.d.d.p.AbstractJdbcDataSourceProvider - 成功加载数据库驱动程序
2025-07-21 14:10:47.029 [] [/] [main] INFO  c.b.d.d.p.AbstractJdbcDataSourceProvider - 成功获取数据库连接
2025-07-21 14:10:47.073 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3,master} inited
2025-07-21 14:10:47.120 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4,db1} inited
2025-07-21 14:10:47.122 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db1] success
2025-07-21 14:10:47.123 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-21 14:10:47.123 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-21 14:10:50.838 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-21 14:10:50.846 [] [/] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-21 14:10:50.846 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-21 14:10:50.847 [] [/] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-21 14:10:50.847 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-21 14:10:50.848 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-21 14:10:50.848 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-21 14:10:50.848 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2ef812b
2025-07-21 14:10:51.061 [] [/] [main] INFO  c.s.a.c.filter.CSRFValidationFilter - 未配置CSRF验证请求源url，不进行Referer请求来源地址验证。
2025-07-21 14:10:51.085 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 开始初始化风险公共内容。
2025-07-21 14:10:51.085 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 数据库驱动名称：org.postgresql.Driver
2025-07-21 14:10:51.087 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 初始化数据库类型VariableArs.dbType = DbTypeEnum{driverClassName='org.postgresql.Driver,', upperCase=false}
2025-07-21 14:10:51.087 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 为DataConnectionUtil设置dataSource
2025-07-21 14:10:51.088 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 初始化风险公共内容完成。
2025-07-21 14:10:52.107 [] [/] [main] INFO  c.s.a.c.d.config.DruidAuthConfig - 是否开启Druid授权访问：false
2025-07-21 14:10:53.159 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - 初始化flow-config加载
2025-07-21 14:10:53.178 [] [/] [main] DEBUG c.s.a.f.dao.NodeMapper.selectAllFlow - ==>  Preparing: select a.value value, a.defaultNode defaultNode, a.id id, b.id bId,b.flowId flowId,b.value bvalue,b.text btext,b.purview purview,b.canBeCancel canBeCancel,b.cancelNode cancelNode,b.canBeClose canBeClose, c.value cvalue,c.text ctext,c.flowNodeId flowNodeId FROM fm_flow a LEFT JOIN fm_flow_node b ON b.flowId = a.id LEFT JOIN fm_next_flow_node c ON c.flowNodeId = b.id
2025-07-21 14:10:53.194 [] [/] [main] DEBUG c.s.a.f.dao.NodeMapper.selectAllFlow - ==> Parameters: 
2025-07-21 14:10:53.220 [] [/] [main] DEBUG c.s.a.f.dao.NodeMapper.selectAllFlow - <==      Total: 7
2025-07-21 14:10:53.221 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - 初始化flow-config完成
2025-07-21 14:10:53.222 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - 初始化log模板
2025-07-21 14:10:53.222 [] [/] [main] DEBUG c.s.a.f.d.L.sysInitLogTemplate - ==>  Preparing: select TYPE "TYPE", INFO "INFO",DETAIL "DETAIL" FROM FM_LOG_TEMPLATE
2025-07-21 14:10:53.222 [] [/] [main] DEBUG c.s.a.f.d.L.sysInitLogTemplate - ==> Parameters: 
2025-07-21 14:10:53.227 [] [/] [main] DEBUG c.s.a.f.d.L.sysInitLogTemplate - <==      Total: 30
2025-07-21 14:10:53.228 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - log模板size为30
2025-07-21 14:10:53.235 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 注入配置  ---------------
2025-07-21 14:10:53.235 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 获取配置文件参数开始  ---------------
2025-07-21 14:10:53.236 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 获取配置文件参数完成！ ---------------
2025-07-21 14:10:53.462 [] [/] [main] INFO  org.redisson.Version - Redisson 3.26.1
2025-07-21 14:10:53.761 [] [/] [redisson-netty-4-4] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-21 14:10:53.776 [] [/] [redisson-netty-4-13] INFO  o.r.connection.ConnectionsHolder - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-21 14:10:56.612 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-07-21 14:10:56.676 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-07-21 14:10:56.676 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-07-21 14:10:56.800 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-07-21 14:10:56.800 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-07-21 14:10:57.168 [] [/] [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:10:57.191 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-07-21 14:10:57.192 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-07-21 14:10:57.192 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-07-21 14:10:57.192 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-07-21 14:10:57.192 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-07-21 14:10:57.193 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-07-21 14:10:57.193 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-07-21 14:10:57.255 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-07-21 14:10:57.258 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-07-21 14:10:57.261 [] [/] [main] INFO  c.n.discovery.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-21 14:10:57.266 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1753078257265 with initial instances count: 6
2025-07-21 14:10:57.575 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - 是否开启实物档案流程控制影像采集开关为false
2025-07-21 14:10:57.969 [] [/] [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-21 14:11:00.451 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1753078260451, current=UP, previous=STARTING]
2025-07-21 14:11:00.454 [] [/] [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNARS-FM/localhost:9009: registering service...
2025-07-21 14:11:00.480 [] [/] [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNARS-FM/localhost:9009 - registration status: 204
2025-07-21 14:11:02.766 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-21 14:11:02.774 [] [/] [main] INFO  c.s.a.c.util.easyExcel.ExportUtil - 获取配置的excel下载文件夹路径/home/<USER>/template/downloadPath/
2025-07-21 14:11:02.775 [] [/] [main] INFO  c.s.a.c.util.easyExcel.ExportUtil - 填充模板文件目录/home/<USER>/template/fillTemplate/
2025-07-21 14:11:02.776 [] [/] [main] INFO  c.s.a.common.util.easyExcel.FillUtil - 填充模板文件目录/home/<USER>/template/fillTemplate/
2025-07-21 14:11:02.776 [] [/] [main] INFO  c.s.a.common.util.easyExcel.FillUtil - 导出文件目录/home/<USER>/template/downloadPath/
2025-07-21 14:11:02.815 [] [/] [main] INFO  com.sunyard.aos.FmApplication - Started FmApplication in 28.411 seconds (JVM running for 30.388)
2025-07-21 14:11:02.824 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 初始化服务「SUNARS-FM」 |获取应用服务信息配置 | 开始执行
2025-07-21 14:11:03.677 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20190129000000000001 | 「地址」127.0.0.1:9007 | 「访问目录」/ | 「服务类型」0 | 「启用状态」1
2025-07-21 14:11:03.677 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20191217154129494011 | 「地址」**************:8089 | 「访问目录」/SunAOS | 「服务类型」2 | 「启用状态」1
2025-07-21 14:11:03.680 [] [/] [main] ERROR c.s.aos.common.init.InitCurrServer - 初始化服务「SUNARS-FM」 |执行失败 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
com.sunyard.aos.common.exception.SunAOSException: 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
2025-07-21 14:11:03.681 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 初始化服务「SUNARS-FM」 |获取应用服务信息配置 | 开始执行
2025-07-21 14:11:04.564 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20190129000000000001 | 「地址」127.0.0.1:9007 | 「访问目录」/ | 「服务类型」0 | 「启用状态」1
2025-07-21 14:11:04.565 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20191217154129494011 | 「地址」**************:8089 | 「访问目录」/SunAOS | 「服务类型」2 | 「启用状态」1
2025-07-21 14:11:04.565 [] [/] [main] ERROR c.s.aos.common.init.InitCurrServer - 初始化服务「SUNARS-FM」 |执行失败 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
com.sunyard.aos.common.exception.SunAOSException: 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
2025-07-21 14:11:06.610 [] [/] [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=SUNARS-FM, managementUrl=http://************:9009/actuator, healthUrl=http://************:9009/actuator/health, serviceUrl=http://************:9009/) at spring-boot-admin ([http://127.0.0.1:8878/admin/instances]): I/O error on POST request for "http://127.0.0.1:8878/admin/instances": Connect to 127.0.0.1:8878 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8878 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-07-21 14:15:57.198 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:20:57.208 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:25:57.219 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:30:57.219 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:35:57.235 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:40:57.243 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:45:57.259 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:50:57.260 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 14:55:57.262 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:00:57.263 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:05:57.277 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:10:57.287 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:15:57.299 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:20:57.303 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:25:57.306 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:30:57.319 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:35:57.332 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:40:57.343 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:45:57.349 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:50:57.359 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 15:55:57.365 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:00:57.379 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:05:57.394 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:10:57.402 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:15:57.418 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:20:57.433 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:25:57.438 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:30:57.451 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:34:39.210 [] [0f31a7f26abaa8bb/1b1c2c7384d45187] [http-nio-9009-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:38 操作结束时间: 2025-07-21 16:34:39!总共花费时间: 563 毫秒！
2025-07-21 16:34:43.226 [] [9a0fa477b8efc7bd/802b8c069e3a53ae] [http-nio-9009-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:43 操作结束时间: 2025-07-21 16:34:43!总共花费时间: 57 毫秒！
2025-07-21 16:34:43.331 [] [aa0581308fa3ca48/1446c3c11e486888] [http-nio-9009-exec-4] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:43 操作结束时间: 2025-07-21 16:34:43!总共花费时间: 76 毫秒！
2025-07-21 16:34:48.347 [] [494e236bfe5fd452/2e6e33580f4ec59a] [http-nio-9009-exec-6] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 移交!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:48 操作结束时间: 2025-07-21 16:34:48!总共花费时间: 80 毫秒！
2025-07-21 16:34:48.423 [] [ff896d07065b806d/250cf3b168cfee0e] [http-nio-9009-exec-7] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:48 操作结束时间: 2025-07-21 16:34:48!总共花费时间: 58 毫秒！
2025-07-21 16:34:54.861 [] [8b32bd64881b2a2e/c0aac5eba37e1e18] [http-nio-9009-exec-9] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:54 操作结束时间: 2025-07-21 16:34:54!总共花费时间: 51 毫秒！
2025-07-21 16:34:59.989 [] [ecca16207fc773d1/7927bc35f07e2c26] [http-nio-9009-exec-11] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 延后接收!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:59 操作结束时间: 2025-07-21 16:34:59!总共花费时间: 53 毫秒！
2025-07-21 16:35:00.077 [] [302205a4893c085b/d568f57563b1935a] [http-nio-9009-exec-12] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:00 操作结束时间: 2025-07-21 16:35:00!总共花费时间: 72 毫秒！
2025-07-21 16:35:34.052 [] [7e15c9c3cd20d2d9/0096601092abf3ab] [http-nio-9009-exec-14] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 回退!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:34 操作结束时间: 2025-07-21 16:35:34!总共花费时间: 52 毫秒！
2025-07-21 16:35:34.146 [] [9ccaaa92d6b11c2e/83ac984f483641f5] [http-nio-9009-exec-15] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:34 操作结束时间: 2025-07-21 16:35:34!总共花费时间: 74 毫秒！
2025-07-21 16:35:51.292 [] [df3c034047d2d849/99943dbfae1d0ec3] [http-nio-9009-exec-17] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:51 操作结束时间: 2025-07-21 16:35:51!总共花费时间: 36 毫秒！
2025-07-21 16:35:51.384 [] [d6c76e16ded50ea9/240d393fd12e3620] [http-nio-9009-exec-18] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:51 操作结束时间: 2025-07-21 16:35:51!总共花费时间: 65 毫秒！
2025-07-21 16:35:56.455 [] [a748766ed11f9cea/969f7a70bb1bd8d4] [http-nio-9009-exec-20] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 打印档案!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:56 操作结束时间: 2025-07-21 16:35:56!总共花费时间: 281 毫秒！
2025-07-21 16:35:57.460 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:36:53.497 [] [900c34cf2c4fa648/cf07b6b7e6fad5ce] [http-nio-9009-exec-22] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:36:53 操作结束时间: 2025-07-21 16:36:53!总共花费时间: 88 毫秒！
2025-07-21 16:36:55.130 [] [d65e9786b6c2a220/93299d4574b413e2] [http-nio-9009-exec-23] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:36:55 操作结束时间: 2025-07-21 16:36:55!总共花费时间: 84 毫秒！
2025-07-21 16:36:55.224 [] [1d3c9f4426e99bde/bd5a3b26abad3402] [http-nio-9009-exec-24] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:36:55 操作结束时间: 2025-07-21 16:36:55!总共花费时间: 60 毫秒！
2025-07-21 16:36:55.669 [] [bd7efc8af42f640d/ebf32bb397beaf3f] [http-nio-9009-exec-25] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:36:55 操作结束时间: 2025-07-21 16:36:55!总共花费时间: 59 毫秒！
2025-07-21 16:37:00.835 [] [d7d72e5ba984f851/587f2030bb6bcdcc] [http-nio-9009-exec-27] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:00 操作结束时间: 2025-07-21 16:37:00!总共花费时间: 58 毫秒！
2025-07-21 16:37:00.942 [] [05f5bc3975f89d5d/7641d726cee52f6d] [http-nio-9009-exec-28] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 库房定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:00 操作结束时间: 2025-07-21 16:37:00!总共花费时间: 84 毫秒！
2025-07-21 16:37:01.067 [] [d51aec50cd63d0e9/56234b78e2d1f32e] [http-nio-9009-exec-29] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 库房架定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:00 操作结束时间: 2025-07-21 16:37:01!总共花费时间: 94 毫秒！
2025-07-21 16:37:01.234 [] [46fc288f7a83cf34/d1b8f8260a159f4e] [http-nio-9009-exec-30] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 装箱操作 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:01 操作结束时间: 2025-07-21 16:37:01!总共花费时间: 132 毫秒！
2025-07-21 16:37:10.860 [] [7f23913522190469/bebce57c82cba154] [http-nio-9009-exec-32] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:10 操作结束时间: 2025-07-21 16:37:10!总共花费时间: 72 毫秒！
2025-07-21 16:37:10.951 [] [fff13169d7fd6fed/792e99638147dfa3] [http-nio-9009-exec-33] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:10 操作结束时间: 2025-07-21 16:37:10!总共花费时间: 62 毫秒！
2025-07-21 16:37:14.617 [] [1559ead1ad899ca3/65b91d809a8d270e] [http-nio-9009-exec-35] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:14 操作结束时间: 2025-07-21 16:37:14!总共花费时间: 98 毫秒！
2025-07-21 16:37:20.414 [] [229b751b44bf12c5/807ebd587b77d683] [http-nio-9009-exec-37] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 根据箱号查询档案!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:20 操作结束时间: 2025-07-21 16:37:20!总共花费时间: 36 毫秒！
2025-07-21 16:40:57.473 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:45:57.488 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:50:57.491 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 16:55:57.500 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:00:57.507 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:05:57.518 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:10:57.519 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:15:57.522 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:20:57.530 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:25:57.532 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:30:57.534 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:35:57.545 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:40:57.559 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:45:57.562 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:50:57.563 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 17:55:57.563 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:00:57.571 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:05:57.579 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:10:57.584 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:15:57.585 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:20:57.592 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:25:57.601 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:30:57.615 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:35:57.616 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:40:57.617 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:45:57.624 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:50:57.639 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 18:55:57.651 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:00:57.663 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:05:57.668 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:10:57.682 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:15:57.693 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:20:57.709 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:25:57.710 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:30:57.713 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:35:57.717 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:40:57.733 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:45:57.740 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:50:57.746 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 19:55:57.756 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:00:57.771 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:05:57.777 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:10:57.785 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:15:57.794 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:20:57.808 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:25:57.818 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:30:57.819 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:35:57.828 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:40:57.836 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:45:57.843 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:50:57.853 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 20:55:57.860 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:00:57.871 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:05:57.886 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:10:57.894 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:15:57.903 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:20:57.915 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:25:57.921 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:30:57.932 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:35:57.936 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:40:57.945 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:45:57.959 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:50:57.968 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 21:55:57.979 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:00:57.990 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:05:57.997 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:10:58.007 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:15:58.022 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:20:58.037 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:25:58.049 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:30:58.054 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:35:58.060 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:40:58.063 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:45:58.079 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:50:58.087 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 22:55:58.096 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:00:58.103 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:05:58.113 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:10:58.120 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:15:58.128 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:20:58.137 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:25:58.145 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:30:58.151 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:35:58.152 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:40:58.167 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:45:58.183 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:50:58.185 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-21 23:55:58.187 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
