<template>
  <div class="app-container">
    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="服务器编号" width="50px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.node_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="服务器名称" width="150px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="服务器IP" width="150px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_ip }}</span>
        </template>
      </el-table-column>

      <el-table-column label="同步状态" width="150px" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.synchro_status==0">同步失败</span>
          <span v-if="row.synchro_status==1">同步成功</span>
          <span v-if="row.synchro_status==2">同步过期</span>
        </template>
      </el-table-column>

      <el-table-column label="配置类型" width="200px" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.config_type==1">服务器组和内容对象关联</span>
          <span v-if="row.config_type==2">内容模型模板</span>
          <span v-if="row.config_type==3">缓存策略</span>
          <span v-if="row.config_type==4">内容存储服务器</span>
          <span v-if="row.config_type==5">日志</span>
          <span v-if="row.config_type==6">存储对象</span>
          <span v-if="row.config_type==7">统一接入服务器</span>
          <span v-if="row.config_type==8">内容模型分表</span>
          <span v-if="row.config_type==9">下发所有配置信息</span>
        </template>
      </el-table-column>

      <el-table-column label="配置信息表" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.config_table }}</span>
        </template>
      </el-table-column>

      <el-table-column label="配置信息编号" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.config_code }}</span>
        </template>
      </el-table-column>

      <el-table-column label="修改类型" width="200px" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.modify_type==1"></span>
          <span v-if="row.config_type==2">删除</span>
          <span v-if="row.config_type==3">修改</span>
          <span v-if="row.config_type==4">启用</span>
          <span v-if="row.config_type==5">禁用</span>
        </template>
      </el-table-column>

      <el-table-column label="同步日期" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.synchro_date }}</span>
        </template>
      </el-table-column>

      <el-table-column label="配置信息修改说明" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.remark }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="230"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button v-if="hasPerm('manuallySyn')" type="primary" size="mini" @click="synchronization(row)">
            手动同步
          </el-button>

          <el-button
            v-if="row.status != 'deleted' && hasPerm('manuallyDel')"
            size="mini"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>


    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination'
  import {getNodeStatusInfo,delSynStatusInfo,send2LowerLevels} from "@/api/synchronizationStatus";


  export default {
    components: { Pagination},
    data() {
      return {
        tableKey: 0,
        list: null,
        total: 0,
        listLoading: true,
        listQuery: {
          page: 1,
          start:0,
          limit: 20,
        }
      }
    },
    created() {
      this.getList()
    },
    methods: {
      getList() {
        this.listLoading = true
        getNodeStatusInfo(this.listQuery).then(response => {
          this.list = response.root
          this.total = Number(response.totalProperty)
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
      handleDelete(row) {
        this.openDelConfirm().then(() => {
          delSynStatusInfo(row).then(response => {
            this.getList()
             this.$notify({
              title: "Success",
              message: "Delete Successfully",
              type: "success",
              duration: 2000,
            });
          })
        })
      },
      openDelConfirm(){
        return this.$confirm(`是否确定删除？`,'提示',{
          confirmButtonText:'确定',
          cancelButtonText:'取消',
          type:'warning'
        })
      },
      synchronization(row) {
        send2LowerLevels(row).then(response => {
          this.getList()
          this.$notify({
              title: "Success",
              message: "加入同步列表成功",
              type: "success",
              duration: 2000,
            });
        })
      }
    }
  }
</script>
