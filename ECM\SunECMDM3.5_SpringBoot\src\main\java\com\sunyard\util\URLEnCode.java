package com.sunyard.util;

import java.io.IOException;
import java.security.SecureRandom;
import java.util.Arrays;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.common.Configuration;


public class URLEnCode {
	private static final URLEnCode instance = new URLEnCode();
	private final static String encoding = "UTF-8";  
	private static final String Algorithm = Configuration.get("algorithm"); // 定义 加密算法,可用 DESede  , AES
	private static final int Keysize = 168; //AES  128 ,  DESede  168
	public final static  Logger logger = LoggerFactory.getLogger(URLEnCode.class);

	public static URLEnCode getInstance() {   
	       return instance;   
	} 
	  /**  
	   * AES加密  
	   *   
	   * @param content  
	   * @param password  
	   * @return  
	   */  
	  public static String encryptAES(String content, String password) throws Exception{   
	      byte[] encryptResult = encrypt(content, password);   
	      String encryptResultStr = parseByte2HexStr(encryptResult);   
	      // BASE64位加密   
	      encryptResultStr = ebotongEncrypto(encryptResultStr);   
	      return encryptResultStr;   
	  }   

	  /**  
	   * AES解密  
	   *   
	   * @param encryptResultStr  
	   * @param password  
	   * @return  
	   */  
	  public static String decryptAES(String encryptResultStr, String password) throws Exception {   
		  try{
			  // BASE64位解密   
		      String decrpt = ebotongDecrypto(encryptResultStr);   
		      byte[] decryptFrom = parseHexStr2Byte(decrpt);   
		      byte[] decryptResult = decrypt(decryptFrom, password);   
		      StringBuffer sbbyte = new StringBuffer();
		      for (int i = 0; i < decryptResult.length; i++) {
				sbbyte.append(decryptResult[i]);
		      }
		      Arrays.fill(decryptResult, new Byte(""));
		      return sbbyte.toString();   
		  }catch (Exception e) {
			  throw e; 
		}
	  }   

	   /**  
	   * 加密字符串  
	   */  
	public static String ebotongEncrypto(String str) throws Exception {
		logger.debug("ebotongEncrypto str=["+str+"]");
		try {
			EncryptUtil util = EncryptUtil.getInstance("UK.dvc");
			String msg = util.Encrypt(str);
			if (msg != null && !msg.equals("")) {
				return msg.replaceAll("\r\n", "").replaceAll("\r", "").replaceAll("\n", "");

			}
		} catch (Exception e) {
			logger.warn("encrypto error,begin to use base 64", e);
		}

		Base64 base64encoder = new Base64();
		String result = str;
		if (str != null && str.length() > 0) {
			try {
				byte[] encodeByte = str.getBytes(encoding);
				result = new String(base64encoder.encode(encodeByte));
			} catch (Exception e) {
				throw e;
			}
		}
		// base64加密超过一定长度会自动换行 需要去除换行符
		String msg = "";
		if (result != null) {
			msg = result.replaceAll("\r\n", "").replaceAll("\r", "").replaceAll("\n", "");
		}
		return msg;
	}   
	  /**  
	   * 解密字符串  
	   */  
	public static String ebotongDecrypto(String str) throws IOException {
		try {
			EncryptUtil util = EncryptUtil.getInstance("UK.dvc");
			String msg = util.deEncrypt(str);
			if (msg != null && !msg.equals("")) {
				return msg;
			}
		} catch (Exception e) {
			logger.error("出错");
		}
		Base64 base64decoder = new Base64();
		byte[] encodeByte = base64decoder.decode(str.getBytes());
		return new String(encodeByte);
	}   
//	public static void main(String[] args) {
//		try {
//			String msg=ebotongDecrypto("ZmlsZVBhdGg9L25ld2RhdGUxMjIvMjAxNzAyLzM3Ni8mZWZmVGltZT0yMDE3MDIyMjE1NDEmZmlsZVR5cGU9anBnJmZpbGVJRD1BOThDQzMxRS0wNTQzLTYwMjgtQjYwQi1FQzkxNzAwNUJGMDcmU0FWRV9OQU1FPUE5OENDMzFFLTA1NDMtNjAyOC1CNjBCLUVDOTE3MDA1QkYwNyZmaWxlU2l6ZT00Nzg1NjQmc2VydmVySUQ9MQ==");
//			System.out.println(msg);
//		} catch (IOException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//	}
	  /**    
	   * 加密    
	   *     
	   * @param content 需要加密的内容    
	   * @param password  加密密码    
	   * @return    
	   */     
	  private static byte[] encrypt(String content, String password) throws Exception{      
	          try {                 
	                  KeyGenerator kgen = KeyGenerator.getInstance(Algorithm);    
	                  //防止linux下 随机生成key   
	                  SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG" );      
	                  secureRandom.setSeed(password.getBytes());      
	                  kgen.init(Keysize, secureRandom);   
	                  //kgen.init(Keysize, new SecureRandom(password.getBytes()));      
	                  SecretKey secretKey = kgen.generateKey();      
	                  byte[] enCodeFormat = secretKey.getEncoded();      
	                   SecretKeySpec key = new SecretKeySpec(enCodeFormat, Algorithm);      
	                   Cipher cipher = Cipher.getInstance(Algorithm);// 创建密码器      
	                   byte[] byteContent = content.getBytes(encoding);      
	                   cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化      
	                   byte[] result = cipher.doFinal(byteContent);      
	                   return result; // 加密      
	           } catch (Exception e) {      
	                   throw e;      
	           }      
	   }     
	 
	 
	   /**解密    
	    * @param content  待解密内容    
	    * @param password 解密密钥    
	    * @return    
	    */     
	   private static byte[] decrypt(byte[] content, String password) throws Exception{      
	           try {      
	                    KeyGenerator kgen = KeyGenerator.getInstance(Algorithm);    
	                  //防止linux下 随机生成key   
	                    SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG" );      
	                    secureRandom.setSeed(password.getBytes());      
	                    kgen.init(Keysize, secureRandom);   
	                    //kgen.init(Keysize, new SecureRandom(password.getBytes()));      
	                    SecretKey secretKey = kgen.generateKey();      
	                    byte[] enCodeFormat = secretKey.getEncoded();      
	                    SecretKeySpec key = new SecretKeySpec(enCodeFormat, Algorithm);                  
	                    Cipher cipher = Cipher.getInstance(Algorithm);// 创建密码器      
	                   cipher.init(Cipher.DECRYPT_MODE, key);// 初始化      
	                   byte[] result = cipher.doFinal(content);      
	                   return result; // 加密      
	           } catch (Exception e) {      
	                   throw e;     
	           }      
	   }     
	 
	   /**将二进制转换成16进制    
	    * @param buf    
	    * @return    
	    */     
	   public static String parseByte2HexStr(byte buf[]) {      
	           StringBuffer sb = new StringBuffer();      
	           for (int i = 0; i < buf.length; i++) {      
	                   String hex = Integer.toHexString(buf[i] & 0xFF);      
	                   if (hex.length() == 1) {      
	                           hex = '0' + hex;      
	                   }      
	                   sb.append(hex.toUpperCase());      
	           }      
	           return sb.toString();      
	   }     
	 
	 
	   /**将16进制转换为二进制    
	    * @param hexStr    
	    * @return    
	    */     
	   public static byte[] parseHexStr2Byte(String hexStr) {      
	           if (hexStr.length() < 1)      
	                   return null;      
	           byte[] result = new byte[hexStr.length()/2];      
	           for (int i = 0;i< hexStr.length()/2; i++) {      
	                   int high = Integer.parseInt(hexStr.substring(i*2, i*2+1), 16);      
	                   int low = Integer.parseInt(hexStr.substring(i*2+1, i*2+2), 16);      
	                   result[i] = (byte) (high * 16 + low);      
	           }      
	           return result;      
	   }     
 
}
