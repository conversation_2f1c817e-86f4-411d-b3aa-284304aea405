package com.sunyard.console.configmanager.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

// 内容模型表信息中的分表信息

/**
 * <p>
 * Title:xstream 内容模型表信息中的分表信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("Table")
public class TableBean {

	@XStreamAsAttribute
	private String TABLE_NAME;// 分表名

	@XStreamAsAttribute
	private String BEGIN_TIME;// 开始时间
	@XStreamAsAttribute
	private String END_TIME;// 结束时间
	public String getTable_name() {
		return TABLE_NAME;
	}
	public void setTable_name(String tableName) {
		TABLE_NAME = tableName;
	}
	public String getBegin_time() {
		return BEGIN_TIME;
	}
	public void setBegin_time(String beginTime) {
		BEGIN_TIME = beginTime;
	}
	public String getEnd_time() {
		return END_TIME;
	}
	public void setEnd_time(String endTime) {
		END_TIME = endTime;
	}
	@Override
	public String toString() {
		return "TableBean [BEGIN_TIME=" + BEGIN_TIME + ", END_TIME=" + END_TIME
				+ ", TABLE_NAME=" + TABLE_NAME + "]";
	}
	
	

}
