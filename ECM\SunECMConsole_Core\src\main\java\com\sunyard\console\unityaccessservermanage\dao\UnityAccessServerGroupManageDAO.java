package com.sunyard.console.unityaccessservermanage.dao;

import com.sunyard.console.contentmodelmanage.bean.TreeBean;
import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerGroupInfoBean;

import java.util.List;

/**
 * 统一接入服务器组管理数据操作接口
 * 
 * <AUTHOR>
 * 
 */
public interface UnityAccessServerGroupManageDAO {
	/**
	 * 获取统一接入服务器组列表,如果参数为空，返回记录条数内的所有list
	 * 
	 * @param groupId
	 *            服务器组id
	 * @param groupName
	 *            服务器组名称
	 * @param start
	 *            开始记录
	 * @param limit总条数
	 * @return 列表
	 */
	public List<UnityAccessServerGroupInfoBean> getUnityAccessServerGroupList(
			int groupId, String groupName, int start, int limit);

	/**
	 * 获取统一接入服务器组列表，如果参数为空，返回所有的list
	 * 
	 * @param groupId
	 *            服务器组id
	 * @param groupNamet
	 *            服务器组名称
	 * @return 列表
	 */
	public List<UnityAccessServerGroupInfoBean> getUnityAccessServerGroupList(
			int groupId, String groupName);

	/**
	 * 校验统一接入服务器组ip和端口是否存在，返回0表示不存在，否则存在
	 * 
	 * @param groupId
	 *            服务器组id
	 * @param serverIp
	 *            服务器组ip
	 * @param httpPort
	 *            http端口
	 * @param socketPort
	 *            socket端口
	 * @return 操作结果，为0标示不存在
	 */
	public int checkServerGroupIPandPort(int groupId, String serverIp,
			int httpPort, int socketPort);

	/**
	 * 增加统一接入服务器组，返回成功与否信息
	 * 
	 * @param bean
	 *            统一接入服务器组bean
	 * @param serverIds
	 *            由服务器id组成的字符串，用","隔开
	 * @return 操作结果
	 */
	public boolean addUnityAccessServerGroup(
			UnityAccessServerGroupInfoBean bean, String serverIds);

	/**
	 * 修改统一接入服务器组，返回成功与否信息
	 * 
	 * @param bean
	 *            统一接入服务器组bean
	 * @param serverIds
	 *            由服务器id组成的字符串，用","隔开
	 * @return 操作结果
	 */
	public boolean updateUnityAccessServerGroup(
			UnityAccessServerGroupInfoBean bean, String serverIds);
	/**
	 * 获取统一接入服务器组关联的服务器树
	 * 
	 * @param groupId
	 *            服务器id
	 * @return 列表
	 */
	public List<TreeBean> getRelUnityAccessServerTree(int groupId);
	/**
	 * 获取未被统一接入服务器组关联的服务器树
	 * 
	 * @return 列表
	 */
	public List<TreeBean> getUnRelUnityAccessServerTree();
	/**
	 * 校验服务器组名称唯一性
	 * 
	 * @param groupId
	 *            组id
	 * @param groupName
	 *            组名称
	 * @return 返回拥有该名称的服务器数目，没有返回0
	 */
	public int checkGroupName(int groupId, String groupName);

}
