package com.sunyard.util.pageTool;

import net.sf.jsqlparser.JSQLParserException;

/**
 * <p>
 * Title: sqlServer数据库查询分页
 * </p>
 * <p>
 * Description: sqlServer数据库查询分页
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SqlServerPageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
		start = start - 1;
		if(start<0){
			start=0;
		}
		int end = start + limit;
		String newSql="";
		try {
			newSql = new SqlServerParse().removeOrderBy(sql);
		} catch (JSQLParserException e) {
			throw new RuntimeException(e);
		}
		String pageSql = "select * from (select  ROW_NUMBER()" + " OVER(order by (select 0)) AS row_num,* FROM " + "("
				+ newSql + ")x ) " + "t where " + "t.row_num >" + start + " and t.row_num<=" + end;
		return pageSql;
	}

	public String getTableSpaceName(String sql, String tableSpaceName) {
		return sql;
	}

	public String getTopSql(String sql, int top) {
		String newSql="";
		try {
			newSql = new SqlServerParse().removeOrderBy(sql);
		} catch (JSQLParserException e) {
			throw new RuntimeException(e);
		}
		return "select top "+top +" * from ("+ newSql+") t"  ;
	}

	public String getRandSql(String sql, int top) {
		return getTopSql(sql, top);
	}

	public String getMaxVersionAndGroupInDMDB() {
		return "getMaxVersionAndGroupInDMDB";
	}

	
	public String getDistinctRandSql(String sql, int top) {
		return getRandSql(sql,top);
	}
}
