package com.sunyard.client.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sunyard.client.bean.converter.ClientStringCustomConverter;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamConverter;

/**
 * 索引信息
 * <AUTHOR>
 *
 */
@XStreamAlias("BatchIndexBean")
public class ClientBatchIndexBean {
	/** 内容ID **/
	@XStreamAsAttribute
	private String CONTENT_ID;
	/** 批次文件数 **/
	@XStreamAsAttribute
	private String AMOUNT;
	/** 批次版本 **/
	@XStreamAsAttribute
	private String VERSION;
	/** 自定义属性 **/
	@XStreamConverter(ClientStringCustomConverter.class)
	private Map<String, String> customMap;
	/** 标签属性 **/

    private List<ClientTagBean> tagBeanList;
 
   
	public List<ClientTagBean> getTagBeanList() {
		return tagBeanList;
	}

	public void setTagBeanList(List<ClientTagBean> tagBeanList) {
		this.tagBeanList = tagBeanList;
	}

	/**
	 *   添加标签
	 * 
	 * @param fangyue
	 *           
	 */
	public void addTagBeanList(ClientTagBean tagBean) {
		if (tagBeanList == null) {
			tagBeanList = new ArrayList<ClientTagBean>();
		}
		this.tagBeanList.add(tagBean);
	}
	
	public String getContentID() {
		return CONTENT_ID;
	}
	/**
	 * 设定内容ID
	 * @param contentID 内容ID
	 */
	public void setContentID(String contentID) {
		this.CONTENT_ID = contentID;
	}
	public String getAmount() {
		return AMOUNT;
	}
	/**
	 * 设定文件数量
	 * @param amount 文件数量
	 */
	public void setAmount(String amount) {
		this.AMOUNT = amount;
	}
	public String getVersion() {
		return VERSION;
	}
	/**
	 * 设定批次版本
	 * @param version 批次版本
	 */
	public void setVersion(String version) {
		this.VERSION = version;
	}
	public Map<String, String> getCustomMap() {
		return customMap;
	}
	/**
	 * 设定自定义属性数组
	 * @param customMap 自定义属性数组
	 */
	public void setCustomMap(Map<String, String> customMap) {
		this.customMap = customMap;
	}
	/**
	 * 添加自定义属性
	 * @param key 属性字段
	 * @param value 属性值
	 */
	public void addCustomMap(String key, String value) {
		if(customMap == null) {
			customMap = new HashMap<String, String>();
		}
		this.customMap.put(key, value);
	}
}