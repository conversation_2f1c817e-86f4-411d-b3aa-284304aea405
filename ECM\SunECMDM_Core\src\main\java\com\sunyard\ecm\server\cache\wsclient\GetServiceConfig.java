package com.sunyard.ecm.server.cache.wsclient;

import com.sunyard.ecm.server.bean.*;
import com.sunyard.ecm.server.dao.SunECMDao;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Title: DM初始化时主动获取配置文件
 * </p>
 * <p>
 * Description: 发送webService请求，主动获得配置文件
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class GetServiceConfig {

    private GetConfigInterface getConfig = new GetConfigInterfaceImpl();
    private final static Logger log = LoggerFactory.getLogger(GetServiceConfig.class);


    /**
     * 主动获取内容模型模板信息
     *
     * @param groupID     服务器组ID
     * @param objectNames 内容模型模版名称列表
     */
    public List<ModelTemplateBean> getModelTemplate(String groupID,
                                                    String[] objectNames) throws SunECMException {
        try {
            List<ModelTemplateBean> list = getConfig.getModelTemplate(
                    objectNames, groupID);
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadModelDoc-->setModelDoc-->GetServiceConfig-->getModelTemplate-->webService异常-->"
                            + e.getMessage());
        }
    }

    /**
     * 获取所有服务器信息
     */
    public List<NodeInfo> getNodeInfo() throws SunECMException {
        try {
            List<NodeInfo> list = getConfig.getContentServer();
            if (list != null) {
                log.debug("list.size=" + list.size());
            } else {
                log.error("list is null,reset list");
                list = new ArrayList<NodeInfo>();
            }
            // 对于统一接入服务器 还需要 查询统一接入的表,获取相应信息
            //TODO 暂时放在此处，以后将此代码移到console处，取消对UA,DM的区别对待
            SunECMDao sunECMDao = (SunECMDao) SpringUtil.getSpringBean("sunECMDao");
            String sql = "SELECT S.*, G.GROUP_ID AS GID , G.GROUP_NAME AS GNAME, G.IP AS GIP, G.HTTP_PORT AS GHTTP , " +
                    "G.SOCKET_PORT AS GSOCKET,G.REMARK AS GREMARK FROM UNITY_ACCESS_SERVER S LEFT JOIN UNITY_ACCESS_SERVER_GROUP G ON S.GROUP_ID = G.GROUP_ID";
            log.debug("get ua sql[" + sql + "]");
            List<Map<String, String>> unityList = sunECMDao.searchSql(sql, null);
            if (unityList != null) {
                log.debug("unityList.size[" + unityList.size() + "]");
                // 将统一接入服务器加入服务器队列
                for (Map<String, String> unityMap : unityList) {
                    NodeInfo unityNode = new NodeInfo();
                    /**
                     * 设计时将统一接入与DM的表分开,serverID可能出现重复,
                     * 统一接入的seriverID添加的前缀UA
                     */
                    unityNode.setServer_id("UA" + String.valueOf(unityMap.get("SERVER_ID")));
                    unityNode.setServer_name(String.valueOf(unityMap.get("SERVER_NAME")));
                    unityNode.setServer_ip(String.valueOf(unityMap.get("SERVER_IP")));
                    unityNode.setHttp_port(String.valueOf(unityMap.get("HTTP_PORT")));
                    unityNode.setSocket_port(String.valueOf(unityMap.get("SOCKET_PORT")));
                    unityNode.setRemark(String.valueOf(unityMap.get("REMARK")));
                    unityNode.setGroup_id("-1");//通过-1区分是否为UA
                    unityNode.setState(String.valueOf(unityMap.get("STATE")));

                    unityNode.setGroup_name(String.valueOf(unityMap.get("GNAME")));
                    unityNode.setGroup_ip(String.valueOf(unityMap.get("GIP")));
                    unityNode.setGroup_http_port(String.valueOf(unityMap.get("GHTTP")));
                    unityNode.setGroup_socketport(String.valueOf(unityMap.get("GSOCKET")));
                    unityNode.setGroup_remark(String.valueOf(unityMap.get("GREMARK")));
                    unityNode.setHTTPS_PORT(String.valueOf(unityMap.get("HTTPS_PORT")));
                    unityNode.setTRANS_PROTOCOL(String.valueOf(unityMap.get("TRANS_PROTOCOL")));
                    list.add(unityNode);
                }
            } else {
                log.warn("ua list is null");
            }
            return list;
        } catch (Exception e) {
            log.error("出错", e);
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadAllNodeInfoTable-->GetServiceConfig-->GetServiceConfig-->getNodeInfo-->webService异常-->"
                            + e);
        }

    }

    /**
     * 获取生命周期策略
     *
     * @param groupID 服务器组ID
     */
    public List<LifeCycleStrategyBean> getLifeCycleStrategy(String groupID)
            throws SunECMException {
        try {
            List<LifeCycleStrategyBean> list = getConfig.getLifeCycleStrategy(groupID);
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadLifeCycleStrategy-->setAllLifeCycleStrategy-->GetServiceConfig-->getLifeCycleStrategy-->webService异常-->"
                            + e.getMessage());
        }

    }

    /**
     * 获取存储对象
     *
     * @param groupID 服务器组ID
     */
    public List<StoreObjectBean> getStoreObject(String groupID)
            throws SunECMException {
        try {
            List<StoreObjectBean> list = getConfig.getStoreObject(groupID);
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadStoreObject-->setStoreObject-->GetServiceConfig-->getStoreObject-->webService异常-->"
                            + e.getMessage());
        }
    }

    /**
     * 获取内容模型列表信息
     *
     * @throws SunECMException
     */
    public List<AllModelMsgBean> getAllModelMsg() throws SunECMException {
        try {
            List<AllModelMsgBean> list = getConfig.getAllModelMsg();
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadModelObjectSet-->setMetadataObjectSet-->GetServiceConfig-->getAllModelMsg-->webService异常-->"
                            + e.getMessage());
        }
    }

    /**
     * 获得参数配置列表信息
     *
     * @throws SunECMException
     */
    public List<AllParamMsgBean> getAllParConfigMsg() throws SunECMException {
        try {
            List<AllParamMsgBean> list = getConfig.getAllParConfigMsg();
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadParConfigSet-->setParamObjectSet-->GetServiceConfig-->getAllParConfigMsg-->webService异常-->"
                            + e.getMessage());
        }
    }

    /**
     * 获取内容模型的表
     */
    public List<ModelTableBean> getModelTable() throws SunECMException {
        try {
            List<ModelTableBean> list = getConfig
                    .getModelTable();
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadModelTable-->setModelTable-->GetServiceConfig-->getModelTable-->webService异常-->"
                            + e.getMessage());
        }
    }

    /**
     * 获取内容服务器组和内容对象对应卷
     *
     * @param groupID 服务器组ID
     * @throws SunECMException
     */

    public List<SGroupModleSetBean> getSgroupmodleSet(String groupID)
            throws SunECMException {
        try {
            List<SGroupModleSetBean> list = getConfig.getSgroupmodleSet(groupID);
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadSGroupModle-->setSgroupmodlemap-->GetServiceConfig-->getSgroupmodleSet-->webService异常-->"
                            + e.getMessage());
        }
    }

    /**
     * 主动获取日志策略
     *
     * @throws SunECMException
     */

    public List<DMLogRuleBean> getLogRule() throws SunECMException {
        try {
            List<DMLogRuleBean> list = getConfig.getLogRule();
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadLogRule-->setRuleMap-->GetServiceConfig-->getLogRule-->webService异常-->"
                            + e.getMessage());
        }
    }

    public void setGetConfig(GetConfigInterfaceImpl getConfig) {
        this.getConfig = getConfig;
    }

    public GetConfigInterface getGetConfig() {
        return getConfig;
    }

    /**
     * 主动获取标签信息
     *
     * @throws SunECMException
     */
    /**
     * 主动获取标签信息
     * @throws SunECMException
     * */
    public List<TagInfoBean> getAllEsInfoBean() throws SunECMException{
        try {
            List<TagInfoBean> list = getConfig.getAllTagInfoBean();
            return list;
//			return new ArrayList<TagInfoBean>();
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadtagInfoBean-->setEsInfoBeans-->GetServiceConfig-->getAllEsInfoBean-->webService异常-->"
                            + e.getMessage());
        }
    }

    public List<AttrDesenRuleBean> getAttrDesenRuleList() throws SunECMException{
        try {
            List<AttrDesenRuleBean> list = getConfig.getAllAttrDesenBean();
            return list;
        } catch (Exception e) {
            throw new SunECMException(
                    SunECMExceptionStatus.WEBSERVICE_EXCEPTION,
                    "--->LoadAttrDesenRule-->setDesenRuleInfoBean-->SetgetAttrDesenRuleList-->webService异常-->"
                            + e.getMessage());
        }
    }
}