package com.sunyard.console.common.ext.form;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: 普通文本框类</p>
 * <p>Description: 存放动态表单中的字符串字段</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class TextField extends Field{
	
	public static final String	WIDTH		= "width";			//文本框宽度
	public static final String	ALLOWBLANK	= "allowBlank";		//是否允许为空，默认为true 
	public static final String	BLANKTEXT	= "blankText";		//空验证失败后显示的提示信息 
	public static final String	EMPTYTEXT	= "emptyText";		//在一个空字段中默认显示的信息 
	public static final String 	GROW		= "grow" ;			//字段是否自动伸展和收缩，默认为false 
	public static final String	GROWMIN		= "growMin";		//收缩的最小宽度 
	public static final String	GROWMAX		= "growMax";		//伸展的最大宽度 
	public static final String	MASKRE		= "maskRe";			//用于过滤不匹配字符输入的正则表达式 
	public static final String	MAXLENGTH	= "maxLength";		//字段允许输入的最大长度 
	public static final String	MAXLENGTHTEXT = "maxLengthText";//最大长度验证失败后显示的提示信息 
	public static final String	MINLENGTH	= "minLength";		//字段允许输入的最小长度 
	public static final String	MINLENGTHTEXT = "minLengthText";//最小长度验证失败后显示的提示信息 
	public static final String	REGEX		= "regex";			//正则表达式 
	public static final String	REGEXTEXT	= "regexText";		//正则表达式验证失败后显示的提示信息 
	public static final String	VTYPE		= "vtype";			//验证类型的名字 
	public static final String	ALPHA		= "alpha";			//限制只能输入字母 
	public static final String	ALPHANUM	= "alphanum";		//限制只能输入字母和数字 
	public static final String	VTYPETEXT	= "vtypeText";		//验证失败时的提示信息 
	public static final String	VALIDATOR	= "validator";		//自定义验证函数 
	public static final String	SELECTONFOCUS = "selectOnFocus";//当字段得到焦点时自动选择已存在的文本，默认为false
	public static final String	XTYPE		= "xtype";			//类型
	public static final String  ID          ="id";				//ID
	
	private Map<String,Object> textField = new HashMap<String,Object>();
	

	public Map<String, Object> getTextField() {
		if(textField != null && textField.get("xtype") == null){
			textField.put("xtype", "textfield");
		}
		return textField;
	}
	/**
	 * 类型
	 * @param xtype
	 */
	public void setXtype(String xtype) {
		textField.put("xtype", xtype);
	}
	/**
	 * 设置属性
	 * @param key
	 * @param value
	 */
	public void setProperties(String key , String value){
		textField.put(key, value);
	}
	public void setProperties(String key , int value){
		textField.put(key, value);
	}
	public void setProperties(String key , boolean value){
		textField.put(key, value);
	}
	public void setProperties_obj(String key , String value){
		textField.put(key, "#"+value+"#");
	}
	/**
	 * 获取某个属性
	 * @param key
	 * @return
	 */
	public Object getProperties(String key){
		return textField.get(key);
	}
	public void removeProperties(String key){
		textField.remove(key);
	}

}
