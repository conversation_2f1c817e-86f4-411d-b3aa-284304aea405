/**
 * 预警详情页面
 */
var risk_warning_detail = new Vue({
    el: "#risk-warning-detail",
    data: {
        title: "",  // 页面标题
        modelName: "",
        showBatchPassButton: false,
        form: {
            form_id: "risk-warning-detail-fm",
            form_class: "ars-form",
            form_data: [],
            buttons: [
                {
                    type: 'button',
                    name: '查询',
                    icon: 'search',
                    id: 'risk-warning-detail-query-btn',
                    class: 'ars-btn btn-primary',
                    method: 'queryDetailData()'
                }
            ]
        },
        // 添加表格配置，参考 queryList.js
        table: {
            table_id: 'detail-table', // 当前页表格ID
            page_id: 'detail-table-page', // 表格页码ID
            table_column: [], // 表格列
            table_data: [], // 表格数据
            current_page: 1 // 当前页码
        },
        // 添加导出按钮配置
        buttons: [
            {'type': 'button', 'name': '批量通过', 'icon': 'check', 'id':'batch-pass-btn', 'class': 'btn-success', 'method': 'batchPass()'},
            {'type': 'button', 'name': '导出', 'icon': 'sign-out', 'id':'risk-warning-export-btn', 'class': 'btn-primary', 'method': ''}
        ],
        variable: {
            modelId: "", // 模型ID
            modelRowId: "", // 行ID
            ishandle: "", // 处理状态
            curTab: "detail", // 当前tab
            showFilter: false, // 是否显示筛选区域
            isInit: true, // 是否初始化表格
            searchFields: [], // 搜索字段
            findFields: {}, // 查找字段
            isHistory: false, // 是否查看历史数据
            formType: "", // 表单类型
            workDate: "", // 工作日期
            workTime: "", // 工作时间
            modelLevel: "", // 模型级别
            imgFields: [] // 用于存储看图相关字段配置
        },
        modelInfo: null // 模型信息
    },
    methods: {
        /**
         * 将字符串转换为函数
         */
        callFn:function(item) {
            var reg1 = /^\w+/g;
            var reg2 = /\(((.|)+?)\)/; //取小括号中的参数
            var fn = item.match(reg1)[0];
            var args = item.match(reg2)[1];
            if(commonBlank(args)){ //函数无参数
                this[fn].apply(this); //调用函数
            }else { //函数有参数
                this[fn].apply(this,args.split(',')); //调用函数
            }
        },

        /**
         * 表单显示隐藏
         */
        formShow: function(event) {
            var obj = event.currentTarget;
            $.CurrentNavtab.find(".cont-container").css("background", "#fff");
            if(this.variable.showFilter) {
                $(obj).closest(".cont-header").find("form").slideUp(10, function() {
                    var header_height = $(risk_warning_detail.$options.el).find(".cont-header").height();
                    $(risk_warning_detail.$options.el).find(".cont-container").css('top', header_height);
                    $(risk_warning_detail.$options.el).find(".cont-container").css('height', $(risk_warning_detail.$options.el).height() - header_height - 35 + 'px');
                    $(window).trigger(BJUI.eventType.resizeGrid);
                });
                this.variable.showFilter = false;
            } else {
                $(obj).closest(".cont-header").find("form").slideDown(10, function() {
                    var header_height = $(risk_warning_detail.$options.el).find(".cont-header").height();
                    $(risk_warning_detail.$options.el).find(".cont-container").css('top', header_height);
                    $(risk_warning_detail.$options.el).find(".cont-container").css('height', $(risk_warning_detail.$options.el).height() - header_height - 35 + 'px');
                    $(window).trigger(BJUI.eventType.resizeGrid);
                });
                this.variable.showFilter = true;
            }
        },

        /**
         * 切换标签页
         */
        toggleTab: function(tab) {
            if(this.variable.curTab !== tab) {
                this.variable.curTab = tab;
                if(tab === 'detail') {
                    this.variable.isHistory = false;
                    this.showBatchPassButton = this.variable.ishandle === '0';
                } else {
                    this.variable.isHistory = true;
                    this.showBatchPassButton = false;
                }
                this.loadTableData();
            }
        },

        /**
         * 返回上一页
         */
        goBack: function() {
            var url = 'static/html/risk/arms/infoProcessing/riskWarningList.html';
            // if(this.variable.isHistory) {
            //     url = 'static/html/risk/arms/infoProcessing/historyRiskWarningList.html';
            // }
            BJUI.ajax('doload', {
                url: url,
                target: arms_main.main_nav.target
            });
        },

        /**
         * 初始化页面
         */
        initPage: function() {
            // 获取模型ID
            this.variable.modelId = arms_main.variable.show_modelId;

            // 获取处理状态
            this.variable.ishandle = arms_main.variable.base_arms_ishandle;

            // 设置批量通过按钮显示
            this.showBatchPassButton = this.variable.ishandle === '0';

            // 获取模型行ID（如果是随机任务或指定任务）
            if(arms_main.variable.model_row_id) {
                this.variable.modelRowId = arms_main.variable.model_row_id;
            }

            // 获取看图字典配置
            this.variable.imgFields = commonFieldData(commonConst("IMAGE_DIC"));

            // 从首页传递的模型信息中获取模型信息
            this.getModelInfo();

            // 添加按钮到页面右下角
            var buttonHtml = '<div class="btn-group" style="position:absolute; bottom:10px; right:15px; z-index:100;">';
            for(var i=0; i<this.buttons.length; i++) {
                var button = this.buttons[i];
                // 批量通过按钮只在未处理状态下显示
                if(button.id === 'batch-pass-btn' && !this.showBatchPassButton) {
                    continue;
                }
                buttonHtml += '<button type="' + button.type + '" id="' + button.id + '" class="btn ' + button.class + '"';
                if(button.method) {
                    buttonHtml += ' onclick="risk_warning_detail.callFn(\'' + button.method + '\')"';
                }
                buttonHtml += '><i class="fa fa-' + button.icon + '"></i> ' + button.name + '</button> ';
            }
            buttonHtml += '</div>';

            // 将按钮添加到页面
            $('#risk-warning-detail').find('.cont-container').append(buttonHtml);

            // 然后获取表格字段信息
            var self = this;
            setTimeout(function() {
                self.getTableFields();
            }, 100);
        },

        /**
         * 获取模型信息
         */
        getModelInfo: function() {
            // 从arms_main中获取模型信息
            if (arms_main.variable.modelInfoList && arms_main.variable.modelInfoList.length > 0) {
                // 遍历模型列表，查找当前模型ID对应的信息
                for (var i = 0; i < arms_main.variable.modelInfoList.length; i++) {
                    var model = arms_main.variable.modelInfoList[i];
                    if (model.model_id == this.variable.modelId) {
                        // 找到匹配的模型信息
                        this.modelName = model.model_name;

                        // 设置页面标题
                        this.title = model.model_name /* + "--" + (this.variable.isHistory ? "历史预警数据" : "预警数据") */;

                        // 保存模型信息到Vue实例
                        this.modelInfo = {
                            "tableName": model.table_name,
                            "modelId": this.variable.modelId,
                            "modelName": model.model_name,
                            "modelType": model.model_type
                        };

                        // 设置模型级别
                        this.variable.modelLevel = model.model_type || '3';
                        return;
                    }
                }
            }
        },

        /**
         * 获取表格字段信息
         */
        getTableFields: function() {
            // 确保已经有模型信息
            if (!this.modelInfo) {
                commonError("模型信息不存在，请先获取模型信息");
                return;
            }

            var msg = {
                "parameterList": [{
                    "model_id": this.variable.modelId,
                    "modelInfo": this.modelInfo  // 添加模型信息到请求参数
                }],
                "sysMap": {}
            };

            var response = commonGet(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getRiskWarningDetailData.do", $.toJSON(msg));
            if(response.retCode == commonConst('HANDLE_SUCCESS')) {
                var fields = response.retMap.exhibitFields || [];
                // 字段名映射处理：将 PRE_TRANS_FLOW 改为 FLOW_ID
                // for(var i=0; i<fields.length; i++) {
                //     if(fields[i].name === 'PRE_TRANS_FLOW') {
                //         fields[i].name = 'FLOW_ID';
                //     }
                // }

                // 处理看图字段，为imgFields添加mappingField属性
                if(this.variable.imgFields && this.variable.imgFields.length > 0) {
                    // 直接设置默认属性
                    $.each(this.variable.imgFields, function(index, imgField) {
                        // 默认使用value作为mappingField
                        imgField.mappingField = imgField.value;
                        imgField.imageFlag = true;
                    });
                }

                // 设置查询字段
                this.setSearchFields(fields);

                // 设置表格列
                this.setTableColumns(fields);

                // 加载表格数据
                this.loadTableData();
            } else {
                commonError(response.retMsg);
            }
        },

        /**
         * 设置查询字段
         */
        setSearchFields: function(fields) {
            // 清空查询字段
            this.form.form_data = [];
            this.variable.findFields = {};

            // 添加查询字段
            for(var i=0; i<fields.length; i++) {
                var field = fields[i];
                if(field.IS_FIND === '1') { // 可查询字段
                    this.form.form_data.push({
                        'type': 'input',
                        'name': field.CH_NAME,
                        'id': 'filter_' + field.NAME,
                        'value': '',
                        'alias': field.NAME
                    });
                    this.variable.findFields[field.NAME] = true;
                }
            }
        },

        /**
         * 设置表格列
         */
        setTableColumns: function(fields) {
            // 表格列
            var columns = [
                {
                    name: 'num',
                    label: '序号',
                    align: 'center',
                    width: 40,
                    finalWidth: true,
                    render: function(value, data) {
                        return ((risk_warning_detail.table.current_page-1)*20)+data.gridIndex + 1;
                    }
                },
                {
                    name: 'OP',
                    label: '操作',
                    align: 'center',
                    width: 150,
                    finalWidth: true,
                    render: function(value, rowData) {
                        var html = '';
                        // 未处理时显示通过按钮和下发按钮
                        console.log(rowData);
                        if(rowData.ISHANDLE === '0') {
                            html += ' <a title="通过" onclick="risk_warning_detail.pass(\'' +
                                risk_warning_detail.variable.modelId + '\',\'' + rowData.MODELROW_ID + '\')">通过<a>';

                            // 添加下发按钮
                            html += ' <a title="下发" onclick="risk_warning_detail.openSlipEt(\'' +
                                risk_warning_detail.variable.modelId + '\',\'' + rowData.MODELROW_ID + '\')">下发<a>';
                        }

                        // 直接添加看图按钮，不做条件判断
                        html += ' <a title="查看影像" data-row-index="' + rowData.gridIndex +
                            '" onclick="risk_warning_detail.showImage(this)">看图<a>';

                        return html;
                    }
                }
            ];

            // 添加模型字段
            for(var i=0; i<fields.length; i++) {
                var field = fields[i];

                // 排除一些系统字段
                if(field.name === 'MODEL_ID' || field.name === 'MODELROW_ID' || field.name === 'ISHANDLE') {
                    continue;
                }

                columns.push({
                    name: field.name,
                    label: field.ch_name,
                    align: field.align || 'center',
                    width: field.width || 80,
                    finalWidth: true,
                    render: function(value) {
                        return value || '';
                    }
                });
            }

            // 添加检查结果列
            columns.push({
                name: 'CHECK_RESULT',
                label: '检查结果',
                align: 'center',
                width: 80,
                finalWidth: true,
                render: function(value, row) {
                    if(row.ISHANDLE === '0') {
                        return '<span class="label label-warning">未处理</span>';
                    } else if(row.ISHANDLE === '1') {
                        return '<span class="label label-success">通过</span>';
                    } else if(row.ISHANDLE === '2') {
                        return '<span class="label label-danger">差错</span>';
                    } else {
                        return '';
                    }
                }
            });

            // 设置表格列
            this.table.table_column = columns;
        },

        /**
         * 加载表格数据
         */
        loadTableData: function() {
            // 确保已经有模型信息
            if (!this.modelInfo) {
                commonError("模型信息不存在，请先获取模型信息");
                return;
            }

            var params = {
                "model_id": this.variable.modelId,
                "ishandle": this.variable.ishandle,
                "is_history": this.variable.isHistory ? '1' : '0',
                "modelInfo": this.modelInfo  // 添加模型信息
            };

            // 如果是随机任务或指定任务，添加行ID
            if(this.variable.modelRowId) {
                params["model_row_id"] = this.variable.modelRowId;
            }

            // 添加查询条件
            this.form.form_data.forEach(function(field) {
                var value = $('#' + field.id).val();
                if(value) {
                    params[field.alias] = value;
                }
            });

            var msg = {
                "parameterList": [params],
                "sysMap": {}
            };

            var response = commonGet(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getRiskWarningDetailData.do", $.toJSON(msg));
            if(response.retCode == commonConst('HANDLE_SUCCESS')) {
                var tableData = response.retMap.detailData || [];

                // 字段名映射处理：将表格数据中的 PRE_TRANS_FLOW 改为 FLOW_ID
                // for(var i=0; i<tableData.length; i++) {
                //     if(tableData[i].PRE_TRANS_FLOW !== undefined) {
                //         tableData[i].FLOW_ID = tableData[i].PRE_TRANS_FLOW;
                //         delete tableData[i].PRE_TRANS_FLOW;
                //     }
                // }

                this.table.table_data = tableData;

                // 初始化表格
                this.initRiskWarningTable();
            } else {
                commonError(response.retMsg);
            }
        },

        /**
         * 初始化表格
         */
        initRiskWarningTable: function() {
            // 判断是初始化还是刷新
            if(this.variable.isInit) {
                commonInitDatagrid(this.table.table_id, {
                    dialogFilterW: 10,
                    showCheckboxcol: true,
                    columns: this.table.table_column,
                    data: this.table.table_data,
                    showPager: true,
                    rowNum: 20
                });
                this.variable.isInit = false;
            } else {
                $("#" + this.table.table_id).datagrid('reload', {data: $.toJSON(this.table.table_data)});
            }
        },

        /**
         * 查询明细数据
         */
        queryDetailData: function() {
            this.loadTableData();
        },

        /**
         * 通过预警
         */
        pass: function(modelId, modelRowId) {
            // 获取表名
            var tableName = this.modelInfo.tableName;

            var msg = {
                "parameterList": [{
                    "model_id": modelId,
                    "model_row_id": modelRowId
                }],
                "sysMap": {
                    "table_name": tableName
                }
            };

            var self = this;

            // 确认弹窗
            BJUI.alertmsg('confirm', '确定要通过该预警信息吗?', {
                okCall: function() {
                    var response = commonAjax(commonConst("SUNDA_RISK")+"/armsExhibitDataController/passRiskWarning.do", $.toJSON(msg));
                    if(response.retCode == commonConst('HANDLE_SUCCESS')) {
                        if(response.retMap.result === '-1') {
                            BJUI.alertmsg('warn', '该预警信息已经被处理!');
                        } else if(response.retMap.result === '0') {
                            BJUI.alertmsg('warn', '锁定失败，该预警信息可能已被其他人锁定!');
                        } else {
                            BJUI.alertmsg('ok', '通过成功!');
                            self.loadTableData(); // 刷新数据
                        }
                    } else {
                        commonError(response.retMsg);
                    }
                }
            });
        },

        /**
         * 批量通过
         */
        batchPass: function() {
            var selectedDatas = $("#" + this.table.table_id).data('selectedDatas');// 获取选中行数据
            if(selectedDatas === undefined || selectedDatas.length === 0) {
                BJUI.alertmsg('warn', '请先选择要批量通过的记录!');
                return;
            }

            // 获取表名
            var tableName = this.modelInfo.tableName;

            // 获取选中的记录的model_row_id数组
            var modelRowIds = [];
            var modelId = this.variable.modelId; // 使用当前模型ID

            for(var i=0; i<selectedDatas.length; i++) {
                if(selectedDatas[i].ISHANDLE === '0') { // 只处理未处理的记录
                    modelRowIds.push(selectedDatas[i].MODELROW_ID);
                }
            }

            if(modelRowIds.length === 0) {
                BJUI.alertmsg('warn', '选中的记录中没有未处理的记录!');
                return;
            }

            var msg = {
                "parameterList": [{
                    "model_id": modelId,
                    "modelrow_ids": modelRowIds
                }],
                "sysMap": {
                    "table_name": tableName
                }
            };

            var self = this;

            // 确认弹窗
            BJUI.alertmsg('confirm', '确定要批量通过选中的 ' + modelRowIds.length + ' 条预警信息吗?', {
                okCall: function() {
                    var response = commonAjax(commonConst("SUNDA_RISK")+"/armsExhibitDataController/batchPassRiskWarning.do", $.toJSON(msg));
                    if(response.retCode == commonConst('HANDLE_SUCCESS')) {
                        BJUI.alertmsg('ok', '批量通过成功!');
                        self.loadTableData(); // 刷新数据
                    } else {
                        commonError(response.retMsg);
                    }
                }
            });
        },

        /**
         * 导出数据
         */
        exportData: function() {
            // 导出按钮
            var formatField = 'occur_date#%yyyy-MM-dd,operator_no#@OPERATOR_NO';
            $('#risk-warning-export-btn').selectExport('init', {
                targetId: this.table.table_id,                          // 需要导出的datagrid控件id
                columns: this.table.table_column,                       // 需要导出的datagrid控件列信息定义
                menu_id: arms_main.variable.menu_id,
                button_id: 'risk-warning-export-btn',                   // 导出按钮ID
                allRecordsHandler: function() {                         // 将上次查询出来的条数传给导出插件
                    return risk_warning_detail.table.table_data;
                },
                exportTitle: '预警信息',
                exportDataUrl: commonConst("SUNDA_RISK") + '/armsExhibitDataController/exportRiskWarningData.do',  // 导出数据url
                exportFormatvalue: formatField,
                getParamHandler: function() {
                    // 导出数据时需要传递的参数
                    var params = {
                        "model_id": risk_warning_detail.variable.modelId,
                        "ishandle": risk_warning_detail.variable.ishandle,
                        "is_history": risk_warning_detail.variable.isHistory ? '1' : '0',
                        "modelInfo": risk_warning_detail.modelInfo
                    };

                    // 如果是随机任务或指定任务，添加行ID
                    if(risk_warning_detail.variable.modelRowId) {
                        params["model_row_id"] = risk_warning_detail.variable.modelRowId;
                    }

                    // 添加查询条件
                    risk_warning_detail.form.form_data.forEach(function(field) {
                        var value = $('#' + field.id).val();
                        if(value) {
                            params[field.alias] = value;
                        }
                    });

                    // 构造符合后端要求的参数格式
                    var requestParams = {
                        "parameterList": [params],
                        "sysMap": {}
                    };

                    return '&params=' + encodeURIComponent($.toJSON(requestParams));
                },
                subMenuLabels: '当前页数据,,,全部数据,',
                noExportColumn: '序号,操作',
                exportFileName: '预警数据_' + risk_warning_detail.modelName + "_" + commonCurrentDateStr() + '.xls',
                direction: 'up',                               // 导出选项弹出方向，支持 向上（up）或 向下（down）
                horizontalDirection: 'right'                   // 导出选项的水平位置，支持右对齐(right),默认不写为左对齐(left)
            });
        },

        /**
         * 下发预警
         * @param modelId 模型ID
         * @param modelRowId 行ID
         */
        openSlipEt: function(modelId, modelRowId) {
            // 检查modelId是否有效，如果无效则尝试从this.variable.modelId获取
            if (!modelId || modelId === 'undefined') {
                modelId = this.variable.modelId;
            }

            // 确保modelId和modelRowId都有效
            if (!modelId || !modelRowId) {
                commonError("无效的模型ID或行ID");
                return;
            }

            // 创建请求参数
            var msg = {
                "parameterList": [{
                    "model_id": modelId,
                    "modelrow_ids": [modelRowId],
                    "formType": "3",                   // 单据类型：3表示预警单
                    "isBatch": "0"                     // 是否批量：0表示单笔
                }],
                "sysMap": {}
            };

            // 获取业务信息
            var data = commonGet(commonConst("SUNDA_RISK") + "/armsExhibitDataController/getRiskWarningDataForSlip.do", $.toJSON(msg));
            if (data.retCode == commonConst('HANDLE_SUCCESS')) {
                // 调用arms_dialog_et显示差错单弹窗
                BJUI.dialog({
                    id: 'arms-dialog-et',
                    title: '下发差错单',
                    url: 'static/html/risk/arms/dialog/dialogEt.html',
                    width: 700,
                    height: 420,
                    loadingmask: true,
                    max: true,
                    onLoad: function() {

                        // 确保modelData存在
                        if (!data.retMap.modelData || data.retMap.modelData.length === 0) {
                            commonError("预警数据为空");
                            BJUI.dialog('closeCurrent', '');
                            return;
                        }

                        // 将获取到的数据传递给arms_dialog_et
                        arms_dialog_et.dialog.left[1].table.table_data = data.retMap.modelData;

                        // 设置基础信息表单
                        arms_dialog_et.dialog.left[0].form_data.forEach(function (item) {
                            if(item.alias=='formTypeName'){
                                // 修改表单类型为下拉列表
                                item.type = 'select';
                                item.value = '2'; // 默认值设为处理单
                                item.data = [
                                    {value: '2', name: '处理单'},
                                    {value: '7', name: '金库流程单'},
                                    {value: '8', name: '网点处理单'}
                                ];
                                item.show = true;
                                item.disabled = false; // 确保不是禁用状态
                                item.readonly = false; // 确保不是只读状态
                                item.required = 'required'; // 设置为必填项（字符串格式）
                                item.allowEmpty = false; // 不允许空选项
                                item.emptyText = null; // 不显示空文本

                                // 直接在DOM准备好后设置下拉框值，不使用延迟
                                arms_dialog_et.$nextTick(function() {
                                    var selectElem = $("#arms-dialog-et-formTypeName");
                                    if (selectElem.length > 0) {
                                        // 触发选择事件，确保默认值被选中
                                        selectElem.val('2').trigger('change');

                                        // 移除bootstrap-select中的空选项
                                        selectElem.parent().find('.dropdown-menu li[data-original-index="0"]').remove();
                                    }
                                });
                            } else if(item.alias=='operationDate'){
                                item.value = commonFormatDate(data.retMap.modelData[0].occur_date);
                            } else if(item.alias=='procDealTime'){
                                item.value = commonCurrentDateFormatStr('yyyy-MM-dd HH:mm:ss');
                            } else if(item.alias=='checkerNo'){
                                item.value = sessionObject.user_no;
                            } else if(item.alias=='netNo'){
                                item.value = commonFormatOrgan(data.retMap.modelData[0].site_no, data.retMap.modelData[0].site_name);
                            } else {
                                item.value = '';
                            }
                            item.show = true;
                        });

                        // 显示业务信息表单
                        arms_dialog_et.dialog.left[1].form = true;

                        // 使用fieldsDefines构建表单数据
                        var customFormData = [];
                        var modelData = data.retMap.modelData[0];
                        var fieldDefs = data.retMap.modelFieldResulets;
                        // 遍历所有字段定义
                        for (var i = 0; i < fieldDefs.length; i++) {
                            var field = fieldDefs[i];
                            var fieldName = field.name || field.NAME;
                            var displayName = field.ch_name || field.CH_NAME;

                            if (!fieldName || !displayName) continue;

                            // 尝试多种可能的键名格式
                            var value = "";
                            if (modelData[fieldName] !== undefined) {
                                value = modelData[fieldName];
                            } else if (modelData[fieldName.toUpperCase()] !== undefined) {
                                value = modelData[fieldName.toUpperCase()];
                            } else if (modelData[fieldName.toLowerCase()] !== undefined) {
                                value = modelData[fieldName.toLowerCase()];
                            }

                            // 确保value不是undefined或null
                            value = value === undefined || value === null ? "" : value;

                            customFormData.push({
                                'type': 'info',
                                'name': displayName,
                                'value': value,
                                'alias': fieldName
                            });
                        }

                        // 使用构建的表单数据
                        arms_dialog_et.dialog.left[1].form_data = customFormData;

                        // 隐藏警报信息栏
                        arms_dialog_et.dialog.left[2].show = false;

                        // 设置相关预警和关联预警模型
                        if (data.retMap.sameSiteCount || data.retMap.sameTellCount || data.retMap.sameAcctCount) {
                            arms_dialog_et.etSameEntry(data.retMap.sameSiteCount, data.retMap.sameTellCount, data.retMap.sameAcctCount);
                        }
                        if (data.retMap.relateList) {
                            arms_dialog_et.etRelateEntry(data.retMap.relateList, modelId);
                        }

                        // 处理表单配置
                        var sendSlipBean = data.retMap.sendSlipBean;
                        if (sendSlipBean) {
                            arms_dialog_et.variable.workDate = sendSlipBean.workDate;
                            arms_dialog_et.dialog.left[0].form_data[1].value = commonFormatDate(sendSlipBean.backDate); // 应反馈日期
                            arms_dialog_et.variable.workTime = sendSlipBean.workTime;
                        }

                        // 处理差错单/预警单表单配置
                        arms_dialog_et.dialog.left[3].show = true;  // 显示差错信息
                        arms_dialog_et.dialog.left[6].show = false; // 隐藏撤销信息
                        arms_dialog_et.dialog.left[7].show = false; // 隐藏补充信息
                        arms_dialog_et.dialog.left[4].show = false; // 隐藏流程处理

                        // 获取差错类型和问题类型列表
                        var sendMsg = {
                            "parameterList": [{"formId":""}],
                            "sysMap": {
                                "other_operation": "saveOrUpdateBusiformTb",
                                "entryId": modelId
                            }
                        };
                        var recordData = commonAjax(commonConst("SUNDA_ET")+"/busiFormController/otherOperation.do", $.toJSON(sendMsg));
                        if (recordData.retCode == commonConst("HANDLE_SUCCESS")) {
                            var slipTypeList = recordData.retMap.slipTypeList;
                            arms_dialog_et.variable.arms_slipTypeList = [];
                            for(var i=0; i<slipTypeList.length; i++) {
                                arms_dialog_et.variable.arms_slipTypeList.push({value:slipTypeList[i], name:slipTypeList[i]});
                            }

                            var sourceTypeList = recordData.retMap.typeList;
                            arms_dialog_et.variable.arms_sourceList = [];
                            for(var j=0; j<sourceTypeList.length; j++) {
                                arms_dialog_et.variable.arms_sourceList.push({value:sourceTypeList[j].SOURCE_TYPE, name:sourceTypeList[j].SOURCE_TYPE});
                            }

                            // 获取管理条线列表
                            var gltxList = recordData.retMap.gltxList;
                            arms_dialog_et.variable.arms_gltxList = [];
                            if (gltxList && gltxList.length > 0) {
                                for(var k=0; k<gltxList.length; k++) {
                                    arms_dialog_et.variable.arms_gltxList.push({
                                        value: gltxList[k].substring(0, gltxList[k].indexOf("-")),
                                        name: gltxList[k]
                                    });
                                }
                            }

                            arms_dialog_et.dialog.left[3].form_data[0].data = arms_dialog_et.variable.arms_slipTypeList; // 差错归类
                            arms_dialog_et.dialog.left[3].form_data[2].data = arms_dialog_et.variable.arms_slipLevelList; // 差错级别
                            arms_dialog_et.dialog.left[5].form_data[0].data = arms_dialog_et.variable.arms_sourceList; // 问题类型

                            // 设置管理条线下拉列表
                            if (arms_dialog_et.dialog.left[3].form_data[5]) {
                                arms_dialog_et.dialog.left[3].form_data[5].data = arms_dialog_et.variable.arms_gltxList;
                            }

                            arms_dialog_et.variable.arms_sourceMap = recordData.retMap.riskSourceMap;
                            arms_dialog_et.variable.arms_riskmarkisrequired = "0";
                        }

                        // 设置提交按钮处理函数
                        $("#arms-dialog-et-submit").on("click", function() {
                            // 直接从select元素获取选择的表单类型
                            var selectedFormType = $("#arms-dialog-et-formTypeName").val();

                            // 如果没有找到，使用默认值
                            if (!selectedFormType) {
                                selectedFormType = "2"; // 默认使用"2"(处理单)
                            }

                            // 保存当前实例的引用
                            var self = risk_warning_detail;

                            // 监听AJAX成功事件，只执行一次
                            $(document).one('ajaxSuccess', function(event, xhr, settings) {
                                if (settings.url.indexOf("/busiFormController/add.do") > -1) {
                                    var response = $.parseJSON(xhr.responseText);
                                    if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                                        // 获取差错单ID
                                        var formIdList = response.retMap.fromIdList;
                                        if (formIdList && formIdList.length > 0) {
                                            // 更新预警状态
                                            self.updateRiskWarningStatus({
                                                modelId: modelId,
                                                modelRowId: modelRowId,
                                                formId: formIdList[0],
                                                formType: selectedFormType,
                                                tableName: data.retMap.modelInfo.table_name
                                            }, function(success, msg) {
                                                if (success) {
                                                    console.log("预警状态更新成功");
                                                } else {
                                                    console.error("预警状态更新失败: " + msg);
                                                }
                                            });
                                        }
                                    }
                                }
                            });

                            // 使用选择的表单类型
                            arms_dialog_et.addSlipForm(modelId, modelRowId, selectedFormType, 0, 0, 0, function(success) {
                                if (success) {
                                    // 提交成功后刷新预警详情表格
                                    setTimeout(function() {
                                        self.loadTableData();
                                    }, 500);
                                }
                            });
                        });

                        // 为表单类型下拉框添加变更事件处理
                        arms_dialog_et.$nextTick(function() {
                            $("#arms-dialog-et-formTypeName").off("change").on("change", function() {
                                var selectedFormType = $(this).val();

                                // 更新显示的文本，确保下拉框显示正确的选项文本
                                var selectedText = "";
                                switch(selectedFormType) {
                                    case "2":
                                        selectedText = "处理单";
                                        break;
                                    case "7":
                                        selectedText = "金库流程单";
                                        break;
                                    case "8":
                                        selectedText = "网点处理单";
                                        break;
                                    default:
                                        selectedText = "处理单";
                                }

                                // 更新下拉框显示的文本
                                $(this).siblings(".filter-option").find(".filter-option-inner-inner").text(selectedText);

                                // 查询对应类型的差错归类列表
                                var msg = {
                                    "parameterList": [{
                                        "belongFlag": selectedFormType,
                                        "activeFlag": "1"
                                    }],
                                    "sysMap": {
                                        "currentPage": -1
                                    }
                                };

                                var data = commonGet(commonConst("SUNDA_ET") + "/slipDefController/query.do", $.toJSON(msg));
                                if (data.retCode == commonConst("HANDLE_SUCCESS")) {
                                    // 提取不重复的差错归类
                                    var slipTypeList = [];
                                    var slipList = data.retMap.returnList;

                                    for (var i = 0; i < slipList.length; i++) {
                                        if (slipTypeList.indexOf(slipList[i].slipType) === -1) {
                                            slipTypeList.push(slipList[i].slipType);
                                        }
                                    }

                                    // 更新差错归类下拉框
                                    var slipTypeSelect = $("#arms-main-slipType-dialog");
                                    slipTypeSelect.empty();
                                    slipTypeSelect.append('<option value=""></option>');

                                    for (var j = 0; j < slipTypeList.length; j++) {
                                        slipTypeSelect.append('<option value="' + slipTypeList[j] + '">' + slipTypeList[j] + '</option>');
                                    }

                                    // 更新Vue数据
                                    var slipTypeOptions = [];
                                    for (var k = 0; k < slipTypeList.length; k++) {
                                        slipTypeOptions.push({value: slipTypeList[k], name: slipTypeList[k]});
                                    }

                                    arms_dialog_et.dialog.left[3].form_data[0].data = slipTypeOptions;

                                    // 重置差错名称、差错级别、处罚金额和处罚分数
                                    $("#arms-main-slipNo-dialog").empty().append('<option value=""></option>').val('').trigger('change');
                                    $("#arms-main-slipLevel-dialog").val('').trigger('change');
                                    $("#arms-main-amerceMoney-dialog").val('');
                                    $("#arms-main-amerceScore-dialog").val('');

                                    // 更新Vue数据
                                    arms_dialog_et.dialog.left[3].form_data[1].data = [];
                                    arms_dialog_et.dialog.left[3].form_data[1].selected = '';
                                    arms_dialog_et.variable.arms_slipList = [];
                                    arms_dialog_et.variable.arms_slipMap = {};
                                } else {
                                    commonError(data.retMsg || "获取差错归类失败");
                                }
                            });

                            // 初始加载时触发一次change事件
                            $("#arms-dialog-et-formTypeName").trigger('change');
                        });
                    }
                });
            } else {
                commonError(data.retMsg || "获取预警数据失败");
            }
        },

        /**
         * 查看影像
         * @param element 点击的按钮元素
         */
        showImage: function(element) {
            try {
                // 获取行索引
                var rowIndex = $(element).data("row-index");

                // 直接从Vue实例的表格数据中获取完整的行数据
                var rowData = this.table.table_data[rowIndex];

                if (!rowData) {
                    commonError("获取影像数据失败");
                    return;
                }

                // 调用新的预警看图详情功能
                this.openRiskWarningImageDetail(rowData);
            } catch (err) {
                commonError("查看影像失败: " + err.message);
            }
        },

        /**
         * 打开预警看图详情
         * @param rowData 行数据
         */
        openRiskWarningImageDetail: function(rowData) {
            // 获取模型ID
            var modelId = rowData.MODEL_ID || this.variable.modelId;
            var modelRowId = rowData.MODELROW_ID || rowData.MODEL_ROW_ID;
            var busiDataDate = rowData.BUSI_DATA_DATE || rowData.OCCUR_DATE;

            // 验证必要参数
            if (!modelId) {
                commonError("模型ID不能为空");
                return;
            }
            if (!modelRowId) {
                commonError("模型行ID不能为空");
                return;
            }

            console.log("看图参数：", {
                model_id: modelId,
                model_row_id: modelRowId,
                busi_data_date: busiDataDate
            });

            var msg = {
                "parameterList": [{
                    "model_id": modelId,
                    "model_row_id": modelRowId,
                    "busi_data_date": busiDataDate
                }],
                "sysMap": {}
            };

            var response = commonPost(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getRiskWarningImageDetail.do", $.toJSON(msg));

            console.log("后端响应：", response);

            if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                // 检查是否有图像数据（学习老系统的处理方式）
                var imageData = response.retMap;
                var tmpDataList = imageData.tmpDataList || [];
                var flowList = imageData.flowList || [];

                if (tmpDataList.length === 0 && flowList.length === 0) {
                    // 没有图像数据，显示提示信息（与老系统一致）
                    commonError("未查询到相应的流水信息，无法看图");
                    return;
                }

                this.displayRiskWarningImageDialog(response.retMap);
            } else {
                console.error("获取看图数据失败：", response.retMsg);
                commonError(response.retMsg);
            }
        },

        /**
         * 处理凭证数据，添加从1开始的递增序号
         * @param dataList 原始数据列表
         * @returns {Array} 处理后的数据列表
         */
        processVoucherData: function(dataList) {
            if (!dataList || dataList.length === 0) {
                return [];
            }

            // 为每条记录添加显示序号，从1开始递增
            return dataList.map(function(item, index) {
                return Object.assign({}, item, {
                    displayIndex: index + 1 // 从1开始的递增序号
                });
            });
        },

        /**
         * 显示预警看图对话框
         * @param data 影像数据
         */
        displayRiskWarningImageDialog: function(data) {
            var flowInfo = data.flowList;
            var npsFlag = flowInfo && flowInfo.length > 0 ? flowInfo[0].NPS_FLAG : null;

            if (!commonBlank(npsFlag) && npsFlag == 1) {
                // 无纸化看图
                np_showFlow(data.flowFields, flowInfo);
                return;
            }

            // 预警看图对话框配置
            common_dialog = {
                'dialog_img': {
                    'title': '预警看图处理',
                    'url': 'static/html/risk/arms/dialog/riskWarningShowImg.html',
                    'dialog_id': 'ars-risk-warning-image-dialog',
                    'table_voucher': {
                        'table_data': this.processVoucherData(data.tmpDataList || []),
                        'table_column': [
                            {name: 'displayIndex', label: '图像序号', align: 'center', width: 50},
                            {name: 'formName', label: '版面名称', align: 'center', width: 100},
                            {name: 'flowId', label: '业务流水', align: 'center', width: 100},
                            {name: 'batchId', label: '批次号', align: 'center', width: 100},
                            {name: 'inccodeinBatch', label: '批内码', align: 'center', width: 80},
                            {name: 'errorFlag', label: '差错标志', align: 'center', width: 80},
                            {name: 'insertDate', label: '录入日期', align: 'center', width: 100},
                            {name: 'fileName', label: '文件名', align: 'center', width: 150}
                        ]
                    },
                    'table_flow': {
                        'table_data': flowInfo || []
                    },
                    'variable': {
                        'image_data': data.tmpDataList || [],
                        'flow_data': flowInfo || [],
                        'batch_data': data.batchList || [],
                        'business_data': data.businessData,
                        'model_info': data.modelInfo,
                        'showOperations': true,
                        'modelId': data.businessData ? (data.businessData.model_id || data.businessData.MODEL_ID) : '',
                        'modelRowId': data.businessData ? (data.businessData.modelrow_id || data.businessData.MODELROW_ID) : '',
                        'tableName': data.tableName || '',
                        'warningData': data.businessData || {},
                        // 添加看图需要的变量
                        'img_batchId': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].batchId : '',
                        'img_inputDate': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].inputDate : '',
                        'img_fileName': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].fileName : '',
                        'img_backFileName': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].backFileName : '',
                        'img_siteNo': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].siteNo : '',
                        'img_contentId': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].contentId : '',
                        'img_imgType': '1',
                        'img_serialNo': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].serialNo : ''
                    }
                }
            };

            // 构建流水表列配置
            var fl_field_col = "";
            if (data.flowFields && data.flowFields.length > 0) {
                for (var i = 0; i < data.flowFields.length; i++) {
                    fl_field_col += ",{name:'" + data.flowFields[i].FIELD_NAME +
                                   "',label:'" + data.flowFields[i].ELSE_NAME +
                                   "',align:'center',finalWidth:true}";
                }
            } else {
                // 如果没有flowFields，使用默认的列配置
                fl_field_col += ",{name:'OCCUR_DATE',label:'发生日期',align:'center',finalWidth:true}";
                fl_field_col += ",{name:'SITE_NO',label:'机构号',align:'center',finalWidth:true}";
                fl_field_col += ",{name:'OPERATOR_NO',label:'操作员号',align:'center',finalWidth:true}";
                fl_field_col += ",{name:'FLOW_ID',label:'流水号',align:'center',finalWidth:true}";
            }
            common_dialog.dialog_img.table_flow.table_column = "[" + fl_field_col.substring(1) + "]";

            // 打开对话框
            arsCommonDialog({
                title: common_dialog.dialog_img.title,
                id: common_dialog.dialog_img.dialog_id,
                url: common_dialog.dialog_img.url,
                max: true,
                onLoad: function ($dialog) {
                    // 对话框加载完成后的处理
                }
            }, 13);
        },

        /**
         * 构建影像URL
         * @param businessData 业务数据
         * @returns {string} 影像URL
         */
        buildImageUrl: function(businessData) {
            var baseUrl = "../imgq/others-module-query!OthershowImg.action";
            var params = [
                "occurDate=" + (businessData.occur_date || businessData.OCCUR_DATE || businessData.busi_data_date || businessData.BUSI_DATA_DATE),
                "siteNo=" + (businessData.site_no || businessData.SITE_NO),
                "operatorNo=" + (businessData.operator_no || businessData.OPERATOR_NO),
                "flowId=" + (businessData.flow_id || businessData.FLOW_ID)
            ];
            return baseUrl + "?" + params.join("&");
        },

        /**
         * 预警挂起操作
         * @param modelRowId 模型行ID
         * @param reason 挂起原因
         */
        suspendRiskWarning: function(modelRowId, reason) {
            var msg = {
                "parameterList": [{
                    "model_row_id": modelRowId,
                    "suspend_reason": reason
                }],
                "sysMap": {}
            };

            var response = commonPost(commonConst("SUNDA_RISK")+"/armsExhibitDataController/suspendRiskWarning.do", $.toJSON(msg));

            if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                commonSuccess("预警挂起成功");
                this.refreshTable();
            } else {
                commonError(response.retMsg);
            }
        },

        /**
         * 取消预警挂起
         * @param modelRowId 模型行ID
         */
        cancelSuspendRiskWarning: function(modelRowId) {
            var msg = {
                "parameterList": [{
                    "model_row_id": modelRowId
                }],
                "sysMap": {}
            };

            var response = commonPost(commonConst("SUNDA_RISK")+"/armsExhibitDataController/cancelSuspendRiskWarning.do", $.toJSON(msg));

            if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                commonSuccess("取消挂起成功");
                this.refreshTable();
            } else {
                commonError(response.retMsg);
            }
        },

        /**
         * 刷新表格数据
         */
        refreshTable: function() {
            // 重新加载表格数据
            this.loadTableData();
        },

        /**
         * 预警处理看图功能（带预警处理操作）
         * @param armsData 预警信息数据
         * @param imgField 看图字段信息
         */
        arsCommonShowRiskImgWithProcess: function(armsData, imgField) {
            // 传入字段信息和字段值信息
            for(var i = 0; i < imgField.length; i++){
                imgField[i].dataValue = armsData[imgField[i].mappingField];
            }

            // 组装查询语句
            var msg = {
                "parameterList": [{}],
                "sysMap": {
                    "imgField": imgField,
                    "oper_type": "queryDaRiskImageInfo",
                    // 添加预警处理相关参数
                    "modelId": armsData.MODEL_ID || this.variable.modelId,
                    "modelRowId": armsData.MODELROW_ID || armsData.MODEL_ROW_ID,
                    "warningData": armsData
                }
            };

            var data = commonPost(commonConst("SUNARS_SYSTEM")+"/riskShowImgController/queryDaRiskImageInfo.do", $.toJSON(msg));
            if (data.retCode == commonConst("HANDLE_SUCCESS")) {
                var flowInfo = data.retMap.flowList;
                var npsFlag = data.retMap.flowList[0].NPS_FLAG;
                if (!commonBlank(npsFlag) && npsFlag == 1) {//无纸化
                    np_showFlow(data.retMap.flowFields, flowInfo);//走无纸化看图
                    return;
                }

                // 看图弹出框配置
                common_dialog = {
                    'dialog_img': {
                        'title': '预警处理看图',
                        'url': 'static/html/risk/arms/dialog/riskWarningShowImg.html',
                        'dialog_id': 'ars-risk-warning-img-dialog',
                        'table_voucher': {
                            'table_data': []
                        },
                        'table_flow': {
                            'table_data': []
                        },
                        'variable': {
                            'batch_data': [],
                            'flow_data': [],
                            'image_data': [],
                            // 添加预警处理相关数据
                            'warningData': armsData,
                            'modelId': armsData.MODEL_ID || this.variable.modelId,
                            'modelRowId': armsData.MODELROW_ID || armsData.MODEL_ROW_ID,
                            'showOperations': true
                        }
                    }
                };

                // 初始化看图数据
                common_dialog.dialog_img.table_voucher.table_data = data.retMap.tmpDataList;
                var fl_field_col = "";
                for (var i = 0; i < data.retMap.flowFields.length; i++) {
                    fl_field_col += ",{name:'" + data.retMap.flowFields[i].name + "',label:'" + data.retMap.flowFields[i].label + "',width:" + data.retMap.flowFields[i].width + ",align:'" + data.retMap.flowFields[i].align + "'}";
                }
                common_dialog.dialog_img.table_flow.table_column = "[" + fl_field_col.substring(1) + "]";
                common_dialog.dialog_img.table_flow.table_data = flowInfo;
                common_dialog.dialog_img.variable.image_data = data.retMap.imageData;

                // 打开对话框
                arsCommonDialog({
                    title: common_dialog.dialog_img.title,
                    id: common_dialog.dialog_img.dialog_id,
                    url: common_dialog.dialog_img.url,
                    max: true,
                    onLoad: function ($dialog) {
                        // 放大、还原 格式化布局
                    }
                }, 13);

            } else {
                commonError(data.retMsg || "获取看图数据失败");
            }
        },

        /**
         * 更新预警信息状态
         * 用于在差错单成功下发后更新预警状态
         * @param {Object} data 包含modelId, modelRowId, formId, formType等
         * @param {Function} callback 回调函数
         */
        updateRiskWarningStatus: function(data, callback) {
            if (!data || !data.modelId || !data.modelRowId || !data.formId || !data.formType) {
                console.error("更新预警状态参数不完整");
                if (typeof callback === 'function') {
                    callback(false, "参数不完整");
                }
                return;
            }

            var msg = {
                "parameterList": [{
                    "model_id": data.modelId,
                    "model_row_id": data.modelRowId
                }],
                "sysMap": {
                    "form_id": data.formId,
                    "form_type": data.formType,
                    "tableName": data.tableName
                }
            };

            var response = commonAjax(commonConst("SUNDA_RISK") + "/armsExhibitDataController/updateRiskWarningStatus.do", $.toJSON(msg));

            if (response.retCode == commonConst('HANDLE_SUCCESS')) {
                console.log("预警状态更新成功: ", data);
                if (typeof callback === 'function') {
                    callback(true, "更新成功");
                }
                // 刷新表格数据
                this.loadTableData();
            } else {
                console.error("预警状态更新失败: ", response.retMsg);
                if (typeof callback === 'function') {
                    callback(false, response.retMsg);
                }
            }
        }
    },
    mounted: function() {
        this.$nextTick(function() {
            try {
                mainCont.subPageSize($(this.$options.el)); // 重置页面布局
                this.initPage(); // 初始化页面
                this.exportData(); // 初始化导出功能
            } catch (err) {
                commonError(err.name + '：' + err.message, '前台执行异常');
            }
        });
    }
});
