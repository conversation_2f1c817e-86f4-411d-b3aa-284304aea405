import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getTokenList(data) {
  return request({
    url: '/safeManage/getTokenListVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: data }
  })
}

export function addTokenInfo(data) {
  // data.server_info = encodeURI(data.server_info);
   const obj = { 
      'ip': data.ip, 
      'isTrend': data.isTrend, 
      'server_info': encodeURI(data.server_info)
    }
  return request({
    url: '/safeManage/addTokenVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function deleteToken(data) {
  return request({
    url: '/safeManage/delTokenVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 
      'ip': data.ip
    }
  })
}

export function deleteMachine(data) {
  return request({
    url: '/safeManage/deleteServerVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 
      'ip_s': data.ip
    }
  })
}