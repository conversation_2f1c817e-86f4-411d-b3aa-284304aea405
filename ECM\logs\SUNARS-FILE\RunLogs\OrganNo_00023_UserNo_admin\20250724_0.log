2025-07-24 16:13:06.251 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:13:06.312 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:13:06.314 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:13:06.314 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:13:06.316 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:13:06.317 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:13:06.319 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:13:06.336 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:13:06.337 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:13:06.343 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:13:06.359 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:13:06.359 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:13:06.359 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:13:06.365 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:13:06.445 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:13:06.445 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:13:06.446 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:13:06.446 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:13:06.460 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:13:06.461 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:13:06.461 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:13:06.463 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:13:06.536 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJW4Z0oAABB5dcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:13:06.537 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:13:06.537 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:13:06.537 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJW4Z0oAABB5dcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:13:06.540 [OrganNo_00023_UserNo_admin] [66ed12056f559406/72611dee446cb0c7] [http-nio-9110-exec-65] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:13:06.562 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJW4Z0oAABB5dcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:13:06.562 [OrganNo_00023_UserNo_admin] [66ed12056f559406/dfcd18b2dddf4555] [http-nio-9110-exec-65] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161306225019，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:07.091 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/c106d49bd8dcafde] [http-nio-9110-exec-67] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:07.104 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/c106d49bd8dcafde] [http-nio-9110-exec-67] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:07.104 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/c106d49bd8dcafde] [http-nio-9110-exec-67] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:07.105 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:07.105 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:07.105 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:07.108 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:07.119 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:07.119 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:07.121 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.129 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:07.129 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:07.129 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:07.130 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:07.159 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:07.160 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:07.160 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:07.160 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:07.161 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:07.161 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:07.161 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:07.163 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:07.207 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:07.208 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:07.208 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:07.208 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:07.210 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/54c047b7e0c312a8] [http-nio-9110-exec-67] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:07.230 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/c106d49bd8dcafde] [http-nio-9110-exec-67] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:07.230 [OrganNo_00023_UserNo_admin] [731cf6b6674f1e71/c106d49bd8dcafde] [http-nio-9110-exec-67] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161407088020，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:07.888 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/8de6b04455b66ef6] [http-nio-9110-exec-68] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:07.911 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/8de6b04455b66ef6] [http-nio-9110-exec-68] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:07.912 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/8de6b04455b66ef6] [http-nio-9110-exec-68] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:07.912 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:07.912 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:07.913 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:07.914 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:07.924 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:07.924 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:07.925 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.933 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:07.934 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:07.934 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:07.935 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:07.960 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:07.960 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:07.961 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:07.961 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:07.963 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:07.963 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:07.963 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:07.964 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:08.003 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:08.003 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:08.004 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:08.004 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:08.006 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/62ad26d429e10597] [http-nio-9110-exec-68] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:08.024 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/8de6b04455b66ef6] [http-nio-9110-exec-68] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:08.024 [OrganNo_00023_UserNo_admin] [63e36fe03f45f4f7/8de6b04455b66ef6] [http-nio-9110-exec-68] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161407885021，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:08.528 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/3556eba9ed3c6a0b] [http-nio-9110-exec-69] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:08.551 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/3556eba9ed3c6a0b] [http-nio-9110-exec-69] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:08.551 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/3556eba9ed3c6a0b] [http-nio-9110-exec-69] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:08.551 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:08.551 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:08.553 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:08.555 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:08.566 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:08.567 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:08.568 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:08.575 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:08.576 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:08.576 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:08.578 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:08.606 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:08.606 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:08.606 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:08.606 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:08.607 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:08.608 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:08.608 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:08.609 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:08.655 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:08.655 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:08.656 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:08.656 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:08.657 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/060c641d9393b31b] [http-nio-9110-exec-69] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:08.670 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/3556eba9ed3c6a0b] [http-nio-9110-exec-69] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:08.671 [OrganNo_00023_UserNo_admin] [87acf394bf78ba88/3556eba9ed3c6a0b] [http-nio-9110-exec-69] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161408525022，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:09.159 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/735c12faafb4cf2e] [http-nio-9110-exec-70] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:09.184 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/735c12faafb4cf2e] [http-nio-9110-exec-70] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:09.184 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/735c12faafb4cf2e] [http-nio-9110-exec-70] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:09.184 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:09.185 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:09.185 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:09.187 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:09.195 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:09.195 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:09.196 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:09.204 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:09.205 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:09.205 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:09.206 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:09.233 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:09.233 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:09.234 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:09.234 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:09.235 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:09.235 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:09.235 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:09.236 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:09.277 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:09.277 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:09.278 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:09.278 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:09.279 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/84a405b5e55c5fdc] [http-nio-9110-exec-70] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:09.289 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/735c12faafb4cf2e] [http-nio-9110-exec-70] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:09.289 [OrganNo_00023_UserNo_admin] [19c71d8414b9aa3f/735c12faafb4cf2e] [http-nio-9110-exec-70] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161409157023，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:09.736 [OrganNo_00023_UserNo_admin] [63847ee46908df67/8fcca51c63726e72] [http-nio-9110-exec-71] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:09.757 [OrganNo_00023_UserNo_admin] [63847ee46908df67/8fcca51c63726e72] [http-nio-9110-exec-71] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:09.757 [OrganNo_00023_UserNo_admin] [63847ee46908df67/8fcca51c63726e72] [http-nio-9110-exec-71] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:09.758 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:09.758 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:09.758 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:09.759 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:09.767 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:09.767 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:09.767 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:09.773 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:09.773 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:09.773 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:09.773 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:09.798 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:09.798 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:09.798 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:09.799 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:09.800 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:09.800 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:09.800 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:09.801 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:09.839 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:09.840 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:09.840 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:09.840 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:09.841 [OrganNo_00023_UserNo_admin] [63847ee46908df67/43b64bb412e0781a] [http-nio-9110-exec-71] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:09.858 [OrganNo_00023_UserNo_admin] [63847ee46908df67/8fcca51c63726e72] [http-nio-9110-exec-71] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:09.858 [OrganNo_00023_UserNo_admin] [63847ee46908df67/8fcca51c63726e72] [http-nio-9110-exec-71] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161409734024，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:10.361 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/99fa126c958762b5] [http-nio-9110-exec-72] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:10.374 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/99fa126c958762b5] [http-nio-9110-exec-72] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:10.374 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/99fa126c958762b5] [http-nio-9110-exec-72] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:10.375 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:10.375 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:10.376 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:10.377 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:10.385 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:10.386 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:10.387 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:10.393 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:10.393 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:10.393 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:10.394 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:10.417 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:10.418 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:10.418 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:10.418 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:10.420 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:10.420 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:10.420 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:10.421 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:10.461 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:10.461 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:10.462 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:10.462 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:10.464 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/cc8b0159d7671812] [http-nio-9110-exec-72] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:10.481 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/99fa126c958762b5] [http-nio-9110-exec-72] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:10.481 [OrganNo_00023_UserNo_admin] [7c2c53fcae689ecd/99fa126c958762b5] [http-nio-9110-exec-72] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161410359025，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:10.995 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/0de8278381e9d1cf] [http-nio-9110-exec-73] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:11.021 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/0de8278381e9d1cf] [http-nio-9110-exec-73] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:11.021 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/0de8278381e9d1cf] [http-nio-9110-exec-73] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:11.021 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:11.022 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:11.022 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:11.023 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:11.032 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:11.033 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:11.033 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:11.039 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:11.039 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:11.039 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:11.040 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:11.065 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:11.065 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:11.065 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:11.066 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:11.067 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:11.067 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:11.067 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:11.068 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:11.105 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:11.105 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:11.105 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:11.106 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:11.107 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/8ad2ecd6ecf53efe] [http-nio-9110-exec-73] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:11.125 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/0de8278381e9d1cf] [http-nio-9110-exec-73] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:11.125 [OrganNo_00023_UserNo_admin] [8e86f1afffd6cd37/0de8278381e9d1cf] [http-nio-9110-exec-73] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161410993026，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:11.674 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/ede28baaee607c10] [http-nio-9110-exec-74] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:11.697 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/ede28baaee607c10] [http-nio-9110-exec-74] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:11.698 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/ede28baaee607c10] [http-nio-9110-exec-74] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:11.698 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:11.698 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:11.699 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:11.699 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:11.708 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:11.708 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:11.709 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:11.715 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:11.715 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:11.715 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:11.716 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:11.739 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:11.739 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:11.739 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:11.739 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:11.741 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:11.741 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:11.741 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:11.743 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:11.778 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:11.778 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:11.779 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:11.779 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:11.780 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/8998ab417c981f72] [http-nio-9110-exec-74] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:11.801 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/ede28baaee607c10] [http-nio-9110-exec-74] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:11.801 [OrganNo_00023_UserNo_admin] [da57141b334dfb33/ede28baaee607c10] [http-nio-9110-exec-74] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161411673027，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:12.534 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/0fc55dcf6384b5ff] [http-nio-9110-exec-75] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:12.543 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/0fc55dcf6384b5ff] [http-nio-9110-exec-75] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:12.544 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/0fc55dcf6384b5ff] [http-nio-9110-exec-75] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:12.544 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:12.544 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:12.545 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:12.545 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:12.551 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:12.553 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:12.557 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:12.564 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:12.564 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:12.564 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:12.565 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:12.587 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:12.587 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:12.588 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:12.588 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:12.590 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:12.590 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:12.590 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:12.591 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:12.625 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:12.625 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:12.626 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:12.626 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:12.627 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/21c71d6a0e3b322d] [http-nio-9110-exec-75] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:12.636 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/0fc55dcf6384b5ff] [http-nio-9110-exec-75] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:12.637 [OrganNo_00023_UserNo_admin] [6f54b58ab40db412/0fc55dcf6384b5ff] [http-nio-9110-exec-75] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161412531028，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:13.421 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/221e5f73a94b7a24] [http-nio-9110-exec-76] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:13.448 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/221e5f73a94b7a24] [http-nio-9110-exec-76] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:13.449 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/221e5f73a94b7a24] [http-nio-9110-exec-76] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:13.449 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:13.449 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:13.450 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:13.450 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:13.458 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:13.458 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:13.459 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:13.466 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:13.466 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:13.466 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:13.467 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:13.492 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:13.492 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:13.493 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:13.493 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:13.494 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:13.494 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:13.494 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:13.495 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:13.531 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:13.531 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:13.531 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:13.532 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:13.533 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/0ac7f4bc27867834] [http-nio-9110-exec-76] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:13.540 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/221e5f73a94b7a24] [http-nio-9110-exec-76] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:13.540 [OrganNo_00023_UserNo_admin] [8733e613d0fdd3d3/221e5f73a94b7a24] [http-nio-9110-exec-76] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161413419029，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:14.201 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/0cfc38f9f6def136] [http-nio-9110-exec-77] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:14.222 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/0cfc38f9f6def136] [http-nio-9110-exec-77] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:14.222 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/0cfc38f9f6def136] [http-nio-9110-exec-77] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:14.223 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:14.223 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:14.224 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:14.225 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:14.232 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:14.232 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:14.232 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:14.239 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:14.239 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:14.240 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:14.240 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:14.266 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:14.267 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:14.267 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:14.267 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:14.269 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:14.269 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:14.269 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:14.270 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:14.311 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:14.311 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:14.312 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:14.312 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:14.313 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/f0d46adee185c6cd] [http-nio-9110-exec-77] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:14.326 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/0cfc38f9f6def136] [http-nio-9110-exec-77] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:14.327 [OrganNo_00023_UserNo_admin] [b66c5f2da5f14e3b/0cfc38f9f6def136] [http-nio-9110-exec-77] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161414200030，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:14.908 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/26cbb0efbdf93876] [http-nio-9110-exec-78] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:14.918 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/26cbb0efbdf93876] [http-nio-9110-exec-78] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:14.919 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/26cbb0efbdf93876] [http-nio-9110-exec-78] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:14.919 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:14.919 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:14.920 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:14.921 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:14.927 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:14.928 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:14.928 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:14.937 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:14.937 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:14.937 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:14.938 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:14.961 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:14.961 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:14.962 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:14.962 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:14.963 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:14.964 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:14.964 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:14.965 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:15.001 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:15.001 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:15.001 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:15.003 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:15.005 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/c03aebf0914d3141] [http-nio-9110-exec-78] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:15.012 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/26cbb0efbdf93876] [http-nio-9110-exec-78] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:15.012 [OrganNo_00023_UserNo_admin] [9a5d6aeae2a84607/26cbb0efbdf93876] [http-nio-9110-exec-78] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161414906031，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:15.853 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/25baf1f5e77f06a5] [http-nio-9110-exec-79] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:15.869 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/25baf1f5e77f06a5] [http-nio-9110-exec-79] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:15.870 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/25baf1f5e77f06a5] [http-nio-9110-exec-79] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:15.870 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:15.870 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:15.871 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:15.872 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:15.878 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:15.878 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:15.879 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:15.885 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:15.885 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:15.886 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:15.886 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:15.909 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:15.910 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:15.910 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:15.910 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:15.912 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:15.912 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:15.912 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:15.912 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:15.947 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:15.948 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:15.948 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:15.948 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:15.951 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/65496f76d969908b] [http-nio-9110-exec-79] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:15.972 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/25baf1f5e77f06a5] [http-nio-9110-exec-79] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:15.973 [OrganNo_00023_UserNo_admin] [5876175cc56f57a3/25baf1f5e77f06a5] [http-nio-9110-exec-79] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161415850032，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:14:17.075 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/6aac171d40477639] [http-nio-9110-exec-80] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:14:17.085 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/6aac171d40477639] [http-nio-9110-exec-80] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:14:17.085 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/6aac171d40477639] [http-nio-9110-exec-80] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:14:17.085 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:14:17.086 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:14:17.086 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:14:17.087 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:14:17.093 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:14:17.093 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:14:17.099 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:17.105 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:14:17.105 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:17.105 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:17.106 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:17.136 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:14:17.136 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:17.136 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:14:17.136 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:14:17.138 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:14:17.138 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:14:17.138 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:14:17.139 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:14:17.175 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:14:17.175 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:14:17.175 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:14:17.176 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:14:17.177 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/83e801278f8fb623] [http-nio-9110-exec-80] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:14:17.190 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/6aac171d40477639] [http-nio-9110-exec-80] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJ71Ub28HRDXpcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:14:17.191 [OrganNo_00023_UserNo_admin] [4a46b258ca270cda/6aac171d40477639] [http-nio-9110-exec-80] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724161417074033，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:32:06.888 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/ad008669a3254021] [http-nio-9110-exec-83] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:32:06.899 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/ad008669a3254021] [http-nio-9110-exec-83] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:32:06.899 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/ad008669a3254021] [http-nio-9110-exec-83] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:32:06.899 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:32:06.900 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:32:06.900 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:32:06.901 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:32:06.913 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:32:06.914 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:32:06.915 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:32:06.922 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:32:06.923 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:32:06.923 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:32:06.924 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:32:06.957 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:32:06.957 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:32:06.958 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:32:06.958 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:32:06.960 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:32:06.960 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:32:06.960 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:32:06.962 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:32:07.005 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJC9ZlZOJD5GZcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:32:07.005 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:32:07.006 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:32:07.006 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJC9ZlZOJD5GZcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:32:07.008 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/509f8eb9f624d77b] [http-nio-9110-exec-83] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:32:07.021 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/ad008669a3254021] [http-nio-9110-exec-83] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJC9ZlZOJD5GZcKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:32:07.022 [OrganNo_00023_UserNo_admin] [710f353ebfc9a454/ad008669a3254021] [http-nio-9110-exec-83] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724163206886034，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:40:34.558 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/4c720fa7b789d7bd] [http-nio-9110-exec-87] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:40:34.674 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/4c720fa7b789d7bd] [http-nio-9110-exec-87] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:40:34.675 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/4c720fa7b789d7bd] [http-nio-9110-exec-87] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:40:34.675 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg
2025-07-24 16:40:34.675 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:40:34.676 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String), 00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg(String)
2025-07-24 16:40:34.676 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:40:34.685 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:40:34.685 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:40:34.686 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:40:34.694 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:40:34.695 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:40:34.695 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:40:34.696 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:40:34.726 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:40:34.727 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:40:34.727 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:40:34.728 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:40:34.730 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:40:34.730 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:40:34.730 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:40:34.731 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:40:34.785 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJfMgTI4suWe5cKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:40:34.786 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:40:34.786 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:40:34.786 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="628857F1-9CB8-4369-616B-4A72BB661F4D" SAVE_NAME="628857F1-9CB8-4369-616B-4A72BB661F4D" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/628857F1-9CB8-4369-616B-4A72BB661F4D" OPTION_TYPE="1" FILE_SIZE="1016952" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJfMgTI4suWe5cKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00003-ACD2ABBC-E407-46c5-8C83-FF1570C7274F-A.jpg</string></TRUENAME>            <FILEMD5><string>72E18E33C052D8DFEC440ACE591842C3                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00003_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:40:34.788 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/1891251e31245e08] [http-nio-9110-exec-87] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:40:34.810 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/4c720fa7b789d7bd] [http-nio-9110-exec-87] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4zHKdVIQgnrYS5rf3EVxjrFvpMjXIYFSQGWYb9xqsvvVLJg6IhHYq5OaiIuURDQXo15mVVW5D2+4K8h0I2qdqiEVoPKVojidJfMgTI4suWe5cKF47O4NFzaIuvhNQQcZ7F1xW+MhVOSFaK7XHCL4n+XFpgQLhRNZc0IIWS75y/z5rW03JqiTDQkuniYw2l4Fd1rvMoiSBuNhp1b9W5nRTbp1szsA8x6vKyjxSh1wHAMDBa16xW+/OYYb6CVcMMxd7zCeuYztYfAU="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:40:34.810 [OrganNo_00023_UserNo_admin] [36da0f8fefdc62ff/4c720fa7b789d7bd] [http-nio-9110-exec-87] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724164034556035，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 16:55:16.769 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/4de4f643d9fc3924] [http-nio-9110-exec-91] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 16:55:16.777 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/4de4f643d9fc3924] [http-nio-9110-exec-91] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 16:55:16.778 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/4de4f643d9fc3924] [http-nio-9110-exec-91] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 16:55:16.778 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg
2025-07-24 16:55:16.779 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 16:55:16.779 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String)
2025-07-24 16:55:16.780 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 16:55:16.789 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 16:55:16.789 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 16:55:16.789 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:55:16.797 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 16:55:16.798 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:55:16.798 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:55:16.799 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:55:16.830 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 16:55:16.830 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:55:16.831 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 16:55:16.831 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 16:55:16.832 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 16:55:16.833 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 16:55:16.833 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 16:55:16.834 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 16:55:16.869 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jLg5uzA/g1hTO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 16:55:16.869 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 16:55:16.870 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 16:55:16.871 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jLg5uzA/g1hTO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 16:55:16.873 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/9e1becc643bf5941] [http-nio-9110-exec-91] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 16:55:16.883 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/4de4f643d9fc3924] [http-nio-9110-exec-91] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jLg5uzA/g1hTO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 16:55:16.884 [OrganNo_00023_UserNo_admin] [c85d501c35d423b4/4de4f643d9fc3924] [http-nio-9110-exec-91] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724165516766036，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 20:14:19.928 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/ba9e9244d30f0829] [http-nio-9110-exec-95] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 20:14:19.937 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/ba9e9244d30f0829] [http-nio-9110-exec-95] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 20:14:19.937 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/ba9e9244d30f0829] [http-nio-9110-exec-95] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 20:14:19.937 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg
2025-07-24 20:14:19.938 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 20:14:19.939 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String)
2025-07-24 20:14:19.940 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 20:14:19.952 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 20:14:19.953 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 20:14:19.953 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:14:19.962 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 20:14:19.962 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 20:14:19.962 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 20:14:19.964 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 20:14:20.426 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 20:14:20.426 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 20:14:20.427 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 20:14:20.427 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 20:14:20.430 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 20:14:20.430 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 20:14:20.430 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 20:14:20.431 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 20:14:20.469 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jv/PgDekbUiXO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 20:14:20.469 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 20:14:20.470 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 20:14:20.470 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jv/PgDekbUiXO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 20:14:20.473 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/d7315d2eb28608ab] [http-nio-9110-exec-95] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 20:14:20.494 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/ba9e9244d30f0829] [http-nio-9110-exec-95] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jv/PgDekbUiXO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 20:14:20.494 [OrganNo_00023_UserNo_admin] [4955f9c42a67db07/ba9e9244d30f0829] [http-nio-9110-exec-95] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724201419924037，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 20:14:37.430 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/90a88c01e18eaea9] [http-nio-9110-exec-99] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 20:14:37.438 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/90a88c01e18eaea9] [http-nio-9110-exec-99] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 20:14:37.438 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/90a88c01e18eaea9] [http-nio-9110-exec-99] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 20:14:37.438 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg
2025-07-24 20:14:37.439 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 20:14:37.439 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String)
2025-07-24 20:14:37.440 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 20:14:37.453 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 20:14:37.454 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 20:14:37.455 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:14:37.466 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 20:14:37.466 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 20:14:37.467 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 20:14:37.468 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 20:14:37.538 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 20:14:37.539 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 20:14:37.540 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 20:14:37.540 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 20:14:37.543 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 20:14:37.543 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 20:14:37.543 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 20:14:37.544 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 20:14:37.594 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jv/PgDekbUiXO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 20:14:37.594 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 20:14:37.594 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 20:14:37.595 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jv/PgDekbUiXO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 20:14:37.597 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/191449d45019c758] [http-nio-9110-exec-99] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 20:14:37.621 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/90a88c01e18eaea9] [http-nio-9110-exec-99] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jv/PgDekbUiXO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 20:14:37.621 [OrganNo_00023_UserNo_admin] [0a16db8889917aa9/90a88c01e18eaea9] [http-nio-9110-exec-99] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724201437427038，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-24 20:17:32.907 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/3ee619735c4ca37a] [http-nio-9110-exec-3] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-24 20:17:32.962 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/3ee619735c4ca37a] [http-nio-9110-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-24 20:17:32.963 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/3ee619735c4ca37a] [http-nio-9110-exec-3] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-24 20:17:32.963 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg
2025-07-24 20:17:32.964 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-24 20:17:32.964 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String)
2025-07-24 20:17:32.965 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-24 20:17:32.979 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-24 20:17:32.980 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-24 20:17:32.981 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:17:32.990 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-24 20:17:32.991 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 20:17:32.992 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 20:17:32.993 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 20:17:33.021 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-24 20:17:33.022 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 20:17:33.022 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-24 20:17:33.022 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-24 20:17:33.024 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-24 20:17:33.025 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-24 20:17:33.025 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-24 20:17:33.025 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-24 20:17:33.075 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jceC9K+3oxzbO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-24 20:17:33.075 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-24 20:17:33.076 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-24 20:17:33.076 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jceC9K+3oxzbO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-24 20:17:33.078 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/b2ed8a7d04f46dfe] [http-nio-9110-exec-3] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-24 20:17:33.101 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/3ee619735c4ca37a] [http-nio-9110-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jceC9K+3oxzbO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-24 20:17:33.102 [OrganNo_00023_UserNo_admin] [a5e879cdb14f1ff5/3ee619735c4ca37a] [http-nio-9110-exec-3] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250724201732903039，清空日志输出文件标识：OrganNo_00023_UserNo_admin
