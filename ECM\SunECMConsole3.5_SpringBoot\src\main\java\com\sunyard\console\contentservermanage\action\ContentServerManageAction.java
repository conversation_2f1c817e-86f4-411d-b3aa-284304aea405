package com.sunyard.console.contentservermanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.contentservermanage.dao.ContentServerManageDAO;
import com.sunyard.console.monitor.monitorCenter.singleton.LazySingleton;
import com.sunyard.console.threadpoool.IssueUtils;
import com.sunyard.ws.comm.License;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>
 * Title: 内容存储服务器管理Action
 * </p>
 * <p>
 * Description: 内容存储服务器管理
 * </p>
 * <p>
 * Copyright: Copyright (c) 2012
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@Controller
public class ContentServerManageAction extends BaseAction {
	@Autowired
	ContentServerManageDAO contentServerManageDao;
	private int server_id;// 服务器代码
	private String server_name;// 服务器名称
	private String server_ip;// 服务器ip
	private int http_port;// http端口
	private String remark;// 注释
	private int state;// 状态
	private int isdb_conn;//
	private int socket_port;// socket端口
	private int group_id;// 服务器组代码
	private int weight;// 权重
	private int start;
	private int limit;
	private String optionFlag;// 新增or修改标识
	private String server_ids;// 启用和禁用的server_id字符串
	private String value;// 校验唯一性
	private int https_port;// https端口
	private String trans_protocol;//传输协议
	/**
	 * 日志对象
	 */
	private final static Logger log = LoggerFactory.getLogger(ContentServerManageAction.class);
	public ContentServerManageDAO getContentServerManageDao() {
		return contentServerManageDao;
	}


	public String getTrans_protocol() {
		return trans_protocol;
	}


	public void setTrans_protocol(String trans_protocol) {
		this.trans_protocol = trans_protocol;
	}


	public void setContentServerManageDao(
			ContentServerManageDAO contentServerManageDao) {
		this.contentServerManageDao = contentServerManageDao;
	}

	public int getServer_id() {
		return server_id;
	}

	public void setServer_id(int serverId) {
		server_id = serverId;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getServer_name() {
		return server_name;
	}

	public void setServer_name(String serverName) {
		server_name = serverName;
	}

	public String getServer_ip() {
		return server_ip;
	}

	public void setServer_ip(String serverIp) {
		server_ip = serverIp;
	}

	public int getHttp_port() {
		return http_port;
	}

	public void setHttp_port(int httpPort) {
		http_port = httpPort;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public int getIsdb_conn() {
		return isdb_conn;
	}

	public void setIsdb_conn(int isdbConn) {
		isdb_conn = isdbConn;
	}

	public int getSocket_port() {
		return socket_port;
	}

	public void setSocket_port(int socketPort) {
		socket_port = socketPort;
	}

	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int groupId) {
		group_id = groupId;
	}

	public int getWeight() {
		return weight;
	}

	public void setWeight(int weight) {
		this.weight = weight;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	public String getServer_ids() {
		return server_ids;
	}

	public void setServer_ids(String serverIds) {
		server_ids = serverIds;
	}
	

	public int getHttps_port() {
		return https_port;
	}

	public void setHttps_port(int https_port) {
		this.https_port = https_port;
	}

	/**
	 * 分页查询内容存储服务器list，将结果集组合成jsonStr，返回给页面
	 * 
	 * @return ContentServerInfoBean
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getContentServerListAction.action", method = RequestMethod.POST)
	public String getContentServerList(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		String server_id = (String) modelJson.getOrDefault("server_id", "");
		server_id = "".equals(server_id) || server_id == null ? "0" : server_id;
		String server_name = (String) modelJson.getOrDefault("server_name", "");
		server_name = "undefined".equals(server_name) || server_name == null ? "" : server_name;

		start = (page_int-1) * limit_int;
		try {
			server_name = URLDecoder.decode(server_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode contentServer fields error, server_name=" + server_name,e1);
		}
		log.info("--getContentServerList(start)-->server_id:" + server_id + ";server_name:" + server_name);
		String jsonStr = null;
		try {
			List<ContentServerInfoBean> contentServerInfoList = contentServerManageDao
					.getContentServerList(Integer.valueOf(server_id), server_name, start + 1,
							limit_int);
			List<ContentServerInfoBean> AllInfoList = contentServerManageDao
					.getContentServerList(Integer.valueOf(server_id), server_name);
			int size = 0;
			if (AllInfoList != null && AllInfoList.size() > 0) {
				size = AllInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					contentServerInfoList, size, new ContentServerInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器管理->查询服务器列表失败：" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getContentServerList(over)" );
		return null;
	}
	
	/**
	 * 查询内容存储服务器list,不分页查询全部，将结果集组合成jsonStr，返回给页面
	 * 
	 * @return ContentServerInfoBean
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getAllContentServerListAction.action", method = RequestMethod.POST)
	public String getAllContentServerList() {
		log.info("--getAllContentServerList(start)" );
		String jsonStr = null;
		JSONObject jsonResp=new JSONObject();
		server_id = 0;
		server_name = null;
		try {
			List<ContentServerInfoBean> AllInfoList = contentServerManageDao
					.getContentServerList(Integer.valueOf(server_id), server_name);
			if(AllInfoList == null || AllInfoList.size() == 0){
				log.debug( "--AllInfoList--> AllInfoList is null or AllInfoList.size == 0");
				jsonResp.put("result", "failure");
				jsonResp.put("failure", "true");
				jsonResp.put("message", "没有查询到相应的信息");
				jsonResp.put("totalProperty","0");//总行数
				jsonResp.put("root","");//总行数
			}else{
				jsonResp.put("code", 20000);//TODO mock
				jsonResp.put("result", "success");
				jsonResp.put("success", "true");
				JSONArray jsonArray=new JSONArray();
				for (int i = 0; i < AllInfoList.size(); i++) {
					JSONObject jsonObj = new JSONObject();
					jsonObj.put("server_id", AllInfoList.get(i).getServer_id());
					jsonObj.put("server_name", AllInfoList.get(i).getServer_name());
					jsonObj.put("weight", AllInfoList.get(i).getWeight());
					jsonArray.add(jsonObj);
				}
				jsonResp.put("root", jsonArray);
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器管理->查询服务器列表失败：" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getAllContentServerList(over)" );
		return null;
	}

	/**
	 * 新增内容存储服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/addContentServerAction.action", method = RequestMethod.POST)
	public String addContentServer(String data) {
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = (String) modelJson.getOrDefault("server_id", "");
		String server_name = (String) modelJson.getOrDefault("server_name", "");
		String group_id = (String) modelJson.getOrDefault("group_id", "");
		int http_port = modelJson.getInt("http_port");
		int isdb_conn = modelJson.getInt("isdb_conn");
		String remark = (String) modelJson.getOrDefault("remark", "");
		int socket_port = modelJson.getInt("socket_port");
		int state = modelJson.getInt("state");
		String weight = (String) modelJson.getOrDefault("weight", "");
		String server_ip = (String) modelJson.getOrDefault("server_ip", "");
		int https_port = modelJson.getInt("https_port");
		String trans_protocol = (String) modelJson.getOrDefault("trans_protocol", "");
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");

		server_id = "".equals(server_id) || server_id == null ? "0" : server_id;
		weight = "".equals(weight) || weight == null ? "0" : weight;
		group_id = "".equals(group_id) || group_id == null ? "0" : group_id;
		
		log.info("--addContentServer(start)-->server_id:" + server_id + ";server_name:" + server_name);
		ContentServerInfoBean bean = new ContentServerInfoBean();
		bean.setGroup_id(Integer.valueOf(group_id));
		bean.setHttp_port(http_port);
		bean.setIsdb_conn(isdb_conn);
		try {
			remark = URLDecoder.decode(remark, "utf-8");
			server_name = URLDecoder.decode(server_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode contentServer fields error, remark=" + remark
					+ ", server_name=" + server_name,e1);
		}
		bean.setRemark(remark);
		bean.setServer_id(Integer.valueOf(server_id));
		bean.setServer_name(server_name);
		bean.setSocket_port(socket_port);
		bean.setState(state);
		bean.setWeight(Integer.valueOf(weight));
		bean.setServer_ip(server_ip);
		bean.setHttps_port(https_port);
		bean.setTrans_protocol(trans_protocol);
		try {
			log.debug( "--addContentServer-->optionFlag:" + optionFlag );
			int result = 0;
			if(checkServerNameVue(server_name, server_id)){
				if(checkIPandPortVue(server_ip, http_port, socket_port, server_id)){
					if (optionFlag != null && optionFlag.equals("create1")) {// 新增服务器信息
						result = contentServerManageDao.addContentServer(bean);
					} else if (optionFlag != null && optionFlag.equals("update1")) {// 修改服务器信息
						result = contentServerManageDao.updateContentServer(bean);
						if (contentServerManageDao
						.getContentServerGroupInfoByServerId(bean
								.getServer_id()) != null) {
							Set<Integer> set=new HashSet<Integer>();;
							/*// 如果关联服务器组，需要下发服务器修改信息
							ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentServerThread(server_id, 3)));*/
							set.add(Integer.valueOf(server_id));
							IssueUtils.IssueContentServerGroupInfo(set);
						}
					}
					log.debug( "--addContentServer-->result:" + result );
					if (result != 0) {
						// 重新加载服务器列表信息
						LazySingleton.getInstance().resetServerList();
						jsonResp.put("success", true);
						jsonResp.put("message", "配置内容存储服务器成功!!");
						jsonResp.put("code", 20000);//TODO mock
					} else {
						jsonResp.put("success", false);
						jsonResp.put("message", "配置内容存储服务器失败!!");
					}
				}else{
					jsonResp.put("success", false);
					jsonResp.put("message", "校验IP地址和端口唯一性失败!");
				}
			}else{
				jsonResp.put("success", false);
				jsonResp.put("message", "校验服务器名称唯一性失败!");
			}
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置内容存储服务器失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器管理->新增服务器失败：" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--addContentServer(over)");
		return null;
	}

	/**
	 * 禁用服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/stopContentServerAction.action", method = RequestMethod.POST)
	public String stopContentServer(String server_ids) {
		log.info("--stopContentServer(start)-->server_ids:" + server_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean result = contentServerManageDao
					.stopContentServer(server_ids);
			log.debug( "--stopContentServer-->result:" + result );
			if (result) {
				Set<Integer> set=new HashSet<Integer>();
				String[] ids = server_ids.split(",");
				for (int i = 0; i < ids.length; i++) {// 下发服务器禁用信息
					if (contentServerManageDao
							.getContentServerGroupInfoByServerId(Integer
									.parseInt(ids[i])) != null) {
						// 判断服务器是否被服务器组关联，如果关联则下发服务器禁用信息
						/*ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentServerThread(Integer
								.parseInt(ids[i]), 5)));*/
						set.add(Integer.parseInt(ids[i]));
						log.debug( "--stopContentServer(" + i + ") SendContentServerThread start");
					}
				}
				
				IssueUtils.IssueContentServerGroupInfo(set);
				// 重新加载服务器列表信息
				LazySingleton.getInstance().resetServerList();
				jsonResp.put("success", true);
				jsonResp.put("message", "停用服务成功!!");
				jsonResp.put("code", 20000);//TODO mock
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "停用服务失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "停用服务失败!!");
			jsonStr = jsonResp.toString();
			log.error( "内容存储服务器管理->禁用服务器失败：" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.debug( "--stopContentServer(over)");
		return null;
	}

	/**
	 * 启用服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/startContentServerAction.action", method = RequestMethod.POST)
	public String startContentServer(String server_ids) {
		log.info("--startContentServer-->server_ids:" + server_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			String[] ids = server_ids.split(",");
			int activeNodeCount = contentServerManageDao
					.getActiveContentServerSum();
			if (License.maxUsers < activeNodeCount + ids.length
					&& License.maxUsers != 0) {
				log.warn( "(server count out permission count)启用服务数量超过授权,目前只能启动" + License.maxUsers + "个服务器");
				jsonResp.put("success", false);
				jsonResp.put("message", "启用服务数量超过授权,目前只能启动" + License.maxUsers
						+ "个服务器");
				jsonStr = jsonResp.toString();
				this.outJsonString(jsonStr);
				return null;
			}
			boolean result = contentServerManageDao
					.startContentServer(server_ids);
			log.debug( "--startContentServer-->result:" + result );
			if (result) {
				Set<Integer> set=new HashSet<Integer>();
				for (int i = 0; i < ids.length; i++) {
					if (contentServerManageDao
							.getContentServerGroupInfoByServerId(Integer
									.parseInt(ids[i])) != null) {
						// 判断服务器是否被服务器组关联，如果关联则下发服务器启用信息
						/*ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentServerThread(Integer
								.parseInt(ids[i]), 4)));*/
						set.add(Integer.parseInt(ids[i]));
						log.debug( "--startContentServer(" + i + ") SendContentServerThread start");
					}
				}
				IssueUtils.IssueContentServerGroupInfo(set);
				// 重新加载服务器列表信息
				LazySingleton.getInstance().resetServerList();
				jsonResp.put("success", true);
				jsonResp.put("message", "启用服务成功!!");
				jsonResp.put("code", 20000);//TODO mock
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "启用服务失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "启用服务失败!!");
			jsonStr = jsonResp.toString();
			log.error( "内容存储服务器管理->启用服务器失败：" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--startContentServer(over)");
		return null;
	}

	/**
	 *校验IP地址和端口唯一性
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/checkIPandPortAction.action", method = RequestMethod.POST)
	public String checkIPandPort(String server_ip, int http_port, int socket_port, String server_id) {
		log.info("--checkIPandPort-->server_id:" + server_id + ";server_ip:" + server_ip);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		server_id = "".equals(server_id) || server_id == null ? "0" : server_id;
		int count = 0;
		try {
			count = contentServerManageDao.checkIPandPort(Integer.valueOf(server_id), server_ip,
					http_port, socket_port);
		} catch (Exception e) {
			log.error( "内容存储服务器管理->校验IP地址和端口唯一性失败：" + e.toString(), e);

			count = -1;
		}
		log.debug( "--checkIPandPort count:" + count);
		if (count == 0) {
			jsonResp.put("success", true);
			jsonStr = jsonResp.toString();
		} else if (count > 0) {
			jsonResp.put("success", false);
			jsonResp.put("reason", "IP和端口已经存在!!");
			jsonStr = jsonResp.toString();
		} else {
			jsonResp.put("success", true);
			jsonResp.put("reason", "检验失败!!");
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--checkIPandPort(over)");
		return null;
	}
	/**
	 * 检测该服务器名称是否存在
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/checkServerNameAction.action", method = RequestMethod.POST)
	public String checkServerName(String server_name, String server_id) {
		log.info("--checkServerName(start)-->server_id:" + server_id + ";server_name:" + server_name);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		server_id = "".equals(server_id) || server_id == null ? "0" : server_id;
		try {
			server_name = URLDecoder.decode(server_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode contentServer fields error, server_name=" + server_name,e1);
		}
		try {
			count = contentServerManageDao.checkServerName(Integer.valueOf(server_id),
					server_name);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "内容存储服务器管理->校验服务器名称唯一性失败!" + e.toString());
		}
		log.debug( "--checkServerName count:" + count);
		if (count == 0) {
			jsonResp.put("success", true);
			jsonStr = jsonResp.toString();
		} else if (count > 0) {
			jsonResp.put("success", false);
			jsonResp.put("reason", "服务器名称已经存在!!");
			jsonStr = jsonResp.toString();
		} else {
			jsonResp.put("success", true);
			jsonResp.put("reason", "检验失败!!");
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--checkServerName(over)-->server_id:" + server_id);
		return null;
	}
	
	public boolean checkServerNameVue(String server_name, String server_id) {
		log.info("--checkServerNameVue(start)-->server_id:" + server_id + ";server_name:" + server_name);
		int count = 0;
		try {
			count = contentServerManageDao.checkServerName(Integer.valueOf(server_id),
					server_name);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error("内容存储服务器管理->校验服务器名称唯一性失败!" + e.toString());
		}
		log.debug( "--checkServerName count:" + count);
		if (count == 0) {
			return true;
		} else if (count > 0) {
			log.error("内容存储服务器管理->服务器名称已经存在!");
			return false;
		} else {
			log.error("内容存储服务器管理->检验失败!");
			return false;
		}
	}
	
	public boolean checkIPandPortVue(String server_ip, int http_port, int socket_port, String server_id) {
		log.info("--checkIPandPortVue-->server_id:" + server_id + ";server_ip:" + server_ip);
		int count = 0;
		try {
			count = contentServerManageDao.checkIPandPort(Integer.valueOf(server_id), server_ip,
					http_port, socket_port);
		} catch (Exception e) {
			log.error( "内容存储服务器管理->校验IP地址和端口唯一性失败：" + e.toString(), e);

			count = -1;
		}
		log.debug( "--checkIPandPortVue count:" + count);
		if (count == 0) {
			return true;
		} else if (count > 0) {
			log.error("内容存储服务器管理->IP和端口已经存在!");
			return false;
		} else {
			log.error("内容存储服务器管理->检验失败!");
			return false;
		}
	}
}
