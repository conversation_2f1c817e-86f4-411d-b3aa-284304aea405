package com.sunyard.ecm.server.cache;

import com.sunyard.initialization.LoadConfigFile;
import com.sunyard.ws.client.WSAccessClient;
import com.sunyard.ws.internalapi.SunEcmAccess;

/**
 * 统一接入的webservice客户端工具
 * <AUTHOR>
 *
 */
public class WSAccessUtil {

	public static SunEcmAccess getAccessClient(){
		String url = LoadConfigFile.getInstance().getConfigBean().getAccessServer();
		return new WSAccessClient().getAccessClient(url, 300000);
	}
}