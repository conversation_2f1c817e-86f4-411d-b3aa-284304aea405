package com.sunyard.util;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.security.SecureRandom;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;

/**
 * <p>Title: 文件工具类</p>
 * <p>Description: 对文件操作的工具类</p>
 * <p>Copyright: Copyright (c) 2010</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class FileUtil {
	private final static Logger log = LoggerFactory.getLogger(FileUtil.class);
	/**
	 * 得到两层1000以内文件夹名
	 */
	public static String getRandomFolder() {
		return getRandomFolder(1000);

	}
	/**
	 * 得到一层1000以内文件夹名
	 * @return
	 */
	public static String getLayerRandomFolder(){
		SecureRandom random = new SecureRandom();
		return String.valueOf(random.nextInt(1000));
	}
	/**
	 * 得到两层最大值文件夹名
	 * @param max
	 * 		每层文件数最大值
	 * @return
	 */
	public static String getRandomFolder(int max) {
		SecureRandom random = new SecureRandom();
		return String.valueOf(random.nextInt(max)) + File.separator
				+ String.valueOf(random.nextInt(max));
	}
	/**
	 * 得到一层最大值文件夹名
	 * @param max
	 * 		每层文件数最大值
	 * @return
	 */
	public static String getLayerRandomFolder(int max){
		SecureRandom random = new SecureRandom();
		return String.valueOf(random.nextInt(max));
	}
	/**
	 * 创建文件夹
	 * @param path
	 */
	public static boolean creatFolder(String path){
		File file = new File(path);
		if(!file.exists()){
			return file.mkdirs();
		}else{
			return true;
		}
	}
	/**
	 * 将原文件名修改成fileId
	 * @param fileId
	 * @param fileName
	 * @return
	 */
	public static String fileNameOption(String fileId, String fileName){
		int index = fileName.indexOf(".");
		if(index < 0){
			return fileId;
		}
		return fileId + fileName.substring(index);
	}
	/**
	 * 判断文件是否存在
	 * @param filePath
	 * @return
	 */
	public static boolean existsFile(String filePath){
		File file = new File(filePath);
		return file.exists();
	}
	
	/**
	 * 拷贝文件
	 * @param oldRootPath
	 * 			旧文件的根路径
	 * @param newRootPath
	 * 			新文件的根路径
	 * @param filePath
	 * 			文件的相对路径
	 */
	public static void copyFile(String oldRootPath, String newRootPath, String filePath) throws Exception{
		File oldPathFile = new File(oldRootPath + filePath);
		String oldPath = oldPathFile.getParent();
		File oldFile = new File(oldRootPath);
		String s = oldFile.getName();
		oldPath = oldPath.substring(oldPath.indexOf(s)+s.length());
		String newPath = newRootPath + oldPath;
		//无限循环创建文件夹
		while(!creatFolder(newPath));
		copyFile(oldRootPath+filePath, newRootPath+filePath);
	}
	/**
	 * 拷贝文件
	 * @param oldPath
	 * 			旧文件路径
	 * @param newPath
	 * 			新文件路径
	 */
	public static void copyFile(String oldPath, String newPath)throws Exception{
		File oldFile = new File(oldPath);
		File newFile = new File(newPath);
		File pareFile=newFile.getParentFile();
		if(pareFile!=null&&!pareFile.exists()){
			pareFile.mkdirs();
		}
		forChannel(oldFile, newFile);
	}
	
	/**
	 * 文件拷贝实现方法
	 * @param f1
	 * @param f2
	 * @throws Exception
	 */
	public static void forChannel(File f1,File f2) throws Exception{
        int length = 2097152;
        FileInputStream in=null;
        FileOutputStream out=null;
        FileChannel inC = null;
        FileChannel outC =null;
        ByteBuffer b=null;
        try{
        	 in=new FileInputStream(f1);
        	 out=new FileOutputStream(f2);
        	 inC = in.getChannel();
        	 outC = out.getChannel();
        while(true){
            if(inC.position() == inC.size()){
            	break;
            }
            if((inC.size()-inC.position())<length){
                length=(int)(inC.size()-inC.position());
            }else
                length=2097152;
	            b=ByteBuffer.allocateDirect(length);
	            inC.read(b);
	            b.flip();
	            outC.write(b);
	            outC.force(false);
	        }
		} finally {
			if (inC != null) {
				inC.close();
			}
			if (outC != null) {
				outC.close();
			}
			if (in != null) {
				in.close();
			}
			if (out != null) {
				out.close();
			}
		}
    }
	/**
	 * 删除文件
	 * @param filePath
	 */
	public static boolean deleteFile(String filePath){
		boolean result;
		File file = new File(filePath);
		try {
			if (file.exists()) {
				for (int i = 0; i < 10; i++) {
					file.delete();
					if (!file.exists()) {
						break;
					}
					Thread.sleep(100);
				}
			}
		} catch (Exception e) {
			log.error("delete file:" + filePath, e);
		}
		result = file.exists();
		if (result) {
			log.error("file can not delete:" + filePath);
		}
		return result;
	}
	
	/**
	 * 文件是否存在
	 * @param filePath
	 * @return
	 */
	public static boolean isFileExists(String filePath){
		File file = new File(filePath);
		return file.exists();
	}
	
	/**
	 * 读取properties文件
	 * @param path 文件路径
	 * @return
	 */
	public static Properties readProperties(String path){
		Properties properties = new Properties();
		InputStream inputStream=null;
		try {
		 inputStream = new FileInputStream(path);
			properties.load(inputStream);
		} catch (IOException e) {
			log.error("出错",e);
		}finally{
			if(inputStream!=null){
				try {
					inputStream.close();
				} catch (IOException e) {
					log.error("出错");
				}
			}
		}
		return properties;
	}
	
	/**
	 * 查询文件，获取文件
	 * @param filePath 文件全路径
	 * @return
	 * @throws SunECMException
	 * @throws FileNotFoundException
	 */
	public static BufferedInputStream getFile (String filePath) throws SunECMException, FileNotFoundException{
		File file = new File(filePath);
		if(!file.exists()) {
			throw new SunECMException(SunECMExceptionStatus.FILE_NOT_FOUND,"getFile: 找不到文件");
		}
		BufferedInputStream inputStream = new BufferedInputStream(new FileInputStream(file));
		return inputStream;
	}
	
	/**
	 * 读取本地磁盘上的xml文件
	 * @param path xml文件路径
	 * @return
	 * @throws IOException
	 * @throws SunECMException 
	 */
	public static String readXml(String path) throws IOException, SunECMException{
		File file = new File(path);
		InputStream in = null;
		long date1 = System.currentTimeMillis();
		StringBuffer buffer = new StringBuffer();
		if(file.isFile()) {
			try {
				byte[] tempbytes = new byte[1024];
				in = new FileInputStream(file);
				int len = 0;
				while((len = in.read(tempbytes)) != -1){
					buffer.append(new String(tempbytes, 0, len));
				}
			} catch (FileNotFoundException e) {
				throw new SunECMException(SunECMExceptionStatus.FILE_NOT_FOUND, "FileUtil-->readXml: " + e.toString());
			} finally {
				if(in!=null){
					in.close();
				}
			}
			String anStr = buffer.toString();
			long date2 = System.currentTimeMillis();
			log.debug("读取一个批注xml文件花费时间： " + (date2-date1) + "毫秒");
			return anStr;
		} else {
			throw new SunECMException(SunECMExceptionStatus.FILE_NOT_FOUND, "FileUtil-->readXml...path is not file name ...");
		}
	}
	
	/**
	 * 写入xml文件，以xml作为文件后缀
	 * @param xml xml文件内容
	 * @param filePath 文件路径
	 * @param fileName 文件名
	 * @throws SunECMException
	 */
	public static void writeXML(String xml, String filePath, String fileName) throws SunECMException{
		File path = new File(filePath);
		if(!path.exists()) {
			path.mkdirs();
		}
		File file = new File(filePath + fileName + ".xml");
		FileOutputStream fileOutputStream = null;
		try {
			fileOutputStream = new FileOutputStream(file,false);
			// 以UTF-8编码写入xml文件
			fileOutputStream.write(xml.getBytes());
		} catch (FileNotFoundException e) {
			throw new SunECMException(SunECMExceptionStatus.FILE_NOT_FOUND, "FileUtil-->writeXML: " + e.toString());
		} catch (IOException e) {
			throw new SunECMException(SunECMExceptionStatus.IO_EXCEPTION, "FileUtil-->writeXML: " + e.toString());
		} finally {
			try {
				if(fileOutputStream != null){
					fileOutputStream.flush();
					fileOutputStream.close();
				}
			} catch (IOException e) {
				throw new SunECMException(SunECMExceptionStatus.IO_EXCEPTION, "FileUtil-->writeXML: " + e.toString());
			}
		}
	}
	/**
	 * 根据文件ID创建文件保存名
	 * 当批次中存在文件替换时，文件ID不变，文件保存名采用文件ID+序号的形式生成
	 * @param filePath 	文件路径
	 * @param fileNO	文件ID 	
	 * @return 	文件保存名 
	 */
	public static String createSaveName(String filePath, String fileNO) {
		if(!isFileExists(filePath+fileNO)){
			return fileNO;
		}
		int i = 1;
		while(isFileExists(filePath+fileNO+"-"+i)){
			i++;
		}
		return fileNO+"-"+i;
	}
}