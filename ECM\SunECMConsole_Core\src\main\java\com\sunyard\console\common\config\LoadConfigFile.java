package com.sunyard.console.common.config;

import com.sunyard.console.common.config.bean.ConsoleConfigBean;

/**
 * <p>
 * Title: 加载配置文件
 * </p>
 * <p>
 * Description: 加载transConfig.properties文件
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class LoadConfigFile {
	private static ConsoleConfigBean configBean = null;
	/**
	 * 获取配置文件信息
	 * @return
	 */
	public static ConsoleConfigBean getConfigBean() {
		if(configBean == null){
			configBean = ReadConfig.getConsoleConfigBean();
		}
		return configBean;
	}

	/**
	 * 设置配置文件信息
	 * @param configBean
	 */
	public static void setConfigBean(ConsoleConfigBean configBean) {
		LoadConfigFile.configBean = configBean;
	}
}
