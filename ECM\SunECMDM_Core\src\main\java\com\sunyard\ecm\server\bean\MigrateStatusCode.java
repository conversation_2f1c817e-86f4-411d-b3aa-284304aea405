package com.sunyard.ecm.server.bean;


/**
 * 迁移状态的标志位                
 * <AUTHOR>
 *
 */
public class MigrateStatusCode {
	/**
	 * 未迁移 ,未离线状态 1
	 */
	public final static String READYMIGRATEOROFFLINE = "1";
	/**
	 * 已经迁移 状态 2
	 */
	public final static String ALREADYMIGRATE= "2";
	
	/**
	 * 记录待更新 3,只有文档表有这个状态
	 */
	public final static String UPDATEMIGRATE = "3";
	
	/**
	 * 迁移失败  状态 4
	 */
	public final static String FAILMIGRATE= "4";
	
 
	/**
	 * 批次已离线  状态 5
	 */
	public final static String ALREADYOFFLINE= "5";
	
	/**
	 * 批次离线失败  状态 6
	 */
	public final static String FAILOFFLINE= "6";
	
	/**
	 * 批次离线已清理  状态 7
	 */
	public final static String ALREADYOFFLINECLEAR= "7";
	/**
	 * 批次放弃离线 状态8
	 */
	public final static String ABANDONOFFLINECLEAR= "8";
	
	/**
	 * 批次离线清理失败 状态9
	 */
	public final static String FAILOFFLINECLEAR= "9";
	

}