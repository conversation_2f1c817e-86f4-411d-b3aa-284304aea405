package com.sunyard.console.tagmanage.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <p>
 * Title: 标签关联属性信息bean
 * </p>
 * <p>
 * Description: 标签关联属性信息
 * </p>
 * <p>
 * Copyright: Copyright (c) 2022
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@XStreamAlias("tagRelModelBean")
public class TagRelModelBean {

	private String model_code;// 模型代码
	private String model_name;// 模型名
	private String state; // 关联状态
	public String getModel_code() {
		return model_code;
	}
	public void setModel_code(String model_code) {
		this.model_code = model_code;
	}
	public String getModel_name() {
		return model_name;
	}
	public void setModel_name(String model_name) {
		this.model_name = model_name;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
}
