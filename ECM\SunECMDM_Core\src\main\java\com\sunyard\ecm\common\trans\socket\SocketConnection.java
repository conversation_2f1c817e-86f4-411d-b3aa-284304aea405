package com.sunyard.ecm.common.trans.socket;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.net.Socket;
import java.util.Map;

import com.sunyard.ecm.common.trans.AbstractConnection;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.event.ConnectionCloseEvent;
import com.sunyard.ecm.server.event.ConnectionOpenEvent;
import com.sunyard.ecm.server.event.EventManageCenter;
import com.sunyard.ecm.server.service.EncrypterService;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import com.sunyard.common.Configuration;
import com.sunyard.ecm.server.context.Context;
import com.sunyard.ecm.server.context.ContextUtil;
import com.sunyard.exception.SunECMException;
import com.sunyard.util.MapUtil;
import com.sunyard.util.SpringUtil;

public class SocketConnection extends AbstractConnection implements Runnable {
	final static Logger log = LoggerFactory.getLogger(SocketConnection.class);
	private Socket socket;
	private BufferedReader in = null;
	private BufferedWriter out = null;
	private int fileBufferSize = 65536;
	private EventManageCenter eventManageCenter;
	private EncrypterService encrypterService;

	public SocketConnection(Socket socket) throws IOException {
		super();
		this.socket = socket;
		init();
		eventManageCenter = (EventManageCenter) SpringUtil
				.getSpringBean("eventManageCenter");
		encrypterService = (EncrypterService)SpringUtil
				.getSpringBean("encrypterService");
	}

	private void init() throws IOException {
		String charSet = Configuration.get("socketStreamCharSet", "UTF-8");
		try {
			socket.setSoTimeout(Configuration.getInt("sockettimeout", 30) * 1000);

			int sendBuffer = Configuration
					.getInt("socketsendbuffersize", 65536);
			log.debug("socket send buffersize :{}", sendBuffer);
			socket.setSendBufferSize(sendBuffer);

			int recvBuffer = Configuration.getInt("sockereceivetbuffersize",
					65536);
			log.debug("socket recv buffersize :{}", recvBuffer);
			socket.setReceiveBufferSize(recvBuffer);
			socket.setTcpNoDelay(true);
			log.debug("socket stream 编码方式 :{}", charSet);
			in = new BufferedReader(new InputStreamReader(
					socket.getInputStream(), charSet));
			out = new BufferedWriter(new OutputStreamWriter(
					socket.getOutputStream(), charSet));
			clientIp = socket.getInetAddress().getHostAddress();

			fileBufferSize = Configuration.getInt("fileBufferSize", 65536);
			log.debug("file buffer size :{}", fileBufferSize);
			log.debug("accept connection:{}", clientIp);
		} catch (UnsupportedEncodingException e) {
			log.error("This server not support " + charSet, e);
			throw new IOException(
					"Init conection failed caused by:This server not support UTF-8 ");
		}

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.sunyard.ecm.common.trans.socket.Connection#getMessages()
	 */
	public String getMessages() throws IOException {
		String msg = in.readLine();
		Context context = ContextUtil.getContext();
		context.recvRecord(msg);
		log.debug("{}>> [{}]", getIp(), msg);
		return msg;

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.sunyard.ecm.common.trans.socket.Connection#sendMessages(java.lang
	 * .String)
	 */
	public boolean sendMessages(String msg) throws IOException {
		out.write(msg);
		log.debug("{}<< [{}]", getIp(), msg);
		out.newLine();
		out.flush();
		Context context = ContextUtil.getContext();
		context.sendRecord(msg);
		return true;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.sunyard.ecm.common.trans.socket.Connection#setClientIp(java.lang.
	 * String)
	 */
	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	private String getIp() {
		return "[" + this.clientIp + "]";
	}

	public void run() {
		try {
			ConnectionOpenEvent event = new ConnectionOpenEvent();
			event.setOpenTime(System.currentTimeMillis());
			event.setClientIp(socket.getInetAddress().getHostAddress());
			eventManageCenter.fire(event);

			process();
		} catch (Throwable e) {
			log.error("此处不应该发生异常:", e);
		} finally {

			ConnectionCloseEvent event = new ConnectionCloseEvent();
			event.setCloseTime(System.currentTimeMillis());
			event.setClientIp(socket.getInetAddress().getHostAddress());
			eventManageCenter.fire(event);
		}

	}

	public void close() {
		log.debug("close connection:{}", clientIp);
		IOUtils.closeQuietly(in);
		IOUtils.closeQuietly(out);
		try {
			this.socket.close();
		} catch (Exception e) {
			log.warn("close socket get excption：", e);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.sunyard.ecm.common.trans.socket.Connection#reciveFile(long,
	 * java.io.OutputStream)
	 */
	public void reciveFile(long filesize, OutputStream out, FileBean fileBean)
			throws IOException {
		Long received = fileBean.getReceived();
		long remain = filesize - received;
		int buffer = (int) Math.min(fileBufferSize, remain);
		byte[] bytes = new byte[buffer];
		int read;
		long readExpend = 0L;
		long writeExpend = 0L;
		long t3 = System.nanoTime();
		while (remain > 0
				&& -1 != (read = socket.getInputStream().read(bytes, 0, buffer))) {
			long t1 = System.nanoTime();
			out.write(bytes, 0, read);
			long t2 = System.nanoTime();
			readExpend += t1 - t3;
			writeExpend += t2 - t1;
			received += read;
			fileBean.setReceived(received);
			remain = remain - read;
			buffer = (int) Math.min(fileBufferSize, remain);
			t3 = System.nanoTime();
		}
		log.info("从客户端接收文件耗时:{}ns", Long.valueOf(readExpend));
		log.info("向输出流中写入文件耗时:{}ns", Long.valueOf(writeExpend));
		if (remain != 0) {
			log.error("SOCKET连接{}异常结束，文件剩余：[{}]未接收", getClientIp(), remain);
			throw new IOException("在接受所有文件数据前读到了文件流结尾，剩余：" + remain);
		}
	}
	
	public void reciveFile(long filesize, OutputStream out, FileBean fileBean, String encodeLength)
			throws IOException, SunECMException{
		if(StringUtils.isEmpty(encodeLength) || encodeLength.equals("0")||"null".equals(encodeLength)){	
			reciveFile(filesize, out, fileBean);
			return;
		}
		int encodeLengthInt=Integer.parseInt(encodeLength);
		Long received=fileBean.getReceived();
		boolean flag = true;
		long remain = filesize-received;
		int buffer = (int) Math.min(fileBufferSize, remain);
		byte[] bytes = new byte[buffer];
		int read;
		byte[] cipherText = null;
	
		bytes = encrypterService.getbytes(buffer, encodeLengthInt);

		//进行第一次读写（部分加密）
		while(flag && -1 != (read = socket.getInputStream().read(bytes, 0, (int)bytes.length))){
			cipherText = encrypterService.encryptData_ECB(bytes);//cipherText为加密后字节
			log.debug("加密后的字节长度为" + cipherText.length);
			out.write(cipherText);
			fileBean.setENCODESIZE(bytes.length +"_"+ cipherText.length);
			received+=read;
			fileBean.setReceived(received);
			remain = remain - read;
			flag = false;
			buffer = (int) Math.min(fileBufferSize, remain);
			bytes = new byte[buffer];
		}
		//剩余字节读写
		while (remain > 0
				&& -1 != (read = socket.getInputStream().read(bytes, 0, buffer))) {
			out.write(bytes, 0, read);
			received+=read;
			fileBean.setReceived(received);
			remain = remain - read;
			buffer = (int) Math.min(fileBufferSize, remain);
			bytes = new byte[buffer];
		}
		if (remain != 0) {
			log.error("SOCKET连接{}异常结束，文件剩余：[{}]未接收", getClientIp(), remain);
			throw new IOException("在接受所有文件数据前读到了文件流结尾，剩余：" + remain);
		}
	}

	@Override
	protected Map<String, String> getInputParam(String[] content) {
		return MapUtil
				.parseMap(content[content.length - 1], this.getClientIp());
	}

	@Override
	protected String getOption(String[] content) {
		return content[0];
	}

	@Override
	public void needAlive() {
		// do nothing
	}

	public boolean sendErrorMessages(int code, String msg) throws IOException {
		log.error("发生异常返回：{}", msg);
		return sendMessages(msg);
	}

	private String connectionType;

	public String getConnectionType() {
		return connectionType;
	}

	public void setConnectionType(String connectionType) {
		this.connectionType = connectionType;
	}

	public String getHttpMessages() throws IOException {
		// http需要实现，Ws不需要
		return null;
	}


	@Override
	public Map<String, MultipartFile> getHttpFiles() {
		// http需要实现，Ws不需要
		return null;
	}

	public void httpReciveFile(long fileSize, OutputStream out, FileBean fileBean, String encodeLength)
			throws IOException, SunECMException {
		// http需要实现，Ws不需要
		
	}

	public void httpReciveFile(long fileSize, OutputStream out, FileBean fileBean) throws IOException {
		//http需要实现，Ws不需要
		
	}



	

}