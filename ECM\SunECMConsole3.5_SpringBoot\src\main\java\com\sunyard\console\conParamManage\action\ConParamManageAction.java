package com.sunyard.console.conParamManage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;

import com.sunyard.console.conParamManage.bean.ConParamInfoBean;
import com.sunyard.console.conParamManage.bean.ParamShowInfoBean;
import com.sunyard.console.conParamManage.dao.ConParamManageDAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.sunyard.console.threadpoool.IssueUtils;

import net.sf.json.JSONObject;
@Controller
public class ConParamManageAction extends BaseAction {
	@Autowired
	ConParamManageDAO conParamManageDao;
	private int par_id;//参数ID
	private String par_key;//参数键
	private String par_val;//参数值
	private String par_old_key;//修改前的参数值
	private int par_all;//全局参数
	private int par_group;//组ID
	private int par_server;//服务器ID
	private String par_model;//索引模型ID
	private String optionFlag;// 增加和修改标识
	private int group_id;//服务器组ID
	private String group_name;//服务器组名称
	private int server_id;//服务器ID
	private String server_name;//服务器名称
	private int par_state;//参数状态
	private String par_ids;// 启用和禁用的par_id字符串
	private int par_show_id;//参数项ID
	private String par_show_ids;//删除时的配置项id字符串
	private String par_keys;//删除时的配置项par_key字符串
	private String par_show_name;//参数显示名
	private String par_remark;//参数说明
	private int start;
	private int limit;
	/**
	 * 日志对象
	 */
	private final static Logger log = LoggerFactory.getLogger(ConParamManageAction.class);
	
	public ConParamManageDAO getConParamManageDao() {
		return conParamManageDao;
	}

	public void setConParamManageDao(ConParamManageDAO conParamManageDao) {
		this.conParamManageDao = conParamManageDao;
	}	
	
	public int getPar_id() {
		return par_id;
	}

	public void setPar_id(int par_id) {
		this.par_id = par_id;
	}

	public String getPar_key() {
		return par_key;
	}

	public void setPar_key(String par_key) {
		this.par_key = par_key;
	}

	public String getPar_val() {
		return par_val;
	}

	public void setPar_val(String par_val) {
		this.par_val = par_val;
	}

	public int getPar_all() {
		return par_all;
	}

	public void setPar_all(int par_all) {
		this.par_all = par_all;
	}

	public int getPar_group() {
		return par_group;
	}

	public void setPar_group(int par_group) {
		this.par_group = par_group;
	}

	public int getPar_server() {
		return par_server;
	}

	public void setPar_server(int par_server) {
		this.par_server = par_server;
	}

	public String getPar_model() {
		return par_model;
	}

	public void setPar_model(String par_model) {
		this.par_model = par_model;
	}

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}
	
	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}
	
	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int group_id) {
		this.group_id = group_id;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String group_name) {
		this.group_name = group_name;
	}

	public int getServer_id() {
		return server_id;
	}

	public void setServer_id(int server_id) {
		this.server_id = server_id;
	}

	public String getServer_name() {
		return server_name;
	}

	public void setServer_name(String server_name) {
		this.server_name = server_name;
	}
	
	public int getPar_state() {
		return par_state;
	}

	public void setPar_state(int par_state) {
		this.par_state = par_state;
	}

	public String getPar_ids() {
		return par_ids;
	}

	public void setPar_ids(String par_ids) {
		this.par_ids = par_ids;
	}

	public int getPar_show_id() {
		return par_show_id;
	}

	public void setPar_show_id(int par_show_id) {
		this.par_show_id = par_show_id;
	}

	public String getPar_show_name() {
		return par_show_name;
	}

	public void setPar_show_name(String par_show_name) {
		this.par_show_name = par_show_name;
	}

	public String getPar_remark() {
		return par_remark;
	}

	public void setPar_remark(String par_remark) {
		this.par_remark = par_remark;
	}

	public String getPar_old_key() {
		return par_old_key;
	}

	public void setPar_old_key(String par_old_key) {
		this.par_old_key = par_old_key;
	}

	public String getPar_show_ids() {
		return par_show_ids;
	}

	public void setPar_show_ids(String par_show_ids) {
		this.par_show_ids = par_show_ids;
	}

	public String getPar_keys() {
		return par_keys;
	}

	public void setPar_keys(String par_keys) {
		this.par_keys = par_keys;
	}

	/**
	 * 得到配置参数
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/getConParamAction.action", method = RequestMethod.POST)
	public String getConParam(String data){
		JSONObject modelJson = JSONObject.fromObject(data);
		String par_key = (String) modelJson.getOrDefault("par_key", "");
		String par_group = (String) modelJson.getOrDefault("par_group", "");
		String par_model = (String) modelJson.getOrDefault("par_model", "");
		int page = modelJson.getInt("page");
		int limit = modelJson.getInt("limit");
		start = (page-1) * limit;
		log.info( "--getconParam(start)-->group_id:" + par_group + "model_code:" + par_model + "par_id:" + par_id + ";par_key:" + par_key);
		String jsonStr = null;

		try {
			List<ConParamInfoBean> conParamInfoList = conParamManageDao.getConParam(par_id, par_key,par_group, par_model, start + 1, limit);
			List<ConParamInfoBean> AllInfoList = conParamManageDao.getConParam(par_id, par_key,par_group, par_model);

			int size = 0;
			if (AllInfoList != null && AllInfoList.size() > 0) {
				size = AllInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					conParamInfoList, size, new ConParamInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取配置参数信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "配置参数管理->查询配置参数列表失败：" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getconParam(over)" );
		return null;
	}
	
	/**
	 * 增加配置参数
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/addConParamAction.action", method = RequestMethod.POST)
	public String addConParam(String data) {		
		JSONObject modelJson = JSONObject.fromObject(data);
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");
		String par_id = (String) modelJson.getOrDefault("par_id", "0");
		String par_val = (String) modelJson.getOrDefault("par_val", "");
		String par_key = (String) modelJson.getOrDefault("par_key", "");
		String par_all = (String) modelJson.getOrDefault("par_all", "0");
		String par_group = (String) modelJson.getOrDefault("par_group", "0");
		String par_server = (String) modelJson.getOrDefault("par_server", "0");
		String par_model = (String) modelJson.getOrDefault("par_model", "0");
		String par_state = (String) modelJson.getOrDefault("par_state", "0");

		log.info( "--addConParam(start)-->par_id:" + par_id + "par_key:" + par_key);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		par_id=par_id.equals("")?"0":par_id;
		par_all=par_all.equals("")?"0":par_all;
		par_group=par_group.equals("")?"0":par_group;
		par_server=par_server.equals("")?"0":par_server;
		par_state=par_state.equals("")?"0":par_state;
		ConParamInfoBean conParamBean = new ConParamInfoBean();
		conParamBean.setPar_id(Integer.valueOf(par_id));
		conParamBean.setPar_key(par_key);
		conParamBean.setPar_val(par_val);
		conParamBean.setPar_all(Integer.valueOf(par_all));
		conParamBean.setPar_group(Integer.valueOf(par_group));
		conParamBean.setPar_server(Integer.valueOf(par_server));
		conParamBean.setPar_model(par_model);
		conParamBean.setPar_state(Integer.valueOf(par_state));
		log.debug( "--addConParam-->conParamBean:" + conParamBean);
		boolean checkV = checkParamValue(par_key,par_val);
		if(!checkV){
			jsonResp.put("message", "参数值不允许!!");
			jsonStr = jsonResp.toString();
		}else{
			int count = checkParamStore(par_id,par_key,par_val,par_all,par_group,par_server,par_model);
			if (count > 0) {
				jsonResp.put("success", false);
				jsonResp.put("message", "参数相同作用域已存在!!");
				jsonStr = jsonResp.toString();
			} else if(count == -2){
				jsonResp.put("success", false);
				jsonResp.put("message", "不允许没有任何作用域");
				jsonStr = jsonResp.toString();
			}else if(count == -3){
				jsonResp.put("success", false);
				jsonResp.put("message", "请配置全局模型参数");
				jsonStr = jsonResp.toString();
			}else if(count == 0){
				try {
					log.debug( "--addConParam-->optionFlag:" + optionFlag );
					int result = 0;
					if (optionFlag != null && optionFlag.equals("createPar")) {// 新增配置参数
						result = conParamManageDao.addConParam(conParamBean);
					} else if (optionFlag != null && optionFlag.equals("updatePar")) {// 修改配置参数
						result = conParamManageDao.updateConParam(conParamBean);
					}
					log.debug( "--addConParam-->result:" + result );
					if (result != 0) {
						IssueUtils.IssueParamInfo(conParamBean);
						jsonResp.put("success", true);
						jsonResp.put("message", "配置参数成功!!");
						jsonResp.put("code", 20000);
						
					} else {
						jsonResp.put("success", false);
						jsonResp.put("message", "配置参数失败!!");
					}
					jsonStr = jsonResp.toString();
				} catch (Exception e) {
					jsonResp.put("success", false);
					jsonResp.put("message", "配置参数失败!!");
					jsonStr = jsonResp.toString();
					// 记录日志
					log.error( "配置参数管理->配置参数失败!" + e.toString(), e);
				}
			}else{
				jsonResp.put("success", false);
				jsonResp.put("message", "检验失败!!");
				jsonStr = jsonResp.toString();
			}
		}
		log.info( "--addConParam(over)-->par_key:" + par_key);
		this.outJsonString(jsonStr);
		return null;
	}
	
	/**
	 *校验参数作用域唯一性
	 * 
	 * @return
	 */
	public int checkParamStore(String par_id,String par_key,String par_val,String par_all,String par_group,String par_server,String par_model) {
		log.info( "--checkParamStore-->par_id:" + par_id + ";par_key:" + par_key);
		int count = 0;
		try {
			if(par_all.equals("0") && par_group.equals("0") && par_server.equals("0") && par_model.equals("0")){
				count = -2;
			}else if(par_all.equals("0")&& par_group.equals("0") && par_server.equals("0") && !par_model.equals("0")){
				count = -3;
			}else{
				count = conParamManageDao.checkParamStore(par_id, par_key,
						par_all, par_group, par_server, par_model);
			}
		} catch (Exception e) {
			log.error( "配置参数管理->校验参数作用域唯一性失败：" + e.toString(), e);
			count = -1;
		}
		log.debug( "--checkParamStore count:" + count);
		return count;
	}
	
	/**
	 *校验参数值
	 * 
	 * @return
	 */
	public boolean checkParamValue(String par_key,String par_val) {
		log.info( "--checkParamValue-->par_key:" + par_key + ";par_val:" + par_val);
		boolean checkVal;
		try {
			checkVal = conParamManageDao.checkParamValue(par_key,par_val);
		} catch (Exception e) {
			log.error( "配置参数管理->校验参数值失败：" + e.toString(), e);
			checkVal = false;
		}
		log.debug( "--checkParamValue checkVal:" + checkVal);
		return checkVal;
	}
	
	/**
	 *校验参数项
	 * 
	 * @return
	 */
	public String checkParamShow(String par_show_id,String par_key,String par_old_key) {
		log.info( "--checkParamShow-->par_show_id:" + par_show_id +", par_key:" + par_key + ",par_old_key:" + par_old_key);
		int count;
		int count_old = 0;
		try {
			count = conParamManageDao.checkParamShow(par_show_id, par_key);
		} catch (Exception e) {
			log.error( "配置参数管理->校验参数项失败：" + e.toString(), e);
			count = -1;
		}
		if(!par_key.equals(par_old_key)){//修改了参数项
			try{
				count_old = conParamManageDao.checkParamKey(par_old_key);
			} catch (Exception e) {
				log.error( "配置参数管理->校验参数key失败：" + e.toString(), e);
				count_old = -1;
			}
		}
		log.debug( "--checkParamShow count:" + count + ",count_old:" + count_old);
		if (count == 0) {
			if (count_old == 0) {
				return "pass";
			} else {
				return "noedit";
			}
		} else {
			return "exist";
		}
	}
	
	/**
	 * 禁用参数
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/stopConParamAction.action", method = RequestMethod.POST)
	public String stopConParam(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String par_ids = (String) modelJson.getOrDefault("par_ids", "");
		log.info( "--stopConParam(start)-->par_ids:" + par_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean result = conParamManageDao
					.stopConParam(par_ids);
			log.debug( "--stopConParam-->result:" + result );
			if (result) {
				String[] ids = par_ids.split(",");
				for (int i = 0; i < ids.length; i++) {
				ConParamInfoBean conParamBean = conParamManageDao.getConParamByParId(ids[i]);
				IssueUtils.IssueParamInfo(conParamBean);
				}
				jsonResp.put("success", true);
				jsonResp.put("message", "停用参数成功!!");
				jsonResp.put("code", 20000);
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "停用参数失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "停用参数失败!!");
			jsonStr = jsonResp.toString();
			log.error( "配置参数管理->禁用参数失败：" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.debug( "--stopConParam(over)");
		return null;
	}

	/**
	 * 启用服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/startConParamAction.action", method = RequestMethod.POST)
	public String startConParam(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String par_ids = (String) modelJson.getOrDefault("par_ids", "");
		log.info( "--startConParam-->par_ids:" + par_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			String[] ids = par_ids.split(",");
			boolean result = conParamManageDao
					.startConParam(par_ids);
			log.debug( "--startConParam-->result:" + result );
			if (result) {
				for (int i = 0; i < ids.length; i++) {
				ConParamInfoBean conParamBean = conParamManageDao.getConParamByParId(ids[i]);
				IssueUtils.IssueParamInfo(conParamBean);
				}
				jsonResp.put("success", true);
				jsonResp.put("message", "启用参数成功!!");
				jsonResp.put("code", 20000);
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "启用参数失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "启用参数失败!!");
			jsonStr = jsonResp.toString();
			log.error( "配置参数管理->启用参数失败：" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--startConParam(over)");
		return null;
	}
	
	/**
	 * 增加配置项
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/addParamShowAction.action", method = RequestMethod.POST)
	public String addParamShow(String data) {
		log.info( "--addParamShow(start)-->par_key:" + par_key);
		
		JSONObject modelJson = JSONObject.fromObject(data);
		String par_key = (String) modelJson.getOrDefault("par_key", "");
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");
		String par_show_id = (String) modelJson.getOrDefault("par_show_id", "");
		String par_show_name = (String) modelJson.getOrDefault("par_show_name", "");
		String par_remark = (String) modelJson.getOrDefault("par_remark", "");
		String par_old_key = (String) modelJson.getOrDefault("par_old_key", "");
		try {
			par_show_name = URLDecoder.decode(par_show_name, "utf-8");
			par_remark = URLDecoder.decode(par_remark, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode UnityAccessServer fields error, par_show_name=" + par_show_name
					+ ", par_remark=" + par_remark, e1);
		}
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		ParamShowInfoBean paramShowBean = new ParamShowInfoBean();
		par_show_id=par_show_id.equals("")?"0":par_show_id;
		paramShowBean.setPar_show_id(Integer.valueOf(par_show_id));
		paramShowBean.setPar_key(par_key);
		paramShowBean.setPar_remark(par_remark);
		paramShowBean.setPar_show_name(par_show_name);
		log.debug( "--addParamShow-->paramShowBean:" + paramShowBean);
		String check = checkParamShow(par_show_id,par_key,par_old_key);
		if(check.equals("noedit")){
			jsonResp.put("success", false);
			jsonResp.put("message", "此参数项已配置参数，不允许修改");
			jsonStr = jsonResp.toString();
		}else if(check.equals("exist")){
			jsonResp.put("success", false);
			jsonResp.put("message", "参数项已存在!!");
			jsonStr = jsonResp.toString();
		}else if(check.equals("pass")){
			try {
				log.debug( "--addParamShow-->optionFlag:" + optionFlag );
				int result = 0;
				if (optionFlag != null && optionFlag.equals("createShow")) {// 新增配置项
					result = conParamManageDao.addParamShow(paramShowBean);
				} else if (optionFlag != null && optionFlag.equals("updateShow")) {// 修改配置项
					result = conParamManageDao.updateParamShow(paramShowBean);
				}			
				log.debug( "--addParamShow-->result:" + result );
				if (result != 0 && optionFlag.equals("createShow")) {
					jsonResp.put("success", true);
					jsonResp.put("message", "新增参数项成功!!");
					jsonResp.put("code", 20000);
				} else if(result != 0 && optionFlag.equals("updateShow")){
					jsonResp.put("success", true);
					jsonResp.put("message", "修改参数项成功!!");
					jsonResp.put("code", 20000);
				}else {
					jsonResp.put("success", false);
					jsonResp.put("message", "新增参数项失败!!");
				}
				jsonStr = jsonResp.toString();
			} catch (Exception e) {
				jsonResp.put("success", false);
				jsonResp.put("message", "新增参数项失败!!");
				jsonStr = jsonResp.toString();
				// 记录日志
				log.error( "配置参数管理->新增参数项失败!" + e.toString(), e);
			}
		}
			log.info( "--addParamShow(over)-->par_key:" + par_key);
			this.outJsonString(jsonStr);
			return null;
	}
	
	/**
	 * 得到配置项
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/getParamShowAction.action", method = RequestMethod.POST)
	public String getParamShow(String data){
		JSONObject modelJson = JSONObject.fromObject(data);
		int page = modelJson.getInt("page");
		int limit = modelJson.getInt("limit");
		start = (page-1) * limit;
		String par_key = (String) modelJson.getOrDefault("par_key", "");

		log.info( "--getParamShow(start)-->par_key:" + par_key);
		String jsonStr = null;

		try {
			List<ParamShowInfoBean> paramShowInfoList = conParamManageDao.getParamShow(par_key, start + 1, limit);
			List<ParamShowInfoBean> AllInfoList = conParamManageDao.getParamShow(par_key);

			int size = 0;
			if (AllInfoList != null && AllInfoList.size() > 0) {
				size = AllInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					paramShowInfoList, size, new ParamShowInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取配置参数项失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "配置参数管理->查询配置参数项失败：" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getParamShow(over)" );
		return null;
	}
	
	/**
	 * 得到配置参数信息
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/getParKeyAction.action", method = RequestMethod.GET)
	public String getParKey(){
		log.info( "--getParKey(start)");
		String jsonStr = null;

		try {
			List<ParamShowInfoBean> AllInfoList = conParamManageDao.getParKey();

			int size = 0;
			if (AllInfoList != null && AllInfoList.size() > 0) {
				size = AllInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					AllInfoList, size, new ParamShowInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取配置参数信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "配置参数管理->查询配置参数信息失败：" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getParKey(over)" );
		return null;
		
	}
	
	/**
	 * 删除配置项信息
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/delParamShowAction.action", method = RequestMethod.POST)
	public String delParamShow(String data){
		JSONObject modelJson = JSONObject.fromObject(data);
		String par_keys = (String) modelJson.getOrDefault("par_keys", "");
		String par_show_ids = (String) modelJson.getOrDefault("par_show_ids", "");
		
		log.info( "--delParamShow(start)-->par_show_ids:" + par_show_ids + ",par_keys:" + par_keys);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean result = conParamManageDao
						.delParamShow(par_show_ids, par_keys);
				jsonResp.put("success", result);
				if (result) {
					log.debug( "--delParamShow-->删除配置项成功!!-->par_show_ids:" +  par_show_ids + ",par_keys:" + par_keys);
					jsonResp.put("message", "删除配置项成功!!");
					jsonResp.put("code", 20000);
				} else {
					log.debug( "--delParamShow-->删除配置项失败!!-->par_show_ids:" +  par_show_ids + ",par_keys:" + par_keys);
					jsonResp.put("message", "删除配置项失败!!");
				}
				jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "删除配置项失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "配置参数管理->删除配置项失败！" + e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--delParamShow(over)");
		return null;
	}
	
	/**
	 *校验参数项是否已经创建参数配置
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/conParamManage/checkDelExistAction.action", method = RequestMethod.POST)
	public String checkDelExist(String par_show_ids,String par_keys) {
		log.info( "--checkDelExist-->par_show_ids:" + par_show_ids + ",par_keys:" + par_keys);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count;
		try {
			count = conParamManageDao.checkDelExist(par_show_ids, par_keys);
			log.debug( "--checkDelExist count:" + count);
			if (count == 0) {
					jsonResp.put("success", true);
					jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "删除项中存在已创建配置参数的项,此操作会同步删除关联的配置参数");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			log.error( "配置参数管理->校验参数项是否已创建参数失败：" + e.toString(), e);
			count = -1;
		}
		this.outJsonString(jsonStr);
		log.info( "--checkDelExist(over)");
		return null;
	}
	
}
