package com.sunyard.console.common.config.bean;

import java.util.Map;

/**
 * <p>
 * Title: Console配置Bean
 * </p>
 * <p>
 * Description: 读取consoleConfig.properties配置信息返回对象
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ConsoleConfigBean {
	private String token_able_time;
	private String server_DM_Name;
	private String server_UA_Name;
	private String otherConsoleUrl;
	private String one_user_can_deleteBatch;
	private String offLineConsoleUrl;
	private int 	monitorTime=1;
	private int logout_max_day;//超过多少天必须修改密码
	private int logout_remind_day;//超过多少天进行修改密码提醒
	private boolean haixiaSSO;
	private int loginErrorCount;
	private int loginErrorInterval;
	private int maxLoginSessionInterval;
	private int maxIssueThreadSize;
	private Map<String, String> monitAddress;//监控地址
	private int separate_table_type;//分表模式(0为默认自动分表，1为手动分表)
	private String sessionType;
	private String es_sunecmdmip;
	private String dubboIsOn;
	
	public String getDubboIsOn() {
        return this.dubboIsOn;
    }

    public void setDubboIsOn(String dubboIsOn) {
        this.dubboIsOn = dubboIsOn;
    }

	public String getEs_sunecmdmip() {
		return es_sunecmdmip;
	}
	public void setEs_sunecmdmip(String es_sunecmdmip) {
		this.es_sunecmdmip = es_sunecmdmip;
	}public String getSessionType() {
		return sessionType;
	}
	public void setSessionType(String sessionType) {
		this.sessionType = sessionType;
	}
	public Map<String, String> getMonitAddress() {
		return monitAddress;
	}
	public void setMonitAddress(Map<String, String> monitmap) {
		this.monitAddress = monitmap;
	}
	public int getMaxIssueThreadSize() {
		return maxIssueThreadSize;
	}
	public void setMaxIssueThreadSize(int maxIssueThreadSize) {
		this.maxIssueThreadSize = maxIssueThreadSize;
	}
	public int getMaxLoginSessionInterval() {
		return maxLoginSessionInterval;
	}
	public void setMaxLoginSessionInterval(int maxLoginSessionInterval) {
		this.maxLoginSessionInterval = maxLoginSessionInterval;
	}
	public int getLoginErrorCount() {
		return loginErrorCount;
	}
	public void setLoginErrorCount(int loginErrorCount) {
		this.loginErrorCount = loginErrorCount;
	}
	public int getLoginErrorInterval() {
		return loginErrorInterval;
	}
	public void setLoginErrorInterval(int loginErrorInterval) {
		this.loginErrorInterval = loginErrorInterval;
	}
	public boolean isHaixiaSSO() {
		return haixiaSSO;
	}
	public void setHaixiaSSO(boolean haixiaSSO) {
		this.haixiaSSO = haixiaSSO;
	}
	public int getMonitorTime() {
		return monitorTime;
	}
	public void setMonitorTime(int monitorTime) {
		this.monitorTime = monitorTime;
	}
	public int getLogout_max_day() {
		return logout_max_day;
	}
	public void setLogout_max_day(int logoutMaxDay) {
		logout_max_day = logoutMaxDay;
	}
	public int getLogout_remind_day() {
		return logout_remind_day;
	}
	public void setLogout_remind_day(int logoutRemindDay) {
		logout_remind_day = logoutRemindDay;
	}
	public String getOffLineConsoleUrl() {
		return offLineConsoleUrl;
	}
	public void setOffLineConsoleUrl(String offLineConsoleUrl) {
		this.offLineConsoleUrl = offLineConsoleUrl;
	}
	public String getOne_user_can_deleteBatch() {
		return one_user_can_deleteBatch;
	}
	public void setOne_user_can_deleteBatch(String oneUserCanDeleteBatch) {
		one_user_can_deleteBatch = oneUserCanDeleteBatch;
	}
	public String getOtherConsoleUrl() {
		return otherConsoleUrl;
	}
	public void setOtherConsoleUrl(String otherConsoleUrl) {
		this.otherConsoleUrl = otherConsoleUrl;
	}
	public String getToken_able_time() {
		return token_able_time;
	}
	public void setToken_able_time(String token_able_time) {
		this.token_able_time = token_able_time;
	}
	public String getServer_DM_Name() {
		return server_DM_Name;
	}
	public void setServer_DM_Name(String server_DM_Name) {
		this.server_DM_Name = server_DM_Name;
	}
	public String getServer_UA_Name() {
		return server_UA_Name;
	}
	public void setServer_UA_Name(String serverUAName) {
		server_UA_Name = serverUAName;
	}
	public int getSeparate_table_type() {
		return separate_table_type;
	}
	public void setSeparate_table_type(int separate_table_type) {
		this.separate_table_type = separate_table_type;
	}

}
