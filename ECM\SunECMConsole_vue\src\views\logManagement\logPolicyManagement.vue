<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-if="this.hasPerm('addLog')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        新增日志
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="当前级别" min-width="16%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.level_t}}</span>
        </template>
      </el-table-column>

      <el-table-column label="类路径" min-width="16%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.class_path}}</span>
        </template>
      </el-table-column>
      <el-table-column label="名称" min-width="14%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.save_path }}</span>
        </template>
      </el-table-column>

      <el-table-column label="日志大小(M)" min-width="12%" align="center" >
        <template slot-scope="{ row }">
          <span>{{ row.log_size }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        min-width="22%"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <el-button v-if="hasPerm('modifyLog')"  type="primary"  icon="el-icon-edit" size="mini" @click="handleUpdate(row)">
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getLogPolicys"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="类路径" prop="c_calss_path">
          <el-input
            v-model="temp.c_calss_path"
            :disabled="attribute_code_status ? true : false"
            prop="attribute_code"
            onkeyup="value=value.replace(/[\u4e00-\u9fa5]/g,'')"
          />
        </el-form-item>
                  <el-form-item label="日志级别" prop="level">
                        <el-select v-model="temp.level" placeholder="请选择">
                           <el-option
                              v-for="at in levels"
                              :key="at.key"
                              :label="at.display_name"
                              :value="at.key"
                            />
                        </el-select>
                    </el-form-item>
        <el-form-item label="文件夹名称" prop="save_path">
            <el-input v-model="temp.save_path"   :disabled="attribute_code_status ? true : false"/>
        </el-form-item>
        <el-form-item label="日志大小" prop="log_size">
          <el-input v-model="temp.log_size" type="number" min="0" step="10" />
        </el-form-item>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="dialogStatus === 'create1' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
 getLogRuleList,
 configLogRule
} from "@/api/logManage";

import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import {checkEnglish } from '@/utils/validate.js'

import global from "../../store/global.js";
const canNull = [
  { key: "1", display_name: "是" },
  { key: "0", display_name: "否" },
];



export default {
  name: "attributeManage",
  components: { Pagination },
  directives: { waves,elDragDialog },
  data() {
    return {
        levels: [
             { key: "1", display_name: "DEBUG" },
              { key: "2", display_name: "INFO" },
               { key: "3", display_name: "WARN" },
                { key: "4", display_name: "ERROR" },
               { key: "5", display_name: "OFF" },
            ],
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
      },
      temp: {
        level: "",
        c_calss_path: "",
        log_id: "",
        save_path: "",
        level_k: "",
        log_size:"",
        optionFlag: "",
      },
      canNull,
      dialogFormVisible: false,
      textMap: {
        update: "修改属性",
        create: "新增属性",
      },
      dialogPvVisible: false,
      downloadLoading: false,
      attribute_code_status: false,
            rules: {
              server_name: [
                { required: true,
                  pattern: global.regexName,
                  message: global.regexNameText,
                  trigger: "blur" },
              ],
              c_calss_path: [
                { required: true, message: "例如输入com.sunyard", trigger: "blur" },
              ],
              level: [
                { required: true, message: "日志级别不能为空", trigger: "blur" },
              ],
              save_path: [
                { required: true, message: "请输入日志存放文件夹名称", trigger: "blur" },
              ],
              log_size: [
                { required: true, message: "请输入日志大小", trigger: "blur" },
              ],
            },
    };
  },

  created() {
    this.getLogPolicys();
  },

  methods: {
    getLogPolicys() {
    this.listLoading = true;
        getLogRuleList(this.listQuery).then((response) => {
          this.list = response.root;
          this.total = Number(response.totalProperty)
              this.listLoading = false;
        });
      },
    resetTemp() {
      this.temp = {
        level: "",
        c_calss_path: "",
        save_path: "",
        level_k: "",
        log_size: "",
        optionFlag: "",
        log_id:"",
      };
    },
    handleCreate() {
      this.resetTemp();
      this.attribute_code_status = false;
      this.dialogStatus = "create1";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
          createData() {
                this.temp.optionFlag = "create1";
                configLogRule(this.temp).then(() => {
                  this.dialogFormVisible = false;
                  this.$notify({
                    title: 'Success',
                    message: 'Created Successfully',
                    type: 'success',
                    duration: 2000
                  })
                  this.getLogPolicys();
                })
          },

    handleUpdate(row) {
     this.resetTemp();
     this.temp.optionFlag = "modify1";
      this.attribute_code_status = true;
      this.temp = Object.assign({}, row); // copy obj
       this.temp.level_k = row.level_t;
      this.temp.log_id=row.id;
      this.temp.c_calss_path = row.class_path;
      this.dialogStatus = "update";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    //更新
    updateData() {
           this.temp.optionFlag = "modify1";
          configLogRule(this.temp).then(() => {
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Update Successfully",
              type: "success",
              duration: 2000,
            });
             this.getLogPolicys();
          });
    },
        //检查文件夹
        checkFile() {
              checkSavePath(this.temp).then(() => {
                this.dialogFormVisible = false;
                this.$notify({
                  title: "Success",
                  message: "Update Successfully",
                  type: "success",
                  duration: 2000,
                });
                 this.getLogPolicys();
              });
        },
  },
};
</script>
