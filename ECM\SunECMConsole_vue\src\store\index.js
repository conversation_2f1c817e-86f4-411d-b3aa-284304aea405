import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import app from './modules/app'
import settings from './modules/settings'
import user from './modules/user'
import attribute from './modules/attribute/attribute'
import permission from "@/store/modules/permission";
import tagsView from "@/store/modules/tagsView";


Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    settings,
    user,
    attribute,
    permission,
    tagsView
  },
  getters
})

export default store
