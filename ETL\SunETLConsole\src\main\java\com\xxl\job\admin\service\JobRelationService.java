package com.xxl.job.admin.service;

import com.xxl.job.admin.core.model.JobRelation;
import com.xxl.job.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 任务关系
 */
public interface JobRelationService {
    //获取参数列表
    public Map<String,Object> jobRelationPageList(int start, int length, int jobGroup, String executorHandler, String filterTime);


    /**
     * 参数信息
     * @param jobRelation
     * @return
     */
    public ReturnT<String> add(List<JobRelation> jobRelation);

    /**
     * 修改参数配置
     * @param jobRelation
     * @return
     */
    public  ReturnT<String> reschedule(JobRelation jobRelation);

    /**
     * 删除参数
     * @param id
     * @return
     */
    public ReturnT<String> remove(int id);

    String jobQueryList(int id);

    JobRelation loadJobRelation(int id);
}
