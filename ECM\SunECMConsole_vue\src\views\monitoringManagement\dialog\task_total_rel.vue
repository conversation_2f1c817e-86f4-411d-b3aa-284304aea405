<template>
  <div>
	 <el-date-picker v-model="queryMonth" type="month" value-format="yyyy-MM">
	 </el-date-picker>	
	<el-button type="text" @click="queryMonthData()">查询当月数据</el-button>
	<el-date-picker v-model="queryDate" type="date" value-format="yyyy-MM-dd">
	 </el-date-picker>	
	<el-button type="text" @click="queryDayData()">查询当日数据</el-button>
	<!-- <div id='button1'>
			<select id="selYear"></select> 
			<font color='#e2e9ff'> 年</font> 
			<select id="selMonth"></select> 
			<font color='#e2e9ff'> 月</font> 
			<el-button @click="queryMonthData()">查询</el-button>
	</div> -->
	
	<el-row :gutter="32">
		<el-col :xs="24" :sm="24" :lg="10">
			<div :id="canvas"  :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
	 </el-row>
  </div>
</template>


<style>
/* .transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
} */
</style>

<script>
import {getTaskAction,getTaskDayAction} from '@/api/monitorManage'
import * as echarts from 'echarts'

export default {
  name: "task-total-rel",
    props: {
	listQuery: {
      require: true,
      type: Object,
    },
    canvas: {
      type: String,
      default: 'taskchart'
    },
    width: {
      type: String,
      default: '1100px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
      default: '16px 16px'
    }
  },
  data() {
    return{
		init : 0,
        successTask : [], 
        failTask : [], 
		taskchart : null,
		taskdaychart : null,
		days : [],
		daySuccessTask : [], 
		dayFailTask : [], 
		hours : [], 
		queryMonth:this.getNowMonth(),
		queryDate:this.getNowDate(),
		echartTitle : ""
	}
  },
  
    mounted() {
      this.$nextTick(() => {
		//   this.checkInit();
      })
    },


  methods: {
	getNowMonth(){
		let now = new Date();
		let year = now.getFullYear();
		let month = now.getMonth()+1;
		month = month < 10 ? "0" + month : month;

		return `${year}-${month}`;
	},
	getNowDate(){
		let now = new Date();
		let year = now.getFullYear();
		let month = now.getMonth()+1;
		let day = now.getDate();
		month = month < 10 ? "0" + month : month;
		day = day < 10 ? "0" + day : day;
		
		return `${year}-${month}-${day}`;
	},
	checkInit(){
		let taskoption = this.listQuery.option;
		if(taskoption == 'UPLOAD'){
			this.echartTitle = "上传"
		}else if(taskoption == 'UPDATE'){
			this.echartTitle = "更新"
		}else if(taskoption == 'HEIGQUERY'){
			this.echartTitle = "高级检索"
		}else if(taskoption == 'QUERY'){
			this.echartTitle = "查询"
		}else if(taskoption == 'GETFILE'){
			this.echartTitle = "文件下载"
		}else if(taskoption == 'MIGRATE'){
			this.echartTitle = "批次迁移"
		}
		if (this.init == 0) {
			let selYear = this.queryMonth.split("-")[0];
			let selMonth = this.queryMonth.split("-")[1];
			this.time = new Date(selYear,selMonth, 0).getDate();
			this.days = this.getDays();
			this.showTaskMonthChart();
			this.showTaskMonthData();
		}
	},
	queryMonthData() {
		let selYear = this.queryMonth.split("-")[0];
		let selMonth = this.queryMonth.split("-")[1];
		this.time = new Date(selYear,selMonth, 0).getDate();
		this.days = this.getDays();
		this.showTaskMonthChart();
		this.showTaskMonthData();
	},

	queryDayData() {
		this.hours = [];
		for (let i = 0; i < 24; i++) {
			this.hours.push(i);
		}
		this.showTaskDayChart();
		this.showTaskDayData();
	},

	getDays() {
		let temp = [];
		for (let i = 1; i <= this.time; i++) {
			temp.push(i);
			this.successTask.push(0);
			this.failTask.push(0);
		}
		return temp;
	},
    showTaskMonthChart(){
      	this.taskchart = echarts.init(document.getElementById(this.canvas), 'dark');
        let option = {
			title : {
				textStyle : {
					color : '#e2e9ff',
				},
				x : 'center',
				text : this.echartTitle + '接口任务数统计'
			},
			legend : {
				textStyle : {
					color : '#e2e9ff'
				},
				x : 'right',
				data : [ '成功', '失败' ]
			},
			tooltip : {
				trigger : 'axis',
				axisPointer : {
					type : 'shadow',
				// animation: true
				}
			},
			grid : { //控制图的大小
				left : '3%',
				right : '5%',
				bottom : '3%',
				containLabel : true
			},
			xAxis : [ {
				name : 'Day',
				axisLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				axisLabel : {
					margin : 10,
					color : '#e2e9ff',
					textStyle : {
						fontSize : 12
					},
				},
				axisLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				axisLabel : {
					margin : 10,
					color : '#e2e9ff',
					textStyle : {
						fontSize : 12
					},
				},
				triggerEvent : {
					componentType : 'xAxis',
					value : '',
					name : 'trigger'
				},
				type : 'category',
				data : this.days
			} ],
			yAxis : [ {
				name : '(笔)',
				nameTextStyle : {
					color : "#e2e9ff"
				},
				type : 'value',
				axisLabel : {
					formatter : '{value}',
					color : '#e2e9ff',
				},
				axisLine : {
					show : false
				},
				splitLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				type : 'value'
			} ],
			series : [{
						name : '成功',
						type : 'bar',
						barWidth : '35%',
						stack : '数量',
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0,0,0,1,
										[{
											offset : 0,
											color : 'rgba(0,244,255,1)' // 0% 处的颜色
										},
										{
											offset : 1,
											color : 'rgba(0,77,167,1)' // 100% 处的颜色
										} ], false),
								barBorderRadius : [ 30, 30, 30, 30 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						// 各事件对应的成功个数
						data : this.successTask
					},
					{
						name : '失败',
						type : 'bar',
						barWidth : '35%',
						stack : '数量',
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0,0,0,1,
										[{
											offset : 0,
											color : 'rgba(238,93,2,1)' // 0% 处的颜色
										},
										{
											offset : 1,
											color : 'rgba(237,206,53,1)' // 100% 处的颜色
										} ], false),
								barBorderRadius : [ 20, 20, 20, 20 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						//各事件对应的失败个数
						data : this.failTask
					} ]
						};

    this.taskchart.setOption(option);
    },

    showTaskMonthData(){
		this.listQuery.date = this.queryMonth.replace("-","");
		getTaskAction(this.listQuery).then(response => {
			this.taskchart.hideLoading();
			this.failTask = [];
			this.successTask = [];
			// let date = [];
			for (let i = 1; i <= this.time; i++) {
				if (i <= 9) {
					i = '0' + i;
				}
				if (response['' + i] != "undefined"
						&& response['' + i] != null) {
					this.failTask.push(response[''+ i].failTask);
					this.successTask.push(response[''+ i].successTask);
				} else {
					this.failTask.push(0);
					this.successTask.push(0);
				}
			}

			this.taskchart.setOption({
				xAxis : {
					data : this.days
				},
				series : [ {
					data : this.successTask
				}, {
					data : this.failTask
				} ]
			})
		})
 	},

	showTaskDayChart(){
      	this.taskdaychart = echarts.init(document.getElementById(this.canvas), 'dark');
		let year = this.queryDate.split("-")[0];
		let month = this.queryDate.split("-")[1];
		let day = this.queryDate.split("-")[2];
        let option = {						
			title : {
				text : this.echartTitle+'接口任务数统计('+year + '年' + month + '月' + day + '日 )',
				textStyle : {
					color : '#e2e9ff',
				},
				x : 'center'
			},
			legend : {
				textStyle : {
					color : '#e2e9ff'
				},
				x : 'right',
				data : [ '成功', '失败' ]
			},
			tooltip : {
				trigger : 'axis',
				axisPointer : {
					type : 'shadow',
				}
			},
			grid : { //控制图的大小
				/* top:'5%', */
				left : '3%',
				right : '5%',
				bottom : '3%',
				containLabel : true
			},
			xAxis : [ {
				name : 'Hour',
				axisLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				axisLabel : {
					margin : 10,
					color : '#e2e9ff',
					textStyle : {
						fontSize : 12
					},
				},
				axisLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				axisLabel : {
					margin : 10,
					color : '#e2e9ff',
					textStyle : {
						fontSize : 12
					},
				},
			/* 	triggerEvent : {
					componentType : 'xAxis',
					value : '',
					name : 'trigger'
				}, */
				type : 'category',
				data : this.hours
			} ],
			yAxis : [ {
				name : '(笔)',
				nameTextStyle : {
					color : "#e2e9ff"
				},
				type : 'value',
				axisLabel : {
					formatter : '{value}',
					color : '#e2e9ff',
				},
				axisLine : {
					show : false
				},
				splitLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				type : 'value'
			} ],
			series : [
					{
						name : '成功',
						type : 'bar',
						barWidth : '35%',
						stack : '数量',
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0,0,0,1,
									[{
										offset : 0,
										color : 'rgba(0,244,255,1)' // 0% 处的颜色
									},
									{
										offset : 1,
										color : 'rgba(0,77,167,1)' // 100% 处的颜色
									} ], false),
								barBorderRadius : [ 30, 30, 30, 30 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						// 各事件对应的成功个数
						data : this.daySuccessTask
					},
					{
						name : '失败',
						type : 'bar',
						barWidth : '35%',
						stack : '数量',
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0,0,0,1,
										[{
											offset : 0,
											color : 'rgba(238,93,2,1)' // 0% 处的颜色
										},
										{
											offset : 1,
											color : 'rgba(237,206,53,1)' // 100% 处的颜色
										} ], false),
								barBorderRadius : [ 20, 20, 20, 20 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						//各事件对应的失败个数
						data : this.dayFailTask
					} ]
		};

    this.taskdaychart.setOption(option);
    },

    showTaskDayData(){
		this.listQuery.date = this.queryDate.replaceAll("-","");
		getTaskDayAction(this.listQuery).then(response => {
			this.taskdaychart.hideLoading();
			this.dayFailTask = [];
			this.daySuccessTask = [];
			this.hours = [];					
			for ( let key in response) {
				for ( let task in response[key]) {
					if (task == 'successTask') {
						this.daySuccessTask
								.push(Number(response[key][task]));
					} else {
						this.dayFailTask
								.push(Number(response[key][task]));
					}
				}
			}
			this.taskdaychart.setOption({
				series : [ {
					data : this.daySuccessTask
				}, {
					data : this.dayFailTask
				} ]
			});
		})
 	}
  }
};
</script>
<style scoped>

</style>