<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">



	<!--<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">
		<property name="driverClass" value="com.microsoft.sqlserver.jdbc.SQLServerDriver" />
		<property name="jdbcUrl" value="***********************************************************" />
		<property name="user" value="sunecm" />
		<property name="password" value="123" />
		<property name="maxPoolSize" value="2" />
		<property name="initialPoolSize" value="1" />
		<property name="maxIdleTime" value="30" />
		<property name="minPoolSize" value="1" />
		<property name="idleConnectionTestPeriod" value="60" />
	</bean> -->


	<!-- <bean id="directoryIncrementer" class="org.springframework.jdbc.support.incrementer.DB2SequenceMaxValueIncrementer"> <property name="dataSource" ref="dataSource"></property> 
		<property name="incrementerName" value="S_DMS_DIRECTORY"></property> </bean> -->

	<bean id="pageTool" class="com.sunyard.util.pageTool.SqlServerPageTool">
	</bean>
</beans> 