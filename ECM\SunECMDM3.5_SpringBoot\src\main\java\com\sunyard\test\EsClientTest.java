//package com.sunyard.test;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import org.junit.Test;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import com.sunyard.client.Client;
//import com.sunyard.es.EsClient;
//
//
//public class EsClientTest {
//	private final static  Logger log = LoggerFactory.getLogger(Client.class);
//
//
////	//创建索引(通过)
////    @Test
////	public void CreateIndex() {
////       EsClient es = new EsClient();
////       Result<String> result= es.CreateIndex("test111");
////       System.out.print(result);
////       es.CloseClient();
////    }
////
//
////	//删除索引(通过)
////    @Test
////	public void DeleteIndex() {
////       EsClient es = new EsClient();
////       Result<String> result= es.DeleteIndex("test111");
////       System.out.print(result);
////       es.CloseClient();
////    }
//
////	//查询索引,缺少jar包
////   @Test
////	public void SearchIndex() {
////       EsClient es = new EsClient();
////       Result<String> result= es.SearchIndex("test");
////       System.out.print(result);
////       es.CloseClient();
////    }
////
//
////	//批量上传(通过)
////    @Test
////	public void bulkAddDoc() {
////       EsClient es = new EsClient();
////       HashMap map = new HashMap();
////       map.put("name", "1");
////       map.put("SunEsId", "ca");
////       HashMap map1 = new HashMap();
////       map1.put("key", "222");
////       map1.put("SunEsId", "va");
////       List<HashMap<String, String>> list= new ArrayList<HashMap<String, String>>();
////       list.add(map);
////       list.add(map1);
////       Result<String> result= es.bulkAddDoc("shopping", list);
////       System.out.print(result);
////       es.CloseClient();
////    }
//
////	 //修改文档(通过)
////   @Test
////	public void UpdateDoc() {
////       EsClient es = new EsClient();
////       Map map = new HashMap();
////       map.put("title", "华为2");
////       map.put("category","中国2");
////       Result<String> result = es.UpdateDoc("shopping", "2", map);
////       System.out.print(result);
////       es.CloseClient();
////    }
//
//
////  //删除文档(通过)
////    @Test
////	public void DeleteDoc() {
////       EsClient es = new EsClient();
////       Result<String> result= es.DeleteDoc("shopping", "1");
////       System.out.print(result);
////       es.CloseClient();
////    }
//
////    //根据id查询文档(通过)
////    @Test
////	public void SearchDoc() {
////       EsClient es = new EsClient();
////       Result<String> result = es.SearchDoc("shopping","2");
////       System.out.print(result);
////       es.CloseClient();
////    }
////
////
////    //分页查询所有文档(不确定)
////    @Test
////	public void SearchAllDoc() {
////       EsClient es = new EsClient();
////       Result<String> result = es.SearchAllDoc("shopping", 1, 10);
////       System.out.print(result);
////       es.CloseClient();
////    }
//
//
//  }
