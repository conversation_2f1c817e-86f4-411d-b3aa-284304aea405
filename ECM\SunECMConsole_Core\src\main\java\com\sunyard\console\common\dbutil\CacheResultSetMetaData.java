package com.sunyard.console.common.dbutil;


import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class CacheResultSetMetaData implements ResultSetMetaData {
	
	private class MetaData {
		public String name;
		public int type;
		public MetaData(String name, int type) {
			this.name = name;
			this.type = type;
		}
	}
	
	
	private ResultSetMetaData meta = null;
	
	private List list = new ArrayList();
	
	public CacheResultSetMetaData(ResultSetMetaData meta) throws SQLException {
		this.meta = meta;
		getData();
	}

	private void getData() throws SQLException {
		for (int i = 0; i < meta.getColumnCount(); i++) {
			String name = meta.getColumnName(i+1);
			int type = meta.getColumnType(i+1);
			list.add(new MetaData(name, type));
		}
	}

	public String getCatalogName(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public String getColumnClassName(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public int getColumnCount() throws SQLException {
		return list.size();
	}

	public int getColumnDisplaySize(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public String getColumnLabel(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public String getColumnName(int column) throws SQLException {
		if (column <= 0) {
			throw new IllegalArgumentException();
		}
		return ((MetaData) list.get(column - 1)).name;
	}

	public int getColumnType(int column) throws SQLException {
		if (column <= 0) {
			throw new IllegalArgumentException();
		}
		return ((MetaData) list.get(column - 1)).type;
	}

	public String getColumnTypeName(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public int getPrecision(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public int getScale(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public String getSchemaName(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public String getTableName(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isAutoIncrement(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isCaseSensitive(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isCurrency(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isDefinitelyWritable(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public int isNullable(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isReadOnly(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isSearchable(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isSigned(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isWritable(int column) throws SQLException {
		throw new UnsupportedOperationException();
	}

	public boolean isWrapperFor(Class<?> iface) throws SQLException {
		// TODO Auto-generated method stub
		return false;
	}

	public <T> T unwrap(Class<T> iface) throws SQLException {
		// TODO Auto-generated method stub
		return null;
	}
	
	
}


