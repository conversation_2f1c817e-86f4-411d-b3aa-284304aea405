package com.sunyard.ecm.server.cache;

import com.sunyard.common.ServiceStatus;
import com.sunyard.ecm.server.bean.StoreObjectBean;
import com.sunyard.ecm.server.cache.wsclient.GetServiceConfig;
import com.sunyard.exception.SunECMException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Title:存储对象信息管理类
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class LoadStoreObject {
	private Map<String, StoreObjectBean> storeObject;
	// 获取配置信息类
	private GetServiceConfig getConfig = new GetServiceConfig();
	private final static Logger log = LoggerFactory.getLogger(LoadStoreObject.class);

	/** DM初始化时获取存储对象信息 */
	public LoadStoreObject() {
		setStoreObject();
	}

	/**
	 * 获得存储对象信息
	 * */
	private void setStoreObject() {

		try {
			String gtoupID = LazySingleton.getInstance().load.getNodeInfoBean()
					.getGroup_id();
			List<StoreObjectBean> list = getConfig.getStoreObject(gtoupID);
			storeObject = new HashMap<String, StoreObjectBean>();
			for (StoreObjectBean storeObjectBean : list) {
				storeObject
						.put(storeObjectBean.getVolume_id(), storeObjectBean);
			}
			log.debug( "-DM主动获取存储对象成功-->" + storeObject);
			ServiceStatus.DM_MEMORY_OBG = true;
		} catch (SunECMException e) {
			log.error( "DM主动获取存储对象失败-->" + e.getMessage());
			ServiceStatus.DM_MEMORY_OBG = false;
		}

	}

	/**
	 * 更新存储对象
	 * */
	public void updateStoreObject(List<StoreObjectBean> list) {
		for (StoreObjectBean storeObjectBean : list) {
			storeObject.put(storeObjectBean.getVolume_id(), storeObjectBean);
		}
	}

	public Map<String, StoreObjectBean> getStoreObject() {
		return storeObject;
	}

	public void setStoreObject(Map<String, StoreObjectBean> storeObject) {
		this.storeObject = storeObject;
	}

	public GetServiceConfig getGetConfig() {
		return getConfig;
	}

	public void setGetConfig(GetServiceConfig getConfig) {
		this.getConfig = getConfig;
	}

	/**
	 * 指定卷id获取该存储信息
	 * 
	 * @param volumeId
	 * @return
	 */
	public StoreObjectBean getStoreObjectBeanByVId(String volumeId) {
		StoreObjectBean storeBean = storeObject.get(volumeId);
		return storeBean;
	}

}