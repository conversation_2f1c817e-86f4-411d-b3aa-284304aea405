<?xml version="1.0" encoding="UTF-8"?>

<!-- 配置文件修改时重新加载，默认true -->
<configuration scan="true" scanPeriod="30 seconds">

    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径 -->
    <property name="LOG_HOME" value="D:/logs/sunecm/dm/log"></property>
    <!--定义日志文件的名称-->
    <property name="LOG_NAME" value="SunECMDM"></property>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <!-- 输出日志记录格式 -->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} -
                %msg%n
            </pattern>
        </encoder>
    </appender>

    <!-- console日志输出 -->
    <appender name="ROOT"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/root/${LOG_NAME}.log</file>
        <!--按天生成日志  -->
        <!--
            <rollingPolicy
                class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>${LOG_HOME}/root/console.%d{yyyy-MM-dd}
                </fileNamePattern>
                <MaxHistory>30</MaxHistory>保存30天
            </rollingPolicy>
        -->


        <!--按大小生成日志  -->
        <rollingPolicy
                class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/root/${LOG_NAME}.%d{yyyy-MM-dd}.%i.log.zip
            </fileNamePattern>
            <maxFileSize>300MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <!-- encoder负责两件事，一是把日志信息转换成字节数组，二是把字节数组写入到输出流。 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <!-- 编码 -->
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤器，可以过滤掉不符合条件的日志，INFO及以上的日志被处理，其它的拒绝 -->
        <!-- <filter class="ch.qos.logback.classic.filter.LevelFilter"> <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch> <onMismatch>DENY</onMismatch> </filter> -->
    </appender>
    <!-- error日志输出 按大小 -->
    <appender name="ERROR_FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error/${LOG_NAME}.log</file> <!-- 日志名称 -->
        <rollingPolicy
                class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/error/${LOG_NAME}.%d{yyyy-MM-dd}.log
            </fileNamePattern>
            <MaxHistory>30</MaxHistory><!-- 保存30天 -->
        </rollingPolicy>
        <!-- encoder负责两件事，一是把日志信息转换成字节数组，二是把字节数组写入到输出流。 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <!-- 编码 -->
            <charset>UTF-8</charset>
        </encoder>
        <!-- 过滤器，可以过滤掉不符合条件的日志，INFO及以上的日志被处理，其它的拒绝 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <logger name="org.springframework" level="ERROR"/>

    <logger name="org.apache.dubbo" level="off"/>

    <logger name="org.reflections.Reflections" level="off"/>

    <!-- 设置日志输出级别 -->
    <root level="DEBUG">
        <!-- 文件输出 -->
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="ROOT"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>