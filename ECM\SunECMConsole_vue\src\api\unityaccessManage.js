import request from '@/utils/request'
import EndUrl from './publicUrl.js'
export function getUnityAccessServerListAction(data) {
    const newdata={'start': data.start,'limit': data.limit,'server_name':encodeURI(data.server_name)}
    return request({
      url: '/untiyAccessServerManage/getUnityAccessServerListAction'+EndUrl.EndUrl,
      method:'post',
      params: { data: newdata } 
      //params: {'start': data.start,'limit': data.limit,'server_name': data.server_name}
    })
}
export function disableUA(data) {
    return request({
      url: '/untiyAccessServerManage/stopUnityAccessServerAction'+EndUrl.EndUrl,
      method: 'post',
      params: { data: data } 
      //params: { 'server_ids': data.server_id}
})
}
export function activeUA(data) {
        return request({
          url: '/untiyAccessServerManage/startUnityAccessServerAction'+EndUrl.EndUrl,
          method: 'post',
          params: { data: data } 
          //params: { 'server_ids': data.server_id}
    })
}
export function checkServerNameAction(data) {
    const newdata={'server_id' : data.server_id, 'server_name': encodeURI(data.server_name)}
            return request({
              url: '/untiyAccessServerManage/checkServerNameAction'+EndUrl.EndUrl,
              method: 'post',
              params: { data: newdata }
        })
}
export function checkServerIPandPortAction(data) {
    const newdata={'server_id' : data.server_id, 'server_ip' : data.server_ip,'http_port' : data.http_port,
                   'https_port': data.https_port,'socket_port': data.socket_port}
            return request({
              url: '/untiyAccessServerManage/checkServerIPandPortAction'+EndUrl.EndUrl,
              method: 'post',
              params: { data: newdata }
        })
}
export function updateUA(data) {
    const newdata={'optionFlag': data.optionFlag,
                   'server_id' : data.server_id,
                   'group_id'  :data.group_id,
                   'server_name': encodeURI(data.server_name),
                   'server_ip' : data.server_ip,
                   'http_port' : data.http_port,
                   'https_port': data.https_port,
                   'socket_port': data.socket_port,
                   'state': data.state,
                   'remark': encodeURI(data.remark),
                   'trans_protocol': data.trans_protocol}
        return request({
          url: '/untiyAccessServerManage/addUnityAccessServerAction'+EndUrl.EndUrl,
          method: 'post',
          params: { data: newdata }
    })
}
export function createUA(data) {
    const newdata={'optionFlag': data.optionFlag,
                   'server_id' : data.server_id,
                   'group_id'  :data.group_id,
                   'server_name': encodeURI(data.server_name),
                   'server_ip' : data.server_ip,
                   'http_port' : data.http_port,
                   'https_port': data.https_port,
                   'socket_port': data.socket_port,
                   'state': data.state,
                   'remark': encodeURI(data.remark),
                   'trans_protocol': data.trans_protocol}
            return request({
              url: '/untiyAccessServerManage/addUnityAccessServerAction'+EndUrl.EndUrl,
              method: 'post',
              params: { data: newdata }
        })
}
export function getGroupListAction(data) {
            return request({
              url: '/untiyAccessServerManage/getUnityAccessServerGroupListAction'+EndUrl.EndUrl,
              method: 'post',
              params: { data: data }
              //params: {'start': data.start,'limit': data.limit}
        })
}
export function getUnRelUnityAccessServerTreeAction() {
            return request({
              url: '/untiyAccessServerManage/getUnRelUnityAccessServerTreeAction'+EndUrl.EndUrl,
              method: 'post',
              params: {}
        })
}
export function getRelUnityAccessServerTreeAction(data) {
            return request({
              url: '/untiyAccessServerManage/getRelUnityAccessServerTreeAction'+EndUrl.EndUrl,
              method: 'post',
              params: { data: data }
              //params: {'group_id':data.group_id}
        })
}
export function addUnityAccessServerGroupAction(data) {
            const newdata={'server_ids':data.server_ids,'group_id':data.group_id,'group_name':encodeURI(data.group_name),
                               'ip':data.ip,'http_port':data.http_port,'socket_port':data.socket_port,'remark':encodeURI(data.remark),
                               'optionFlag':data.optionFlag}
            return request({
              url: '/untiyAccessServerManage/addUnityAccessServerGroupAction'+EndUrl.EndUrl,
              method: 'post',
              params: { data: newdata }
        })
}
export function checkGroupNameAction(data) {
    const newdata={'group_id':data.group_id,'group_name':encodeURI(data.group_name)}
        return request({
          url: '/untiyAccessServerManage/checkGroupNameAction'+EndUrl.EndUrl,
          method: 'post',
      params: { data: newdata }
    })
}
export function checkServerGroupIPandPortAction(data) {
    const newdata={'group_id':data.group_id,'ip':data.ip,'http_port':data.http_port,'socket_port':data.socket_port}
        return request({
          url: '/untiyAccessServerManage/checkServerGroupIPandPortAction'+EndUrl.EndUrl,
          method: 'post',
      params: { data: newdata }
    }) 
}

export function singletonInfoSearch(data){
    let url = '/singletonManage/getUASingletonInfoAction'+EndUrl.EndUrl;
    return request({
      url: url,
      method: 'post',
      params: { data: data }
    })
  }
  
  export function resetSingletonInfoSearch(data){
    let url = '/singletonManage/resetUASingletonInfoAction'+EndUrl.EndUrl;
    const obj = {
        'server_ip' : data.server_ip,
        'http_port': data.http_port.toString()
      }
      return request({
        url: url,
        method: 'post',
        params: { data: obj }
      })
  }