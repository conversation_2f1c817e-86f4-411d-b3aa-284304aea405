# Default Properties file for use by StdSchedulerFactory
# to create a Quartz Scheduler Instance, if a different
# properties file is not explicitly specified.
#

org.quartz.scheduler.instanceName= DefaultQuartzScheduler
org.quartz.scheduler.rmi.export= false
org.quartz.scheduler.rmi.proxy= false
org.quartz.scheduler.wrapJobExecutionInUserTransaction= false

org.quartz.threadPool.class= org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount= 10
org.quartz.threadPool.threadPriority= 5
org.quartz.threadPool.threadsInheritContextClassLoaderOfInitializingThread= true

org.quartz.jobStore.misfireThreshold= 5000

#==============================================================
#Configure JobStore 配置Job存储
#==============================================================
#持久化方式配置
org.quartz.jobStore.class = org.quartz.simpl.RAMJobStore
#org.quartz.jobStore.class=  org.quartz.impl.jdbcjobstore.JobStoreTX
#持久化方式配置数据驱动，标准数据库（如MYSQL）
#org.quartz.jobStore.driverDelegateClass = org.quartz.impl.jdbcjobstore.oracle.OracleDelegate
##quartz相关数据表前缀名
#org.quartz.jobStore.tablePrefix = QRTZ_
##配置数据源
#org.quartz.jobStore.dataSource = myDS
#
##==============================================================
##Configure DataSource 配置数据源
##==============================================================
#org.quartz.dataSource.myDS.driver = oracle.jdbc.driver.OracleDriver
#org.quartz.dataSource.myDS.URL = ******************************************
#org.quartz.dataSource.myDS.user = sunaos_102b
#org.quartz.dataSource.myDS.password = sunaos
#org.quartz.dataSource.myDS.maxConnections = 5
##validationQuery是用来验证数据库连接的查询语句，这个查询语句必须是至少返回一条数据的SELECT语句
#org.quartz.dataSource.myDS.validationQuery = select 0 from dual