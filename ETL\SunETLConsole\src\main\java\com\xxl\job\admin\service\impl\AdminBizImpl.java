package com.xxl.job.admin.service.impl;

import com.xxl.job.admin.core.Constants;
import com.xxl.job.admin.core.enums.WriteTypeEnum;
import com.xxl.job.admin.core.model.JobRelation;
import com.xxl.job.admin.core.model.XxlJobInfo;
import com.xxl.job.admin.core.model.XxlJobLog;
import com.xxl.job.admin.core.schedule.XxlJobDynamicScheduler;
import com.xxl.job.admin.core.trigger.XxlJobTrigger;
import com.xxl.job.admin.dao.*;
import com.xxl.job.admin.service.XxlJobService;
import com.xxl.job.core.biz.AdminBiz;
import com.xxl.job.core.biz.model.HandleCallbackParam;
import com.xxl.job.core.biz.model.RegistryParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.JobStartEnum;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> 2017-07-27 21:54:20
 */
@Service
public class AdminBizImpl implements AdminBiz {
    private static Logger logger = LoggerFactory.getLogger(AdminBizImpl.class);

    @Resource
    public XxlJobLogDao xxlJobLogDao;
    @Resource
    private XxlJobInfoDao xxlJobInfoDao;
    @Resource
    private XxlJobRegistryDao xxlJobRegistryDao;
    @Resource
    private XxlJobService xxlJobService;
    @Resource
    private JobRelationDao jobRelationDao;
    @Resource
    private JobDateDao jobDateDao;


    @Override
    public ReturnT<String> callback(List<HandleCallbackParam> callbackParamList) {
        for (HandleCallbackParam handleCallbackParam : callbackParamList) {
            ReturnT<String> callbackResult = callback(handleCallbackParam);
            logger.info("JobApiController.callback {}, handleCallbackParam={}, callbackResult={}",
                    (callbackResult.getCode() == ReturnT.SUCCESS_CODE ? "success" : "fail"), handleCallbackParam, callbackResult);
        }

        return ReturnT.SUCCESS;
    }

    private ReturnT<String> callback(HandleCallbackParam handleCallbackParam) {
        // valid log item
        logger.info("任务回调开始进行处理:");
        XxlJobLog log = xxlJobLogDao.load(handleCallbackParam.getLogId());

        if (log == null) {
            logger.error("根据回调日志id没有查询该日志Id");
            return new ReturnT<String>(ReturnT.FAIL_CODE, "log item not found.");
        }
        XxlJobInfo xxlJobInfo = xxlJobInfoDao.loadById(log.getJobId());
        logger.info("根据日志信息关联查询任务信息" + xxlJobInfo);
        String childTriggerMsg = "";

        StringBuffer handleMsg = new StringBuffer();
        if (log.getHandleMsg() != null) {
            handleMsg.append(log.getHandleMsg()).append("<br>");
        }
        if (handleCallbackParam.getExecuteResult().getMsg() != null) {
            handleMsg.append(handleCallbackParam.getExecuteResult().getMsg());
        }

        // success, save log
        log.setHandleTime(new Date());
        log.setHandleCode(handleCallbackParam.getExecuteResult().getCode());
        log.setHandleMsg(handleMsg.toString());
        log.setExecutortime(String.valueOf(handleCallbackParam.getExecuteResult().getTime()));
        logger.info("任务"+xxlJobInfo+"更新执行状态");
        xxlJobLogDao.updateHandleInfo(log);
        //如果是日终任务更新状态值到info表
        if (WriteTypeEnum.match(xxlJobInfo.getWriteType()) == WriteTypeEnum.REACTIVE) {
            if (ReturnT.SUCCESS_CODE == handleCallbackParam.getExecuteResult().getCode()) {
                xxlJobInfoDao.updateJobSatrt(xxlJobInfo.getId(),String.valueOf(JobStartEnum.TASK_STATE_HAS_VISITED.getCode()));
                return ReturnT.SUCCESS;
            }else {
                xxlJobInfoDao.updateJobSatrt(xxlJobInfo.getId(),String.valueOf(handleCallbackParam.getExecuteResult().getError_code()));
            }
        }



        //如果任务执行成功需要去执行子任务
        if (ReturnT.SUCCESS_CODE == handleCallbackParam.getExecuteResult().getCode()) {
            logger.info("任务执行成功，判断该任务是否又依赖任务需要执行");
            List<JobRelation> jobRelations = null;
            try {
                jobRelations = jobRelationDao.loadJByJobId(log.getJobId());
            } catch (Exception e) {
                e.printStackTrace();
            }
            if (WriteTypeEnum.match(xxlJobInfo.getWriteType()) == WriteTypeEnum.AUTO) {//日终任务不需要进行依赖调度
                if (xxlJobInfo != null && jobRelations != null && jobRelations.size() > 0) {
                    childTriggerMsg = "<hr>";
                    for (int i = 0; i < jobRelations.size(); i++) {
                        JobRelation jobRelation = jobRelations.get(i);
                        if (jobRelation != null) {
                            //已经在Quartz队列中,则不需要进行任务依赖调度,由Quartz调度
                            childTriggerMsg += validatedispatch(i,jobRelation,jobRelations.size());
                        } else {
                            childTriggerMsg += MessageFormat.format("<br> {0}/{1} 触发子任务失败, 子任务Key格式错误, 子任务Key: {2}",
                                    (i + 1), jobRelations.size(), "未找到子任务");
                        }
                    }

                    StringBuffer attemperMsg = new StringBuffer();
                    if (childTriggerMsg != null && !"".equals(childTriggerMsg)) {
                        attemperMsg.append("<br>子任务触发备注：").append(childTriggerMsg);
                    }
                    log.setAttemperMsg(attemperMsg.toString());
                    xxlJobLogDao.updateAttemperMsg(log);

                }else {
                    logger.info("任务执行成功，没有依赖任务需要执行");
                }
            }
        }


        return ReturnT.SUCCESS;
    }

    public  String validatedispatch(int number,JobRelation jobRelation,int count){

        String childTriggerMsg = "";
        XxlJobInfo xxlJobInfo_child = xxlJobInfoDao.loadById(jobRelation.getChildrenJob());
        //已经在Quartz队列中,则不需要进行任务依赖调度,由Quartz调度
        try {
            if (XxlJobDynamicScheduler.checkExists(String.valueOf(xxlJobInfo_child.getId()), String.valueOf(xxlJobInfo_child.getJobGroup()))) {
                String jobStart = XxlJobDynamicScheduler.queryStart(String.valueOf(xxlJobInfo_child.getId()), String.valueOf(xxlJobInfo_child.getJobGroup()));
                if ("BLOCKED".equals(jobStart) || "WAITING".equals(jobStart) || "PAUSED".equals(jobStart)) {
                    boolean flag = XxlJobDynamicScheduler.resumeJob(String.valueOf(xxlJobInfo_child.getId()), String.valueOf(xxlJobInfo_child.getJobGroup()));
                    childTriggerMsg += MessageFormat.format("<br> {0}/{1} 触发子任务, 当前任务状态为{2},修改Quartz执行状态{3}, 子任务Key: {4}",
                            (number + 1),count,jobStart,flag?"成功" : "失败", xxlJobInfo_child.getId());
                    logger.info(MessageFormat.format("<br> {0}/{1} 触发子任务, 当前任务状态为{2},修改Quartz执行状态{3}, 子任务Key: {4}",
                            (number + 1),count,jobStart,flag?"成功" : "失败", xxlJobInfo_child.getId()));
                }else{
                    childTriggerMsg += MessageFormat.format("<br> {0}/{1} 触发子任务失败, 子任务已经在Quartz中不需要依赖调度, 子任务Key: {2}",
                            (number + 1), count,xxlJobInfo_child.getId());

                    logger.info(MessageFormat.format("<br> {0}/{1} 触发子任务失败, 子任务已经在Quartz中不需要依赖调度, 子任务Key: {2}",
                            (number + 1), count,xxlJobInfo_child.getId()));
                }
            } else {
                ReturnT<String> triggerChildResult = XxlJobTrigger.trigger(Integer.valueOf(jobRelation.getChildrenJob()),null);
                childTriggerMsg += MessageFormat.format("<br> {0}/{1} 触发子任务{2}, 子任务Key: {3}, 子任务触发备注: {4}",
                        (number + 1), count, (triggerChildResult.getCode() == ReturnT.SUCCESS_CODE ? "成功" : "失败"), jobRelation.getChildrenJob(), triggerChildResult.getMsg());
                logger.info(MessageFormat.format("<br> {0}/{1} 触发子任务{2}, 子任务Key: {3}, 子任务触发备注: {4}",
                        (number + 1), count, (triggerChildResult.getCode() == ReturnT.SUCCESS_CODE ? "成功" : "失败"), jobRelation.getChildrenJob(), triggerChildResult.getMsg()));
            }
        } catch (SchedulerException e) {
            logger.error("任务" + xxlJobInfo_child + "在Quartz任务队列中,调用出现问题");
            childTriggerMsg += MessageFormat.format("<br>触发移除实时执行任务失败, 任务Key: {0},失败信息备注:{1}"
                    , xxlJobInfo_child.getId(), e.getMessage());
        }

        return  childTriggerMsg;

    }


    @Override
    public ReturnT<String> registry(RegistryParam registryParam) {
        int ret = xxlJobRegistryDao.registryUpdate(registryParam.getRegistGroup(), registryParam.getRegistryKey(), registryParam.getRegistryValue());
        if (ret < 1) {
            xxlJobRegistryDao.registrySave(registryParam.getRegistGroup(), registryParam.getRegistryKey(), registryParam.getRegistryValue());
        }

        Constants.ALL_HANDLER.put(registryParam.getRegistryKey(), registryParam.getJobHandlerRepository());

        return ReturnT.SUCCESS;
    }

    @Override
    public ReturnT<String> registryRemove(RegistryParam registryParam) {
        xxlJobRegistryDao.registryDelete(registryParam.getRegistGroup(), registryParam.getRegistryKey(), registryParam.getRegistryValue());
        return ReturnT.SUCCESS;
    }

    @Override
    public ReturnT<String> triggerJob(int jobId) {
        return xxlJobService.triggerJob(jobId);
    }

}
