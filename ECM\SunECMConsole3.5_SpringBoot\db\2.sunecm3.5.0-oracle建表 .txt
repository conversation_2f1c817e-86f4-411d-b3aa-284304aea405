﻿drop table APPLY_TOKEN_SERVER;

drop table ATTRIBUTE_SET;

drop table ATTRIBUTE_TYPE_DEFINE;

drop table ATTRIBUTE_VALUE_RANGE;

drop table CACHESERVER_REPORT;

drop table CHECK_LOCK_INFO;

drop table CONFIG_INFO_SYNCHRO;

drop table CONTENT_MODEL_SET;

drop table CONTENT_SERVER_GROUP;

drop table CONTENT_SERVER_INFO;

drop table DMTASK;

drop table ERROR_INFO;

drop table GROUP_LOCK;

drop table INDEXCOMPRESS;

drop table INDEX_INFO;

drop table ITEMTYPE_INSNO_DMSNAME;

drop table LOG_RULE;

drop table MIGRATE_ERROR;

drop table MIGRATE_ERROR_DETAIL;

drop table MODEL_ATTRIBUTE_REL;

drop table MODEL_TABLE_SET;

drop table NEARLINE_ERROR;

drop table PARAM_CONFIG_INFO;

drop table PARAM_SHOW_INFO;

drop table PARAM_TABLE;

drop table ROLE_INFO;

drop table ROLE_MODEL_REL;

drop table ROLE_PERMISSION_MAP;

drop table SCHEDULER;

drop table SCHEDULER_DICTIONARY;

drop table SGROUP_CMODEL_REL;

drop table SYS_PERMISSION;

drop table TOKEN_TABLE;

drop table token_user_rel;

drop table UNITY_ACCESS_SERVER;

drop table UNITY_ACCESS_SERVER_GROUP;

drop table USER_CMODEL_REL;

drop table USER_INFO;

drop table USER_PERMISSION_MAP;

drop table USER_ROLE_MAP;

drop table VOLUME_INFO;

drop table WS_ERROR;

drop table OFFLINE_TASK;

drop table MIGRATE_TASK;
drop table ATTRIBUTE_DESENSITIVE_RULE;
drop table ES_TAGS_ATTRS;
drop table ES_TAGS ;
drop table ES_MODEL_REL;
drop table ES_TAG_ERROR;
drop table ES_ATTRIBUTE_TYPE_DEFINE;

drop sequence S_ATTRIBUTE_VALUE_RANGE;

drop sequence S_CACHESERVER_REPORT;

drop sequence S_CONFIG_INFO_SYNCHRO;

drop sequence S_CONTENT_MODEL_SET;

drop sequence S_CONTENT_SERVER_GROUP;

drop sequence S_CONTENT_SERVER_INFO;

drop sequence S_DMTASK;

drop sequence S_LOG_RULE;

drop sequence S_ROLE_INFO;

drop sequence S_SCHEDULER;

drop sequence S_SCHEDULER_DICTIONARY;

drop sequence S_SGROUP_CMODEL_REL;

drop sequence S_UNITY_ACCESS_SERVER;

drop sequence S_UNITY_ACCESS_SERVER_GROUP;

drop sequence S_VOLUME_INFO;

drop sequence S_INSNO_DMS__REL;

drop sequence S_PARAM_CONFIG_INFO;

drop sequence S_PARAM_SHOW_INFO;

create sequence S_INSNO_DMS__REL;

create sequence S_ATTRIBUTE_VALUE_RANGE;

create sequence S_CACHESERVER_REPORT;

create sequence S_CONFIG_INFO_SYNCHRO;

create sequence S_CONTENT_MODEL_SET;

create sequence S_CONTENT_SERVER_GROUP;

create sequence S_CONTENT_SERVER_INFO;

create sequence S_DMTASK;

create sequence S_LOG_RULE;

create sequence S_ROLE_INFO;

create sequence S_SCHEDULER;

create sequence S_SCHEDULER_DICTIONARY;

create sequence S_SGROUP_CMODEL_REL;

create sequence S_UNITY_ACCESS_SERVER;

create sequence S_UNITY_ACCESS_SERVER_GROUP;

create sequence S_VOLUME_INFO;

create sequence S_PARAM_CONFIG_INFO;

create sequence S_PARAM_SHOW_INFO;

/*==============================================================*/
/* Table: APPLY_TOKEN_SERVER                                    */
/*==============================================================*/
create table APPLY_TOKEN_SERVER  (
   SERVER_INFO          VARCHAR2(100),
   IP                   VARCHAR2(15)
);

/*==============================================================*/
/* Table: ATTRIBUTE_SET                                         */
/*==============================================================*/
create table ATTRIBUTE_SET  (
   ATTRIBUTE_CODE       VARCHAR2(20)                    NOT NULL,
   ATTRIBUTE_NAME       VARCHAR2(30)                    NOT NULL,
   ATTRIBUTE_TYPE       VARCHAR2(10)                     NOT NULL,
   ATTRIBUTE_LENGTH     VARCHAR2(20)                   DEFAULT '0',
   ATTRIBUTE_DEFAULT    VARCHAR2(20),
   ATTRIBUTE_ISNULL     INTEGER                        DEFAULT 1,
   ATTRIBUTE_UPTIME     VARCHAR2(14),
   ATTRIBUTE_UPUSER     VARCHAR2(20),
   ATTRIBUTE_STATE      INTEGER                        DEFAULT 0,
   ATTRIBUTE_IS_MULTIVALUE INTEGER,
   ATTRIBUTE_VALUE_MAPPING INTEGER,
   ATTRIBUTE_MOTEL_TYPE INTEGER,
   ATTRIBUTE_ISBATCH    VARCHAR2(2),
   ATTRIBUTE_OTHERNAME  VARCHAR2(8),
   ATTRIBUTE_DESENRULE VARCHAR(4),
   CONSTRAINT PK_ATTRIBUTE_SET PRIMARY KEY (ATTRIBUTE_CODE)
);

comment on column ATTRIBUTE_SET.ATTRIBUTE_TYPE is
'对应数据库中的 “字符串”、“数字”、“日期”等';

comment on column ATTRIBUTE_SET.ATTRIBUTE_ISNULL is
'0 不 可以为空
1可以为空';

comment on column ATTRIBUTE_SET.ATTRIBUTE_STATE is
'0 未使用,1使用
当状态为1时不能进行删除及修改操作.';

comment on column ATTRIBUTE_SET.ATTRIBUTE_MOTEL_TYPE is
'对应原：ATTRIBUTE_ATTRIBUTE

1:索引对象
2:文档对象
3:索引和文档
4:自定义';

/*==============================================================*/
/* Table: ATTRIBUTE_TYPE_DEFINE                                 */
/*==============================================================*/
create table ATTRIBUTE_TYPE_DEFINE  (
   APP_TYPE_ID          VARCHAR2(4)                     NOT NULL,
   APP_TYPE_NAME        VARCHAR2(16),
   CONSTRAINT PK_ATTRIBUTE_TYPE_DEFINE PRIMARY KEY (APP_TYPE_ID)
);

/*==============================================================*/
/* Table: ATTRIBUTE_VALUE_RANGE                                 */
/*==============================================================*/
create table ATTRIBUTE_VALUE_RANGE  (
   VALUERANGE_ID        INTEGER                         NOT NULL,
   MAPPING_DIC_NAME     VARCHAR2(32),
   CONSTRAINT PK_ATTRIBUTE_VALUE_RANGE PRIMARY KEY (VALUERANGE_ID)
);

/*==============================================================*/
/* Table: CACHESERVER_REPORT                                    */
/*==============================================================*/
create table CACHESERVER_REPORT  (
   GROUP_ID             INTEGER,
   GENERATE_TIME        VARCHAR2(12),
   OBJECT_NAME          VARCHAR2(30),
   DATA_ITEM            VARCHAR2(20),
   DATA_NUM             INTEGER
);

comment on column CACHESERVER_REPORT.DATA_ITEM is
'SEND:发送量
RECEIVE:接收量
WAIT:滞留量';

/*==============================================================*/
/* Table: CHECK_LOCK_INFO                                       */
/*==============================================================*/
create table CHECK_LOCK_INFO  (
   CONTENTID            VARCHAR2(64)                    NOT NULL,
   CHECKFLAG            VARCHAR2(32),
   CHECKTIME            VARCHAR2(16),
   CONSTRAINT PK_CHECK_LOCK_INFO PRIMARY KEY (CONTENTID)
);

/*==============================================================*/
/* Table: CONFIG_INFO_SYNCHRO                                   */
/*==============================================================*/
create table CONFIG_INFO_SYNCHRO  (
   CONFIG_ID            INTEGER                         NOT NULL,
   CONFIG_TYPE          VARCHAR2(10),
   CONFIG_TABLE         VARCHAR2(20),
   NODE_ID              INTEGER,
   SYNCHRO_STATUS       VARCHAR2(1),
   CONFIG_CODE          VARCHAR2(20),
   REMARK               VARCHAR2(100),
   MODIFY_TYPE          VARCHAR2(1),
   SYNCHRO_DATE         VARCHAR2(20),
   TARGET_SERVER_TYPE   INTEGER,
   CONSTRAINT PK_CONFIG_INFO_SYNCHRO PRIMARY KEY (CONFIG_ID)
);

comment on table CONFIG_INFO_SYNCHRO is
'根据<配置类型>判断是那个配置信息被修改，并根据不同的类型查找并下发到相应的缓存（如：路由策略被修改，下发到策略对应的节点）。

根据<配置信息表>和<配置信息编号>查找到对应的表中的记录。';

comment on column CONFIG_INFO_SYNCHRO.CONFIG_ID is
'主键，自增序列';

comment on column CONFIG_INFO_SYNCHRO.CONFIG_TYPE is
'存放当前修改的配置信息属于什么类型：

''1'', ''服务器组和内容对象关联''
''2'', ''内容模型模板''
''3'', ''缓存''
''4'', ''内容存储服务器''
''5'', ''日志''
''6'',''存储对象''
''7'',''统一接入服务器''
''8'',''内容模型分表''';

comment on column CONFIG_INFO_SYNCHRO.CONFIG_TABLE is
'当前修改的配置信息所属的表';

comment on column CONFIG_INFO_SYNCHRO.NODE_ID is
'需要同步的机器编号';

comment on column CONFIG_INFO_SYNCHRO.SYNCHRO_STATUS is
'同步是否成功

1：成功
0：失败';

comment on column CONFIG_INFO_SYNCHRO.CONFIG_CODE is
'被修改的配置信息在所属的配置信息表中对应的唯一编号';

comment on column CONFIG_INFO_SYNCHRO.MODIFY_TYPE is
'1：新增
2：删除
3：修改';

comment on column CONFIG_INFO_SYNCHRO.SYNCHRO_DATE is
'下发日期';

comment on column CONFIG_INFO_SYNCHRO.TARGET_SERVER_TYPE is
'1:缓存
2：统一接入';

/*==============================================================*/
/* Table: CONTENT_MODEL_SET                                     */
/*==============================================================*/
create table CONTENT_MODEL_SET  (
   MODEL_NAME           VARCHAR2(50)                    NOT NULL,
   MODEL_CODE           VARCHAR2(32)                    NOT NULL,
   MODEL_DEFINES_TIME   VARCHAR2(14),
   MODEL_DELINES_PERSON VARCHAR2(14),
   MODEL_TYPE           INTEGER,
   F_MODEL_ID           VARCHAR2(32),
   SEPARATE_TABLE_DAYS  INTEGER                         NOT NULL,
   VERSION_CONTROL      INTEGER                        	DEFAULT 0,
   DATA_FROM            INTEGER,
   TOKEN_CHECK          INTEGER,
   TEXT_SEARCH          INTEGER,
   CREATION_DATE_COLUMN VARCHAR2(20),
   FINISH_DATE_COLUMN   VARCHAR2(20),
   TABLE_SPACE    		VARCHAR2(32),
   ENCODEBYTESIZE 		VARCHAR2(32),
   ENCODE_TYPE 			VARCHAR2(32),
   CONSTRAINT PK_CONTENT_MODEL_SET PRIMARY KEY (MODEL_CODE)
);

comment on table CONTENT_MODEL_SET is
'对应原先的：元数据对象集-METADATA_OBJECT_SET';

comment on column CONTENT_MODEL_SET.MODEL_NAME is
'对应原：OBJECT_NAME_CN';

comment on column CONTENT_MODEL_SET.MODEL_CODE is
'对应原：OBJECT_NAME_EN';

comment on column CONTENT_MODEL_SET.MODEL_DEFINES_TIME is
'对应原：OBJECT_TIME';

comment on column CONTENT_MODEL_SET.MODEL_DELINES_PERSON is
'对应原：OBJECT_USER';

comment on column CONTENT_MODEL_SET.MODEL_TYPE is
'对应原：DOC_FLAG

0:索引对象
1:文档对象';

comment on column CONTENT_MODEL_SET.F_MODEL_ID is
'对应原：F_OBJECT_ID';

comment on column CONTENT_MODEL_SET.SEPARATE_TABLE_DAYS is
'对应原：OBJECT_TABLE_RULE

0  表示 此元数据对象不进行分表操作，
其他表示每多少天进行分表操作';

comment on column CONTENT_MODEL_SET.VERSION_CONTROL is
'对应原：IS_VERSION

0  否
1  是';

comment on column CONTENT_MODEL_SET.DATA_FROM is
'1 为ECM
2 为CM
3 为CE
4 为DOCUMENT

预留做扩展';

comment on column CONTENT_MODEL_SET.TEXT_SEARCH is
'对应原：IS_TEST_SEARCH
1:是
0:否';

comment on column CONTENT_MODEL_SET.CREATION_DATE_COLUMN is
'存储（业务）数据的创建（或办理）时间的字段';

comment on column CONTENT_MODEL_SET.FINISH_DATE_COLUMN is
'存储（业务）数据的结束（或办结）时间的字段';

comment on column CONTENT_MODEL_SET.ENCODEBYTESIZE is
'影像加密长度字节
0：部分加密字节为0
-1：全量加密';

comment on column CONTENT_MODEL_SET.ENCODE_TYPE is
'加密类型
0：不加密
1：SM4部分加密
 2：全量加密';

/*==============================================================*/
/* Table: CONTENT_SERVER_GROUP                                  */
/*==============================================================*/
create table CONTENT_SERVER_GROUP  (
   GROUP_ID             INTEGER                   NOT NULL,
   GROUP_NAME           VARCHAR2(40),
   GROUP_IP             VARCHAR2(15),
   HTTP_PORT            INTEGER,
   SOCKET_PORT          INTEGER,
   STATE                INTEGER,
   OS                   INTEGER,
   REMARK               VARCHAR2(128),
   DEPLOY_MODE          INTEGER,
   BACKUP_GROUP_ID      INTEGER,
   IS_ECM_DB   			INTEGER,
   HTTPS_PORT  			INTEGER,
   TRANS_PROTOCOL   	VARCHAR2(32),
   CONSTRAINT PK_CONTENT_SERVER_GROUP PRIMARY KEY (GROUP_ID)
);

comment on column CONTENT_SERVER_GROUP.GROUP_IP is
'采用第三方其群服务时 ， 集群服务器组对外的IP';

comment on column CONTENT_SERVER_GROUP.STATE is
'0:停用
1:启用';

comment on column CONTENT_SERVER_GROUP.OS is
'1:windows
2:mac
3:linux
4:aix';

comment on column CONTENT_SERVER_GROUP.DEPLOY_MODE is
'0:ECM负载均衡
1:其他集群方式
';

/*==============================================================*/
/* Table: CONTENT_SERVER_INFO                                   */
/*==============================================================*/
create table CONTENT_SERVER_INFO  (
   SERVER_ID            INTEGER                         NOT NULL,
   SERVER_NAME          VARCHAR2(40),
   SERVER_IP            VARCHAR2(15)                    NOT NULL,
   HTTP_PORT            INTEGER                         NOT NULL,
   REMARK               VARCHAR2(128),
   STATE                INTEGER                        	DEFAULT 1,
   ISDB_CONN            INTEGER,
   SOCKET_PORT          INTEGER                         NOT NULL,
   GROUP_ID             INTEGER,
   WEIGHT               INTEGER,
   HTTPS_PORT			INTEGER,
   TRANS_PROTOCOL       VARCHAR2(32),
   CONSTRAINT PK_CONTENT_SERVER_INFO PRIMARY KEY (SERVER_ID),
   CONSTRAINT AK_CACHENODE_INFO_UNI_CONTENT_ unique (SERVER_IP, HTTP_PORT)
);

comment on table CONTENT_SERVER_INFO is
'对应原：CACHENODE_INFO';

comment on column CONTENT_SERVER_INFO.STATE is
'0 停用
1 启用';

comment on column CONTENT_SERVER_INFO.ISDB_CONN is
'1 代表 可用数据库直连
0 通过WEBSERVICES 连接数据库';

/*==============================================================*/
/* Table: DMTASK                                                */
/*==============================================================*/
create table DMTASK  (
   TASK_NO              INTEGER                         NOT NULL,
   TASK_NAME            VARCHAR2(128),
   TASK_CLASS           VARCHAR2(128),
   PARAM_KEY            VARCHAR2(255),
   CONSTRAINT PK_DMTASK PRIMARY KEY (TASK_NO)
);

comment on column DMTASK.PARAM_KEY is
'排程中使用的参数key，如有多个可以;(分号)隔开(如：天数:days;名字:name)';

/*==============================================================*/
/* Table: ERROR_INFO                                            */
/*==============================================================*/
create table ERROR_INFO  (
   ERROR_INFO_CODE      VARCHAR2(6)                     NOT NULL,
   ERROR_INFO_LEVEL     INTEGER,
   ERROR_INFO_DES       VARCHAR2(256),
   CONSTRAINT PK_ERROR_INFO PRIMARY KEY (ERROR_INFO_CODE)
);

/*==============================================================*/
/* Table: GROUP_LOCK                                            */
/*==============================================================*/
create table GROUP_LOCK(
  LOCK_NAME 			VARCHAR(80) 					NOT NULL,
  SERVER_ID 			VARCHAR(10) 					DEFAULT NULL,
  THREAD_TYPE 			VARCHAR(64) 					DEFAULT NULL,
  LOCK_TIME				VARCHAR(12),
  PRIMARY KEY (LOCK_NAME)
);
comment on table GROUP_LOCK is '用于排程同步的表';
comment on COLUMN GROUP_LOCK.LOCK_NAME is '锁名';
comment on COLUMN GROUP_LOCK.SERVER_ID is '获取锁的服务器id';
comment on COLUMN GROUP_LOCK.THREAD_TYPE is '获取锁的线程的简单类名';
comment on COLUMN GROUP_LOCK.LOCK_TIME is '数据库锁超时时间（分钟）'
;

/*==============================================================*/
/* Table: INDEX_INFO                                            */
/*==============================================================*/
create table INDEX_INFO  (
   MODEL_CODE           VARCHAR2(20),
   ATTRIBUTE_CODE       VARCHAR2(120),
   INDEX_NAME           VARCHAR2(18),
   VERSION              INTEGER,
   HAS_DELETE           INTEGER,
   INDEX_STATE          INTEGER,
   INDEX_TYPE           INTEGER
);

comment on column INDEX_INFO.HAS_DELETE is
'1:已删除
2:未删除';

comment on column INDEX_INFO.INDEX_STATE is
'1:正在创建
2:正在删除
3:创建完成';

comment on column INDEX_INFO.INDEX_TYPE is
'1:普通索引
2:唯一索引';

/*==============================================================*/
/* Table: LOG_RULE                                              */
/*==============================================================*/
create table LOG_RULE  (
   ID                   INTEGER                         NOT NULL,
   CLASS_PATH           VARCHAR2(255),
   "LEVEL"              INTEGER,
   SAVE_PATH            VARCHAR2(255),
   LOG_SIZE             INTEGER,
   STATUS               INTEGER,
   CONSTRAINT PK_LOG_RULE PRIMARY KEY (ID)
);

comment on column LOG_RULE."LEVEL" is
'1:INFO
2:DEBUG
3:WARN
4:ERROR
5:OFF';

comment on column LOG_RULE.STATUS is
'0:禁用
1:启用';

/*==============================================================*/
/* Table: MIGRATE_ERROR                                         */
/*==============================================================*/
create table MIGRATE_ERROR(
	CONTENT_ID 			VARCHAR2(64) 					NOT NULL,
	TABLE_NAME	 		VARCHAR2(32),
	GROUP_ID 			VARCHAR2(32) 					NOT NULL,
	MODEL_CODE 			VARCHAR2(32),
	ERROR_CASE 			VARCHAR2(1024),
	VERSION 			VARCHAR2(32) 					NOT NULL,
	FAIL_TIME 			VARCHAR2(32),
	CONSTRAINT PK_1 PRIMARY KEY (CONTENT_ID, VERSION, GROUP_ID)
);

/*==============================================================*/
/* Table: MIGRATE_ERROR_DETAIL                                  */
/*==============================================================*/
create table MIGRATE_ERROR_DETAIL(
	CONTENT_ID 			VARCHAR2(64) 					NOT NULL,
	INDEX_TABLE_NAME 	VARCHAR2(32),
	FILE_TABLE_NAME 	VARCHAR2(32),
	BUSINESS_START_DATE VARCHAR2(32),
	GROUP_ID 			VARCHAR2(32) 					NOT NULL,
	INDEX_MODEL_CODE 	VARCHAR2(32),
	FILE_MODEL_CODE 	VARCHAR2(32),
	VERSION 			VARCHAR2(32) 					NOT NULL,
	FAIL_TIME 			VARCHAR2(32),
	CONSTRAINT PK_MIGRATE_ERROR_DETAIL PRIMARY KEY (CONTENT_ID, VERSION, GROUP_ID,FILE_TABLE_NAME)
);

/*==============================================================*/
/* Table: MIGRATE_TASK                                          */
/*==============================================================*/
create table MIGRATE_TASK (
	CONTENT_ID 			VARCHAR2(64) 					NOT NULL,
	MODEL_NAME 			VARCHAR2(20) 					NOT NULL,
	SERVERID 			VARCHAR2(20) 					NOT NULL,
	BUSI_SERIAL_NO 		VARCHAR2(48),
	START_DATE 			VARCHAR2(8),
	TASK_STATUS 		INTEGER 						DEFAULT 1,
	TASK_UPTIME 		VARCHAR2(20),
	MIGRATION_STATUS 	INTEGER 						DEFAULT 1,
	QUERY_CONDITION 	VARCHAR2(20),
	PRIMARY KEY (CONTENT_ID)
);

/*==============================================================*/
/* Table: MODEL_ATTRIBUTE_REL                                   */
/*==============================================================*/
create table MODEL_ATTRIBUTE_REL  (
   MODEL_CODE           VARCHAR2(32)                    NOT NULL,
   ATTRIBUTE_CODE       VARCHAR2(20)                    NOT NULL,
   STATE                INTEGER                         NOT NULL,
   UPDATE_LEVEL         VARCHAR2(2)                     DEFAULT 0
);

comment on table MODEL_ATTRIBUTE_REL is
'对应原先的 ：元数据对象与属性对应关系-OBJECT_ATTRIBUTE_REL

该表只记录属性和内容模型的对应关系 ';

comment on column MODEL_ATTRIBUTE_REL.MODEL_CODE is
'对应：OBJECT_ID';

comment on column MODEL_ATTRIBUTE_REL.STATE is
'0:已删除
1:已关联';

/*==============================================================*/
/* Table: MODEL_TABLE_SET                                       */
/*==============================================================*/
create table MODEL_TABLE_SET  (
   MODEL_CODE           VARCHAR2(20),
   TABLE_NAME           VARCHAR2(30)                    NOT NULL,
   BEGIN_TIME           VARCHAR2(14),
   END_TIME             VARCHAR2(14),
   CONSTRAINT PK_MODEL_TABLE_SET PRIMARY KEY (TABLE_NAME)
);

comment on table MODEL_TABLE_SET is
'对应原：元数据批次表集-OBJECT_TABLE_SET';

comment on column MODEL_TABLE_SET.MODEL_CODE is
'对应原：OBJECT_ID';

comment on column MODEL_TABLE_SET.TABLE_NAME is
'对应原：OBJECT_NAME_EN';

comment on column MODEL_TABLE_SET.BEGIN_TIME is
'对应原：TABLE_BEGIN_TIME';

comment on column MODEL_TABLE_SET.END_TIME is
'对应原：TABLE_END_TIME';

/*==============================================================*/
/* Table: NEARLINE_ERROR                                        */
/*==============================================================*/
create table NEARLINE_ERROR(
	CONTENT_ID 			VARCHAR2(64) 					NOT NULL,
	TABLE_NAME 			VARCHAR2(32),
	GROUP_ID 			VARCHAR2(32) 					NOT NULL,
	MODEL_CODE 			VARCHAR2(32),
	ERROR_CASE 			VARCHAR2(1024),
	VERSION 			VARCHAR2(32) 					NOT NULL,
	FAIL_TIME 			VARCHAR2(32),
	CONSTRAINT NEARERROR_PK_1 PRIMARY KEY (CONTENT_ID, VERSION, GROUP_ID)
);

/*==============================================================*/
/* Table: OFFLINE_TASK                                          */
/*==============================================================*/
create table OFFLINE_TASK (
	CONTENT_ID 			VARCHAR2(64) 					NOT NULL,
	MODEL_NAME 			VARCHAR2(20) 					NOT NULL,
	BUSI_SERIAL_NO 		VARCHAR2(48),
	START_DATE 			VARCHAR2(8),
	MIGRATION_STATUS 	INTEGER 						DEFAULT 1,
	DATA_RULE 			INTEGER 						DEFAULT 1 NOT NULL,
	FILEPATH 			VARCHAR2(50),
	TASK_STATUS 		VARCHAR2(25),
	QUERY_CONDITION		VARCHAR2(20),
	PRIMARY KEY (CONTENT_ID)
);

comment on column OFFLINE_TASK.DATA_RULE is
'数据规则 （1-业务规则 2-管理规则）';

/*==============================================================*/
/* Table: PARAM_CONFIG_INFO                                     */
/*==============================================================*/
create table PARAM_CONFIG_INFO  (
   PAR_ID				INTEGER						NOT NULL,
   PAR_KEY				VARCHAR2(256)				NOT NULL,
   PAR_VAL				VARCHAR2(256),
   PAR_ALL				INTEGER,
   PAR_GROUP			INTEGER,
   PAR_SERVER			INTEGER,
   PAR_MODEL			VARCHAR2(32),
   PAR_STATE			INTEGER,
   CONSTRAINT PK_PARAM_CONFIG_INFO PRIMARY KEY (PAR_ID)
);

/*==============================================================*/
/* Table: PARAM_SHOW_INFO                                     */
/*==============================================================*/
create table PARAM_SHOW_INFO(
	PAR_SHOW_ID 		INTEGER						NOT NULL,
	PAR_KEY				VARCHAR2(256)				NOT NULL,
	PAR_SHOW_NAME		VARCHAR2(256)				NOT NULL,
	PAR_REMARK			VARCHAR2(256),
	CONSTRAINT PK_PARAM_SHOW_INFO PRIMARY KEY (PAR_SHOW_ID)
);

/*==============================================================*/
/* Table: PARAM_TABLE                                           */
/*==============================================================*/
create table PARAM_TABLE (
	PARAM_NAME 			VARCHAR(32) 					NOT NULL,
	PARAM_VALUE 		VARCHAR(60) 					DEFAULT NULL,
	DESCRIBE_INFO 		VARCHAR(128) 					DEFAULT NULL,
	PRIMARY KEY (PARAM_NAME)
);

comment on table PARAM_TABLE is
'参数表';
comment on column param_table.PARAM_NAME is
'参数名';
comment on column param_table.PARAM_VALUE is
'参数值';
comment on column param_table.describe_info is
'参数描述';

/*==============================================================*/
/* Table: ROLE_INFO                                             */
/*==============================================================*/
create table ROLE_INFO  (
   ROLE_ID              INTEGER                         NOT NULL,
   ROLE_NAME            VARCHAR2(32),
   ROLE_STATE           INTEGER                        DEFAULT 1,
   ROLE_DES             VARCHAR2(64),
   CONSTRAINT PK_ROLE_INFO PRIMARY KEY (ROLE_ID)
);

comment on table ROLE_INFO is
'0 可用
1 不可用';

comment on column ROLE_INFO.ROLE_STATE is
'1可用
0停用';

/*==============================================================*/
/* Table: ROLE_MODEL_REL                                        */
/*==============================================================*/
create table ROLE_MODEL_REL  (
   ROLE_ID              INTEGER                         NOT NULL,
   MODEL_CODE           VARCHAR2(40)                    NOT NULL,
   PERMISSION_CODE      VARCHAR2(6)
);

/*==============================================================*/
/* Table: ROLE_PERMISSION_MAP                                   */
/*==============================================================*/
create table ROLE_PERMISSION_MAP  (
   ROLE_ID              INTEGER                         NOT NULL,
   PERMISSION_CODE      VARCHAR2(16)                    NOT NULL,
   CONSTRAINT PK_ROLE_PERMISSION_MAP PRIMARY KEY (ROLE_ID, PERMISSION_CODE)
);

/*==============================================================*/
/* Table: SCHEDULER                                             */
/*==============================================================*/
create table SCHEDULER  (
   TASK_ID              INTEGER                         NOT NULL,
   TASK_NO              INTEGER                         NOT NULL,
   GROUP_ID             INTEGER                         NOT NULL,
   MODEL_CODE           VARCHAR2(20)                    NOT NULL,
   RUN_TYPE             VARCHAR2(10),
   BEGIN_TIME           VARCHAR2(20),
   END_TIME             VARCHAR2(20),
   PARAMETERS           VARCHAR2(255),
   TASK_STATE           INTEGER,
   CONSTRAINT PK_SCHEDULER PRIMARY KEY (TASK_ID),
   CONSTRAINT AK_KEY_3_SCHEDULE unique (TASK_NO, GROUP_ID, MODEL_CODE)
);

comment on column SCHEDULER.RUN_TYPE is
'即时   0
每天   1
每周   2
每月   3
每年   4
';

/*==============================================================*/
/* Table: SCHEDULER_DICTIONARY                                  */
/*==============================================================*/
create table SCHEDULER_DICTIONARY  (
   D_TYPE               INTEGER,
   P_NAME               VARCHAR2(20),
   P_CODE               VARCHAR2(20)                    NOT NULL,
   P_TYPE               INTEGER                         NOT NULL,
   P_PARM               INTEGER,
   CONSTRAINT PK_SCHEDULER_DICTIONARY PRIMARY KEY (P_CODE, P_TYPE)
);

/*==============================================================*/
/* Table: SGROUP_CMODEL_REL                                     */
/*==============================================================*/
create table SGROUP_CMODEL_REL  (
   ID                   INTEGER,
   GROUP_ID             INTEGER                         NOT NULL,
   MODEL_CODE           VARCHAR2(20)                    NOT NULL,
   VOLUME_ID            VARCHAR2(10)                    NOT NULL
);

/*==============================================================*/
/* Table: SYS_PERMISSION                                        */
/*==============================================================*/
create table SYS_PERMISSION  (
   PERMISSION_CODE      VARCHAR2(16)                    NOT NULL,
   PERMISSION_NAME      VARCHAR2(32),
   PERMISSION_TYPE      VARCHAR2(16),
   CONSTRAINT PK_SYS_PERMISSION PRIMARY KEY (PERMISSION_CODE)
);

comment on column SYS_PERMISSION.PERMISSION_TYPE is
'1 菜单级
2 节点级';

/*==============================================================*/
/* Table: TOKEN_TABLE                                           */
/*==============================================================*/
create table TOKEN_TABLE  (
   TOKEN_CODE           VARCHAR2(48),
   CHECK_VALUE          VARCHAR2(64),
   TOKEN_TIME           VARCHAR2(14),
   USER_ID              VARCHAR2(20),
   OPERATION_CODE       VARCHAR2(20),
   BINDING_IP           VARCHAR2(15),
   SERVER_INFO          VARCHAR(100)
);

comment on column TOKEN_TABLE.CHECK_VALUE is
'批次的唯一标识，申请令牌时 由客户端提供';

comment on column TOKEN_TABLE.OPERATION_CODE is
'ADD 上传
DEL 删除
UPD 更新
QUY 查询
MAK 批注';

/*==============================================================*/
/* Table: TOKEN_USER_REL                                        */
/*==============================================================*/
create table TOKEN_USER_REL(
  TOKEN_VALUE 	  		VARCHAR(60) 					NOT NULL,
  USER_INFO 	  		VARCHAR(128) 					NOT NULL ,
  EXPIRED_TIME    		VARCHAR(21) 					NOT NULL ,
  PRIMARY KEY (TOKEN_VALUE)
);
create index TOKEN_USER_REL_ET on TOKEN_USER_REL(EXPIRED_TIME);
comment on table TOKEN_USER_REL is
'用户令牌记录表';

comment on column TOKEN_USER_REL.token_value is
'令牌值';
comment on column TOKEN_USER_REL.user_info is
'用户信息';
comment on column TOKEN_USER_REL.EXPIRED_TIME is
'失效时间点';

/*==============================================================*/
/* Table: UNITY_ACCESS_SERVER                                   */
/*==============================================================*/
create table UNITY_ACCESS_SERVER  (
   SERVER_ID            INTEGER                         NOT NULL,
   SERVER_NAME          VARCHAR2(40),
   SERVER_IP            VARCHAR2(15),
   HTTP_PORT            INTEGER,
   SOCKET_PORT          INTEGER,
   REMARK               VARCHAR2(128),
   GROUP_ID             INTEGER,
   STATE                INTEGER,
   HTTPS_PORT  			INTEGER,
   TRANS_PROTOCOL  		VARCHAR(32),
   CONSTRAINT PK_UNITY_ACCESS_SERVER PRIMARY KEY (SERVER_ID)
);

comment on column UNITY_ACCESS_SERVER.STATE is
'0:禁用
1:启用';

/*==============================================================*/
/* Table: UNITY_ACCESS_SERVER_GROUP                             */
/*==============================================================*/
create table UNITY_ACCESS_SERVER_GROUP  (
   GROUP_ID             INTEGER                         NOT NULL,
   GROUP_NAME           VARCHAR2(20),
   IP                   VARCHAR2(15),
   HTTP_PORT            INTEGER,
   SOCKET_PORT          INTEGER,
   REMARK               VARCHAR2(128),
   CONSTRAINT PK_UNITY_ACCESS_SERVER_GROUP PRIMARY KEY (GROUP_ID)
);

/*==============================================================*/
/* Table: USER_CMODEL_REL                                       */
/*==============================================================*/
create table USER_CMODEL_REL  (
   LOGIN_ID             VARCHAR2(20),
   MODEL_CODE           VARCHAR2(20),
   PERMISSION_CODE      VARCHAR2(6)
);

/*==============================================================*/
/* Table: USER_INFO                                             */
/*==============================================================*/
create table USER_INFO  (
   LOGIN_ID             VARCHAR2(20)                    NOT NULL,
   PASSWORD             VARCHAR2(32),
   USER_NAME            VARCHAR2(32),
   USER_POST            VARCHAR2(64),
   USER_DEPARTMENT      VARCHAR2(64),
   USER_STATE           INTEGER                         DEFAULT 1,
   USER_DOMAIN          VARCHAR2(32),
   LDAP_CODE            VARCHAR2(32),
   USER_CITY            VARCHAR2(32),
   PSW_MDF_DATE 				VARCHAR(12),
   CONSTRAINT PK_USER_INFO PRIMARY KEY (LOGIN_ID)
);

comment on column USER_INFO.USER_STATE is
'0 不可用
1 可用';

/*==============================================================*/
/* Table: USER_PERMISSION_MAP                                   */
/*==============================================================*/
create table USER_PERMISSION_MAP  (
   LOGIN_ID             VARCHAR2(20)                    NOT NULL,
   PERMISSION_CODE      VARCHAR2(16)                    NOT NULL
);

/*==============================================================*/
/* Table: USER_ROLE_MAP                                         */
/*==============================================================*/
create table USER_ROLE_MAP  (
   LOGIN_ID             VARCHAR2(20)                    NOT NULL,
   ROLE_ID              INTEGER                         NOT NULL
);

/*==============================================================*/
/* Table: VOLUME_INFO                                           */
/*==============================================================*/
create table VOLUME_INFO  (
   VOLUME_ID            VARCHAR2(10)                    NOT NULL,
   VOLUME_NAME          VARCHAR2(50),
   SERVER_GROUP_ID      INTEGER,
   ROOT_PATH            VARCHAR2(100),
   VOLUME_REMARK        VARCHAR2(100),
   VOLUME_STATUS        INTEGER,
   SAVE_PATH            VARCHAR2(100),
   PATH_RULE            VARCHAR2(100),
   DIR_NUMBER           VARCHAR2(4),
   PATH_NUMBER          VARCHAR2(2),
   OS                   VARCHAR2(20),
   CONSTRAINT PK_VOLUME_INFO PRIMARY KEY (VOLUME_ID)
);

/*==============================================================*/
/* Table: ITEMTYPE_INSNO_DMSNAME                                */
/*==============================================================*/
create table ITEMTYPE_INSNO_DMSNAME (
	PID 				INTEGER 						 NOT NULL,
	GROUP_ID 			NUMBER,
	MODEL_CODE 			VARCHAR2(32),
	INSNO 				VARCHAR2(100),
	CONSTRAINT PK_PID PRIMARY KEY (PID),
	CONSTRAINT UK_GROUPID_INSNO_MODELCODE UNIQUE (GROUP_ID, INSNO, MODEL_CODE)
);


/*==============================================================*/
/* Table: INDEXCOMPRESS                                         */
/*==============================================================*/
create table INDEXCOMPRESS (
	MODELCODE           VARCHAR(20),
	OCCURDATE           VARCHAR(8),
    BATCHCOUNT          INTEGER,
	LIFECYCLESTATE      VARCHAR(3),
	LASTMODATE          VARCHAR(12),
    CONSTRAINT PK_INDEXCOMPRESS PRIMARY KEY (MODELCODE, OCCURDATE)
);

COMMENT ON TABLE  INDEXCOMPRESS                     IS '在线系统批次压缩表';
COMMENT ON COLUMN INDEXCOMPRESS.MODELCODE           IS '模型名';
COMMENT ON COLUMN INDEXCOMPRESS.OCCURDATE           IS '业务发生日期';
COMMENT ON COLUMN INDEXCOMPRESS.BATCHCOUNT          IS '该日期内的批次数';
COMMENT ON COLUMN INDEXCOMPRESS.LIFECYCLESTATE      IS '批次生命周期状态（在线离线共存/离线）';
COMMENT ON COLUMN INDEXCOMPRESS.LASTMODATE          IS '最后修改日期';


/*==============================================================*/
/* Table: WS_ERROR                                              */
/*==============================================================*/
create table WS_ERROR(
	CONTENT_ID 			VARCHAR(64) 					NOT NULL,
	FILE_NO 			VARCHAR(48) 					NOT NULL,
	VERSION 			INT 							DEFAULT 0 NOT NULL,
	GROUP_ID 			VARCHAR(40)	 					NOT NULL,
	MODEL_CODE 			VARCHAR(100) 					NOT NULL,
	FILE_PART_NAME 		VARCHAR(100) 					NOT NULL,
	PRIMARY KEY (CONTENT_ID, GROUP_ID, FILE_NO, VERSION, FILE_PART_NAME, MODEL_CODE)
);
create table ATTRIBUTE_DESENSITIVE_RULE(
   DESRULE_ID          VARCHAR(4)                  NOT NULL,
   DESRULE_NAME        VARCHAR(128),
   CONSTRAINT PK_ATTRIBUTE_DESENSITIVE_RULE PRIMARY KEY (DESRULE_ID)
);


create table ES_TAGS  (
   TAG_CODE        	VARCHAR2(30)                	NOT NULL,
   TAG_NAME        	VARCHAR2(30)                	NOT NULL,
   TAG_STATE        	VARCHAR2(2)						,
    CONSTRAINT PK_ES_TAGS PRIMARY KEY (TAG_CODE)
);

comment on column ES_TAGS.TAG_STATE is
'1是未同步,2是已同步,3是修改待同步';


create table ES_TAGS_ATTRS  (
   TAG_CODE        	VARCHAR2(30) NOT NULL,
   ATTR_CODE       	VARCHAR2(20),
   ATTR_TYPE       	 	VARCHAR2(20)   DEFAULT 'keyword',
   IS_INDEX    			VARCHAR2(2),
   ATTR_SYS_TYPE     VARCHAR2(2),
   STATE        			VARCHAR2(2),
   IGNORE_ABOVE			INTEGER,
   CONSTRAINT PK_ES_TAGS_ATTRS PRIMARY KEY (TAG_CODE,ATTR_CODE)
);

comment on column ES_TAGS_ATTRS.IS_INDEX is
'0 表示非索引,1表示索引';

comment on column ES_TAGS_ATTRS.ATTR_SYS_TYPE is
'1是索引对象字段,2是文档对象字段,3是索引对象和文档对象字段,4是自定义字段';


create table ES_MODEL_REL  (
   TAG_CODE        	VARCHAR2(30)                	NOT NULL,
   MODEL_CODE        VARCHAR2(30)                	NOT NULL,
   STATE        			VARCHAR2(2),
   CONSTRAINT PK_MODEL_ES_REL PRIMARY KEY (TAG_CODE,MODEL_CODE)
);




create table ES_TAG_ERROR(
	CONTENT_ID 		VARCHAR(100),
    FILE_NO         VARCHAR(100),
	TAG_CODE  		VARCHAR(100),
	TAG_JSON	    CLOB,
	FAIL_TIME 		VARCHAR(100),
	IS_INDEXOBJ     VARCHAR(100),
	ERROR_MESSAGE   CLOB,
	TABLE_NAME      VARCHAR(100),
	GROUP_ID        VARCHAR(100)
);


create table ES_ATTRIBUTE_TYPE_DEFINE (
	TYPE_ID          VARCHAR2(16)                  NOT NULL,
	TYPE_NAME        VARCHAR2(32),
   CONSTRAINT PK_ES_ATTRIBUTE_TYPE_DEFINE PRIMARY KEY (TYPE_ID)
);





