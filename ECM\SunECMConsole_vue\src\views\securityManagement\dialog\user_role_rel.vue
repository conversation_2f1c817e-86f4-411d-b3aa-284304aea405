<template>
  <div>
    <el-dialog v-el-drag-dialog :close-on-click-modal="false"
      :title="userRoleTitle"
      :visible.sync="userRoledialogFormVisible" width="1200px"
    >
    <div class="edit_dev">
      <el-transfer
        style="text-align: left; display: inline-block"
        v-model="choiceDataList"
        filterable
        :left-default-checked="[1]"
        :right-default-checked="[2]"
        :titles="['未分配角色', '已有角色']"
        :button-texts="['删除', '添加']"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}',
        }"
        :data="notChoiceDataList"
      >
        <span slot-scope="{ option }"> {{ option.label }}</span>
      </el-transfer>
    </div>  
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="hide">取消</el-button>
        <el-button size="mini" type="primary" @click="handlePostRole()"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>


<style>
.transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
}
</style>

<script>
import {getExistRolesTreeByUserId,getNotExistRolesTreeByUserId,updateUserRoles} from "@/api/userManage";
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "user-role-rel",
  directives: { elDragDialog },
  props: {
    userId: {
      required: false,
      type: String,
    },
    objMsg: {
      require: true,
      type: Object,
    }
  },
  data: function () {
    return {
      notChoiceDataList: [],
      value: [1],
      choiceDataList: [],
      userRoledialogFormVisible: false,
      userRoleTitle: "绑定角色",
      listLoading: true    
    };
  },
  created() {},
  methods: {
    show() {
      this.userRoledialogFormVisible = true;
    },
    hide() {
      this.userRoledialogFormVisible = false;
    },
    handlePostRole() {
      var choiceKeys = "";
      for (var i = 0; i < this.choiceDataList.length; i++) {
        if (choiceKeys.length > 0) {
          choiceKeys += ",";
        }
        choiceKeys += this.choiceDataList[i];
      }
      this.$message.info("提交中...");
        updateUserRoles({
          role_ids: choiceKeys,
          objMsg: this.objMsg,
        }).then(() => {
          this.hide();
          this.$notify({
            title: "Success",
            message: "Created Successfully",
            type: "success",
            duration: 1000,
          });
        });

    },
    getAllRolesTreeById(userId) {    
      this.notChoiceDataList = [];
      this.choiceDataList = [];
        getExistRolesTreeByUserId(userId).then((response) => {
          let existrolesTree = response.msg;
            for(let item1 of existrolesTree){
            let childrenList = item1.children;
            if(childrenList == null){
                this.notChoiceDataList.push({
                  key: item1.id,
                  label: item1.text
                });
                this.choiceDataList.push(item1.id);
            }else{
              for(let item of childrenList){
                this.notChoiceDataList.push({
                  key: item.id,
                  label: item.text
                });  
                this.choiceDataList.push(item.id);
              }
            }
          }
        });
        getNotExistRolesTreeByUserId(userId).then((response) => {
          let notexistrolesTree = response.msg;
          for(let item1 of notexistrolesTree){
          let childrenList = item1.children;
          if(childrenList == null){
              this.notChoiceDataList.push({
                key: item1.id,
                label: item1.text
              });
          }else{
            for(let item of childrenList){
              this.notChoiceDataList.push({
                key: item.id,
                label: item.text
              });
            }
          }
          }
        });
       this.show();
    },
  }
};
</script>
<style scoped>
.edit_dev >>> .el-transfer-panel {
     width:350px;
   }
</style>