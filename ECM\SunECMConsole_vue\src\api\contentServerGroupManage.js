import request from '@/utils/request'
import EndUrl from './publicUrl.js'
export function getContentServerGroupListAction(data) {
    const newdata={'start': data.start,'limit':data.limit,'group_name': encodeURI(data.group_name),'group_id':data.group_id}
    return request({
        url: '/contentServerManage/getContentServerGroupListAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: newdata }
        //params: {'start': data.start,'limit': data.limit,'group_name': encodeURI(data.group_name),'group_id':data.group_id}
    })    
}
export function activeGroup(data) {
    return request({
        url: '/contentServerManage/startContentServerGroupAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: data }
        //params: {'group_ids': data.group_id}
    })    
}
export function disableGroup(data) {
    return request({
        url: '/contentServerManage/stopContentServerGroupAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: data }
        //params: {'group_ids': data.group_id}
    })    
}
export function getVolumeInfoListAction(data) {
    const newdata={'start': data.start,'limit': data.limit,'server_group_id': data.server_group_id,'volume_name':encodeURI(data.volume_name)}
    return request({
        url: '/contentServerManage/volumeInfoSearchAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: newdata }
        //params: {'start': data.start,'limit': data.limit,'server_group_id': data.server_group_id,'volume_name':data.volume_name}
    })    
}
export function configVolume(data) {
    const newdata={'server_group_id': data.server_group_id,'dir_number': data.dir_number,
             'path_number': data.path_number,'path_rule':data.path_rule,
             'root_path': data.root_path,'save_path':data.save_path,
             'volume_id': data.volume_id,'volume_name':encodeURI(data.volume_name),
             'volume_remark': encodeURI(data.volume_remark),'optionFlag':data.optionFlag,
             'c_volume_id': data.c_volume_id}
    return request({
        url: '/contentServerManage/configVolumeAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: newdata }
    })    
}
export function getRelContentServerTree(data) {
    return request({
        url: '/contentServerManage/getRelContentServerTreeAction'+EndUrl.EndUrl,
        method:'post',
        params:{data:data}
        //params: {'group_id': data.group_id}
    })    
}
export function getUnRelContentServerTree(data) {
    return request({
        url: '/contentServerManage/getUnRelContentServerTreeAction'+EndUrl.EndUrl,
        method:'post',
        params:{data:data}
        //params: {'group_id': data.group_id}
    })    
}
export function getAllContentServerList() {
    return request({
        url: '/contentServerManage/getAllContentServerListAction'+EndUrl.EndUrl,
        method:'post'
    })    
}
export function checkGroupNameAction(data) {
    const newdata={'group_id': data.group_id,'group_name':encodeURI(data.group_name)}
    return request({
        url: '/contentServerManage/checkGroupNameAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: newdata }
    })    
}
export function checkGroupIPandPortAction(data) {
    const newdata={'group_id': data.group_id,'group_ip':data.group_ip,'http_port':data.http_port,'socket_port':data.socket_port}
    return request({
        url: '/contentServerManage/checkGroupIPandPortAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: newdata }
    })    
}
export function addContentServerGroupAction(data) {
    const newdata={'group_id': data.group_id,'group_name':encodeURI(data.group_name),'optionFlag':data.optionFlag,'group_ip':data.group_ip,
    'http_port':data.http_port,'socket_port':data.socket_port,'https_port':data.https_port,
    'state':data.state,'os':data.os,'remark':encodeURI(data.remark),'deploy_mode':data.deploy_mode,
    'server_ids':data.server_ids,'serverId_weight':data.serverId_weight,'is_ecm_db':data.is_ecm_db,
    'trans_protocol':data.trans_protocol
    }
    return request({
        url: '/contentServerManage/addContentServerGroupAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: newdata }
    })    
}
export function getUnRelContentObjectTreeAction(data) {
    return request({
        url: '/contentServerManage/getUnRelContentObjectTreeAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: data }
        //params:{data:data.group_id}
        //params: {'group_id': data.group_id}
    })    
}
export function getRelContentObjectTreeAction(data) {
    return request({
        url: '/contentServerManage/getRelContentObjectTreeAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: data }
        //params: {'group_id': data.group_id}
    })    
}
export function getRelVolumnAction(data) {
    return request({
        url: '/contentServerManage/getRelVolumnAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: data }
        //params: {'group_id': data.group_id}
    })    
}
export function addRelGroupAndContentObjectAction(data) {
    return request({
        url: '/contentServerManage/addRelGroupAndContentObjectAction'+EndUrl.EndUrl,
        method:'post',
        params:{ data: data }
        //params: {'group_id': data.group_id,'modelCode_volumeId':data.modelCode_volumeId,'modelCodes':data.modelCodes}
    })
}

export function getContentServerGroup() {
    return request({
        url: '/contentServerManage/getContentServerGroupVueAction'+EndUrl.EndUrl,
        method:'get'            
    })    
}

export function getRelContentObject(data) {
    return request({
        url: '/contentServerManage/getRelContentObjectVueAction'+EndUrl.EndUrl,
        method:'post',    
//        params:{ data: data }
        params: {'group_id': parseInt(data.group_id)}  
    })    
}

export function getSuperGroup(data) {
    return request({
        url: '/contentServerManage/getSuperContentServerGroupVueAction'+EndUrl.EndUrl,
        method:'post',    
        params: {'group_id': parseInt(data.group_id)}  
    })    
}

export function getNearLine(data) {
    return request({
        url: '/contentServerManage/getNearLineVolumeVueAction'+EndUrl.EndUrl,
        method:'post',    
        params: {
            'group_id': parseInt(data.group_id),
            'modelCodes' : data.model_code
        }  
    })
}

export function getRelContentServer(data) {
    return request({
        url: '/contentServerManage/getRelContentServerAction'+EndUrl.EndUrl,
        method:'post',
        params: {'group_id': parseInt(data.group_id)}  
    })    
}


export function getUnRelOrRelContentObject(data) {
    return request({
        url: '/contentServerManage/getUnRelOrRelContentObjectAction'+EndUrl.EndUrl,
        method:'post',
        params: {'group_id': parseInt(data.group_id)}  
    })    
}
