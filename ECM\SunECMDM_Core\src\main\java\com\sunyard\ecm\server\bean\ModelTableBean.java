package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

import java.util.List;

//内容模型的表信息

/**
 * <p>
 * Title:xstream 内容模型的表信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("ModelTableBean")
public class ModelTableBean {
//	// 是否可以作为唯一标识
//	@XStreamAsAttribute
//	private String objectId;
	//
	@XStreamAsAttribute
	private String MODEL_NAME;// 中文名称MODEL_NAME
	@XStreamAsAttribute
	private String MODEL_CODE;// 英文名称MODEL_CODE
	@XStreamAsAttribute
	private String MODEL_TYPE;// 标识 标记文档对象还是索引对象
	// 分表信息
	@XStreamAsAttribute
	private List<TableBean> table;// 分表列表
	public String getModel_name() {
		return MODEL_NAME;
	}
	public void setModel_name(String modelName) {
		MODEL_NAME = modelName;
	}
	public String getModel_code() {
		return MODEL_CODE;
	}
	public void setModel_code(String modelCode) {
		MODEL_CODE = modelCode;
	}
	public String getModel_type() {
		return MODEL_TYPE;
	}
	public void setModel_type(String modelType) {
		MODEL_TYPE = modelType;
	}
	public List<TableBean> getTable() {
		return table;
	}
	public void setTable(List<TableBean> table) {
		this.table = table;
	}
	@Override
	public String toString() {
		return "ModelTableBean [MODEL_CODE=" + MODEL_CODE + ", MODEL_NAME="
				+ MODEL_NAME + ", MODEL_TYPE=" + MODEL_TYPE + ", table="
				+ table + "]";
	}



	

}