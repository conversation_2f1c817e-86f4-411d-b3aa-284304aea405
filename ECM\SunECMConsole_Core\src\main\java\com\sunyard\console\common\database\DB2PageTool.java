package com.sunyard.console.common.database;

import com.sunyard.console.contentmodelmanage.bean.IndexInfoBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Title: db2数据库查询分页
 * </p>
 * <p>
 * Description: db2数据库查询分页
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ConditionalOnProperty(prefix = "pageTool",name = "Type", havingValue = "db2PageTool")
@Service("pageTool")
public class DB2PageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
		int rownum = start + limit - 1;
		return "select * from (select a.*,rownumber() over() as rn from( " + sql + " ) a ) b where rn between " + start
				+ " and " + rownum;
	}

	public String getTableSpaceName(String sql, String tableSpaceName) {
		return sql + " IN " + tableSpaceName;
	}

	public String getOnlyOneSql(String sql, String tableName) {
		return sql + " FETCH FIRST 1 ROWS ONLY ";
	}

	@Override
	public String delIndexSql(IndexInfoBean bean) {
		return "DROP INDEX " + bean.getTable_name() + "_" + bean.getIndex_name();

	}
}
