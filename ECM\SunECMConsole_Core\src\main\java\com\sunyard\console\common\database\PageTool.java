package com.sunyard.console.common.database;


import com.sunyard.console.contentmodelmanage.bean.IndexInfoBean;

public interface PageTool {
	public String getPageSql(String sql, int start, int limit);

	public String getTableSpaceName(String sql, String tableSpaceName);

	/**
	 * 只取数据库中1条记录
	 * 
	 * @param sql
	 * @return
	 */
	public String getOnlyOneSql(String sql, String tableName);

	public String delIndexSql(IndexInfoBean bean);

}
