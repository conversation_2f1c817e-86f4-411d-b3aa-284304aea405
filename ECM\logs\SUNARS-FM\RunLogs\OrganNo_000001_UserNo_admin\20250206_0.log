2025-02-06 16:35:34.458 [OrganNo_000001_UserNo_admin] [e5bd04b926f24f6b/8f9acf50250956f4] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-06 16:35:34.684 [OrganNo_000001_UserNo_admin] [e5bd04b926f24f6b/8f9acf50250956f4] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-06 16:35:34.750 [OrganNo_000001_UserNo_admin] [96941df993719e49/a27c7468dcb479df] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-02-06 16:35:34.807 [OrganNo_000001_UserNo_admin] [96941df993719e49/a27c7468dcb479df] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-06 16:43:38.958 [OrganNo_000001_UserNo_admin] [d8fc91c0b5285739/57bc8a79d55e1a8e] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-06 16:43:38.976 [OrganNo_000001_UserNo_admin] [d8fc91c0b5285739/57bc8a79d55e1a8e] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-06 16:43:39.020 [OrganNo_000001_UserNo_admin] [f6c7c256a96e751c/116d28b9ef8b6861] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-02-06 16:43:39.047 [OrganNo_000001_UserNo_admin] [f6c7c256a96e751c/116d28b9ef8b6861] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-06 16:43:40.294 [OrganNo_000001_UserNo_admin] [d41569471f111c6a/cb921496d22e0118] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-06 16:43:40.311 [OrganNo_000001_UserNo_admin] [d41569471f111c6a/cb921496d22e0118] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-06 16:43:40.342 [OrganNo_000001_UserNo_admin] [956e98e8b025a9a7/b9949b357d77330a] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-02-06 16:43:40.367 [OrganNo_000001_UserNo_admin] [956e98e8b025a9a7/b9949b357d77330a] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-06 16:43:40.407 [OrganNo_000001_UserNo_admin] [1ad07775ad674565/01a2e395db1e7a70] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-02-06 16:43:40.431 [OrganNo_000001_UserNo_admin] [1ad07775ad674565/01a2e395db1e7a70] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
