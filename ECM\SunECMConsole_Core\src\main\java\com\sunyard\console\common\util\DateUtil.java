package com.sunyard.console.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class DateUtil {
	private final static  Logger log = LoggerFactory.getLogger(DateUtil.class);

	public static int getMDrqzhsti8() {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String s_date = sdf.format(date);
		int i_date = Integer.parseInt(s_date);
		return i_date;
	}

	public static String getMDrqzhsti14() {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String s_date = sdf.format(date);
		long l_date = Long.parseLong(s_date);
		String ddate = String.valueOf(l_date);
		return ddate;
	}
	
	/**
	 * 计算出N天后的日期
	 * @param dateStr		基数日期
	 * @param day			天数
	 * @return	
	 * @throws ParseException 
	 */
	public static String getMDrqzhsti8(String dateStr,int day) throws ParseException {
		Date date = new Date();
		//============修改人Gejiajie========
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		 //=============end=============
		date = sdf.parse(dateStr);
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		
		//计算时间
		cal.add(Calendar.DAY_OF_MONTH, day);
		
		String s_date = sdf.format(cal.getTime());
		long l_date = Long.parseLong(s_date);
		String ddate = String.valueOf(l_date);
		return ddate.substring(0,8);
	}
	/**
	 * 计算出N天后的日期
	 * @param dateStr		基数日期
	 * @param day			天数
	 * @return	
	 * @throws ParseException 
	 */
	public static String getMDrqzhsti12(String dateStr,int day) throws ParseException {
		Date date = new Date();
		//============修改人Gejiajie========
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
		 //=============end=============
		date = sdf.parse(dateStr);
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		
		//计算时间
		cal.add(Calendar.DAY_OF_MONTH, day);
		
		String s_date = sdf.format(cal.getTime());
		long l_date = Long.parseLong(s_date);
		String ddate = String.valueOf(l_date);
		return ddate.substring(0,12);
	}
	
	/**
	 * 计算出N分钟后的时间
	 * @param dateStr
	 * @param mintue
	 * @return
	 * @throws ParseException
	 */
	public static String getMDrqzhsti14(String dateStr,int mintue) throws ParseException {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		date = sdf.parse(dateStr);
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		//追加时间
		cal.add(Calendar.MINUTE, mintue);
		
		String s_date = sdf.format(cal.getTime());
		return s_date;
	}
	
	/**
	 * 得到当前的日期和时间 返回的格式为:年/月/日 小时:分
	 * 
	 * @return
	 */
	public static String getCurrentDateTime() {
		long now = System.currentTimeMillis();
		Date currentTime = new Date(now);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String date = formatter.format(currentTime);
		StringBuffer time = new StringBuffer();
		time.append(date.substring(0, 4));
		time.append("/");
		time.append(date.substring(4, 6));
		time.append("/");
		time.append(date.substring(6, 8));
		time.append(" ");
		time.append(date.substring(8, 10));
		time.append(":");
		time.append(date.substring(10, 12));
		return time.toString();
	}
	public static String getDateFor17(){
		long now = System.currentTimeMillis();
		Date currentTime = new Date(now);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String date = formatter.format(currentTime);
		return date;
	}

	/**
	 * 得到当前的日期 返回的格式为:年/月/日
	 * 
	 * @return
	 */
	public static String getCurrentDate() {
		long now = System.currentTimeMillis();
		Date currentTime = new Date(now);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
		String date = formatter.format(currentTime);
		StringBuffer time = new StringBuffer();
		time.append(date.substring(0, 4));
		time.append("/");
		time.append(date.substring(4, 6));
		time.append("/");
		time.append(date.substring(6, 8));
		return time.toString();
	}

	public static String getDate() {
		long now = System.currentTimeMillis();
		Date currentTime = new Date(now);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
		String date = formatter.format(currentTime);
		StringBuffer time = new StringBuffer();
		time.append(date.substring(0, 4));
		time.append("-");
		time.append(date.substring(4, 6));
		time.append("-");
		time.append(date.substring(6, 8));
		return time.toString();
	}

	/**
	 * 得到当前的日期 返回的格式为: 月/日
	 * 
	 * @return
	 */
	public static String getTodayDate() {
		StringBuffer date = new StringBuffer();
		String now = DateUtil.getCurrentDateTime();
		date.append(now.substring(5, 10));

		return date.toString();
	}

	/**
	 * 得到当前的时间 返回的格式: 小时:分
	 * 
	 * @return
	 */
	public static String getCurrentTime() {
		StringBuffer time = new StringBuffer();
		String now = DateUtil.getCurrentDateTime();
		time.append(now.substring(11, 16));

		return time.toString();
	}
	
	/**
	 * 获取10位日期格式 
	 * @return
	 */
	public static String get10bitDateStr() {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");
		String s_date = sdf.format(date);
		long l_date = Long.parseLong(s_date);
		String ddate = String.valueOf(l_date);
		return ddate;
	}

	/**
	 * 比较两个时间 两个要比较的时间的格式为:年/月/日
	 * 
	 * @param sDate
	 * @param eDate
	 * @return
	 */
	public static int Validate(String sDate, String eDate) {
		int flag = 0;
		if (sDate.equals(getCurrentDate())) {
			sDate = sDate + " " + getCurrentTime();
		} else {
			sDate = sDate + " 00:00";
		}
		if (eDate.equals(getCurrentDate())) {
			eDate = eDate + " " + getCurrentTime();
		} else {
			eDate = eDate + " 24:00";
		}
		long s = format(sDate);
		long e = format(eDate);
		long c = format(getCurrentDateTime());

		// 开始时间小于当前时间
		if (s < c)
			flag = 1;
		// 开始时间大于结束时间
		else if (s > e)
			flag = 2;
		else if (s == e) {
			flag = 3;
		}
		return flag;
	}

	public static long format(String date) {
		StringBuffer format = new StringBuffer();
		format.append(date.substring(0, 4));
		format.append(date.substring(5, 7));
		format.append(date.substring(8, 10));
		format.append(date.substring(11, 13));
		format.append(date.substring(14, 16));
		return new Long(format.toString()).longValue();
	}

	public static String getFormatDate(String format) {

		StringBuffer date = new StringBuffer();
		date.append("[");
		date.append(format.substring(5, 7).replaceAll("0", ""));
		date.append("-");
		date.append(format.substring(8, 10).replaceAll("0", ""));
		date.append("]");

		return date.toString();
	}

	public static String getWeek() {
		Calendar cal = new GregorianCalendar();
		int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);
		if ((dayOfWeek - 1) == 0) {
			return "星期日";
		} else if ((dayOfWeek - 1) == 1) {
			return "星期一";
		} else if ((dayOfWeek - 1) == 2) {
			return "星期二";
		} else if ((dayOfWeek - 1) == 3) {
			return "星期三";
		} else if ((dayOfWeek - 1) == 4) {
			return "星期四";
		} else if ((dayOfWeek - 1) == 5) {
			return "星期五";
		} else {
			return "星期六";
		}
	}

	/**
	 * 比较两个日期相差多少天，多少小时，多少分钟，多少秒
	 * 
	 * @param startTime
	 *            源日期
	 * @param endTime
	 *            要比较的日期
	 * @param format
	 *            两个日期格式
	 */
	public static Map<String, Long> dateDiff(String startTime, String endTime,
			String format) {
		// 按照传入的格式生成一个simpledateformate对象
		SimpleDateFormat sd = new SimpleDateFormat(format);
		long nd = 1000 * 24 * 60 * 60;// 一天的毫秒数
		long nh = 1000 * 60 * 60;// 一小时的毫秒数
		long nm = 1000 * 60;// 一分钟的毫秒数
		long ns = 1000;// 一秒钟的毫秒数
		long diff;
		Map<String, Long> map = new HashMap<String, Long>();
		try {
			// 获得两个时间的毫秒时间差异
			diff = sd.parse(endTime).getTime() - sd.parse(startTime).getTime();
			long day = diff / nd;// 计算差多少天
			long hour = diff % nd / nh;// 计算差多少小时
			long min = diff % nd % nh / nm;// 计算差多少分钟
			long sec = diff % nd % nh % nm / ns;// 计算差多少秒
			// 输出结果
			map.put("day", day);
			map.put("hour", hour);
			map.put("min", min);
			map.put("sec", sec);
		} catch (ParseException e) {
			log.error("比较日期出错[startTime:" + startTime + ",endTime:" + endTime
					+ ",format:" + format + "]", e);
		}
		return map;
	}
	/**
	 * 比较两个日期，如果last>=now 返回false,last小于now 返回true
	 * @param last
	 * @return 第2个>=第1个参数 返回true ，2<1返回false
	 */
	public static boolean compareTime(String last, String now) {
		boolean flag = false;
		Calendar lastCal = Calendar.getInstance();
		Calendar nowCal = Calendar.getInstance();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		try {
			lastCal.setTime(sdf.parse(last));
			nowCal.setTime(sdf.parse(now));
			flag = nowCal.compareTo(lastCal) >= 0;
		} catch (Exception e) {
			log.error("计算时间出错", e);
			return flag;
		}
		return flag;

	}
	
	/**
	 * 比较两个日期，如果last>=now 返回false,last小于now 返回true
	 * @param last
	 * @return 第2个>=第1个参数 返回true ，2<1返回false
	 */
	public static boolean compareMDrqzhsti8Time(String last, String now) {
		boolean flag = false;
		Calendar lastCal = Calendar.getInstance();
		Calendar nowCal = Calendar.getInstance();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		try {
			lastCal.setTime(sdf.parse(last));
			nowCal.setTime(sdf.parse(now));
			flag = nowCal.compareTo(lastCal) >= 0;
		} catch (Exception e) {
			log.error("计算时间出错", e);
			return flag;
		}
		return flag;

	}

	/**
	 * 时间戳转换成日期格式字符串
	 * @param seconds
	 * @return
	 */
	public static String timeStamp2Date(String seconds, String format) {
		if(seconds == null||seconds.isEmpty()||seconds.equals("null")){
			return "";
		}
		if(format == null||format.isEmpty()){
			format = "yyyy-MM-dd HH:mm:ss";
		}
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		return sdf.format(new Date(Long.valueOf(seconds)));
	}
}
