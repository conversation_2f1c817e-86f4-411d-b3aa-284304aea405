package com.sunyard.console.common.util;

import org.apache.log4j.Logger;

import java.util.*;

/**
 * <p>
 * Title: 日志管理
 * </p>
 * <p>
 * Description: 打印调试信息、错误信息等日志
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class Log4JUtil {

	public static void logExceptionInErrorLevel(Logger log, Exception e,
			String reason) {
		log.error("----->"+reason);
		logThrowable(log, e);
	}
	/**
	 * 打印错误堆栈
	 * @param log
	 * @param e
	 */
	public static void logThrowable(Logger log, Exception e){
		log.error("ERROR STACK", e);
	}

	/**
	 * 打印调试信息
	 * 
	 * @param log
	 * @param debugMsg
	 *            调试信息
	 */
	public static void logDebug(Logger log, String debugMsg) {
		log.debug(debugMsg);
	}

	/**
	 * 打印错误信息
	 * 
	 * @param log
	 * @param errorMsg
	 *            错误信息
	 */
	public static void logError(Logger log, String errorMsg) {
		log.error(errorMsg);
	}
	
	/**
	 * 打印错误信息，并打印错误堆栈
	 * @param log
	 * @param errorMsg
	 * @param e
	 */
	public static void logError(Logger log, String errorMsg, Exception e){
		log.error(errorMsg);
		logThrowable(log, e);
	}

	public static void logException(Logger log, Map map, Exception e,
			String reason) {
		logExceptionInErrorLevel(log, e, reason);
		log.error("参数: " + map);
	}

//	public static void logException(Logger log, HttpServletRequest request,
//			Exception e, String reason) {
//		logExceptionInErrorLevel(log, e, reason);
//		log.error("requestUrl: " + request.getRequestURI() + " 参数: "
//				+ changeMap(request.getParameterMap()));
//	}

	public static void logParameterInDebug(Logger log, Map map) {
		if (log.isDebugEnabled()) {
			log.debug(map);
		}
	}

	// 取当前时间并转换格式
	public static String getDateStr() {
		String CREAT_DATE;
		String y, m, d, h, f, s;
		Calendar cal = Calendar.getInstance();
		y = cal.get(Calendar.YEAR) + "";
		m = cal.get(Calendar.MONTH) + 1 + "";
		if (m.length() == 1)
			m = "0" + m;
		d = cal.get(Calendar.DATE) + "";
		if (d.length() == 1)
			d = "0" + d;
		// h=cal.get(Calendar.HOUR_OF_DAY)+"";
		// if(h.length()==1) h="0"+h;
		// f=cal.get(Calendar.MINUTE)+"";
		// if(f.length()==1) f="0"+f;
		// s=cal.get(Calendar.SECOND)+"";
		// if(s.length()==1) s="0"+s;
		CREAT_DATE = y + m + d;
		return CREAT_DATE;
	}

	// 取当前时间并转换格式
	public static String getLendDateStr() {
		String CREAT_DATE;
		String y, m, d, h, f, s;
		Calendar cal = Calendar.getInstance();
		y = cal.get(Calendar.YEAR) + "";
		m = cal.get(Calendar.MONTH) + 1 + "";
		if (m.length() == 1)
			m = "0" + m;
		d = cal.get(Calendar.DATE) + "";
		if (d.length() == 1)
			d = "0" + d;
		h = cal.get(Calendar.HOUR_OF_DAY) + "";
		if (h.length() == 1)
			h = "0" + h;
		f = cal.get(Calendar.MINUTE) + "";
		if (f.length() == 1)
			f = "0" + f;
		s = cal.get(Calendar.SECOND) + "";
		if (s.length() == 1)
			s = "0" + s;
		CREAT_DATE = y + m + d + h + f + s;
		return CREAT_DATE;
	}

	// 取当前时间并转换格式
	public static String getDateStrShort() {
		String CREAT_DATE;
		String y, m, d, h, f, s;
		Calendar cal = Calendar.getInstance();
		y = cal.get(Calendar.YEAR) + "";
		m = cal.get(Calendar.MONTH) + 1 + "";
		if (m.length() == 1)
			m = "0" + m;
		d = cal.get(Calendar.DATE) + "";
		if (d.length() == 1)
			d = "0" + d;
		// h=cal.get(Calendar.HOUR_OF_DAY)+"";
		// if(h.length()==1) h="0"+h;
		// f=cal.get(Calendar.MINUTE)+"";
		// if(f.length()==1) f="0"+f;
		// s=cal.get(Calendar.SECOND)+"";
		// if(s.length()==1) s="0"+s;
		CREAT_DATE = y + m + d;
		return CREAT_DATE;
	}

	public static Map changeMap(Map map) {
		Map parameters = new HashMap();

		Set keys = map.keySet();
		for (Iterator iterator = keys.iterator(); iterator.hasNext();) {
			String key = (String) iterator.next();
			String type = map.get(key).toString();
			// 参数是数组
			if (type.startsWith("[Ljava.lang.String")) {
				int len = ((String[]) map.get(key)).length;
				String s2[] = new String[len];
				StringBuffer sBuffer = new StringBuffer();
				sBuffer.append("[");
				for (int i = 0; i < len; i++) {
					s2[i] = ((String[]) map.get(key))[i];
					sBuffer.append(s2[i]);
					if (i < len - 1) {
						sBuffer.append(" ,");
					}
				}
				sBuffer.append("]");
				parameters.put(key, sBuffer.toString());
			} else {
				parameters.put(key, map.get(key));
			}
		}
		return parameters;
	}
	/**
	 * 打印info级别的日志
	 * @param log
	 * @param infoMsg
	 */
	public static  void logInfo(Logger log, String infoMsg) {
		log.info(infoMsg);
	}
	/**
	 * 打印警告信息
	 * @param log
	 * @param warnMsg
	 */
	public static void logWarning(Logger log , String warnMsg){
		log.warn(warnMsg);
	}
}
