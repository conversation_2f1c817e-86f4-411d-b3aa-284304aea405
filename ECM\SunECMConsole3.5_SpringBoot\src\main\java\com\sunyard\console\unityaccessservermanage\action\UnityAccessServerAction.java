package com.sunyard.console.unityaccessservermanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONObject;

import com.sunyard.console.monitor.monitorCenter.singleton.LazySingleton;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.threadpoool.IssueUtils;
import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerInfoBean;
import com.sunyard.console.unityaccessservermanage.dao.UnityAccessServerManageDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 统一接入服务器管理action
 * 
 * <AUTHOR>
 * 
 */
@Controller
public class UnityAccessServerAction extends BaseAction {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	@Autowired
	UnityAccessServerManageDAO unityAccessServerManageDao;
	private int server_id;// 服务器id
	private String server_name;// 服务器名称
	private String server_ip;// 服务器ip
	private int http_port;// http端口
	private int socket_port;// socket端口
	private String remark;// 备注
	private int group_id;// 服务器组id
	private int state;// 状态
	private int start;
	private int limit;
	private String group_name;// 服务器组名称
	private String group_ip;// 服务器组ip
	private String optionFlag;// 新增或修改的标识符
	private String server_ids;// 多个服务器id组成的字符串
	private int https_port;// https端口
	private String trans_protocol;
	
	public String getTrans_protocol() {
		return trans_protocol;
	}

	public void setTrans_protocol(String trans_protocol) {
		this.trans_protocol = trans_protocol;
	}
	/**
	 * 日志对象
	 */
	private  final static Logger log = LoggerFactory.getLogger(UnityAccessServerAction.class);
	public UnityAccessServerManageDAO getUnityAccessServerManageDao() {
		return unityAccessServerManageDao;
	}

	public void setUnityAccessServerManageDao(
			UnityAccessServerManageDAO unityAccessServerManageDao) {
		this.unityAccessServerManageDao = unityAccessServerManageDao;
	}

	public int getHttps_port() {
		return https_port;
	}

	public void setHttps_port(int https_port) {
		this.https_port = https_port;
	}

	public int getServer_id() {
		return server_id;
	}

	public void setServer_id(int serverId) {
		server_id = serverId;
	}

	public String getServer_name() {
		return server_name;
	}

	public void setServer_name(String serverName) {
		server_name = serverName;
	}

	public String getServer_ip() {
		return server_ip;
	}

	public void setServer_ip(String serverIp) {
		server_ip = serverIp;
	}

	public int getHttp_port() {
		return http_port;
	}

	public void setHttp_port(int httpPort) {
		http_port = httpPort;
	}

	public int getSocket_port() {
		return socket_port;
	}

	public void setSocket_port(int socketPort) {
		socket_port = socketPort;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int groupId) {
		group_id = groupId;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String groupName) {
		group_name = groupName;
	}

	public String getGroup_ip() {
		return group_ip;
	}

	public void setGroup_ip(String groupIp) {
		group_ip = groupIp;
	}

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	public String getServer_ids() {
		return server_ids;
	}

	public void setServer_ids(String serverIds) {
		server_ids = serverIds;
	}

	/**
	 * 分页获取统一接入服务器列表
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/getUnityAccessServerListAction.action", method = RequestMethod.POST)
	public String getUnityAccessServerList(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int start = modelJson.getInt("start");
		int limit = modelJson.getInt("limit");
		String server_name = (String) modelJson.getOrDefault("server_name", "");
		String server_id = (String) modelJson.getOrDefault("server_id", "0");
		try {
			server_name = URLDecoder.decode(server_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode UnityAccessServer fields error, server_name=" + server_name, e1);
		}
		log.info("--getUnityAccessServerList(start)-->server_name：" + server_name);
		String jsonStr = null;
		server_id = "".equals(server_id) || server_id == null ? "0" : server_id;
		start = (start-1) * limit;
		try {
			List<UnityAccessServerInfoBean> ServerInfoList = unityAccessServerManageDao
					.getUnityAccessServerList(Integer.valueOf(server_id), server_name,
							start + 1, limit);
			log.debug("--getUnityAccessServerList-->ServerInfoList:" + ServerInfoList);
			List<UnityAccessServerInfoBean> AllInfoList = unityAccessServerManageDao
					.getUnityAccessServerList(Integer.valueOf(server_id), server_name);
			log.debug("--getUnityAccessServerList-->AllInfoList:" + AllInfoList);
			jsonStr = new JSONUtil().createJsonDataByColl(ServerInfoList,
					AllInfoList.size(), new UnityAccessServerInfoBean());
			log.debug( "--getUnityAccessServerList-->获取统一接入服务器成功!;server_name："+server_name);
		} catch (DBRuntimeException e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取统一接入服务器信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "统一接入服务器管理->获取服务器失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getUnityAccessServerList(over)-->server_name："+server_name);
		return null;
	}

	/**
	 *校验服务器IP地址和端口唯一性
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/checkServerIPandPortAction.action", method = RequestMethod.POST)
	public String checkServerIPandPort(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = (String) modelJson.getOrDefault("server_id", "");
		String server_ip = (String) modelJson.getOrDefault("server_ip", "");
		int http_port = modelJson.getInt("http_port");
		int socket_port = modelJson.getInt("socket_port");
		log.info("--checkServerIPandPort(start)-->server_id:" + server_id + ",server_ip:" + server_ip + ",http_port:" + http_port + ",socket_port:" + socket_port);
		String jsonStr = null;
		server_id = "".equals(server_id) || server_id == null ? "0" : server_id;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			count = unityAccessServerManageDao.checkServerIPandPort(Integer.valueOf(server_id),
					server_ip, http_port, socket_port);
			log.debug( "--checkServerIPandPort-->校验服务器IP地址和端口唯一性成功!");
		} catch (Exception e) {
			count = -1;
			log.error( "统一接入服务器管理->检验服务器IP地址和端口唯一性失败:" + e.toString(), e);
		}
		if (count == 0) {
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();
		} else if (count > 0) {
			jsonResp.put("success", false);
			jsonResp.put("message","IP和端口已经存在!!");
			jsonResp.put("reason", "IP和端口已经存在!!");
			jsonStr = jsonResp.toString();
		} else {
			jsonResp.put("success", true);
			jsonResp.put("message","IP和端口检验失败!!");
			jsonResp.put("reason", "检验失败!!");
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--checkServerIPandPort(over)-->server_id:"+server_id);
		return null;
	}

	/**
	 * 新增或修改统一接入服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/addUnityAccessServerAction.action", method = RequestMethod.POST)
	public String addUnityAccessServer(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = (String) modelJson.getOrDefault("server_id", "");
		String group_id = (String) modelJson.getOrDefault("group_id", "");
		int http_port = modelJson.getInt("http_port");
		String remark = (String) modelJson.getOrDefault("remark", "");
		String server_name = (String) modelJson.getOrDefault("server_name", "");
		int socket_port = modelJson.getInt("socket_port");
		int state = modelJson.getInt("state");
		String server_ip = (String) modelJson.getOrDefault("server_ip", "");
		int https_port = modelJson.getInt("https_port");
		String trans_protocol = (String) modelJson.getOrDefault("trans_protocol", "");
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");
		
		log.info("--addUnityAccessServer(start)-->server_id:" + server_id + ";group_id:" + group_id);
		try {
			server_name = URLDecoder.decode(server_name, "utf-8");
			remark = URLDecoder.decode(remark, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode UnityAccessServer fields error, server_name=" + server_name
					+ ", remark=" + remark, e1);
		}
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		server_id = "".equals(server_id) || server_id == null ? "0" : server_id;
		group_id = "".equals(group_id) || group_id == null ? "0" : group_id;
		UnityAccessServerInfoBean bean = new UnityAccessServerInfoBean();
		bean.setServer_id(Integer.valueOf(server_id));
		bean.setGroup_id(Integer.valueOf(group_id));
		bean.setHttp_port(http_port);
		bean.setRemark(remark);
		bean.setServer_name(server_name);
		bean.setSocket_port(socket_port);
		bean.setState(state);
		bean.setServer_ip(server_ip);
		bean.setHttps_port(https_port);
		bean.setTrans_protocol(trans_protocol);
		log.debug( "--addUnityAccessServer-->bean:"+bean);
		try {
			Set<Integer> set=new HashSet<Integer>();
			int result = 0;
			if (optionFlag != null && optionFlag.equals("create1")) {
				result = unityAccessServerManageDao.addUnityAccessServer(bean);
				if (state == 1 && result != 0) {
					// 新增启用的server才下发，给所有启用的统一接入服务器
				//	ConsoleThreadPool.getThreadPool().submit(new Thread(new SendUnityAccessServerThread(result, 1)));
					set.add(result);

				}
			} else if (optionFlag != null && optionFlag.equals("update1")) {
				result = unityAccessServerManageDao
						.updateUnityAccessServer(bean);
				if (result != 0) {
						//ConsoleThreadPool.getThreadPool().submit(new Thread(new SendUnityAccessServerThread(result, 3)));
						set.add(result);
			
				}
			}
			if (result != 0) {
				IssueUtils.IssueInfoToUA(IssueUtils.getUaServerInfoByUaServerIds(set));
				// 重新加载服务器列表信息
				LazySingleton.getInstance().restUntiyAccessServerList();
				jsonResp.put("success", true);
				jsonResp.put("message", "配置统一接入服务器成功!!");
				jsonResp.put("code", 20000);//TODO mock

			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "配置统一接入服务器失败!!");
			}
			jsonStr = jsonResp.toString();
			log.debug( "--addUnityAccessServer-->result:"+result);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置统一接入服务器失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志S
			log.error( "统一接入服务器管理->配置服务器失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--addUnityAccessServer(over)-->server_id:"+server_id);
		return null;
	}

	/**
	 * 启用服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/startUnityAccessServerAction.action", method = RequestMethod.POST)
	public String startUnityAccessServer(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_ids = (String) modelJson.getOrDefault("server_id", "");
		log.info("--startUnityAccessServer(start)-->server_ids:" + server_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try {
			Set<Integer> set=new HashSet<Integer>();
			boolean result = unityAccessServerManageDao
					.startUnityAccessServer(server_ids);
			if (result) {
				String[] str = server_ids.split(",");
				for (int i = 0; i < str.length; i++) {
					/*ConsoleThreadPool.getThreadPool().submit(new Thread(new SendUnityAccessServerThread(Integer
							.parseInt(str[i]), 4)));*/
					set.add(Integer.parseInt(str[i]));
				}
				IssueUtils.IssueInfoToUA(IssueUtils.getUaServerInfoByUaServerIds(set));
				// 重新加载服务器列表信息
				LazySingleton.getInstance().restUntiyAccessServerList();
				jsonResp.put("success", true);
				jsonResp.put("message", "启用服务器成功!!");
				jsonResp.put("code", 20000);//TODO mock
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "启用服务器失败!!");
				jsonStr = jsonResp.toString();
			}
			log.debug( "--startUnityAccessServer-->启用服务器成功！");
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "启用服务器失败!!");
			jsonStr = jsonResp.toString();
			log.error( "统一接入服务器管理->启用服务器失败:" + e.toString(),e);
		}
		this.outJsonString(jsonStr);
		log.info( "--startUnityAccessServer(over)-->server_ids:"+server_ids);
		return null;

	}

	/**
	 * 禁用服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/stopUnityAccessServerAction.action", method = RequestMethod.POST)
	public String stopUnityAccessServer(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_ids = (String) modelJson.getOrDefault("server_id", "");
		log.info( "--stopUnityAccessServer(start)-->server_ids"+server_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try {
			Set<Integer> set=new HashSet<Integer>();
			boolean result = unityAccessServerManageDao
					.stopUnityAccessServer(server_ids);
			if (result) {
				String[] str = server_ids.split(",");
				for (int i = 0; i < str.length; i++) {
				/*	ConsoleThreadPool.getThreadPool().submit(new Thread(new SendUnityAccessServerThread(Integer
							.parseInt(str[i]), 5)));*/
					set.add(Integer.parseInt(str[i]));
				}
				IssueUtils.IssueInfoToUA(IssueUtils.getUaServerInfoByUaServerIds(set));
				// 重新加载服务器列表信息
				LazySingleton.getInstance().restUntiyAccessServerList();
				jsonResp.put("success", true);
				jsonResp.put("message", "禁用服务器成功!!");
				jsonResp.put("code", 20000);//TODO mock
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "禁用服务器失败!!");
				jsonStr = jsonResp.toString();
			}
			log.debug( "--stopUnityAccessServer-->禁用服务器成功！");
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "禁用服务器失败!!");
			jsonStr = jsonResp.toString();
			log.error( "统一接入服务器管理->禁用服务器失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--stopUnityAccessServer(over)-->server_ids"+server_ids);
		return null;
	}
	/**
	 * 检测该服务器名称是否存在
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/checkServerNameAction.action", method = RequestMethod.POST)
	public String checkServerName(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = (String) modelJson.getOrDefault("server_id", "");
		String server_name = (String) modelJson.getOrDefault("server_name", "");
		try {
			server_name = URLDecoder.decode(server_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode UnityAccessServer fields error, server_name=" + server_name, e1);
		}
		log.info("--checkServerName(start)-->server_id:" + server_id + ",server_name" + server_name);
		String jsonStr = null;
		server_id = "".equals(server_id) || server_id == null ? "0" : server_id;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			count = unityAccessServerManageDao.checkServerName(Integer.valueOf(server_id),
					server_name);
			log.debug( "--checkServerName-->count:"+count);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "统一接入服务器管理->校验服务器名称唯一性失败!" + e.toString(), e);
		}
		if (count == 0) {
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();
		} else if (count > 0) {
			jsonResp.put("success", false);
			jsonResp.put("message","服务器名称已经存在!!");
			jsonResp.put("reason", "服务器名称已经存在!!");
			jsonStr = jsonResp.toString();
		} else {
			jsonResp.put("success", true);
			jsonResp.put("message","服务器名称检验失败在!!");
			jsonResp.put("reason", "检验失败!!");
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--checkServerName(over)");
		return null;
	}
}
