package com.sunyard.client;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DownFile {
	private final static  Logger log = LoggerFactory.getLogger(DownFile.class);
//	static String BEGIN_VALUE = "20190918";
//	// 下载文件的路径
//
//	static String DOWN_LOAD_FILE_PATH = "E://image/queryFromOffline/" + BEGIN_VALUE + "/";
//	public static void main(String[] args) {
//		String fileName = "4.jpg";
//		String url = "http://172.1.11.108:8080/SunECMDM/servlet/getFile?s/lV3bcibQhbukTsCsY9YUoaQdmv9CyXENl4gxScQ6wL0Xg6DblBtDccXAemM7KB5qIi5RENBejTOQO9iDHnOdz8OqD9KtggtYVCR/+RWwXXQMECOCzSHZrpm7UXKD85B4hCjmK8T1FNE1GhlZZnvPTy5B7nlU3wKFzuGujqD2NQjfNZG++Kxss4ockjT55eEq4OUU0V/89c7f82RdWL5xc0fshaqJqlhkehdAk7Nc9nmTmzRSnalbz6M9emfrLW8HLfZ/bxvwM=";
//		downLoad(url,DOWN_LOAD_FILE_PATH,fileName);
//	}
	/**
	 * 将文件下载到path路径下
	 * 设置Referer
	 * @param urlStr 传入url
	 * @param path 要下载到的路径
	 * @param fileName 文件名称
	 */
	public void downLoad(String urlStr, String path,String fileName) {
		File file = new File(path+fileName);
		File pareFile = file.getParentFile();
		if (!pareFile.exists()) {
			log.info("no parefile ,begin to create mkdir,path=" + pareFile.getPath());
			pareFile.mkdirs();
		}
		HttpURLConnection url = null;
		InputStream in = null;
		FileOutputStream fos=null;
		try {
			url = (HttpURLConnection)new URL(urlStr).openConnection();
			url.setRequestProperty("Referer", "SunECM");
			in = url.getInputStream();
			fos = new FileOutputStream(file);
			if (in != null) {
				byte[] b = new byte[1024];
				int len = 0;
				while ((len = in.read(b)) != -1) {
					fos.write(b, 0, len);
				}
			}
		} catch (FileNotFoundException e) {
			log.error("unitedaccess http -- GetFileServer: " + e.toString());
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				if (in != null) {
					in.close();
				}
				if (fos != null) {
					fos.close();
				}
			} catch (IOException e) {
				log.error("unitedaccess http -- GetFileServer: " + e.toString());
			}
		}
	}
}
