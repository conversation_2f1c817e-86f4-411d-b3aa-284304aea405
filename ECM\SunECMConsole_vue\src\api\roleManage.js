import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function roleInfoSearch(data) {
  // data.role_name = encodeURI(data.role_name);
  const obj = {
    'page': data.page, 
    'limit': data.limit, 
    'role_name': encodeURI(data.role_name), 
    'role_state': data.role_state
  }
  return request({
    url: '/safeManage/roleInfoVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function addRoleInfo(data) {
  const url = '/safeManage/configRoleVueAction'+EndUrl.EndUrl;
  // data.role_name = encodeURI(data.role_name);
  // data.role_des = encodeURI(data.role_des);
  const obj = {
    'role_name' : encodeURI(data.role_name), 
    'role_des': encodeURI(data.role_des), 
    'role_id': data.role_id
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function updateRoleInfo(data) {
  const url = '/safeManage/configRoleVueAction'+EndUrl.EndUrl;
  // data.role_name = encodeURI(data.role_name);
  // data.role_des = encodeURI(data.role_des);
  const obj = {
    'role_name' : encodeURI(data.role_name), 
    'role_des': encodeURI(data.role_des), 
    'role_id': data.role_id
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function stopRoleState(data) {
  return request({
    url: '/safeManage/modifyRoleStateVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'role_ids': data.role_id,
      'role_state' : "0"
    }
  })
}

export function startRoleState(data) {
  return request({
    url: '/safeManage/modifyRoleStateVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'role_ids': data.role_id,
      'role_state' : "1"
    }
  })
}

export function roleInfoQuery() {
  return request({
    url: '/safeManage/roleQueryAction'+EndUrl.EndUrl,
    method: 'get',
  })
}

export function getRoleCmodel(data) {
  return request({
    url: '/safeManage/getRoleConferVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: data }
  })
}

export function configRoleConfer(data) {
  return request({
    url: '/safeManage/configRoleConferVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'role_id': data.role_id,
      'model_codes': data.model_code, 
      'permission_code': data.permission_code 
    }
  })
}

export function getExistPersTreeByRoleId(data) {
  return request({
    url: '/safeManage/getRoleExistComponentsVue'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'role_ids': data
    }
  })
}

export function getNotExistPersTreeByRoleId(data) {
  return request({
    url: '/safeManage/getRoleNotExistComponentsVue'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'role_ids': data
    }
  })
}

export function updateRoleComponents(data) {
  return request({
    url: '/safeManage/updateRoleComponentsVue'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'role_ids': data.objMsg.role_id,
      'componentIDs': data.componentIDs
    }
  })
}