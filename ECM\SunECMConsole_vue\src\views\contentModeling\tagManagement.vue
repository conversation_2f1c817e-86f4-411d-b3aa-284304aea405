<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.tag_code"
        placeholder="标签代码"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.tag_name"
        placeholder="标签名称"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-if="this.hasPerm('tagSearch')"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain
        round
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain
        round
        @click="handleclear"
      >
        清空
      </el-button>
      <el-button
        v-if="this.hasPerm('addTag')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        添加标签
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      :cell-style="cellStyleTag"
      style="width: 100%"
      @sort-change="sortChange"
    >

      <el-table-column label="标签代码"  align="center" min-width="20%">
        <template slot-scope="{ row }">
          <span>{{ row.tag_code }}</span>
        </template>
      </el-table-column>

      <el-table-column label="标签名称"  align="center" min-width="20%">
        <template slot-scope="{ row }">
          <span>{{ row.tag_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="标签同步状态" align="center" min-width="15%">
        <template slot-scope="{ row }">
          <span v-if="row.tag_state == '1'">未同步</span>
          <span v-if="row.tag_state == '2'">已同步</span>
          <span v-if="row.tag_state == '3'">修改待同步</span>
       </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="300"
        align="left"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row}">
          <el-button
            v-if="hasPerm('modifyTag')"
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            style="margin-bottom: 5px; margin-left: 10px"
          >
            修改标签
          </el-button>

          <el-button
            v-if="hasPerm('deleteTag')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="handleDelete(row)"
            style="margin-bottom: 5px"
          >
            删除标签
          </el-button>

          <el-button
            v-if="hasPerm('relateModel')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="handleRelate(row)"
            style="margin-bottom: 5px"
          >
            关联内容模型
          </el-button>

          <el-button
            v-if="hasPerm('synToES')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="handleSynES(row)"
            style="margin-bottom: 5px"
          >
            同步标签至ES
          </el-button>
          <el-button
            v-if = "row.tag_state==2 ||row.tag_state==3"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="searchEs(row)"
            style="margin-bottom: 5px"
          >
            查看ES信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" style="width:130%">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="95px"
      >
        <el-form-item label="标签代码" prop="tag_code" >
          <el-input v-model="temp.tag_code" 
          :disabled="tag_code_status ? true : false" 
           prop="tag_code" style="width:200px"/>
        </el-form-item>
        <el-form-item label="标签名称" prop="tag_name">
          <el-input v-model="temp.tag_name"  style="width:200px"/>
        </el-form-item>

        <div v-for="(item,i) in attrList" :key="item.name">
          <el-row :gutter="3">
            <el-col :span="6">
              <el-tooltip effect="dark" :content="item.nameContent" placement="bottom">
              <el-form-item label="关联属性:" prop="attName">
                <el-select
                  v-model="attrList[i].attName"
                  placeholder="属性名"
                  :disabled="attrList[i].nameStatus ? true : false"
                >
                  <el-option
                    v-for="attr in notExistAttributeTree"
                    :key="attr.id"
                    :label="attr.text"
                    :value="attr.id"
                  />
                </el-select>
              </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="7">
              <el-form-item label="属性类型:" prop="attType">
                <el-select v-model="attrList[i].attType"                   
                  :disabled="attrList[i].typeStatus ? true : false"
                  clearable placeholder="属性类型"> 
                <el-option
                  v-for="at in ESAttributeTypes"
                  :key="at.type_id"
                  :label="at.type_name"
                  :value="at.type_id"
                />            
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="3">
              <el-tooltip effect="dark" content="不分词字符串长度，不输入则为默认值" placement="bottom">
                <!-- <el-form-item prop="above"> -->
                  <span>
                  <el-input type="number" prop="above" v-model.number="attrList[i].above"
                    v-if="attrList[i].attType == 'keyword'"
                    :disabled="attrList[i].typeStatus ? true : false"
                    placeholder="ignore_above"
                  />
                <!-- </el-form-item> -->
                  </span>
              </el-tooltip>
            </el-col>
            <el-col :span="4">
              <el-form-item label="检索关键字:" prop="attIndex">
                <el-select
                  v-model="attrList[i].attIndex"
                  :disabled="attrList[i].indexStatus ? true : false"
                >
                  <el-option label="否" value="0" />
                  <el-option label="是" value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button circle icon="el-icon-plus" @click="addList(i)" v-if ="addIsShow(i)"></el-button>
              <el-button circle icon="el-icon-minus" @click="subList(i)" v-if ="subIsShow(i)"></el-button>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false">
          取消
        </el-button>
         <el-button size="mini" type="primary" @click="handlePostAttr()"
          >提交</el-button
        >
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="title2" :visible.sync="ModelDialogVisible">
      标签代码：{{modlistQuery.tag_code}},  标签名称：{{modlistQuery.tag_name}}<br/>
      <div class="app-container"> 
        <div class="filter-container">
        <el-button
            v-if="this.hasPerm('relateModel')"
            class="filter-item"
            style="margin-left: 10px"
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="TagModelAdd"
        >
            添加标签与内容模型关联关系
        </el-button>
    </div>

        <el-table
          :key="tableKey2"
          v-loading="modlistLoading"
          :data="modlist"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
            <el-table-column label="模型名" min-width="20%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.model_name }}</span>
            </template>
          </el-table-column>
           <el-table-column label="模型代码" v-if="false">
            <template slot-scope="{ row }">
              <span>{{ row.model_code }}</span>
            </template>
          </el-table-column>
            <el-table-column label="状态" min-width="20%" align="center">
            <template slot-scope="{ row }">
             <span v-if="row.state == '1'">启用</span>
             <span v-if="row.state == '0'">禁用</span>       
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            min-width="30%"
            class-name="small-padding fixed-width"
          >
        <template slot-scope="{ row }">
          <el-button
            v-if="row.state == '0'"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="modelRelStart(row)"
            style="margin-bottom: 5px"
          >
            启用
          </el-button>

          <el-button
            v-if="row.state == '1'"
            size="mini"
            type="danger"
            icon="el-icon-turn-off"
            @click="modelRelStop(row)"
            style="margin-bottom: 5px"
          >
            禁用
          </el-button>
        </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="modtotal > 0"
        :total="modtotal"
        :page.sync="modlistQuery.page"
        :limit.sync="modlistQuery.limit"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="ModelDialogVisible = false">
          取消
        </el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :visible.sync="ESInfodialogVisible" title="ES信息展示页面">
      <el-form :inline="true" title="索引信息">
        <el-form-item
          v-for="item in listData"
          :key="item.name"
          :label="item.title + ':'"
          style="margin-bottom: 0"
        >
          [<span style="color: blue">{{ setting[item.name] }}</span
          >]
        </el-form-item>
      </el-form>
      <el-card class="box-card" style="margin-top: 15px">
        <div slot="header" lass="clearfix">
          <span style="font-weight: bolder">标签关联属性信息</span>
        </div>
        <el-table
          :data="mapping"
          :max-height="300"
          border
          fit
          highlight-current-row
          style="width: 100%"
          :cell-style="cellStyle"
          @sort-change="sortChange"
        >
          <el-table-column label="属性代码" min-width="40%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.AttrCode }}</span>
            </template>
          </el-table-column>

          <el-table-column label="类型" min-width="30%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.AttrType }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否检索关键字" min-width="30%" align="center">
            <template slot-scope="{ row }">
              <span v-if="row.AttrIndex == 0">否</span>
              <span v-if="row.AttrIndex == 1">是</span>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="mini" @click="ESInfodialogVisible = false"
          >关闭</el-button
        >
      </span>
    </el-dialog>

    <tagAttrRel ref="tag_attr_rel"
      v-on:backTag="handleCreate2"
      v-on:refreshTag="getList"
    >
    </tagAttrRel>
    <tagModelRel ref="tag_model_rel" 
      v-on:refreshModel="getModels"
    >
    </tagModelRel>
  </div>
</template>

<script>
import {getTags,delTag,getTagModel,synTagtoES,startModelRelState,stopModelRelState,checkTagName,searchEsFromDM
      ,getExistAttrsTree,getNotExistAttrsTree,updateTagAttrs,getESAttrTypeList} from "@/api/tagManage";
import tagAttrRel from "./dialog/tag_attr_rel.vue";
import tagModelRel from "./dialog/tag_model_rel.vue";

import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";

import {parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import global from "../../store/global.js";

export default {
  name: "ComplexTable",
  components: { Pagination, tagAttrRel, tagModelRel},
  directives: { waves,elDragDialog },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
  },

  data() {
    return {
      attrList:[
        {nameContent:this.nameC,attName:'',attType:'',attIndex:'1',nameStatus:false,typeStatus:false,indexStatus:false}
      ],
      // attName:'',
      // attType:'',
      // attIndex:'',
      tableKey: 0,
      tableKey2: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        tag_code: "",
        tag_name: "",
        title: undefined,
        type: undefined,
        sort: "+tag_code",
      },
      storeObj : [],
      importanceOptions: [1, 2, 3],
      sortOptions: [
        { label: "ID Ascending", key: "+role_name" },
        { label: "ID Descending", key: "-role_name" },
      ],
      temp: {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
      },
      tag: {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
      },
      modlistLoading: true,
      modlist: null,
      modtotal: 0,
      modlistQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined
      },
      dialogFormVisible: false,
      ModelDialogVisible: false,
      ESInfodialogVisible: false,
      dialogStatus: "",
      textMap: {
        update: "修改标签",
        create: "新增标签",
      },   
      title2: "标签与内容模型关联关系",
      nameC:'业务开始时间已默认关联，无需配置',
      rules: {
        tag_code: [
          { required: true,  message: global.regexLowerText, pattern:global.regexLowerCode, trigger: "blur" },
        ],
        tag_name: [
          { required: true,  message: global.regexIndexText, pattern:global.regexIndex, trigger: "blur" },
        ],
        // attName :[{required:false}],
        // attType:[{required:false}],
        // attIndex :[{required:false}],
      },
      downloadLoading: false,
      tag_code_status: false,
      mapping:[],
      setting:[],
      existAttributeTree:[],
      notExistAttributeTree:[],
      allnotExistAttributeTree:[],
      AlreadyChoice: [],
      subShow:[],
      ESAttributeTypes:[],
      listData: [
        { name: "index.creation_date", title: "索引创建时间" },
        { name: "index.number_of_replicas", title: "副本数量" },
        { name: "index.number_of_shards", title: "分片数量" },
        { name: "index.provided_name", title: "索引名" },
        { name: "index.uuid", title: "UUID" },
        { name: "index.version.created", title: "版本号" }]
    };
  },

  created() {
    this.getESAttrType();
    this.getList();
  },

  methods: {
    getESAttrType(){
      getESAttrTypeList().then((response) => {
        this.ESAttributeTypes = response.root;
        for(let item of this.ESAttributeTypes){
            if(item.type_id == 'keyword'){
              item.type_name+='(默认）';
            }
          }
      });
    },

    getList() {
      this.listLoading = true;
      getTags(this.listQuery).then((response) => {
        this.list = response.root;
        this.total = Number(response.totalProperty);
        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },

    handleclear() {
      this.listQuery.tag_code = "";
      this.listQuery.tag_name = "";
    },

    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    sortChange(data) {
      const { prop, order } = data;
      alert(prop);
      if (prop === "tag_code") {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === "ascending") {
        this.listQuery.sort = "+tag_code";
      } else {
        this.listQuery.sort = "-tag_code";
      }
      this.handleFilter();
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
        tag_state:'1'
      };
    },

    addList(index){
      let attN  = this.attrList[index].attName;
      if(attN == undefined || attN == '' || attN == null){
        alert("请选择第一个关联的属性");
        return;
      }
      this.AlreadyChoice = [];
      for (let i = 0; i < this.attrList.length; i++) {
        this.AlreadyChoice.push(this.attrList[i].attName);
      }
      for(let y = 0; y < this.notExistAttributeTree.length; y++) {
        if(this.AlreadyChoice.indexOf(this.notExistAttributeTree[y].id)>=0){
          this.attrList[index].nameContent = this.notExistAttributeTree[y].text;
          this.notExistAttributeTree.splice(y,1);
        }
      }
      this.attrList[index].nameStatus = true;
      this.attrList.push({nameContent:this.nameC,attName:'',attType:'',attIndex:'1',nameStatus:false,typeStatus:false,indexStatus:false});
      // this.rules.attName = [{required:true, message:"属性必选"}];
      // this.rules.attType = [{required:true, message:"类型必填"}];
      // this.rules.attIndex = [{required:true, message:"必选"}];
      if(this.dialogStatus == 'update'){
        this.subShow[index+1] = true;
      }
    },

    subList(index){
      if(index == 0){//清除第一行
        this.attrList[0].attName = "";
        this.attrList[0].attType = "";
        this.attrList[0].attIndex = "";
        this.attrList[0].nameStatus = false;
        // this.rules.attName = [{required:false}];
        // this.rules.attType = [{required:false}];
        // this.rules.attIndex = [{required:false}];
      }else{
        for(let item of this.allnotExistAttributeTree){
          if(item.id == this.attrList[index].attName){
            this.notExistAttributeTree.push(item);
          }
        }
        this.attrList.splice(index,1) 
      }
    },

    addIsShow(index){
      return index+1 == this.attrList.length;
    },
    subIsShow(index){
      if(this.dialogStatus == 'create'){
        return index+1 == this.attrList.length;
      }else if(this.dialogStatus == 'update'){
        return this.subShow[index];
      }
    },

    handleCreate() {
      this.resetTemp();
      this.attrList = [{nameContent:this.nameC,attName:'',attType:'',attIndex:'1'}];
      this.getnotExistAttr('');
      this.dialogStatus = "create";
      this.tag_code_status = false;
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    getnotExistAttr(tagCode){
      getNotExistAttrsTree(tagCode).then((response) => {
          this.notExistAttributeTree = response.msg;
      });
      getNotExistAttrsTree(tagCode).then((response) => {
          this.allnotExistAttributeTree = response.msg;
      });
    },

    getexistAttr(tagCode){
      getExistAttrsTree(tagCode).then((response) => {
        this.existAttributeTree = response.msg;
        this.attrList = [];
        let i = 0;
        if(this.existAttributeTree.length<1){
          this.attrList.push({nameContent:this.nameC,attName:'',attType:'',attIndex:'',nameStatus:false,typeStatus:false,indexStatus:false});
          this.subShow[0] = true;
        }
        for(let item of this.existAttributeTree){
          if(item.type == 'keyword' && item.above != 0){
            this.attrList.push({nameContent:item.text,attName:item.id,attType:item.type,above:item.above,attIndex:item.index,
              nameStatus:true,typeStatus:true,indexStatus:true});
          }else{
            this.attrList.push({nameContent:item.text,attName:item.id,attType:item.type,attIndex:item.index,
              nameStatus:true,typeStatus:true,indexStatus:true});
          }
          this.subShow[i] = false;
          i++;
        }
      });
    },

    handlePostAttr() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          for(let item of this.attrList){
            let attN = item.attName;
            let attT = item.attType;
            let attI = item.attIndex;
            if(attN != undefined && attN != '' && attN != null){
              if(attT == undefined || attT == '' || attT == null){
                alert("请输入属性类型或点击“-”号删除此属性")
                return;
              }
              if(attI == undefined || attI == '' || attI == null){
                alert("请输入是否检索关键字或点击“-”号删除此属性")
                return;
              }
            }
          }
          checkTagName(this.temp,this.dialogStatus).then(() => {
            let attrNames = "";
            let attrTypes = "";
            let attrIndexs = "";
            let attrAboves = "";
            if(this.dialogStatus == 'update'){//修改只提交新增部分
                this.attrList.splice(0,this.existAttributeTree.length)
            }
            for (let i = 0; i < this.attrList.length; i++) {
                let above = this.attrList[i].above;
                if(above == undefined){
                  above = 0;
                }
                if (attrNames.length > 0) {
                  attrNames += ",";
                  attrTypes += ",";
                  attrIndexs += ",";
                  attrAboves += ",";
                }
                attrNames += this.attrList[i].attName;
                attrTypes += this.attrList[i].attType;
                attrIndexs += this.attrList[i].attIndex;
                attrAboves += above;
            }
            this.$message.info("提交中...");
              updateTagAttrs({
                tag_code: this.temp.tag_code,
                tag_name: this.temp.tag_name,
                optionFlag:this.dialogStatus,
                attrNames: attrNames,
                attrTypes: attrTypes,
                attrIndexs: attrIndexs,
                attrAboves:attrAboves
              }).then(() => {
                this.getList();
                this.dialogFormVisible = false;
                this.$notify({
                  title: "Success",
                  message: "Update Successfully",
                  type: "success",
                  duration: 2000,
                });
              });
          })
        }
      })
    },

    handleCreate2() {
      this.$refs.tag_attr_rel.hide();
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },

    // showTag_Attr() {
    //   this.$refs["dataForm"].validate((valid) => {
    //     if (valid) {
    //       checkTagName(this.temp,this.dialogStatus).then(() => {
    //         this.dialogFormVisible = false;
    //         this.$refs.tag_attr_rel.getAllAttrsTree(this.temp.tag_code,this.temp.tag_name,this.temp.tag_state,this.dialogStatus);
    //         this.$refs.tag_attr_rel.show();
    //       })
    //     }
    //   });
    // },

    handleUpdate(row) {
      this.temp = Object.assign({}, row); // copy obj
      this.dialogStatus = "update";
      // if(this.temp.tag_state == 2){//已同步
      //   alert("此标签已同步，不允许修改仅供查看信息");
      //   // return;
      // }
      this.tag_code_status = true;
      this.getnotExistAttr('');
      this.getexistAttr(this.temp.tag_code);
      this.getnotExistAttr(this.temp.tag_code);
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },

    handleDelete(row) {
      this.openDelConfirm(row.tag_state).then(() => {
        delTag(row).then(() => {
          this.getList();
           this.$notify({
              title: "Success",
              message: "Delete Successfully",
              type: "success",
              duration: 2000,
            });
        })
      })
    },
    openDelConfirm(state){
      if(state == 1){//未同步
        return this.$confirm(`是否确定删除此标签？`,'提示',{
          confirmButtonText:'确定',
          cancelButtonText:'取消',
          type:'warning'
        })
      }else if(state == 2 || state == 3){//已同步
          return this.$confirm(`此标签已同步，是否确定删除此标签及相关ES数据？`,'提示',{
          confirmButtonText:'确定',
          cancelButtonText:'取消',
          type:'warning'
        })
      }
    },
    modelRelStart(row){
      row.tag_code = this.modlistQuery.tag_code;
      startModelRelState(row).then(() => {
        this.getModels(this.modlistQuery.tag_code,this.modlistQuery.tag_name);
      });
    },

    modelRelStop(row){
      row.tag_code = this.modlistQuery.tag_code;
      stopModelRelState(row).then(() => {
        this.getModels(this.modlistQuery.tag_code,this.modlistQuery.tag_name);      
      });
    },
    handleRelate(row) {
      this.getModels(row.tag_code,row.tag_name);
      this.ModelDialogVisible = true;

    },
    getModels(tCode,tName) {
      this.modlistLoading = true;
      this.modlistQuery.tag_code = tCode;
      this.modlistQuery.tag_name = tName;
      getTagModel(this.modlistQuery).then((response) => {
        this.modlist = response.root;
        this.modtotal = Number(response.totalProperty);
        setTimeout(() => {
          this.modlistLoading = false;
        }, 1 * 100);
      });
    },

    TagModelAdd() {
      this.$refs.tag_model_rel.getAllModelsTree(this.modlistQuery.tag_code,this.modlistQuery.tag_name);
      this.$refs.tag_model_rel.show();
    },

    handleSynES(row) {
      this.temp = Object.assign({}, row); // copy obj
      if(this.temp.tag_state == '2'){//已同步
          alert("此标签已同步，请勿重复同步");
          return ;
      }
      synTagtoES(this.temp.tag_code).then(() => {
        this.getList();
        this.$notify({
          title: "Success",
          message: "synchronize Successfully",
          type: "success",
          duration: 2000,
        });
      });
    },

    searchEs(row) {
      this.temp = Object.assign({}, row);
      this.mapping = [];
      this.setting = [];
      searchEsFromDM(this.temp.tag_code).then((response) => {
        this.mapping = response.Attr;
        this.setting = response.Setting;
      });
      this.ESInfodialogVisible = true;
    },

    cellStyleTag({row,columnIndex}){
      let cellColor;
      if(columnIndex == 2){//同步状态
        switch(row.tag_state){
          case '1':
            cellColor = 'color:#EAC100';
            break;
          case '2':
            cellColor = 'color:#006030';
            break;
          case '3':
            cellColor = 'color:#FF5809';
            break;
        }
      }
      return cellColor;
    },

    cellStyle({row,columnIndex}){
      //状态栏字体颜色
      let cellColor;
      if(columnIndex == 1){//类型
        switch(row.AttrType){
          case 'date':
            cellColor = 'color:#E800E8'
            break;
          case 'float':
            cellColor = 'color:#004B97'
            break;
          case 'integer':
            cellColor = 'color:#C6A300'
            break;
          case 'keyword':
            cellColor = 'color:#006000'
            break;
          case 'text':
            cellColor = 'color:#AD5A5A'
            break;
        }
      }else if(columnIndex == 2){//是否索引
        switch(row.AttrIndex){
          case '1':
            cellColor = 'color:#006000';
            break;
          case '0':
            cellColor = 'color:#FF2D2D'
            break;
        }
      }
      return cellColor;
    },

    formatJson(filterVal) {
      return this.list.map((v) =>
        filterVal.map((j) => {
          if (j === "timestamp") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort;
      return sort === `+${key}` ? "ascending" : "descending";
    }
  },
};
</script>
<style scoped>
.el-row {
  margin-bottom: 20px;
}
.el-col {
  border-radius:4px;
}
</style>

