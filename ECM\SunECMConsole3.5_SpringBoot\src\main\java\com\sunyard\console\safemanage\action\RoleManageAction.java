package com.sunyard.console.safemanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONObject;

import com.sunyard.console.safemanage.bean.NodeBean;
import com.sunyard.console.safemanage.bean.RoleCmodelRelBean;
import com.sunyard.console.safemanage.bean.RoleInfoBean;
import com.sunyard.console.safemanage.dao.RoleManageDAO;
import com.sunyard.console.threadpoool.IssueUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>Title: 角色管理Action</p>
 * <p>Description: 处理角色信息管理中的跳转</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class RoleManageAction extends BaseAction {
	@Autowired
	RoleManageDAO rmdao ;

	public void setRmdao(RoleManageDAO rmdao) {
		this.rmdao = rmdao;
	}
	
	private String role_id;			//角色ID
	private String role_name;		//角色名称
	private String role_state;		//角色状态
	private String role_des;		//角色备注
	private int start;
	private int limit;
	private String role_ids;		//角色ID集合
	private String componentIDs;	//权限ID集合
	private String model_codes;		//内容模型代码集合
	private String permission_code;	//内容模型操作权限
	private  final static Logger log = LoggerFactory.getLogger(RoleManageAction.class);
	private String value;

	public String getModel_codes() {
		return model_codes;
	}
	public void setModel_codes(String model_codes) {
		this.model_codes = model_codes;
	}
	public String getPermission_code() {
		return permission_code;
	}
	public void setPermission_code(String permission_code) {
		this.permission_code = permission_code;
	}
	public String getComponentIDs() {
		return componentIDs;
	}
	public void setComponentIDs(String componentIDs) {
		this.componentIDs = componentIDs;
	}
	public String getRole_id() {
		return role_id;
	}
	public void setRole_id(String role_id) {
		this.role_id = role_id;
	}
	public String getRole_name() {
		return role_name;
	}
	public void setRole_name(String role_name) {
		this.role_name = role_name;
	}
	public String getRole_state() {
		return role_state;
	}
	public void setRole_state(String role_state) {
		this.role_state = role_state;
	}
	public String getRole_des() {
		return role_des;
	}
	public void setRole_des(String role_des) {
		this.role_des = role_des;
	}
	public int getStart() {
		return start;
	}
	public void setStart(int start) {
		this.start = start;
	}
	public int getLimit() {
		return limit;
	}
	public void setLimit(int limit) {
		this.limit = limit;
	}
	public String getRole_ids() {
		return role_ids;
	}
	public void setRole_ids(String role_ids) {
		this.role_ids = role_ids;
	}
	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	/**
	 * 查询所有角色列表
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/safeManage/roleQueryAction.action")
	public String roleInfoQuery() {
		log.info( "--roleInfoQuery(start)");
		List result = rmdao.getAllRoleList();
		log.debug( "--roleInfoQuery-->result:" + result);
		this.outJsonString(new JSONUtil().createJsonDataByMapList(result, result.size()));
		log.info( "--roleInfoQuery(over)");
		return null;
	}
	
	/**
	 * 角色查询Action
	 * @return
	 */
	@RequestMapping(value = "/safeManage/roleInfoAction.action")
	@ResponseBody
	public String roleInfoSearch(String role_name, String role_state, int start, int limit) {
		log.info("--roleInfoSearch(start)-->role_name:" + role_name + ";role_state:" + role_state);
		String jsonStr = null;
		try{
			List<RoleInfoBean> roleInfoList = rmdao.searchRoleInfoList(role_name, role_state , 
					start+1 , limit);
			List<RoleInfoBean> roleInfoAllList = rmdao.searchRoleInfoAllList(role_name, role_state);
			
			jsonStr = new JSONUtil().createJsonDataByColl(roleInfoList,roleInfoAllList.size(),new RoleInfoBean());
			log.debug( "--roleInfoSearch(over)-->获取角色信息成功！");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取角色信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->角色查询失败->"+e.toString());
			log.error( "Exception:",e);
		}
		
		this.outJsonString(jsonStr);
		log.info( "--roleInfoSearch(over)");
		return null;
	}
	
	/**
	 * 新增、修改角色
	 * @return
	 */
	@RequestMapping(value = "/safeManage/configRoleAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String configRole(String role_name, String role_des, String role_id, String value) {
		log.info("--SconfigRole(start)");
		RoleInfoBean role = new RoleInfoBean();
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		role.setRole_name(role_name);
		role.setRole_state("1");
		role.setRole_des(role_des);
		boolean success=true;;
		String msg="配置角色成功!!";
		try{
			if (role_id != null && !"".equals(role_id)) {
				role.setRole_name(value);
				role.setRole_id(role_id);
				rmdao.modifyRole(role);
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			} else {
				int count = rmdao.checkRoleName(role_name);
				if (count == 0) {
					
					rmdao.addRole(role);
				} else {
					success=false;
					msg="角色名称已存在!!";
				}
			}
			jsonResp.put("success", success);
			jsonResp.put("message", msg);
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "配置角色失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->配置角色信息失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--SconfigRole(over)");
		return null;
	}
	
	/**
	 * 启用、禁用角色
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/modifyRoleStateAction.action", method = RequestMethod.POST)
	public String modifyRoleState(String role_ids, String role_state) {
		log.info("--modifyRoleState(start)-->role_ids:" + role_ids + ";role_state:" + role_state);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try{
			rmdao.modifyRoleState(role_ids, role_state);
			jsonResp.put("success", true);
			if(Integer.valueOf(role_state) == 1)
				jsonResp.put("message", "启用角色成功!!");
			else
				jsonResp.put("message", "禁用角色成功!!");
			log.debug( "--modifyRoleState--启用/禁用角色成功!! role_ids:" + role_ids);
			IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
			IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			if(Integer.valueOf(role_state) == 1){
				jsonResp.put("message", "启用角色失败!!");
				log.error( "角色管理->启用角色失败->"+e.toString());
			}	
			else{
				jsonResp.put("message", "禁用角色失败!!");
				log.error( "角色管理->禁用角色失败->"+e.toString());
			}
			jsonStr = jsonResp.toString();
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--modifyRoleState(over)");
		return null;
	}
	
	/**
	 * 获取角色已有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getRoleExistComponents.action", method = RequestMethod.POST)
	public String getRoleExistComponents(String role_ids) {
		log.info( "--getRoleExistComponent(start)-->role_ids:"+role_ids);
		String jsonStr  = null;
		try{
			List<NodeBean> PermissionInfos = rmdao.getExistsComponents(role_ids);
			jsonStr = NodeBean.getTreeString(PermissionInfos);
			log.debug( "--getRoleExistComponents-->获取角色已有权限信息成功！");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->获取角色已有权限信息失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--getRoleExistComponent(over)");
		return null;
	}
	
	/**
	 * 获取角色未有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getRoleNotExistComponents.action", method = RequestMethod.POST)
	public String getRoleNotExistComponents(String role_ids) {
		log.info( "--getRoleNotExistComponents(start)-->role_ids:"+role_ids);
		String jsonStr  = null;
		try{
			List<NodeBean> PermissionInfos = rmdao.getNotExistsComponents(role_ids);
			jsonStr = NodeBean.getTreeString(PermissionInfos);
			log.debug( "--getRoleNotExistComponents-->获取角色未有权限信息成功！");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->获取角色未有权限信息失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--getRoleNotExistComponents(over)");
		return null;
	}
	
	/**
	 * 修改角色的权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/updateRoleComponents.action", method = RequestMethod.POST)
	public String updateRoleComponents(String role_ids, String componentIDs) {
		log.info("--updateRoleComponents(start)-->role_ids:" + role_ids + "componentIDs:" + componentIDs);
		JSONObject jsonResp = new JSONObject();
		String		 jsonStr	= null;
		try{
			rmdao.updateRoleComponents(role_ids, componentIDs);
			log.debug( "--updateRoleComponents--修改权限成功!! role_ids:" + role_ids);
			jsonResp.put("success", true);
			jsonResp.put("message", "修改权限成功!!");
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "修改权限失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->修改角色权限信息失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--updateRoleComponents(over)");
		return null;
	}
	
	/**
	 * 获取角色内容对象操作权限列表
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getRoleConferAction.action", method = RequestMethod.POST)
	public String getConfer(String role_id, int start, int limit) {
		log.info( "--getConfer(start)-->role_id:"+role_id);
		String		 jsonStr	= null;
		try{
			List<RoleCmodelRelBean> conferList = rmdao.getConferList(role_id, start+1, limit);
			List<RoleCmodelRelBean> conferAllList = rmdao.getConferAllList(role_id);
			jsonStr = new JSONUtil().createJsonDataByColl(conferList,conferAllList.size(),new RoleCmodelRelBean());
			log.debug( "--getConfer-->获取角色内容对象操作权限列表成功!");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取角色内容对象操作权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->获取角色内容对象操作权限列表失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--getConfer(over)-->role_id:"+role_id);
		return null;
	}
	
	/**
	 * 配置角色内容对象操作权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/configRoleConferAction.action", method = RequestMethod.POST)
	public String configConfer(String role_id, String model_codes, String permission_code) {
		log.info("--configConfer(start)-->role_id:" + role_id + ";model_codes:" + model_codes + ";permission_code:" + permission_code);
		JSONObject jsonResp = new JSONObject();
		String		 jsonStr	= null;
		
		try{
			rmdao.configConfer(role_id, model_codes, permission_code);
			log.debug( "--configConfer--授予权限成功!! role_id:" + role_id);
			String[] modelCodes=model_codes.split(",");
			IssueUtils.IssueContentModelsInfo(modelCodes);
			jsonResp.put("success", true);
			jsonResp.put("message", "授予权限成功!!");
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "授予权限失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->配置角色内容对象操作权限失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr); 
		log.info( "--configConfer(over)");
		return null;
		
	}
	
	/**
	 * 角色名唯一校验
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/checkRoleNameAction.action", method = RequestMethod.POST)
	public String checkRoleName(String role_name, String value) {
		log.info( "--checkRoleName(start)-->role_name:"+role_name);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			if(value!=null){
				value=value.toLowerCase();
			}
			count = rmdao.checkRoleName(role_name);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "角色管理->校验该角色是否存在失败!" + e.getMessage());
			log.error( e.toString());
		}
		log.debug( "--checkRoleName-->count:"+count);
		if (count == 0) {
			jsonResp.put("valid", true);
			jsonResp.put("reason", true);
		} else if (count > 0) {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "角色名已经被使用!!");
		} else {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "检验失败!!");
		}
		jsonResp.put("success", true);
		jsonStr = jsonResp.toString();
		this.outJsonString(jsonStr);
		log.info( "--checkRoleName(over)");
		return null;

	}
	
	/**
	 * 角色查询Action
	 * @return
	 */
	@RequestMapping(value = "/safeManage/roleInfoVueAction.action")
	@ResponseBody
	public String roleInfoSearchVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		String role_state = (String) modelJson.getOrDefault("role_state", "");
		String role_name = (String) modelJson.getOrDefault("role_name", "");
		log.info("--roleInfoSearch(start)-->role_name:" + role_name + ";role_state:" + role_state);
		String jsonStr = null;
		start = (page_int-1) * limit_int;
		try {
			role_name = URLDecoder.decode(role_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode roleManage fields error, role_name=" + role_name,e1);
		}
		try{
			List<RoleInfoBean> roleInfoList = rmdao.searchRoleInfoList(role_name, role_state , 
					start+1 , limit_int);
			List<RoleInfoBean> roleInfoAllList = rmdao.searchRoleInfoAllList(role_name, role_state);
			
			jsonStr = new JSONUtil().createJsonDataByColl(roleInfoList,roleInfoAllList.size(),new RoleInfoBean());
			log.debug( "--roleInfoSearch(over)-->获取角色信息成功！");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取角色信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->角色查询失败->"+e.toString());
			log.error( "Exception:",e);
		}
		
		this.outJsonString(jsonStr);
		log.info( "--roleInfoSearch(over)");
		return null;
	}
	
	/**
	 * 新增、修改角色
	 * @return
	 */
	@RequestMapping(value = "/safeManage/configRoleVueAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String configRoleVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);

		String role_id = (String) modelJson.getOrDefault("role_id", "");
		String role_name = (String) modelJson.getOrDefault("role_name", "");
		String role_des = (String) modelJson.getOrDefault("role_des", "");
		role_des = "undefined".equals(role_des) || role_des == null ? "" : role_des;

		log.info("--SconfigRole(start)");
		RoleInfoBean role = new RoleInfoBean();
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try {
			role_name = URLDecoder.decode(role_name, "utf-8");
			role_des = URLDecoder.decode(role_des, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode roleManage fields error, role_name=" + role_name,e1);
		}
		role.setRole_name(role_name);
		role.setRole_state("1");
		role.setRole_des(role_des);
		boolean success=true;;
		String msg="配置角色成功!!";
		jsonResp.put("code", 20000);
		try{
			if (role_id != null && !"".equals(role_id)) {
				role.setRole_name(role_name);
				role.setRole_id(role_id);
				rmdao.modifyRole(role);
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			} else {
				int count = rmdao.checkRoleName(role_name);
				if (count == 0) {
					rmdao.addRole(role);
				} else {
					success=false;
					msg="角色名称已存在!!";
					jsonResp.remove("code");
				}
			}
			jsonResp.put("success", success);
			jsonResp.put("message", msg);
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.remove("code");
			jsonResp.put("message", "配置角色失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->配置角色信息失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--SconfigRole(over)");
		return null;
	}
	
	/**
	 * 启用、禁用角色
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/modifyRoleStateVueAction.action", method = RequestMethod.POST)
	public String modifyRoleStateVue(String role_ids, String role_state) {
		log.info("--modifyRoleState(start)-->role_ids:" + role_ids + ";role_state:" + role_state);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try{
			rmdao.modifyRoleState(role_ids, role_state);
			jsonResp.put("success", true);
			if(Integer.valueOf(role_state) == 1){
				jsonResp.put("message", "启用角色成功!!");
				jsonResp.put("code", 20000);
			}else{
				jsonResp.put("message", "禁用角色成功!!");
				jsonResp.put("code", 20000);
			}
			log.debug( "--modifyRoleState--启用/禁用角色成功!! role_ids:" + role_ids);
			IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
			IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			if(Integer.valueOf(role_state) == 1){
				jsonResp.put("message", "启用角色失败!!");
				log.error( "角色管理->启用角色失败->"+e.toString());
			}	
			else{
				jsonResp.put("message", "禁用角色失败!!");
				log.error( "角色管理->禁用角色失败->"+e.toString());
			}
			jsonStr = jsonResp.toString();
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--modifyRoleState(over)");
		return null;
	}
	
	/**
	 * 获取角色内容对象操作权限列表
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getRoleConferVueAction.action", method = RequestMethod.POST)
	public String getConferVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		String role_id = (String) modelJson.getOrDefault("role_id", "");
		log.info( "--getConfer(start)-->role_id:"+role_id);
		String		 jsonStr	= null;
		start = (page_int-1) * limit_int;
		try{
			List<RoleCmodelRelBean> conferList = rmdao.getConferList(role_id, start+1, limit_int);
			List<RoleCmodelRelBean> conferAllList = rmdao.getConferAllList(role_id);
			jsonStr = new JSONUtil().createJsonDataByColl(conferList,conferAllList.size(),new RoleCmodelRelBean());
			log.debug( "--getConfer-->获取角色内容对象操作权限列表成功!");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取角色内容对象操作权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->获取角色内容对象操作权限列表失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--getConfer(over)-->role_id:"+role_id);
		return null;
	}
	
	/**
	 * 配置角色内容对象操作权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/configRoleConferVueAction.action", method = RequestMethod.POST)
	public String configConferVue(String role_id, String model_codes, String permission_code) {
		log.info("--configConfer(start)-->role_id:" + role_id + ";model_codes:" + model_codes + ";permission_code:" + permission_code);
		JSONObject jsonResp = new JSONObject();
		String		 jsonStr	= null;
		try{
			rmdao.configConfer(role_id, model_codes, permission_code);
			log.debug( "--configConfer--授予权限成功!! role_id:" + role_id);
			String[] modelCodes=model_codes.split(",");
			IssueUtils.IssueContentModelsInfo(modelCodes);
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "授予权限成功!!");
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "授予权限失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->配置角色内容对象操作权限失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr); 
		log.info( "--configConfer(over)");
		return null;	
	}
	
	/**
	 * 获取角色已有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getRoleExistComponentsVue.action", method = RequestMethod.POST)
	public String getRoleExistComponentsVue(String role_ids) {
		log.info( "--getRoleExistComponent(start)-->role_ids:"+role_ids);
		String jsonStr  = null;
		JSONObject jsonResp = new JSONObject();
		try{
			List<NodeBean> PermissionInfos = rmdao.getExistsComponents(role_ids);
			jsonStr = NodeBean.getTreeString(PermissionInfos);
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonStr);
			log.debug( "--getRoleExistComponents-->获取角色已有权限信息成功！");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			log.error( "角色管理->获取角色已有权限信息失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getRoleExistComponent(over)");
		return null;
	}
	
	/**
	 * 获取角色未有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getRoleNotExistComponentsVue.action", method = RequestMethod.POST)
	public String getRoleNotExistComponentsVue(String role_ids) {
		log.info( "--getRoleNotExistComponents(start)-->role_ids:"+role_ids);
		String jsonStr  = null;
		JSONObject jsonResp = new JSONObject();
		try{
			List<NodeBean> PermissionInfos = rmdao.getNotExistsComponents(role_ids);
			jsonStr = NodeBean.getTreeString(PermissionInfos);
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonStr);
			log.debug( "--getRoleNotExistComponents-->获取角色未有权限信息成功！");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			log.error( "角色管理->获取角色未有权限信息失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getRoleNotExistComponents(over)");
		return null;
	}
	
	/**
	 * 修改角色的权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/updateRoleComponentsVue.action", method = RequestMethod.POST)
	public String updateRoleComponentsVue(String role_ids, String componentIDs) {
		log.info("--updateRoleComponents(start)-->role_ids:" + role_ids + "componentIDs:" + componentIDs);
		JSONObject jsonResp = new JSONObject();
		String		 jsonStr	= null;
		try{
			componentIDs = andComponent(componentIDs);	
			rmdao.updateRoleComponents(role_ids, componentIDs);
			log.debug( "--updateRoleComponents--修改权限成功!! role_ids:" + role_ids);
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "修改权限成功!!");
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "修改权限失败!!");
			jsonStr = jsonResp.toString();
			log.error( "角色管理->修改角色权限信息失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--updateRoleComponents(over)");
		return null;
	}
	
	public boolean containRepeatChar(String str,String str1){
		String[] arr1 = str.split(",");	
		String[] arr2 = str1.split(",");
		for (int i=0;i<arr2.length;i++){
			for (int j=0;j<arr1.length;j++){
				if(arr1[j].equals(arr2[i])){
					return true;
				}
			}
		}
		return false;
	}
	
	public String andComponent(String s){
		String a1 = "addAttribute,attributeSearch,delAttribute,modifyAttribute";
		String a10 = "scheduleSearch";
		String a11 = "ableServer,addGroup,addServer,disableServer,editServer,serverSearch";
		String a12 = "addLog,modifyLog";
		String a13 = "logDown,logSearch";
		String a14 = "addBatch,batchSearch,delBatch,modifyBatch";
		String a16 = "queryFailMigrate,reMigrate";
		String a17 = "manuallyDel,manuallySyn";
		String a18 = "addInSno,delInSno,queryInSno,updateInSno";
		String a2 = "addSepTable,hbaseSQLExport,objectCreate,objectDelete,objectExport,objectImport,objectPermission,objectUpdate";
		String a21 = "reNearLine";
		String a23 = "ableConParam,addConParam,addParamShow,conParamSearch,delParamShow,disableConParam,modifyConParam,modifyParamShow,paramShowSearch";
		String a26 = "tagSearch,addTag,modifyTag,deleteTag,relateModel.synToES";
		String a3 = "addHistoryTable,historySearch";
		String a4 = "ableUser,addUser,bindRole,disableUser,modifyUser,resetPwd,userMetadata,userPermission,userSearch";
		String a5 = "ableRole,addRole,disableRole,editRole,roleMetadata,rolePermission,roleSearch";
		String a6 = "addtoken,delToken";
		String a7 = "ableNode,addNode,disableNode,editNode,nodeSearch";
		String a8 = "ableNodeGroup,addNodeGroup,disableNodeGroup,modifyNodeGroup,nodeGroupSearch,nodeMetadata,relateVolume";
		String a9 = "ableStrategy,addStrategy,disableStrategy,strategySearch,updateStrategy";
		if(containRepeatChar(a1,s)){
			s+=",1";
		}if(containRepeatChar(a10,s)){
			s+=",10";
		}if(containRepeatChar(a11,s)){
			s+=",11";
		}if(containRepeatChar(a12,s)){
			s+=",12";
		}if(containRepeatChar(a13,s)){
			s+=",13";
		}if(containRepeatChar(a14,s)){
			s+=",14";
		}if(containRepeatChar(a16,s)){
			s+=",16";
		}if(containRepeatChar(a17,s)){
			s+=",17";
		}if(containRepeatChar(a18,s)){
			s+=",18";
		}if(containRepeatChar(a2,s)){
			s+=",2";
		}if(containRepeatChar(a21,s)){
			s+=",21";
		}if(containRepeatChar(a23,s)){
			s+=",23";
		}if(containRepeatChar(a26,s)){
			s+=",26";
		}if(containRepeatChar(a3,s)){
			s+=",3";
		}if(containRepeatChar(a4,s)){
			s+=",4";
		}if(containRepeatChar(a5,s)){
			s+=",5";
		}if(containRepeatChar(a6,s)){
			s+=",6";
		}if(containRepeatChar(a7,s)){
			s+=",7";
		}if(containRepeatChar(a8,s)){
			s+=",8";
		}if(containRepeatChar(a9,s)){
			s+=",9";
		}
		return s;
	}
}
