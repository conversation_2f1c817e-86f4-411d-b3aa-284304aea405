package com.sunyard.util.ecm3_1;

import java.util.Map;

import com.sunyard.ecm.server.bean.SGroupModleSetBean;
import com.sunyard.ecm.server.bean.StoreObjectBean;
import com.sunyard.ecm.server.cache.LazySingleton;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;

/**
 * 
 * 获取存储对象的工具类
 * 提供console配置的
 * 
 * <AUTHOR>
 *
 */
public class StoreObjectUtil {

	/**
	 * 从console的配置中获取内容模型modecode的文件存放路径
	 * @param modeCode
	 * @return StoreObjectBean 卷信息
	 * @throws SunECMException
	 */
	public static StoreObjectBean getStoreObj(String modeCode) throws SunECMException{
		// 获取内容模型所配置的卷信息
		Map<String, SGroupModleSetBean> groupMap = LazySingleton.getInstance().SGroupModle.getSgroupmodlemap();
		SGroupModleSetBean groupModleSetBean = groupMap.get(modeCode);
		StoreObjectBean storeObjectBean = LazySingleton.getInstance().storeObject.getStoreObject().get(groupModleSetBean.getVolume_id());
		if(storeObjectBean != null) {
			return storeObjectBean;
		} else {
			throw new SunECMException(SunECMExceptionStatus.SERVER_UNSUPPORT_CONTENTMODEL, "本地不支持- "+modeCode+" -元数据对象，请在本地添加该模型的策略");
		}
	}
	
}