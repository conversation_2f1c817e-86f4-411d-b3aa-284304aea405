package com.sunyard.console.singletonManage.dao;

import com.sunyard.console.configmanager.wsserviceutil.DMAdressConstuct;
import com.sunyard.console.singletonManage.bean.LazySingletonBean;
import com.sunyard.exception.SunECMException;
import com.sunyard.ws.client.WSAccessClient;
import com.sunyard.ws.internalapi.SunEcmAccess;
import com.sunyard.ws.utils.XMLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;


@Repository
public class SingletonManageDAOIMP implements SingletonManageDAO {
	private  final static Logger log = LoggerFactory.getLogger(SingletonManageDAOIMP.class);

	public LazySingletonBean getDMInfo(String ip, String port) throws SunECMException {
		LazySingletonBean lazy = null;
		WSAccessClient wsclient = new WSAccessClient();	
		//连接指定服务器
		String dmName = DMAdressConstuct.getDMAdress(ip+":"+port);
		
		log.debug( "#################内存查询的url链接：" + dmName + "#################");
		
		SunEcmAccess access = wsclient.getAccessClient(dmName, 300000);
		String result = access.getSingletonInfo();
		lazy = XMLUtil.xml2Bean(XMLUtil.removeHeadRoot(result), LazySingletonBean.class);
		return lazy;
	}

	public LazySingletonBean regetDMInfo(String ip, String port) throws SunECMException {
		LazySingletonBean lazy = null;
		WSAccessClient wsclient = new WSAccessClient();	
		//连接指定服务器
		String dmName = DMAdressConstuct.getDMAdress(ip+":"+port);
		
		log.debug( "#################内存查询的url链接：" + dmName + "#################");
		
		SunEcmAccess access = wsclient.getAccessClient(dmName, 300000);
		String result = access.regetSingletonInfo();
		lazy = XMLUtil.xml2Bean(XMLUtil.removeHeadRoot(result), LazySingletonBean.class);
		return lazy;
	}

	public LazySingletonBean getUAInfo(String ip, String port) throws SunECMException {
		LazySingletonBean lazy = null;
		WSAccessClient wsclient = new WSAccessClient();	
		//连接指定服务器
		String uaName = DMAdressConstuct.getUAAdress(ip+":"+port);
		
		log.debug( "#################内存查询的url链接：" + uaName + "#################");
		
		SunEcmAccess access = wsclient.getAccessClient(uaName, 300000);
		String result = access.getUASingletonInfo();
		lazy = XMLUtil.xml2Bean(XMLUtil.removeHeadRoot(result), LazySingletonBean.class);
		return lazy;
	}
	
	public LazySingletonBean regetUAInfo(String ip, String port) throws SunECMException {
		LazySingletonBean lazy = null;
		WSAccessClient wsclient = new WSAccessClient();	
		//连接指定服务器
		String uaName = DMAdressConstuct.getUAAdress(ip+":"+port);
		
		log.debug( "#################内存查询的url链接：" + uaName + "#################");
		
		SunEcmAccess access = wsclient.getAccessClient(uaName, 300000);
		String result = access.regetUASingletonInfo();
		lazy = XMLUtil.xml2Bean(XMLUtil.removeHeadRoot(result), LazySingletonBean.class);
		return lazy;
	}

	public String createEsIndexToSunECMDM(String url,String json) throws SunECMException {
		log.info("向dm发送新建es索引信息：" + url+","+json );
		WSAccessClient wsclient = new WSAccessClient();
		// 连接指定服务器
		String uaName = DMAdressConstuct.getDMAdress(url);
		SunEcmAccess access = wsclient.getAccessClient(uaName, 300000);
		return access.createESIndex(json);
	}
	public String deleteEsIndexToSunECMDM(String url, String indexName) throws SunECMException {
		log.info("向dm发送刪除es索引信息：" + url+","+indexName);
		WSAccessClient wsclient = new WSAccessClient();
		// 连接指定服务器
		String uaName = DMAdressConstuct.getDMAdress(url);
		SunEcmAccess access = wsclient.getAccessClient(uaName, 300000);
		return access.deleteEsIndex(indexName);
	}
	public String searchEsFromDM(String url,String indexCode) throws SunECMException {
		log.info("向dm发送查询es索引信息：" + url+","+indexCode );
		WSAccessClient wsclient = new WSAccessClient();
		// 连接指定服务器
		String uaName = DMAdressConstuct.getDMAdress(url);
		SunEcmAccess access = wsclient.getAccessClient(uaName, 300000);
		return access.searchEsIndex(indexCode);
	}
}