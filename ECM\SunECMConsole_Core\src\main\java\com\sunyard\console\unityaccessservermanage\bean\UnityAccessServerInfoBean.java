package com.sunyard.console.unityaccessservermanage.bean;

/**
 * 统一接入服务器信息bean
 * 
 * <AUTHOR>
 * 
 */
public class UnityAccessServerInfoBean {
	private int server_id;// 服务器id
	private String server_name;// 服务器名称
	private String server_ip;// 服务器ip
	private int http_port;// http端口
	private int socket_port;// socket端口
	private String remark;// 备注
	private int group_id;// 服务器组id
	private int state;// 状态
	private String group_name;//服务器组名称
	private String ip;//服务器组ip
	private int https_port;// http端口
	private String trans_protocol;

	public String getTrans_protocol() {
		return trans_protocol;
	}

	public void setTrans_protocol(String trans_protocol) {
		this.trans_protocol = trans_protocol;
	}

	public int getHttps_port() {
		return https_port;
	}

	public void setHttps_port(int https_port) {
		this.https_port = https_port;
	}

	public int getServer_id() {
		return server_id;
	}

	public void setServer_id(int serverId) {
		server_id = serverId;
	}

	public String getServer_name() {
		return server_name;
	}

	public void setServer_name(String serverName) {
		server_name = serverName;
	}

	public String getServer_ip() {
		return server_ip;
	}

	public void setServer_ip(String serverIp) {
		server_ip = serverIp;
	}

	public int getHttp_port() {
		return http_port;
	}

	public void setHttp_port(int httpPort) {
		http_port = httpPort;
	}

	public int getSocket_port() {
		return socket_port;
	}

	public void setSocket_port(int socketPort) {
		socket_port = socketPort;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int groupId) {
		group_id = groupId;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String groupName) {
		group_name = groupName;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append("server_id:").append(server_id);
		sBuilder.append("server_name:").append(server_name);
		sBuilder.append("server_ip:").append(server_ip);
		sBuilder.append("http_port:").append(http_port);
		sBuilder.append("socket_port:").append(socket_port);
		sBuilder.append("remark:").append(remark);
		sBuilder.append("group_id:").append(group_id);
		sBuilder.append("state:").append(state);
		sBuilder.append("group_name:").append(group_name);
		sBuilder.append("ip:").append(ip);
		return sBuilder.toString();
	}
	
	

}
