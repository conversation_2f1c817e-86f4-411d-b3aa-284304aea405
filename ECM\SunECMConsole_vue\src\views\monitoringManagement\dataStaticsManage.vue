<template>
  <div class="app-container">
    <div class="filter-container">
       <el-select v-model="listQuery.group_id" placeholder="请选择存储服务器组" @change='getServers()'>
        <el-option
          v-for="item in storeGroups"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
     <el-select v-model="listQuery.server_id" placeholder="请选择存储服务器" @change='getServerModelCode()'>
        <el-option
          v-for="item in storeServers"
          :key="item.id"
          :label="item.text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-select v-model="listQuery.model_code" placeholder="请选择内容对象">
        <el-option
          v-for="item in storeObj"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
     </div> 

	<dataTotalRel ref="data_total_rel" 
		v-show="total_visible" 
		:total_visible="this.total_visible" 
	  :listQuery="this.listQuery"
    v-on:checkChartNum="checkChartNum"
	>
    </dataTotalRel>

    <taskTotalRel ref="task_total_rel" 
		v-show="task_visible" 
		:task_visible="this.task_visible" 
	  :listQuery="this.listQuery"
	>
    </taskTotalRel>

    <timeTotalRel ref="time_total_rel" 
		v-show="time_visile" 
		:time_visile="this.time_visile" 
	  :listQuery="this.listQuery"
	>
    </timeTotalRel>
        
    <fileTotalRel ref="file_total_rel" 
		v-show="countFile_visible" 
		:countFile_visible="this.countFile_visible" 
	  :listQuery="this.listQuery"
	>
    </fileTotalRel>
    
    <fsizeTotalRel ref="fsize_total_rel" 
		v-show="countFileSize_visible" 
		:countFileSize_visible="this.countFileSize_visible" 
	  :listQuery="this.listQuery"
	>
    </fsizeTotalRel>
  </div>

</template>

<script>
import {getContentServerGroup,getRelServers,getRelContentObject} from '@/api/monitorManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import taskTotalRel from "./dialog/task_total_rel.vue";
import dataTotalRel from "./dialog/data_total_rel.vue";
import timeTotalRel from "./dialog/time_total_rel.vue";
import fileTotalRel from "./dialog/file_total_rel.vue";
import fsizeTotalRel from "./dialog/fsize_total_rel.vue";

import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
    name: 'ComplexTable',
    components: { Pagination,dataTotalRel,taskTotalRel,timeTotalRel,fileTotalRel,fsizeTotalRel},
    directives: { waves,elDragDialog },
    filters: {
        statusFilter(status) {
        const statusMap = {
            published: 'success',
            draft: 'info',
            deleted: 'danger'
        }
        return statusMap[status]
    }
  },

  props: {
  },
  data() {
    return{
		  total_visible : false,
      task_visible : false,
      time_visile : false,
      countFile_visible : false,
      countFileSize_visible : false,
      storeGroups : [],
      storeServers : [],
      storeObj : [],
      listQuery: {
        importance: undefined,
        title: undefined,
        type: undefined
      },
    }
  },
  
    // mounted() {
    //   this.$nextTick(() => {
    //     this.showCharts()
    //   })
    // },
    created() {
        this.getGroups()
        // this.total_visible = true;
        // this.$refs.data_total_rel.showCharts();
        // this.checkChartNum()
    },
	// beforeDestroy(){
	// 	clearInterval(this.timer);
	// 	this.timer=null;
	// },

  methods: {
    handleclear() {
      this.listQuery.group_id= "";
      this.listQuery.server_id= "";
      this.listQuery.model_code= ""
    },

    handleFilter() {
      this.task_visible = false;
      this.time_visile = false;
      this.countFile_visible = false;
      this.countFileSize_visible = false;
		  this.total_visible = true;
      this.$refs.data_total_rel.showData();
    },
    getGroups(){
        getContentServerGroup().then(response => {
        this.storeGroups = response.root;
      })
    },

    getServers(){
        getRelServers(this.listQuery).then(response => {
        this.storeServers = response.root;
      })
    },

    getServerModelCode(){
        getRelContentObject(this.listQuery).then(response => {
        this.storeObj = response.root
      })
    },

    checkChartNum(val){
      let chartnum = val.split("_");
      this.listQuery.option = chartnum[1];
      this.total_visible = false;
      this.task_visible = false;
      this.time_visile = false;
      this.countFile_visible = false;
      this.countFileSize_visible = false;
      if(chartnum[0] == '1'){//任务数统计
        this.task_visible = true;
        this.$refs.task_total_rel.checkInit();
      }else if(chartnum[0] == '2'){//任务耗时统计
        this.time_visile = true;
        this.$refs.time_total_rel.checkInit();
      }else if(chartnum[0] == '3'){//文件接收数量
        this.countFile_visible = true;       
         this.$refs.file_total_rel.checkInit();
     }else if(chartnum[0] == '4'){//文件大小
        this.countFileSize_visible = true;       
        this.$refs.fsize_total_rel.checkInit();
     }
    }
  }
}
</script>

<style scoped>

</style>
