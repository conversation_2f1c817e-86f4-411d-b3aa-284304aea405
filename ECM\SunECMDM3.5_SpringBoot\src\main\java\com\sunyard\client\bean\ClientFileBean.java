package com.sunyard.client.bean;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sunyard.client.bean.converter.ClientStringCustomConverter;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamConverter;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

/**
 * 文档部件信息
 * 
 * <AUTHOR>
 * 
 */
@XStreamAlias("FileBean")
public class ClientFileBean {
	/** 客户端传入的文件流* */
	@XStreamOmitField
	private InputStream InputStream;
	/** 文件版本 * */
	@XStreamAsAttribute
	private String VERSION;
	/** 文件格式 * */
	@XStreamAsAttribute
	private String FILE_FORMAT;
	/** 文件ID * */
	@XStreamAsAttribute
	private String FILE_NO;
	/** 文件上传之前在客户端的路径+名称 * */
	@XStreamAsAttribute
	private String FILE_NAME;
	/** 文件大小 * */
	@XStreamAsAttribute
	private String FILE_SIZE;
	@XStreamAsAttribute
	private String REAL_FILE_SIZE;
	/** 文件访问的url * */
	@XStreamAsAttribute
	private String URL;
	/** 文件MD5码 * */
	@XStreamAsAttribute
	private String MD5_STR;
	/** 操作类型：U_ADD为追加，U_REPLACE替换， U_DEL为删除, U_MODIFY为更新索引 * */
	@XStreamAsAttribute
	private String OPTION_TYPE;
	/**
	 * 断点文件生成时，服务端会修改saveName。需添加此字段用以在断点时比较文件名。
	 */
	@XStreamAsAttribute
	private String origName;
	/**
	 * 服务端已接收文件大小
	 */
	@XStreamAsAttribute
	private Long received=null;
	/**
	 * 传输文件所使用的协议
	 */
	@XStreamAsAttribute
	private String PROTOCOL;
	/**
	 * 文件位置
	 */
	@XStreamAsAttribute
	private String LOCATION;
	
	/** 文件标签 **/
    private List<ClientTagBean> tagBeanList;
	
	public List<ClientTagBean> getTagBeanLis() {
		return tagBeanList;
	}

	public void setTagBeanLis(List<ClientTagBean> clientTaList) {
		this.tagBeanList = clientTaList;
	}
	
	public void addTagBeanList(ClientTagBean clientTagBean) {
		if (tagBeanList == null) {
			tagBeanList = new ArrayList<ClientTagBean>();
		}
		this.tagBeanList.add(clientTagBean);
	}

	public String getProtocol() {
		return PROTOCOL;
	}

	public void setProtocol(String protocol) {
		this.PROTOCOL = protocol;
	}

	public String getLocation() {
		return LOCATION;
	}
	public void setLocation(String location) {
		this.LOCATION=location;
	}
	public Long getReceived() {
		return received;
	}

	public String getREAL_FILE_SIZE() {
		return REAL_FILE_SIZE;
	}

	public void setREAL_FILE_SIZE(String rEAL_FILE_SIZE) {
		REAL_FILE_SIZE = rEAL_FILE_SIZE;
	}

	public void setReceived(Long received) {
		this.received = received;
	}
	public String getOptionType() {
		return OPTION_TYPE;
	}

	public void setOptionType(String optionType) {
		this.OPTION_TYPE = optionType;
	}

	/** 用户的其余自定义属性 * */
	@XStreamConverter(ClientStringCustomConverter.class)
	private Map<String, String> otherAtt;
	/** 文件自定义的批注信息 * */
	private List<ClientAnnotationBean> annoList;

	public String getVersion() {
		return VERSION;
	}

	/**
	 * 设定版本信息
	 * 
	 * @param version
	 *            版本信息
	 */
	public void setVersion(String version) {
		this.VERSION = version;
	}

	public String getFileFormat() {
		return FILE_FORMAT;
	}

	/**
	 * 设定文件后缀
	 * 
	 * @param fileFormat
	 *            文件后缀
	 */
	public void setFileFormat(String fileFormat) {
		this.FILE_FORMAT = fileFormat;
	}

	public String getFileName() {
		return FILE_NAME;
	}

	/**
	 * 设定文件名加上全路径,如E:/image/123.txt
	 * 
	 * @param fileName
	 *            文件名加上全路径
	 */
	public void setFileName(String fileName) {
		this.FILE_NAME = fileName;
		this.setREAL_FILE_SIZE(new File(fileName).length()+"");
	}

	public String getFilesize() {
		return FILE_SIZE;
	}

	/**
	 * 设定文件大小,字节为单位. 为大小图显示所用,没有设置则默认为文件本身大小
	 * 
	 * @param filesize
	 *            文件大小
	 */
	public void setFilesize(String filesize) {
		this.FILE_SIZE = filesize;
	}

	public String getUrl() {
		return URL;
	}

	public void setUrl(String url) {
		this.URL = url;
	}

	public String getMd5Str() {
		return MD5_STR;
	}

	/**
	 * 文件MD5码
	 * 
	 * @param md5Str
	 *            MD5码
	 */
	public void setMd5Str(String md5Str) {
		this.MD5_STR = md5Str;
	}

	public Map<String, String> getOtherAtt() {
		return otherAtt;
	}

	/**
	 * 文件自定义属性
	 * 
	 * @param otherAtt
	 *            自定义属性
	 */
	public void setOtherAtt(Map<String, String> otherAtt) {
		this.otherAtt = otherAtt;
	}

	/**
	 * 添加文件自定义属性
	 * 
	 * @param key
	 *            属性字段
	 * @param value
	 *            属性值
	 */
	public void addOtherAtt(String key, String value) {
		if (otherAtt == null) {
			otherAtt = new HashMap<String, String>();
		}
		this.otherAtt.put(key, value);
	}

	public List<ClientAnnotationBean> getAnnoList() {
		return annoList;
	}

	/**
	 * 文件批注
	 * 
	 * @param annoList
	 *            批注队列
	 */
	public void setAnnoList(List<ClientAnnotationBean> annoList) {
		this.annoList = annoList;
	}

	/**
	 * 添加批注
	 * 
	 * @param anno
	 *            添加批注
	 */
	public void addAnnoList(ClientAnnotationBean anno) {
		if (annoList == null) {
			annoList = new ArrayList<ClientAnnotationBean>();
		}
		this.annoList.add(anno);
	}

	public String getFileNO() {
		return FILE_NO;
	}

	/**
	 * 设定文件NO
	 * 
	 * @param fileNO
	 *            文件NO
	 */
	public void setFileNO(String fileNO) {
		this.FILE_NO = fileNO;
	}

	public String getOrigName() {
		return origName;
	}

	public void setOrigName(String origName) {
		this.origName = origName;
	}

	public InputStream getInputStream() {
		return InputStream;
	}

	public void setInputStream(InputStream inputStream) {
		InputStream = inputStream;
	}

}