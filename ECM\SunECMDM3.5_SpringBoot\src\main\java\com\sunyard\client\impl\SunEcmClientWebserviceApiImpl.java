package com.sunyard.client.impl;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.sunyard.ecm.server.bean.MigrateBatchBean;

import com.sunyard.util.TransOptionKey;
import com.sunyard.ws.client.WSConsoleClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.client.bean.ClientHeightQuery;
import com.sunyard.client.conn.WSConn;
import com.sunyard.es.util.EsBean;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.util.CodeUtil;
import com.sunyard.util.OptionKey;
import com.sunyard.ws.internalapi.SunEcmConsole;
import com.sunyard.ws.utils.XMLUtil;

public class SunEcmClientWebserviceApiImpl extends AbstractSunECMClientApi {
	private final static  Logger log = LoggerFactory.getLogger(SunEcmClientWebserviceApiImpl.class);
	private WSConn conn;
	// private String ip;
	// private int httpPort;
	// private String serverName = DMNameConstuct.getDM();
	private String splitSym = TransOptionKey.SPLITSYM;

	public SunEcmClientWebserviceApiImpl(String ip, int httpPort,
			String serverName) {
		this.conn = new WSConn(ip, httpPort, serverName);
	}

	public String checkIn(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--checkIn-->检入");
		String result = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		String sendMsg = "OPTION=" + OptionKey.CHECKIN + ",XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
		sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
		conn.sendMsg(sendMsg);
		log.debug( "--checkIn-->检入时发送的消息：" + sendMsg);
		String msg = conn.receiveMsg();
		log.debug( "--checkIn-->检入时返回的消息：" + msg);
		String[] strArray = msg.split(splitSym);
		if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (strArray[1].equals(TransOptionKey.SUCCESS)) {
				result = TransOptionKey.SUCCESS + splitSym;
			} else {
				result = TransOptionKey.FAIL;
			}
		} else {
			log.warn( "--checkIn-->检入时发生异常,异常代码：" + msg);
			throw new SunECMException("--checkIn-->检入时发生异常,异常代码：" + msg);
		}
		log.info( "--checkIn-->检入(over)-->result:" + result);
		return result;
	}

	public String checkOut(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--checkOut-->检出");
		String result = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		String sendMsg = "OPTION=" + OptionKey.CHECKOUT + ",XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
		sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
		conn.sendMsg(sendMsg);
		log.debug( "--checkOut-->检出时发送的消息：" + sendMsg);
		String msg = conn.receiveMsg();
		log.debug( "--checkOut-->检出时返回的消息：" + msg);
		String[] strArray = msg.split(splitSym);
		if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (strArray[1].equals(TransOptionKey.SUCCESS)) {
				result = TransOptionKey.SUCCESS + splitSym + strArray[2];
			} else {
				result = TransOptionKey.FAIL + splitSym + strArray[2];
			}
		} else {
			log.warn( "--checkOut-->检出时发生异常,异常代码：" + msg);
			throw new SunECMException("--checkOut-->检出时发生异常,异常代码：" + msg);
		}
		log.info( "--checkOut-->检出(over)-->result:" + result);
		return result;
	}

	
	public String createContentID(ClientBatchBean clientBatchBean, String groupName)
			throws SunECMException, IOException {
		log.info("--createID-->申请批次号");
		String result = "";
		CodeUtil.encodeInBean(clientBatchBean);
		String sendMsg = "OPTION=" + OptionKey.CREATEID + ",XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",GROUPNAME=" + groupName;
		sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
		conn.sendMsg(sendMsg);
		String msg = conn.receiveMsg();
		log.debug("--createContentID-->申请批次号时返回的信息：" + msg);
		String[] strArray = msg.split(splitSym);
		if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (strArray[1].equals(TransOptionKey.SUCCESS)) {
				result = msg;
			} else {
				result = TransOptionKey.FAIL;
			}
		} else {
			log.warn("--createContentID-->申请批次号时发生异常...异常代码：" + msg);
			throw new SunECMException("--createContentID-->申请批次号时发生异常...异常代码：" + msg);
		}
		log.info("--createContentID-->申请批次号-->result:" + result);
		return result;
	}
	
	public String delete(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--delete-->删除");
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		String sendMsg = "OPTION=" + OptionKey.DEL + ",XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
		sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
		conn.sendMsg(sendMsg);
		log.debug( "--delete-->删除时发送的消息：" + sendMsg);
		resultStr = conn.receiveMsg();
		log.info( "--delete-->删除时返回的消息：" + resultStr);
		return resultStr;
	}

	public String getAllModelMsg_Client() throws Exception {
		log.info( "--getAllModelMsg_Client-->获取内容模型列表信息");
		String resultStr = TransOptionKey.FAIL;
		String sendMsg = "OPTION=" + OptionKey.ALLMODELMSG;
		conn.sendMsg(sendMsg);
		log.debug( "--getAllModelMsg_Client-->获取内容模型列表信息时发送的消息："
				+ sendMsg);
		resultStr = conn.receiveMsg();
		log.info( "--getAllModelMsg_Client-->获取内容模型列表信息时返回的消息："
				+ resultStr);
		return resultStr;
	}

	public String getContentServerInfo_Client() throws Exception {
		log.info( "--getContentServerInfo_Client-->获取所有服务器信息");
		String resultStr = TransOptionKey.FAIL;
		String sendMsg = "OPTION=" + OptionKey.GET_NODE;
		conn.sendMsg(sendMsg);
		log.debug(
				"--getContentServerInfo_Client-->获取所有服务器信息时发送的消息：" + sendMsg);
		resultStr = conn.receiveMsg();
		log.info(
				"--getContentServerInfo_Client-->获取所有服务器信息时返回的消息：" + resultStr);
		return resultStr;
	}

	public String getModelTemplate_Client(String[] modeCodes) throws Exception {
		log.info( "--getModelTemplate_Client-->获取内容模型模版");
		String resultStr = TransOptionKey.FAIL;
		StringBuffer modeNamesStr = new StringBuffer();
		for (String modeCode : modeCodes) {
			modeNamesStr.append(modeCode).append(TransOptionKey.MODESPLITSYM);
		}
		String sendMsg = "OPTION=" + OptionKey.METATEMPLATE + ",MODENAMES="
				+ modeNamesStr.toString();
		conn.sendMsg(sendMsg);
		log.debug( "--getModelTemplate_Client-->获取内容模型模版时发送的消息："
				+ sendMsg);
		resultStr = conn.receiveMsg();
		log.info( "--getModelTemplate_Client-->获取内容模型模版时返回的消息："
				+ resultStr);
		return resultStr;
	}

	public String getPermissions_Client(String userName, String passWord)
			throws Exception {
		log.info( "--getPermissions_Client-->获取内容模型权限");
		String resultStr = TransOptionKey.FAIL;
		String sendMsg = "OPTION=" + OptionKey.PERMISSION + ",USERNAME="
				+ userName + ",PASSWORD=" + CodeUtil.encode(passWord);
		conn.sendMsg(sendMsg);
		log.debug( "--getPermissions_Client-->获取内容模型权限时发送的消息："
				+ sendMsg);
		resultStr = conn.receiveMsg();
		log.info( "--getPermissions_Client-->获取内容模型权限时返回的消息："
				+ resultStr);
		return resultStr;
	}

	public String getToken(String ipAddress, String tokenCheckValue,
			String userName, String operationCode) throws Exception {
		log.info( "--getToken-->获取令牌");
		String token = TransOptionKey.FAIL;
		WSConsoleClient consoleClient = new WSConsoleClient();
		SunEcmConsole console = consoleClient.getEcmConsoleClient(ipAddress
				+ "/webservices/WsInterface", 300000);
		token = console.getToken(tokenCheckValue, userName, operationCode);
		log.info( "--getToken-->获取令牌(over)-->token:" + token);
		return token;
	}

	public String heightQuery(ClientHeightQuery heightQuery, String dmsName)
			throws Exception {
		log.info( "--heightQuery-->高级查询");
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(heightQuery);
		log.debug(XMLUtil.bean2XML(heightQuery));
		String sendMsg = "OPTION=" + OptionKey.HEIGHT_QUERY + ",XML="
				+ XMLUtil.bean2XML(heightQuery) + ",DMSNAME=" + dmsName;
		sendMsg=createHeightQuerySendMsgbyToken(heightQuery, sendMsg);
		conn.sendMsg(sendMsg);
		log.debug( "--heightQuery-->高级查询时发送的消息：" + sendMsg);
		resultStr = conn.receiveMsg();
		log.info( "--heightQuery-->高级查询时返回的消息：" + resultStr);
		return resultStr;
	}

	public String inquireDMByGroup(String userName, String groupName)
			throws Exception {
		log.info( "--inquireDMByGroup-->向统一接入问询内容存储服务器的地址");
		String resultStr = TransOptionKey.FAIL;
		String sendMsg = "OPTION=" + OptionKey.INQUIREDM + ",USERNAME="
				+ userName + ",DMSNAME=" + groupName;
		conn.sendMsg(sendMsg);
		log.debug( "--inquireDMByGroup-->向统一接入问询内容存储服务器的地址时发送的消息："
				+ sendMsg);
		resultStr = conn.receiveMsg();
		log.info( "--inquireDMByGroup-->向统一接入问询内容存储服务器的地址时返回的消息："
				+ resultStr);
		return resultStr;
	}

	public String login(String userName, String password) throws Exception {
		password = CodeUtil.encode(password);
		log.info( "--login-->登录 userName:" + userName + "password" + password);
		String resultMsg = TransOptionKey.FAIL;
		StringBuffer sbuf = new StringBuffer();
		sbuf.append("OPTION=" + OptionKey.LOGIN + ",USERNAME=")
				.append(userName).append(",PASSWORD=").append(password);
		conn.sendMsg(sbuf.toString());
		log.debug( "--login-->登录时发送的消息：" + sbuf.toString());
		String msg = conn.receiveMsg();
		log.debug( "--login-->登录时返回的消息：" + msg);

		// 服务端异常有可能返回NULL判断为空时提示客户端登陆失败
		if (msg == null || "null".equals(msg)) {
			throw new SunECMException(
					"--SunEcmClientSocketApiImpl-->login-->登陆失败");
		}

		String[] strArray = msg.split(splitSym);
		if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (strArray[1].equals(TransOptionKey.SUCCESS)) {
				resultMsg = TransOptionKey.SUCCESS;
			} else {
				log.warn( "登陆失败");
//				throw new SunECMException("登陆失败");
			}
		}
		log.info( "--login-->登录(over)-->resultMsg:" + resultMsg);
		return resultMsg;
	}

	public String logout(String userName) throws Exception {
		log.info( "--logout-->登出 userName:" + userName);
		String resultMsg = TransOptionKey.FAIL;
		StringBuffer sbuf = new StringBuffer();
		sbuf.append("OPTION=" + OptionKey.LOGOUT + ",USERNAME=").append(
				userName);
		conn.sendMsg(sbuf.toString());
		log.debug( "--logout-->登出时发送的消息：" + sbuf.toString());
		String msg = conn.receiveMsg();
		log.debug( "--logout-->登出时返回的消息：" + msg);
		log.debug(msg);
		String[] strArray = msg.split(splitSym);
		if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (strArray[1].equals(TransOptionKey.SUCCESS)) {
				resultMsg = TransOptionKey.SUCCESS;
			} else {
				log.warn( "登出异常");
				throw new SunECMException("登出异常");
			}
		}
		log.info( "--logout-->登出(over)-->resultMsg:" + resultMsg);
		return resultMsg;
	}

	public String operAnnotation(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--operAnnotation-->批注操作");
		String resultStr = TransOptionKey.FAIL;
		conn.sendMsg("OPTION=" + OptionKey.A_OR_U_ANNOTATION + ",XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName);
		resultStr = conn.receiveMsg();
		log.info( "--operAnnotation-->批注操作(over)-->resultStr:"
				+ resultStr);
		return resultStr;
	}

	public String queryAnnotation(ClientBatchBean clientBatchBean,
			String dmsName) throws Exception {
		log.info( "--queryAnnotation-->批注查询");
		String resultStr = TransOptionKey.FAIL;
		conn.sendMsg("OPTION=" + OptionKey.ANNOTATION + ",XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName);
		resultStr = conn.receiveMsg();
		log.info( "--queryAnnotation-->批注查询(over)-->resultStr:"
				+ resultStr);
		return resultStr;
	}

	public String queryBatch(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--queryBatch-->查询");
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		String sendMsg = "OPTION=" + OptionKey.QUERY + ",XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName
				+ ",USERNAME=" + clientBatchBean.getUser() + ",PASSWORD="
				+ clientBatchBean.getPassWord();
		conn.sendMsg(sendMsg);
		log.debug( "--queryBatch-->查询时发送的消息：" + sendMsg);
		resultStr = conn.receiveMsg();
		log.info( "--queryBatch-->查询时返回的消息：" + resultStr);
		return resultStr;
	}

	public String update(ClientBatchBean clientBatchBean, String dmsName,
			boolean isAutoCheck) throws Exception {
		log.info( "--update-->更新");
		String resultStr = TransOptionKey.FAIL;
		if (!checkFileExist(clientBatchBean)) {
			return TransOptionKey.FAIL+TransOptionKey.SPLITSYM+SunECMExceptionStatus.FILE_NOT_FOUND;  //文件不存在
		}
		// 添加MD5码
		MD5Util.addBatchMD5Code(clientBatchBean);
		if (isAutoCheck) {
			//保存之前的password
			String OldPassWord=clientBatchBean.getPassWord();
			
			String checkOutMsg = checkOut(clientBatchBean, dmsName);
			if (checkOutMsg.split(splitSym)[0].equals(TransOptionKey.SUCCESS)) {
				clientBatchBean.setCheckToken(checkOutMsg.split(splitSym)[1]);
				log.debug( "--update-->批次["
						+ clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出成功...");
			} else {
				log.warn( "--update-->批次["
						+ clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出失败:" + checkOutMsg);
				throw new SunECMException("--update-->批次["
						+ clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出失败:" + checkOutMsg);
			}
			try {
				//重置之前的password
				clientBatchBean.setPassWord(OldPassWord);
				resultStr = update(clientBatchBean, dmsName);
				log.debug( "--update-->自动检入检出更新返回结果：" + resultStr);
			} finally {
				//重置之前的password
				clientBatchBean.setPassWord(OldPassWord);
				String checkInMsg = checkIn(clientBatchBean, dmsName);
				if (!checkInMsg.split(splitSym)[0]
						.equals(TransOptionKey.SUCCESS)) {
					log.warn( "--update-->批次["
							+ clientBatchBean.getIndex_Object().getContentID()
							+ "]自动检入失败:" + checkInMsg);
					throw new SunECMException("--update-->批次["
							+ clientBatchBean.getIndex_Object().getContentID()
							+ "]自动检入失败:" + checkInMsg);
				}
			}
			log.debug( "--update-->批次["
					+ clientBatchBean.getIndex_Object().getContentID()
					+ "]自动检入成功...");
		} else {
			resultStr = update(clientBatchBean, dmsName);
			log.debug( "--update-->手动检入检出更新返回结果：" + resultStr);
		}
		log.info( "--update-->更新(over)");
		return resultStr;
	}

	private String update(ClientBatchBean clientBatchBean, String dmsName)
			throws SunECMException, IOException {
		log.info( "--update-->更新");
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		String sendMsg = "OPTION=" + OptionKey.UPDATE + ",START=START,XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
		sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
		conn.sendMsg(sendMsg);
		log.debug( "--update-->更新前建立连接时发送的消息：" + sendMsg);
		String msg = conn.receiveMsg();
		log.debug( "--update-->更新前建立连接时返回的消息：" + msg);
		String[] msgArray = msg.split(splitSym);
		if (msgArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (clientBatchBean.isBreakPoint()) {
				sendBreakClientBatchFileBean(clientBatchBean
						.getDocument_Objects(), clientBatchBean
						.getIndex_Object().getContentID(), clientBatchBean
						.getModelCode());
			} else {
				sendClientBatchFileBean(clientBatchBean.getDocument_Objects(),
						clientBatchBean.getIndex_Object().getContentID());
			}
			String sendMsg_1 = "OPTION=" + OptionKey.UPDATE
					+ ",START=END,CONTENTID="
					+ clientBatchBean.getIndex_Object().getContentID()
					+ ",DMSNAME=" + dmsName;
			conn.sendMsg(sendMsg_1);
			log.debug( "--update-->更新文件上传完成后发送的消息：" + sendMsg_1);
			msg = conn.receiveMsg();
			log.debug( "--update-->更新文件上传完成后返回的消息：" + msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					resultStr = TransOptionKey.SUCCESS;
				}
			} else {
				resultStr += TransOptionKey.SPLITSYM + msg;
			}
		} else {
			resultStr = msg;
		}
		log.info( "--update-->更新(over)-->resultStr:" + resultStr);
		return resultStr;
	}

	public String upload(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--upload-->上传");
		if (!checkFileExist(clientBatchBean)) {
			return TransOptionKey.FAIL;
		}
		// 添加MD5码
		MD5Util.addBatchMD5Code(clientBatchBean);
		String reslutMsg = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		StringBuffer uploadMsg = new StringBuffer();
		uploadMsg.append(
				"OPTION=" + OptionKey.UPLOAD + ",START=START,XML="
						+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME="
						+ dmsName);
		uploadMsg.append(createBatchSendMsgbyToken(clientBatchBean, uploadMsg.toString()));		
		// HTTP传递信息
		conn.sendMsg(uploadMsg.toString());
		log.debug("--upload-->上传文件前发送的消息：" + uploadMsg.toString());
		String returnMsg = conn.receiveMsg();
		log.debug( "--upload-->上传文件前返回的信息：" + returnMsg);
		String[] params = returnMsg.split(splitSym);
		String contentID = TransOptionKey.FAIL; // 内容ID初始化
		if (!params[0].equals(TransOptionKey.SERVER_OK)) {
			//此时生成批次号失败
			return  TransOptionKey.FAIL + TransOptionKey.SPLITSYM
			+ returnMsg;
			
		}
		if (params[0].equals(TransOptionKey.SERVER_OK)) {
			contentID = params[1];
			reslutMsg = TransOptionKey.FAIL + TransOptionKey.SPLITSYM
					+ contentID;
			clientBatchBean.getIndex_Object().setContentID(contentID);
			// 根据是否断点续传进行文件的上传
			if (clientBatchBean.isBreakPoint()) {
				sendBreakClientBatchFileBean(clientBatchBean
						.getDocument_Objects(), contentID, clientBatchBean
						.getModelCode());
			} else {
				sendClientBatchFileBean(clientBatchBean.getDocument_Objects(),
						contentID);
			}
		}
		String sendMsg = "OPTION=" + OptionKey.UPLOAD + ",START=END,CONTENTID="
				+ clientBatchBean.getIndex_Object().getContentID()
				+ ",DMSNAME=" + dmsName;
		conn.sendMsg(sendMsg);
		log.debug( "--upload-->上传文件完成后发送的消息：" + sendMsg);
		returnMsg = conn.receiveMsg();
		log.debug( "--upload-->上传文件完成后返回的信息：" + returnMsg);
		String[] strArray = returnMsg.split(splitSym);
		if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (strArray[1].equals(TransOptionKey.SUCCESS)) {
				reslutMsg = TransOptionKey.SUCCESS + TransOptionKey.SPLITSYM
						+ strArray[2];
			}
		} else {
			reslutMsg += TransOptionKey.SPLITSYM + returnMsg;
		}
		log.info( "--upload-->上传(over)-->reslutMsg:" + reslutMsg);
		return reslutMsg;
	}

	private void sendClientBatchFileBean(
			List<ClientBatchFileBean> ClientBatchFileBeans, String contentID)
			throws IOException {
		log.info( "--sendClientBatchFileBean-->上传文件 contentID:"
				+ contentID);
		for (ClientBatchFileBean batchFileBean : ClientBatchFileBeans) {
			List<ClientFileBean> fileBeans = batchFileBean.getFiles();
			for (ClientFileBean fileBean : fileBeans) {
				if (fileBean.getFileName() != null) {
					conn.sendFileData(fileBean.getFileName(), contentID, null);
				}
			}
		}
	}

	private void sendBreakClientBatchFileBean(
			List<ClientBatchFileBean> ClientBatchFileBeans, String contentID,
			String modeCode) throws IOException {
		log.info( "--sendBreakClientBatchFileBean-->断点上传文件");
		conn.sendMsg("OPTION=" + OptionKey.BREAK_POINT + ",CONTENTID="
				+ contentID + ",MODECODE=" + modeCode);
		String filesStr = conn.receiveMsg().split(splitSym)[1];
		log.debug(
				"--sendBreakClientBatchFileBean-->Break Point Msg is: "
						+ filesStr);
		List<ClientFileBean> completeFiles = XMLUtil.xml2list(filesStr,
				ClientFileBean.class);

		// 统计所有的待上传文件
		List<ClientFileBean> totalFileBean = new ArrayList<ClientFileBean>();
		for (ClientBatchFileBean batchFileBean : ClientBatchFileBeans) {
			List<ClientFileBean> fileBeans = batchFileBean.getFiles();
			for (ClientFileBean fileBean : fileBeans) {
				if (fileBean.getFileName() != null) {
					totalFileBean.add(fileBean);
				}
			}
		}
		// 将不属于已上传的文件上传
		if (completeFiles.size() != 0) {
			List<ClientFileBean> breakFileBean=new ArrayList<ClientFileBean>();
			for (ClientFileBean fileBean : totalFileBean) {
				boolean flag=true;
				for (ClientFileBean complteFile : completeFiles) {
					if (fileBean.getFileName().equals(complteFile.getOrigName())) {
						flag=false;
						Long l=new File(fileBean.getFileName()).length();
						if(complteFile.getReceived()!=null&&!l.equals(complteFile.getReceived())){
							breakFileBean.add(fileBean);
						}
					}
				}
				if(flag){
					breakFileBean.add(fileBean);
				}
			}
			for (ClientFileBean fileBean : breakFileBean) {
				conn.sendFileData(fileBean.getFileName(), contentID,
						TransOptionKey.FILE_RECIEVE + TransOptionKey.SPLITSYM);
			}
		} else {
			sendClientBatchFileBean(ClientBatchFileBeans, contentID);
		}
	}

	/**
	 * 校验文件是否存在,并统计文件数
	 * 
	 * @param clientBatchBean
	 * @return
	 */
	private boolean checkFileExist(ClientBatchBean clientBatchBean) {
		log.info( "--checkFileExist-->校验文件是否存在");
		boolean flag = false; // 是否自动统计批次下的文件数
		if (clientBatchBean.getIndex_Object().getAmount() == null
				|| clientBatchBean.getIndex_Object().getAmount().equals("")) {
			flag = true;
		}
		int totalFile = 0;
		// 校验文件是否存在
		List<ClientBatchFileBean> batchFileBeans = clientBatchBean
				.getDocument_Objects();
		for (ClientBatchFileBean clientBatchFileBean : batchFileBeans) {
			List<ClientFileBean> files = clientBatchFileBean.getFiles();
			for (ClientFileBean clientFileBean : files) {
				if (clientBatchFileBean.getFilePartName().equals(
						clientBatchBean.getModelCode())) {
					clientBatchBean.getIndex_Object().setCustomMap(
							clientFileBean.getOtherAtt());
				}
				if (clientFileBean.getFileName() != null) {
					File file = new File(clientFileBean.getFileName());
					if (!file.exists() || file.isDirectory()) {
						log.info( "--checkFileExist-->"
								+ file.getPath() + "文件不存在...");
						return false;
					} else {
						if (flag) {
							totalFile++;
						}
					}
				}
				if(clientFileBean.getProtocol()!=null&&flag){
					totalFile++;
				}
			}
		}
		if (flag) {
			clientBatchBean.getIndex_Object().setAmount(
					String.valueOf(totalFile));
		}
		return true;
	}

	public String immedMigrate(MigrateBatchBean migrateBatchBean, String dmsName)
			throws Exception {
		log.info( "--immedMigrate-->立即迁移");
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(migrateBatchBean);
		String sendMsg = "OPTION=" + OptionKey.IMMEDIATEMIGRATE + ",XML="
				+ XMLUtil.bean2XML(migrateBatchBean) + ",DMSNAME=" + dmsName;
		sendMsg=createImmigrateBatchSendMsgbyToken(migrateBatchBean, sendMsg);
		conn.sendMsg(sendMsg);
		log.debug( "--immedMigrate-->立即迁移时发送的消息：" + sendMsg);
		resultStr = conn.receiveMsg();
		log.debug( "--immedMigrate-->立即迁移时返回的消息：" + resultStr);
		return resultStr;
	}

	public String queryNodeInfoByGroupIdAndInsNo(String itemType, String insNo)
			throws Exception {
		log.info( "--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息,"
				+ "modelCode" + itemType + "insNo" + insNo);
		String resultStr = TransOptionKey.FAIL;
		String sendMsg = "OPTION=" + OptionKey.QUERY_NODEINFO_BY_GROUPID_AND_INSNO+",MODELCODE="+itemType+",INSNO="+insNo;
		conn.sendMsg(sendMsg);
		log.debug(
				"--getContentServerInfo_Client-->向统一接入问询机构配置信息的服务器信息时发送的消息：" + sendMsg);
		resultStr = conn.receiveMsg();
		log.info(
				"--getContentServerInfo_Client-->向统一接入问询机构配置信息的服务器信息时返回的消息：" + resultStr);
		return resultStr;
	}

	public String createUserToken(String username, String password) throws Exception {
		log.info("开始获取用户令牌");
		String result = TransOptionKey.FAIL;
		String sendMsg="OPTION="+OptionKey.CREATEUSERTOKEN+",USERNAME="+username+",PASSWORD="+CodeUtil.encode(password);
		conn.sendMsg(sendMsg);
		log.debug( "--createUserToken-->获取用户令牌时发送的消息：" + sendMsg);
		String res=conn.receiveMsg();
		log.debug( "--createUserToken-->获取用户令牌时返回的消息：" + res);

		// 服务端异常有可能返回NULL判断为空时提示客户端登陆失败
		if (res == null || "null".equals(res)) {
			log.warn( "--login-->获取用户令牌失败-->msg=" + res);
			throw new SunECMException("--createUserToken-->获取用户令牌失败");
		}

		String[] strArray = res.split(splitSym);
		if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (strArray[1].equals(TransOptionKey.SUCCESS)) {
				result = TransOptionKey.SUCCESS + splitSym + strArray[2];
			} else {
				result = TransOptionKey.FAIL + splitSym + strArray[2];
			}
		} else {
			throw new SunECMException("获取用户令牌时发生异常...异常代码：" + res);
		}
		return result;
	}

	public String copyBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	public String immedCopyBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	public String otherRuquest(ClientBatchBean clientBatchBean, String requestName) throws Exception {
		log.info( "--第三方接口调用-->");
		String resultStr = "";
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + "OTHERREQUEST" + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",REQUESTNAME=" + requestName;
			conn.sendMsg(sendMsg);
			log.debug("--otherRuquest-->第三方请求发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--otherRuquest-->第三方请求返回的消息：" + resultStr);
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.debug("--第三方请求--->(over)");
		return resultStr;
	}
	
	public String uploadByStream(ClientBatchBean clientBatchBean, String groupName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public String queryESByECM_DOC_ID(String indexName, String ecm_doc_id) throws Exception {
		return null;
	}
	@Override
	public String queryESByBool(EsBean esBean) throws Exception {
			return null;
	}
	@Override
	public String uploadESTag(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public String updateESTag(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
}