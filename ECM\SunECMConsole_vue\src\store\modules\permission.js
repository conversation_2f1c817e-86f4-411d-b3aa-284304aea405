import { asyncRoutes, constantRoutes } from '@/router'
import store from "@/store";


/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some(role => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []
// console.log("routes",routes)
  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {

      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      // console.log("push",tmp)
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  }
}

const actions = {
  generateRoutes({ commit }, roles) {
      // console.info("before",roles);
    return new Promise(resolve => {
      let accessedRoutes = filterAsyncRoutes(asyncRoutes, roles)
      // console.log("after",accessedRoutes)
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

// 用来控制按钮的显示
export function hasBtnPermission(permission) {
  const myBtns = store.getters.buttons
  return myBtns.indexOf(permission) > -1
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

