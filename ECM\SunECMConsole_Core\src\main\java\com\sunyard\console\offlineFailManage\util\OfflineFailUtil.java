package com.sunyard.console.offlineFailManage.util;

import com.sunyard.console.common.util.SpringUtil;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.configmanager.bean.OfflineCountBean;
import com.sunyard.console.configmanager.wsserviceutil.DMAdressConstuct;
import com.sunyard.console.configmanager.wsserviceutil.WsBeanInterface;
import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.contentservermanage.dao.ContentServerGroupManageDAO;
import com.sunyard.exception.SunECMException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;

public class OfflineFailUtil {
	ContentServerGroupManageDAO contentServerGroupManageDao = (ContentServerGroupManageDAO) SpringUtil.ctx.getBean("contentServerGroupManageDao");
	private  final static Logger log = LoggerFactory.getLogger(OfflineFailUtil.class);

	public List<OfflineCountBean> getOfflineFailCount(String groupId, String modelCode, String beginDate, String endDate, boolean queryAbandon) {
		String sendIp = getConnectionDmURL(groupId);
		if (sendIp == null) {
			log.warn("找不到可以联通的dm,组id为:" + groupId + "]");
			return null;
		}
		WsBeanInterface wsClient = (WsBeanInterface) SpringUtil.ctx.getBean("wsclient");
		return wsClient.getOfflineFailCount(sendIp, modelCode, beginDate, endDate, queryAbandon);
	}

	/**
	 * 查询放弃和离线失败批次
	 * 
	 * @param groupId
	 * @param modelCode
	 * @param beginDate
	 * @param endDate
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<OfflineCountBean> getFailOfflineList(String groupId, String modelCode, String beginDate, String endDate,String type,int start, int limit) {
		String sendIp = getConnectionDmURL(groupId);
		if (sendIp == null) {
			log.warn("找不到可以联通的dm,组id为:" + groupId + "]");
			return null;
		}
		WsBeanInterface wsClient = (WsBeanInterface) SpringUtil.ctx.getBean("wsclient");
		return wsClient.getFailOfflineList(sendIp, modelCode, beginDate, endDate,type, start, limit);
	}

	/**
	 * 按天放弃离线
	 * 
	 * @param groupId
	 * @param modelCode
	 * @param startDates
	 */
	public void abandonOfflineByDate(String groupId, String modelCode, String startDates) {
		if (StringUtil.stringIsNull(groupId) || StringUtil.stringIsNull(modelCode) || StringUtil.stringIsNull(startDates)) {
			log.warn("groupId:" + groupId + ",modelCode=" + modelCode);
			return;
		}
		String sendIp = getConnectionDmURL(groupId);
		if (sendIp == null) {
			log.warn("找不到可以联通的dm,组id为:" + groupId + "]");
			return;
		}
		WsBeanInterface wsClient = (WsBeanInterface) SpringUtil.ctx.getBean("wsclient");
		wsClient.abandonOfflineByDate(sendIp, modelCode, startDates);
	}

	/**
	 * 按批次放弃离线
	 * 
	 * @param groupId
	 * @param modelCode
	 * @param startDates
	 */
	public void abandonOfflineByContentId(String groupId, String modelCode,List<OfflineCountBean> beanlist) {
		if (StringUtil.stringIsNull(groupId) || StringUtil.stringIsNull(modelCode)) {
			log.warn("groupId:" + groupId + ",modelCode=" + modelCode);
			return;
		}
		if (beanlist == null || beanlist.size() == 0) {
			log.warn("beanlist is null");
			return;
		}
		String sendIp = getConnectionDmURL(groupId);
		if (sendIp == null) {
			log.warn("找不到可以联通的dm,组id为:" + groupId + "]");
			return;
		}
		WsBeanInterface wsClient = (WsBeanInterface) SpringUtil.ctx.getBean("wsclient");
		try {
			wsClient.abandonOfflineByContentId(sendIp, modelCode, beanlist);
		} catch (SunECMException e) {
			log.error("按批次离线失败", e);
		}
	}

	/**
	 * 获取组内http端口可以联通的dm 的url
	 * 
	 * @param contentServerList
	 * @return
	 */
	public String getConnectionDmURL(String groupId) {
		log.info("begin to getDMUrl:" + groupId);
		List<ContentServerInfoBean> contentServerList = contentServerGroupManageDao.getRelContentServerList(groupId, true);
		if (contentServerList == null) {
			log.warn("该服务器组下没有启用的服务器，groupid=" + groupId + "]");
			return null;
		}
		String sendIp = null;
		if (contentServerList != null) {
			for (ContentServerInfoBean server : contentServerList) {
				String ipAndPort;
				ipAndPort = server.getServer_ip() + ":" + server.getHttp_port();
				sendIp = DMAdressConstuct.getDMAdress(ipAndPort);
				sendIp+="?wsdl";
				int httpCode = getUrlConnection(sendIp);
				log.info("ip:" + sendIp + ",httpCode=" + httpCode + "]");
				if (httpCode == 200) {
					// 网络可以联通，
					break;
				} else {
					log.error(sendIp + " can not connection,httpCode=" + httpCode);
				}
			}
		}
		return sendIp;
	}

	public static int getUrlConnection(String urlString) {
		int connectionInt = 500;
		try {
			URL url = new URL(urlString);
			HttpURLConnection conn = (HttpURLConnection) url.openConnection();
			int size = conn.getHeaderFields().size();
			if (size > 0) {
				connectionInt = conn.getResponseCode();
			}
		} catch (IOException x) {
			log.error("error");
		}
		return connectionInt;
	}

//	public static void main(String[] args) {
//		 System.out.println(getUrlConnection("http://127.0.0.1:8080/SunECMDM/webservices/WsInterface"));
//		 
//
////		List<Integer> list = new ArrayList<Integer>();
////		for (int i = 1; i < 101; i++) {
////			list.add(i);
////		}
////
////		int pagesize = 30;
////		int totalcount = list.size();
////
////		int pagecount = 0;
////
////		int m = totalcount % pagesize;
////
////		if (m > 0) {
////			pagecount = totalcount / pagesize + 1;
////		} else {
////			pagecount = totalcount / pagesize;
////		}
////
////		for (int i = 1; i <= pagecount; i++) {
////
////			if (m == 0) {
////				List<Integer> subList = list.subList((i - 1) * pagesize, pagesize * (i));
////				System.out.println(subList);
////			} else {
////				if (i == pagecount) {
////					List<Integer> subList = list.subList((i - 1) * pagesize, totalcount);
////					System.out.println(subList);
////				} else {
////					List<Integer> subList = list.subList((i - 1) * pagesize, pagesize * (i));
////					System.out.println(subList);
////				}
////			}
////		}
//	}
}
