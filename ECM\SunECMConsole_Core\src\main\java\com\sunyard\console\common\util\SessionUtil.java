package com.sunyard.console.common.util;

import javax.servlet.http.HttpSession;
import java.util.Map;


public class SessionUtil {

	public static Map<String,String> getNodeInfoMap(HttpSession session){
		Object obj = session.getAttribute(Constant.SUN_NODE_INFO_MAP);
		if(obj != null && obj instanceof Map<?,?>){
			return (Map<String,String>) obj;
		}
		return null;
	}
	/**
	 * 
	 * @param session
	 * @return
	 */
	public static Map<String,String> getPermissionRightMap(HttpSession session){
		Object obj = session.getAttribute(Constant.SUN_PERMISSION_RIGHTS);
		if(obj != null && obj instanceof Map<?,?>){
			return (Map<String,String>) obj;
		}
		return null;
	}
	
	/**
	 * 从session得到所有的请求参数信息集合
	 * 返回的集合key为参数名，值为参数为对应的参数值
	 * @param session
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String,String> getRequestParameterMap(HttpSession session){
		Object obj = session.getAttribute(Constant.SUN_REQUEST_PARAMETERS);
		if(obj != null && obj instanceof Map<?,?>){
			return (Map<String,String>) obj;
		}
		return null;
	}
	
	
}
