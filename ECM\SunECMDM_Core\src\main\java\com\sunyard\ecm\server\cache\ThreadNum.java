package com.sunyard.ecm.server.cache;

/**
 * 统计socket线程数
 * <AUTHOR>
 *
 */
public class ThreadNum {
	private volatile static ThreadNum unique;

	private int threadNum = 0;
	
	private ThreadNum() {
	};

	public static ThreadNum getInstance() {
		if (unique == null) {
			synchronized (ThreadNum.class) {
				if (unique == null) {
					unique = new ThreadNum();
				}
			}
		}
		return unique;
	}
	
	public synchronized int addThreadNum(){
		 return threadNum++;
	};
	
	public synchronized void minusThreadNum(){
		threadNum--;
	}

	public int getThreadNum() {
		return threadNum;
	};
}