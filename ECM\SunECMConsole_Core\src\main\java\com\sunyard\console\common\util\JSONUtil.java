package com.sunyard.console.common.util;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * <p>Title: json格式数据生成类</p>
 * <p>Description: 用于ajax交互中 , 发送到客户端的数据转换成json格式</p>
 * <p>Copyright: Copyright (c) 2010</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class JSONUtil {
	private final static  Logger log = LoggerFactory.getLogger(JSONUtil.class);
	
	public String createJsonDataOne(String line){
		log.info( "--createJsonDataOne(start)-->line:"+line);
		JSONObject jsonResp=new JSONObject();
		if(line == null || "".equals(line)){
			log.debug( "--createJsonDataOne--> line is null");
			jsonResp.put("result", "failure");
			jsonResp.put("failure", "true");
			jsonResp.put("message", "没有查询到相应的信息");
			jsonResp.put("totalProperty","0");//总行数
			jsonResp.put("root","");//总行数
		}else {
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonResp.put("text", line);
		}
		 return jsonResp.toString(1);
	}
	/**
	 * 
	 * @param coll要显示的一页数据
	 * @param size 总数据量
	 * @return JSON String
	 */
	public String createJsonDataByColl(Collection coll,int size,Object obj){
		log.info( "--createJsonDataByColl(start)-->coll:"+coll+",totalSize="+size);
		JSONObject jsonResp=new JSONObject();
		jsonResp.put("code", 20000);//TODO mock
		if(coll == null || coll.size() == 0){
			log.debug( "--createJsonDataByColl--> coll is null or coll.size == 0");
			jsonResp.put("result", "failure");
			jsonResp.put("failure", "true");
			jsonResp.put("message", "没有查询到相应的信息");
			jsonResp.put("totalProperty","0");//总行数
			jsonResp.put("root","");//总行数

		}else{
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			Iterator iter = coll.iterator();
			JSONArray jsonArray=new JSONArray();
			JSONObject jsonObj = null;
			Map map = null;
			Set keySet = null;
			Iterator keyIter = null;
			Object key = null;
			while(iter.hasNext()){
				obj = (Object) iter.next();
				try {
					map = BeanUtils.describe(obj);
					
					if(log.isDebugEnabled()){
						log.debug( "==>>jsonStr："+map.toString());
					}
					
					keySet = map.keySet();
					keyIter = keySet.iterator();
					jsonObj = new JSONObject();
					while(keyIter.hasNext()){
						key = keyIter.next();
						jsonObj.put(key, map.get(key));
					}
					jsonArray.add(jsonObj);
				} catch (IllegalAccessException e) {
					jsonResp.put("result", "failure");
					jsonResp.put("failure", "true");
					jsonResp.put("totalProperty","0");//总行数
					jsonResp.put("root","");//总行数
					jsonResp.put("message", e.getMessage());
					log.error( e.getStackTrace()+"", e);
				} catch (InvocationTargetException e) {
					jsonResp.put("result", "failure");
					jsonResp.put("failure", "true");
					jsonResp.put("totalProperty","0");//总行数
					jsonResp.put("root","");//总行数
					jsonResp.put("message", e.getMessage());
					log.error( e.getStackTrace()+"", e);
				} catch (NoSuchMethodException e) {
					jsonResp.put("result", "failure");
					jsonResp.put("failure", "true");
					jsonResp.put("totalProperty","0");//总行数
					jsonResp.put("root","");//总行数
					jsonResp.put("message", e.getMessage());
					log.error( e.getStackTrace()+"", e);
				}
			}
			jsonResp.put("totalProperty",size+"");//总行数
			jsonResp.put("root", jsonArray);
		}
		
		if(log.isDebugEnabled()){
			log.debug("==>>"+jsonResp.toString(1));
			log.debug( "==>>"+jsonResp.toString(1));
		}
		log.debug( "--createJsonDataByColl(over)-->jsonResp:"+jsonResp.toString());
		return jsonResp.toString(1);
	}
	
	public String createJsonDataByBean(Object bean){
		JSONObject jsonResp=new JSONObject();
		
		if(bean == null){
			jsonResp.put("result", "failure");
			jsonResp.put("failure", "true");
			jsonResp.put("message", "没有查询到相关记录");
			jsonResp.put("totalProperty","0");//总行数
			jsonResp.put("root","");//总行数
		}else{
			try {
				jsonResp.put("result", "success");
				jsonResp.put("success", "true");
				Map map = BeanUtils.describe(bean);
				
				if(log.isDebugEnabled()){
					log.debug("==>>jsonStr："+map.toString());
				}
				
				Set keySet = map.keySet();
				Iterator keyIter = keySet.iterator();
				Object key = null;
				while(keyIter.hasNext()){
					key = keyIter.next();
					jsonResp.put(key, map.get(key));
				}
			} catch (IllegalAccessException e) {
				log.error("==>>"+e.getMessage());
			} catch (InvocationTargetException e) {
				log.error("==>>"+e.getMessage());
			} catch (NoSuchMethodException e) {
				log.error("==>>"+e.getMessage());
			}
		}
		return jsonResp.toString(1);
	}
	
	public String createJsonDataByMapList(List<Map<String,Object>> coll,int size){
		log.info( "--SunECMConsole-->JSONUtil-->createJsonDataByMapList-->coll:"+coll+";size:"+size);
		JSONObject jsonResp=new JSONObject();
		jsonResp.put("code", 20000);
		if(coll == null || coll.size() == 0){
			log.debug( "--SunECMConsole-->JSONUtil-->createJsonDataByMapList-->coll is null or coll.size() == 0");
			jsonResp.put("result", "failure");
			jsonResp.put("failure", true);
			jsonResp.put("message", "没有查询到相应的信息");
			jsonResp.put("totalProperty","0");//总行数
			jsonResp.put("root","");//总行数
		}else{
			jsonResp.put("result", "success");
			jsonResp.put("code", 20000);
			jsonResp.put("success", true);
			JSONArray jsonArray=new JSONArray();
			JSONObject jsonObj = null;
			for(Map<String,Object> map : coll){
				
				if(log.isDebugEnabled()){
					log.debug( "==>>jsonStr："+map.toString());
				}
				
				jsonObj = new JSONObject();
				for(String s : map.keySet()){
					jsonObj.put(s, map.get(s));
				}
				jsonArray.add(jsonObj);
			}
			jsonResp.put("totalProperty",size+"");//总行数
			jsonResp.put("root", jsonArray);
		}
		
		if(log.isDebugEnabled()){
			log.debug( "==>>"+jsonResp.toString(1));
		}
		log.debug( "--SunECMConsole-->JSONUtil-->createJsonDataByMapList-->jsonResp:"+jsonResp.toString());
		return jsonResp.toString(1);
	}
	
	public String createExtJS(Map<String,Object> map){
		log.info( "--SunECMConsole-->JSONUtil-->createExtJS-->map:"+map);
		JSONObject jsonResp=new JSONObject();
		if(log.isDebugEnabled()){
			log.debug( "==>>jsonStr："+map.toString());
		}
		for(String s : map.keySet()){
			if(map.get(s) instanceof String || map.get(s) instanceof Number || map.get(s) instanceof Boolean){
				try{
					jsonResp.put(s, map.get(s));
				}catch(Exception e){
					log.error( e.toString(), e);
				}
			}else{
				try{
					jsonResp.put(s, createItems((List<Map<String,Object>>)map.get(s)));
				}catch(Exception e){
					log.error( e.toString()+map.get(s), e);
				}
			}
		}
		log.debug( "--SunECMConsole-->JSONUtil-->createExtJS-->jsonResp:"+jsonResp.toString());
		return jsonResp.toString(1);
	}
	private JSONArray createItems(List<Map<String,Object>> l){
		JSONArray jsonArray=new JSONArray();
		JSONObject jsonResp=null;
		for(Map<String,Object> m : l){
			jsonResp=new JSONObject();
			for(String s : m.keySet()){
				if(m.get(s) instanceof String || m.get(s) instanceof Number || m.get(s) instanceof Boolean){
					jsonResp.put(s, m.get(s));
				}else{
					try{
						jsonResp.put(s, createItems((List<Map<String,Object>>)m.get(s)));
					}catch(Exception e){
						log.error("error",e);
					}
				}
			}
			jsonArray.add(jsonResp);
		}
		return jsonArray;
	}
}