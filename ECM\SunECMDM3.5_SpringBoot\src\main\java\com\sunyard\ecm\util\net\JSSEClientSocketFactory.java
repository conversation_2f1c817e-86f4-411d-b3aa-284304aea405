package com.sunyard.ecm.util.net;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.Socket;
import java.net.UnknownHostException;
import java.security.KeyStore;

import javax.net.SocketFactory;
import javax.net.ssl.KeyManager;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;

import org.apache.commons.io.IOUtils;

import com.sunyard.common.Configuration;

public class JSSEClientSocketFactory extends SocketFactory {
	private static SSLSocketFactory scf = null;

	private synchronized void setupSsl() throws IOException {
		if (scf != null) {
			return;
		}
		InputStream fis = null;
		try {
			SSLContext sc = SSLContext.getInstance("SSL");
			KeyManager[] kms = null;
			TrustManager[] tms = null;
			if (Configuration.get("ssl.client.keystore.location") != null) {
				// initialize default key manager with keystore file and pass
				KeyManagerFactory kmf = KeyManagerFactory
						.getInstance("SunX509");
				KeyStore ks = KeyStore.getInstance("JKS");
				char[] ksPass = Configuration.get(
						"ssl.client.keystore.password", "changeit")
						.toCharArray();
				String keyStoreFile = Configuration.get(
						"ssl.client.keystore.location", "keystore.jks");
				fis = JSSEClientSocketFactory.class.getResourceAsStream(keyStoreFile);
				ks.load(fis, ksPass);
				kmf.init(ks, Configuration.get(
						"ssl.client.keystore.keypassword", "changeit")
						.toCharArray());
				kms = kmf.getKeyManagers();
				fis.close();
				fis = null;
			}
			// initialize default trust manager with truststore file and
			// pass
			if (Configuration.getBoolean(
					"ssl.client.do.not.authenticate.server", false)) {
				// by pass trustmanager validation
				tms = new DummyTrustManager[] { new DummyTrustManager() };
			} else {
				TrustManagerFactory tmf = TrustManagerFactory
						.getInstance("PKIX");
				KeyStore ts = KeyStore.getInstance(Configuration.get(
						"ssl.client.truststore.type", "JKS"));
				char[] tsPass = Configuration.get(
						"ssl.client.truststore.password", "changeit")
						.toCharArray();
				fis =  JSSEClientSocketFactory.class.getResourceAsStream(Configuration.get(
						"ssl.client.truststore.location", "truststore.jks"));
				ts.load(fis, tsPass);
				tmf.init(ts);
				tms = tmf.getTrustManagers();
			}
			sc.init(kms, tms, new java.security.SecureRandom());
			scf = sc.getSocketFactory();
		} catch (Exception e) {
			IOException ioe = new IOException("Could not initialize SSLContext");
			ioe.initCause(e);
			throw ioe;
		} finally {
			IOUtils.closeQuietly(fis);
		}
	}

	@Override
	public Socket createSocket(String ip, int port) throws IOException,
			UnknownHostException {
		setupSsl();
		return scf.createSocket(ip, port);
	}

	@Override
	public Socket createSocket(InetAddress address, int port)
			throws IOException {
		setupSsl();
		return scf.createSocket(address, port);
	}

	@Override
	public Socket createSocket(InetAddress address, int port,
			InetAddress localAddress, int localPort) throws IOException,
			UnknownHostException {
		setupSsl();
		return scf.createSocket(address, port, localAddress, localPort);
	}

	@Override
	public Socket createSocket(String host, int port, InetAddress localHost,
			int localPort) throws IOException {
		setupSsl();
		return scf.createSocket(host, port, localHost, localPort);
	}

}