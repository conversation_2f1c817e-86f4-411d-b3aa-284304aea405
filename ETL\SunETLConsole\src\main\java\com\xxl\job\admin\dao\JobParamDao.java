package com.xxl.job.admin.dao;

import com.xxl.job.admin.core.model.JobParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * job info
 */
public interface JobParamDao {

	public List<JobParam> pageList(@Param("offset") int offset, @Param("pagesize") int pagesize, @Param("jobId") int jobId, @Param("param_field") String param_field);
	public int pageListCount(@Param("offset") int offset, @Param("pagesize") int pagesize, @Param("jobId") int jobId, @Param("param_field") String param_field);
	
	public int save(JobParam param);
	
	public int update(JobParam item);
	
	public int delete(@Param("id") int id);

	public Integer getMaxId();

    void deleteParam(@Param("jobId")int jobId);
}
