package com.sunyard.ecm.server.event.impl;

import java.io.FileWriter;

/**
 * 事件监听 文件对象
 * 
 * <AUTHOR>
 */
public class FileObj {
	private String ModelCode;
	private String date;
	private FileWriter out;
	private String filePath;

	public String getModelCode() {
		return ModelCode;
	}

	public void setModelCode(String modelCode) {
		ModelCode = modelCode;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public FileWriter getOut() {
		return out;
	}

	public void setOut(FileWriter out) {
		this.out = out;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

}
