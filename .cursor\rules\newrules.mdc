---
description: 
globs: 
alwaysApply: false
---
{
  "name": "中国银行科技公司运营平台重构规则(优化版)",
  "description": "优化后的老系统向数字运营平台重构规则集，提高重构效率和质量",
  "rules": [
    {
      "name": "智能模块识别与映射",
      "description": "自动识别老系统模块并映射到新平台对应模块",
      "patterns": [
        {
          "from": "老系统需要重构的项目/([A-Z]+)/src/com/sunyard/\\*\\*/\\*.java",
          "to": "后端/数字运营平台-父工程-dop/运营风险监测-{{getModuleName($1)}}-{{$1.toLowerCase()}}/src/main/java/com/sunyard/aos/{{$1.toLowerCase()}}/{{path}}",
          "transform": "autoDetectModule"
        }
      ]
    },
    {
      "name": "分层架构标准化",
      "description": "将老系统代码按标准分层架构重新组织",
      "patterns": [
        {
          "from": "**/action/**/*.java",
          "to": "{{basePath}}/controller/{{path}}",
          "priority": 1
        },
        {
          "from": "**/service/**/*.java",
          "to": "{{basePath}}/service/{{path}}",
          "priority": 1
        },
        {
          "from": "**/dao/**/*.java",
          "to": "{{basePath}}/dao/{{path}}",
          "priority": 1
        },
        {
          "from": "**/model/**/*.java",
          "to": "{{basePath}}/model/{{path}}",
          "priority": 1
        },
        {
          "from": "**/util/**/*.java",
          "to": "{{basePath}}/common/util/{{path}}",
          "priority": 1
        }
      ]
    },
    {
      "name": "Spring Boot现代化改造",
      "description": "将传统Spring应用转换为Spring Boot应用",
      "patterns": [
        {
          "from": "public class (\\w+)Controller extends BaseController",
          "to": "@RestController\n@RequestMapping(\"/api/{{getModulePath()}}/$1\")\n@Api(tags = \"{{getChineseName()}}服务\")\npublic class $1Controller",
          "priority": 2
        },
        {
          "from": "@Resource",
          "to": "@Autowired",
          "priority": 3
        },
        {
          "from": "import javax.annotation.Resource;",
          "to": "import org.springframework.beans.factory.annotation.Autowired;",
          "priority": 3
        }
      ]
    },
    {
      "name": "MyBatis Plus转换",
      "description": "将JDBC和传统MyBatis转换为MyBatis Plus",
      "patterns": [
        {
          "from": "jdbcTemplate\\.queryForList\\(([^,]*), new Object\\[\\] \\{([^\\}]*)\\}\\)",
          "to": "// 自动生成查询方法\nlambdaQuery()\n  .eq($2)\n  .list()",
          "priority": 2
        },
        {
          "from": "public class (\\w+)Dao \\{",
          "to": "@Repository\npublic interface $1Mapper extends BaseMapper<$1> {",
          "priority": 2
        }
      ]
    },
    {
      "name": "前后端分离适配",
      "description": "适配前后端分离架构",
      "patterns": [
        {
          "from": "ModelAndView",
          "to": "ResponseResult",
          "priority": 2
        },
        {
          "from": "request\\.getParameter\\(([^)]*)\\)",
          "to": "@RequestParam $1",
          "priority": 2
        }
      ]
    },
    {
      "name": "微服务基础设施适配",
      "description": "适配微服务架构",
      "patterns": [
        {
          "from": "import com.sunyard.common.util.R;",
          "to": "import com.sunyard.aos.common.core.domain.R;",
          "priority": 3
        },
        {
          "from": "R\\.error\\(([^)]*)\\)",
          "to": "R.fail($1)",
          "priority": 3
        }
      ]
    },
    {
      "name": "智能依赖管理",
      "description": "自动生成适合模块的pom.xml",
      "patterns": [
        {
          "from": "**/*.iml",
          "to": "pom.xml",
          "transform": "generatePomFile",
          "priority": 1
        }
      ]
    },
    {
      "name": "配置现代化转换",
      "description": "将传统配置转换为Spring Boot配置",
      "patterns": [
        {
          "from": "**/jdbc.properties",
          "to": "application.yml",
          "transform": "convertToYaml",
          "priority": 1
        },
        {
          "from": "**/applicationContext.xml",
          "to": "{{basePath}}/config/{{filename}}.java",
          "transform": "convertToJavaConfig",
          "priority": 1
        }
      ]
    },
    {
      "name": "前端工程化转换",
      "description": "将传统JSP转换为Vue工程",
      "patterns": [
        {
          "from": "**/*.jsp",
          "to": "src/views/{{module}}/{{path}}.vue",
          "transform": "convertToVue",
          "priority": 1
        },
        {
          "from": "<%@ include file=\"([^\"]*)\" %>",
          "to": "<import-component name=\"$1\" />",
          "priority": 2
        }
      ]
    },
    {
      "name": "API文档生成",
      "description": "自动添加Swagger注解",
      "patterns": [
        {
          "from": "@RequestMapping\\(([^)]*)\\)",
          "to": "@Operation(summary = \"{{getMethodSummary()}}\")\n@RequestMapping($1)",
          "priority": 3
        },
        {
          "from": "@RequestParam ([^)]*)\\)",
          "to": "@Parameter(description = \"{{getParamDesc()}}\")\n@RequestParam $1)",
          "priority": 3
        }
      ]
    }
  ],
  "transformers": {
    "autoDetectModule": {
      "description": "自动识别模块并映射到新架构",
      "function": "function(content, context) {\n  const moduleMap = {\n    'RCS': '风险预警',\n    'FM': '实物档案',\n    'ET': '差错管理',\n    'TRT': '实时预警',\n    'MC': '警报规则配置',\n    'SUPERVISE': '重点监督',\n    'SYSTEM': '系统管理',\n    'FILE': '电子档案',\n    'ModelRun': '模型运行',\n    'AI': '音视频管理'\n  };\n  \n  return {\n    moduleName: moduleMap[context.captures[0]] || context.captures[0],\n    modulePath: context.captures[0].toLowerCase()\n  };\n}"
    },
    "generatePomFile": {
      "description": "根据模块特性生成pom.xml",
      "function": "function(content, context) {\n  const moduleType = detectModuleType(context.filePath);\n  const dependencies = getModuleDependencies(moduleType);\n  \n  return generatePomTemplate({\n    moduleName: context.moduleName,\n    artifactId: `SunARS-${context.modulePath}`,\n    dependencies: dependencies\n  });\n}"
    },
    "convertToYaml": {
      "description": "将properties转换为YAML格式",
      "function": "function(content) {\n  return convertPropertiesToYaml(content);\n}"
    },
    "convertToJavaConfig": {
      "description": "将XML配置转换为Java Config",
      "function": "function(content, context) {\n  return convertXmlConfigToJava(content, context.filename);\n}"
    },
    "convertToVue": {
      "description": "将JSP转换为Vue组件",
      "function": "function(content, context) {\n  return convertJspToVue(content, {\n    module: context.modulePath,\n    path: context.path\n  });\n}"
    }
  },
  "config": {
    "priorityStrategy": "REPLACE_HIGHER",
    "fileProcessingOrder": ["pom.xml", "*.java", "*.xml", "*.properties", "*.jsp"],
    "moduleDetectionRules": {
      "pathPatterns": ["**/RCS/**=>risk", "**/FM/**=>fm"],
      "contentPatterns": ["package com.sunyard.fm=>fm"]
    },
    "defaultPackageStructure": {
      "controller": "controller",
      "service": "service",
      "dao": "dao",
      "model": "model",
      "config": "config"
    }
  }

}