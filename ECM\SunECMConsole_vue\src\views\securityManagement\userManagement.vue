<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.user_id"
        placeholder="用户ID"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.user_name"
        placeholder="用户名称"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-select v-model="listQuery.user_state" placeholder="请选择用户状态">
        <el-option
          v-for="item in userStates"
          :key="item.key"
          :label="item.label"
          :value="item.key"
        >
        </el-option>
      </el-select>
      <el-select v-model="listQuery.role_id" placeholder="请选择用户角色">
        <el-option
          v-for="item in userRoles"
          :key="item.ROLE_ID"
          :label="item.ROLE_NAME"
          :value="item.ROLE_ID"
        >
        </el-option>
      </el-select>
      <el-button
        v-if="this.hasPerm('userSearch')"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini" plain round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
      <el-button
        v-if="this.hasPerm('addUser')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        新增用户
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column label="用户密码" v-if="false" min-width="10%">
        <template slot-scope="{ row }">
          <span>{{ row.user_password }}</span>
        </template>
      </el-table-column>

      <el-table-column label="用户ID" align="center" min-width="10%">
        <template slot-scope="{ row }">
          <span>{{ row.login_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="用户名称" align="center" min-width="10%">
        <template slot-scope="{ row }">
          <span>{{ row.user_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="职位" align="center" min-width="10%">
        <template slot-scope="{ row }">
          <span>{{ row.user_post }}</span>
        </template>
      </el-table-column>

      <el-table-column label="所属部门" align="center" min-width="10%">
        <template slot-scope="{ row }">
          <span>{{ row.user_department }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" min-width="10%">
        <template slot-scope="{ row }">
          <span v-if="row.user_state == '1'">启用</span>
          <span v-if="row.user_state == '0'">禁用</span>
        </template>
      </el-table-column>

      <el-table-column label="Idap代码" align="center" min-width="10%">
        <template slot-scope="{ row }">
          <span>{{ row.ldap_code }}</span>
        </template>
      </el-table-column>

      <el-table-column label="所在城市" align="center" min-width="10%">
        <template slot-scope="{ row }">
          <span>{{ row.user_city }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="left"
        class-name="small-padding fixed-width"
        width="250"
      >
        <template slot-scope="{ row, $index }">
          <el-button
            v-if="hasPerm('modifyUser')"
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            style="margin-bottom: 5px; margin-left: 10px"
          >
            修改用户
          </el-button>
          <el-button
            v-if="row.user_state == '0' && hasPerm('ableUser')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="handleStart(row, $index)"
            style="margin-bottom: 5px"
          >
            启用
          </el-button>

          <el-button
            v-if="row.user_state == '1' && hasPerm('disableUser')"
            size="mini"
            type="danger"
            icon="el-icon-turn-off"
            @click="handleStop(row, $index)"
            style="margin-bottom: 5px"
          >
            禁用
          </el-button>

          <el-button
            v-if="hasPerm('resetPwd')"
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="resetPwd(row, $index)"
            style="margin-bottom: 5px"
          >
            重置密码
          </el-button>
          <el-button
            v-if="hasPerm('bindRole')"
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="bindRole(row, $index)"
            style="margin-bottom: 5px"
          >
            绑定角色
          </el-button>
          <el-button
            v-if="hasPerm('userPermission')"
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="grantedPer(row, $index)"
            style="margin-bottom: 5px"
          >
            授予权限
          </el-button>
          <el-button
            v-if="hasPerm('bindRole')"
            size="mini"
            type="primary"
            icon="el-icon-edit"
            @click="releaseMC(row, $index)"
            style="margin-bottom: 5px"
          >
            分配内容对象操作权限
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="用户ID" prop="user_id">
          <el-input
            v-model="temp.user_id"
            maxlength="20"
            :disabled="dialogStatus == 'update'"
          />
        </el-form-item>
        <el-form-item label="用户名称" prop="user_name">
          <el-input v-model="temp.user_name" maxlength="32" />
        </el-form-item>
        <el-form-item label="登陆密码" prop="user_password">
          <el-input
            v-model="temp.user_password"
            :disabled="dialogStatus == 'update'"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="user_password2">
          <el-input
            v-model="temp.user_password2"
            :disabled="dialogStatus == 'update'"
          />
        </el-form-item>
        <el-form-item label="职位" prop="user_post">
          <el-input v-model="temp.user_post" />
        </el-form-item>
        <el-form-item label="所在城市" prop="user_city">
          <el-input v-model="temp.user_city" />
        </el-form-item>
        <el-form-item label="部门" prop="user_department">
          <el-input v-model="temp.user_department" />
        </el-form-item>
        <el-form-item label="Idap代码" prop="ldap_code">
          <el-input v-model="temp.ldap_code" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary" size="mini"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textPwdMap[resetPwd]" :visible.sync="dialogPwdVisible">
      <el-form
        ref="pwdForm"
        :rules="rules2"
        :model="pwd"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
     <el-form-item label="请输入新密码" prop="user_password">
          <el-input v-model="pwd.user_password" show-password/>
        </el-form-item>
        <el-form-item label="确认密码" prop="user_password2">
          <el-input v-model="pwd.user_password2" show-password/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogPwdVisible = false"> 取消 </el-button>
        <el-button size="mini" type="primary" @click="postResetPwd()"> 提交 </el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="titleper2" :visible.sync="MCDialogVisible">
      <div class="app-container">
        <el-table
          :key="tableKey2"
          v-loading="mclistLoading"
          :data="mclist"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="内容对象" v-if="false">
            <template slot-scope="{ row }">
              <span>{{ row.model_code }}</span>
            </template>
          </el-table-column>
          <el-table-column label="内容对象名称" min-width="35%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.model_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="权限" min-width="35%" align="center">
            <template slot-scope="{ row }">
              <span v-if="row.permission_code.charAt(0) == '1'"> 上传</span>
              <span v-if="row.permission_code.charAt(1) == '1'"> 删除</span>
              <span v-if="row.permission_code.charAt(2) == '1'"> 更新</span>
              <span v-if="row.permission_code.charAt(3) == '1'"> 查询</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            min-width="30%"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="{ row }">
              <el-button
                type="primary"
                size="mini"
                @click="configPermission(row)"
              >
                配置权限
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="mclistQuery.page"
        :limit.sync="mclistQuery.limit"
        @pagination="getMCList"
      />

      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="MCDialogVisible = false"> 取消 </el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="titleper" :visible.sync="dialogPermissionVisible">
      <el-card class="box-card" style="margin-top: 15px">
              <div class="text item">

            <input type="checkbox" v-model="checked" @click="checkedAll" />全选
            <div style="margin: 15px 0"></div>

            <div
              v-for="item in data1"
              :key="item"
              style="float: left; margin-right: 10px"
            >

              <input
                type="checkbox"
                v-model="arr"
                :value="item.id"
                @click="fn2()"
              />{{ item.value }}
            </div>

          <br />
          <div style="margin: 15px 0"></div>
          <div slot="footer" class="dialog-footer" align="right">
            <el-button size="mini" @click="dialogPermissionVisible = false">
              取消
            </el-button>
            <el-button size="mini" type="primary" @click="postPermission()">
              提交
            </el-button>
          </div>
        </div>
      </el-card>
    </el-dialog>

    <userPerRel ref="user_per_ref" :isUser="true" :objMsg="this.per">
    </userPerRel>

    <userRoleRel ref="user_role_ref" :objMsg="this.per">
    </userRoleRel>
  </div>
</template>

<script>
import {
  userInfoSearch,
  addUserInfo,
  updateUserInfo,
  stopUserState,
  startUserState,
  resetUserPwd,
  getUserCmodel,
  configUserConfer,
} from "@/api/userManage";
import { roleInfoQuery } from "@/api/roleManage";
import userPerRel from "./dialog/user_per_rel.vue";
import userRoleRel from "./dialog/user_role_rel.vue";
import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination

import global from "../../store/global.js";

export default {
  // name: 'ComplexTable',
  components: { Pagination, userPerRel, userRoleRel},
  directives: { waves,elDragDialog },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
  },

  data() {
    return {
      titleper: "配置权限",
      titleper2: "分配内容对象操作权限",
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        user_id: "",
        user_name: "",
        user_state: "",
        role_id: "",
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+user_name",
      },
      tableKey2: 0,
      mclistLoading: true,
      mclist: null,
      mclisttotal: 0,
      mclistQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+model_name",
      },

      data1: [
        {
          id: "1",
          value: "上传",
        },
        {
          id: "2",
          value: "删除",
        },
        {
          id: "3",
          value: "更新",
        },
        {
          id: "4",
          value: "查询",
        },
      ],
      arr: [],
      checked: false,

      importanceOptions: [1, 2, 3],
      sortOptions: [
        { label: "ID Ascending", key: "+user_name" },
        { label: "ID Descending", key: "-user_name" },
      ],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
      },
      per: {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
      },
      mc: {
        id: undefined,
        importance: 1,
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
      },
      pwd: {
        id: undefined,
        importance: 1,
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
      },
      conferdata: {
        user_id: "",
        model_code: "",
        permission_code: "",
      },
      userStates: [
        { key: "1", label: "启用" },
        { key: "0", label: "禁用" },
      ],
      userRoles: [],
      dialogFormVisible: false,
      dialogPwdVisible: false,
      MCDialogVisible: false,
      dialogPermissionVisible: false,
      dialogStatus: "",
      textMap: {
        update: "修改用户",
        create: "新增用户",
      },
      textPwdMap: {
        resetPwd: "重置密码",
      },
      rules: {
        user_id: [
          {
            required: true,
            pattern: /^\w{1,20}$/,
            message: "用户ID必输",
            trigger: "blur",
          },
        ],
        user_name: [
          {
            required: true,
            pattern: /^[\w\u4E00-\u9FA5]*$/,
            message: "用户名称必输",
            trigger: "blur",
          },
        ],
        user_password: [
          { required: true, message: "登陆密码必输", trigger: "blur" },
        ],
        user_password2: [
          { required: true, message: "确认密码必输", trigger: "blur" },
        ],
      },
      rules2: {
        user_password: [
          { required: true, message:global.regexPasswordText,pattern:global.regexPasswordNeed2, trigger: "blur" },
        ],
        user_password2: [
          { required: true, message: "确认密码必输且与新密码一致", trigger: "blur" },
        ],
      },
      downloadLoading: false,
    };
  },

  created() {
    this.getList(), this.getUserRoles();
  },

  watch: {
    arr: {
      handler: function (val, oldVal) {
        if (this.arr.length === this.data1.length) {
          this.checked = true;
        } else {
          this.checked = false;
        }
      },
      deep: true,
    },
  },

  methods: {
    getList() {
      this.listLoading = true;
      userInfoSearch(this.listQuery).then((response) => {
        this.list = response.root;
        this.total = Number(response.totalProperty);
        // Just to simulate the time of the request
        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },
    getUserRoles() {
      roleInfoQuery().then((response) => {
        this.userRoles = response.root;
      });
    },
    getMCList() {
      this.mclistLoading = true;
      this.mclistQuery.user_id = this.mc.user_id;
      getUserCmodel(this.mclistQuery).then((response) => {
        this.mclist = response.root;
        this.total = Number(response.totalProperty);
        for (let item of this.mclist)
          if (
            item.permission_code == undefined ||
            item.permission_code == null ||
            item.permission_code == ""
          ) {
            item.permission_code = "000011";
          }
        setTimeout(() => {
          this.mclistLoading = false;
        }, 1 * 100);
      });
    },
    handleclear() {
      this.listQuery.user_id = "";
      this.listQuery.user_name = "";
      this.listQuery.user_state = "";
      this.listQuery.role_id = "";
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    sortChange(data) {
      const { prop, order } = data;
      alert(prop);
      if (prop === "user_name") {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === "ascending") {
        this.listQuery.sort = "+user_name";
      } else {
        this.listQuery.sort = "-user_name";
      }
      this.handleFilter();
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
      };
    },
    resetPwdText() {
      this.pwd = {
        id: undefined,
        importance: 1,
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
      };
    },
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    createData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.temp.id = parseInt(Math.random() * 100) + 1024; // mock a id
          this.temp.author = "vue-element-admin";
          addUserInfo(this.temp).then(() => {
            // this.list.unshift(this.temp)
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Created Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row); // copy obj
      this.temp.user_id = this.temp.login_id;
      this.temp.user_password = "invisible";
      this.temp.user_password2 = "invisible";
      this.temp.timestamp = new Date(this.temp.timestamp);
      this.dialogStatus = "update";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    updateData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp);
          tempData.timestamp = +new Date(tempData.timestamp);
          updateUserInfo(tempData).then(() => {
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Update Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },

    handleStart(row, index) {
      startUserState(row).then((response) => {
        this.getList();
      });
    },

    handleStop(row, index) {
      stopUserState(row).then((response) => {
        this.getList();
      });
    },

    resetPwd(row, index) {
      this.resetPwdText();
      this.dialogPwdVisible = true;
      this.pwd.user_id = row.login_id;
      this.$nextTick(() => {
        this.$refs["pwdForm"].clearValidate();
      });
    },

    postResetPwd() {
      this.$refs["pwdForm"].validate((valid) => {
        if (valid) {
          this.pwd.id = parseInt(Math.random() * 100) + 1024; // mock a id
          this.pwd.author = "vue-element-admin";
          if(this.pwd.user_password!=this.pwd.user_password2){
             alert("两次输入的密码不一样");
             return;          
          }
          resetUserPwd(this.pwd).then(() => {
            this.getList();
            this.dialogPwdVisible = false;
            this.$notify({
              title: "Success",
              message: "Reset Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },
    grantedPer(row) {
      this.per = Object.assign({}, row);
      this.$refs.user_per_ref.getAllPersTreeById(this.per.login_id);
      this.$refs.user_per_ref.show();
    },
    bindRole(row) {
      this.per = Object.assign({}, row);
      this.$refs.user_role_ref.getAllRolesTreeById(this.per.login_id);
      this.$refs.user_role_ref.show();
    },
    releaseMC(row) {
      this.temp = Object.assign({}, row);
      this.mc.user_id = this.temp.login_id;
      this.getMCList();
      this.resetMCTemp();
      this.MCDialogVisible = true;
    },

    resetMCTemp() {
      this.mc = {
        id: undefined,
        importance: 1,
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
        user_id: "",
      };
    },

    configPermission(row) {
      this.dialogPermissionVisible = true;
      this.arr = [];
      for(let i=0;i<4;i++){
        if(row.permission_code.charAt(i) == '1'){
          this.arr.push(this.data1[i].id)
        }
      }
      this.mc.model_code = row.model_code;
   
    },
    checkedAll() {
      if (this.checked) {
        //实现反选
        this.arr = [];
      } else {
        //实现全选
        this.arr = [];
        this.data1.forEach((item) => {
          this.arr.push(item.id);
        });
      }
    },

    postPermission() {
      let permission_code = "";
      if (this.arr.indexOf("1") == "-1") {
        permission_code += "0";
      } else {
        permission_code += "1";
      }
      if (this.arr.indexOf("2") == "-1") {
        permission_code += "0";
      } else {
        permission_code += "1";
      }
      if (this.arr.indexOf("3") == "-1") {
        permission_code += "0";
      } else {
        permission_code += "1";
      }
      if (this.arr.indexOf("4") == "-1") {
        permission_code += "0";
      } else {
        permission_code += "1";
      }
      permission_code += "11";
      this.conferdata.user_id = this.mclistQuery.user_id;
      this.mc.user_id = this.mclistQuery.user_id;
      this.conferdata.model_code = this.mc.model_code;
      this.conferdata.permission_code = permission_code;
      configUserConfer(this.conferdata).then(() => {
        this.getMCList();
        this.dialogPermissionVisible = false;
        this.$notify({
          title: "Success",
          message: "Config Successfully",
          type: "success",
          duration: 2000,
        });
      });
    },

    formatJson(filterVal) {
      return this.list.map((v) =>
        filterVal.map((j) => {
          if (j === "timestamp") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },

    getSortClass: function (key) {
      const sort = this.listQuery.sort;
      return sort === `+${key}` ? "ascending" : "descending";
    },
  },
};
</script>
