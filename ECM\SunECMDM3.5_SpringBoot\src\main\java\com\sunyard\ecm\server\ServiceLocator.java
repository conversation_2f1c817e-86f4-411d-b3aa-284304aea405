package com.sunyard.ecm.server;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * 本地配置的spring
 * <AUTHOR>
 *
 */
@Component
public class ServiceLocator implements BeanFactoryAware {
	private static BeanFactory beanFactory = null;
	private static ServiceLocator serviceLocator = null;

	public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
		ServiceLocator.beanFactory = beanFactory;
	}
	@Bean
	@Scope(value = "singleton")
	public static ServiceLocator getInstance() {
		if (serviceLocator == null) {
			serviceLocator = (ServiceLocator) beanFactory
					.getBean("serviceLocator");
		}
		return serviceLocator;
	}

	/**
	 * 根据BEAN名称得到相应的服务类
	 */
	public static Object getServiceClass(String bean) {
		return beanFactory.getBean(bean);
	}

	/**
	 * 根据指定名称得到对应的指定类型的服务类
	 */
	public static Object getServiceClass(String bean, Class clazz) {
		return beanFactory.getBean(bean, clazz);
	}
}