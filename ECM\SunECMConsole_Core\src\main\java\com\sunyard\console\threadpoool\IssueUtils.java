package com.sunyard.console.threadpoool;

import com.sunyard.console.common.util.SpringUtil;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.conParamManage.bean.ConParamInfoBean;
import com.sunyard.console.conParamManage.dao.ConParamManageDAO;
import com.sunyard.console.contentmodelmanage.bean.ContentObjectInfoBean;
import com.sunyard.console.contentmodelmanage.dao.ContentObjectManageDao;
import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.contentservermanage.dao.ContentServerGroupManageDAO;
import com.sunyard.console.contentservermanage.dao.ContentServerManageDAO;
import com.sunyard.console.lifemanage.bean.ScheduleInfoBean;
import com.sunyard.console.lifemanage.dao.CachingStrategyManageDAO;
import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerInfoBean;
import com.sunyard.console.unityaccessservermanage.dao.UnityAccessServerManageDAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Service("iss")
public class IssueUtils {
	private static Logger log = LoggerFactory.getLogger(IssueUtils.class);
	/**
	 * 下发线程同步变量
	 */
	public static final Lock lock = new ReentrantLock();
	public static final Condition condition = lock.newCondition();
	private static ContentServerManageDAO contentServerManageDao = (ContentServerManageDAO) SpringUtil.ctx
			.getBean("contentServerManageDao");
	private static CachingStrategyManageDAO cachingStrategyManageDao = (CachingStrategyManageDAO) SpringUtil.ctx
			.getBean("cachingStrategyManageDao");
	// 从spring获取contentObjectManageDao类 注入
	private static ContentObjectManageDao contentObjectManageDao = (ContentObjectManageDao) SpringUtil.ctx
			.getBean("contentObjectManageDao");
	private static ConParamManageDAO conParamManageDao = (ConParamManageDAO) SpringUtil.ctx
			.getBean("conParamManageDao");
	// 统一接入实现类
	private static UnityAccessServerManageDAO unityAccessServerManageDao = (UnityAccessServerManageDAO) SpringUtil.ctx
			.getBean("unityAccessServerManageDao");
	private static ContentServerGroupManageDAO contentServerGroupManageDao = (ContentServerGroupManageDAO) SpringUtil.ctx
			.getBean("contentServerGroupManageDao");

	/**
	 * 获取传输协议port
	 * 
	 * @param server
	 * @param isgroup
	 *            如果是组则传true，server传flase
	 * @return
	 */
	public static Map<String, String> getContentServerTransPort(ContentServerInfoBean server, boolean isgroup) {
		String sendPort;
		String type = "http";
		Map<String, String> map = new HashMap<String, String>();
		if (isgroup) {
			if ("https".equals(server.getGtrans_protocol())) {
				sendPort = Integer.toString(server.getG_https_port());
				type = "https";
			} else {
				sendPort = Integer.toString(server.getG_http_port());
			}
		} else {
			if ("https".equals(server.getTrans_protocol())) {
				sendPort = Integer.toString(server.getHttps_port());
				type = "https";
			} else {
				sendPort = Integer.toString(server.getHttp_port());
			}
		}
		map.put("sendPort", sendPort);
		map.put("type", type);
		return map;
	}

	public static String assmbleIpAndPort(Map<String, String> map, String ip) {
		String port = map.get("sendPort");
		return ip + ":" + port;
	}

	public static String createURL(String IPandPort, String type, boolean isdm) {
		String str;
		if (isdm) {
			str = StringUtil.assemblyDMUrl(IPandPort, type);
		} else {
			str = StringUtil.assemblyUAUrl(IPandPort, type);
		}
		return str;
	}

	/**
	 * 获取UA传输协议port
	 * 
	 * @param server
	 * @return
	 */
	public static Map<String, String> getUnityAccessTransPort(UnityAccessServerInfoBean server) {
		String sendPort;
		String type = "http";
		Map<String, String> map = new HashMap<String, String>();
		if ("https".equals(server.getTrans_protocol())) {
			sendPort = Integer.toString(server.getHttps_port());
			type = "https";
		} else {
			sendPort = Integer.toString(server.getHttp_port());
		}
		map.put("sendPort", sendPort);
		map.put("type", type);
		return map;
	}

	/**
	 * 下发模型相关信息
	 * 
	 * @param modelCode
	 */

	public static void IssueContentModelInfo(String modelCode) {
		List<UnityAccessServerInfoBean> unityServerList = getAliveUA();
		List<ContentServerInfoBean> contentServerList = getAliveByModelCodeDM(modelCode);
		IssueInfoToDM(contentServerList);
		IssueInfoToUA(unityServerList);
	}
	
	/**
	 * 下发配置参数相关信息
	 * 
	 * @param modelCode
	 */

	public static void IssueParamInfo(ConParamInfoBean conParamBean) {
		List<UnityAccessServerInfoBean> unityServerList = getAliveUA();
		Set<Integer> set=new HashSet<Integer>();
		if(conParamBean.getPar_all() == 1)//全局变量
		{
			List<ContentServerInfoBean> contentServerList = conParamManageDao.getAllContentServerList(true);
			IssueInfoToDM(contentServerList);
		}else if(conParamBean.getPar_group() != 0){//作用于服务器组
			List<ContentServerInfoBean> contentServerList = contentServerGroupManageDao.getRelContentServerList(String.valueOf(conParamBean.getPar_group()),true);
			if(conParamBean.getPar_server() != 0){//作用于服务器
				set.clear();
				set.add(conParamBean.getPar_server());
				IssueInfoToDM(getDMServerInfoByDmServerIds(set));
			}else{
				IssueInfoToDM(contentServerList);
			}
		}
		IssueInfoToUA(unityServerList);
	}

	/**
	 * 根据serverId集合下发服务器组信息
	 * 
	 * @param set
	 */
	public static void IssueContentServerGroupInfo(Set<Integer> set) {
		IssueInfoToDM(getDMServerInfoByDmServerIds(set));
		IssueInfoToUA(getAliveUA());
	}

	/**
	 * 下发信息给UA
	 * 
	 * @param unityServerList
	 *            要下发的UA列表
	 */
	public static void IssueInfoToUA(List<UnityAccessServerInfoBean> unityServerList) {
		if (unityServerList == null) {
			log.warn("没有待下发的UA");
			return;
		}
		if (unityServerList.isEmpty()) {
			log.warn("没有待下发的UA");
			return;
		}
		log.info("将要下发UA信息加入待下发列表");
		lock.lock();
		if (unityServerList != null) {
			for (UnityAccessServerInfoBean server : unityServerList) {
				Map<String, String> map = getUnityAccessTransPort(server);
				String url = createURL(assmbleIpAndPort(map, server.getServer_ip()), map.get("type"), false);
				ConsoleThreadPool.addIssueInfoToSet(url);
			}
		}
		condition.signalAll();
		lock.unlock();
		log.info("UA信息加入结束结束");
	}

	/**
	 * 下发信息给DM
	 * 
	 * @param serverList
	 *            要下发的DM列表
	 */
	public static void IssueInfoToDM(List<ContentServerInfoBean> serverList) {
		if (serverList == null) {
			log.warn("没有待下发的DM");
			return;
		}
		if (serverList.isEmpty()) {
			log.warn("没有待下发的DM");
			return;
		}
		log.info("将要下发DM信息加入待下发列表");
		lock.lock();
		for (ContentServerInfoBean server : serverList) {
			Map<String, String> map;
			String sendIp;
			if (server.getDeploy_mode() == 2) {// 2为其他集群方式（NAT），则取服务器组的ip和服务器的http_port
				log.debug("2为其他集群方式（NAT），下发信息到服务器组");
				sendIp = server.getGroup_ip();
				map = IssueUtils.getContentServerTransPort(server, true);
			} else {
				sendIp = server.getServer_ip();
				map = IssueUtils.getContentServerTransPort(server, false);
			}
			ConsoleThreadPool.addIssueInfoToSet(
					IssueUtils.createURL(IssueUtils.assmbleIpAndPort(map, sendIp), map.get("type"), true));
		}
		condition.signalAll();
		lock.unlock();
		log.info("DM信息加入结束结束");
	}

	/**
	 * 获取serverId集合对应的dm信息
	 * 
	 * @param set
	 * @return
	 */
	public static List<ContentServerInfoBean> getDMServerInfoByDmServerIds(Set<Integer> set) {
		log.debug("根据dm的id列表获取dm服务器信息,set:{}", set);
		List<ContentServerInfoBean> contentServerInfoList = new ArrayList<ContentServerInfoBean>();
		for (int serverId : set) {
			List<ContentServerInfoBean> tmpContentServerInfoList = contentServerManageDao.getContentServerList(serverId,
					null);
			contentServerInfoList.addAll(tmpContentServerInfoList);
		}
		return contentServerInfoList;
	}

	/**
	 * 获取serverId集合对应的ua信息
	 * 
	 * @param set
	 * @return
	 */
	public static List<UnityAccessServerInfoBean> getUaServerInfoByUaServerIds(Set<Integer> set) {
		log.debug("根据ua的id列表获取ua服务器信息,set:{}", set);
		List<UnityAccessServerInfoBean> list = new ArrayList<UnityAccessServerInfoBean>();
		for (int id : set) {
			List<UnityAccessServerInfoBean> myServerList = unityAccessServerManageDao.getUnityAccessServerList(id,
					false);
			list.addAll(myServerList);
		}
		return list;
	}

	/**
	 * 获取统一接入服务器列表，并包含serverId所在的服务器信息
	 * 
	 * @return
	 */
	public static List<UnityAccessServerInfoBean> getAliveUA() {
		// 获取所有启用的统一接入服务器列表
		List<UnityAccessServerInfoBean> unityServerList = unityAccessServerManageDao.getUnityAccessServerList(0, true);
		if (unityServerList == null) {
			return new ArrayList<UnityAccessServerInfoBean>();
		}
		return unityServerList;
	}

	/**
	 * 获取与该内容对象相关联的所有服务器信息列表
	 * 
	 * @param modelCode
	 * @return
	 */
	public static List<ContentServerInfoBean> getAliveByModelCodeDM(String modelCode) {
		log.debug("获取与内容对象{}相关联的所有服务器信息列表", modelCode);
		List<ContentServerInfoBean> contentServerList = new ArrayList<ContentServerInfoBean>();
		ContentObjectInfoBean contentObjectInfoBean = contentObjectManageDao.getContentObjectByCode(modelCode);
		String fatherModelCode = modelCode;
		if (contentObjectInfoBean.getModel_type() == 1) {
			fatherModelCode = contentObjectManageDao.getFatherContentObj(modelCode);
			if (fatherModelCode == null)
				return contentServerList;
		} // 查询文档对象和索引对象一起关联的启用服务器列表
		contentServerList = contentObjectManageDao.getContentServerListByModelCode(fatherModelCode, true);
		if (contentServerList == null) {
			return contentServerList;
		}
		return contentServerList;

	}

	/**
	 * 获取所有启用的DM信息
	 * 
	 * @return
	 */
	public static List<ContentServerInfoBean> getAllAliveDMInfo() {

		List<ContentServerInfoBean> list = contentServerManageDao.getStartNodeList(0);
		if (list == null)
			return new ArrayList<ContentServerInfoBean>();
		return list;
	}

	public static List<ContentServerInfoBean> getServerListbyTaskId(int taskId) {
		ScheduleInfoBean scheduleBean = cachingStrategyManageDao.getCachingStrategy(taskId);
		List<ContentServerInfoBean> serverList = contentServerManageDao
				.getFamilyContentServerList(scheduleBean.getGroup_id(), true);
		if (serverList == null)
			return new ArrayList<ContentServerInfoBean>();
		return serverList;
	}

	public static List<ContentServerInfoBean> getServerListByGroupId(String groupId) {
		List<ContentServerInfoBean> contentServerList = contentServerGroupManageDao.getRelContentServerList(groupId,
				true);
		if (contentServerList == null)
			return new ArrayList<ContentServerInfoBean>();
		return contentServerList;
	}
	/**
	 * 下发模型相关信息
	 * @param modelCodes
	 */
	public static void IssueContentModelsInfo(String[] modelCodes) {
		List<ContentServerInfoBean> contentServerList = new ArrayList<ContentServerInfoBean>();
		for (String modelCode : modelCodes) {
			List<ContentServerInfoBean> tmpContentServerList = getAliveByModelCodeDM(modelCode);
			contentServerList.addAll(tmpContentServerList);
		}
		IssueInfoToDM(contentServerList);
		List<UnityAccessServerInfoBean> unityServerList = getAliveUA();
		IssueInfoToUA(unityServerList);

	}
}
