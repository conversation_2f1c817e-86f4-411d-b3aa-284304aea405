package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;

import java.util.List;

@XStreamAlias("SearchResultBean")
public class SearchResultBean {
	/** 索引对象信息 **/
	private List<BatchIndexBean> batchIndex;
	
	/** 文档对象信息 **/
	private List<BatchFileBean> batchFile;

	public List<BatchIndexBean> getBatchIndex() {
		return batchIndex;
	}

	public void setBatchIndex(List<BatchIndexBean> batchIndex) {
		this.batchIndex = batchIndex;
	}

	public List<BatchFileBean> getBatchFile() {
		return batchFile;
	}

	public void setBatchFile(List<BatchFileBean> batchFile) {
		this.batchFile = batchFile;
	}
}