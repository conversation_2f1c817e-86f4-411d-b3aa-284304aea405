package com.sunyard.ecm.server.cache.wsserver;

import java.util.ArrayList;
import java.util.List;


public class configRegist {
	/** 内容模型模版 */
	public final static int modelDoc = 0;
	/** 内容存储服务器 */
	public final static int contentServer = 1;
	/** 内容能够模型表信息 */
	public final static int metaTable = 2;
	/** 存储对象 */
	public final static int storeObject = 3;
	/** 内容模型列表信息 */
	public final static int allModelMsg = 4;
	/** 生命周期 */
	public final static int scheduler = 5;
	/** 内容服务器组和内容对象对应卷 */
	public final static int sgroupmodleSet = 6;
	/** 日志策略 */
	public final static int logRule = 7;
	/**内容存储服务的启用和禁用*/
	public final static int conentServerStat = 8;
	// 配置信息改变绑定列表
	private List<configChangeInterface> list =new ArrayList<configChangeInterface>();
	public volatile static configRegist regist =new configRegist();
	private configRegist(){
		
	}
	public static configRegist getInstance(){
		if (regist == null){
			regist = new configRegist();
		}
		return regist;
	}
	/** 配置信息时间绑定 */
	public String bind(int i, String xml) {
		switch (i) {
		case configRegist.allModelMsg:
			allModelMsgChange(xml);
			break;
		case configRegist.contentServer:
			contentServerChange(xml);
			break;
		case configRegist.logRule:
			logRuleChange(xml);
			break;
		case configRegist.metaTable:
			metaTableChange(xml);
			break;
		case configRegist.modelDoc:
			modelDocChange(xml);
			break;
		case configRegist.scheduler:
			schedulerChange(xml);
			break;
		case configRegist.sgroupmodleSet:
			sroupmodleSetChange(xml);
			break;
		case configRegist.storeObject:
			storeObjectChange(xml);
			break;
		case configRegist.conentServerStat:
			contentServerstatChange( xml);
			break;
		default:
			break;
		}
		return "configManager  seccuss";
	}
	/**内容存储服务器启用活着禁用*/
	 private void contentServerstatChange(String xml){
			for (configChangeInterface configChange : list) {
				configChange.contentServerstatChange(xml);
			}
	 }
	/** 存储对象改变调用所有注册的方法 */
	private void storeObjectChange(String xml) {
		for (configChangeInterface configChange : list) {
			configChange.storeObjectChange(xml);
		}
	}

	/** 内容服务器组和内容对象关系改变调用所有注册的方法 */
	private void sroupmodleSetChange(String xml) {
		for (configChangeInterface configChange : list) {
			configChange.sroupmodleSetChange(xml);
		}
	}

	/** 生命周期改变调用所有注册的方法 */
	private void schedulerChange(String xml) {
		for (configChangeInterface configChange : list) {
			configChange.schedulerChange(xml);
		}
	}

	/** 生命周期改变调用所有注册的方法 */
	private void modelDocChange(String xml) {
		for (configChangeInterface configChange : list) {
			configChange.modelDocChange(xml);
		}
	}

	/** 内容模型表改变调用所有注册的方法 */
	private void metaTableChange(String xml) {
		for (configChangeInterface configChange : list) {
			configChange.metaTableChange(xml);
		}
	}

	/** 日志策略改变调用所有注册的方法 */
	private void logRuleChange(String xml) {
		for (configChangeInterface configChange : list) {
			configChange.logRuleChange(xml);
		}
	}

	/** 内容存储服务器服务器信息改变调用所有注册的方法 */
	private void contentServerChange(String xml) {
		for (configChangeInterface configChange : list) {
			configChange.contentServerChange(xml);
		}
	}

	/** 内容模型列表改变调用所有注册的方法 */
	private void allModelMsgChange(String xml) {
		for (configChangeInterface configChange : list) {
			configChange.allModelMsgChange(xml);
		}
	}

	public List<configChangeInterface> getList() {
		return list;
	}

	public void setList(List<configChangeInterface> list) {
		this.list = list;
	}
	public void add(configChangeInterface intreface){
		list.add(intreface);
	}
}