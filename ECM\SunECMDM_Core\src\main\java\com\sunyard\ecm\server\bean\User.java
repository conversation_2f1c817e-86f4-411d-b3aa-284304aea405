package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

@XStreamAlias("User")
public class User {
	@XStreamAsAttribute
	private String USER_NAME;
	@XStreamAsAttribute
	private String PASSWORD;

	public String getUserName() {
		return USER_NAME;
	}

	public void setUserName(String userName) {
		this.USER_NAME = userName;
	}

	public String getPassword() {
		return PASSWORD;
	}

	public void setPassword(String password) {
		this.PASSWORD = password;
	}
	
}