package com.sunyard.ecm.server.cache.wsserver;


public interface configChangeInterface {
	public static configRegist regise = configRegist.getInstance();

	public abstract void add();

	public void storeObjectChange(String xml);

	public void sroupmodleSetChange(String xml);

	public void schedulerChange(String xml);

	public void modelDocChange(String xml);

	public void metaTableChange(String xml);

	public void logRuleChange(String xml);

	public void contentServerChange(String xml);

	public void allModelMsgChange(String xml);

	public void contentServerstatChange(String xml);

}