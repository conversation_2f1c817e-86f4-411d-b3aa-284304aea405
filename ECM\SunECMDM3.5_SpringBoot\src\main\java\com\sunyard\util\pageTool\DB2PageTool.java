package com.sunyard.util.pageTool;
/**
 * <p>Title: db2数据库查询分页</p>
 * <p>Description: db2数据库查询分页</p>
 * <p>Copyright: Copyright (c) 2010</p>
 * <p>Company: sunyard</p>
 * <p>20120418新增了对获取sql的前多少条方法的实现</p>
 * <AUTHOR>
 * @version 1.0
 */
public class DB2PageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
		int rownum = start + limit - 1;
		return "select * from (select a.*,rownumber() over() as rn from( "
				+ sql + " ) a ) b where rn between " + start + " and " + rownum;
	}
	
	public String getTopSql(String sql , int top){
		return sql + " fetch first " + top + " rows only";
	}

	public String getRandSql(String sql, int top) {
		return sql + " order by rand() fetch first "+top +" rows only";
	}

	public String getMaxVersionAndGroupInDMDB() {
		return "DB2_getMaxVersionAndGroupInDMDB";
	}

	
	public String getDistinctRandSql(String sql, int top) {
		return "select * from (" +sql+")  order by rand() fetch first "+top +" rows only";
	}
}