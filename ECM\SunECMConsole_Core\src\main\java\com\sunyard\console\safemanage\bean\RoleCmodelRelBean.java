package com.sunyard.console.safemanage.bean;
/**
 * <p>Title: 角色与内容模型对应bean</p>
 * <p>Description: 存放角色与内容模型关联信息</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class RoleCmodelRelBean {
	private String role_id;
	private String model_code;
	private String model_name;
	private String permission_code;
	public String getRole_id() {
		return role_id;
	}
	public void setRole_id(String role_id) {
		this.role_id = role_id;
	}
	public String getModel_code() {
		return model_code;
	}
	public void setModel_code(String model_code) {
		this.model_code = model_code;
	}
	public String getModel_name() {
		return model_name;
	}
	public void setModel_name(String model_name) {
		this.model_name = model_name;
	}
	public String getPermission_code() {
		return permission_code;
	}
	public void setPermission_code(String permission_code) {
		this.permission_code = permission_code;
	}
	
	
}
