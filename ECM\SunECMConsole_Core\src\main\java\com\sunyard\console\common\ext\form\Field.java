package com.sunyard.console.common.ext.form;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: 文本框类</p>
 * <p>Description: 存放动态表单中的字段</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class Field {
		/**
		 * 字段名 
		 */
		public static final String NAME			= "name";
		/**
		 * 字段的初始化值 
		 */
		public static final String VALUE		= "value";
		/**
		 * 字段是否不可用，默认为false 
		 */
		public static final String DISABLED 	= "disabled";
		/**
		 * 字段标签说明 
		 */
		public static final String FIELDLABEL	= "fieldLabel";
		/**
		 * 隐藏字段标签，默认为false 
		 */
		public static final String HIDELABEL	= "hideLabel";
		/**
		 * 字段标签与字段之间的分隔符，默认为':' 
		 */
		public static final String LABELSEPARATOR	= "labelSeparator";	
		/**
		 * 字段标签样式 
		 */
		public static final String LABELSTYLE		= "labelStyle";
		/**
		 * 默认为text 
		 */
		public static final String INPUTTYPE		= "inputType";
		/**
		 * 默认为x-form-invalid 
		 */
		public static final String INVALIDCLASS		= "invalidClass";
		/**
		 * 字段非法文本提示 
		 */
		public static final String INVALIDTEXT		= "invalidText";
		/**
		 * 错误信息显示的位置，默认为qtip 
		 */
		public static final String MSGTARGET		= "msgTarget";
		/**
		 * 显示一个浮动的提示信息 
		 */
		public static final String QTIP				= "qtip";
		/**
		 * 显示一个浏览器的浮动提示信息 
		 */
		public static final String TITLE			= "title";
		/**
		 * 在字段下方显示一个提示信息 
		 */
		public static final String UNDER			= "under";
		/**
		 * 在字段右边显示一个提示信息 
		 */
		public static final String SIDE				= "side";
		/**
		 * 字段是否只读，默认为false 
		 */
		public static final String READONLY			= "readOnly";
		/**
		 * 字段在失去焦点时被验证，默认为true 
		 */
		public static final String VALIDATEONBLUR	= "validateOnBlur";
		
		private Map<String,Object> field = new HashMap<String,Object>();
		
		public void setProperties(String key , String value){
			field.put(key, value);
		}
		public void setProperties(String key , int value){
			field.put(key, value);
		}
		public void setProperties(String key , boolean value){
			field.put(key, value);
		}
		public void setProperties_obj(String key , String value){
			field.put(key, "#"+value+"#");
		}
		/**
		 * 获取某个属性
		 * @param key
		 * @return
		 */
		public Object getProperties(String key){
			return field.get(key);
		}
		
		public Map<String,Object> getField(){
			return field;
		}

}
