package com.sunyard.console.configmanager.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * <p>
 * Title:xstream 动态令牌信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("TokenBean")
public class TokenBean {
	/** 令牌号 */
	@XStreamAsAttribute
	private String TOKEN_CODE;
	/** 批次的唯一标识，申请令牌时 由客户端提供 */
	@XStreamAsAttribute
	private String CHECK_VALUE;
	/** 生成时间 */
	@XStreamAsAttribute
	private String TOKEN_TIME;
	/** 用户标识 */
	@XStreamAsAttribute
	private String USER_ID;
	/** 操作类型 */
	@XStreamAsAttribute
	private String OPERATION_CODE;
	/** 绑定ip*/
	@XStreamAsAttribute
	private String BINDING_IP;
	/** 客户端ip*/
	@XStreamAsAttribute
	private String CLIENT_IP;
	public String getToken_code() {
		return TOKEN_CODE;
	}
	public void setToken_code(String tokenCode) {
		TOKEN_CODE = tokenCode;
	}

	public String getCheck_value() {
		return CHECK_VALUE;
	}
	public void setCheck_value(String checkValue) {
		CHECK_VALUE = checkValue;
	}
	public String getToken_time() {
		return TOKEN_TIME;
	}
	public void setToken_time(String tokenTime) {
		TOKEN_TIME = tokenTime;
	}
	public String getUser_id() {
		return USER_ID;
	}
	public void setUser_id(String userId) {
		USER_ID = userId;
	}
	public String getOperation_code() {
		return OPERATION_CODE;
	}
	public void setOperation_code(String operationCode) {
		OPERATION_CODE = operationCode;
	}
	public String getBinding_ip() {
		return BINDING_IP;
	}
	public void setBinding_ip(String bindingIp) {
		BINDING_IP = bindingIp;
	}
	@Override
	public String toString() {
		return "TokenBean [BINDING_IP=" + BINDING_IP + ", CHECK_VALUE="
				+ CHECK_VALUE + ", OPERATION_CODE=" + OPERATION_CODE
				+ ", TOKEN_CODE=" + TOKEN_CODE + ", TOKEN_TIME=" + TOKEN_TIME
				+ ", USER_ID=" + USER_ID + "]";
	}
	public String getCLIENT_IP() {
		return CLIENT_IP;
	}
	public void setCLIENT_IP(String client_ip) {
		CLIENT_IP = client_ip;
	}
	

}
