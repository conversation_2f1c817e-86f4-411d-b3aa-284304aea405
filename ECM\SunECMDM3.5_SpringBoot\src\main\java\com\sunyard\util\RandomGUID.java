package com.sunyard.util;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 随机的36位GUID
 * <AUTHOR>
 *
 */
public class RandomGUID {
	private static final Logger log = LoggerFactory
			.getLogger(RandomGUID.class);
	public String valueBeforeMD5 = "";
	public String valueAfterMD5 = "";
	private static Random myRand;
	private static SecureRandom mySecureRand;

	private static String s_id;

	static {
		mySecureRand = new SecureRandom();
		long secureInitializer = mySecureRand.nextLong();
		myRand = new Random(secureInitializer);
		try {
			s_id = InetAddress.getLocalHost().toString();
		} catch (UnknownHostException e) {
			log.error("出错");
		}

	}

	public RandomGUID() {
		getRandomGUID(false);
	}

	public RandomGUID(boolean secure) {
		getRandomGUID(secure);
	}

	private void getRandomGUID(boolean secure) {
		StringBuffer sbValueBeforeMD5 = new StringBuffer();

		try {
			long time = System.currentTimeMillis();
			long rand = 0;

			if (secure) {
				rand = mySecureRand.nextLong();
			} else {
				rand = mySecureRand.nextLong();
			}

			sbValueBeforeMD5.append(s_id);
			sbValueBeforeMD5.append(":");
			sbValueBeforeMD5.append(Long.toString(time));
			sbValueBeforeMD5.append(":");
			sbValueBeforeMD5.append(Long.toString(rand));

			valueBeforeMD5 = sbValueBeforeMD5.toString();
			MessageDigest md5  = MessageDigest.getInstance("MD5");
			md5.update(valueBeforeMD5.getBytes());

			byte[] array = md5.digest();
			StringBuffer sb = new StringBuffer();
			for (int j = 0; j < array.length; ++j) {
				int b = array[j] & 0xFF;
				if (b < 0x10)
					sb.append('0');
				sb.append(Integer.toHexString(b));
			}

			valueAfterMD5 = sb.toString();

		}catch (Exception e) {
			log.error("Error:" + e);
		}
	}

	public String toString() {
		String raw = valueAfterMD5.toUpperCase();
		StringBuffer sb = new StringBuffer();
		sb.append(raw.substring(0, 8));
		sb.append("-");
		sb.append(raw.substring(8, 12));
		sb.append("-");
		sb.append(raw.substring(12, 16));
		sb.append("-");
		sb.append(raw.substring(16, 20));
		sb.append("-");
		sb.append(raw.substring(20));

		return sb.toString();
	}

	public static String getGUIDStr() {
		RandomGUID myGUID = new RandomGUID();
		return myGUID.toString();
	}

//	public static void main(String args[]) {
//		for (int i = 0; i < 1; i++) {
//			RandomGUID myGUID = new RandomGUID();
//			System.out.println("Seeding String=" + myGUID.valueBeforeMD5);
//			System.out.println("rawGUID=" + myGUID.valueAfterMD5);
//			System.out.println("RandomGUID=" + myGUID.toString());
//			System.out.println("GUID size=" + myGUID.toString().length());
//		}
//	}

}