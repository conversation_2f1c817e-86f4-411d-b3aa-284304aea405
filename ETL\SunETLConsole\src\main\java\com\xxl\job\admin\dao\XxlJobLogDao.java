package com.xxl.job.admin.dao;

import com.xxl.job.admin.core.model.XxlJobLog;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * job log
 * <AUTHOR> 2016-1-12 18:03:06
 */
public interface XxlJobLogDao {
	
	public List<XxlJobLog> pageList(@Param("offset") int offset,
									@Param("pagesize") int pagesize,
									@Param("jobGroup") int jobGroup,
									@Param("jobId") int jobId,
									@Param("jobType") String jobType,
									@Param("triggerTimeStart") Date triggerTimeStart,
									@Param("triggerTimeEnd") Date triggerTimeEnd,
									@Param("logStatus") int logStatus);
	public int pageListCount(@Param("offset") int offset,
							 @Param("pagesize") int pagesize,
							 @Param("jobGroup") int jobGroup,
							 @Param("jobId") int jobId,
							 @Param("jobType") String jobType,
							 @Param("triggerTimeStart") Date triggerTimeStart,
							 @Param("triggerTimeEnd") Date triggerTimeEnd,
							 @Param("logStatus") int logStatus);
	
	public XxlJobLog load(@Param("id") int id);

	public int save(XxlJobLog xxlJobLog);

	public int updateTriggerInfo(XxlJobLog xxlJobLog);

	public int updateHandleInfo(XxlJobLog xxlJobLog);
	
	public int delete(@Param("jobId") int jobId);

	public int triggerCountByHandleCode(@Param("handleCode") int handleCode);

	public List<Map<String, Object>> triggerCountByDay(@Param("from") Date from,
													   @Param("to") Date to,
													   @Param("handleCode") int handleCode);


	public List<Map<String, Object>> triggerCountByDayN(@Param("from") Date from,
													   @Param("to") Date to);


	public int clearLog(@Param("jobGroup") int jobGroup,
						@Param("jobId") int jobId,
						@Param("clearBeforeTime") Date clearBeforeTime,
						@Param("clearBeforeNum") int clearBeforeNum,
						@Param("jobType") String jobType,
						@Param("occur_date") String ocur_date);

	/**
	 * 查询该任务当天是否有执行成功
	 * @param jobId
	 * @param occurDate
	 * @param handleCode
	 */
	public int checkJobIsSuccess(@Param("jobId") int jobId, 
								  @Param("occurDate") String occurDate,
								  @Param("handleCode") String handleCode);

	int updateAttemperMsg(XxlJobLog xxlJobLog);

	Map<String, Object> getMaxJobIdLogTime(@Param("jobId")int Job,@Param("occurDate") String occurDate);
	
	/**
	 * 查询该任务是否有成功日志
	 * @param Job
	 * @param occurDate
	 * @return
	 */
	int getJobIdSuccessLogTime(@Param("jobId")int Job,@Param("occurDate") String occurDate);

	/**
	 * 获取最大的成功 执行日期
	 * @return
	 */
    String getMaxSuccessDate();
}
