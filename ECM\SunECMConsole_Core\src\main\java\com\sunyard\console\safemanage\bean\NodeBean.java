package com.sunyard.console.safemanage.bean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>Title: 角色管理生成树的bean</p>
 * <p>Description: 实现树的生成</p>
 * <p>Copyright: Copyright (c) 2010</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public abstract class NodeBean {
	private List<NodeBean> childNodes;
	private List<NodeBean> allNodeList;
	
	public List<NodeBean> getChildNodes() {
		return childNodes;
	}

	public void setChildNodes(List<NodeBean> childNodes) {
		this.childNodes = childNodes;
	}

	/**
	 * 获取节点ID
	 * @return String
	 */
	public abstract String getNodeID();
	
	/**
	 * 获取父节点ID
	 * @return String
	 */
	public abstract String getParentNodeID();
	
	/**
	 * 获取节点NAME
	 * @return String
	 */
	public abstract String getNodeName();
	
	/**
	 *
	 * @return
	 */
	public abstract String getHasCheckbox();
	
	/**
	 * 获取从数据库取出的节点集合中取出节点的孩子节点
	 * @param nodeList 所有节点的集合
	 * @return List<NodeBean>
	 */
	public List<NodeBean> getChildNodes(List<NodeBean> nodeList){
		childNodes = new ArrayList<NodeBean>();
		for(int i=0;i<nodeList.size();i++){
			NodeBean node = nodeList.get(i);
			if(this.getNodeID().equals(node.getParentNodeID()) || this.getNodeID().equals(node.getParentNodeID())){
				childNodes.add(node);
			}
		}
		return nodeList;
	}
	
	/**
	 * 生成当前节点的json串
	 * @param nodeList 所有节点的集合
	 * @return String
	 */
	public String getNodeString(List<NodeBean> nodeList){
		StringBuffer nodeString = new StringBuffer("{");
		nodeString.append("'id':'").append(this.getNodeID()).append("','text':'")
			.append(this.getNodeName()).append("'");
		nodeList = this.getChildNodes(nodeList);
		if(childNodes.size()>0){
			nodeString.append(",'leaf':false,'expanded':true,'children':").append(this.getNodesString(nodeList,childNodes));
		}else{
			nodeString.append(",'leaf':true");
		}
		if("true".equals(this.getHasCheckbox())){
			nodeString.append(",'checked':false");
		}
		nodeString.append("}");
		return nodeString.toString();
	};
	
	/**
	 * 生成节点数组的json串
	 * @param nodeList 所有节点的集合
	 * @param childNodeList 孩子节点的集合
	 * @return String
	 */
	public static String getNodesString(List<NodeBean> nodeList,List<NodeBean> childNodeList){
		StringBuffer nodeString = new StringBuffer("[");
		for(int i=0;i<childNodeList.size();i++){
			if(i>0) nodeString.append(",");
			nodeList = childNodeList.get(i).getChildNodes(nodeList);
			nodeString.append(childNodeList.get(i).getNodeString(nodeList));
		}
		nodeString.append("]");
		return nodeString.toString();
	};
	
	/**
	 * 生成节点数组的json串
	 * @param nodeList 所有节点的集合
	 * @param childNodeList 孩子节点的集合
	 * @return String
	 */
	public static String getNodesString(List<NodeBean> nodeList,List<NodeBean> childNodeList,boolean isLeaf){
		StringBuffer nodeString = new StringBuffer("[");
		for(int i=0;i<childNodeList.size();i++){
			if(i>0) nodeString.append(",");
			nodeList = childNodeList.get(i).getChildNodes(nodeList);
			nodeString.append(childNodeList.get(i).getNodeString(nodeList,isLeaf));
		}
		nodeString.append("]");
		return nodeString.toString();
	};
	
	/**
	 * 生成整棵树的json串
	 * @param nodeList 所有节点的集合
	 * @return String
	 */
	public static String getTreeString(List<NodeBean> nodeList){
		List<NodeBean> topNodes = getTopNodeList(nodeList);
		//this.setChildNodes(topNodes);
		return getNodesString(nodeList,topNodes);
	}
	
	/**
	 * 生成整棵树的json串
	 * @param nodeList 所有节点的集合
	 * @return String
	 */
	public static String getTreeString(List<NodeBean> nodeList,boolean isLeaf){
		List<NodeBean> topNodes = getTopNodeList(nodeList);
		//this.setChildNodes(topNodes);
		return getNodesString(nodeList,topNodes,isLeaf);
	}
	
	/**
	 * 从所有节点中查找顶级节点,节点的父ID为0的节点
	 * @param nodeList 所有节点的集合
	 * @return List<NodeBean>
	 */
	public static List<NodeBean> getTopNodeList(List<NodeBean> nodeList){
		List<NodeBean> topNodes = new ArrayList<NodeBean>();
		for(int i=0;i<nodeList.size();i++){
			NodeBean tempNode = nodeList.get(i);
			if(tempNode.getParentNodeID()==null||tempNode.getParentNodeID().equals("null")||"0".equals(tempNode.getParentNodeID())||"".equals(tempNode.getParentNodeID())){
				topNodes.add(tempNode);
				nodeList.remove(tempNode);
				i--;
			}
		}
		return topNodes;
	}
	
	/**
	 * 生成当前节点的json串
	 * @param nodeList 所有节点的集合
	 * @param flag		多选标志
	 * @return String
	 */
	public String getNodeString(List<NodeBean> nodeList,boolean isLeaf){
		StringBuffer nodeString = new StringBuffer("{");
		nodeString.append("'id':'").append(this.getNodeID()).append("','text':'")
			.append(this.getNodeName()).append("'");
		nodeList = this.getChildNodes(nodeList);
		if(childNodes.size()>0){
			nodeString.append(",'leaf':false,'expanded':true,'children':").append(this.getNodesString(nodeList,childNodes));
		}else{
			nodeString.append(",'leaf':").append(!isLeaf);
		}
		if("true".equals(this.getHasCheckbox())){
			nodeString.append(",'checked':false");
		}
		nodeString.append("}");
		return nodeString.toString();
	};

	public List<NodeBean> getAllNodeList() {
		return allNodeList;
	}

	public void setAllNodeList(List<NodeBean> allNodeList) {
		this.allNodeList = allNodeList;
	};
}
