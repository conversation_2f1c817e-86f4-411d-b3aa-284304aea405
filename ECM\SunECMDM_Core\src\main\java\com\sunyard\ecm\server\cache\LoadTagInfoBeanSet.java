package com.sunyard.ecm.server.cache;

import com.sunyard.common.Configuration;
import com.sunyard.ecm.server.bean.TagInfoBean;
import com.sunyard.ecm.server.cache.wsclient.GetServiceConfig;
import com.sunyard.exception.SunECMException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;

/**
 * <p>
 * Title:标签信息内存管理类
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2020
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class LoadTagInfoBeanSet {
	private final static Logger log = LoggerFactory.getLogger(LoadTagInfoBeanSet.class);
	private GetServiceConfig getConfig = new GetServiceConfig();
	private HashMap<String, TagInfoBean> tagMap;

	public HashMap<String, TagInfoBean> getTagMap() {
		return tagMap;
	}

	public LoadTagInfoBeanSet() {
		String IsOpenEsMainThread = Configuration.get("IsOpenEsMainThread", "false");
		if ("true".equals(IsOpenEsMainThread)) {
			log.info("begin to getESTagSet");
			setTagInfoBeanSet();
		}
	}

	public TagInfoBean getTagInfoBeanByCode(String obj) {
		TagInfoBean tagInfoBean = tagMap.get(obj);
		return tagInfoBean;
	}

	/**
	 * 初始化标签列表信息
	 */
	private void setTagInfoBeanSet() {
		try {
			List<TagInfoBean> list = getConfig.getAllEsInfoBean();
			tagMap = new HashMap<String, TagInfoBean>();
			for (TagInfoBean tagInfoBean : list) {
				tagMap.put(tagInfoBean.getTag_code(), tagInfoBean);
			}
			log.info("DM主动获取标签列表成功-->" + list);
		} catch (SunECMException e) {
			// webservice异常时，每隔20秒钟重新获取信息 直到获取成功
			log.error("DM主动获取标签列表失败" , e);
			try {
				Thread.sleep(20 * 1000);
			} catch (InterruptedException e1) {
			}
			if (LazySingleton.run) {
				setTagInfoBeanSet();
				log.error("DM主动获取标签列表失败", e);
			} else {
				log.info("serverStop");
			}
		}

	}
}
