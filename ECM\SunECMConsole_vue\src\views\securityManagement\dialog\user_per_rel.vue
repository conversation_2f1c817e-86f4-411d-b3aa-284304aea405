<template>
  <div>
    <el-dialog v-el-drag-dialog :close-on-click-modal="false"
      :title="userPerTitle"
      :visible.sync="userPerdialogFormVisible" width="1200px"
    >
    <div class="edit_dev">
      <el-transfer
        style="text-align: left; display: inline-block"
        v-model="choiceDataList"
        filterable
        :left-default-checked="[1]"
        :right-default-checked="[2]"
        :titles="['未分配权限', '已有权限']"
        :button-texts="['删除', '添加']"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}',
        }"
        :data="notChoiceDataList"
      >
        <span slot-scope="{ option }"> {{ option.label }}</span>
      </el-transfer>
    </div>  
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="hide">取消</el-button>
        <el-button size="mini" type="primary" @click="handlePostPer()"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>


<style>
.transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
}
</style>

<script>
import {getExistPersTreeByUserId,getNotExistPersTreeByUserId,updateUserComponents} from "@/api/userManage";
import {getExistPersTreeByRoleId,getNotExistPersTreeByRoleId,updateRoleComponents} from "@/api/roleManage";
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "user-per-rel",
  directives: { elDragDialog },
  props: {
    userId: {
      required: false,
      type: String,
    },
    objMsg: {
      require: true,
      type: Object,
    },
    isUser: {
      require: true,
      type: Boolean,
    },
  },
  data: function () {
    return {
      notChoiceDataList: [],
      value: [1],
      choiceDataList: [],
      userPerdialogFormVisible: false,
      userPerTitle: "分配权限",
      listLoading: true    
    };
  },
  created() {},
  methods: {
    show() {
      this.userPerdialogFormVisible = true;
    },
    hide() {
      this.userPerdialogFormVisible = false;
    },
    handlePostPer() {
      var choiceKeys = "";
      for (var i = 0; i < this.choiceDataList.length; i++) {
        if (choiceKeys.length > 0) {
          choiceKeys += ",";
        }
        choiceKeys += this.choiceDataList[i];
      }
      this.$message.info("提交中...");
      if (this.isUser) {
        //用户分配权限
        updateUserComponents({
          componentIDs: choiceKeys,
          objMsg: this.objMsg,
        }).then(() => {
          this.hide();
          this.$notify({
            title: "Success",
            message: "Created Successfully",
            type: "success",
            duration: 1000,
          });
        });
      }else{
        //角色分配权限
       updateRoleComponents({
          componentIDs: choiceKeys,
          objMsg: this.objMsg,
        }).then(() => {
          this.hide();
          this.$notify({
            title: "Success",
            message: "Created Successfully",
            type: "success",
            duration: 1000,
          });
        });
      } 
    },
    getAllPersTreeById(userId) {    
      this.notChoiceDataList = [];
      this.choiceDataList = [];
      if (this.isUser) {
        getExistPersTreeByUserId(userId).then((response) => {
          let existpersTree = response.msg;
            for(let item1 of existpersTree){
            let childrenList = item1.children;
            if(childrenList == null){
                this.notChoiceDataList.push({
                  key: item1.id,
                  label: item1.text
                });
                this.choiceDataList.push(item1.id);
            }else{
              for(let item of childrenList){
                this.notChoiceDataList.push({
                  key: item.id,
                  label: item.text
                });  
                this.choiceDataList.push(item.id);
              }
            }
          }
        });
        getNotExistPersTreeByUserId(userId).then((response) => {
          let notexistpersTree = response.msg;
          for(let item1 of notexistpersTree){
          let childrenList = item1.children;
          if(childrenList == null){
              this.notChoiceDataList.push({
                key: item1.id,
                label: item1.text
              });
          }else{
            for(let item of childrenList){
              this.notChoiceDataList.push({
                key: item.id,
                label: item.text
              });
            }
          }
          }
        });
       this.show();
      }else{   
        getExistPersTreeByRoleId(userId).then((response) => {
          let existpersTree = response.msg;
            for(let item1 of existpersTree){
            let childrenList = item1.children;
            if(childrenList == null){
                this.notChoiceDataList.push({
                  key: item1.id,
                  label: item1.text
                });
                this.choiceDataList.push(item1.id);
            }else{
              for(let item of childrenList){
                this.notChoiceDataList.push({
                  key: item.id,
                  label: item.text
                });  
                this.choiceDataList.push(item.id);
              }
            }
          }
        });
        
        getNotExistPersTreeByRoleId(userId).then((response) => {
          let notexistpersTree = response.msg;
          for(let item1 of notexistpersTree){
          let childrenList = item1.children;
          if(childrenList == null){
              this.notChoiceDataList.push({
                key: item1.id,
                label: item1.text
              });
          }else{
            for(let item of childrenList){
              this.notChoiceDataList.push({
                key: item.id,
                label: item.text
              });
            }
          }
          }
        });
       this.show();
      
      }
    },
  }
};
</script>
<style scoped>
.edit_dev >>> .el-transfer-panel {
     width:350px;
   }
</style>