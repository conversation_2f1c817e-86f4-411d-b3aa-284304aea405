package com.sunyard.console.common.struts;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.PrintWriter;

/**
 * <p>Title: 扩展struts2 action 的基础类</p>
 * <p>Description: 方便action的编写</p>
 * <p>Copyright: Copyright (c) 2010</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class BaseAction {
	public String jsonString = "";
	private final static  Logger log = LoggerFactory.getLogger(BaseAction.class);

	public String getJsonString() {
		return jsonString;
	}

	public void setJsonString(String jsonString) {
		this.jsonString = jsonString;
	}

	public void outJsonString(String str) {
		getResponse().setContentType("text/javascript;charset=UTF-8");
		outString(str);
	}

	/*
	 * public void outJson(Object obj) {
	 * outJsonString(JSONObject.fromObject(obj).toString()); }
	 *
	 * public void outJsonArray(Object array) {
	 * outJsonArray(JSONArray.fromObject(array).toString()); }
	 */

	public void outString(String str) {
		getResponse().setContentType("text/javascript;charset=UTF-8");
		try {
			PrintWriter out = getResponse().getWriter();
			out.write(str);
			out.flush();
		} catch (IOException e) {
			log.error("error:"+e.toString());
		}
	}
	public void outTxtString(String str) {
		try {
			getResponse().setHeader("Content-Disposition", "attachment;filename=" + new String("hbaseTableSql.txt"));
			getResponse().setContentType("text/x-component;charset=utf-8");
			PrintWriter out = getResponse().getWriter();
			out.write(str);
			out.flush();
		} catch (Exception e) {
			log.error("", e);
		}
	}
	public void outXMLString(String xmlStr) {
		getResponse().setContentType("application/xml;charset=UTF-8");
		outString(xmlStr);
	}

	public void outExcelString(byte[] buffer, String fileName) {
		try {
			getResponse().setHeader("Content-Disposition", "attachment;filename=" + new String(fileName + ".xls"));
			getResponse().setContentType("application/vnd.ms-excel;charset=utf-8");
			OutputStream toClient = new BufferedOutputStream(getResponse().getOutputStream());
			toClient.write(buffer);
			toClient.flush();
			toClient.close();
		} catch (Exception e) {
			log.error("", e);
		}
	}

	/**
	 * 获得request
	 *
	 * @return
	 */
	public HttpServletRequest getRequest() {
		return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
	}

	/**
	 * 获得response
	 *
	 * @return
	 */
	public HttpServletResponse getResponse() {
		return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
	}

	/**
	 * 获得session
	 *
	 * @return
	 */
	public HttpSession getSession() {

//		if (LoadConfigFile.getConfigBean().isHaixiaSSO()) {
//			//为海峡sso，获取之前的-----
//			HttpSession	 session=getRequest().getSession();
//			UserInfoBean oldUser=(UserInfoBean)session.getAttribute("loginUser");
//			String user_pwd="";
//			String pwd="";
//			if(oldUser!=null&&oldUser.getUser_password()!=null&&!oldUser.getUser_password().equals("")){
//				 user_pwd=oldUser.getUser_password();
//				 pwd=oldUser.getPassword();
//			}
//			//----------------------------
//
//			// 福建海峡SSO，需要从海峡sso中获取用户信息
//			UserInfoBean loginUser=	HaiXiaSSOUtil.getLoginUserInfo();
//			loginUser.setUser_password(user_pwd);
//			loginUser.setPassword(pwd);
//			getRequest().getSession().setAttribute("loginUser", loginUser);
//		}
		return getRequest().getSession();
	}

	/**
	 * 获得servlet上下文
	 *
	 * @return
	 */
//	public ServletContext getServletContext() {
//		return ServletActionContext.getServletContext();
//	}

//	public String getRealyPath(String path) {
//		return getServletContext().getRealPath(path);
//	}


}
