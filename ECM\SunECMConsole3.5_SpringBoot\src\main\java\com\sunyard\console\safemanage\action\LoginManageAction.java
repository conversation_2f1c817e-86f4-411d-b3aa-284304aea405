package com.sunyard.console.safemanage.action;

import java.security.SecureRandom;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

//import javax.servlet.http.HttpSession;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.DateUtil;
import net.sf.json.JSONObject;

import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.console.EncrypterService.impl.DESEncrypter;
import com.sunyard.console.common.accesscontrol.LicenseFileConstant;
import com.sunyard.console.common.config.LoadConfigFile;
import com.sunyard.console.common.config.ReadConfig;
import com.sunyard.console.monitor.monitorCenter.singleton.LazySingleton;
import com.sunyard.console.safemanage.bean.PermissionInfoBean;
import com.sunyard.console.safemanage.bean.UserInfoBean;
import com.sunyard.console.safemanage.dao.LoginManageDAO;
import com.sunyard.console.safemanage.filter.SessionManage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>Title: 用户登入,登出管理Action</p>
 * <p>Description: 处理用户登入,登出事件</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class LoginManageAction extends BaseAction {

	private String login_id;	//用户ID
	private String password;	//用户密码
	@Autowired
	private LoginManageDAO lmdao ;
	//模型代码，
	private String del_modelCode;
	private  final static Logger log = LoggerFactory.getLogger(LoginManageAction.class);
	public String getLogin_id() {
		return login_id;
	}
	public void setLogin_id(String login_id) {
		this.login_id = login_id;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public LoginManageDAO getLmdao() {
		return lmdao;
	}
	public void setLmdao(LoginManageDAO lmdao) {
		this.lmdao = lmdao;
	}


	public String getDel_modelCode() {
		return del_modelCode;
	}
	public void setDel_modelCode(String delModelCode) {
		del_modelCode = delModelCode;
	}
//	@RequestMapping("/safeManage/redirectAction.action")
//	public String redirect(){
//		HttpSession session = getSession();
//		log.debug("当前SESSION ID是[" + session.getId() + "]创建时间是[" + session.getCreationTime() +"]");
//		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//		if(user == null) {
//			if(getRequest().getParameter("modifyPWD")!=null){
//				//需要修改密码，
//				return "modifyPWD";
//			}
//		}
//		//return "SUCCESS";
//		return "/index";
//	}
//
	/**
	 * 登录
	 * @return
	 */
	@RequestMapping("/safeManage/loginAction.action")
	@ResponseBody
	public String login(@RequestParam("login_id") String login_id, @RequestParam("password") String password) {
		log.info( "尝试登录控制台, the username:" + login_id + ";password:" + "****************");
		String		 jsonStr	= null;
		JSONObject	 jsonResp	= new JSONObject();
		UserInfoBean user = null;
		DESEncrypter desEncrypter=new DESEncrypter();
		try {
			 login_id=desEncrypter.desdecrypt(login_id);
			 password=desEncrypter.desdecrypt(password);
		} catch (Exception e2) {
			log.error("login-["+login_id+"]--p["+password+"]",e2);
		}
		try {
			log.info("尝试登录控制台, the username:" + login_id + ";password:" + "****************");
			int errorCount = ReadConfig.getConsoleConfigBean().getLoginErrorCount();
			int errorInterval = ReadConfig.getConsoleConfigBean().getLoginErrorInterval();
			String msg;
//			if (expire()) {
//				log.debug("系统授权未到期，可以登陆");
//			} else {
//				log.warn("系统授权已到期，拒绝登陆，请联系出厂商");
//				jsonResp.put("success", false);
//				jsonResp.put("message", "登陆失败,系统使用权限【" + LicenseFileConstant.getExpiration() + "】已到期");
//				jsonStr = jsonResp.toString();
//				this.outJsonString(jsonStr);
//
//				return null;
//			}
			if (!checkUserLoginError(login_id)) {
				msg = "错误密码超过" + errorCount + "次，无法登录，" + errorInterval + "分钟后再尝试";
				log.warn(msg);
				jsonResp.put("success", false);
				jsonResp.put("message", msg);
				jsonStr = jsonResp.toString();
				this.outJsonString(jsonStr);
				return null;
			}
			user = lmdao.login(login_id, password);
			if (user == null || !DigestUtils.md5Hex(password).equals(user.getPassword())) {
				user = setUserLoginErrorTime(login_id);
				String time = "1";
				String date = "";
				if (user != null) {
					time = user.getUser_post();
					date = user.getPsw_mdf_date();
				}
				log.warn("login fail,username:" + login_id + ";password:" + password + "errorTotalTime:" + time
						+ ",now:" + date);
				jsonResp.put("success", false);
				jsonResp.put("message", "请确认用户是否启用,或用户名和密码是否正确");// ,可以尝试" + errorCount + "次,已错" + time + "次
				jsonStr = jsonResp.toString();
			} else {
				LazySingleton.getUserLoginErrorMap().remove(login_id);
				if (0 != LoadConfigFile.getConfigBean().getLogout_max_day()
						|| 0 != LoadConfigFile.getConfigBean().getLogout_remind_day()) {
					// 开关如果为0，则不需要进行提醒密码修改
					// 判断是否是系统用户，如果是系统用户需要做其他校验
					Map<String, String> map = lmdao.sysUserLoginCheck(user);
					map.put("result", "true");
					String result = map.get("result");
					msg = map.get("msg");
					if ("false".equals(result)) {
						// 超过密码修改天数，直接必须修改密码
						jsonResp = new JSONObject();
						jsonResp.put("success", false);
						jsonResp.put("modifyPWD", true);
						jsonResp.put("message", msg);
						jsonStr = jsonResp.toString();
						this.outJsonString(jsonStr);
						return null;
					}
//					if (msg != null) {
						// 提醒修改密码
//							session.setAttribute("passwordNeedModify", msg);
//					}
				}
				log.debug("dao success,begin to write redis");

				JSONObject tokens = new JSONObject();
				String token = (System.currentTimeMillis() + new SecureRandom().nextInt(100000)) + "";
				jsonResp.put("code", 20000);
				tokens.put("token", token);
				tokens.put("userName",user.getUser_name());
				jsonResp.put("data", tokens);
				jsonStr = jsonResp.toString();
				log.info("tokne->" + token + "]");
				SessionManage sm=new SessionManage();
				user.setToken(token);
				user.setPsw_mdf_date(DateUtil.getMDrqzhsti14());
				sm.addUserSession(user);

			}
		} catch (Exception e) {
			log.error("安全管理->用户登录失败->" , e);
		}
		log.info("--login(over)");
		this.outJsonString(jsonStr);
		return null;
	}

	//@RequestMapping("/safeManage/redirectAction.action")
	public String index() {
		return "WEB-INF/jsp/index";
	}

	public UserInfoBean setUserLoginErrorTime(String loginid) {
		Map<String, UserInfoBean> userLoginErrorMap = LazySingleton.getUserLoginErrorMap();
		UserInfoBean user = userLoginErrorMap.get(loginid);
		if (user == null) {
			user = new UserInfoBean();
			user.setLogin_id(loginid);
			// 错误登录次数为1
			user.setUser_post("1");
			user.setPsw_mdf_date(DateUtil.getMDrqzhsti14());
		} else {
			int errorTime = 1;
			try {
				errorTime = Integer.parseInt(user.getUser_post());
				errorTime++;
			} catch (Exception e) {
				log.error("转换出错" + user.getUser_post() + ",重置为1", e);
				errorTime = 1;
			}
			user.setUser_post(Integer.toString(errorTime));
			if("".equals(user.getPsw_mdf_date())){
				//失败次数重新计算
				user.setPsw_mdf_date(DateUtil.getMDrqzhsti14());
			}
		}
		userLoginErrorMap.put(loginid, user);
		return user;
	}
	/**
	 * 校验用户错误密码到期时间
	 * @param loginid
	 * @return true表示到期了，可以重新登录；false表示未到期（5分钟），无法登录
	 */
	public boolean checkUserLoginError(String loginid) {
		Map<String, UserInfoBean> userLoginErrorMap = LazySingleton.getUserLoginErrorMap();
		UserInfoBean user = userLoginErrorMap.get(loginid);
		if (user == null) {
			return true;
		} else {
			String errorEndTime = user.getPsw_mdf_date();
			try {
				errorEndTime = DateUtil.getMDrqzhsti14(errorEndTime, ReadConfig.getConsoleConfigBean().getLoginErrorInterval());
			} catch (ParseException e1) {
				log.error("" + errorEndTime, e1);
				errorEndTime = DateUtil.getMDrqzhsti14();
			}
			String time = user.getUser_post();
			boolean flag = DateUtil.compareTime(errorEndTime, DateUtil.getMDrqzhsti14());
			if (flag) {
				user.setPsw_mdf_date("");
				user.setUser_post("0");
			}
			if (Integer.parseInt(time) < ReadConfig.getConsoleConfigBean().getLoginErrorCount()) {
				// 如果密码错误次数不到4次，则可以再尝试登录
				return true;
			}
			return flag;
		}
	}

//	/**
//	 * 日期和当前日期比较，如果当前日期大于last返回true，否则返回false
//	 *
//	 * @param last
//	 * @return 如果当前日期大于last返回true，否则返回false
//	 */
//	public boolean compareDate(String last) {
//		boolean flag = false;
//		Calendar lastCal = Calendar.getInstance();
//		Calendar nowCal = Calendar.getInstance();
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//		try {
//			lastCal.setTime(sdf.parse(last));
//			flag = nowCal.compareTo(lastCal) >= 0;
//		} catch (Exception e) {
//			log.error("计算时间出错", e);
//			return flag;
//		}
//		return flag;
//
//	}


	/**
	 * 登出
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/safeManage/loginOutAction.action")
	public String loginout(@RequestParam("token") String token) {
		log.info("--loginout(start)-->用户登出");
		SessionManage sm = new SessionManage();
		sm.rmUserSession(token);
		JSONObject jsonResp = new JSONObject();
		jsonResp.put("success", true);
		jsonResp.put("message", "登出成功!!");
		jsonResp.put("code", "200");
		// 登出的时候判断跳转页面需要
		this.outJsonString(jsonResp.toString());
		log.info("--loginout(over)-->用户登出");
		return null;
	}

	/**
	 * 使用权限到期校验
	 * 判断系统使用权限是否到期 如果到期则拒绝登陆
	 * @return true 可以使用 false 不能使用
	 */
	private boolean  expire() {
		log.info( "--expire(start)-->使用权限到期校验");
		boolean flag = false;
		SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");//设置日期格式 yyyy-MM-dd HH:mm:ss
		String now = df.format(new Date());
		log.debug( "系统当前时间为："+now);
		String expriation = LicenseFileConstant.getExpiration();
		log.debug( "系统到期时间为："+expriation);
		if(expriation.equals("99999999")){
			flag = true;
		}
		if(Integer.parseInt(now)<=Integer.parseInt(expriation)){
			flag = true;
		}
		log.info( "--expire(over)-->使用权限到期校验,flag:"+flag);
		return flag;
	}

		@ResponseBody
		@RequestMapping("/safeManage/ssoUserLoginAction.action")
	public String ssoUserLogin(String token) {
		JSONObject jsonResp = new JSONObject();
		SessionManage sm = new SessionManage();
		try {
			UserInfoBean userBean = sm.getUserSession(token, getRequest());
//		Map<String, String> userMap = getUserFromRedis(token);
			if (userBean == null) {
				log.error("用户未登陆或登陆超时");
				jsonResp.put("success", false);
				jsonResp.put("message", "用户未登陆或登陆超时");
				jsonResp.put("code", 50008);
				this.outJsonString(jsonResp.toString());
				return null;
			}
			String userName = userBean.getUser_name();

			List<PermissionInfoBean> pers = lmdao.getMenuAndButtonPermission(userName);
			List<String> zimuList = new ArrayList<String>();
			List<String> shuziList = new ArrayList<String>();
			for (int i = 0; i < pers.size(); i++) {

				if (pers.get(i).getPermission_type() != null && pers.get(i).getPermission_type().compareTo("0") > 0) {
					zimuList.add(pers.get(i).getPermission_code());
				} else {
					shuziList.add(pers.get(i).getPermission_code());
				}

			}
			jsonResp.put("code", 20000);// TODO mock
			JSONObject jsonResp2 = new JSONObject();
			jsonResp2.put("roles", shuziList);
			jsonResp2.put("buttons", zimuList);
			jsonResp2.put("name", userName);
//			jsonResp2.put("avatar", "https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif");

//				jsonResp.put("data", JSON.parse("{\n" +
//						"    roles: ['admin'],\n" +
//						"    introduction: 'I am a super administrator',\n" +
//						"    avatar: 'https://wpimg.wallstcn.com/f778738c-e4f8-4870-b634-56703b4acafe.gif',\n" +
//						"    name: 'Super Admin'\n" +
//						"  }"));

			jsonResp.put("data", jsonResp2);
		} catch (Exception e) {
			log.error("", e);
			// TODO: handle exception
		}
		this.outJsonString(jsonResp.toString());

		return null;

	}
	}
