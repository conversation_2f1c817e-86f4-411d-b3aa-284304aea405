package com.sunyard.ecm.server;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.sunyard.ecm.common.trans.Connection;
import com.sunyard.util.TransOptionKey;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;

import com.sunyard.ecm.common.trans.http.HttpConnection;

/**
 * HTTP端口的服务端
 * 
 * <AUTHOR>
 * 
 */
@Controller("httpReqDispacher")
@RequestMapping(value = "/servlet/httpReqDispacher")
public class HttpReqDispacher {
	@RequestMapping(value = "", method = RequestMethod.POST)
	public ModelAndView handleRequest(HttpServletRequest request,
			HttpServletResponse response) throws Exception {
		Connection connection = new HttpConnection(request, response,
				TransOptionKey.MESSAGE_PROCESS);
		connection.process();
		return null;
	}

}