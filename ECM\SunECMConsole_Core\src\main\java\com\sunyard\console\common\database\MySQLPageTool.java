package com.sunyard.console.common.database;

import com.sunyard.console.contentmodelmanage.bean.IndexInfoBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Title: oracle数据库查询分页
 * </p>
 * <p>
 * Description: oracle数据库查询分页
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ConditionalOnProperty(prefix = "pageTool",name = "Type", havingValue = "mysqlPageTool")
@Service("pageTool")

public class MySQLPageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
//		int rownum = start + limit - 1;
		return sql + " limit " + (start - 1) + "," + limit + " ";
	}

	public String getTableSpaceName(String sql, String tableSpaceName) {
		return sql;
	}

	public String getOnlyOneSql(String sql, String tableName) {
		return sql + " limit 1 ";
	}

	@Override
	public String delIndexSql(IndexInfoBean bean) {
		return "DROP INDEX " + bean.getTable_name() + "_" + bean.getIndex_name() + " ON " + bean.getTable_name();
	}

}
