package com.sunyard.util;

/**
 * 符号转换工具类
 * <AUTHOR>
 */
public class SymbolChange {
	
	/**
	 * 客户端socket符号转换
	 * @param value
	 * @return
	 */
	public static String clientSocketSymbolChange(String value){
		value = value.replaceAll(",", "<<DH>>");
		return clientHttpSymbolChange(value);
	}
	/**
	 * 客户端http符号转换
	 * @param value
	 * @return
	 */
	public static String clientHttpSymbolChange(String value){
		value = value.replaceAll("'", "''");
		return value;
	}
	/**
	 * 服务端socket符号转换
	 * @param value
	 * @return
	 */
	public static String severSocketSymbolChange(String value){
		value = value.replaceAll("<<DH>>", ",");
		return value;
	}
}