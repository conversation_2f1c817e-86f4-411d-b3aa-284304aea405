package com.sunyard.dmeclient;

import java.io.File;
import java.io.IOException;
import java.net.Socket;
import java.net.UnknownHostException;
import java.util.List;

import com.sunyard.util.TransOptionKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.client.conn.SocketConn;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.util.OptionKey;
import com.sunyard.ws.utils.XMLUtil;

public class DmSocketClient {
	private String splitSym = TransOptionKey.SPLITSYM;
	final static Logger log = LoggerFactory.getLogger(DmSocketClient.class);
	private String ipAddress;
	private int socketPort;
	private SocketConn conn;
	
	public DmSocketClient(String ip, int socketPort) {
		this.ipAddress = ip;
		this.socketPort = socketPort;
	}
	
	/**
	 * 建立socket
	 * 
	 * @param host
	 *            主机IP
	 * @param port
	 *            主机端口
	 * @return 成功/失败
	 */
	private boolean connectToHost(String host, int port) throws SunECMException {
		int connectretrytimes = 5;
		// 重复连接CONNECTRETRYTIMES
		for (int i = 0; i < connectretrytimes; i++) {
			if (newHost(host, port)) {
				return true;
			}
			// 等待200毫秒再下一次连接
			try {
				Thread.sleep(200);
			} catch (InterruptedException e) {
				log.debug("当前连接次数：" + (i + 1));
			}
			if (i == connectretrytimes - 1) {
				throw new SunECMException(
						SunECMExceptionStatus.CLIENT_CONNECT_TO_SERVER_ERROR,
						"连接主机：" + host + " 端口:" + port + "失败");
			}
		}
		return false;
	}

	/**
	 * 建立连接
	 * 
	 * @param ip
	 * @param socketPort
	 * @return
	 */
	private boolean newHost(String ip, int socketPort) {
		try {
			Socket socket = new Socket(ip, socketPort);
			this.conn = new SocketConn(socket);
		} catch (UnknownHostException e) {
			return false;
		} catch (IOException e) {
			return false;
		}
		return true;
	}

	public String login(String userName, String password)
			throws SunECMException, IOException {
		connectToHost(ipAddress, socketPort);
		String resultMsg = TransOptionKey.FAIL;
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append(TransOptionKey.MESSAGE_PROCESS).append(
					splitSym + "OPTION=" + OptionKey.LOGIN + ",USERNAME=")
					.append(userName).append(",PASSWORD=").append(password);
			conn.sendMsg(sbuf.toString());
			String msg = conn.receiveMsg();
			log.debug("登陆消息:" + msg);
			if(msg!=null){
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					resultMsg = TransOptionKey.SUCCESS;
				} else {
					throw new SunECMException("登陆失败");
				}
			}}else {
				throw new SunECMException("登陆失败");
			}
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
		}
		return resultMsg;
	}

	public String logout(String userName) throws SunECMException, IOException {
		connectToHost(ipAddress, socketPort);
		String resultMsg = TransOptionKey.FAIL;
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append(TransOptionKey.MESSAGE_PROCESS).append(
					splitSym + "OPTION=" + OptionKey.LOGOUT + ",USERNAME=")
					.append(userName);
			conn.sendMsg(sbuf.toString());
			String msg = conn.receiveMsg();
			log.debug("登出的消息" + msg);
			if (msg != null) {

				String[] strArray = msg.split(splitSym);
				if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
					if (strArray[1].equals(TransOptionKey.SUCCESS)) {
						resultMsg = TransOptionKey.SUCCESS;
					} else {
						throw new SunECMException("登出异常");
					}
				}
			} else {
				throw new SunECMException("登出异常");
			}
			
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
		}
		return resultMsg;
	}
	
	public String upload(String xml, String dmsName)
			throws SunECMException, IOException {
		connectToHost(ipAddress, socketPort);
		ClientBatchBean clientBatchBean = XMLUtil.xml2Bean(xml, ClientBatchBean.class);
		log.debug("begin to reset md5------------------------------");
		clientBatchBean.setOwnMD5(true);
		if (!checkFileExist(clientBatchBean)) {
			return "false";
		}
		String resultStr = "";
		String contentID = TransOptionKey.FAIL;
		try {
			log.debug(XMLUtil.bean2XML(clientBatchBean));
			conn.sendMsg(TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION="
					+ OptionKey.UPLOAD + ",START=START,XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName);
			String msg = conn.receiveMsg();
			if(msg!=null){
			String[] params = msg.split(splitSym);
			log.debug("上传文件前返回:" + msg);
			if (params[0].equals(TransOptionKey.SERVER_OK)) {
				contentID = params[1];
				resultStr = TransOptionKey.FAIL + TransOptionKey.SPLITSYM
						+ contentID;
				clientBatchBean.getIndex_Object().setContentID(contentID);
//				if (clientBatchBean.isBreakPoint()) {
//					sendBreakClientBatchFileBean(clientBatchBean
//							.getDocument_Objects(), contentID, clientBatchBean
//							.getModelCode());
//				} else {
					sendClientBatchFileBean(clientBatchBean
							.getDocument_Objects(), contentID);
//				}
				conn.sendMsg(TransOptionKey.MESSAGE_PROCESS + splitSym
						+ "OPTION=" + OptionKey.UPLOAD
						+ ",START=END,CONTENTID="
						+ clientBatchBean.getIndex_Object().getContentID()
						+ ",DMSNAME=" + dmsName);
				msg = conn.receiveMsg();
				log.debug("上传文件后返回:" + msg);
				if(msg!=null){
				String[] strArray = msg.split(splitSym);
				if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
					if (strArray[1].equals(TransOptionKey.SUCCESS)) {
						resultStr = TransOptionKey.SUCCESS
								+ TransOptionKey.SPLITSYM + strArray[2];
					}
				}} else {
					resultStr += TransOptionKey.SPLITSYM + msg;
				}
			}} else {
				resultStr = msg;
			}
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
		}
		return resultStr;
	}
	
	private String checkIn(ClientBatchBean clientBatchBean, String dmsName)
			throws SunECMException, IOException {
		connectToHost(ipAddress, socketPort);
		String result = TransOptionKey.FAIL;
		try {
			conn.sendMsg(TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION="
					+ OptionKey.CHECKIN + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName);
			String msg = conn.receiveMsg();
			log.debug("检入时返回的信息：" + msg);
			if(msg!=null){
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = TransOptionKey.SUCCESS + splitSym;
				} else {
					result = TransOptionKey.FAIL;
				}
			}} else {
				throw new SunECMException("检入时发生异常...异常代码：" + msg);
			}
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
		}
		return result;
	}

	private String checkOut(ClientBatchBean clientBatchBean, String dmsName)
			throws SunECMException, IOException {
		connectToHost(ipAddress, socketPort);
		String result = TransOptionKey.FAIL;
		try {
			conn.sendMsg(TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION="
					+ OptionKey.CHECKOUT + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName);
			String msg = conn.receiveMsg();
			log.debug("检出时返回的信息：" + msg);
			if(msg!=null){
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = TransOptionKey.SUCCESS + splitSym + strArray[2];
				} else {
					result = TransOptionKey.FAIL + splitSym + strArray[2];
				}
			}} else {
				throw new SunECMException("检出时发生异常...异常代码：" + msg);
			}
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
		}
		return result;
	}
	
	public String update(String xml, String dmsName,
			boolean isAutoCheck) throws SunECMException, IOException {
		String resultStr = TransOptionKey.FAIL;
		ClientBatchBean clientBatchBean = XMLUtil.xml2Bean(xml, ClientBatchBean.class);
		if (!checkFileExist(clientBatchBean)) {
			return TransOptionKey.FAIL;
		}
		if (isAutoCheck) {
			String checkOutMsg = checkOut(clientBatchBean, dmsName);
			log.debug("检出的结果信息: " + checkOutMsg);
			if (checkOutMsg.split(splitSym)[0].equals(TransOptionKey.SUCCESS)) {
				clientBatchBean.setCheckToken(checkOutMsg.split(splitSym)[1]);
				log.debug("批次["
						+ clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出成功...");
			} else {
				throw new SunECMException("批次["
						+ clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出失败:" + checkOutMsg);
			}
			try {
				resultStr = update(clientBatchBean, dmsName);
				log.debug("自动检入检出更新返回结果：" + resultStr);
			} finally {
				String checkInMsg = checkIn(clientBatchBean, dmsName);
				if (!checkInMsg.split(splitSym)[0]
						.equals(TransOptionKey.SUCCESS)) {
					throw new SunECMException("批次["
							+ clientBatchBean.getIndex_Object().getContentID()
							+ "]自动检入失败:" + checkInMsg);
				}
			}
			log.debug("批次[" + clientBatchBean.getIndex_Object().getContentID()
					+ "]自动检入成功...");
		} else {
			resultStr = update(clientBatchBean, dmsName);
			log.debug("手动检入检出更新返回结果：" + resultStr);
		}
		return resultStr;
	}

	private String update(ClientBatchBean clientBatchBean, String dmsName)
			throws SunECMException, IOException {
		connectToHost(ipAddress, socketPort);
		String resultStr = TransOptionKey.FAIL;
		try {
			log.debug(XMLUtil.bean2XML(clientBatchBean));
			conn.sendMsg(TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION="
					+ OptionKey.UPDATE + ",START=START,XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName);
			String msg = conn.receiveMsg();
			log.debug("更新前建立连接时服务端返回：" + msg);
			if (msg != null) {
				String[] msgArray = msg.split(splitSym);
				if (msgArray[0].equals(TransOptionKey.SERVER_OK)) {
					// if (clientBatchBean.isBreakPoint()) {
					// sendBreakClientBatchFileBean(clientBatchBean
					// .getDocument_Objects(), clientBatchBean
					// .getIndex_Object().getContentID(), clientBatchBean
					// .getModelCode());
					// } else {
					sendClientBatchFileBean(clientBatchBean.getDocument_Objects(), clientBatchBean.getIndex_Object().getContentID());
					// }
					conn.sendMsg(TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPDATE + ",START=END,CONTENTID=" + clientBatchBean.getIndex_Object().getContentID() + ",DMSNAME="
							+ dmsName);
					msg = conn.receiveMsg();
					log.debug("更新文件上传完成后服务端返回：" + msg);
					if (msg != null) {
						String[] strArray = msg.split(splitSym);
						if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
							if (strArray[1].equals(TransOptionKey.SUCCESS)) {
								resultStr = TransOptionKey.SUCCESS;
							}
						} else {
							resultStr += TransOptionKey.SPLITSYM + msg;
						}
					}
				}} else {
				resultStr = msg;
			}
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
		}
		return resultStr;
	}
	
	private void sendClientBatchFileBean(
			List<ClientBatchFileBean> ClientBatchFileBeans, String contentID) throws SunECMException, IOException {
		for (ClientBatchFileBean batchFileBean : ClientBatchFileBeans) {
			List<ClientFileBean> fileBeans = batchFileBean.getFiles();
			for (ClientFileBean fileBean : fileBeans) {
				if (fileBean.getFileName() != null) {
					conn.sendFileData(fileBean.getFileName(), contentID,
							TransOptionKey.FILE_RECIEVE
									+ TransOptionKey.SPLITSYM);
				}
			}
		}
	}
	
	private boolean checkFileExist(ClientBatchBean clientBatchBean) {
		boolean flag = true;
		// 校验文件是否存在
		List<ClientBatchFileBean> batchFileBeans = clientBatchBean
				.getDocument_Objects();
		for (ClientBatchFileBean clientBatchFileBean : batchFileBeans) {
			List<ClientFileBean> files = clientBatchFileBean.getFiles();
			for (ClientFileBean clientFileBean : files) {
				if (clientFileBean.getFileName() != null) {
					File file = new File(clientFileBean.getFileName());
					if (!file.exists()) {
						log.debug(file.getPath() + "文件不存在...");
						return false;
					}
				}
			}
		}
		return flag;
	}
}