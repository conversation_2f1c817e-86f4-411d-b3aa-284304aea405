<template>
  <div class="app-container">
    <div class="filter-container">
       <el-select v-model="listQuery.group_id" placeholder="请选择存储服务器组" @change='getServerModelCode()'>
        <el-option
          v-for="item in storeGroups"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-select v-model="listQuery.model_code" placeholder="请选择内容对象">
        <el-option
          v-for="item in storeObj"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
	 <el-date-picker v-model="listQuery.date" type="date" v-if="isdate" placeholder="选择统计日期" 
	 	value-format="yyyy-MM-dd">
	 </el-date-picker>
	 <el-date-picker v-model="listQuery.month" type="month" v-if="!isdate" placeholder="选择统计月份"
	 	value-format="yyyy-MM">
	 </el-date-picker>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
     </div> 
	<el-button type="text" @click="queryIsMonthData()">{{btnText}}</el-button>
    
	<scheDayRel ref="scheduler_day_rel" 
		v-show="day_visible" 
		:day_visible="this.day_visible" 
	  	:listQuery="this.listQuery"
	  	:serverIds="this.serverIds"
	  	:serverNames="this.serverNames"
	>
    </scheDayRel>
	
	<scheMonthRel ref="scheduler_month_rel" 
		v-show="month_visible" 
		:month_visible="this.month_visible" 
	  	:listQuery="this.listQuery"
	>
    </scheMonthRel>
  </div>

</template>

<script>
import {getContentServerGroup,getRelServers,getRelContentObject,getTaskCountNum} from '@/api/monitorManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import scheDayRel from "./dialog/scheduler_day_rel.vue";
import scheMonthRel from "./dialog/scheduler_month_rel.vue";

import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

export default {
    name: 'ComplexTable',
    components: { Pagination,scheDayRel,scheMonthRel},
    directives: { waves,elDragDialog },
    filters: {
        statusFilter(status) {
        const statusMap = {
            published: 'success',
            draft: 'info',
            deleted: 'danger'
        }
        return statusMap[status]
    }
  },

  props: {
    className: {
      type: String,
      default: 'chart'
    },
    canvas1: {
      type: String,
      default: 'chart1'
    },
	canvas2: {
      type: String,
      default: 'chart2'
    },
    width: {
      type: String,
      default: '700px'
    },
	width1: {
      type: String,
      default: '400px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
    //   default: '16px 16px'
    }
  },
  data() {
    return{
		btnText : "按月统计该定时任务",
		isdate :true,
        storeGroups :  [],
        storeServers :  [],
        storeObj : [],
		serverIds:[],
		serverNames:[],
      	server_ids:"",
      	server_names:"",
		day_visible:false,
		month_visible:false,
      listQuery: {
        importance: undefined,
        title: undefined,
        type: undefined,
		group_id: "",
      	server_id: "",
     	model_code: "",
	  	date: "",
	  	month: ""
      },

    }
  },
  
    mounted() {
    //   this.$nextTick(() => {
    //     this.showCharts()
    //   })
    },
    created() {
        this.getGroups();
		this.initData();
    },


  methods: {
    handleclear() {
      this.listQuery.group_id= "";
      this.listQuery.server_id= "";
      this.listQuery.model_code= "";
	  this.listQuery.date= "";
	  this.listQuery.month= ""

    },
	queryIsMonthData() {
		this.listQuery.server_id = this.server_ids;
		if (this.btnText == '按月统计该定时任务') {
			this.btnText = '按天统计该定时任务';
			this.isdate = false;
			this.day_visible = false;
			this.month_visible = true;
			this.$refs.scheduler_month_rel.showData();
		} else {
			this.btnText = '按月统计该定时任务';
			this.isdate = true;
			this.day_visible = true;
			this.month_visible = false;
			this.$refs.scheduler_day_rel.showData();
		}
	},
	initData() {
      //获取当前时间
      let now   = new Date();
      let monthn = now.getMonth()+1;
      let yearn  = now.getFullYear();
      let dayn = now.getDate();
      monthn = monthn < 10 ? "0" + monthn : monthn;
      dayn = dayn < 10 ? "0" + dayn : dayn;
      this.listQuery.date = yearn+"-"+monthn+"-"+dayn;
	  this.listQuery.month = yearn+"-"+monthn;
    //   this.selectUser = parseInt(sessionStorage.getItem("userid"));
    },

    handleFilter() {
		this.listQuery.server_id = this.server_ids;
		if (this.isdate) {
			this.day_visible = true;
			this.month_visible = false;
			this.$refs.scheduler_day_rel.showData();
		} else {
			this.day_visible = false;
			this.month_visible = true;
			this.$refs.scheduler_month_rel.showData();
		}
    },
    getGroups(){
        getContentServerGroup().then(response => {
        this.storeGroups = response.root;
      })
    },

    getServerModelCode(){
        getRelContentObject(this.listQuery).then(response => {
        	this.storeObj = response.root
     	})
	    getRelServers(this.listQuery).then(response => {
          this.storeServers = response.root;
          this.serverIds = [];
          this.serverNames = [];
          this.server_ids="";
          this.server_names="";

          for (let i = 0; i < this.storeServers.length; i++) {
            this.serverIds[i] = this.storeServers[i].id;
            this.serverNames[i] = this.storeServers[i].text;
            if (i == 0) {
              this.server_ids += this.storeServers[i].id;
              this.server_names += this.storeServers[i].text;
            } else {
              this.server_ids += ','+ this.storeServers[i].id;
              this.server_names += ',' + this.storeServers[i].text;
            }
          }
        })
    }

  }
}
</script>

<style scoped>

</style>
