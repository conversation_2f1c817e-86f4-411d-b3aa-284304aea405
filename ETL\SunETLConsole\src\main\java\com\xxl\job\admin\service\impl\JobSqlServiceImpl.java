package com.xxl.job.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.xxl.job.admin.core.model.JobSql;
import com.xxl.job.admin.dao.JobSQLDao;
import com.xxl.job.admin.service.JobSqlService;
import com.xxl.job.core.biz.model.ReturnT;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class JobSqlServiceImpl implements JobSqlService {

    @Resource
    private JobSQLDao jobSQLDao;

    @Override
    public Map<String, Object> paramPageList(int start, int length, Integer jobId) {
        // page list
        // page query
        PageHelper.startPage(start/length+1, length);
        List<JobSql> list = jobSQLDao.pageList(jobId);
        int list_count = jobSQLDao.pageListCount(jobId);

        // package result
        Map<String, Object> maps = new HashMap<String, Object>();
        maps.put("recordsTotal", list_count); // 总记录数
        maps.put("recordsFiltered", list_count); // 过滤后的总记录数
        maps.put("data", list); // 分页列表
        return maps;
    }

    @Override
    public ReturnT<String> add(JobSql jobSql) {
        int vnt = jobSQLDao.save(jobSql);
        if (vnt > 0) {
            return ReturnT.SUCCESS;
        } else {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "新增SQL配置失败");
        }
    }

    @Override
    public ReturnT<String> reschedule(JobSql jobSql) {
        int vnt = jobSQLDao.update(jobSql);
        if (vnt > 0) {
            return ReturnT.SUCCESS;
        } else {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "修改SQL配置失败");
        }
    }

    @Override
    public ReturnT<String> remove(int id) {
        int i = jobSQLDao.delete(id);
        if (i > 0) {
            return ReturnT.SUCCESS;
        } else {
            return new ReturnT<String>(ReturnT.FAIL_CODE, "新增参数配置失败");
        }
    }

    @Override
    public ReturnT<String> add(List<JobSql> jobSqls) {
        return null;
    }
}
