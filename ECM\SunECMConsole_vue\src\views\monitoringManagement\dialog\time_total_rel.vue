<template>
  <div>
	 <el-date-picker v-model="queryMonth" type="month" value-format="yyyy-MM">
	 </el-date-picker>	
	<el-button type="text" @click="queryMonthData()">查询当月数据</el-button>
	
	<el-row :gutter="32">
		<el-col :xs="24" :sm="24" :lg="10">
			<div :id="timeCanvas"  :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
	 </el-row>
  </div>
</template>


<style>

</style>

<script>
import {getTimeAction} from '@/api/monitorManage'
import * as echarts from 'echarts'

export default {
  name: "time-total-rel",
    props: {
	listQuery: {
      require: true,
      type: Object,
    },
    timeCanvas: {
      type: String,
      default: 'timechart'
    },
    width: {
      type: String,
      default: '1100px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
      default: '16px 16px'
    }
  },
  data() {
    return{
		init : 0,
		minTime : [], 
		avgTime : [], 
		maxTime : [], 
		timechart : null,
		days : [],
		queryMonth:this.getNowMonth(),
		echartTitle : ""
	}
  },
  
    mounted() {
      this.$nextTick(() => {
		//   this.checkInit();
      })
    },


  methods: {
	getNowMonth(){
		let now = new Date();
		let year = now.getFullYear();
		let month = now.getMonth()+1;
		month = month < 10 ? "0" + month : month;

		return `${year}-${month}`;
	},

	checkInit(){
		let taskoption = this.listQuery.option;
		if(taskoption == 'UPLOAD'){
			this.echartTitle = "上传"
		}else if(taskoption == 'UPDATE'){
			this.echartTitle = "更新"
		}else if(taskoption == 'HEIGQUERY'){
			this.echartTitle = "高级检索"
		}else if(taskoption == 'QUERY'){
			this.echartTitle = "查询"
		}else if(taskoption == 'GETFILE'){
			this.echartTitle = "文件下载"
		}else if(taskoption == 'MIGRATE'){
			this.echartTitle = "批次迁移"
		}
		if (this.init == 0) {
			let selYear = this.queryMonth.split("-")[0];
			let selMonth = this.queryMonth.split("-")[1];
			this.time = new Date(selYear,selMonth, 0).getDate();
			this.days = this.getDays();
			this.showTimeMonthChart();
			this.showTimeMonthData();
		}
	},
	queryMonthData() {
		let selYear = this.queryMonth.split("-")[0];
		let selMonth = this.queryMonth.split("-")[1];
		this.time = new Date(selYear,selMonth, 0).getDate();
		this.days = this.getDays();
		this.showTimeMonthChart();
		this.showTimeMonthData();
	},

	getDays() {
		let temp = [];
		for (let i = 1; i <= this.time; i++) {
			temp.push(i);
			this.minTime.push[0];
			this.avgTime.push(0);
			this.maxTime.push(0);
		}
		return temp;
	},
    showTimeMonthChart(){
      	this.timechart = echarts.init(document.getElementById(this.timeCanvas), 'dark');
        let option = {
			title : {
				textStyle : {
					color : '#e2e9ff',
				},
				x : 'center',
				text : this.echartTitle + '接口耗时统计'
			},
			grid : { //控制图的大小
				left : '3%',
				right : '5%',
				bottom : '3%',
				containLabel : true
			},
			legend : {
				textStyle : {
					color : '#e2e9ff'
				},
				x : 'right',
				data : [ '最小', '平均', '最大' ]
			},
			tooltip : {
				trigger : 'axis',
				axisPointer : {
					type : 'shadow',
				}
			},
			xAxis : {
				name : 'Day',
				axisLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				axisLabel : {
					margin : 10,
					color : '#e2e9ff',
					textStyle : {
						fontSize : 12
					},
				},
				/* triggerEvent:{
					componentType: 'xAxis',
					value: '',
					name: 'trigger'
					}, */
				type : 'category',
				data : this.days
			},
			yAxis : {
				name : '(ms)',
				nameTextStyle : {
					color : "#e2e9ff"
				},
				type : 'value',
				axisLabel : {
					formatter : '{value}',
					color : '#e2e9ff',
				},
				axisLine : {
					show : false
				},
				splitLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				}
			},
			series : [
					{
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0,0,0,1,
										[{
											offset : 0,
											color : 'rgba(32,223,94,1)' // 0% 处的颜色
										},
										{
											offset : 1,
											color : 'rgba(101,252,155,1)' // 100% 处的颜色
										} ], false),
								barBorderRadius : [ 30, 30, 30,30 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						name : '最小',
						type : 'bar',
						data : this.minTime
					},
					{
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0,0,0,1,
										[{
											offset : 0,
											color : 'rgba(238,93,2,1)' // 0% 处的颜色
										},
										{
											offset : 1,
											color : 'rgba(237,206,53,1)' // 100% 处的颜色
										} ], false),
								barBorderRadius : [ 30, 30, 30,30 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						name : '平均',
						type : 'bar',
						data : this.avgTime
					},
					{
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0,0,0,1,
										[{
											offset : 0,
											color : 'rgba(0,244,255,1)' // 0% 处的颜色
										},
										{
											offset : 1,
											color : 'rgba(0,77,167,1)' // 100% 处的颜色
										} ], false),
								barBorderRadius : [ 30, 30, 30,30 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						name : '最大',
						type : 'bar',
						data : this.maxTime
					} ]
		};

    this.timechart.setOption(option);
    },

    showTimeMonthData(){
		this.listQuery.date = this.queryMonth.replace("-","");
		getTimeAction(this.listQuery).then(response => {
			this.timechart.hideLoading();
			this.minTime = [];
			this.avgTime = [];
			this.maxTime = [];

			// let date = [];
			for (let i = 1; i <= this.time; i++) {
				if (i <= 9) {
					i = '0' + i;
				}
				if (response['' + i] != "undefined"
						&& response['' + i] != null) {
					this.minTime.push(response['' + i].minTime);
					this.avgTime.push(response['' + i].avgTime);
					this.maxTime.push(response['' + i].maxTime);
				} else {
					this.minTime.push(0);
					this.avgTime.push(0);
					this.maxTime.push(0);
				}
			}

			this.timechart.setOption({
				xAxis : {
					data : this.days
				},
				series : [ {
					data : this.minTime
				}, {
					data : this.avgTime
				}, {
					data : this.maxTime
				} ]
			})
		})
 	}
  }
};
</script>
<style scoped>

</style>