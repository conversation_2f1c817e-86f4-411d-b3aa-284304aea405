#socket连接次数
client.socket.retrytimes=1
#socket每次传输字节数
client.socket.buffersize=65536
#socket连接超时（0为不超时，单位为秒）
client.socket.timeout=60
#socket接收缓存大小
client.socket.receivebuffersize=65536
#socket发送缓存大小
client.socket.sendbuffersize=65536

client.socket.charset=UTF-8


#ssl.client.enable=false
#ssl.client.port=8025
#ssl.client.protocol=SSL
#ssl.client.keystore.location=/sunecm.keystore
#ssl.client.keystore.keypassword=123456
#ssl.client.keystore.password=123456
#
#
#ssl.client.do.not.authenticate.server = true
#ssl.client.truststore.type
#ssl.client.truststore.password
#ssl.client.truststore.location
#是否开启断点续传#,true为开启，false为不开启
openBreakPoint=false