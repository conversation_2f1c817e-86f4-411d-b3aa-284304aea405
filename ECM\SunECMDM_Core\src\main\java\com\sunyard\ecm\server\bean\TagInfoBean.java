package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;

import java.util.List;

/**
 * <p>
 * Title: 标签bean
 * </p>
 * <p>
 * Description: 标签信息
 * </p>
 * <p>
 * Copyright: Copyright (c) 2022
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@XStreamAlias("tagInfoBean")
public class TagInfoBean {
	private String tag_code;//标签代码
	private String tag_name;//标签名称
	private String tag_state;//标签状态
	private String attr_ids;//关联属性code
	private String model_ids;//关联模型code
	private List<TagRelAttributeBean> attrsList;// 属性列表
	private List<TagRelModelBean> tagRelModelBeans;//标签关联模型列表
	
	public String getTag_code() {
		return tag_code;
	}
	public String getTag_state() {
		return tag_state;
	}
	public void setTag_state(String tag_state) {
		this.tag_state = tag_state;
	}
	public void setTag_code(String tag_code) {
		this.tag_code = tag_code;
	}
	public String getTag_name() {
		return tag_name;
	}
	public void setTag_name(String tag_name) {
		this.tag_name = tag_name;
	}
	public String getAttr_ids() {
		return attr_ids;
	}
	public void setAttr_ids(String attr_ids) {
		this.attr_ids = attr_ids;
	}	
	public String getModel_ids() {
		return model_ids;
	}
	public void setModel_ids(String model_ids) {
		this.model_ids = model_ids;
	}
	public List<TagRelAttributeBean> getAttrsList() {
		return attrsList;
	}
	public void setAttrsList(List<TagRelAttributeBean> attrsList) {
		this.attrsList = attrsList;
	}
	public List<TagRelModelBean> getTagRelModelBeans() {
		return tagRelModelBeans;
	}
	public void setTagRelModelBeans(List<TagRelModelBean> tagRelModelBeans) {
		this.tagRelModelBeans = tagRelModelBeans;
	}	
}
