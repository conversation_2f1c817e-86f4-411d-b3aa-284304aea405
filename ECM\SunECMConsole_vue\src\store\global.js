export default {
  attributeMapping: {},
  desensitiveMapping: {},
  /**
 * 校验ip地址有效性
 */
  regexIP:/^([1-9]|[1-9]\d|1\d{2}|2[0-1]\d|22[0-3])(\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])){3}$/,
 /**
  * 以英文或者汉字开头，包括数字、字母、下划线
  */
  regexName:/^[a-zA-Z\u4E00-\u9FA5]+[a-zA-Z0-9_\u4e00-\u9fa5]*$/,
 /**
  * 显示regexName校验失败的提示信息
  */
  regexNameText:"以英文或者汉字开头，包括数字、字母、下划线",
 /**
  * 以英文字开头，包括数字、字母、下划线
  */
  regexCode: /^[a-zA-Z]+\w*$/,
  /**
  * 以小写英文开头，包括数字、小写字母、下划线（不允许使用大写字母）
  */
  regexLowerCode: /^[a-z]+[0-9a-z_]+$/,

  regNoSpecial:/^[\u4E00-\u9FA5A-Za-z0-9._]+$/,

 /**
  * 显示regexCode校验失败的提示信息
  */
  regexCodeText:"以英文开头，包括数字、字母、下划线",
  /**
  * 显示regexLowerCode校验失败的提示信息
  */
  regexLowerText:"以小写英文开头，包括数字、小写字母、下划线（不允许使用大写字母）",
 /**
  * 密码校验，包括数字，大小写字母,每个都需要出现
  */
  regexPasswordNeed3:/([A-Z]{1,}[a-z]{1,}\d{1,}|[A-Z]{1,}\d{1,}[a-z]{1,}|[a-z]{1,}[A-Z]{1,}\d{1,}|[a-z]{1,}\d{1,}[A-Z]{1,}|\d{1,}[a-z]{1,}[A-Z]{1,}|\d{1,}[A-Z]{1,}[a-z]{1,})[0-9a-zA-Z]{0,}$/,
  regexPasswordText:"包含英文大、小写、数字、特殊字符中的至少两种字符",
 /**
  *   * 密码校验，包含英文大、小写、数字、特殊字符中的至少两种字符
  */
  regexPasswordNeed2:/^(?![A-Z]+$)(?![a-z]+$)(?!\d+$)(?![\W_]+$)\S+$/,
  /**
  * 索引校验
  */
  regexIndex:/^[a-zA-Z\u4E00-\u9FA5]+\w*$/,
  regexIndexText:"以英文或者汉字开头，包括数字、字母、下划线",
  //pattern:/^[1-9]*[1-9][0-9]*$/
  regexNum:/^[0-9]*[1-9][0-9]*$/
}