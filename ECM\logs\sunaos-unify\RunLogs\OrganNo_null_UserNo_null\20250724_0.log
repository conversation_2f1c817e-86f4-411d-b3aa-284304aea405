2025-07-24 15:43:29.428 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/5a6bda27ecb15fac] [http-nio-9007-exec-88] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-24 15:43:29.500 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-24 15:43:29.501 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-24 15:43:29.501 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-24 15:43:29.504 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-24 15:43:29.505 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-24 15:43:29.505 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-24 15:43:29.508 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-24 15:43:29.509 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-24 15:43:29.509 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-24 15:43:29.511 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-24 15:43:29.512 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-24 15:43:29.512 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-24 15:43:29.512 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-24 15:43:29.523 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-24 15:43:29.524 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-24 15:43:29.524 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-24 15:43:29.524 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-24 15:43:29.529 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-24 15:43:29.530 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-24 15:43:29.530 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-24 15:43:29.533 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-24 15:43:29.533 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-24 15:43:29.533 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-24 15:43:29.535 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/bd35f9ac67103904] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-24 15:43:29.542 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/dde645f4a7735901] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-24 15:43:29.543 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/dde645f4a7735901] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-24 15:43:29.561 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/dde645f4a7735901] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-24 15:43:29.566 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/dde645f4a7735901] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-24 15:43:29.566 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/dde645f4a7735901] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-24 15:43:29.578 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/dde645f4a7735901] [http-nio-9007-exec-88] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-24 15:43:29.592 [OrganNo_null_UserNo_null] [3a55e5e9c89f1159/5a6bda27ecb15fac] [http-nio-9007-exec-88] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
2025-07-24 18:14:12.177 [OrganNo_null_UserNo_null] [cba1d956341a49b7/c209e4621a073652] [http-nio-9007-exec-72] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-24 18:14:12.218 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-24 18:14:12.219 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-24 18:14:12.219 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-24 18:14:12.222 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-24 18:14:12.222 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-24 18:14:12.222 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-24 18:14:12.226 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-24 18:14:12.227 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-24 18:14:12.227 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-24 18:14:12.232 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-24 18:14:12.232 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-24 18:14:12.233 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-24 18:14:12.236 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-24 18:14:12.237 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-24 18:14:12.237 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-24 18:14:12.237 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-24 18:14:12.238 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-24 18:14:12.241 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-24 18:14:12.242 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-24 18:14:12.243 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-24 18:14:12.245 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-24 18:14:12.245 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-24 18:14:12.245 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-24 18:14:12.247 [OrganNo_null_UserNo_null] [cba1d956341a49b7/bfd062dee463fce8] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-24 18:14:12.252 [OrganNo_null_UserNo_null] [cba1d956341a49b7/6f55d530f4f01d44] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-24 18:14:12.253 [OrganNo_null_UserNo_null] [cba1d956341a49b7/6f55d530f4f01d44] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-24 18:14:12.269 [OrganNo_null_UserNo_null] [cba1d956341a49b7/6f55d530f4f01d44] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-24 18:14:12.275 [OrganNo_null_UserNo_null] [cba1d956341a49b7/6f55d530f4f01d44] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-24 18:14:12.276 [OrganNo_null_UserNo_null] [cba1d956341a49b7/6f55d530f4f01d44] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-24 18:14:12.285 [OrganNo_null_UserNo_null] [cba1d956341a49b7/6f55d530f4f01d44] [http-nio-9007-exec-72] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-24 18:14:12.295 [OrganNo_null_UserNo_null] [cba1d956341a49b7/c209e4621a073652] [http-nio-9007-exec-72] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
2025-07-24 20:13:59.783 [OrganNo_null_UserNo_null] [94e412399e428fb4/1f3977a8f81b8ea3] [http-nio-9007-exec-95] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-24 20:13:59.853 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-24 20:13:59.854 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-24 20:13:59.854 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-24 20:13:59.858 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-24 20:13:59.858 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-24 20:13:59.858 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-24 20:13:59.861 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-24 20:13:59.861 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-24 20:13:59.862 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-24 20:13:59.865 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-24 20:13:59.865 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-24 20:13:59.865 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-24 20:13:59.866 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-24 20:13:59.876 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-24 20:13:59.876 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-24 20:13:59.876 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-24 20:13:59.877 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-24 20:13:59.879 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-24 20:13:59.880 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-24 20:13:59.880 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-24 20:13:59.882 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-24 20:13:59.883 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-24 20:13:59.883 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-24 20:13:59.885 [OrganNo_null_UserNo_null] [94e412399e428fb4/7002cc7b22ef40e0] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-24 20:13:59.890 [OrganNo_null_UserNo_null] [94e412399e428fb4/ff8f189e210e3f2f] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-24 20:13:59.891 [OrganNo_null_UserNo_null] [94e412399e428fb4/ff8f189e210e3f2f] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-24 20:13:59.907 [OrganNo_null_UserNo_null] [94e412399e428fb4/ff8f189e210e3f2f] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-24 20:13:59.912 [OrganNo_null_UserNo_null] [94e412399e428fb4/ff8f189e210e3f2f] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-24 20:13:59.912 [OrganNo_null_UserNo_null] [94e412399e428fb4/ff8f189e210e3f2f] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-24 20:13:59.919 [OrganNo_null_UserNo_null] [94e412399e428fb4/ff8f189e210e3f2f] [http-nio-9007-exec-95] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-24 20:13:59.931 [OrganNo_null_UserNo_null] [94e412399e428fb4/1f3977a8f81b8ea3] [http-nio-9007-exec-95] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
