<template>
  <div class="app-container">
    <div class="filter-container">
       <el-select v-model="listQuery.group_id" placeholder="请选择存储服务器组" @change='getServers()'>
        <el-option
          v-for="item in storeGroups"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
       <el-select v-model="listQuery.server_id" placeholder="请选择存储服务器">
        <el-option
          v-for="item in storeServers"
          :key="item.id"
          :label="item.text"
          :value="item.id">
        </el-option>
      </el-select>

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
     </div> 
   <el-button type="text" @click="queryBeforeOneHour()">{{btnText}}</el-button>
	<el-row :gutter="32">
		<el-col :xs="24" :sm="24" :lg="10">
			<div :id="canvas"  :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
	 </el-row>
  </div>

</template>

<script>
import {getContentServerGroup,getRelServers,getRelContentObject,getSocketConnNum} from '@/api/monitorManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import * as echarts from 'echarts'

export default {
    name: 'ComplexTable',
    components: { Pagination },
    directives: { waves,elDragDialog },
    filters: {
        statusFilter(status) {
        const statusMap = {
            published: 'success',
            draft: 'info',
            deleted: 'danger'
        }
        return statusMap[status]
    }
  },

  props: {
    canvas: {
      type: String,
      default: 'chart1'
    },
    width: {
      type: String,
      default: '1100px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
      default: '16px 16px'
    }
  },
  data() {
    return{
      event1:null,
      btnText : "显示当前数据",
      flag : true,//是否第一次访问
      storeGroups :  [],
      storeServers :  [],
      storeObj : [], 
      modelNames:[],
      model_names:"",
      listQuery: {
        importance: undefined,
        title: undefined,
        type: undefined,
        group_id:"",
        server_id:"",
      },
		  chart : null,
      socketText : '服务器模型socket连接数统计(前一小时数据)'
    }
  },
  
    mounted() {
      this.$nextTick(() => {
        this.showCharts()
      })
    },
    created() {
        this.getGroups()
    },

    beforeDestroy(){
      if(this.event1!=null){
        clearInterval(this.event1);
      }
    },

  methods: {
    handleclear() {
      this.listQuery.group_id= "";
      this.listQuery.server_id= "";
      this.listQuery.modelNames= "";
    },

    handleFilter() {
        this.objs = [];
        for (let i = 0; i < this.storeObj.length; i++) {
				  this.objs[i] = [];
			  }
        this.setChartParams();
        this.showCharts();
        this.flag = true;
        this.showData()
    },

    getGroups(){
        getContentServerGroup().then(response => {
        this.storeGroups = response.root;
      })
    },

    getServers(){
      getRelServers(this.listQuery).then(response => {
        this.storeServers = response.root;
      })
      getRelContentObject(this.listQuery).then(response => {
        this.storeObj = response.root;
        this.modelNames = [];
        this.model_names = '';
        for (let i = 0; i < this.storeObj.length; i++) {
          this.modelNames[i] = this.storeObj[i].id;
          if (i == 0) {
            this.model_names += this.storeObj[i].id;
          } else {
            this.model_names += ','+ this.storeObj[i].id;
          }
        }
      })
    },

    queryBeforeOneHour() {
			if (this.btnText == '显示当前数据') {
				this.btnText = '显示前一小时数据';
        this.socketText = '服务器模型socket连接数统计(当前数据)'
				this.init = 1;
				this.flag = false;
			} else {
        this.btnText = '显示当前数据';
        this.socketText = '服务器模型socket连接数统计(前一小时数据)'
				this.init = 0;
				this.flag = true;
			}
			for (let i = 0; i < this.modelNames.length; i++) {
				this.objs[i].data = [];
			}
        this.showCharts();
        this.showData()
		},

    setChartParams() {
			for (let i = 0; i < this.modelNames.length; i++) {
				this.objs[i] = {
          type : 'line',
					name : this.modelNames[i],
					//symbol:'star',//拐点样式
					symbolSize : 2,
					itemStyle : {
						normal : {
							lineStyle : {
								width : 1,//折线宽度
							//color:"#FF0000"//折线颜色
							}
						}
					},
					data : []
				}
			}
		},

    showCharts(){
      this.chart = echarts.init(document.getElementById(this.canvas), 'dark');
      let option = {
          title : {
            top : '3%',
            text : this.socketText,
            left : 'center'
          },
          tooltip : {
            trigger : 'axis',
            axisPointer : {
              show : true,
              type : 'cross',
              lineStyle : {
                type : 'dashed',
                width : 1
              }
            }
          },
          legend : {
            type:'scroll',
            pageIconColor:'#9cd9e4',
            orient : 'vertical',
            x : 'right',
            height : '40%',
            right:'3%',
            top: '4%',
            data : this.modelNames
          },
          grid : {
            top : '10%',
            left : '5%',
            right : '5%',
            bottom : '10%',
            containLabel : true
          },
          //保存圖片
          /*   toolbox: {
                feature: {
                    saveAsImage: {}
                }
            }, */
          xAxis : {
            type : 'time',
            scale : true,
            splitLine : {
              show : false
            }
          // boundaryGap: false,
          },
          yAxis : {
            type : 'value'
          },
          series : this.objs
		};
      this.chart.setOption(option);
    },

    timerEvent(){
			let map = new Map();
      this.listQuery.modelNames = this.model_names;
      this.listQuery.flag = this.flag;
			getSocketConnNum(this.listQuery).then(response => {
          for (let key in response) {
            if(key != "code"){
              if (map.hasOwnProperty(key)) {
                continue;
              } else {
                let oldParam = key.split('_');
                let param = ["",""];
                for(let i=0;i<oldParam.length-1;i++){
                  if(i==0){
                    param[0] = oldParam[i];
                  }else{
                  param[0] += "_"+oldParam[i];
                  }
                }
                param[1] = oldParam[oldParam.length-1];
                for (let i = 0; i < this.modelNames.length; i++) {
                  if (this.modelNames[i] == param[0]) {
                    map[key] = response[key];
                    let point = [
                        Number(param[1]),
                        Number(response[key]) ];
                    this.objs[i].data.push(point);
                    if (this.objs[i].data.length > 700) {
                      this.objs[i].data.shift();
                    }
                    break;
                  }
                }
              }
            }
          }
          this.chart.setOption({
            series : this.objs
          });
          this.flag = false;
			})
	  },
    showData(){
      clearInterval(this.event1);
      this.chart.hideLoading();
      this.timerEvent();
      this.event1 = setInterval(this.timerEvent, 5000);
	  }
  }
}
</script>

<style scoped>

</style>
