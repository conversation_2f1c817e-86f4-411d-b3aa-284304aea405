package com.sunyard.util;

/**
 * 文档对象系统默认字段
 * <AUTHOR>
 *
 */
public class FileObjSystemDefaultColumn {
	/** 内容编号 **/
	public final static String CONTENT_ID = "CONTENT_ID";
	/** 修改时间 **/
	public final static String UPLOAD_TIME = "UPLOAD_TIME";
	/** 修改人 **/
	public final static String UPLOAD_USER = "UPLOAD_USER";
	/** 检入检出用户 **/
	public final static String CHECK_USER = "CHECK_USER";
	/** 检出标识 1检出表示该批次被锁定 0检入表示该批次解锁 **/
	public final static String CHECK_FLAG = "CHECK_FLAG";
	/** 检出时间 12位 分 **/
	public final static String CHECK_TIME = "CHECK_TIME";
	/** 服务器编号 **/
	public final static String SERVER_ID = "SERVER_ID";
	/** 所在服务器组 **/
	public final static String GROUP_ID = "GROUP_ID";
	/** 版本 **/
	public final static String VERSION = "VERSION";
	/** 文件编号 **/
	public final static String FILE_NO = "FILE_NO";
	/** 文件格式 **/
	public final static String FILE_FORMAT = "FILE_FORMAT";
	/** 文件路径 **/
	public final static String FILE_PATH = "FILE_PATH";
	/** 操作类型 1新增 2删除 **/
	public final static String OPTION_TYPE = "OPTION_TYPE";
	/** 被覆盖文件号 **/
	public final static String COVER_FILE_NO = "COVER_FILE_NO";
	/** 批注路径 **/
	public final static String ANNO_PATH = "ANNO_PATH";
	/** 内容接收时间 **/
	public final static String RECEIVE_TIME = "RECEIVE_TIME";
	/** 文件大小 **/
	public final static String FILE_SIZE = "FILE_SIZE";
	/** 归档标志 0未归档 1已归档**/
	public final static String IS_ACTIVE = "IS_ACTIVE";
	/** 所属卷编号 **/
	public final static String VOLUME_ID = "VOLUME_ID";
	/** 内容状态  0不可用（表示逻辑删除）1可用**/
	public final static String CONTENT_STATUS = "CONTENT_STATUS";
	/** 迁移状态  1表示记录与文件都需要迁移 2已迁移 3记录待更新 **/
	public final static String MIGRATION_STATUS = "MIGRATION_STATUS";
	/**
	 * 近线内容路径
	 */
	public final static String NEAR_PATH = "NEAR_PATH";
	
	/**
	 * 文件名
	 */
	public final static String SAVE_NAME="SAVE_NAME";
	/**
	 * 加密的字节大小
	 * 部分加密格式为：加密前字节+"_"+加密后字节
	 * 全量加密为-1
	 */
	private static String ENCODESIZE="ENCODESIZE";
	/**
	 * 获取系统默认字段
	 * @return
	 */
	public final static String[] getColumns() {
		String[] strs = {CONTENT_ID, UPLOAD_TIME, UPLOAD_USER, CHECK_USER, CHECK_FLAG, CHECK_TIME, 
				SERVER_ID, GROUP_ID, VERSION, FILE_NO,  FILE_FORMAT, CONTENT_STATUS, FILE_PATH, 
				OPTION_TYPE, ANNO_PATH, RECEIVE_TIME, FILE_SIZE, IS_ACTIVE, VOLUME_ID, COVER_FILE_NO,
				MIGRATION_STATUS,SAVE_NAME,NEAR_PATH,ENCODESIZE};
		return strs;
	}
	
	public final static String getColumnsString() {
		return  "CONTENT_ID, UPLOAD_TIME, UPLOAD_USER, CHECK_USER, CHECK_FLAG, CHECK_TIME," +
				"SERVER_ID, GROUP_ID, VERSION, FILE_NO, FILE_FORMAT, CONTENT_STATUS, FILE_PATH," +
				"OPTION_TYPE, ANNO_PATH, RECEIVE_TIME, FILE_SIZE, IS_ACTIVE, VOLUME_ID, COVER_FILE_NO," +
				"MIGRATION_STATUS,SAVE_NAME,NEAR_PATH,ENCODESIZE,";
	};
}