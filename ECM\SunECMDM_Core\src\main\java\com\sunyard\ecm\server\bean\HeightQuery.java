package com.sunyard.ecm.server.bean;

import com.sunyard.client.bean.ClientHeightQuery;
import com.sunyard.ecm.server.bean.converter.StringCustomConverter;
import com.sunyard.util.CodeUtil;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamConverter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 高级搜索的返回结果bean对象
 * <AUTHOR>
 *
 */
@XStreamAlias("HeightQuery")
public class HeightQuery {
	@XStreamAsAttribute
	private String USER_NAME;// 用户名
	@XStreamAsAttribute
	private int COUNT;// 查询数据总数
	@XStreamAsAttribute
	private String TABLE_NAME;// 查询对象表
	@XStreamAsAttribute
	private int LIMIT;// 每一页的数据行数
	@XStreamAsAttribute
	private int PAGE;// 页码
	
	@XStreamAsAttribute
	private String MODEL_CODE; // 内容模型代码
	@XStreamConverter(StringCustomConverter.class)
	private Map<String, String> customAtt; // 自定义属性条件
	
	private List<String> filters;// 过滤条件
	private String groupId;
	
	private List<BatchIndexBean> indexBeans;// 

	@XStreamAsAttribute
	private String PASSWORD; // 密码
	private String NEED_OFFLINE;//是否需要查询离线
	
	public String getNEED_OFFLINE() {
		return NEED_OFFLINE;
	}

	public void setNEED_OFFLINE(String nEED_OFFLINE) {
		NEED_OFFLINE = nEED_OFFLINE;
	}

	public int getCount() {
		return COUNT;
	}

	public String getPASSWORD() {
		return PASSWORD;
	}

	public void setPASSWORD(String pASSWORD) {
		PASSWORD = pASSWORD;
	}

	public void setCount(int count) {
		this.COUNT = count;
	}

	public int getLimit() {
		return LIMIT;
	}

	public void setLimit(int limit) {
		this.LIMIT = limit;
	}

	public String getModelCode() {
		return MODEL_CODE;
	}

	public void setModelCode(String modelCode) {
		this.MODEL_CODE = modelCode;
	}

	public List<BatchIndexBean> getIndexBeans() {
		return indexBeans;
	}

	public void setIndexBeans(List<BatchIndexBean> indexBeans) {
		this.indexBeans = indexBeans;
	}

	public void addIndexBeans(BatchIndexBean indexBean) {
		if (indexBeans == null) {
			indexBeans = new ArrayList<BatchIndexBean>();
		}
		indexBeans.add(indexBean);
	}

	public int getPage() {
		return PAGE;
	}

	public void setPage(int page) {
		this.PAGE = page;
	}
	
	public Map<String, String> getCustomAtt() {
		return customAtt;
	}

	public void setCustomAtt(Map<String, String> customAtt) {
		this.customAtt = customAtt;
	}
	
	public void addCustomAtt(String key, String value) {
		if(customAtt == null) {
			customAtt = new HashMap<String, String>();
		}
		customAtt.put(key, value);
	}

	public List<String> getFilters() {
		return filters;
	}

	public void setFilters(List<String> filters) {
		this.filters = filters;
	}
	
	public void addfilters(String filter) {
		if(filters == null) {
			filters = new ArrayList<String>();
		}
		filters.add(filter);
	}

	public String getUserName() {
		return USER_NAME;
	}

	public void setUserName(String userName) {
		this.USER_NAME = userName;
	}

	public String getTableName() {
		return TABLE_NAME;
	}

	public void setTableName(String tableName) {
		this.TABLE_NAME = tableName;
	}
	
	public ClientHeightQuery heigehtbean2ClientHeightBean(){
		ClientHeightQuery clientheightbean = new ClientHeightQuery();
		clientheightbean.setUserName(this.USER_NAME);
		clientheightbean.setPassWord(CodeUtil.decode(this.PASSWORD));
		clientheightbean.setCount(this.COUNT);
		clientheightbean.setPage(this.PAGE);
		clientheightbean.setLimit(this.LIMIT);
		clientheightbean.setModelCode(this.MODEL_CODE);
		clientheightbean.setCustomAtt(this.getCustomAtt());
		clientheightbean.setFilters(this.filters);
		return clientheightbean;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	
}