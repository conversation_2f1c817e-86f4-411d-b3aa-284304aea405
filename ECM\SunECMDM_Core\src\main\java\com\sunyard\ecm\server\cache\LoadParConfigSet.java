package com.sunyard.ecm.server.cache;

import com.sunyard.ecm.server.bean.AllParamMsgBean;
import com.sunyard.ecm.server.cache.wsclient.GetServiceConfig;
import com.sunyard.exception.SunECMException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Title:参数配置列表信息内存管理类
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2020
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */

public class LoadParConfigSet {
	private Map<String, AllParamMsgBean> parObjSet;// 所有参数
	private Map<String, AllParamMsgBean> parGlobalObjSet;// 全局参数
	private Map<String, AllParamMsgBean> parGroupObjSet;// 作用于服务器组参数
	private Map<String, AllParamMsgBean> parGroupServerObjSet;// 作用于服务器组及服务器参数
	private Map<String, AllParamMsgBean> parModelObjSet;// 作用于模型参数
	private Map<String, AllParamMsgBean> parGroupModelObjSet;// 作用于服务器组及模型参数
	private Map<String, AllParamMsgBean> parGroupServerModelObjSet;// 作用于服务器组服务器及模型参数
	private GetServiceConfig getConfig = new GetServiceConfig();
	private final static Logger log = LoggerFactory.getLogger(LoadParConfigSet.class);

	/**
	 * DM初始化时获取所有参数配置列表信息
	 */
	public LoadParConfigSet() {
		setParamObjectSet();
	}

	public AllParamMsgBean getMetadataObjectSetBean(String objName) {
		AllParamMsgBean metaBean = parObjSet.get(objName);
		return metaBean;
	}

	public Map<String, AllParamMsgBean> getParObjectSetBean() {
		return parObjSet;
	}
	
	/**
	 * 初始化内容模型列表信息
	 */
	private void setParamObjectSet() {
		try {
			List<AllParamMsgBean> list = getConfig.getAllParConfigMsg();
			parObjSet = new HashMap<String, AllParamMsgBean>();
			parGlobalObjSet = new HashMap<String, AllParamMsgBean>();
			parGroupObjSet = new HashMap<String, AllParamMsgBean>();
			parGroupServerObjSet = new HashMap<String, AllParamMsgBean>();
			parModelObjSet = new HashMap<String, AllParamMsgBean>();
			parGroupModelObjSet = new HashMap<String, AllParamMsgBean>();
			parGroupServerModelObjSet = new HashMap<String, AllParamMsgBean>();
			for (AllParamMsgBean allParMsgBean : list) {
				parObjSet.put(allParMsgBean.getPAR_ID(), allParMsgBean);
				if (allParMsgBean.getPAR_ALL().equals("1")) {// 作用于全局
					if(allParMsgBean.getPAR_MODEL().equals("0")){
						parGlobalObjSet.put(allParMsgBean.getPAR_ID(), allParMsgBean);
					}else{//作用于模型
						parModelObjSet.put(allParMsgBean.getPAR_ID(), allParMsgBean);
					}
				}else if(!allParMsgBean.getPAR_GROUP().equals("0")){// 作用于服务器组参数
						if(allParMsgBean.getPAR_SERVER().equals("0")){
							if(allParMsgBean.getPAR_MODEL().equals("0")){
								parGroupObjSet.put(allParMsgBean.getPAR_ID(), allParMsgBean);
							}else{//作用于服务器组及模型
								parGroupModelObjSet.put(allParMsgBean.getPAR_ID(), allParMsgBean);
							}
						}else{//作用于服务器组及服务器
							if(allParMsgBean.getPAR_MODEL().equals("0")){
								parGroupServerObjSet.put(allParMsgBean.getPAR_ID(), allParMsgBean);
							}else{// 作用于服务器组服务器及模型
								parGroupServerModelObjSet.put(allParMsgBean.getPAR_ID(), allParMsgBean);
							}
						}
				}
			}
			log.debug("DM主动获取参数配置列表成功-->" + parObjSet);
		} catch (SunECMException e) {
			// webservice异常时，每隔20秒钟重新获取信息 直到获取成功
			log.error("DM主动获取参数配置列表失败", e);
			try {
				Thread.sleep(20 * 1000);
			} catch (InterruptedException e1) {
			}
			if (LazySingleton.run) {
				setParamObjectSet();
				log.error("DM主动获取参数配置列表失败", e);
			} else {
				log.info("serverStop");
			}

		}

	}

	/**
	 * 更新配置参数信息
	 */
	public void updateParObjectSet(AllParamMsgBean bean) {
		if (parGlobalObjSet == null) {
			parGlobalObjSet = new HashMap<String, AllParamMsgBean>();
		}
		if (parModelObjSet == null) {
			parModelObjSet = new HashMap<String, AllParamMsgBean>();
		}
		if (parGroupObjSet == null) {
			parGroupObjSet = new HashMap<String, AllParamMsgBean>();
		}
		if (parGroupModelObjSet == null) {
			parGroupModelObjSet = new HashMap<String, AllParamMsgBean>();
		}
		if (parGroupServerObjSet == null) {
			parGroupServerObjSet = new HashMap<String, AllParamMsgBean>();
		}
		if (parGroupServerModelObjSet == null) {
			parGroupServerModelObjSet = new HashMap<String, AllParamMsgBean>();
		}
		if (parObjSet == null) {
			parObjSet = new HashMap<String, AllParamMsgBean>();
		}
		parObjSet.put(bean.getPAR_ID(), bean);
		
		if (bean.getPAR_ALL().equals("1")) {// 全局参数
			parGlobalObjSet.put(bean.getPAR_ID(), bean);
		}else{
			if(bean.getPAR_GROUP().equals("0")){
				if(!bean.getPAR_MODEL().equals("0")){
					parModelObjSet.put(bean.getPAR_ID(), bean);
				}
			}else{// 作用于服务器组参数
				if(bean.getPAR_SERVER().equals("0")){
					if(bean.getPAR_MODEL().equals("0")){
						parGroupObjSet.put(bean.getPAR_ID(), bean);
					}else{//作用于服务器组及模型
						parGroupModelObjSet.put(bean.getPAR_ID(), bean);
					}
				}else{//作用于服务器组及服务器
					if(bean.getPAR_MODEL().equals("0")){
						parGroupServerObjSet.put(bean.getPAR_ID(), bean);
					}else{// 作用于服务器组服务器及模型
						parGroupServerModelObjSet.put(bean.getPAR_ID(), bean);
					}
				}
			}
		}
	}

	/**
	 * 获取作用于服务器组参数
	 */
	public AllParamMsgBean getGroup(String parKey, String groupId) {
		Iterator<AllParamMsgBean> ite = parGroupObjSet.values().iterator();
		while (ite.hasNext()) {
			AllParamMsgBean bean = ite.next();
			if (bean.getPAR_KEY().equals(parKey) && bean.getPAR_STATE().equals("1")) {
				if (bean.getPAR_GROUP().equals(groupId)) {
					log.debug("参数作用于服务器组" + bean);
					return bean;
				}
			}
		}
		return null;
	}

	/**
	 * 获取作用于服务器参数
	 */
	public AllParamMsgBean getGroupServer(String parKey, String groupId, String serverId) {
		Iterator<AllParamMsgBean> ite = parGroupServerObjSet.values().iterator();
		while (ite.hasNext()) {
			AllParamMsgBean bean = ite.next();
			if (bean.getPAR_KEY().equals(parKey) && bean.getPAR_STATE().equals("1")) {
				if (bean.getPAR_GROUP().equals(groupId)
						&& bean.getPAR_SERVER().equals(serverId)) {
					log.debug("参数作用于服务器" + bean);
					return bean;
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取作用于模型参数
	 */
	public AllParamMsgBean getModel(String parKey, String modelCode) {
		Iterator<AllParamMsgBean> ite = parModelObjSet.values().iterator();
		while (ite.hasNext()) {
			AllParamMsgBean bean = ite.next();
			if (bean.getPAR_KEY().equals(parKey) && bean.getPAR_STATE().equals("1")) {
				if (bean.getPAR_MODEL().equals(modelCode)) {
					log.debug("参数作用于模型" + bean);
					return bean;
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取作用于服务器组及模型参数
	 */
	public AllParamMsgBean getGroupModel(String parKey, String groupId, String modelCode) {
		Iterator<AllParamMsgBean> ite = parGroupModelObjSet.values().iterator();
		while (ite.hasNext()) {
			AllParamMsgBean bean = ite.next();
			if (bean.getPAR_KEY().equals(parKey) && bean.getPAR_STATE().equals("1")) {
				if (bean.getPAR_GROUP().equals(groupId)
						&& bean.getPAR_MODEL().equals(modelCode)) {
					log.debug("参数作用于服务器组及模型" + bean);
					return bean;
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取作用于服务器及模型参数
	 */
	public AllParamMsgBean getGroupServerModel(String parKey, String groupId, String serverId, String modelCode) {
		Iterator<AllParamMsgBean> ite = parGroupServerModelObjSet.values().iterator();
		while (ite.hasNext()) {
			AllParamMsgBean bean = ite.next();
			if (bean.getPAR_KEY().equals(parKey) && bean.getPAR_STATE().equals("1")) {
				if (bean.getPAR_GROUP().equals(groupId)
						&& bean.getPAR_SERVER().equals(serverId)
						&& bean.getPAR_MODEL().equals(modelCode)) {
					log.debug("参数作用于服务器组及服务器及模型" + bean);
					return bean;
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取全局参数接口
	 */
	public AllParamMsgBean getGlobalParKey(String parKey) {
		Iterator<AllParamMsgBean> ite = parGlobalObjSet.values().iterator();
		while (ite.hasNext()) {
			AllParamMsgBean bean = ite.next();
			if (bean.getPAR_KEY().equals(parKey) && bean.getPAR_STATE().equals("1")) {
				log.debug("参数作用于全局" + bean);
				return bean;
			}
		}
		log.warn("LoadParConfigSet-->getGlobalParKey-->no such parkey");
		return null;
	}
	
	/**
	 * 获取作用于服务器组参数接口
	 */
	public AllParamMsgBean getGroupParKey(String parKey, String groupId) {
		AllParamMsgBean bean = getGroup(parKey, groupId);
		if(bean == null){
			return getGlobalParKey(parKey);
		}else{
			return bean;
		}
	}

	/**
	 * 获取作用于服务器参数接口
	 */
	public AllParamMsgBean getGroupServerParKey(String parKey, String groupId, String serverId) {
		AllParamMsgBean bean = getGroupServer(parKey, groupId, serverId);
		if(bean == null){
			return getGroupParKey(parKey, groupId);
		}else{
			return bean;
		}
	}
	
	/**
	 * 获取作用于模型参数接口
	 */
	public AllParamMsgBean getModelParKey(String parKey, String modelCode) {
		AllParamMsgBean bean = getModel(parKey, modelCode);
		if(bean == null){
			return getGlobalParKey(parKey);
		}else{
			return bean;
		}
	}

	/**
	 * 获取作用于服务器组及模型参数接口
	 */
	public AllParamMsgBean getGroupModelParKey(String parKey, String groupId, String modelCode) {
		AllParamMsgBean bean = getGroupModel(parKey, groupId, modelCode);
		if(bean == null){
			return getModelParKey(parKey, modelCode);
		}else{
			return bean;
		}
	}

	/**
	 * 获取作用于服务器及模型参数接口
	 */
	public AllParamMsgBean getGroupServerModelParKey(String parKey, String groupId, String serverId, String modelCode) {
		AllParamMsgBean bean = getGroupServerModel(parKey, groupId, serverId, modelCode);
		if(bean == null){
			bean = getGroupModel(parKey, groupId, modelCode);
			if(bean == null){
				bean = getModel(parKey, modelCode);
				if(bean == null){
					bean = getGroupServerParKey(parKey, groupId, serverId);
				}	
			}
		}
		return bean;
	}
}