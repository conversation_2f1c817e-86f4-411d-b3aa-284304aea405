package com.sunyard.console.sendserverinfo;

import com.sunyard.console.common.config.LoadConfigFile;
import com.sunyard.console.configmanager.bean.NodeInfo;
import com.sunyard.ws.utils.XMLUtil;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

public class WsSendNodeThread implements Runnable {

    private final static Logger log = LoggerFactory.getLogger(WsSendNodeThread.class);

    private NodeInfo nodeInfo;

    private CountDownLatch countDownLatch;

    private  boolean isUa;

    public WsSendNodeThread(NodeInfo nodeInfo, boolean isUa ,CountDownLatch countDownLatch){
        this.nodeInfo = nodeInfo;
        this.isUa = isUa;
        this.countDownLatch = countDownLatch;
    }

    @Override
    public void run() {
        try {
            long timeout = 3000;
            String xml = XMLUtil.bean2XML(nodeInfo);
            String ipAndPort = nodeInfo.getServer_ip()+":"+nodeInfo.getHttp_port();
            String url = "";
            if (isUa){
                url = createUAURL(ipAndPort, "http");
            } else {
                url = createDMURL(ipAndPort, "http");
            }
//            SunEcmAccess wsInterface = new WSAccessClient().getAccessClient(url, timeout);
//            wsInterface.setServerInfo(xml);
            Map<String, String> paraMap = new HashMap<String, String>();
            paraMap.put("xml", xml);
            doPost(url, paraMap);
        } catch (Exception e){
            log.error("console下发node信息失败", e);
        } finally {
            countDownLatch.countDown();
        }
    }

    public String createDMURL(String IPandPort, String type) {
//        String str = StringUtil.assemblyDMUrl(IPandPort, type);
        String str = "http://"+IPandPort+"/"+ LoadConfigFile.getConfigBean().getServer_DM_Name()+"/setServerInfo";
        return str;
    }

    public String createUAURL(String IPandPort, String type) {
//        String str = StringUtil.assemblyUAUrl(IPandPort, type);
        String str = "http://"+IPandPort+"/"+ LoadConfigFile.getConfigBean().getServer_UA_Name()+"/setServerInfo";
        return str;
    }

    private String doPost(String url, Map<String, String> param) throws IOException {
        HttpPost httpPost = new HttpPost(url);

        List<NameValuePair> pairs = new ArrayList<NameValuePair>();
        for (Map.Entry<String, String> entry : param.entrySet()) {
            pairs.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
        }

        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(3000).setConnectionRequestTimeout(6000).build();

        httpPost.setConfig(requestConfig);

        CloseableHttpClient client = HttpClientBuilder.create().build();

        String result = "";
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(pairs, "UTF-8"));
            HttpResponse response = client.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                result = EntityUtils.toString(entity, "UTF-8");
            }
        }  finally {
            if (httpPost != null) {
                httpPost.abort();
            }
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

}
