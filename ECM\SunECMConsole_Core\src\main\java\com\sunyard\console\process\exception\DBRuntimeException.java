package com.sunyard.console.process.exception;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * <p>Title: 数据库操作运行时异常类</p>
 * <p>Description: 数据库操作的过程中产生的异常,如:SQL语句错误等</p>
 * <p>Copyright: Copyright (c) 2010</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class DBRuntimeException extends RuntimeException{
	private static Log logger = LogFactory.getLog("dbProblem");
	public DBRuntimeException(){
		super();
		logger.error("数据库操作异常!!!");
	}
	
	public DBRuntimeException(String msg){
		logger.error(msg);
	}
}
