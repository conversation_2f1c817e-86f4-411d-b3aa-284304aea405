package com.sunyard.console.sendserverinfo;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.configmanager.bean.NodeInfo;
import com.sunyard.console.process.exception.DBRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ServerInfoDaoImpl implements ServerInfoDao{

    private final static Logger log = LoggerFactory.getLogger(ServerInfoDaoImpl.class);

    @Override
    public List<NodeInfo> getAllServer() {
        log.info( "--getAllServer(start)--");
        List<NodeInfo> beanList = null;
        StringBuffer sql = new StringBuffer();
        sql
                .append("SELECT    SERVER_ID,    SERVER_NAME,    SERVER_IP,    HTTP_PORT,    REMARK,    STATE,    ISDB_CONN,    SOCKET_PORT,    GROUP_ID, HTTPS_PORT,   WEIGHT ,TRANS_PROTOCOL FROM   CONTENT_SERVER_INFO ");
        sql.append(" WHERE 1=1");
        try {
            log.debug( "--getAllServer-->sql:" + sql.toString());
            beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
                    NodeInfo.class);
            sql = null;
        } catch (Exception e) {
            // 记录日志
            log.error( "内容存储服务器管理->查询内容存储服务器失败:" + e.toString(), e);

            throw new DBRuntimeException(
                    "ServerInfoDaoImpl===>getAllServer--:"
                            + e.getMessage());
        }
        log.info( "--getAllServer(over)-->beanList:"+beanList);
        return beanList;
    }

    @Override
    public List<NodeInfo> getUaserver() {
        log.info( "--getUaserver (start)--");
        List<NodeInfo> beanList = new ArrayList<NodeInfo>();
        StringBuffer sql = new StringBuffer();
        sql
                .append("SELECT    SERVER_ID,    SERVER_NAME,    SERVER_IP,    HTTP_PORT,    REMARK,    STATE,    SOCKET_PORT,    GROUP_ID, HTTPS_PORT,TRANS_PROTOCOL FROM   UNITY_ACCESS_SERVER");
        sql.append(" WHERE 1=1");
        try {
            log.debug( "--getUaserver-->sql:" + sql.toString());
            beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
                    NodeInfo.class);
            sql = null;
        } catch (Exception e) {
            // 记录日志
            log.error( "内容存储服务器管理->查询UA存储服务器失败:" + e.toString(), e);

            throw new DBRuntimeException(
                    "ServerInfoDaoImpl===>getUaserver--:"
                            + e.getMessage());
        }
        log.info( "--getUaserver(over)-->beanList:"+beanList);
        return beanList;
    }
}
