package com.sunyard.client.bean;

import java.util.ArrayList;
import java.util.List;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

/**
 * 上传、更新、查询的批次信息bean对象
 * 
 * <AUTHOR>
 */
@XStreamAlias("BatchBean")
public class ClientBatchBean {
	@XStreamAsAttribute
	private boolean IS_UNITED_ACCESS; // 是否由统一接入服务器转发
	@XStreamAsAttribute
	private String MODEL_CODE; // 内容模型代码
	@XStreamAsAttribute
	private String TOKEN_CODE;// 令牌数值
	@XStreamAsAttribute
	private String TOKEN_CHECK_VALUE;// 令牌校验标识，申请令牌时由客户端提供的数值
	@XStreamAsAttribute
	private String USER;// 用户名
	@XStreamAsAttribute
	private String CHECK_TOKEN;// 检入检出随机数
	@XStreamAsAttribute
	private boolean IS_BREAK_POINT;// 是否断点续传
	@XStreamAsAttribute
	private boolean IS_OWN_MD5;// 是否设置MD5码
	@XStreamAsAttribute
	private boolean IS_DOWNLOAD;// 是否设置下拉
	@XStreamAsAttribute
	private String PASSWORD;//密码
	
	@XStreamAsAttribute
	private String PASSWD;//密码
	
	/**
	 * 令牌
	 */
	@XStreamOmitField
	private String token;
	@XStreamOmitField
	private boolean isCompress=false;
	
	
	
	public boolean isCompress() {
		return isCompress;
	}


	public void setCompress(boolean isCompress) {
		this.isCompress = isCompress;
	}


	public String getToken() {
		return token;
	}


	public void setToken(String token) {
		this.token = token;
	}

	private ClientBatchIndexBean index_Object; // 索引信息
	
	private List<ClientBatchFileBean> document_Objects;// 文档信息

	public String getModelCode() {
		return MODEL_CODE;
	}


	public String getPassWord() {
		return PASSWORD;
	}


	public void setPassWord(String passWord) {
		this.PASSWORD = passWord;
	}


	/**
	 * 内容ID,批次唯一
	 * @param modelCode 内容ID
	 */
	public void setModelCode(String modelCode) {
		this.MODEL_CODE = modelCode;
	}

	public String getToken_code() {
		return TOKEN_CODE;
	}

	/**
	 * 令牌校验所需的令牌数值
	 * @param tokenCode 令牌数值
	 */
	public void setToken_code(String tokenCode) {
		TOKEN_CODE = tokenCode;
	}

	public String getUser() {
		return USER;
	}

	/**
	 * 操作用户名
	 * @param user 用户名
	 */
	public void setUser(String user) {
		this.USER = user;
	}

	public ClientBatchIndexBean getIndex_Object() {
		if(index_Object == null) {
			index_Object = new ClientBatchIndexBean();
		}
		return index_Object;
	}

	/**
	 * 设置批次索引信息
	 * @param indexObject 批次索引信息
	 */
	public void setIndex_Object(ClientBatchIndexBean indexObject) {
		index_Object = indexObject;
	}

	public List<ClientBatchFileBean> getDocument_Objects() {
		if(document_Objects == null){
			document_Objects = new ArrayList<ClientBatchFileBean>();
		}
		return document_Objects;
	}

	/**
	 * 设置批次文档部件信息
	 * @param documentObjects 批次文档部件队列List<ClientBatchFileBean> 
	 */
	public void setDocument_Objects(List<ClientBatchFileBean> documentObjects) {
		document_Objects = documentObjects;
	}

	/**
	 * 添加文档部件
	 * @param documentObject 文档部件信息ClientBatchFileBean
	 */
	public void addDocument_Object(ClientBatchFileBean documentObject) {
		if(document_Objects == null) {
			document_Objects = new ArrayList<ClientBatchFileBean>();
		}
		this.document_Objects.add(documentObject);
	}

	public String getToken_check_value() {
		return TOKEN_CHECK_VALUE;
	}
	
	/**
	 * 设定令牌校验标识，申请令牌时由客户端提供的数值
	 * @param tokenName 令牌校验标识，申请令牌时由客户端提供的数值
	 */
	public void setToken_check_value(String tokenCheckValue) {
		TOKEN_CHECK_VALUE = tokenCheckValue;
	}

	public String getCheckToken() {
		return CHECK_TOKEN;
	}

	/**
	 * 设定检入检出随机数
	 * @param checkToken 检入检出随机数
	 */
	public void setCheckToken(String checkToken) {
		this.CHECK_TOKEN = checkToken;
	}

	public boolean isBreakPoint() {
		return IS_BREAK_POINT;
	}

	/**
	 * 设定是否断点续传
	 * @param isBreakPoint 设定是否断点续传
	 */
	public void setBreakPoint(boolean isBreakPoint) {
		this.IS_BREAK_POINT = isBreakPoint;
	}

	public boolean isOwnMD5() {
		return IS_OWN_MD5;
	}

	/**
	 * 是否为批次上传的文件添加MD5码
	 * @param isOwnMD5
	 */
	public void setOwnMD5(boolean isOwnMD5) {
		this.IS_OWN_MD5 = isOwnMD5;
	}

	public boolean isDownLoad() {
		return IS_DOWNLOAD;
	}

	/**
	 * 是否设置下拉
	 * @param isDownLoad
	 */
	public void setDownLoad(boolean isDownLoad) {
		this.IS_DOWNLOAD = isDownLoad;
	}

	public String getPASSWD() {
		return PASSWD;
	}

	
	public void setPASSWD(String pASSWD) {
		PASSWD = pASSWD;
	}
	
	public boolean isUnitedAccess() {
		return IS_UNITED_ACCESS;
	}

	public void setUnitedAccess(boolean isUnitedAccess) {
		this.IS_UNITED_ACCESS = isUnitedAccess;
	}	
}