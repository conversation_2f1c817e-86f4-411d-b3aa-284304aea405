package com.sunyard.console.sendserverinfo;

import com.sunyard.console.configmanager.bean.NodeInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CountDownLatch;

@Component
public class SendServerInfo {

    private final static Logger log = LoggerFactory.getLogger(SendServerInfo.class);

    @Autowired
    private ServerInfoDao serverInfoDao;

    public void sendServerInfo(){
        List<NodeInfo> nodes = serverInfoDao.getAllServer();
        List<NodeInfo> uaNodes = serverInfoDao.getUaserver();
        if (nodes!=null&&uaNodes!=null){
            CountDownLatch countDownLatch = new CountDownLatch(nodes.size()+uaNodes.size());
            for (NodeInfo nodeInfo : nodes){
                new  Thread(new WsSendNodeThread(nodeInfo,false,countDownLatch)).start();
            }
            for (NodeInfo nodeInfo : uaNodes) {
                new  Thread(new WsSendNodeThread(nodeInfo,true,countDownLatch)).start();
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public void sendServerInfo(NodeInfo nodeInfo){
       if(nodeInfo!=null){
           CountDownLatch countDownLatch = new CountDownLatch(1);
           new  Thread(new WsSendNodeThread(nodeInfo,false,countDownLatch)).start();
           try {
               countDownLatch.await();
           } catch (InterruptedException e) {
               e.printStackTrace();
           }
       }
    }

    public void sendUaServerInfo(NodeInfo nodeInfo){
        if(nodeInfo!=null){
            CountDownLatch countDownLatch = new CountDownLatch(1);
            new  Thread(new WsSendNodeThread(nodeInfo,true,countDownLatch)).start();
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

}
