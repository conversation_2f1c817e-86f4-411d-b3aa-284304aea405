<template>
  <div>
	 <el-date-picker v-model="queryMonth" type="month" value-format="yyyy-MM">
	 </el-date-picker>	
	<el-button type="text" @click="queryMonthData()">查询当月数据</el-button>
	
	<el-row :gutter="32">
		<el-col :xs="24" :sm="24" :lg="10">
			<div :id="fileCanvas"  :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
	 </el-row>
  </div>
</template>


<style>

</style>

<script>
import {getCountFileAction} from '@/api/monitorManage'
import * as echarts from 'echarts'

export default {
  name: "file-total-rel",
    props: {
	listQuery: {
      require: true,
      type: Object,
    },
    fileCanvas: {
      type: String,
      default: 'filechart'
    },
    width: {
      type: String,
      default: '1100px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
      default: '16px 16px'
    }
  },
  data() {
    return{
		init : 0,
		sumFile : [], 
		filechart : null,
		days : [],
		queryMonth:this.getNowMonth(),
		echartTitle : ""
	}
  },
  
    mounted() {
      this.$nextTick(() => {
		//   this.checkInit();
      })
    },


  methods: {
	getNowMonth(){
		let now = new Date();
		let year = now.getFullYear();
		let month = now.getMonth()+1;
		month = month < 10 ? "0" + month : month;

		return `${year}-${month}`;
	},

	checkInit(){
		let taskoption = this.listQuery.option;
		if(taskoption == 'UPLOAD'){
			this.echartTitle = "上传"
		}else if(taskoption == 'UPDATE'){
			this.echartTitle = "更新"
		}
		if (this.init == 0) {
			let selYear = this.queryMonth.split("-")[0];
			let selMonth = this.queryMonth.split("-")[1];
			this.time = new Date(selYear,selMonth, 0).getDate();
			this.days = this.getDays();
			this.showFileMonthChart();
			this.showFileMonthData();
		}
	},
	queryMonthData() {
		let selYear = this.queryMonth.split("-")[0];
		let selMonth = this.queryMonth.split("-")[1];
		this.time = new Date(selYear,selMonth, 0).getDate();
		this.days = this.getDays();
		this.showFileMonthChart();
		this.showFileMonthData();
	},

	getDays() {
		let temp = [];
		for (let i = 1; i <= this.time; i++) {
			temp.push(i);
			this.sumFile.push[0];
		}
		return temp;
	},
    showFileMonthChart(){
      	this.filechart = echarts.init(document.getElementById(this.fileCanvas), 'dark');
        let option = {
			color : [ '#61a0a8' ],
			title : {
				text : this.echartTitle + '接口文件接收数量',
				x : 'center',
				textStyle : {
					color : '#e2e9ff',
				},
			},
			tooltip : {
				trigger : 'axis',
				axisPointer : { // 坐标轴指示器，坐标轴触发有效
					type : 'shadow' // 默认为直线，可选为：'line' | 'shadow'
				}
			},
			grid : { //控制图的大小
				left : '3%',
				right : '5%',
				bottom : '3%',
				containLabel : true
			},
			xAxis : [ {
				name : 'Day',
				axisLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				axisLabel : {
					margin : 10,
					color : '#e2e9ff',
					textStyle : {
						fontSize : 12
					},
				},
				/* triggerEvent:{
					componentType: 'xAxis',
					value: '',
					name: 'trigger'
					}, */
				type : 'category',
				data : this.days
			} ],
			yAxis : [ {
				name : '(张)',
				nameTextStyle : {
					color : "#e2e9ff"
				},
				type : 'value',
				axisLabel : {
					formatter : '{value}',
					color : '#e2e9ff',
				},
				axisLine : {
					show : false
				},
				splitLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				}
			} ],
			series : [ {
				itemStyle : {
					normal : {
						color : new echarts.graphic.LinearGradient(0,
								0, 0, 1, [ {
									offset : 0,
									color : 'rgba(0,244,255,1)' // 0% 处的颜色
								}, {
									offset : 1,
									color : 'rgba(0,77,167,1)' // 100% 处的颜色
								} ], false),
						barBorderRadius : [ 30, 30, 30, 30 ],
						shadowColor : 'rgba(0,160,221,1)',
						shadowBlur : 4,
					}
				},
				type : 'bar',
				barWidth : '35%',
				stack : '数量',
				// 各事件对应的成功个数
				data : this.sumFile
			} ]
		};

    this.filechart.setOption(option);
    },

    showFileMonthData(){
		this.listQuery.date = this.queryMonth.replace("-","");
		getCountFileAction(this.listQuery).then(response => {
			this.filechart.hideLoading();
			this.sumFile = [];

			// let date = [];
			for (let i = 1; i <= this.time; i++) {
				if (i <= 9) {
					i = '0' + i;
				}
				if (response['' + i] != "undefined"
						&& response['' + i] != null) {
					this.sumFile.push(response['' + i].sumFile);
				} else {
					this.sumFile.push(0);
				}
			}

			this.filechart.setOption({
				xAxis : {
					data : this.days
				},
				series : [ {
					data : this.sumFile
				} ]
				})
		})
 	}
  }
};
</script>
<style scoped>

</style>