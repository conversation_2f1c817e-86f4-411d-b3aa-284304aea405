package com.sunyard.ecm.server.event.sample;

import java.text.MessageFormat;
import java.util.Calendar;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import com.sunyard.ecm.server.event.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.ecm.server.event.listener.BatchHandleListener;
import com.sunyard.ecm.server.event.listener.ConnectionListener;
import com.sunyard.ecm.server.event.listener.FileListener;

public class SimpleBatchHandleListener implements BatchHandleListener,
		FileListener, ConnectionListener, Runnable {
	private static final int sleepTime = 5;
	private final static Logger log = LoggerFactory
			.getLogger(SimpleBatchHandleListener.class);
	private Thread t;

	public SimpleBatchHandleListener() {
		super();
		t = new Thread(this, "监听简单实现");
		t.start();
		hour = getCurrentHour();
	}

	public static int getCurrentHour() {
		Calendar now = Calendar.getInstance();
		return now.get(Calendar.HOUR_OF_DAY);
	}

	public static int getCurrentSecond() {
		Calendar now = Calendar.getInstance();
		return now.get(Calendar.SECOND);
	}

	private int hour;
	private int uploadCount = 0;
	private long uploadTotalTime = 0;
	private long uploadFileTime = 0;
	private int updateCount = 0;
	private int deleteCount = 0;

	public void reset() {
		uploadCount = 0;
		uploadTotalTime = 0;
		uploadFileTime = 0;
		updateCount = 0;
		deleteCount = 0;
		count = new AtomicInteger(0);
		totalSize = new AtomicLong(0);
		totalTimes = new AtomicLong(0);
	}

	public boolean onUploadStart(BatchUploadStartEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onUploadEnd(BatchUploadEndEvent e) {
		uploadCount++;
		uploadTotalTime += e.getDuration();
		uploadFileTime += e.getFileUploadTime();
		return false;
	}

	public boolean onUpdateStart(BatchUpdateStartEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onUpdateEnd(BatchUpdateEndEvent e) {
		updateCount++;
		return false;
	}

	public boolean onDelete(BatchDeleteEvent e) {
		deleteCount++;
		return false;
	}

	private boolean running = true;

	public void run() {
		log.info("启动简易事件监听程序");
		int i = 1;
		while (running) {
			try {
				Thread.sleep(sleepTime * 1000);
			} catch (InterruptedException e) {
			}
			int second = getCurrentSecond();
			if (i > 3) {
				log.debug("当前连接数:{}", connectionCount.get());
				i=1;
			}
			if (second >= sleepTime) {
				i++;
				continue;
			}

			if (hour != getCurrentHour()) {
				hour = getCurrentHour();
				reset();
			}

			if (uploadCount > 0) {
				if (log.isInfoEnabled()) {
					log.info("{}点之后的数据统计:{}", hour, getInfo());
				}

			} else {
				if (log.isInfoEnabled()) {
					log.info("{}点之后的数据统计:{}", hour, "无新批次上传");
				}
			}
		}
		log.info("简易事件监听程序结束");
	}

	public String getInfo() {
		StringBuilder str = new StringBuilder();
		str.append("\r\n");
		str.append(MessageFormat.format(
				"一共上传了{0}个批次，平均时间是:{1}ms，批次文件接收耗时：{2}ms", new Object[] {
						uploadCount, uploadTotalTime / uploadCount,
						uploadFileTime / uploadCount }));
		str.append("\r\n");
		if(count.get()>0){
		str.append(MessageFormat.format(
				"接收文件个数{0}，平均大小:{1}KB，平均上传时间:{2}ms，平均速度：{3}mBps",
				new Object[] { count, totalSize.get() / 1024 / count.get(),
						totalTimes.get() / count.get(),
						totalSize.get() * 1000 / 1024 / 1024 / totalTimes.get() }));
		}
		return str.toString();
	}

	public void stop() {
		log.info("stop simple listener");
		running = false;
		try {
			t.join(10000);
		} catch (InterruptedException e) {
			log.error("等待简易监听线程关闭超时：", e);
		}
		log.info("simple listener已关闭");
	}

	private AtomicInteger count = new AtomicInteger(0);
	private AtomicLong totalSize = new AtomicLong(0);
	private AtomicLong totalTimes = new AtomicLong(0);

	public boolean onUploadStart(FileStartEvent event) {
		return false;
	}

	public boolean onUploadEnd(FileEndEvent event) {
		count.getAndIncrement();
		totalSize.addAndGet(event.getFileSize());
		totalTimes.addAndGet(event.getDuration());
		return false;
	}

	private AtomicInteger connectionCount = new AtomicInteger(0);

	public boolean onAccept(ConnectionOpenEvent e) {
		connectionCount.getAndIncrement();
		return false;
	}

	public boolean onClose(ConnectionCloseEvent e) {
		connectionCount.getAndDecrement();
		return false;
	}

	public boolean onMigrateStart(BatchMigrateStartEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onMigrateEnd(BatchMigrateEndEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

}
