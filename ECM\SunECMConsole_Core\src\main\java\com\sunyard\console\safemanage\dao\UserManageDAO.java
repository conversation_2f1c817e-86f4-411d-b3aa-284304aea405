package com.sunyard.console.safemanage.dao;

import com.sunyard.console.safemanage.bean.NodeBean;
import com.sunyard.console.safemanage.bean.UserCmodelRelBean;
import com.sunyard.console.safemanage.bean.UserInfoBean;

import java.util.List;


/**
 * <p>Title: 用户管理数据库操作接口类</p>
 * <p>Description: 定义用户管理中各类数据库操作的方法</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public interface UserManageDAO {
	/**
	 * 根据输入信息查询用户列表
	 * @param userID	用户编号
	 * @param userName	用户名称
	 * @param userState	用户状态
	 * @param RoleID	角色ID
	 * @param start
	 * @param limit
	 * @return			用户信息列表
	 */
	public List<UserInfoBean> searchUserInfoList(String userID, String userName, String userState, String roleID, int start, int limit);
	
	/**
	 * 根据输入信息查询用户列表(取全部记录)
	 * @param userID	用户编号
	 * @param userName	用户名称
	 * @param userState 用户状态
	 * @param roleID	角色编号
	 * @return			用户信息列表
	 */
	public List<UserInfoBean> searchAllUserInfoList(String userID, String userName, String userState, String roleID);
	/**
	 * 根据输入的信息创建新的用户
	 * @param user	用户对象
	 * @return 是否成功
	 */
	public boolean addUser(UserInfoBean user);
	
	/**
	 * 根据用户ID来修改用户信息
	 * @param user		用户对象
	 * @return 			是否成功
	 */
	public boolean modifyUser(UserInfoBean user);
		
	/**
	 * 启用、禁用用户
	 * @param 	userIDs 	用户ID集合
	 * @param 	user_state 	用户状态
	 * @return 				是否成功
	 */
	public boolean modifyUserState(String userIDs , String userState);
	
	/**
	 * 重置密码
	 * @param password 	用户新密码
	 * @param userID	用户ID
	 * @return 			是否成功
	 */
	public boolean resetUserPwd(String userID , String password);
	
	/**
	 * 修改密码
	 * @param oldPwd	用户原始密码
	 * @param newPwd	用户新密码
	 * @param userID	用户ID
	 * @return 			是否成功
	 */
	public int mofifyUserPwd(String oldPwd , String newPwd , String userID);
	
	/**
	 * 获取用户已有的权限
	 * @param userIDs
	 * @return
	 */
	public List<NodeBean> getExistsComponents(String userIDs);

	/**
	 * 获取用户没有的权限
	 * @param userIDs
	 * @return
	 */
	public List<NodeBean> getNotExistsComponents(String userIDs);

	/**
	 * 修改用户权限
	 * @param userIDs
	 * @param componentIDs
	 * @return 
	 */
	public boolean updateUserComponents(String userIDs, String componentIDs);
	
	/**
	 * 获取用户已有的角色
	 * @param userIDs
	 * @return
	 */
	public List<NodeBean> getUserExistRoles(String userIDs);

	/**
	 * 获取用户没有的角色
	 * @param user_id
	 * @return
	 */
	public List<NodeBean> getUserNotExistRoles(String userIDs);

	/**
	 * 修改用户的角色
	 * @param userIDs
	 * @param roleIDs
	 */
	public boolean updateUserRoles(String userIDs, String roleIDs);
	
	/**
	 * 获取用户对内容对象操作权限
	 * @param user_ids
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<UserCmodelRelBean> getConferList(String user_id,int start,int limit);
	
	/**
	 * 获取用户对内容对象操作权限(取全部记录)
	 * @param user_ids
	 * @return
	 */
	public List<UserCmodelRelBean> getConferAllList(String user_id);
	
	/**
	 * 配置内容对象操作权限
	 * @param user_ids
	 * @param model_codes
	 * @param permission_code
	 * @return
	 */
	public boolean configConfer(String user_id,String model_codes,String permission_code);
}
