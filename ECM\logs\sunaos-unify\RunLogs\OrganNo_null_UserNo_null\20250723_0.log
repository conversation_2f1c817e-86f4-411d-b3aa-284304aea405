2025-07-23 09:33:05.971 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/93de2f40abd3ab6a] [http-nio-9007-exec-39] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-23 09:33:06.044 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-23 09:33:06.045 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-23 09:33:06.045 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-23 09:33:06.048 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-23 09:33:06.048 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-23 09:33:06.048 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-23 09:33:06.051 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-23 09:33:06.052 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-23 09:33:06.056 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-23 09:33:06.059 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-23 09:33:06.059 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-23 09:33:06.060 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-23 09:33:06.062 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-23 09:33:06.073 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-23 09:33:06.073 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-23 09:33:06.073 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-23 09:33:06.074 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-23 09:33:06.076 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-23 09:33:06.082 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-23 09:33:06.082 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-23 09:33:06.084 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-23 09:33:06.085 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-23 09:33:06.085 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-23 09:33:06.088 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/65070f2f1d7a58c1] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-23 09:33:06.097 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/73024bf2a53b5ecc] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-23 09:33:06.098 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/73024bf2a53b5ecc] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-23 09:33:06.117 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/73024bf2a53b5ecc] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-23 09:33:06.121 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/73024bf2a53b5ecc] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-23 09:33:06.122 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/73024bf2a53b5ecc] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-23 09:33:06.131 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/73024bf2a53b5ecc] [http-nio-9007-exec-39] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-23 09:33:06.142 [OrganNo_null_UserNo_null] [6ced5cfa5b951034/93de2f40abd3ab6a] [http-nio-9007-exec-39] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
2025-07-23 13:35:27.528 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/c79e348593be0b25] [http-nio-9007-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-23 13:35:27.592 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-23 13:35:27.593 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-23 13:35:27.593 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-23 13:35:27.598 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-23 13:35:27.598 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-23 13:35:27.598 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-23 13:35:27.601 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-23 13:35:27.601 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-23 13:35:27.601 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-23 13:35:27.603 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-23 13:35:27.604 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-23 13:35:27.604 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-23 13:35:27.607 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-23 13:35:27.617 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-23 13:35:27.618 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-23 13:35:27.618 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-23 13:35:27.618 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-23 13:35:27.619 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-23 13:35:27.620 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-23 13:35:27.620 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-23 13:35:27.622 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-23 13:35:27.622 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-23 13:35:27.622 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-23 13:35:27.624 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/6634bc389d0c6843] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-23 13:35:27.631 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/3aed8023307871f1] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-23 13:35:27.631 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/3aed8023307871f1] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-23 13:35:27.647 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/3aed8023307871f1] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-23 13:35:27.651 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/3aed8023307871f1] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-23 13:35:27.652 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/3aed8023307871f1] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-23 13:35:27.661 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/3aed8023307871f1] [http-nio-9007-exec-10] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-23 13:35:27.669 [OrganNo_null_UserNo_null] [7b5342ea6462d7ed/c79e348593be0b25] [http-nio-9007-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
2025-07-23 14:43:47.538 [OrganNo_null_UserNo_null] [b140b1729caf93fa/8717be9d7c5219f4] [http-nio-9007-exec-56] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-23 14:43:47.570 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-23 14:43:47.571 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-23 14:43:47.571 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-23 14:43:47.574 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-23 14:43:47.574 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-23 14:43:47.575 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-23 14:43:47.576 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-23 14:43:47.576 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-23 14:43:47.577 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-23 14:43:47.578 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-23 14:43:47.578 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-23 14:43:47.578 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-23 14:43:47.579 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-23 14:43:47.579 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-23 14:43:47.580 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-23 14:43:47.580 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-23 14:43:47.580 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-23 14:43:47.582 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-23 14:43:47.583 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-23 14:43:47.583 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-23 14:43:47.584 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-23 14:43:47.584 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-23 14:43:47.584 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-23 14:43:47.585 [OrganNo_null_UserNo_null] [b140b1729caf93fa/542b86f2365aa191] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-23 14:43:47.593 [OrganNo_null_UserNo_null] [b140b1729caf93fa/b29f393904fdec7e] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-23 14:43:47.594 [OrganNo_null_UserNo_null] [b140b1729caf93fa/b29f393904fdec7e] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-23 14:43:47.614 [OrganNo_null_UserNo_null] [b140b1729caf93fa/b29f393904fdec7e] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-23 14:43:47.619 [OrganNo_null_UserNo_null] [b140b1729caf93fa/b29f393904fdec7e] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-23 14:43:47.619 [OrganNo_null_UserNo_null] [b140b1729caf93fa/b29f393904fdec7e] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-23 14:43:47.625 [OrganNo_null_UserNo_null] [b140b1729caf93fa/b29f393904fdec7e] [http-nio-9007-exec-56] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-23 14:43:47.637 [OrganNo_null_UserNo_null] [b140b1729caf93fa/8717be9d7c5219f4] [http-nio-9007-exec-56] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
2025-07-23 19:36:19.557 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/f5b3c3fd3d8437c0] [http-nio-9007-exec-36] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-23 19:36:19.610 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-23 19:36:19.610 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-23 19:36:19.610 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-23 19:36:19.614 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-23 19:36:19.615 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-23 19:36:19.615 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-23 19:36:19.617 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-23 19:36:19.618 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-23 19:36:19.618 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-23 19:36:19.620 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-23 19:36:19.620 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-23 19:36:19.620 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-23 19:36:19.624 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-23 19:36:19.636 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-23 19:36:19.637 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-23 19:36:19.637 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-23 19:36:19.638 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-23 19:36:19.640 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-23 19:36:19.640 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-23 19:36:19.640 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-23 19:36:19.643 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-23 19:36:19.644 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-23 19:36:19.644 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-23 19:36:19.646 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/4fac09081920aafe] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-23 19:36:19.654 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/e571adba0ca99e21] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-23 19:36:19.654 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/e571adba0ca99e21] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-23 19:36:19.673 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/e571adba0ca99e21] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-23 19:36:19.678 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/e571adba0ca99e21] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-23 19:36:19.679 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/e571adba0ca99e21] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-23 19:36:19.687 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/e571adba0ca99e21] [http-nio-9007-exec-36] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-23 19:36:19.698 [OrganNo_null_UserNo_null] [fbdc9ab70707d073/f5b3c3fd3d8437c0] [http-nio-9007-exec-36] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
