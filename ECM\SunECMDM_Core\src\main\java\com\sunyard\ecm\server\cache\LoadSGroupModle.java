package com.sunyard.ecm.server.cache;

import com.sunyard.ecm.server.bean.SGroupModleSetBean;
import com.sunyard.ecm.server.cache.wsclient.GetServiceConfig;
import com.sunyard.exception.SunECMException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Title:服务器组和内容对象对应关系管理类
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class LoadSGroupModle {
	private final static Logger log = LoggerFactory.getLogger(LoadSGroupModle.class);
	private GetServiceConfig getConfig = new GetServiceConfig();
	private Map<String, SGroupModleSetBean> sgroupmodlemap = null;

	/**
	 * DM初始化时主动获取服务器组和内容对象信息
	 * */
	public LoadSGroupModle() {
		setSgroupmodlemap();
	}

	/**
	 * 获得服务器组和内容对象信息
	 * */
	private void setSgroupmodlemap() {
		try {
			String gtoupID = LazySingleton.getInstance().load.getNodeInfoBean()
					.getGroup_id();
			List<SGroupModleSetBean> list = getConfig
					.getSgroupmodleSet(gtoupID);
			sgroupmodlemap = new HashMap<String, SGroupModleSetBean>();
			if (list == null) {
				return;
			}
			for (SGroupModleSetBean sGroupModleSetBean : list) {
				sgroupmodlemap.put(sGroupModleSetBean.getModel_code(),
						sGroupModleSetBean);
			}
			log.debug( "DM主动获取服务器组和内容对象对应卷成功-->" + sgroupmodlemap);
		} catch (SunECMException e) {
			// webservice异常时，每隔20秒钟重新获取信息 直到获取成功
			log.error( "DM主动获取服务器组和内容对象对应卷失败-->" + e.getMessage());
			try {
				Thread.sleep(20 * 1000);
			} catch (InterruptedException e1) {
			}
			setSgroupmodlemap();
			log.error( "DM主动获取服务器组和内容对象对应卷失败-->" + e.getMessage());
		}
	}

	/**
	 * 更新服务器组和内容对象信息
	 * */
	public void updateSGroupModle(SGroupModleSetBean bean) {
		if (sgroupmodlemap == null) {
			sgroupmodlemap = new HashMap<String, SGroupModleSetBean>();
		}
		sgroupmodlemap.put(bean.getModel_code(), bean); // 一个内容模型只能同时存在一个卷内
	}

	public GetServiceConfig getGetConfig() {
		return getConfig;
	}

	public void setGetConfig(GetServiceConfig getConfig) {
		this.getConfig = getConfig;
	}

	public Map<String, SGroupModleSetBean> getSgroupmodlemap() {
		return sgroupmodlemap;
	}

	public void setSgroupmodlemap(Map<String, SGroupModleSetBean> sgroupmodlemap) {
		this.sgroupmodlemap = sgroupmodlemap;
	}

}