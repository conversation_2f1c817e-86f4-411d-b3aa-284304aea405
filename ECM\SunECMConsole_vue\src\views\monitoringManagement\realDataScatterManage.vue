<template>
  <div class="app-container">
    <div class="filter-container">
       <el-select v-model="listQuery.group_id" placeholder="请选择存储服务器组" @change='getServerModelCode()'>
        <el-option
          v-for="item in storeGroups"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-select v-model="listQuery.model_code" placeholder="请选择内容对象">
        <el-option
          v-for="item in storeObj"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
	  <el-select v-model="listQuery.scatterOption" placeholder="请选择接口类型">
        <el-option
          v-for="item in storeOption"
          :key="item.id"
          :label="item.text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
     </div> 
  <el-button type="text" @click="queryBeforeOneHour()">{{btnText}}</el-button>
	<el-row :gutter="32">
		<el-col :xs="24" :sm="24" :lg="10">
			<div :id="canvas"  :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
	 </el-row>
  </div>

</template>

<script>
import {getContentServerGroup,getRelServers,getRelContentObject,getRealTimeData} from '@/api/monitorManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import * as echarts from 'echarts'

export default {
    name: 'ComplexTable',
    components: { Pagination },
    directives: { waves,elDragDialog },
    filters: {
        statusFilter(status) {
        const statusMap = {
            published: 'success',
            draft: 'info',
            deleted: 'danger'
        }
        return statusMap[status]
    }
  },

  props: {
    canvas: {
      type: String,
      default: 'chart1'
    },
    width: {
      type: String,
      default: '1100px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
      default: '16px 16px'
    }
  },
  data() {
    return{
      objs : [], 
      btnText : "显示当前数据",
      init : 0,
      flag : true,//是否第一次访问，第一次访问获取前1小时数据
      storeGroups :  [],
      storeServers :  [],
      storeObj : [],  
      storeOption:[
        {id: 'UPLOAD', text: '上传接口' },
        {id: 'UPDATE', text: '更新接口' },
        {id: 'HEIGQUERY', text: '高级检索' },
        {id: 'QUERY', text: '查询接口' },
        {id: 'GETFILE', text: '文件下载' },
        {id: 'MIGRATE', text: '迁移接口' }
      ],
		  serverIds:[],
		  serverNames:[],
      server_ids:"",
      server_names:"",
      listQuery: {
        importance: undefined,
        title: undefined,
        type: undefined,
        group_id:"",
        model_code:"",
        scatterOption:""
      },
		  chart : null,
      scatterText:'准实时耗时统计图(前一小时数据)'
    }
  },
  
    mounted() {
      this.$nextTick(() => {
        this.showCharts()
      })
    },

    created() {
        this.getGroups()
    },

    beforeDestroy(){
      if(this.event1!=null){
        clearInterval(this.event1);
      }
    },

  methods: {
    handleclear() {
      this.listQuery.group_id= "";
      this.listQuery.model_code= "";
	    this.listQuery.scatterOption= "";
    },

    handleFilter() {
        this.objs = [];
        for (let i = 0; i < this.serverIds.length; i++) {
				  this.objs[i] = [];
			  }
        this.setChartParams();
        this.flag = true;
        this.showData()
    },
    getGroups(){
        getContentServerGroup().then(response => {
        this.storeGroups = response.root;
      })
    },

    getServerModelCode(){
        getRelContentObject(this.listQuery).then(response => {
        this.storeObj = response.root
      })
      getRelServers(this.listQuery).then(response => {
          this.storeServers = response.root;
          this.serverIds = [];
          this.serverNames = [];
          this.server_ids="";
          this.server_names="";

          for (let i = 0; i < this.storeServers.length; i++) {
            this.serverIds[i] = this.storeServers[i].id;
            this.serverNames[i] = this.storeServers[i].text;
            if (i == 0) {
              this.server_ids += this.storeServers[i].id;
              this.server_names += this.storeServers[i].text;
            } else {
              this.server_ids += ','+ this.storeServers[i].id;
              this.server_names += ',' + this.storeServers[i].text;
              
            }
          }
        })
    },
    queryBeforeOneHour() {
			if (this.btnText == '显示当前数据') {
				this.btnText = '显示前一小时数据';
        this.scatterText = '准实时耗时统计图(当前数据)';
				this.init = 1;
				this.flag = false;
			} else {
        this.btnText = '显示当前数据';
        this.scatterText = '准实时耗时统计图(前一小时数据)';
				this.init = 0;
				this.flag = true;
			}
			for (let i = 0; i < this.serverIds.length; i++) {
				this.objs[i].data = [];
			}
        this.showCharts();
        this.showData()
		},


    setChartParams() {
			for (let i = 0; i < this.serverIds.length; i++) {
				this.objs[i] = {
					type : 'scatter',
					markArea : {
						silent : true,
						itemStyle : {
							normal : {
								color : 'transparent',
								borderWidth : 1,
								borderType : 'dashed'
							}
						}
					},
					markPoint : {
						data : [ {
							type : 'max',
							name : '最大值'
						}, {
							type : 'min',
							name : '最小值'
						} ]
					},
					markLine : {
						lineStyle : {
							normal : {
								type : 'solid'
							}
						},
						data : [ {
							type : 'average',
							name : '平均值'
						} ]
					},
					name : this.serverNames[i],
					symbolSize : 1.5,
					data : []
				}
			}
		},

    showCharts(){
      this.chart = echarts.init(document.getElementById(this.canvas), 'dark');
      let option = {
        color : [ "#FE5656", "#906BF9", "#01E17E",
            "#fff200", "#3DD1F9", "#FFAD05" ],
        title : {
          top : '3%',
          text : this.scatterText,
          left : 'center',
          padding : 0,
          itemGap : 0,
        },
        grid : {
          top : '10%',
          left : '5%',
          right : '5%',
          bottom : '10%',
          containLabel : true
        },
        tooltip : {
          axisPointer : {
            show : true,
            type : 'cross',
            lineStyle : {
              type : 'dashed',
              width : 1
            }
          }
        },
        legend : {
          left : 'right'
        },
        xAxis : [ {
          type : 'time',
          scale : true,
          splitLine : {
            show : false
          }
        } ],
        yAxis : [ {
          type : 'value',
          name : '(ms)',
          scale : true,
          splitLine : {
            show : false
          }
        } ],
        series : this.objs
		};
      this.chart.setOption(option);
    },

    timerEvent(){
      let map = new Map();
      this.listQuery.server_id = this.server_ids;
      this.listQuery.flag = this.flag;
      // let serversId = this.serverIds.split(',');
			getRealTimeData(this.listQuery).then(response => {
				this.chart.hideLoading();
				let serverNum = 1000;
				for (let key in response) {
          if(key != "code"){
            let arr = key.split('=');
            for (let s = 0; s < this.serverIds.length; s++) {
              if (arr[0] == this.serverIds[s]) {
                serverNum = s;
              } else {
                continue;
              }
            }
            let point = [ Number(arr[1]),
                Number(response[key]) ];
            if (map.hasOwnProperty(key)) {
              continue;
            } else {
              map[key] = response[key];
              this.objs[serverNum].data
                  .push(point);
            }
          }
				}
				this.chart.setOption({
					series : this.objs
				});
				this.flag = false;
				let timeStamp = new Date().getTime();
				for (let i = 0; i < this.serverIds.length; i++) {
					while ((timeStamp - 3540000) > this.objs[i].data[0][0]) {
						this.objs[i].data.shift();
					}
				}
			})
    },

    showData(){
      clearInterval(this.event1);
      this.event1 = null;
      this.chart.hideLoading();
      this.timerEvent();
      this.event1 = setInterval(this.timerEvent, 5000); 
	  }
  }
}
</script>

<style scoped>

</style>
