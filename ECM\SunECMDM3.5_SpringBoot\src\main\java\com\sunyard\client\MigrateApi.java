package com.sunyard.client;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.sunyard.ecm.server.bean.*;
import com.sunyard.ecm.server.cache.LazySingleton;
import com.sunyard.util.TransOptionKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.bean.HttpConnType;
import com.sunyard.client.conn.HttpConn;
import com.sunyard.client.conn.HttpsConn;
import com.sunyard.client.conn.TransConn;
import com.sunyard.client.connQueueControl.SocketConnObject;
import com.sunyard.common.Configuration;
import com.sunyard.ecm.server.util.EcmUtil;
import com.sunyard.ecm.server.util.GenerateMsg;
import com.sunyard.ecm.server.util.GroupServerUtil;
import com.sunyard.ecm.server.util.ModelUtil;
import com.sunyard.ecm.server.util.VolumnUtils;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.msgqueue.MsgPut;
import com.sunyard.scheduler.dao.ContentMigrateDao;
import com.sunyard.util.CodeUtil;
import com.sunyard.util.SpringUtil;

/**
 * 迁移API
 * <AUTHOR>
 * @version 1.0
 *
 */
public class MigrateApi {
	private ContentMigrateDao migrateDao = (ContentMigrateDao) SpringUtil
			.getSpringBean("contentMigrateDao");
	private final static Logger log = LoggerFactory.getLogger(MigrateApi.class);
	private SocketConnObject connObject = null;
	private String serverName =  Configuration.get("localName","SunECMDM");
	private static String agreement = LazySingleton.getInstance().load.getConfigBean().getAgreement();
	
	/**
	 * 获取传输连接对象
	 * @param ip
	 * @param port
	 * @return
	 * @throws SunECMException
	 */
	private TransConn getTransTypeImpl(NodeInfo nodeInfo, String type) throws SunECMException{
//		log.debug( "--MigrateApi-->getTransTypeImpl-->获取传输连接对象");
		if(nodeInfo == null){
			throw new SunECMException(SunECMExceptionStatus.MIGRATE_FAIL,"获取传输连接对象时，nodeInfo 为空(nodeInfo is null ,to get trans conn object)");
		}
		TransConn conn = null;
		if("http".equals(agreement)){
			String url = "";
			int connTimeOut = 30000;
			int reqTimeOut = 30000;
			if("MSG".equals(type)){
				url = createMsgURL(nodeInfo.getServer_ip(),  nodeInfo.getHttp_port());
			}else{
				url = createFileURL(nodeInfo.getServer_ip(),  nodeInfo.getHttp_port());
			}
			conn = new HttpConn(url, connTimeOut, reqTimeOut);
		}else if("socket".equals(agreement)){
			//不必使用池
			//connObject = SocketConnThreadControl.getSocketConnObject(nodeInfo.getServer_ip(), nodeInfo.getSocket_port());
			connObject=new SocketConnObject(nodeInfo.getServer_ip(), nodeInfo.getSocket_port());
			conn =  connObject.getClientTrans();
		}else {			//使用https
			String url = "";
			int connTimeOut = 30000;
			int reqTimeOut = 30000;
			if("MSG".equals(type)){
				url = createMsgURL(nodeInfo.getServer_ip(),  nodeInfo.getHTTPS_PORT());
			}else{
				url = createFileURL(nodeInfo.getServer_ip(),  nodeInfo.getHTTPS_PORT());
			}
			conn = new HttpsConn(url, connTimeOut, reqTimeOut);
		
			
		}
		return conn;
	}
	/**
	 * 创建消息发送URL连接
	 * @param ip
	 * @param httpPort
	 * @return
	 */
	private String createMsgURL(String ip, String port){
		ip = CodeUtil.changeIp(ip);
		if ("http".equals(agreement)) {
			return "http://" + ip + ":" + port + "/" + serverName + "/servlet/httpReqDispacher";
		} else {
			return "https://" + ip + ":" + port + "/" + serverName + "/servlet/httpReqDispacher";
		}
	}
	
	/**
	 * 创建文件URL连接
	 * @param ip
	 * @param httpPort
	 * @return
	 */
	private String createFileURL(String ip, String httpPort) {
		ip = CodeUtil.changeIp(ip);
		if ("http".equals(agreement)) {
			return "http://" + ip + ":" + httpPort + "/" + serverName + "/servlet/httpMigrateFileReceive";
		} else {
			return "https://" + ip + ":" + httpPort + "/" + serverName + "/servlet/httpMigrateFileReceive";
		}}
	/**
	 * 移除socket连接
	 * 		关闭socket连接并从连接队列中删除
	 */
	private void removeSocketConn(){
		log.debug( "removeSocketConn-->移除socket连接");
		if(connObject != null){
//			String agreement = LazySingleton.getInstance().load.getConfigBean().getAgreement();
			try {
				if("socket".equals(agreement)){
					try{
						log.debug( "关闭socket连接");
						connObject.closeConn();//关闭连接
					}finally{
//						SocketConnThreadControl.removeConn(connObject);//从连接队列中移除
						connObject = null;
					}
				}
			} catch (Exception e) {
				log.warn("close socket get exception"+e.getMessage());
			}
		}
	}
	/**
	 * 内容迁移
	 * @param migrateBean
	 * 			迁移对象Bean
	 * @param modelType
	 * 			内容模型类型
	 * @param groupBean
	 * 			目标组对象
	 * @return
	 * 			true迁移成功, false 迁移失败
	 * @throws SunECMException 
	 * @throws Exception
	 */
	public boolean migrate(MigrateBatchBean migrateBean, String modelType, GroupBean groupBean) throws SunECMException, Exception{
//		log.info( "migrate-->内容迁移");
		long t = System.currentTimeMillis();
		String migrateStatus = "0";
		String modelCode = migrateBean.getModelCode();
		try{
			String id = migrateBean.getIndex_Object().getContentID();
			boolean versionContorl = ModelUtil.isVersionControl(migrateBean.getModelCode());
			String verStr = migrateBean.getIndex_Object().getVersion();
			if (versionContorl) {//版本控制
				int version = Integer.parseInt(verStr);
				if (version > 1) {
					String errorVersion = 	migrateDao.getMigrateErrorVersion(migrateBean);
					if(EcmUtil.isNotBlank(errorVersion)){
						log.warn("批次：["+id+"] 存在迁移失败的版本["+errorVersion+"]，因此不能迁移");
						//throw new  SunECMException("批次：["+id+"] 存在迁移失败的版本["+errorVersion+"]，因此不能迁移");
						migrateDao.insertIntoMigrate_error(id,migrateBean.getTABLE_NAME(), migrateBean.getModelCode(),"errorversion="+errorVersion+"  migrate already has error",verStr);
						return false;
					}
				}
			}

			NodeInfo nodeInfo = GroupServerUtil.getGroupNodeServerById(groupBean.getGroup_id());
			TransConn conn = getTransTypeImpl(nodeInfo, HttpConnType.MSG);
			String msg = TransOptionKey.MESSAGE_PROCESS + TransOptionKey.SPLITSYM + "OPTION=" + TransOptionKey.MIGRATE  + ",START=START"+ ",CONTENTID="+id + ",XML=" + migrateBean.toString();
			conn.sendMsg(msg);
			log.debug(msg);
			String rMsg1 = conn.receiveMsg();
			if(!"".equals(rMsg1)){
				if(!String.valueOf(TransOptionKey.SERVER_OK).equals(rMsg1.split(TransOptionKey.SPLITSYM)[0])){
					log.error( "--MigrateApi-->MigrateApi-->迁移批次服务端发生错误, migrate content server error, the content id is" + rMsg1 + ";migrateBean is" + migrateBean.toString());
					throw new SunECMException(
							SunECMExceptionStatus.MIGRATE_SERVER_ERROR,"--MigrateApi-->MigrateApi-->迁移批次服务端发生错误, migrate content server error, the content id is" + rMsg1 + ";migrateBean is" + migrateBean.toString()); 
				}
			}
			if("0".equals(modelType)){//索引对象
				//只需要发送xml
			}else if("1".equals(modelType) || "2".equals(modelType)){//文档对象或者复合对象时需要发送文件
				
				if("http".equals(agreement)){
				  conn = getTransTypeImpl(nodeInfo, HttpConnType.FILE);
				}
				String modelName = migrateBean.getModelCode();
//				log.debug("--MigrateApi-->migrate-->migrateBean :" + migrateBean.toString());
				List<BatchFileBean> tempList = migrateBean.getDocument_Objects();
				if(tempList != null){
					String root = "";
					String filePath = "";
					String contentID = migrateBean.getIndex_Object().getContentID();
					if(contentID == null){//单文档对象下ContentID需要在文档对象下面获取
						contentID = tempList.get(0).getFiles().get(0).getContentID();
					}
					for(BatchFileBean fileBean : tempList){
						List<FileBean> files = fileBean.getFiles();
						String file_part_table_name=fileBean.getFILE_PART_TABLE_NAME();
						if(files != null){
							for(FileBean bean : files){
							    String optionType = bean.getOptionType();
							    //删除文件 不用上传文件
							    if(optionType.equals("3")){
							    	continue;
							    }
							    //非版本控制的修改索引，然后需要上传文件
							    if(optionType.equals("4") && versionContorl){
							    	continue;
							    }
								//当文件路径不为空时表示该文件需要迁移
								if(bean.getFilePath() != null && !"".equals(bean.getFilePath()) && !"null".equals(bean.getFilePath())){
									if("".equals(root)){
										//根据卷ID获取存储配置
										StoreObjectBean storeBean = LazySingleton.getInstance().storeObject.getStoreObjectBeanByVId(bean.getVolume_id());
										//组装根路径
//										root = storeBean.getRoot_path() + storeBean.getSave_path();
										root = VolumnUtils.getsavePath(storeBean.getRoot_path(), storeBean.getSave_path());
									}
									//组装文件绝对路径
									filePath = root + bean.getFilePath() + bean.getSaveName();
									msg = TransOptionKey.MIGRATE_FILE_RECIEVE+TransOptionKey.SPLITSYM + "MODEL_NAME=" + modelName + ",FILE_NO=" + bean.getFileNO()+",SAVE_NAME=" + bean.getSaveName() + ",FILE_FORMAT=" + bean.getFileFormat() + ",VERSION=" + bean.getVersion() + ",FILESIZE="+bean.getFilesize();

//									log.debug("migrate-->发送文件,filePath:"+filePath+",  msg:"+msg);
									if(!conn.sendFileData(filePath, contentID, msg)){//发送文件
										removeSocketConn();//发送文件失败，删除socket连接
										log.error( "migrate-->send file fail ,contentID:" + contentID + ", file path:" + filePath);
										migrateDao.insertIntoMigrate_error_detail(contentID, migrateBean.getTABLE_NAME(), file_part_table_name, migrateBean.getModelCode(), fileBean.getFilePartName(), migrateBean.getBusinessStartDate(), verStr);
										return false;
//										throw new SunECMException(
//												SunECMExceptionStatus.FILE_NOT_FOUND,"批次"+contentID+"---------------->文件不存在"); 
									}else{
										if("http".equals(agreement)){
											String rMsg = conn.receiveMsg();
											log.debug( "--MigrateApi-->migrate-->返回的消息：" + rMsg);
											if(!"".equals(rMsg)){
												if(!String.valueOf(TransOptionKey.SERVER_OK).equals(rMsg.split(TransOptionKey.SPLITSYM)[0])){
													log.error( "--migrate-->MigrateApi-->send file fail ,contentID：" + contentID + ", file path：" + filePath);
												}else{
													//往统计报表中添加发送文件数量
//													Report.addSendFileSumMap(modelName, 1);
												}
											}
										}else{
											//往统计报表中添加发送文件数量
//											Report.addSendFileSumMap(modelName, 1);
										}
									}
								}
								root = "";
							}
						}
					}
					
				}
				
			}
			if("http".equals(agreement)){
				conn = getTransTypeImpl(nodeInfo, HttpConnType.MSG);
			}
			//发送xml
			msg = TransOptionKey.MESSAGE_PROCESS + TransOptionKey.SPLITSYM + "OPTION=" + TransOptionKey.MIGRATE + ",START=END" + ",CONTENTID="+id;
			conn.sendMsg(msg);
			log.debug( "--MigrateApi-->MigrateApi-->内容迁移发送的消息：" + msg);
			String rMsg = conn.receiveMsg();
			log.debug( "--MigrateApi-->MigrateApi-->内容迁移返回的消息：" + rMsg );
			if(!"".equals(rMsg)){
				if(String.valueOf(TransOptionKey.SERVER_OK).equals(rMsg.split(TransOptionKey.SPLITSYM)[0])){
					migrateDao.updateMigrateSuccess(migrateBean);
					migrateStatus = "1";
					t = System.currentTimeMillis()-t;
					return true;
				}else{
					log.error( "--MigrateApi-->MigrateApi-->迁移批次服务端发生错误, migrate content server error, the content id is" + rMsg + ";migrateBean is" + migrateBean.toString());
					throw new SunECMException(
							SunECMExceptionStatus.MIGRATE_SERVER_ERROR,"--MigrateApi-->MigrateApi-->迁移批次服务端发生错误, migrate content server error, the content id is" + rMsg + ";migrateBean is" + migrateBean.toString()); 

				}
			}

			//removeSocketConn();//删除socket连接
			
			return false;
		}catch (SunECMException e) {
			//removeSocketConn();//删除socket连接
			throw e;
		}catch (Exception e) {
			//removeSocketConn();//删除socket连接
			throw e;
		} finally{
//			String writeReport = Configuration.get("writeReport", "false");
//			if("true".equals(writeReport)){
//				putMsg(modelCode, migrateStatus, t);
//			}
			removeSocketConn();
			//returnSocketConn();//将对象返回连接池
		}
	}
	
	private void putMsg(String modelCode,String optStatus,long takeTime){
		String date = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());
		String nodeId = LazySingleton.getInstance().load.getNodeInfoBean().getServer_id();
		String msg = GenerateMsg.getOptMsg(modelCode, nodeId, date, "MIGRATE_VERSION", optStatus, takeTime);
		new MsgPut(msg).putMsg();
	}
	
//	public static void main(String[] args) {
//		Map<String, String> map = new HashMap<String, String>();
//		map.put("a", "1");
//		System.out.println(map.get("A"));
//	}
}