package com.sunyard.console.common.util;

import javax.servlet.http.HttpSession;
import java.util.Map;


/**
 * JSP请求处理辅助类
 * <AUTHOR>
 */
public class JspRequestUtil {
	/**
	 * 从session中得到内容模型、内容模型下的自定义属性、权限信息集合转化成JS字符串
	 * @param session
	 * @return
	 */
	public static String String2JsStr(HttpSession session){

		StringBuffer jsStr = new StringBuffer();
		/**
		 * 缓存服务器信息
		 */
		Map<String,String> map = SessionUtil.getNodeInfoMap(session);
		jsStr.append("var nodeInfoMap = new Map();");
		for(Object key : map.keySet()){
			jsStr.append("nodeInfoMap.put('").append(key).append("','").append(map.get(key)).append("');");
		}
		/**
		 * 客户端请求的所有参数信息
		 */
		Map<String,String> map2 = SessionUtil.getRequestParameterMap(session);
		jsStr.append("var paramInfoMap = new Map();");
		for(Object key : map2.keySet()){
			jsStr.append("paramInfoMap.put('").append(key).append("','").append(map2.get(key)).append("');");
		}
		
		/**
		 * 扫描权限、查看权限、删除权限、修改权限、打印权限、批注权限、管理员权限
		 * 没有管理员权限时只能操作自己上传的文件
		 */
		/*
		map2 = SessionUtil.getPermissionRightMap(session);
		jsStr.append("var rightMap = new Map();");
		for(Object key : map2.keySet()){
			jsStr.append("rightMap.put('").append(key).append("','").append(map2.get(key)).append("');");
		}
		jsStr.append("var scanFileDefaultSize = ").append(GlobalVar.getUploadFileDefaultSize()).append(";");
		
		// 如果是生产模式，禁用右键
		if(GlobalVar.getSystemRunModel().equals(Constant.SUN_SYS_RUN_MODEL_PROD)){
			jsStr.append("document.oncontextmenu = new Function(\"event.returnValue=false;\");");
		}
		jsStr.append("var systemRunModel = '").append(GlobalVar.getSystemRunModel()).append("';");
		*/
		return jsStr.toString();
	}
}
