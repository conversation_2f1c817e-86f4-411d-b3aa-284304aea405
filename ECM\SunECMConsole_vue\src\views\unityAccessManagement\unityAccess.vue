<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input
        v-model="listQuery.server_name"
        placeholder="服务器名称"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-if="this.hasPerm('serverSearch')"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
    <el-button
        v-if="this.hasPerm('addServer')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        新增服务器
    </el-button>
    <el-button
      v-if="this.hasPerm('addGroup')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-edit"
        size="mini"
        @click="handleServerGroup()"
      >
        服务器组管理
    </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="服务器名称" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="服务器id" style="width: 100%" align="center" v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.server_id }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="HTTP端口" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.http_port }}</span>
        </template>
      </el-table-column>
      <el-table-column label="SOCKET端口" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.socket_port }}</span>
        </template>
      </el-table-column>
      <el-table-column label="HTTPS端口" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.https_port }}</span>
        </template>
      </el-table-column>
      <el-table-column label="传输协议" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.trans_protocol }}</span>
        </template>
      </el-table-column>
      <el-table-column label="服务器组名称" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.group_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="服务器组IP" style="width: 100%" align="center">
        <template slot-scope="{ row }">
           <span>{{ row.ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.state==0" style="color:red">禁用</span>
          <span v-if="row.state==1" style="color:green">启用</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" style="width: 100%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.remark }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="left"
        width="330"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <el-button v-if="hasPerm('editServer')" type="primary"  icon="el-icon-edit" size="mini" @click="handleUpdate(row)"    style="margin-bottom:5px;margin-left:10px">
            修改
          </el-button>
          <el-button
            v-if="row.state == '0' && hasPerm('ableServer')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="handleActiveUA(row, $index)" style="margin-bottom:5px"
          >
            激活
          </el-button>
          <el-button
            v-if="row.state == '1' &&hasPerm('disableServer')"
            size="mini"
            type="danger"
            icon="el-icon-turn-off"
            @click="handleDisableUA(row, $index)"  style="margin-bottom:5px"
          >
            禁用
          </el-button>
          <el-button type="primary" size="mini" plain round icon="el-icon-view"  @click="handleInit(row)"  style="margin-bottom:5px">
            内存信息展示
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.start"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />


    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="UAServerRules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="服务器名称" prop="server_name">
          <el-input
            v-model="temp.server_name"
            prop="server_name"
          />
        </el-form-item>
        <el-form-item label="HTTP端口" prop="http_port">
          <el-input-number v-model.number="temp.http_port" />
        </el-form-item>
        <el-form-item label="SOCKET端口" prop="socket_port">
          <el-input-number v-model.number="temp.socket_port" />
        </el-form-item>
        <el-form-item label="HTTPS端口" prop="https_port">
          <el-input-number v-model.number="temp.https_port" />
        </el-form-item>

        <el-form-item label="传输协议" prop="trans_protocol">
          <el-select v-model="temp.trans_protocol" placeholder="请选择类型">
            <el-option label="http" value="http"/>
            <el-option label="https" value="https"/>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-select v-model="temp.state" placeholder="请选择状态">
            <el-option label="启用" value="1"/>
            <el-option label="禁用" value="0"/>
          </el-select>
        </el-form-item>
        <el-form-item label="IP" prop="server_ip">
          <el-input v-model="temp.server_ip" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="temp.remark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary" size="mini"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>



   <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="统一接入服务器组管理" :visible.sync="ServerGroupDialogVisible" width="1200px">
     <div>
      <el-button
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreateUAGroup()"
      >
        新增服务器组
      </el-button>
     </div>
     <div class="app-container">
       <el-table :key="tableKey"  v-loading="grouplistLoading" :data="grouplist" border fit
        highlight-current-row
        style="width: 100%"
       >
        <el-table-column label="服务器组名称" width="200px" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.group_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="IP" width="150px" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.ip }}</span>
          </template>
        </el-table-column>
        <el-table-column label="HTTP端口" width="120px" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.http_port }}</span>
          </template>
        </el-table-column>
        <el-table-column label="SOCKET端口" width="150px" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.socket_port }}</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" width="250px" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column
        label="操作"
        align="center"
        style="width: 100%"
        class-name="small-padding fixed-width"
        >
          <template slot-scope="{ row, $index }">
            <el-button type="primary" icon="el-icon-edit" size="mini" @click="handleUpdateGroup(row)">
              修改服务器组
            </el-button>
          </template>
        </el-table-column>
       </el-table>
     </div>
     <pagination
      v-show="total > 0"
      :total="grouplisttotal"
      :page.sync="grouplistQuery.start"
      :limit.sync="grouplistQuery.limit"
      @pagination="getGroupList"
     />
   </el-dialog>


    <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="新增服务器组信息" :visible.sync="addUAGroupVisiblePage1" >
      <el-form
          ref="dataForm"
          :model="addUAGrouptemp"
          :rules="UAGroupRules"
          label-position="left"
          label-width="120px"
          style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="服务器组名称" prop="group_name">
               <el-input
                  v-model="addUAGrouptemp.group_name"
                  prop="group_name"
               />
        </el-form-item>
        <el-form-item label="HTTP端口" prop="http_port">
               <el-input
                  v-model="addUAGrouptemp.http_port"
                  prop="http_port"
               />
        </el-form-item>
        <el-form-item label="SOCKET端口" prop="socket_port">
               <el-input
                  v-model="addUAGrouptemp.socket_port"
                  prop="socket_port"
               />
        </el-form-item>
        <el-form-item label="IP" prop="ip">
               <el-input
                  v-model="addUAGrouptemp.ip"
                  prop="ip"
               />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
               <el-input
                  v-model="addUAGrouptemp.remark"
                  prop="remark"
               />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="addUAGroupVisiblePage1 = false"> 取消 </el-button>
             <el-button size="mini" type="primary" @click="backToaddUAGroupPage2()">下一步</el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="配置ua服务器" :visible.sync="addUAGroupVisiblePage2" width="1200px" >
        <div class="edit_dev">   
         <el-transfer
            style="text-align: left; display: inline-block"
            v-model="RelUAServeTree"
            filterable
            :left-default-checked="[1]"
            :right-default-checked="[2]"
            :titles="['未分配的服务器', '已有服务器']"
            :button-texts="['到左边', '到右边']"
            :format="{
               noChecked: '${total}',
               hasChecked: '${checked}/${total}',
            }"
            :data="UnRelUAServeTree"
          >
          </el-transfer>
        </div> 
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="backToaddUAGroupPage1()">上一步</el-button>
             <el-button size="mini" type="primary" @click="addGroupDone()">完成</el-button>
          </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="修改统一接入服务器组" :visible.sync="updateGroupDialogVisiblePage1">
        <el-form
            ref="dataForm"
            :model="updateGrouptemp"
            :rules="UAGroupRules"
            label-position="left"
            label-width="120px"
            style="width: 400px; margin-left: 50px"
        >
         <el-form-item label="服务器组名称" prop="group_name">
               <el-input
                  v-model="updateGrouptemp.group_name"
                  prop="group_name"
               />
         </el-form-item>
         <el-form-item label="HTTPS端口" prop="http_port">
               <el-input
                  v-model.number="updateGrouptemp.http_port"
                  prop="http_port" type="number" 
               />
         </el-form-item>
         <el-form-item label="SOCKET端口" prop="socket_port">
               <el-input
                  v-model.number="updateGrouptemp.socket_port"
                  prop="socket_port" type="number" 
               />
         </el-form-item>
         <el-form-item label="IP" prop="ip">
               <el-input
                  v-model="updateGrouptemp.ip"
                  prop="ip"
               />
         </el-form-item>
         <el-form-item label="备注" prop="remark">
               <el-input
                  v-model="updateGrouptemp.remark"
                  prop="remark"
               />
         </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="updateGroupDialogVisiblePage1 = false"> 取消 </el-button>
          <el-button size="mini" type="primary" @click="backToUpdateUAGroupPage2()">下一步</el-button>
        </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="修改统一接入服务器组" :visible.sync="updateGroupDialogVisiblePage2" width="1200px" >
       <div class="edit_dev">
         <el-transfer 
            style="text-align: left; display: inline-block" 
            v-model="RelUAServeTree"
            filterable
            :left-default-checked="[1]"
            :right-default-checked="[2]"
            :titles="['未分配的服务器', '已有服务器']"
            :button-texts="['到左边', '到右边']"
            :format="{
               noChecked: '${total}',
               hasChecked: '${checked}/${total}',
            }"
            :data="UnRelUAServeTree"
          >
          </el-transfer>
        </div>  
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="backToUpdateUAGroupPage1()">上一步</el-button>
             <el-button size="mini" type="primary" @click="updateGroupDone()">完成</el-button>
          </div>
    </el-dialog>

        <el-dialog v-el-drag-dialog :close-on-click-modal="false" :visible.sync="dialogPvVisible" title="UA初始化页面信息">

 <el-form inline="true">
        <el-form-item
          v-for="item in listData"
          :key="item.name"
          :label="item.title + ':'"
          style="margin-bottom: 0"
        >
          [<span style="color: blue">{{ pvData[item.name] }}</span
          >]
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogPvVisible = false" size="mini"
          >关闭</el-button
        >
          <el-button
            size="mini"
            type="primary"
            @click="regetLazySington()"
          >
            发送重新初始化配置命令
          </el-button>
        
      </span>
    </el-dialog>



  </div>
</template>

<script>
import { getUnityAccessServerListAction,disableUA,activeUA,updateUA,createUA,getGroupListAction,
         getUnRelUnityAccessServerTreeAction,addUnityAccessServerGroupAction,
         getRelUnityAccessServerTreeAction,checkServerNameAction,checkServerIPandPortAction,
         checkGroupNameAction,checkServerGroupIPandPortAction,singletonInfoSearch,resetSingletonInfoSearch} from '@/api/unityaccessManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import global from "../../store/global.js";

export default {
    name: 'ComplexTable',
    components: { Pagination },
    directives: { waves,elDragDialog },
    data() {
     return {
     tableKey: 0,
     list: null,
     total: 0,
     listLoading: true,
     listQuery: {
         start: 1,
         limit: 20,
         server_name:''
     },
     grouplistLoading:true,
     grouplist: [],
     grouplisttotal: 0,
     grouplistQuery: {
         start: 1,
         limit: 20,
     },
     temp: {
      optionFlag:'',
      remark:''
     },
     ServerGroupDialogVisible:false,
     dialogFormVisible: false,
     dialogPvVisible: false,
     listData: [
        { name: "server_id", title: "服务器ID" },
        { name: "server_ip", title: "服务器IP" },
        { name: "server_name", title: "服务器名称" },
        { name: "server_status", title: "服务器状态" },
        { name: "http_port", title: "HTTP端口" },
		    { name: "socket_port", title: "SOCKET端口" },
        { name: "group_name", title: "服务器组名称" }
     ],
     pvData: [],
     dialogStatus: '',
     textMap: {
        update: '修改服务器',
        create: '新增服务器'
     },
     UAServerRules: {
        server_name: [{ required: true, pattern: global.regexName, message: global.regexNameText, trigger: 'blur' }],
        http_port: [{ required: true, pattern:global.regexNum, message: 'http端口必输且为数字', trigger: 'blur' }],
        socket_port: [{ required: true, pattern:global.regexNum, message: 'socket端口必输且为数字', trigger: 'blur' }],
        https_port: [{ required: true,pattern:global.regexNum, message: 'https端口必输且为数字', trigger: 'blur' }],
        trans_protocol: [{ required: true, message: '传输协议必输', trigger: 'blur' }],
        state: [{ required: true, message: '状态必输', trigger: 'blur' }],
        server_ip: [{ required: true, message: 'IP必输', trigger: 'blur' }]
      },
     UAGroupRules: {
        group_name: [{ required: true, pattern: global.regexName, message: global.regexNameText, trigger: 'blur' }],
        http_port: [{ required: true, pattern:global.regexNum, message: 'http端口必输且为数字', trigger: 'blur' }],
        socket_port: [{ required: true,pattern:global.regexNum, message: 'socket端口必输且为数字', trigger: 'blur' }],
        trans_protocol: [{ required: true, message: '传输协议必输', trigger: 'blur' }],
        state: [{ required: true, message: '状态必输', trigger: 'blur' }],
        ip: [{ required: true, message: 'IP必输', trigger: 'blur' }]
      },
     addUAGrouptemp:{
        group_id:'',
        group_name:'',
        http_port:'',
        ip:'',
        remark:'',
        socket_port:''
     },
     UnRelUAServeTree:[],
     RelUAServeTree:[],
     addUAGroupVisiblePage1:false,
     addUAGroupVisiblePage2:false,
     updateGrouptemp:{
        group_id:'',
        group_name:'',
        http_port:'',
        ip:'',
        remark:'',
        socket_port:''
     },
     updateGroupDialogVisiblePage1:false,
     updateGroupDialogVisiblePage2:false,
    }
   },
   created() {
   this.getList()
   },
   methods: {
    getList() {
      this.listLoading = true
      getUnityAccessServerListAction(this.listQuery).then(response => {
        this.list = response.root
        this.total = Number(response.totalProperty)
        // Just to simulate the time of the request
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },
    handleclear() {
      this.listQuery.server_name = "";
      //this.listQuery.server_id = "";
    },
    handleFilter() {
      this.listQuery.start = 1
      this.getList()
    },
    resetTemp() {
      this.temp = {
        optionFlag: '',
        remark:''
      }
    },

    resetLazy() {
      this.pvData = {
        id: undefined
      }
    },
    
    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.optionFlag='create1'
          checkServerNameAction(this.temp).then(() => {
              checkServerIPandPortAction(this.temp).then(() => {
                    createUA(this.temp).then(() => {
                      this.getList();
                      this.dialogFormVisible = false
                      this.$notify({
                        title: 'Success',
                        message: '新增服务器成功',
                        type: 'success',
                        duration: 2000
                      })
                    })
              })
          })

        }
      })
    },
    handleUpdate(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          this.temp.optionFlag='update1'
          const tempData = Object.assign({}, this.temp)
          checkServerNameAction(tempData).then(() => {
              checkServerIPandPortAction(tempData).then(() => {
                  updateUA(tempData).then(() => {
                    this.getList();
                    this.dialogFormVisible = false
                    this.$notify({
                      title: 'Success',
                      message: '修改服务器成功',
                      type: 'success',
                      duration: 2000
                    })
                  })
              })
          })
        }
      })
    },
    handleDisableUA(row, index) {
      disableUA(row).then(response => {
        this.getList()
      })
    },
    handleActiveUA(row, index) {
      activeUA(row).then(response => {
        this.getList()
      })
    },
    handleServerGroup() {
      this.grouplist=[]
      this.grouplistQuery.start = 1
      this.getGroupList()
      this.ServerGroupDialogVisible = true
    },
    getGroupList() {
      this.grouplistLoading = true
      this.grouplist=[]
      getGroupListAction(this.grouplistQuery).then(response => {
        this.grouplist = response.root
        this.grouplisttotal = Number(response.totalProperty)
        setTimeout(() => {
          this.grouplistLoading = false
        }, 1 * 100)
      })

    },
    handleCreateUAGroup(){
      this.addUAGrouptemp={
          group_id:'',
          group_name:'',
          http_port:'',
          ip:'',
          remark:'',
          socket_port:''
        }
        this.addUAGroupVisiblePage1=true
    },
    backToaddUAGroupPage2(){
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          checkGroupNameAction(this.addUAGrouptemp).then((response) => {
              checkServerGroupIPandPortAction(this.addUAGrouptemp).then((response) => {
                  this.UnRelUAServeTree=[]
                  this.RelUAServeTree=[]
                  this.getUnRelUAServeTree();
                  this.addUAGroupVisiblePage1=false
                  this.addUAGroupVisiblePage2=true
              })
          })
        }
      })
    },
    getUnRelUAServeTree(){
       getUnRelUnityAccessServerTreeAction().then((response) => {
              let UnRelUAServerTreeResult = response.root
              UnRelUAServerTreeResult.map((item) => {
                 this.UnRelUAServeTree.push({
                  key: item.server_id,
                  label: item.server_name,
                 })
              });
          })
    },
    backToaddUAGroupPage1(){
      this.addUAGroupVisiblePage1=true
      this.addUAGroupVisiblePage2=false
    },
    addGroupDone(){
        var server_ids= '';
        for(var i=0;i<this.RelUAServeTree.length;i++){
           server_ids=server_ids+this.RelUAServeTree[i]+",";
        }
        this.addUAGrouptemp.optionFlag='create1'
        this.addUAGrouptemp.server_ids=server_ids
        addUnityAccessServerGroupAction(this.addUAGrouptemp).then((response) => {
          this.addUAGroupVisiblePage1=false;
          this.addUAGroupVisiblePage2=false;
          this.ServerGroupDialogVisible=false;
          this.handleServerGroup();
          this.getList();
           this.$notify({
               title: 'Success',
               message: '新增统一服务器组成功',
               type: 'success',
               duration: 4000
           })
        })
        
    },
    handleUpdateGroup(row){
        this.updateGrouptemp = Object.assign({}, row) // copy obj
        this.updateGroupDialogVisiblePage1=true
    },
    backToUpdateUAGroupPage2(){
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
         checkGroupNameAction(this.updateGrouptemp).then((response) => {
              checkServerGroupIPandPortAction(this.updateGrouptemp).then((response) => {
                this.UnRelUAServeTree=[];
                this.RelUAServeTree=[];
                this.getUnRelUAServeTree();
                this.getRelUAServeTree();
                this.updateGroupDialogVisiblePage1=false
                this.updateGroupDialogVisiblePage2=true
              })
         })
        }
      })
    },
    getRelUAServeTree(){
       getRelUnityAccessServerTreeAction(this.updateGrouptemp).then((response) => {
              let RelUAServerTreeResult = response.root
                 RelUAServerTreeResult.map((item) => {
                    this.UnRelUAServeTree.push({
                      key: item.server_id,
                      label: item.server_name,
                    })
                    this.RelUAServeTree.push(item.server_id)
                 });
          })
    },
    backToUpdateUAGroupPage1(){
       this.updateGroupDialogVisiblePage1=true
       this.updateGroupDialogVisiblePage2=false
    },
    updateGroupDone(){
      var server_ids= '';
        for(var i=0;i<this.RelUAServeTree.length;i++){
           server_ids=server_ids+this.RelUAServeTree[i]+",";
        }
        this.updateGrouptemp.optionFlag='update1'
        this.updateGrouptemp.server_ids=server_ids
        addUnityAccessServerGroupAction(this.updateGrouptemp).then((response) => {
          this.ServerGroupDialogVisible=false;
          this.updateGroupDialogVisiblePage1=false;
          this.updateGroupDialogVisiblePage2=false;
          this.handleServerGroup();
          this.getList();
           this.$notify({
               title: 'Success',
               message: '修改统一服务器组成功',
               type: 'success',
               duration: 4000
           })
        })
    },
    handleInit(row){
      this.temp = Object.assign({}, row);
      this.getLazySington(this.temp);
      this.dialogPvVisible = true;
    },

    getLazySington(data){
      this.resetLazy();
      singletonInfoSearch(data).then(response => {
        this.pvData = response.root;
        if(this.pvData.server_status == 1){
          this.pvData.server_status = '运行'
        }else if(this.pvData.server_status == 0){
          this.pvData.server_status = '停用'
        }
      })
    },

    regetLazySington(){   
       this.$notify({
              title: 'Refresh',
              message: 'refresh success',
              type: 'success',
              duration: 2000
      })
      resetSingletonInfoSearch(this.temp).then(response => {
        this.pvData = response.root;
        if(this.pvData.server_status == 1){
          this.pvData.server_status = '运行'
        }else if(this.pvData.server_status == 0){
          this.pvData.server_status = '停用'
        }
      })
    }
   
  }
}
    
</script>
<style scoped>
.edit_dev >>> .el-transfer-panel {
     width:350px;
   }
</style>
