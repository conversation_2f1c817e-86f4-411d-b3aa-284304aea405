server.servlet.context-path=/SunECMDM
server.port=8083
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration,org.apache.cxf.spring.boot.autoconfigure.CxfAutoConfiguration,org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration
#DM UA
server.Type=DM
Eureka 配置
eureka.client.service-url.defaultZone:http://127.0.0.1:8100/eureka/
spring.application.name=SunECMDM
#spring.cloud.nacos.discovery.server-addr=***********:8848
#spring.cloud.nacos.discovery.username=nacos
#spring.cloud.nacos.discovery.password=nacos
#spring.cloud.nacos.discovery.namespace=public
#spring.cloud.nacos.discovery.ip=************
#spring.cloud.nacos.discovery.group=yx


