package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * <p>Title: 日志策略信息bean</p>
 * <p>Description: 存放日志策略信息</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("DMLogRuleBean")
public class DMLogRuleBean {
	@XStreamAsAttribute
	private String CLASS_PATH;//类路径CLASS_PATH
	@XStreamAsAttribute
	private String LEVEL;//
	@XStreamAsAttribute
	private String SAVE_PATH;
	@XStreamAsAttribute
	private String LOG_SIZE;
	@XStreamAsAttribute
	private String LEVEL_T;
	public String getClass_path() {
		return CLASS_PATH;
	}
	public void setClass_path(String classPath) {
		CLASS_PATH = classPath;
	}
	public String getLevel() {
		return LEVEL;
	}
	public void setLevel(String level) {
		this.LEVEL = level;
	}
	public String getSave_path() {
		return SAVE_PATH;
	}
	public void setSave_path(String savePath) {
		SAVE_PATH = savePath;
	}
	public String getLog_size() {
		return LOG_SIZE;
	}
	public void setLog_size(String logSize) {
		LOG_SIZE = logSize;
	}
	public String getLevel_t() {
		return LEVEL_T;
	}
	public void setLevel_t(String levelT) {
		LEVEL_T = levelT;
	}
	@Override
	public String toString() {
		return "LogRuleBean [CLASS_PATH=" + CLASS_PATH + ", LEVEL=" + LEVEL
				+ ", LEVEL_T=" + LEVEL_T + ", LOG_SIZE=" + LOG_SIZE
				+ ", SAVE_PATH=" + SAVE_PATH + "]";
	}

	
}