package com.sunyard.client.impl;

import com.sunyard.client.SunEcmClientApi;
import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientHeightQuery;
import com.sunyard.ecm.server.bean.MigrateBatchBean;
import com.sunyard.util.StringUtil;

public abstract class AbstractSunECMClientApi implements SunEcmClientApi {

	public AbstractSunECMClientApi() {
		super();
	}

	protected String createBatchSendMsgbyToken(ClientBatchBean clientBatchBean, String sendMsg) {
		if (StringUtil.stringIsNull(clientBatchBean.getUser())) {
			sendMsg = sendMsg + ",TOKEN=" + clientBatchBean.getToken();
		} else {
			sendMsg = sendMsg + ",USERNAME=" + clientBatchBean.getUser() + ",PASSWORD=" + clientBatchBean.getPassWord();
		}
		return createIsCompressMsg(clientBatchBean.isCompress(), sendMsg);
	}

	protected String createHeightQuerySendMsgbyToken(ClientHeightQuery heightQuery, String sendMsg) {
		if (StringUtil.stringIsNull(heightQuery.getUserName())) {
			sendMsg = sendMsg + ",TOKEN=" + heightQuery.getToken();
		} else {
			sendMsg = sendMsg + ",USERNAME=" + heightQuery.getUserName() + ",PASSWORD=" + heightQuery.getPassWord();
		}
		return sendMsg;
	}

	protected String createImmigrateBatchSendMsgbyToken(MigrateBatchBean migrateBatchBean, String sendMsg) {
		if (StringUtil.stringIsNull(migrateBatchBean.getUserName())) {
			sendMsg = sendMsg + ",TOKEN=" + migrateBatchBean.getToken();
		} else {
			sendMsg = sendMsg + ",USERNAME=" + migrateBatchBean.getUserName() + ",PASSWORD="
					+ migrateBatchBean.getPassWord();

		}
		return sendMsg;
	}
	private String createIsCompressMsg(boolean isCompress,String sendMsg){
		if(isCompress){
			sendMsg=sendMsg + ",ISCOMPRESS=TRUE";
			
		}
		return sendMsg;
	}
}