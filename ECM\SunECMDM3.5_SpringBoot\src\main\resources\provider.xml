<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd
http://dubbo.apache.org/schema/dubbo ">


    <dubbo:registry id="nacos_service" address="nacos://***********:8848" timeout="15" check="false"/>

    <!-- 用dubbo协议在20880端口暴露服务 -->
    <dubbo:protocol name="dubbo" port="20881" threads="1000"/>

    <!--  dubbo管理平台接口  -->
    <bean id="SunEcmDubboDm" class="com.sunyard.ecm.server.SunEcmAccessImpl"/>
    <dubbo:service interface="com.sunyard.ws.internalapi.SunEcmAccess" ref="SunEcmDubboDm" cluster="failfast" registry="nacos_service" timeout="5000" version="1.0.0" group="ALL"/>
    <dubbo:service interface="com.sunyard.ws.internalapi.SunEcmAccess" ref="SunEcmDubboDm" cluster="failfast" registry="nacos_service" timeout="5000" version="1.0.0" group="1"/>
</beans>

