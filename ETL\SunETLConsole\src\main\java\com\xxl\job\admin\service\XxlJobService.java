package com.xxl.job.admin.service;


import com.xxl.job.admin.core.model.XxlJobInfo;
import com.xxl.job.core.biz.model.ReturnT;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * core job action for xxl-job
 * 
 * <AUTHOR> 2016-5-28 15:30:33
 */
public interface XxlJobService {
	
	public Map<String, Object> pageList(int start, int length, int jobGroup, String executorHandler, String filterTime);

	public ReturnT<String> add(XxlJobInfo jobInfo);




	public ReturnT<String> reschedule(XxlJobInfo jobInfo);
	
	public ReturnT<String> remove(int id);
	
	public ReturnT<String> pause(int id);
	
	public ReturnT<String> resume(int id);
	
	public ReturnT<String> triggerJob(int id);

	public Map<String,Object> dashboardInfo();

	public ReturnT<Map<String,Object>> triggerChartDate();
	/**
	 * chart info
	 *
	 * @param startDate
	 * @param endDate
	 * @return
	 */
	public ReturnT<Map<String,Object>> chartInfo(Date startDate, Date endDate);
	/**
	 * 获取任务列表
	 * @return
	 */
	public List<XxlJobInfo> getAllJobInfos();
	/**
	 * 获取任务
	 * @return
	 */
	public XxlJobInfo loadById(int id);

	/**
	 * 如果任务在定时队列中移除定时队列
	 * @param id
	 * @return
	 */
	public ReturnT<String> remjob(int id);

	ReturnT<String> addjob(int id);
}
