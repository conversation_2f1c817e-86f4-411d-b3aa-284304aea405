package com.sunyard.client.conn;

import java.io.IOException;
import java.io.UnsupportedEncodingException;

import org.apache.http.client.ClientProtocolException;

import com.sunyard.exception.SunECMException;

public interface TransConn {
	/**
	 * 发送消息
	 * @param msg
	 */
	public void sendMsg(String msg) throws UnsupportedEncodingException, IOException, SunECMException;
	/**
	 * 接受消息
	 * @return
	 */
	public String receiveMsg() throws IOException;
	/**
	 * 发送文件
	 * @param filePath 文件路径
	 * @param contentID 内容ID
	 * @return
	 */
	public boolean sendFileData(String filePath, String contentID, String transType) throws UnsupportedEncodingException, 
	ClientProtocolException, IOException, SunECMException;
	/**
	 * 断开连接
	 * @throws IOException
	 */
	public void destroy() throws IOException;
}