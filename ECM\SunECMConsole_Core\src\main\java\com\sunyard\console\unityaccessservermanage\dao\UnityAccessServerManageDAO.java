package com.sunyard.console.unityaccessservermanage.dao;

import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerInfoBean;

import java.util.List;

/**
 * 统一接入服务器管理数据操作接口
 * 
 * <AUTHOR>
 * 
 */
public interface UnityAccessServerManageDAO {
	/**
	 * 获取统一接入服务器列表,如果参数为空，返回记录条数内的所有list
	 * 
	 * @param serverId
	 *            服务器id
	 * @param serverName
	 *            服务器名称
	 * @param start
	 *            开始记录
	 * @param limit总条数
	 * @return
	 */
	public List<UnityAccessServerInfoBean> getUnityAccessServerList(
			int serverId, String serverName, int start, int limit);

	/**
	 * 获取统一接入服务器列表，如果参数为空，返回所有的list
	 * 
	 * @param serverId
	 *            服务器id
	 * @param serverNamet
	 *            服务器名称
	 * @return
	 */
	public List<UnityAccessServerInfoBean> getUnityAccessServerList(
			int serverId, String serverName);

	/**
	 * 校验统一接入服务器ip和端口是否存在，返回0表示不存在，否则存在
	 * 
	 * @param serverId
	 *            服务器id
	 * @param serverIp
	 *            服务器ip
	 * @param httpPort
	 *            http端口
	 * @param socketPort
	 *            socket端口
	 * @return
	 */
	public int checkServerIPandPort(int serverId, String serverIp,
			int httpPort, int socketPort);

	/**
	 * 增加统一接入服务器，返回成功与否信息
	 * 
	 * @param bean
	 *            服务器bean
	 * @return 主键，如果返回0表示新增操作失败
	 */
	public int addUnityAccessServer(UnityAccessServerInfoBean bean);

	/**
	 * 修改统一接入服务器，返回成功与否信息
	 * 
	 * @param bean
	 *            服务器bean
	 * @return 主键，如果返回0表示修改操作失败
	 */
	public int updateUnityAccessServer(UnityAccessServerInfoBean bean);

	/**
	 * 启用统一接入服务器
	 * 
	 * @param serverIds
	 *            由服务器id组成的字符串，以逗号隔开
	 * @return
	 */
	public boolean startUnityAccessServer(String serverIds);

	/**
	 * 禁用统一接入服务器
	 * 
	 * @param serverIds
	 *            由服务器id组成的字符串，以逗号隔开
	 * @return
	 */
	public boolean stopUnityAccessServer(String serverIds);

	/**
	 * 获取统一接入服务器列表，并包含serverId所在的服务器信息
	 * 
	 * @param serverId
	 *            服务器id，
	 * @param state
	 *            状态，为true取启用服务器，否则取所有
	 * @return 如果state为true则获取所有启用的服务器列表，如果为false则获取所有服务器列表
	 */
	public List<UnityAccessServerInfoBean> getUnityAccessServerList(
			int serverId, boolean state);
	/**
	 * 校验服务器名称唯一性
	 * 
	 * @param serverId
	 *            服务器id
	 * @param serverName
	 *            服务器名称
	 * @return 返回同名的服务器个数,没有返回0
	 * 
	 */
	public int checkServerName(int serverId, String serverName);

}
