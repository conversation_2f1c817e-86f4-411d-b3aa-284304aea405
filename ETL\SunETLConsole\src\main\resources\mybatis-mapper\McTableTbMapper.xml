<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.McTableDao">
	
	<resultMap id="mcTable" type="com.xxl.job.admin.core.model.McTableTb" >
		<result column="ID" property="id" />
		<result column="DS_ID" property="dsId" />
		<result column="TABLE_NAME" property="tableName" />
	    <result column="TABLE_DESC" property="tableDesc" />
	    <result column="TABLE_TYPE" property="tableType" />
	    <result column="TABLE_SPACE" property="tableSpace" />
	    <result column="INDEX_SPACE" property="indexSpace" />
	</resultMap>


	<sql id="Base_Column_List">
	    t.ID,
    	t.DS_ID,
    	t.TABLE_NAME,
    	t.TABLE_DESC,
    	t.TABLE_TYPE,
    	t.TABLE_SPACE,
    	t.INDEX_SPACE
	</sql>
	
	<select id="pageList" parameterType="java.util.HashMap" resultMap="mcTable">
		SELECT <include refid="Base_Column_List" />
		FROM MC_TABLE_TB t
		WHERE t.TABLE_TYPE = 4
		ORDER BY id
	</select>




</mapper>