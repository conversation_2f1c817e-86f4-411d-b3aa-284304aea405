import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getMigrateFailList(data) {
  const url = '/migrateFailManage/getMigrateFailList'+EndUrl.EndUrl
  data.content_id = encodeURI(data.content_id)
  data.model_code = encodeURI(data.model_code);
  data.fail_time = encodeURI(data.fail_time)
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}


export function reMigrateFaileBatch(data) {
  return request({
    url : '/migrateFailManage/reMigrateFaileBatch'+EndUrl.EndUrl,
    method: 'post',
    params: { 'reMigrateMsg': data.content_id+">>"+data.group_id+">>"+data.version+">>"+data.table_name+";"}
  })
}
//getNoFileMigrateFailList
export function getNoFileMigrateFailList(data) {
  const url = '/migrateFailManage/getMigrateFailList'+EndUrl.EndUrl
  data.content_id = encodeURI(data.content_id)
  data.model_code = encodeURI(data.model_code);
  data.fail_time = encodeURI(data.fail_time)
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

