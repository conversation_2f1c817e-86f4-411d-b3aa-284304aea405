2025-07-23 09:33:12.020 [OrganNo_00023_UserNo_admin] [2cc13f9a59945f31/12d4ccc59383b524] [http-nio-9060-exec-97] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"getAllModelInfos"
	}
}
2025-07-23 09:33:12.063 [OrganNo_00023_UserNo_admin] [2cc13f9a59945f31/12d4ccc59383b524] [http-nio-9060-exec-97] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"modelList":[
			{
				"relating_model_id":1,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":2,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":1,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":3,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":4,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":3,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":5,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":6,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":5,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":7,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":8,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":7,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":9,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":10,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":9,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":11,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":12,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":11,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":13,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":14,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":13,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":15,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":16,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":15,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":17,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":18,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":17,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":19,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":20,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":19,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":21,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":22,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":21,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":23,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":24,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":23,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":25,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":26,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":25,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":27,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":28,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":27,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":29,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":30,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":29,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":31,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":32,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":31,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":33,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":34,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":33,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":36,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1018-经集中核准的人民币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":35,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1018-经集中核准的人民币大额交易(明细)  ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":36,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":38,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":37,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"1"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":38,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":40,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1020-经集中核准的司法扣划交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":39,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1020-经集中核准的司法扣划交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":40,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":42,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":41,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":42,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":44,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":43,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":44,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":46,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":45,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":46,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":48,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":47,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":48,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":50,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":49,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":50,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":52,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":51,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":52,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 09:33:12.064 [] [2cc13f9a59945f31/12d4ccc59383b524] [http-nio-9060-exec-97] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-23 09:33:11 操作结束时间: 2025-07-23 09:33:12!总共花费时间: 74 毫秒！
2025-07-23 09:33:18.697 [OrganNo_00023_UserNo_admin] [0bfd8172e4afc9fa/863e338565bb3016] [http-nio-9060-exec-98] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"end_date":"20250722",
			"model_id":"",
			"site_no":"",
			"start_date":"20240428"
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:33:18.768 [OrganNo_00023_UserNo_admin] [0bfd8172e4afc9fa/863e338565bb3016] [http-nio-9060-exec-98] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"riskWarningStats":[
			{
				"MODEL_ID":6,
				"SUPERVISE_SLIP_COUNT":0,
				"HAVE_DEALED_COUNT":0,
				"NOT_DEALED_COUNT":2,
				"SUPERVISE_PASS_COUNT":0
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 09:33:18.768 [] [0bfd8172e4afc9fa/863e338565bb3016] [http-nio-9060-exec-98] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-23 09:33:18 操作结束时间: 2025-07-23 09:33:18!总共花费时间: 79 毫秒！
2025-07-23 09:33:19.800 [OrganNo_00023_UserNo_admin] [46bd1c75e95e816d/11141170b28e0d47] [http-nio-9060-exec-99] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"modelInfo":{
				"modelName":"1003-账户挂失、解挂交易",
				"modelId":"6",
				"modelType":"1",
				"tableName":"ZD_1003_ZHANGHGSJG"
			},
			"model_id":"6"
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:33:19.846 [OrganNo_00023_UserNo_admin] [46bd1c75e95e816d/11141170b28e0d47] [http-nio-9060-exec-99] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"detailData":[
			{
				"LOSS_ACCOUNT_NO":"6217563100016683998",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行珙县支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":112,
				"FK_SITE_NO":"15263",
				"TX_CODE":"037223              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"10:48:35            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行珙县支行",
				"TX_NAME":"销卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			},
			{
				"LOSS_ACCOUNT_NO":"6216633100000485739",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行郫都红光支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":86,
				"FK_SITE_NO":"35905",
				"TX_CODE":"037241              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"15:34:53            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行郫都红光支行",
				"TX_NAME":"领非预制卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			}
		],
		"exhibitFields":[
			{
				"field_id":366,
				"rowno":1,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"业务日期",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_DATE",
				"is_find":"0",
				"id":366,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":319,
				"rowno":2,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易时间",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_TIME",
				"is_find":"0",
				"id":319,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":83,
				"rowno":3,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"机构号",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NO",
				"is_find":"0",
				"id":83,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":326,
				"rowno":4,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NAME",
				"is_find":"0",
				"id":326,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":367,
				"rowno":5,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NO",
				"is_find":"0",
				"id":367,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":320,
				"rowno":6,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NAME",
				"is_find":"0",
				"id":320,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":394,
				"rowno":7,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属(发卡)机构",
				"type":"INT",
				"isfind":"0",
				"name":"FK_SITE_NO",
				"is_find":"0",
				"id":394,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":399,
				"rowno":8,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属机构名",
				"type":"INT",
				"isfind":"0",
				"name":"ACC_SITENAME",
				"is_find":"0",
				"id":399,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":327,
				"rowno":9,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易代码",
				"type":"INT",
				"isfind":"0",
				"name":"TX_CODE",
				"is_find":"0",
				"id":327,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":372,
				"rowno":10,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易名称",
				"type":"INT",
				"isfind":"0",
				"name":"TX_NAME",
				"is_find":"0",
				"id":372,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":315,
				"rowno":11,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易流水",
				"type":"INT",
				"isfind":"0",
				"name":"FLOW_ID",
				"is_find":"0",
				"id":315,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":406,
				"rowno":12,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账(卡)号",
				"type":"INT",
				"isfind":"0",
				"name":"LOSS_ACCOUNT_NO",
				"is_find":"0",
				"id":406,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":412,
				"rowno":13,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账号（旧）",
				"type":"INT",
				"isfind":"0",
				"name":"FORMER_LOSS_ACC",
				"is_find":"0",
				"id":412,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":314,
				"rowno":14,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"客户名称",
				"type":"INT",
				"isfind":"0",
				"name":"CUSTOMER_NAME",
				"is_find":"0",
				"id":314,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":421,
				"rowno":15,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件类型",
				"type":"INT",
				"isfind":"0",
				"name":"ID_TYPE",
				"is_find":"0",
				"id":421,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":417,
				"rowno":16,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件号",
				"type":"INT",
				"isfind":"0",
				"name":"ID_NO",
				"is_find":"0",
				"id":417,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":341,
				"rowno":17,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证种类",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_TYPE",
				"is_find":"0",
				"id":341,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":328,
				"rowno":18,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证代号",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_NO",
				"is_find":"0",
				"id":328,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":436,
				"rowno":19,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"产品代码/产品码",
				"type":"INT",
				"isfind":"0",
				"name":"PRODUCT_CODE",
				"is_find":"0",
				"id":436,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":376,
				"rowno":20,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易前凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"PRE_VOUCH_STATE",
				"is_find":"0",
				"id":376,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":381,
				"rowno":21,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易后凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"POST_VOUCH_STATE",
				"is_find":"0",
				"id":381,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":368,
				"rowno":22,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"复核柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"AUTH_TELLER_NO",
				"is_find":"0",
				"id":368,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":400,
				"rowno":23,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_TELLER_NAME",
				"is_find":"0",
				"id":400,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":410,
				"rowno":24,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构/核准机构号",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NO",
				"is_find":"0",
				"id":410,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":404,
				"rowno":25,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NAME",
				"is_find":"0",
				"id":404,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":397,
				"rowno":26,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"是否跨网点办理",
				"type":"INT",
				"isfind":"0",
				"name":"TRAN_SITE_HANDLE",
				"is_find":"0",
				"id":397,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":477,
				"rowno":27,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"联网核查结果",
				"type":"INT",
				"isfind":"0",
				"name":"FIELD_CONTENT_CN",
				"is_find":"0",
				"id":477,
				"field_type":1,
				"table_type":"5"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 09:33:19.847 [] [46bd1c75e95e816d/11141170b28e0d47] [http-nio-9060-exec-99] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-23 09:33:19 操作结束时间: 2025-07-23 09:33:19!总共花费时间: 69 毫秒！
2025-07-23 09:33:19.878 [OrganNo_00023_UserNo_admin] [ab1af2ac7bae9312/05c0416c042e59d0] [http-nio-9060-exec-100] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"is_history":"0",
			"ishandle":"0",
			"modelInfo":{
				"modelName":"1003-账户挂失、解挂交易",
				"modelId":"6",
				"modelType":"1",
				"tableName":"ZD_1003_ZHANGHGSJG"
			},
			"model_id":"6"
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:33:19.925 [OrganNo_00023_UserNo_admin] [ab1af2ac7bae9312/05c0416c042e59d0] [http-nio-9060-exec-100] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"detailData":[
			{
				"LOSS_ACCOUNT_NO":"6217563100016683998",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行珙县支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":112,
				"FK_SITE_NO":"15263",
				"TX_CODE":"037223              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"10:48:35            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行珙县支行",
				"TX_NAME":"销卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			},
			{
				"LOSS_ACCOUNT_NO":"6216633100000485739",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行郫都红光支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":86,
				"FK_SITE_NO":"35905",
				"TX_CODE":"037241              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"15:34:53            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行郫都红光支行",
				"TX_NAME":"领非预制卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			}
		],
		"exhibitFields":[
			{
				"field_id":366,
				"rowno":1,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"业务日期",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_DATE",
				"is_find":"0",
				"id":366,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":319,
				"rowno":2,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易时间",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_TIME",
				"is_find":"0",
				"id":319,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":83,
				"rowno":3,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"机构号",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NO",
				"is_find":"0",
				"id":83,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":326,
				"rowno":4,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NAME",
				"is_find":"0",
				"id":326,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":367,
				"rowno":5,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NO",
				"is_find":"0",
				"id":367,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":320,
				"rowno":6,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NAME",
				"is_find":"0",
				"id":320,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":394,
				"rowno":7,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属(发卡)机构",
				"type":"INT",
				"isfind":"0",
				"name":"FK_SITE_NO",
				"is_find":"0",
				"id":394,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":399,
				"rowno":8,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属机构名",
				"type":"INT",
				"isfind":"0",
				"name":"ACC_SITENAME",
				"is_find":"0",
				"id":399,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":327,
				"rowno":9,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易代码",
				"type":"INT",
				"isfind":"0",
				"name":"TX_CODE",
				"is_find":"0",
				"id":327,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":372,
				"rowno":10,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易名称",
				"type":"INT",
				"isfind":"0",
				"name":"TX_NAME",
				"is_find":"0",
				"id":372,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":315,
				"rowno":11,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易流水",
				"type":"INT",
				"isfind":"0",
				"name":"FLOW_ID",
				"is_find":"0",
				"id":315,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":406,
				"rowno":12,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账(卡)号",
				"type":"INT",
				"isfind":"0",
				"name":"LOSS_ACCOUNT_NO",
				"is_find":"0",
				"id":406,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":412,
				"rowno":13,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账号（旧）",
				"type":"INT",
				"isfind":"0",
				"name":"FORMER_LOSS_ACC",
				"is_find":"0",
				"id":412,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":314,
				"rowno":14,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"客户名称",
				"type":"INT",
				"isfind":"0",
				"name":"CUSTOMER_NAME",
				"is_find":"0",
				"id":314,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":421,
				"rowno":15,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件类型",
				"type":"INT",
				"isfind":"0",
				"name":"ID_TYPE",
				"is_find":"0",
				"id":421,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":417,
				"rowno":16,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件号",
				"type":"INT",
				"isfind":"0",
				"name":"ID_NO",
				"is_find":"0",
				"id":417,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":341,
				"rowno":17,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证种类",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_TYPE",
				"is_find":"0",
				"id":341,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":328,
				"rowno":18,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证代号",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_NO",
				"is_find":"0",
				"id":328,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":436,
				"rowno":19,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"产品代码/产品码",
				"type":"INT",
				"isfind":"0",
				"name":"PRODUCT_CODE",
				"is_find":"0",
				"id":436,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":376,
				"rowno":20,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易前凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"PRE_VOUCH_STATE",
				"is_find":"0",
				"id":376,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":381,
				"rowno":21,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易后凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"POST_VOUCH_STATE",
				"is_find":"0",
				"id":381,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":368,
				"rowno":22,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"复核柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"AUTH_TELLER_NO",
				"is_find":"0",
				"id":368,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":400,
				"rowno":23,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_TELLER_NAME",
				"is_find":"0",
				"id":400,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":410,
				"rowno":24,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构/核准机构号",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NO",
				"is_find":"0",
				"id":410,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":404,
				"rowno":25,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NAME",
				"is_find":"0",
				"id":404,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":397,
				"rowno":26,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"是否跨网点办理",
				"type":"INT",
				"isfind":"0",
				"name":"TRAN_SITE_HANDLE",
				"is_find":"0",
				"id":397,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":477,
				"rowno":27,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"联网核查结果",
				"type":"INT",
				"isfind":"0",
				"name":"FIELD_CONTENT_CN",
				"is_find":"0",
				"id":477,
				"field_type":1,
				"table_type":"5"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 09:33:19.925 [] [ab1af2ac7bae9312/05c0416c042e59d0] [http-nio-9060-exec-100] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-23 09:33:19 操作结束时间: 2025-07-23 09:33:19!总共花费时间: 63 毫秒！
2025-07-23 09:33:22.335 [OrganNo_00023_UserNo_admin] [1c416409d4deed11/e972db989a656bde] [http-nio-9060-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"model_id":"6",
			"modelrow_ids":[
				"112"
			]
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:34:56.507 [OrganNo_00023_UserNo_admin] [4034447ac8ba3cec/4b770c11d65f985b] [http-nio-9060-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"model_id":"6",
			"modelrow_ids":[
				"112"
			]
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:40:07.131 [OrganNo_00023_UserNo_admin] [908a99a07c518682/e2ebfc79efbccdcb] [http-nio-9060-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"model_id":"6",
			"model_row_id":"112"
		}
	],
	"sysMap":{
		"form_id":"C202507-14901-00004",
		"form_type":"2",
		"tableName":"ZD_1003_ZHANGHGSJG"
	}
}
2025-07-23 09:40:07.162 [OrganNo_00023_UserNo_admin] [908a99a07c518682/e2ebfc79efbccdcb] [http-nio-9060-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"预警状态更新成功"
}
2025-07-23 09:40:07.163 [] [908a99a07c518682/e2ebfc79efbccdcb] [http-nio-9060-exec-10] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 更新预警状态!请求IP地址: ************** 操作开始时间: 2025-07-23 09:40:07 操作结束时间: 2025-07-23 09:40:07!总共花费时间: 43 毫秒！
2025-07-23 09:40:07.193 [OrganNo_00023_UserNo_admin] [e40ef20ff454c20c/0776a0f2a1fc9d47] [http-nio-9060-exec-11] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"is_history":"0",
			"ishandle":"0",
			"modelInfo":{
				"modelName":"1003-账户挂失、解挂交易",
				"modelId":"6",
				"modelType":"1",
				"tableName":"ZD_1003_ZHANGHGSJG"
			},
			"model_id":"6"
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:40:07.239 [OrganNo_00023_UserNo_admin] [e40ef20ff454c20c/0776a0f2a1fc9d47] [http-nio-9060-exec-11] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"detailData":[
			{
				"LOSS_ACCOUNT_NO":"6216633100000485739",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行郫都红光支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":86,
				"FK_SITE_NO":"35905",
				"TX_CODE":"037241              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"15:34:53            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行郫都红光支行",
				"TX_NAME":"领非预制卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			}
		],
		"exhibitFields":[
			{
				"field_id":366,
				"rowno":1,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"业务日期",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_DATE",
				"is_find":"0",
				"id":366,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":319,
				"rowno":2,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易时间",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_TIME",
				"is_find":"0",
				"id":319,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":83,
				"rowno":3,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"机构号",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NO",
				"is_find":"0",
				"id":83,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":326,
				"rowno":4,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NAME",
				"is_find":"0",
				"id":326,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":367,
				"rowno":5,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NO",
				"is_find":"0",
				"id":367,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":320,
				"rowno":6,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NAME",
				"is_find":"0",
				"id":320,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":394,
				"rowno":7,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属(发卡)机构",
				"type":"INT",
				"isfind":"0",
				"name":"FK_SITE_NO",
				"is_find":"0",
				"id":394,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":399,
				"rowno":8,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属机构名",
				"type":"INT",
				"isfind":"0",
				"name":"ACC_SITENAME",
				"is_find":"0",
				"id":399,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":327,
				"rowno":9,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易代码",
				"type":"INT",
				"isfind":"0",
				"name":"TX_CODE",
				"is_find":"0",
				"id":327,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":372,
				"rowno":10,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易名称",
				"type":"INT",
				"isfind":"0",
				"name":"TX_NAME",
				"is_find":"0",
				"id":372,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":315,
				"rowno":11,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易流水",
				"type":"INT",
				"isfind":"0",
				"name":"FLOW_ID",
				"is_find":"0",
				"id":315,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":406,
				"rowno":12,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账(卡)号",
				"type":"INT",
				"isfind":"0",
				"name":"LOSS_ACCOUNT_NO",
				"is_find":"0",
				"id":406,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":412,
				"rowno":13,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账号（旧）",
				"type":"INT",
				"isfind":"0",
				"name":"FORMER_LOSS_ACC",
				"is_find":"0",
				"id":412,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":314,
				"rowno":14,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"客户名称",
				"type":"INT",
				"isfind":"0",
				"name":"CUSTOMER_NAME",
				"is_find":"0",
				"id":314,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":421,
				"rowno":15,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件类型",
				"type":"INT",
				"isfind":"0",
				"name":"ID_TYPE",
				"is_find":"0",
				"id":421,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":417,
				"rowno":16,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件号",
				"type":"INT",
				"isfind":"0",
				"name":"ID_NO",
				"is_find":"0",
				"id":417,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":341,
				"rowno":17,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证种类",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_TYPE",
				"is_find":"0",
				"id":341,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":328,
				"rowno":18,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证代号",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_NO",
				"is_find":"0",
				"id":328,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":436,
				"rowno":19,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"产品代码/产品码",
				"type":"INT",
				"isfind":"0",
				"name":"PRODUCT_CODE",
				"is_find":"0",
				"id":436,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":376,
				"rowno":20,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易前凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"PRE_VOUCH_STATE",
				"is_find":"0",
				"id":376,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":381,
				"rowno":21,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易后凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"POST_VOUCH_STATE",
				"is_find":"0",
				"id":381,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":368,
				"rowno":22,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"复核柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"AUTH_TELLER_NO",
				"is_find":"0",
				"id":368,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":400,
				"rowno":23,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_TELLER_NAME",
				"is_find":"0",
				"id":400,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":410,
				"rowno":24,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构/核准机构号",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NO",
				"is_find":"0",
				"id":410,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":404,
				"rowno":25,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NAME",
				"is_find":"0",
				"id":404,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":397,
				"rowno":26,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"是否跨网点办理",
				"type":"INT",
				"isfind":"0",
				"name":"TRAN_SITE_HANDLE",
				"is_find":"0",
				"id":397,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":477,
				"rowno":27,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"联网核查结果",
				"type":"INT",
				"isfind":"0",
				"name":"FIELD_CONTENT_CN",
				"is_find":"0",
				"id":477,
				"field_type":1,
				"table_type":"5"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 09:40:07.240 [] [e40ef20ff454c20c/0776a0f2a1fc9d47] [http-nio-9060-exec-11] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-23 09:40:07 操作结束时间: 2025-07-23 09:40:07!总共花费时间: 64 毫秒！
2025-07-23 09:42:18.330 [OrganNo_00023_UserNo_admin] [d65ce7cefc345ccf/97500ec5850d150e] [http-nio-9060-exec-14] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"model_id":"6",
			"modelrow_ids":[
				"86"
			]
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:46:56.278 [OrganNo_00023_UserNo_admin] [3ac96680757dff70/c0d9cb842e87b672] [http-nio-9060-exec-22] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"model_id":"6",
			"model_row_id":"86"
		}
	],
	"sysMap":{
		"form_id":"C202507-14901-00005",
		"form_type":"2",
		"tableName":"ZD_1003_ZHANGHGSJG"
	}
}
2025-07-23 09:46:56.306 [OrganNo_00023_UserNo_admin] [3ac96680757dff70/c0d9cb842e87b672] [http-nio-9060-exec-22] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"预警状态更新成功"
}
2025-07-23 09:46:56.307 [] [3ac96680757dff70/c0d9cb842e87b672] [http-nio-9060-exec-22] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 更新预警状态!请求IP地址: ************** 操作开始时间: 2025-07-23 09:46:56 操作结束时间: 2025-07-23 09:46:56!总共花费时间: 36 毫秒！
2025-07-23 09:46:56.329 [OrganNo_00023_UserNo_admin] [f207d529ee13cd19/6e35cd116c8a4a0d] [http-nio-9060-exec-23] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"is_history":"0",
			"ishandle":"0",
			"modelInfo":{
				"modelName":"1003-账户挂失、解挂交易",
				"modelId":"6",
				"modelType":"1",
				"tableName":"ZD_1003_ZHANGHGSJG"
			},
			"model_id":"6"
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:46:56.367 [OrganNo_00023_UserNo_admin] [f207d529ee13cd19/6e35cd116c8a4a0d] [http-nio-9060-exec-23] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"detailData":[
			{
				"LOSS_ACCOUNT_NO":"6217563100016683998",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行珙县支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":112,
				"FK_SITE_NO":"15263",
				"TX_CODE":"037223              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"10:48:35            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行珙县支行",
				"TX_NAME":"销卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			}
		],
		"exhibitFields":[
			{
				"field_id":366,
				"rowno":1,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"业务日期",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_DATE",
				"is_find":"0",
				"id":366,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":319,
				"rowno":2,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易时间",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_TIME",
				"is_find":"0",
				"id":319,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":83,
				"rowno":3,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"机构号",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NO",
				"is_find":"0",
				"id":83,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":326,
				"rowno":4,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NAME",
				"is_find":"0",
				"id":326,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":367,
				"rowno":5,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NO",
				"is_find":"0",
				"id":367,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":320,
				"rowno":6,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NAME",
				"is_find":"0",
				"id":320,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":394,
				"rowno":7,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属(发卡)机构",
				"type":"INT",
				"isfind":"0",
				"name":"FK_SITE_NO",
				"is_find":"0",
				"id":394,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":399,
				"rowno":8,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属机构名",
				"type":"INT",
				"isfind":"0",
				"name":"ACC_SITENAME",
				"is_find":"0",
				"id":399,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":327,
				"rowno":9,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易代码",
				"type":"INT",
				"isfind":"0",
				"name":"TX_CODE",
				"is_find":"0",
				"id":327,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":372,
				"rowno":10,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易名称",
				"type":"INT",
				"isfind":"0",
				"name":"TX_NAME",
				"is_find":"0",
				"id":372,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":315,
				"rowno":11,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易流水",
				"type":"INT",
				"isfind":"0",
				"name":"FLOW_ID",
				"is_find":"0",
				"id":315,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":406,
				"rowno":12,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账(卡)号",
				"type":"INT",
				"isfind":"0",
				"name":"LOSS_ACCOUNT_NO",
				"is_find":"0",
				"id":406,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":412,
				"rowno":13,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账号（旧）",
				"type":"INT",
				"isfind":"0",
				"name":"FORMER_LOSS_ACC",
				"is_find":"0",
				"id":412,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":314,
				"rowno":14,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"客户名称",
				"type":"INT",
				"isfind":"0",
				"name":"CUSTOMER_NAME",
				"is_find":"0",
				"id":314,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":421,
				"rowno":15,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件类型",
				"type":"INT",
				"isfind":"0",
				"name":"ID_TYPE",
				"is_find":"0",
				"id":421,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":417,
				"rowno":16,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件号",
				"type":"INT",
				"isfind":"0",
				"name":"ID_NO",
				"is_find":"0",
				"id":417,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":341,
				"rowno":17,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证种类",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_TYPE",
				"is_find":"0",
				"id":341,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":328,
				"rowno":18,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证代号",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_NO",
				"is_find":"0",
				"id":328,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":436,
				"rowno":19,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"产品代码/产品码",
				"type":"INT",
				"isfind":"0",
				"name":"PRODUCT_CODE",
				"is_find":"0",
				"id":436,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":376,
				"rowno":20,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易前凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"PRE_VOUCH_STATE",
				"is_find":"0",
				"id":376,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":381,
				"rowno":21,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易后凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"POST_VOUCH_STATE",
				"is_find":"0",
				"id":381,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":368,
				"rowno":22,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"复核柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"AUTH_TELLER_NO",
				"is_find":"0",
				"id":368,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":400,
				"rowno":23,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_TELLER_NAME",
				"is_find":"0",
				"id":400,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":410,
				"rowno":24,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构/核准机构号",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NO",
				"is_find":"0",
				"id":410,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":404,
				"rowno":25,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NAME",
				"is_find":"0",
				"id":404,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":397,
				"rowno":26,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"是否跨网点办理",
				"type":"INT",
				"isfind":"0",
				"name":"TRAN_SITE_HANDLE",
				"is_find":"0",
				"id":397,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":477,
				"rowno":27,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"联网核查结果",
				"type":"INT",
				"isfind":"0",
				"name":"FIELD_CONTENT_CN",
				"is_find":"0",
				"id":477,
				"field_type":1,
				"table_type":"5"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 09:46:56.368 [] [f207d529ee13cd19/6e35cd116c8a4a0d] [http-nio-9060-exec-23] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-23 09:46:56 操作结束时间: 2025-07-23 09:46:56!总共花费时间: 49 毫秒！
2025-07-23 09:46:56.413 [OrganNo_00023_UserNo_admin] [bca73a92bb87ee5e/69685a33e0899699] [http-nio-9060-exec-24] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"model_id":"6",
			"model_row_id":"86"
		}
	],
	"sysMap":{
		"form_id":"C202507-14901-00005",
		"form_type":"2",
		"tableName":"ZD_1003_ZHANGHGSJG"
	}
}
2025-07-23 09:46:56.443 [OrganNo_00023_UserNo_admin] [bca73a92bb87ee5e/69685a33e0899699] [http-nio-9060-exec-24] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"预警状态更新成功"
}
2025-07-23 09:46:56.443 [] [bca73a92bb87ee5e/69685a33e0899699] [http-nio-9060-exec-24] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 更新预警状态!请求IP地址: ************** 操作开始时间: 2025-07-23 09:46:56 操作结束时间: 2025-07-23 09:46:56!总共花费时间: 42 毫秒！
2025-07-23 09:46:56.463 [OrganNo_00023_UserNo_admin] [3a8cd69afcf3a5b7/2c3ddcd634f48241] [http-nio-9060-exec-25] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"is_history":"0",
			"ishandle":"0",
			"modelInfo":{
				"modelName":"1003-账户挂失、解挂交易",
				"modelId":"6",
				"modelType":"1",
				"tableName":"ZD_1003_ZHANGHGSJG"
			},
			"model_id":"6"
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:46:56.505 [OrganNo_00023_UserNo_admin] [3a8cd69afcf3a5b7/2c3ddcd634f48241] [http-nio-9060-exec-25] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"detailData":[
			{
				"LOSS_ACCOUNT_NO":"6217563100016683998",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行珙县支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":112,
				"FK_SITE_NO":"15263",
				"TX_CODE":"037223              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"10:48:35            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行珙县支行",
				"TX_NAME":"销卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			}
		],
		"exhibitFields":[
			{
				"field_id":366,
				"rowno":1,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"业务日期",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_DATE",
				"is_find":"0",
				"id":366,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":319,
				"rowno":2,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易时间",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_TIME",
				"is_find":"0",
				"id":319,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":83,
				"rowno":3,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"机构号",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NO",
				"is_find":"0",
				"id":83,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":326,
				"rowno":4,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NAME",
				"is_find":"0",
				"id":326,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":367,
				"rowno":5,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NO",
				"is_find":"0",
				"id":367,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":320,
				"rowno":6,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NAME",
				"is_find":"0",
				"id":320,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":394,
				"rowno":7,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属(发卡)机构",
				"type":"INT",
				"isfind":"0",
				"name":"FK_SITE_NO",
				"is_find":"0",
				"id":394,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":399,
				"rowno":8,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属机构名",
				"type":"INT",
				"isfind":"0",
				"name":"ACC_SITENAME",
				"is_find":"0",
				"id":399,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":327,
				"rowno":9,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易代码",
				"type":"INT",
				"isfind":"0",
				"name":"TX_CODE",
				"is_find":"0",
				"id":327,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":372,
				"rowno":10,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易名称",
				"type":"INT",
				"isfind":"0",
				"name":"TX_NAME",
				"is_find":"0",
				"id":372,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":315,
				"rowno":11,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易流水",
				"type":"INT",
				"isfind":"0",
				"name":"FLOW_ID",
				"is_find":"0",
				"id":315,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":406,
				"rowno":12,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账(卡)号",
				"type":"INT",
				"isfind":"0",
				"name":"LOSS_ACCOUNT_NO",
				"is_find":"0",
				"id":406,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":412,
				"rowno":13,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账号（旧）",
				"type":"INT",
				"isfind":"0",
				"name":"FORMER_LOSS_ACC",
				"is_find":"0",
				"id":412,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":314,
				"rowno":14,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"客户名称",
				"type":"INT",
				"isfind":"0",
				"name":"CUSTOMER_NAME",
				"is_find":"0",
				"id":314,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":421,
				"rowno":15,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件类型",
				"type":"INT",
				"isfind":"0",
				"name":"ID_TYPE",
				"is_find":"0",
				"id":421,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":417,
				"rowno":16,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件号",
				"type":"INT",
				"isfind":"0",
				"name":"ID_NO",
				"is_find":"0",
				"id":417,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":341,
				"rowno":17,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证种类",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_TYPE",
				"is_find":"0",
				"id":341,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":328,
				"rowno":18,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证代号",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_NO",
				"is_find":"0",
				"id":328,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":436,
				"rowno":19,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"产品代码/产品码",
				"type":"INT",
				"isfind":"0",
				"name":"PRODUCT_CODE",
				"is_find":"0",
				"id":436,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":376,
				"rowno":20,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易前凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"PRE_VOUCH_STATE",
				"is_find":"0",
				"id":376,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":381,
				"rowno":21,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易后凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"POST_VOUCH_STATE",
				"is_find":"0",
				"id":381,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":368,
				"rowno":22,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"复核柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"AUTH_TELLER_NO",
				"is_find":"0",
				"id":368,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":400,
				"rowno":23,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_TELLER_NAME",
				"is_find":"0",
				"id":400,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":410,
				"rowno":24,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构/核准机构号",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NO",
				"is_find":"0",
				"id":410,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":404,
				"rowno":25,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NAME",
				"is_find":"0",
				"id":404,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":397,
				"rowno":26,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"是否跨网点办理",
				"type":"INT",
				"isfind":"0",
				"name":"TRAN_SITE_HANDLE",
				"is_find":"0",
				"id":397,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":477,
				"rowno":27,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"联网核查结果",
				"type":"INT",
				"isfind":"0",
				"name":"FIELD_CONTENT_CN",
				"is_find":"0",
				"id":477,
				"field_type":1,
				"table_type":"5"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 09:46:56.506 [] [3a8cd69afcf3a5b7/2c3ddcd634f48241] [http-nio-9060-exec-25] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警明细数据!请求IP地址: ************** 操作开始时间: 2025-07-23 09:46:56 操作结束时间: 2025-07-23 09:46:56!总共花费时间: 50 毫秒！
2025-07-23 09:47:38.001 [OrganNo_00023_UserNo_admin] [18d251c53eb1ac9a/3a1b4a67d4bf67fa] [http-nio-9060-exec-28] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"model_id":"6",
			"modelrow_ids":[
				"112"
			]
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 09:48:19.885 [OrganNo_00023_UserNo_admin] [d27968725cce26c0/58c67f6e322d0613] [http-nio-9060-exec-30] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"model_id":"6",
			"modelrow_ids":[
				"112"
			]
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 19:36:25.203 [OrganNo_00023_UserNo_admin] [a12f92146077025d/d480a1ace7bb6eef] [http-nio-9060-exec-35] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"getAllModelInfos"
	}
}
2025-07-23 19:36:25.261 [OrganNo_00023_UserNo_admin] [a12f92146077025d/d480a1ace7bb6eef] [http-nio-9060-exec-35] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"modelList":[
			{
				"relating_model_id":1,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":2,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":1,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":3,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":4,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":3,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":5,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":6,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":5,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":7,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":8,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":7,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":9,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":10,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":9,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":11,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":12,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":11,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":13,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":14,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":13,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":15,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":16,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":15,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":17,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":18,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":17,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":19,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":20,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":19,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":21,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":22,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":21,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":23,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":24,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":23,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":25,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":26,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":25,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":27,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":28,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":27,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":29,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":30,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":29,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":31,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":32,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":31,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":33,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":34,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":33,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":36,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1018-经集中核准的人民币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":35,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1018-经集中核准的人民币大额交易(明细)  ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":36,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":38,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":37,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"1"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":38,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":40,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1020-经集中核准的司法扣划交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":39,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1020-经集中核准的司法扣划交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":40,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":42,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":41,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":42,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":44,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":43,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":44,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":46,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":45,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":46,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":48,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":47,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":48,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":50,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":49,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":50,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":52,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":51,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":52,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 19:36:25.262 [] [a12f92146077025d/d480a1ace7bb6eef] [http-nio-9060-exec-35] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 获取预警模型列表!请求IP地址: ************** 操作开始时间: 2025-07-23 19:36:25 操作结束时间: 2025-07-23 19:36:25!总共花费时间: 101 毫秒！
2025-07-23 19:36:27.177 [OrganNo_00023_UserNo_admin] [7ae6994cc88b2df3/d2053d368b3d7ecd] [http-nio-9060-exec-36] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"end_date":"20250722",
			"model_id":"",
			"site_no":"",
			"start_date":"20250622"
		}
	],
	"sysMap":{
		
	}
}
2025-07-23 19:36:27.268 [OrganNo_00023_UserNo_admin] [7ae6994cc88b2df3/d2053d368b3d7ecd] [http-nio-9060-exec-36] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"riskWarningStats":[
			
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 19:36:27.269 [] [7ae6994cc88b2df3/d2053d368b3d7ecd] [http-nio-9060-exec-36] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 预警信息查询 操作方法: 查询预警信息统计数据!请求IP地址: ************** 操作开始时间: 2025-07-23 19:36:27 操作结束时间: 2025-07-23 19:36:27!总共花费时间: 118 毫秒！
