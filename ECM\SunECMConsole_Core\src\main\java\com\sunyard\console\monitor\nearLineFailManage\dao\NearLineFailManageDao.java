package com.sunyard.console.monitor.nearLineFailManage.dao;

import com.sunyard.console.monitor.nearLineFailManage.bean.NearLineFailInfoBean;

import java.util.List;

public interface NearLineFailManageDao {
	/**
	 * 查询近线失败的批次
	 * 
	 * @param model_code
	 *            模型代码
	 * @param content_id
	 *            批次id
	 * @param fail_time
	 *            失败时间
	 * @return
	 */
	public List<NearLineFailInfoBean> getNearLineFailList(String model_code,
			String content_id, String fail_time);

	/**
	 * 分页查询近线失败的批次
	 * 
	 * @param model_code
	 *            模型代码
	 * @param content_id
	 *            批次id
	 * @param fail_time
	 *            迁移失败时间
	 * @param start
	 *            开始时间
	 * @param limit
	 *            结束时间
	 * @return
	 */
	public List<NearLineFailInfoBean> getNearLineFailList(String model_code,
			String content_id, String fail_time, int start, int limit);

	/**
	 * 将近线失败的批次状态设为3，并删除近线失败记录表数据
	 * 
	 * @param reNearLineFaileBatch
	 *            记录要重新迁移的批次信息，一个批次格式 content_id>>group_id>>version>>table_name
	 *            各个批次用;隔开
	 * @return
	 */
	public boolean reNearLineFaileBatch(String reNearLineFaileBatch);
	
}
