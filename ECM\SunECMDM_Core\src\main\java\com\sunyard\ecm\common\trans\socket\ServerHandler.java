package com.sunyard.ecm.common.trans.socket;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.Socket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ServerHandler implements Runnable{
	Socket socket = null;
	private static final Logger log = LoggerFactory
			.getLogger(ServerHandler.class);
	public ServerHandler(Socket socket) {
		this.socket = socket;
	}

	public void run() {
		try {
			BufferedReader in = new BufferedReader(new InputStreamReader(
					socket.getInputStream()));
			log.info("accept:" + in.readLine());
			BufferedWriter out = new BufferedWriter(new OutputStreamWriter(
					socket.getOutputStream()));
			out.write("abc return");
			out.newLine();
			out.flush();
			socket.close();
		} catch (IOException e) {
			log.error("出错",e);
		}
	}
}