package com.sunyard.console.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 近线系统参数
 * 
 * <AUTHOR>
 *
 */
public class NearlineConstant {
	private final static Logger log = LoggerFactory.getLogger(NearlineConstant.class);
	// 近线系统应用名
	private static String NearlineName = null;
	// 近线ip
	private static String NearlineIP = null;
	// 近线Http端口
	private static String NearlineHttpPort = null;

	/**
	 * 初始化加载系统配置文件
	 */
	private static void init() {
		Properties props = new Properties();
		InputStream in = null;
		try {
			ClassLoader cloader = NearlineConstant.class.getClassLoader();
			if (cloader != null) {
				in = cloader.getResourceAsStream("consoleConfig.properties");
			} else {
				throw new IOException("CLASSLOADER IS NULL");
			}
			try {
				props.load(in);
				NearlineName = props.getProperty("server_NL_Name");
				NearlineIP = props.getProperty("server_NL_IP");
				NearlineHttpPort = props.getProperty("server_NL_HttpPort");
			} finally {
				in.close();
			}
		} catch (IOException e) {
			log.error(e.toString());
			throw new RuntimeException("加载本地配置consoleConfig.properties文件错误....");
		}
	}

	/**
	 * 获取近线系统应用名
	 * 
	 * @return
	 */
	public static String getNLName() {
		if (NearlineName == null) {
			init();
		}
		return NearlineName;
	}

	/**
	 * 获取近线ip
	 * 
	 * @return
	 */
	public static String getNLIp() {
		if (NearlineIP == null) {
			init();
		}
		return NearlineIP;
	}

	/**
	 * 获取近线Http端口
	 * 
	 * @return
	 */
	public static String getNLHttpPort() {
		if (NearlineHttpPort == null) {
			init();
		}
		return NearlineHttpPort;
	}
}
