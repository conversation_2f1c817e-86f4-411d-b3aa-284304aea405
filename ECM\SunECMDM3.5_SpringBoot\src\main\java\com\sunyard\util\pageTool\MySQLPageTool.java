package com.sunyard.util.pageTool;

/**
 * <p>
 * Title: oracle数据库查询分页
 * </p>
 * <p>
 * Description: oracle数据库查询分页
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class MySQLPageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
		// int rownum = start + limit - 1;
		 int beginRowNum=start-1;
		if(beginRowNum<0){
			beginRowNum=0;
		}
		return sql + " limit " + beginRowNum+ "," + limit + " ";
	}

	public String getTopSql(String sql, int top) {
		return sql + " limit " + top;
	}

	public String getRandSql(String sql, int top) {
		return sql + " limit " + top;
	}
public String getMaxVersionAndGroupInDMDB() {
	return "MYSQL_getMaxVersionAndGroupInDMDB";
}


public String getDistinctRandSql(String sql, int top) {
	return getRandSql(sql,top);
}

}
