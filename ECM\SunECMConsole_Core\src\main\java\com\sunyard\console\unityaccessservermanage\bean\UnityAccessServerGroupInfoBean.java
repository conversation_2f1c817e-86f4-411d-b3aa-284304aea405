package com.sunyard.console.unityaccessservermanage.bean;

/**
 * 统一接入服务器组信息bean
 * 
 * <AUTHOR>
 * 
 */
public class UnityAccessServerGroupInfoBean {
	/**
	 * 服务器组id
	 */
	private int group_id;
	/**
	 * 服务器组名称
	 */
	private String group_name;
	/**
	 * 服务器组ip
	 */
	private String ip;
	/**
	 * http端口
	 */
	private int http_port;
	/**
	 * socket端口
	 */
	private int socket_port;
	/**
	 * 备注
	 */
	private String remark;

	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int groupId) {
		group_id = groupId;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String groupName) {
		group_name = groupName;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public int getHttp_port() {
		return http_port;
	}

	public void setHttp_port(int httpPort) {
		http_port = httpPort;
	}

	public int getSocket_port() {
		return socket_port;
	}

	public void setSocket_port(int socketPort) {
		socket_port = socketPort;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	@Override
	public String toString() {
		// TODO Auto-generated method stub
		StringBuilder sb = new StringBuilder();
		sb.append("group_id:").append(group_id);
		sb.append(";group_name:").append(group_name);
		sb.append(";ip:").append(ip);
		sb.append(";http_port:").append(http_port);
		sb.append(";socket_port:").append(socket_port);
		sb.append(";remark:").append(remark);
		return sb.toString();
	}
}
