package com.sunyard.ecm.server.event.impl;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.initialization.LoadConfigFile;
import com.sunyard.util.DateUtil;

public class FileRecordUtil {
	private final static Logger log = LoggerFactory.getLogger(FileRecordUtil.class);
	// 用来记录模型的当前文件时间
	private static List<FileObj> fileList = Collections.synchronizedList(new ArrayList<FileObj>());
	private static String ROOT = LoadConfigFile.getInstance().getConfigBean().getFileRecordPath();

	/**
	 * 初始化list
	 * 
	 * @param modelCode
	 * @param date
	 */
	public static void putFileList(String modelCode, String date) {
		String path;
		try {
			File file = new File(ROOT);
			if (!file.exists()) {
				log.info("createdir[" + ROOT);
				file.mkdir();
			}
			// 文件格式shjd_20151020.txt:
			path = ROOT + "/" + modelCode + "_" + date + ".txt";
			FileWriter out = new FileWriter(new File(path),true);
			FileObj obj = new FileObj();
			obj.setModelCode(modelCode);
			obj.setFilePath(path);
			obj.setOut(out);
			obj.setDate(date);
			fileList.add(obj);
		} catch (IOException e) {
			log.error("初始化文件对象失败", e);
		}
	}

	/**
	 * 记录增加文件
	 */
	public synchronized static void writeRecord(String modelCode, String msg) {
		if (fileList != null && fileList.size() > 0) {
			log.info("modelCode:" + modelCode + ",msg[" + msg + "]");
			String date;
			FileWriter out;
			String filePath = null;
			String now;
			for (FileObj obj : fileList) {
				if (obj.getModelCode().equals(modelCode)) {
					date = obj.getDate();
					now = DateUtil.get8bitDateStr();
					if (checkNeedClose(now, date)) {
						log.debug("新的一天，关闭旧文件，打开新文件");
						// 关闭文件
						try {
							obj.getOut().close();
							filePath = ROOT + "/" + modelCode + "_" + now + ".txt";
							log.debug("要关闭的文件路径[" + obj.getFilePath() + "]");
							log.debug("要新建的文件路径[" + filePath + "]");
							File file = new File(filePath);
							if (!file.exists()) {
								log.debug("创建新的记录文件" + filePath);
								file.createNewFile();
							}
							out = new FileWriter(file,true);
							obj.setOut(out);
							obj.setFilePath(filePath);
							obj.setDate(now);
						} catch (IOException e) {
							log.error("创建新文件出错" + obj.getDate() + "要关闭的文件" + obj.getFilePath() + ",要新建的文件路径" + filePath + ",要新增加的消息为msg[" + msg + "]", e);
						}
					}
					out = obj.getOut();
					try {
						out.write(msg);
						out.write("\r\n");
						out.flush();
					} catch (IOException e) {
						log.error("写文件出错，filepath=" + obj.getFilePath() + ",要新增加的消息为msg[" + msg + "]", e);
					}
				}

			}
		} else {
			log.warn("fileList is null");
		}
	}

//	public static void main(String[] args) {
//		for (int i = 0; i < 10; i++) {
//			FileRecordUtil t = new FileRecordUtil();
//			new Thread(t.createTask(i)).start();
//		}
//	}

	// 多线程客户端
//	private Runnable createTask(final int taskID) {
//		return new Runnable() {
//			public void run() {
//				log.info("现在进行第[" + taskID + "]个任务");
//				for (int i = 0; i < 3; i++) {
//					// putFileList("bwj" + i, "20160310");
//					writeRecord("bwj" + i, "nihao" + i);
//				}
//
//			}
//		};
//	}

	/**
	 * 比较两个时间大小,now>date返回true
	 * 
	 * @param now
	 * @param date
	 */
	public static boolean checkNeedClose(String now, String date) {
		log.debug(now + "-" + date);
		Calendar lastCal = Calendar.getInstance();
		Calendar nowCal = Calendar.getInstance();
		// 提前一小时去建表
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		try {
			lastCal.setTime(sdf.parse(date));
			nowCal.setTime(sdf.parse(now));
			// 判断是否到达创建表的时间
			return nowCal.compareTo(lastCal) > 0;
		} catch (ParseException e) {
			return false;
		}
	}
}
