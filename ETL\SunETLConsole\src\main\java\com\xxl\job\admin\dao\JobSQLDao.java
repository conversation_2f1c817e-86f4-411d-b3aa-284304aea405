package com.xxl.job.admin.dao;

import com.xxl.job.admin.core.model.JobParam;
import com.xxl.job.admin.core.model.JobSql;
import com.xxl.job.admin.core.model.XxlJobInfo;
import com.xxl.job.admin.core.model.XxlJobRegistry;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created by x<PERSON><PERSON><PERSON> on 16/9/30.
 */
public interface JobSQLDao {

    public List<JobSql> pageList(@Param("id") int id);
    public int pageListCount(@Param("id") int id);

    public int save(JobSql jobSql);


    public int update(JobSql jobSql);

    public int delete(@Param("id") int id);

    public Integer getMaxId();

}
