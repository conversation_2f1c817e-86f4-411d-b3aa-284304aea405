package com.sunyard.es.util;

public class EsMappingBean {
	//用户自定字段
	private String data;
	//es字段类型,字符串类型(keyword,text)整形(byte、short、integer、long)浮点型(float、double)日期(date)
	private String type;
	//是否被索引(默认为true)
	private String isIndex;
	//字段可索引的长度
	private Integer length;
	
	public String getData() {
		return data;
	}
	public void setData(String data) {
		this.data = data;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getIsIndex() {
		return isIndex;
	}
	public void setIsIndex(String isIndex) {
		this.isIndex = isIndex;
	}
	public Integer getLength() {
		return length;
	}
	public void setLength(Integer length) {
		this.length = length;
	}
	@Override
	public String toString() {
		return "EsMappingBean [data=" + data + ", type=" + type + ", isIndex=" + isIndex + ", length=" + length + "]";
	}
	
}
