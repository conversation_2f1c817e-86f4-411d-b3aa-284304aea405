package com.sunyard.client.conn;


import java.io.IOException;
import java.net.Socket;
import java.net.UnknownHostException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import com.sunyard.ecm.server.bean.NodeInfo;
import com.sunyard.ecm.server.cache.LazySingleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.initialization.LoadConfigFile;
import com.syd.common.serviceinit.ServiceInterface;

public class HeartBeatOfDM implements ServiceInterface, Runnable {
	// 服务器组（使用ECM负载均衡）中可以联通的服务器map（key为服务器组id，list为服务器信息）
	public static Map<String, CopyOnWriteArrayList<NodeInfo>> aliveServers = new ConcurrentHashMap<String, CopyOnWriteArrayList<NodeInfo>>();
	// 服务器组（使用ECM负载均衡）中无法连接的服务器map（key为服务器组id，list为服务器信息）
	public static Map<String, CopyOnWriteArrayList<NodeInfo>> deadServers = new ConcurrentHashMap<String, CopyOnWriteArrayList<NodeInfo>>();

	private final ScheduledExecutorService scheduledPool = Executors.newScheduledThreadPool(2);
	private final static Logger log = LoggerFactory.getLogger(HeartBeatOfDM.class);

	public String getServiceName() {
		return "HeartBeatOfDM";
	}

	public boolean start() {
		Thread socket = new Thread(this);
		socket.start();
		return true;
	};

	/**
	 * 打印map值
	 * 
	 * @param maps
	 * @return
	 */
	private String print(Map<String, CopyOnWriteArrayList<NodeInfo>> maps) {
		StringBuilder sb = new StringBuilder();
		for (Entry<String, CopyOnWriteArrayList<NodeInfo>> entry : maps.entrySet()) {
			sb.append("[groupName=" + entry.getKey());
			sb.append(",serverInfo:");
			for (NodeInfo node : entry.getValue()) {
				sb.append(node.getServer_ip() + ":" + node.getSocket_port());
				sb.append(",");
			}
			sb.append("]");
		}

		return sb.toString();
	}

	public void run() {
		init();
	}

	public void stop() {
		scheduledPool.shutdownNow();
	}
	public void init() {
		try {
			log.info("begin to get heart");
			initAliveServers();

			// 启动检查活动心跳线程
			scheduledPool.scheduleWithFixedDelay(new Runnable() {
				public void run() {
					log.info("begin to checkAliveServers");
					checkAliveServers();
					log.debug("AliveServers:" + print(aliveServers));
					log.debug("DeadServers:" + print(deadServers) + "\n\n");
				}
			}, 4, LoadConfigFile.getInstance().getConfigBean().getClient_dm_heartbeat_alive(), TimeUnit.SECONDS);

			// 启动检查死亡心跳线程
			scheduledPool.scheduleWithFixedDelay(new Runnable() {
				public void run() {
					log.info("begin to checkDeadServers");
					checkDeadServers();
					log.info("AliveServers:" + print(aliveServers));
					log.info("DeadServers:" + print(deadServers) + "\n\n");
				}
			}, 5, LoadConfigFile.getInstance().getConfigBean().getClient_dm_heartbeat_dead(), TimeUnit.SECONDS);
		} catch (Exception e) {
			log.error("heartBeatOfDM init error-", e);
		}
	}

//	public static void main(String[] args) {
//		HeartBeatOfDM test = new HeartBeatOfDM();
//		test.init();
//
//		try {
//			Thread.sleep(10 * 60 * 1000);
//		} catch (InterruptedException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//	}

	/**
	 * 获取启用的服务器信息
	 */
	public static void initAliveServers() {
		initAllServers();
		// mockAliveServers();
	}

	/**
	 * 初始化活动服务器和死亡服务器，默认所有服务器为活动状态，死亡服务器默认为空
	 */
	public static void initAllServers() {
		log.info("begin to initAllServers");
		aliveServers.clear();
		deadServers.clear();

		// 从内存中获取所有服务器信息
		Map<String, NodeInfo> map = LazySingleton.getInstance().allNodeInfoTable.getAllNodeInfoTable();
		Set<String> set = map.keySet();
		String groupName;
		// 存放所有服务器组名称
		List<String> allGroupName = new ArrayList<String>();
		CopyOnWriteArrayList<NodeInfo> nodelist = null;
		for (Iterator<String> iterator = set.iterator(); iterator.hasNext();) {
			// 初始化活动服务器
			String serverID = (String) iterator.next();
			if (serverID.startsWith("UA")) {
				continue;
			}
			NodeInfo node = map.get(serverID);
			if (!"0".equals(node.getDeploy_mode())) {
				// 不是ECM负载均衡，抛弃
				continue;
			}
			groupName = node.getGroup_name();
			nodelist = aliveServers.get(groupName);
			if (nodelist != null) {
				aliveServers.get(groupName).addIfAbsent(node);
			} else {
				nodelist = new CopyOnWriteArrayList<NodeInfo>();
				nodelist.add(node);
				aliveServers.put(groupName, nodelist);
			}
			allGroupName.add(groupName);
		}
		for (String gname : allGroupName) {
			// 初始化死亡服务器
			deadServers.put(gname, new CopyOnWriteArrayList<NodeInfo>());
		}
		log.info("end to initAllServers");

	}

	/**
	 * 模拟服务器列表
	 */
	public static void mockAliveServers() {
		for (int groupSize = 0; groupSize < 1; groupSize++) {
			// 取group的数目
			CopyOnWriteArrayList<NodeInfo> list = new CopyOnWriteArrayList<NodeInfo>();
			for (int i = 0; i < 3; i++) {
				// 取同一个组下的serverbean
				NodeInfo serverBean = new NodeInfo();
				serverBean.setServer_id(String.valueOf(i));
				serverBean.setServer_ip("ip-" + i);
				serverBean.setSocket_port("802" + i);
				serverBean.setState("1");
				list.add(serverBean);
			}
			aliveServers.put("Group-" + groupSize, list);
			deadServers.put("Group-" + groupSize, new CopyOnWriteArrayList<NodeInfo>());
		}
	}

	/**
	 * 根据group信息获取本组可用的server信息
	 * 
	 * @param group
	 */
	public static List<NodeInfo> getAliveServersByGroup(String group) {
		if (aliveServers != null) {
			return aliveServers.get(group);
		}
		return null;
	}

	/**
	 * 判断启用的服务器是否可以连接，如果无法连接，则将该服务器信息在startServerMap中删除，并将其 放入stopServerMap中
	 * 
	 */
	public static void checkAliveServers() {
		if (aliveServers == null) {
			log.warn("begin to checkAliveServers,aliveServer is null");
		} 
		boolean connected = true;

		for (Entry<String, CopyOnWriteArrayList<NodeInfo>> entry : aliveServers.entrySet()) {
			// 遍历启用的服务器组map
			List<NodeInfo> serverList = entry.getValue();
			for (Iterator<NodeInfo> startServerListIterator = serverList.iterator(); startServerListIterator.hasNext();) {
				// 对serverList组内的所有服务器 进行连接测试
				NodeInfo node = startServerListIterator.next();
				connected = checkRealConnect(node.getServer_ip(), Integer.parseInt(node.getSocket_port()));

				String startGname = entry.getKey();
				if (!connected) {
					// 启用列表删除该服务器信息
					aliveServers.get(startGname).remove(node);

					// 无法连接,将其加入停止队列中
					deadServers.get(startGname).addIfAbsent(node);
					log.error(node.getServer_ip() + "[" + startGname + "] disconnect, remove");
				} else {
					log.debug(node.getServer_ip() + "[" + startGname + "] can connect");
				}
			}
		}
	}

	/**
	 * 校验停用的服务器是否已经启用
	 */
	public static void checkDeadServers() {
		if (deadServers == null) {
			log.info("begin to checkDeadServers,deadServer.size is null");
			return;
		} 
		boolean connected = true;

		for (Entry<String, CopyOnWriteArrayList<NodeInfo>> entry : deadServers.entrySet()) {
			// 遍历启用的服务器组map
			List<NodeInfo> serverList = entry.getValue();
			for (Iterator<NodeInfo> stopServerListIterator = serverList.iterator(); stopServerListIterator.hasNext();) {
				// 对serverList组内的所有服务器 进行连接测试
				NodeInfo node = stopServerListIterator.next();
				connected = checkRealConnect(node.getServer_ip(), Integer.parseInt(node.getSocket_port()));

				String stopGname = entry.getKey();
				if (connected) {
					// 启用列表删除该服务器信息
					deadServers.get(stopGname).remove(node);
					// 可以连接,将其加入启用队列中
					aliveServers.get(stopGname).addIfAbsent(node);

					log.info(node.getServer_ip() + "[" + stopGname + "] connect, add");
				} else {
					log.error(node.getServer_ip() + "[" + stopGname + "] can not connect");
				}
			}
		}
	}

	/**
	 * 测试ip+port的机器是否可以联通
	 * 
	 * @param ip
	 *            机器的ip
	 * @param port
	 *            机器的socket端口
	 * @return 连通返回true，不连通返回false
	 */
	public static boolean checkConnected(String ip, int port) {

		// 先mock掉
		SecureRandom random = new SecureRandom();
		if (random.nextInt(5) < 2) {
			return false;
		}

		return true;

		// return checkRealConnect();
	}

	public static boolean checkRealConnect(String ip, int port) {
		boolean connection = true;
		Socket socket = null;
		try {
			socket = new Socket(ip, port);
		} catch (UnknownHostException e) {
			connection = false;
			log.error("无法连接[" + ip + ":" + port + "]");
		} catch (IOException e1) {
			connection = false;
			log.error("无法连接[" + ip + ":" + port + "]");
		} finally {
			if (socket != null) {
				try {
					socket.close();
				} catch (IOException e1) {
					log.error("测试socket联通，关闭socket出错");
				}
			}
		//	socket = null;
		}

		return connection;
	}
}
