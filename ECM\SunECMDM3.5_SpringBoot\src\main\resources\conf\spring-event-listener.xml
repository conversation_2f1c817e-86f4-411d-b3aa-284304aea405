<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
	<bean id="eventManageCenter" class="com.sunyard.ecm.server.event.EventManageCenter"
		destroy-method="stop" init-method="init">
		<property name="listener">
			<!-- 同一类事件可以配置多个监听，按配置的先后顺序执行，如果执行方法的返回值是true，则跳过下一个监听。一般情况都默认返回false，依次执行符合条件的监听。 -->
			<list>
				<!-- <ref bean="fileRecordOperListener" />-->
				<!-- 性能角度考虑，发布版本的时候，默认不配置监听 -->
			</list>
		</property>
	</bean>

	<!--<bean id="fileRecordOperListener"
		class="com.sunyard.ecm.server.event.impl.FileRecordOperListener"
		destroy-method="stop"></bean> -->
</beans>