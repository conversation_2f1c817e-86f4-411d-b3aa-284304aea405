import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getTaskListAction(data) {
  const url = '/lifeManage/getTaskListAction'+EndUrl.EndUrl

  const obj = {
    'start': data.start,
    'page': data.page,
    'limit': data.limit,
    'task_name': encodeURI(data.task_name),
    'model_code': encodeURI(data.model_code),
    'task_no': encodeURI(data.task_no)
  }

  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}


