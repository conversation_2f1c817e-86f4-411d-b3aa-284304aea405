<template>
  <div>
    <el-dialog v-el-drag-dialog :close-on-click-modal="false"
      :title="tagAttrTitle"
      :visible.sync="tagAttrdialogFormVisible" width="1200px"
    >
    <div class="edit_dev">
     <!-- <el-row> 
        <el-col :span="20"> -->
           <el-transfer
        style="text-align: left; display: inline-block"
        v-model="choiceDataList"
        filterable
        :left-default-checked="[1]"
        :right-default-checked="[2]"
        :titles="['未分配属性', '已有属性(已关联业务开始时间字段)']"
        :button-texts="['删除', '添加']"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}',
        }"
        :data="notChoiceDataList"
      >
        <span slot-scope="{ option }"> {{ option.label }}</span>
          </el-transfer>
       <!-- </el-col>
      <el-col :span="4">
        <el-form>
        <el-form-item label = "选中的为检索关键字:"><br/>
          <el-checkbox-group v-model="checkedAttrs" size="small">
                <el-checkbox-button v-for="item in choiceDataList" :label="item" :key="item">
                  {{
                    checkMap.get(item)
                  }}
              </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        </el-form>
      </el-col>
     </el-row> -->
    </div>  
    <div style="margin: 15px 0"></div>    
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="hide">取消</el-button>
        <el-button size="mini" type="primary" @click="getFather()">上一步</el-button>
        <el-button size="mini" type="primary" @click="handlePostAttr()"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>


<style>
.transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
}
</style>

<script>
import {getExistAttrsTree,getNotExistAttrsTree,updateTagAttrs} from "@/api/tagManage";
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "tag-attr-rel",
  directives: { elDragDialog },
  props: {
    tagCode: {
      required: false,
      type: String,
    },
    tagName: {
      required: false,
      type: String,
    }
  },
  data: function () {
    return {
      notChoiceDataList: [],
      choiceDataList: [],
      // checkedAttrs: [],
      AlreadyChoice: [],
      tagAttrdialogFormVisible: false,
      tagAttrTitle: "绑定属性",
      listLoading: true,
      tCode:'',
      tName:'',
      tState:'1',
      optionFlag:''
      // checkMap: null
    };
  },
  created() {},
  methods: {
    show() {
      this.tagAttrdialogFormVisible = true;
    },
    hide() {
      this.tagAttrdialogFormVisible = false;
    },
    handlePostAttr() {
      // if(this.tState == 2){//已同步
      //   alert("此标签已同步，不允许修改");
      //   return;
      // }
      var choiceKeys = "";
      // var checkKeys = "";
      for (var i = 0; i < this.choiceDataList.length; i++) {
        if(this.AlreadyChoice.indexOf(this.choiceDataList[i])<0){
          if (choiceKeys.length > 0) {
            choiceKeys += ",";
          }
          choiceKeys += this.choiceDataList[i];
        }
      }

      // for (var j = 0; j < this.checkedAttrs.length; j++) {
      //   if (choiceKeys.length > 0) {
      //     checkKeys += ",";
      //   }
      //   checkKeys += this.checkedAttrs[j];
      // }
      this.$message.info("提交中...");
        updateTagAttrs({
          tag_code: this.tCode,
          tag_name: this.tName,
          // tag_state: this.tState,
          optionFlag:this.optionFlag,
          attr_ids: choiceKeys
          // check_ids: checkKeys
        }).then(() => {
          this.hide();
          this.$notify({
            title: "Success",
            message: "Created Successfully",
            type: "success",
            duration: 1000,
          });
          this.$emit("refreshTag");

        });

    },
    getAllAttrsTree(tagCode,tagName,tagState,optionFlag) {   
      // this.checkMap = new Map();
      this.notChoiceDataList = [];
      this.choiceDataList = [];
      this.AlreadyChoice = [];
      // this.checkedAttrs = [];
      this.tName = tagName;
      this.tCode = tagCode;
      this.tState = tagState;
      this.optionFlag = optionFlag;
        getExistAttrsTree(tagCode).then((response) => {
          let existattrsTree = response.msg;
            for(let item1 of existattrsTree){
              if(item1 == null){
                  this.notChoiceDataList.push({
                    key: item1.id,
                    label: item1.text
                  });
                  // this.checkMap.set(item1.id,item1.text)
              }else{
                  this.notChoiceDataList.push({
                    key: item1.id,
                    label: item1.text,
                    disabled :true

                  }); 
                  // this.checkMap.set(item1.id,item1.text)
                  this.AlreadyChoice.push(item1.id);
                  this.choiceDataList.push(item1.id);
                  // if(item1.index == "1"){
                  //   this.checkedAttrs.push(item1.id)
                  // }
              }
            }
          });
        getNotExistAttrsTree(tagCode).then((response) => {
          let notexistattrsTree = response.msg;
          for(let item1 of notexistattrsTree){
            if(item1 == null){
                this.notChoiceDataList.push({
                  key: item1.id,
                  label: item1.text
                });
              // this.checkMap.set(item1.id,item1.text)
            }else{
                this.notChoiceDataList.push({
                  key: item1.id,
                  label: item1.text
                });
                // this.checkMap.set(item1.id,item1.text)
            }
          }
        });
       this.show();
    },
    getFather() {
      this.$emit("backTag");
    },
  }
};
</script>
<style scoped>
.edit_dev >>> .el-transfer-panel {
     width:350px;
   }
</style>