<template>
  <div class="app-container">
    <div class="filter-container">
       <el-select v-model="listQuery.group_id" placeholder="请选择存储服务器组" @change='getServerModelCode()'>
        <el-option
          v-for="item in storeServers"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-select v-model="listQuery.model_code" placeholder="请选择内容对象">
        <el-option
          v-for="item in storeObj"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-button
        v-if="this.hasPerm('strategySearch')"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
      <el-button
        v-if="this.hasPerm('addStrategy')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        创建策略
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column label="缓存ID" v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.task_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="任务" v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.task_no }}</span>
        </template>
      </el-table-column>

     <el-table-column label="缓存类型" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.task_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="服务器组名称" width="100px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.group_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="组ID" v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.group_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="内容模型代码" v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.model_code }}</span>
        </template>
      </el-table-column>

      <el-table-column label="参数" v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.parameters }}</span>
        </template>
      </el-table-column>

      <el-table-column label="内容对象名称" width="110px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.model_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="运行类型" width="100px" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.run_type == 'RT_01'">每天</span>
          <span v-if="row.run_type == 'RT_02'">每周</span>
          <span v-if="row.run_type == 'RT_03'">每月</span>
          <span v-if="row.run_type == 'RT_04'">每年</span>
        </template>
      </el-table-column>

      <el-table-column label="开始运行时间" width="130px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.begin_row }}</span>
        </template>
      </el-table-column>

      <el-table-column label="结束运行时间" width="130px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.end_time }}</span>
        </template>
      </el-table-column>

      <el-table-column label="策略状态" width="100px" align="center">
        <template slot-scope="{ row }" >
          <span v-if="row.task_state == '1'">启用</span>
          <span v-if="row.task_state == '0'">禁用</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        align="center"
        width="230"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <el-button v-if="hasPerm('updateStrategy')" type="primary" icon="el-icon-edit" size="mini" @click="handleUpdate(row)">
            修改策略
          </el-button>
          <el-button
            v-if="row.task_state == '0' && hasPerm('ableStrategy')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="handleStart(row, $index)"
          >
            启用
          </el-button>

          <el-button
            v-if="row.task_state == '1' && hasPerm('disableStrategy')"
            size="mini"
            type="danger"
            icon="el-icon-turn-off"
            @click="handleStop(row, $index)"
          >
            禁用
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="服务器组" prop="group_id">
          <el-select v-model="temp.group_id" selected="temp.group_id" placeholder="请选择服务器组" :disabled="dialogStatus == 'update'" @change='getTmpServerModelCode()'>
            <el-option
              v-for="item in tmpServerGroups"
                :key="item.id"
                :label="item.text_text"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="内容对象" prop="obj_name">
          <el-select v-model="temp.obj_name" selected="temp.obj_name" placeholder="请选择内容对象" :disabled="dialogStatus == 'update'" @change='getTmpTaskNo()'>
            <el-option
              v-for="item in tmpStoreObj"
                :key="item.id"
                :label="item.text_text"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="策略名称" prop="task_no">
          <el-select v-model="temp.task_no" selected="temp.task_name" placeholder="请选择策略" :disabled="dialogStatus == 'update'" @change='checkShow()'>
            <el-option
              v-for="item in tmpTaskObj"
                :key="item.task_no"
                :label="item.task_name"
                :value="item.task_no"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="上级服务器组" prop="super_groupid" v-if='isSuper'>
          <el-select v-model="temp.super_groupid" selected="temp.super_groupid" placeholder="请选择上级服务器组">
            <el-option
              v-for="item in super_groupid_data"
                :key="item.id"
                :label="item.text_text"
                :value="item.id"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="近线卷" prop="near_line" v-if='isNear'>
          <el-select v-model="temp.near_line" selected="temp.near_line" placeholder="请选择近线卷">
            <el-option
              v-for="item in nearLine_data"
                :key="item.id"
                :label="item.text_text"
                :value="item.id"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="离线起始时间" prop="off_date" v-if='isOffDate'>
          <el-select v-model="temp.off_date" selected="temp.off_date" placeholder="请选择离线起始时间">
            <el-option
              v-for="item in objOff_data"
                :key="item.id"
                :label="item.text_text"
                :value="item.id"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="ODS推送路径" prop="ods_path" v-if="isODS">
            <el-input v-model="temp.ods_path" maxlength="64" placeholder="请输入文件路径">
            </el-input>
          </el-form-item>
           <el-form-item label="条件配置" prop="con_id" v-if='isCondition'>
          <el-select v-model="temp.con_id" selected="temp.con_id" placeholder="请选择条件配置">
            <el-option
              v-for="item in condition_data"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
          </el-select>
          </el-form-item>
         <el-form-item label="运行类型" prop="run_type">
          <el-select v-model="temp.run_type" placeholder="请选择运行类型" @change='startType()'>
            <el-option
              v-for="item in runTypes"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="开始月份" prop="year_month" v-if='isMonth'>
          <el-select v-model="temp.year_month" placeholder="请选择开始月份" @change='changeMonth()'>
            <el-option
              v-for="item in year_month_data"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="开始日期" prop="day" v-if='isDay'>
          <el-select v-model="temp.day" placeholder="请选择开始日期">
             <el-option
              v-for="item in days"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
          </el-select>
          </el-form-item>
          <el-form-item label="开始时间" prop="begin">
            <el-time-select v-model="temp.begin" value-format="mm:ss" 
              :picker-options="{
                start:'00:00',
                step:'00:15',
                end:'23:45'
              }"
              placeholder="请选择开始时间">
            </el-time-select>
          </el-form-item>
           <el-form-item label="结束时间" prop="end_time">
            <el-time-select v-model="temp.end_time" value-format="mm:ss" 
              :picker-options="{
                start:'00:00',
                step:'00:15',
                end:'23:45'
              }"
              placeholder="请选择结束时间">
            </el-time-select>
          </el-form-item>
          <el-form-item label="保持时间(天）" prop="save_time" v-if="isSave">
            <el-input-number v-model="temp.save_time" placeholder="请选择保持时间(天）" :min="0">
            </el-input-number>
          </el-form-item>
          <el-form-item label="策略状态" prop="task_state">
            <el-select v-model="temp.task_state">
            <el-option
              v-for="item in states"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
            </el-select>
          </el-form-item>
          <el-form-item label="是否只删除逻辑删除后的数据" prop="only_logic" v-if="isLogicDel">
            <el-select v-model="temp.only_logic">
            <el-option
              v-for="item in onlyLogicDel"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
            </el-select>
          </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary" size="mini"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCachingStrategyList, addCaching, updateCaching, stopCaching, startCaching,getUnbindTask} from '@/api/cachingStrategy'
import { getContentServerGroup, getRelContentObject, getSuperGroup, getNearLine} from '@/api/contentServerGroupManage'
import { getContentObjOffDate} from '@/api/contentObjectManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import global from '../../store/global.js'

export default {
  name: 'ComplexTable',
  components: { Pagination },
  directives: { waves,elDragDialog },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: 'success',
        draft: 'info',
        deleted: 'danger'
      }
      return statusMap[status]
    }
  },

  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: '+task_name',
        group_id:'',
        model_code:''
      },
      importanceOptions: [1, 2, 3],
      sortOptions: [{ label: 'ID Ascending', key: '+task_name' }, { label: 'ID Descending', key: '-task_name' }],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        type: '',
        status: 'published',
        group_id:'',
        obj_name:'',
        task_name:'',
        super_groupid:'',
        near_line:'',
        off_date:'',
        ods_path:'',
        con_id:'',
        run_type:'',
        year_month:'',
        day:'',
        begin:'',
        end_time:'',
        save_time:'',
        task_state:'',
        only_logic:'',
        begin_time:'',
        task_no:'',
        parameters:'',
        model_code:''
      },
      states : [
          { key: '1', display_name: '启用' },
          { key: '0', display_name: '禁用' }
      ],
      onlyLogicDel: [
          { key: '1', display_name: '是' },
          { key: '0', display_name: '否' }
      ],
      runTypes : [
            { key: 'RT_01', display_name: '每天' },
            { key: 'RT_02', display_name: '每周' },
            { key: 'RT_03', display_name: '每月' },
            { key: 'RT_04', display_name: '每年' }
      ],
      year_month_data : [
        {key: '1', display_name: '1月' },
        {key: '2', display_name: '2月' },
        {key: '3', display_name: '3月' },
        {key: '4', display_name: '4月' },
        {key: '5', display_name: '5月' },
        {key: '6', display_name: '6月' },
        {key: '7', display_name: '7月' },
        {key: '8', display_name: '8月' },
        {key: '9', display_name: '9月' },
        {key: '10', display_name: '10月' },
        {key: '11', display_name: '11月' },
        {key: '12', display_name: '12月' }
      ],
    month_day_data31 : [
       {key: '1', display_name: '1日'},{key: '2', display_name: '2日'},{key: '3', display_name: '3日'},
       {key: '4', display_name: '4日'},{key: '5', display_name: '5日'},{key: '6', display_name: '6日'},
       {key: '7', display_name: '7日'},{key: '8', display_name: '8日'},{key: '9', display_name: '9日'},
       {key: '10', display_name: '10日'},{key: '11', display_name: '11日'},{key: '12', display_name: '12日'},
       {key: '13', display_name: '13日'},{key: '14', display_name: '14日'},{key: '15', display_name: '15日'},
       {key: '16', display_name: '16日'},{key: '17', display_name: '17日'},{key: '18', display_name: '18日'},
       {key: '19', display_name: '19日'},{key: '20', display_name: '20日'},{key: '21', display_name: '21日'},
       {key: '22', display_name: '22日'},{key: '23', display_name: '23日'},{key: '24', display_name: '24日'},
       {key: '25', display_name: '25日'},{key: '26', display_name: '26日'},{key: '27', display_name: '27日'},
       {key: '28', display_name: '28日'},{key: '29', display_name: '29日'},{key: '30', display_name: '30日'},
       {key: '31', display_name: '31日'}
    ],
    month_day_data30 : [
       {key: '1', display_name: '1日'},{key: '2', display_name: '2日'},{key: '3', display_name: '3日'},
       {key: '4', display_name: '4日'},{key: '5', display_name: '5日'},{key: '6', display_name: '6日'},
       {key: '7', display_name: '7日'},{key: '8', display_name: '8日'},{key: '9', display_name: '9日'},
       {key: '10', display_name: '10日'},{key: '11', display_name: '11日'},{key: '12', display_name: '12日'},
       {key: '13', display_name: '13日'},{key: '14', display_name: '14日'},{key: '15', display_name: '15日'},
       {key: '16', display_name: '16日'},{key: '17', display_name: '17日'},{key: '18', display_name: '18日'},
       {key: '19', display_name: '19日'},{key: '20', display_name: '20日'},{key: '21', display_name: '21日'},
       {key: '22', display_name: '22日'},{key: '23', display_name: '23日'},{key: '24', display_name: '24日'},
       {key: '25', display_name: '25日'},{key: '26', display_name: '26日'},{key: '27', display_name: '27日'},
       {key: '28', display_name: '28日'},{key: '29', display_name: '29日'},{key: '30', display_name: '30日'}
    ],
    month_day_data29 : [
       {key: '1', display_name: '1日'},{key: '2', display_name: '2日'},{key: '3', display_name: '3日'},
       {key: '4', display_name: '4日'},{key: '5', display_name: '5日'},{key: '6', display_name: '6日'},
       {key: '7', display_name: '7日'},{key: '8', display_name: '8日'},{key: '9', display_name: '9日'},
       {key: '10', display_name: '10日'},{key: '11', display_name: '11日'},{key: '12', display_name: '12日'},
       {key: '13', display_name: '13日'},{key: '14', display_name: '14日'},{key: '15', display_name: '15日'},
       {key: '16', display_name: '16日'},{key: '17', display_name: '17日'},{key: '18', display_name: '18日'},
       {key: '19', display_name: '19日'},{key: '20', display_name: '20日'},{key: '21', display_name: '21日'},
       {key: '22', display_name: '22日'},{key: '23', display_name: '23日'},{key: '24', display_name: '24日'},
       {key: '25', display_name: '25日'},{key: '26', display_name: '26日'},{key: '27', display_name: '27日'},
       {key: '28', display_name: '28日'},{key: '29', display_name: '29日'}
    ],
    week_day_data : [
        {key: '1', display_name: '星期一' },
        {key: '2', display_name: '星期二' },
        {key: '3', display_name: '星期三' },
        {key: '4', display_name: '星期四' },
        {key: '5', display_name: '星期五' },
        {key: '6', display_name: '星期六' },
        {key: '7', display_name: '星期天' }
    ],
    condition_data  : [
        {key: '1', display_name: '业务规则' },
        {key: '2', display_name: '管理规则' },
        {key: '3', display_name: '业务规则 + 管理规则' }
    ],
      super_groupid_data : [],
      nearLine_data : [],
      objOff_data : [],
      days : [],
      tmpServerGroups : [],
      storeServers :  [],
      tmpStoreObj :  [],
      storeObj : [],
      tmpTaskObj : [],
      isMonth : false,
      isDay : false,
      isSave : true,
      isSuper : false,
      isNear : false,
      isOffDate : false,
      isODS : false,
      isCondition : false,
      isLogicDel : false,
      dialogFormVisible : false,
      dialogStatus : '',
      textMap: {
        update: '修改策略',
        create: '创建策略'
      },
      rules: {
        group_id: [{ required: true, message: '服务器组必选', trigger: 'blur' }],
        obj_name: [{ required: true, message: '内容模型必选', trigger: 'blur' }],
        task_no: [{ required: true, message: '策略名称必选', trigger: 'blur' }],
        run_type: [{ required: true, message: '运行类型必选', trigger: 'blur' }],
        begin: [{ required: true, message: '开始时间必选', trigger: 'blur' }],
        end_time: [{ required: true, message: '结束时间必选', trigger: 'blur' }],
        save_time: [{ required: true, message: '保持时间必选且为数字', trigger: 'blur' }],
        task_state: [{ required: true, message: '策略状态必选', trigger: 'blur' }],
        ods_path: [{ pattern:/^[a-zA-Z]:(\\([0-9a-zA-Z]+))+|(\/([0-9a-zA-Z]+))+$/, 
        message: '建议windows输入格式如:C:/home; linux如:/home'}]
      },
      downloadLoading: false,
    }
  },

  created() {
    this.getList();
    this.getServers();
  },

  methods: {
    getList() {
      this.listLoading = true
      getCachingStrategyList(this.listQuery).then(response => {
        this.list = response.root
        this.total = Number(response.totalProperty)
         for(let item of this.list){
            item.begin_row = this.checkBegin(item.run_type,item.begin_time)
        }
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },

    checkBegin(rt,value){
      let bg = value.split(':');
      if(rt == 'RT_01'){//每天
        return bg[2] + ':' + bg[3];
      }else if(rt == 'RT_02'){//每周
        return "周" + bg[4] + "  "+ bg[2] + ':'+ bg[3];
      }else if(rt == 'RT_03'){//每月
        return bg[1] + "日   "+ bg[2] + ':'	+ bg[3];
      }else if(rt == 'RT_04'){//每年
        return bg[0] + "月"	+ bg[1] + "日    "+ bg[2] + ':'+ bg[3];
      }
    },

    getServers(){
        getContentServerGroup().then(response => {
        this.storeServers = response.root;
        this.tmpServerGroups  = response.root;
      })
    },

    getServerModelCode(){
        this.listQuery.model_code = '';
        getRelContentObject(this.listQuery).then(response => {
        this.storeObj = response.root
      })
    },

    getTmpServerModelCode(){
        this.temp.obj_name = '';
        getRelContentObject(this.temp).then(response => {
        this.tmpStoreObj = response.root
      })
    },

    getTmpTaskNo(){
        this.temp.model_code = this.temp.obj_name;
        getUnbindTask(this.temp).then(response => {
        this.tmpTaskObj = response.root;
      })
    },

    handleclear() {
      this.listQuery.group_id= "";
      this.listQuery.model_code= "";
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    resetShow(){
      this.isLogicDel = false,
      this.isSave = true,
      this.isSuper = false,
      this.isNear = false,
      this.isOffDate = false,
      this.isODS = false,
      this.isCondition = false
    },

    resetShow2(){
      this.isMonth = false,
      this.isDay = false
    },

    checkShow(){
      this.resetShow();
      let tn = this.temp.task_no;
      if(tn == '1'){//内容清理，增加是否逻辑删除
        this.isLogicDel = true;
      }else if(tn == '2'){//内容迁移，增加上级服务器组
        this.isSuper = true;
        this.getSuperGroupIdData();
      }else if(tn == '3'){//内容离线，增加离线起始时间
        this.isOffDate = true;
        this.getObjOffDate();
      }else if(tn == '5'){//内容近线，增加近线卷
        this.isNear = true;
        this.getNearData();
      }else if(tn == '401' || tn == '402' ){//内容归档任务表/大数据离线任务导入，增加ods推送路径，隐藏保持天数
        this.isODS = true;
        this.isSave = false;
      }else if(tn == '403'){//内容归档，增加上级服务器组、条件配置
         this.isSuper = true;
         this.isCondition = true;
         this.getSuperGroupIdData();
      }else if(tn == '404'){//大数据内容离线并清理，隐藏保持天数
        this.isSave = false;
      }
    },

    checkUpdateShow(){
      this.resetShow();
      let tn = this.temp.task_no;
      let parList =this.temp.parameters.split(";");
      this.temp.save_time = (parList[0].split("="))[1];
      if(tn == '1'){//内容清理，增加是否逻辑删除
        this.isLogicDel = true;
        this.temp.only_logic = (parList[1].split("="))[1];
      }else if(tn == '2'){//内容迁移，增加上级服务器组
        this.isSuper = true;
        this.getSuperGroupIdData();
        this.temp.super_groupid = (parList[1].split("="))[1];
      }else if(tn == '3'){//内容离线，增加离线起始时间
        this.isOffDate = true;
        this.getObjOffDate();
        this.temp.off_date = (parList[1].split("="))[1];
      }else if(tn == '5'){//内容近线，增加近线卷
        this.isNear = true;
        this.getNearData();
        this.temp.near_line = (parList[1].split("="))[1];
      }else if(tn == '401' || tn == '402' ){//内容归档任务表/大数据离线任务导入，增加ods推送路径，隐藏保持天数
        this.isODS = true;
        this.isSave = false;
        this.temp.save_time = '';
        this.temp.ods_path = (parList[2].split("="))[1];
      }else if(tn == '403'){//内容归档，增加上级服务器组、条件配置
         this.isSuper = true;
         this.isCondition = true;
         this.getSuperGroupIdData();
         this.temp.con_id = (parList[1].split("="))[1];
         this.temp.super_groupid = (parList[2].split("="))[1];
      }else if(tn == '404'){//大数据内容离线并清理，隐藏保持天数
        this.isSave = false;
        this.temp.save_time = '';
      }
    },

    getSuperGroupIdData(){
     getSuperGroup(this.temp).then(response => {
        this.super_groupid_data = response.root
      })
    },

    getNearData(){
      getNearLine(this.temp).then(response => {
        this.nearLine_data = response.root
      })
    },

    getObjOffDate(){
      getContentObjOffDate(this.temp.obj_name).then(response => {
        this.objOff_data = response.root
      })
    },

    startType() {
      let rt = this.temp.run_type;
      this.resetShow2();
      if(rt == 'RT_01'){//每天
          this.isMonth = false;
          this.isDay = false;
      }else if(rt == 'RT_02'){//每周
          this.isMonth = false;
          this.isDay = true;
          this.days  = this.week_day_data;
      }else if(rt == 'RT_03'){//每月
          this.isMonth = false;
          this.isDay = true;
          this.days = this.month_day_data31;
      }else if(rt == 'RT_04'){//每年
          this.isMonth = true;
          this.isDay = true;
      }
    },

    changeMonth(){
      let month = this.temp.year_month;
      if (month == '1' || month == '3' || month == '5' || month == '7' || month == '8'
            || month == '10' || month == '12') {
            this.days = this.month_day_data31;
      } else {
          if (month == '2') {
            this.days= this.month_day_data29
          } else {
            this.days= this.month_day_data30
          }
      }
    },

    checkSubmitData(data){
      let tn = data.task_no;
      if(tn == '1'){//内容清理，增加是否逻辑删除
        let check = data.only_logic;
        if(check == undefined || check == '' || check == null){
          alert("请选择是否逻辑删除");
          return;
        }
      }else if(tn == '2'){//内容迁移，增加上级服务器组
        let check = data.super_groupid;
        if(check == undefined || check == '' || check == null){
          alert("请选择上级服务器组");
          return;
        }
      }else if(tn == '3'){//内容离线，增加离线起始时间
        let check = data.off_date;
        if(check == undefined || check == '' || check == null){
          alert("请选择离线起始时间");
          return;
        }
      }else if(tn == '5'){//内容近线，增加近线卷
        let check = data.near_line;
        if(check == undefined || check == '' || check == null){
          alert("请选择近线卷");
          return;
        }
      }else if(tn == '401' || tn == '402' ){//内容归档任务表/大数据离线任务导入，增加ods推送路径，隐藏保持天数
        let check = data.ods_path;
        if(check == undefined || check == '' || check == null){
          alert("请输入ods推送路径");
          return;
        }
      }else if(tn == '403'){//内容归档，增加上级服务器组、条件配置
        let check = data.super_groupid;
        if(check == undefined || check == '' || check == null){
          alert("请选择上级服务器组");
          return;
        }
        let check2 = data.con_id;
        if(check2 == undefined || check2 == '' || check2 == null){
          alert("请选择条件配置");
          return;
        }
      }
      return true;
    },

    sortChange(data) {
      const { prop, order } = data
      alert(prop)
      if (prop === 'task_name') {
        this.sortByID(order)
      }
    },

    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+task_name'
      } else {
        this.listQuery.sort = '-task_name'
      }
      this.handleFilter()
    },

    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        status: 'published',
        type: '',
        name: '',
        group_id:'',
        obj_name:'',
        task_name:'',
        super_groupid:'',
        near_line:'',
        off_date:'',
        ods_path:'',
        con_id:'',
        run_type:'',
        year_month:'',
        day:'',
        begin:'',
        end_time:'',
        save_time:'',
        task_state:'',
        only_logic:'',
        begin_time:'',
        task_no:'',
        parameters:'',
        model_code:''
      }
    },

    handleCreate() {
      this.resetTemp()
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },

    getparams(taskNo,save){
         let params = '';
          if(save){
            params = "SAVE_TIME="+this.temp.save_time
          }else{
            params = "SAVE_TIME="
          }
          if(taskNo == '401' || taskNo == '402'){
            params += ";BIGDATATASK=true";
						params += ";BIGDATA_SAVEPATH="+this.temp.ods_path
          }else if(taskNo == '404'){
            params += ";BIGDATATASK=true";
          }else if(taskNo == '403'){
            params += ";BIGDATARULE="+this.temp.con_id;
            params += ";SUPER_GROUPID="+this.temp.super_groupid;
          }else if(taskNo == '1'){
           params += ";ONLYDELLOGICDATA="+this.temp.only_logic
          }else if(taskNo == '2'){
            params += ";SUPER_GROUPID="+this.temp.super_groupid;
          }else if(taskNo == '3' || taskNo == '32'){
            params += ";OFFLINE_DATE_TYPE="+this.temp.off_date
          }else if(taskNo == '5'){
						params += ";NEARLINE_VOUME="+this.temp.near_line
          }
        return params;
    },

    getBeginTimeValue(runType,month,day,bbb) {
        if (runType == 'RT_02') {//每周
          if (day == '' || day == null) {
            return false;
          }
          return '::' + bbb + ':' + day;
        } else if (runType == 'RT_03') {//每月
          if (day == '' || day == null) {
            return false;
          }
          return ':' + day + ':' + bbb + ':';
        } else if (runType == 'RT_04') {//每年
          if (month == '' || month == null || day == '' || day == null) {
            return false;
          }
          return month + ':' + day + ':' + bbb + ':';
        }
        return '::' + bbb + ':';
      },

    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid && this.checkSubmitData(this.temp)){
          this.temp.id = parseInt(Math.random() * 100) + 1024 // mock a id
          this.temp.author = 'vue-element-admin';
          this.temp.model_code = this.temp.obj_name;
          this.temp.parameters = this.getparams(this.temp.task_no,this.isSave);
          let begindata = this.getBeginTimeValue(this.temp.run_type,this.temp.year_month,this.temp.day,this.temp.begin);
          if (begindata == false) {
            this.$notify({
              title: 'fail',
              message: '请选择开始日期',
              type: 'fail',
              duration: 2000
            })
            return;
          }
          // if(this.temp.begin>=this.temp.end_time){
          //   this.$notify({
          //     title: 'fail',
          //     message: '结束时间必须大于开始时间',
          //     type: 'fail',
          //     duration: 2000
          //   })
          //   return;
          // }
          this.temp.begin_time = begindata;
          addCaching(this.temp).then(() => {
                this.getList();
                this.dialogFormVisible = false
                this.$notify({
                  title: 'Success',
                  message: 'Created Successfully',
                  type: 'success',
                  duration: 2000
                })
              })
        }
      })
    },

    handleUpdate(row) {
      this.resetTemp()
      this.temp = Object.assign({},this.temp, row); // copy obj
      this.getServers();
      this.getTmpServerModelCode();
      this.temp.timestamp = new Date(this.temp.timestamp);
      let btv = this.temp.begin_time.split(":");
      let rt = this.temp.run_type;
      if(rt == 'RT_01'){//每天
          this.isMonth = false;
          this.isDay = false;
      }else if(rt == 'RT_02'){//每周
          this.isMonth = false;
          this.isDay = true;
          this.days  = this.week_day_data;
          this.temp.day = btv[4];
      }else if(rt == 'RT_03'){//每月
          this.isMonth = false;
          this.isDay = true;
          this.days = this.month_day_data31;
          this.temp.day = btv[1];
      }else if(rt == 'RT_04'){//每年
          this.isMonth = true;
          this.isDay = true;
          this.temp.year_month = btv[0];
          this.temp.day = btv[1];
      }
      this.temp.begin = btv[2] + ':' + btv[3];
      this.temp.obj_name = this.temp.model_code;
      this.getTmpTaskNo();
      this.checkUpdateShow();
      this.dialogStatus = 'update';
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid  && this.checkSubmitData(this.temp)) {
          const tempData = Object.assign({}, this.temp)
          tempData.timestamp = +new Date(tempData.timestamp)
          tempData.parameters = this.getparams(this.temp.task_no,this.isSave);
          let begindata = this.getBeginTimeValue(this.temp.run_type,this.temp.year_month,this.temp.day,this.temp.begin);
          if (begindata == false) {
            this.$notify({
              title: 'fail',
              message: '请选择开始日期',
              type: 'fail',
              duration: 2000
            })
            return;
          }
          // if(this.temp.begin>=this.temp.end_time){
          //   this.$notify({
          //     title: 'fail',
          //     message: '结束时间必须大于开始时间',
          //     type: 'fail',
          //     duration: 2000
          //   })
          //   return;
          // }
          tempData.begin_time = begindata;
          updateCaching(tempData).then(() => {
            this.getList();
            this.dialogFormVisible = false
            this.$notify({
              title: 'Success',
              message: 'Update Successfully',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },

    handleStart(row, index) {
      startCaching(row).then(response => {
        this.getList()
      })
    },

    handleStop(row, index) {
      stopCaching(row).then(response => {
        this.getList()
      })
    },

    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    }
  }
}
</script>
