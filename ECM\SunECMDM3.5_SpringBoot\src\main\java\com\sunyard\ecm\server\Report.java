package com.sunyard.ecm.server;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * Title: 报表
 * </p>
 * <p>
 * Description: 获取报表资源
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
public class Report {
	// 文件统计的同步锁
	private static final Object fileSumlock 		= 	new Object();
	// 文件大小的同步锁
	private static final Object fileSizelock 		= 	new Object();
	// 批次发送的同步锁
	private static final Object sendBatchlock		= 	new Object();
	// 接收批次的同步锁
	private static final Object receiveBatchlock    = 	new Object();
	// 发送批次的同步锁
	private static final Object sendFileSumlock		= 	new Object();
	//接受文件数量
	private static Map<String, Long> fileSumMap 	= 	new HashMap<String, Long>();
	//接受文件大小总量
	private static Map<String, Long> fileSizeMap 	= 	new HashMap<String, Long>();
	//发送批次数量
	private static Map<String, Long> sendBatchMap	=	new HashMap<String, Long>();
	//接受批次数量
	private static Map<String, Long> receiveBatchMap =  new HashMap<String, Long>();
	//发送的文件数量
	private static Map<String, Long> sendFileSumMap	= 	new HashMap<String, Long>();
	
	public Report(){
	}
	/**
	 * 根据元数据对象获取文件数量
	 * @param objName
	 * @return
	 */
	public static long getFileSum(String objName){
		synchronized (fileSumlock) {
			Object object = fileSumMap.get(objName);
			if(object != null){
				Long sum = (Long)object;
				return sum;
			}else{
				return 0;
			}
		}
	}
	/**
	 * 增加某元数据对象的文件数量
	 * @param objName
	 * @param fileSum
	 */
	public static void addFileSum(String objName, long fileSum){
		synchronized (fileSumlock) {
			Object object = fileSumMap.get(objName);
			if(object != null){
				Long sum = (Long)object;
				sum = sum + fileSum;
				fileSumMap.put(objName, sum);
			}else{
				fileSumMap.put(objName, fileSum);
			}
		}
	}
	/**
	 * 减少某元数据对象的文件数量
	 * @param objName
	 * @param fileSum
	 */
	public static void minFileSum(String objName, long fileSum){
		synchronized (fileSumlock) {
			Object object = fileSumMap.get(objName);
			if(object != null){
				Long sum = (Long)object;
				sum = sum - fileSum;
				fileSumMap.put(objName, sum);
			}
		}
	}
	/**
	 * 删除某元数据对象的文件数量
	 * @param objName
	 */
	public static void deleteFileSum(String objName){
		synchronized (fileSumlock) {
			fileSumMap.remove(objName);
		}
	}
	/**
	 * 获取并删除某元数据对象的文件数量
	 * @param objName
	 * @return
	 */
	public static long getAndDelFileSum(String objName){
		synchronized (fileSumlock) {
			Object object = fileSumMap.get(objName);
			if(object != null){
				Long sum = (Long)object;
				fileSumMap.remove(objName);
				return sum;
			}else{
				return 0;
			}
		}
	}
	/**
	 * 根据元数据对象获取文件接收量
	 * @param objName
	 * @return
	 */
	public static long getFileSize(String objName){
		synchronized (fileSizelock) {
			Object object = fileSizeMap.get(objName);
			if(object != null){
				Long size = (Long)object;
				return size;
			}else{
				return 0;
			}
		}
	}
	/**
	 * 增加某元数据对象的文件接收量
	 * @param objName
	 * @param fileSize
	 */
	public static void addFileSize(String objName, long fileSize){
		synchronized (fileSizelock) {
			Object object = fileSizeMap.get(objName);
			if(object != null){
				Long size = (Long) object;
				size = size + fileSize;
				fileSizeMap.put(objName, size);
			}else{
				fileSizeMap.put(objName, fileSize);
			}
		}
	}
	/**
	 * 减少某元数据对象的文件接收量
	 * @param objName
	 * @param fileSize
	 */
	public static void minFileSize(String objName, long fileSize){
		synchronized (fileSizelock) {
			Object object = fileSizeMap.get(objName);
			if(object != null){
				Long size = (Long) object;
				size = size - fileSize;
				fileSizeMap.put(objName, size);
			}
		}
	}
	/**
	 * 删除某元数据对象的文件接收量
	 * @param objName
	 */
	public static void deleteFileSize(String objName){
		synchronized (fileSizelock) {
			fileSizeMap.remove(objName);
		}
	}
	/**
	 * 获取并删除某元数据对象的文件接收量
	 * @param objName
	 * @return
	 */
	public static long getAndDelFileSize(String objName){
		synchronized (fileSizelock) {
			Object object = fileSizeMap.get(objName);
			if(object != null){
				Long size = (Long)object;
				fileSizeMap.remove(objName);
				return size;
			}else{
				return 0;
			}
		}
	}
	
	/**
	 * 添加某元数据对象的发送批次数
	 * @param objName
	 * @param batchSum
	 */
	public static void addSendBatch(String objName, long batchSum){
		synchronized (sendBatchlock) {
			Object object = sendBatchMap.get(objName);
			if(object != null){
				Long sum = (Long) object;
				sum = sum + batchSum;
				sendBatchMap.put(objName, sum);
			}else{
				sendBatchMap.put(objName, batchSum);
			}
		}
	}
	/**
	 * 减少某元数据对象的发送批次数
	 * @param objName
	 * @param batchSum
	 */
	public static void minSendBatch(String objName, long batchSum){
		synchronized (sendBatchlock) {
			Object object = sendBatchMap.get(objName);
			if(object != null){
				Long sum = (Long) object;
				sum = sum - batchSum;
				sendBatchMap.put(objName, sum);
			}
		}
	}
	/**
	 * 删除某元数据对象下的发送批次数
	 * @param objName
	 */
	public static void deleteSendBatch(String objName){
		synchronized (sendBatchlock) {
			sendBatchMap.remove(objName);
		}
	}
	/**
	 * 获取某元数据对象的发送批次数
	 * @param objName
	 * @return
	 */
	public static long getSendBatch(String objName){
		synchronized (sendBatchlock) {
			Object object = sendBatchMap.get(objName);
			if(object != null){
				return (Long)object;
			}else{
				return 0;
			}
		}
	}
	/**
	 * 获取并删除某元数据对象的发送批次数
	 * @param objName
	 * @return
	 */
	public static long getAndDelSendBatch(String objName){
		synchronized (sendBatchlock) {
			Object object = sendBatchMap.get(objName);
			if(object != null){
				Long sum = (Long)object;
				sendBatchMap.remove(objName);
				return sum;
			}else{
				return 0;
			}
		}
	}
	/**
	 * 添加接收批次数量
	 * @param objName
	 * @param sendSum
	 */
	public static void addReceiveBatch(String objName, long receiveSum){
		synchronized (receiveBatchlock){
			Object object = receiveBatchMap.get(objName);
			if(object != null){
				Long sum = (Long) object;
				sum = sum + receiveSum;
				receiveBatchMap.put(objName, sum);
			}else{
				receiveBatchMap.put(objName, receiveSum);
			}
		}
	}
	/**
	 * 减少某元数据对象的接收批次数
	 * @param objName
	 * @param batchSum
	 */
	public static void minReceiveBatch(String objName, long receiveSum){
		synchronized (receiveBatchlock) {
			Object object = receiveBatchMap.get(objName);
			if(object != null){
				Long sum = (Long) object;
				sum = sum - receiveSum;
				receiveBatchMap.put(objName, sum);
			}
		}
	}
	/**
	 * 删除某元数据对象下的接收批次数
	 * @param objName
	 */
	public static void deleteReceiveBatch(String objName){
		synchronized (receiveBatchlock) {
			receiveBatchMap.remove(objName);
		}
	}
	/**
	 * 获取某元数据对象的接收批次数
	 * @param objName
	 * @return
	 */
	public static long getReceiveBatch(String objName){
		synchronized (receiveBatchlock) {
			Object object = receiveBatchMap.get(objName);
			if(object != null){
				return (Long)object;
			}else{
				return 0;
			}
		}
	}
	/**
	 * 获取并删除某元数据对象的接收批次数
	 * @param objName
	 * @return
	 */
	public static long getAndDelReceiveBatch(String objName){
		synchronized (receiveBatchlock) {
			Object object = receiveBatchMap.get(objName);
			if(object != null){
				Long sum = (Long)object;
				receiveBatchMap.remove(objName);
				return sum;
			}else{
				return 0;
			}
		}
	}
	
	/**
	 * 添加某元数据对象发送的文件数量
	 * @param objName
	 * @param sendSum
	 */
	public static void addSendFileSumMap(String objName, long sendFileSum){
		synchronized (sendFileSumlock){
			Object object = sendFileSumMap.get(objName);
			if(object != null){
				Long sum = (Long) object;
				sum = sum + sendFileSum;
				sendFileSumMap.put(objName, sum);
			}else{
				sendFileSumMap.put(objName, sendFileSum);
			}
		}
	}
	/**
	 * 减少某元数据对象发送的文件数量
	 * @param objName
	 * @param batchSum
	 */
	public static void minSendFileSumMap(String objName, long sendFileSum){
		synchronized (sendFileSumlock) {
			Object object = sendFileSumMap.get(objName);
			if(object != null){
				Long sum = (Long) object;
				sum = sum - sendFileSum;
				sendFileSumMap.put(objName, sum);
			}
		}
	}
	/**
	 * 删除某元数据对象下发送的文件数量
	 * @param objName
	 */
	public static void deleteSendFileSumMap(String objName){
		synchronized (sendFileSumlock) {
			sendFileSumMap.remove(objName);
		}
	}
	/**
	 * 获取某元数据对象发送的文件数量
	 * @param objName
	 * @return
	 */
	public static long getSendFileSumMap(String objName){
		synchronized (sendFileSumlock) {
			Object object = sendFileSumMap.get(objName);
			if(object != null){
				return (Long)object;
			}else{
				return 0;
			}
		}
	}
	/**
	 * 获取并删除某元数据对象发送的文件数量
	 * @param objName
	 * @return
	 */
	public static long getAndDelSendFileSumMap(String objName){
		synchronized (sendFileSumlock) {
			Object object = sendFileSumMap.get(objName);
			if(object != null){
				Long sum = (Long)object;
				sendFileSumMap.remove(objName);
				return sum;
			}else{
				return 0;
			}
		}
	}
}