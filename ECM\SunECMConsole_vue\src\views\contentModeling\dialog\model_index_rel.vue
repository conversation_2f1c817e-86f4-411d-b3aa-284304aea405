<template>
  <el-dialog v-el-drag-dialog :close-on-click-modal="false"
    :visible.sync="dialogFormVisible"
    :modal-append-to-body="false"
    :title="titleaaa"
  >
    <div class="app-container">
      <div class="filter-container" style="margin: 10px">
        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleCreateModelIndex"
        >
          新增
        </el-button>

        <el-button
          class="filter-item"
          style="margin-left: 10px"
          type="primary"
          icon="el-icon-delete"
          size="mini"
          @click="dialogFormVisible = false"
        >
          取消
        </el-button>
      </div>
      <el-table v-loading="listLoading" :data="modelIndexlist">
        <el-table-column label="索引名称" width="110px" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.index_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="索引类型" width="110px" align="center">
          <template slot-scope="{ row }">
            <span v-if="row.index_type == 1">普通索引</span>
            <span v-if="row.index_type == 2">唯一索引</span>
          </template>
        </el-table-column>
        <el-table-column label="字段信息" width="110px" align="center">
          <template slot-scope="{ row }">
            <span>{{ row.attribute_code }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="110px" align="center">
          <template slot-scope="{ row }">
            <span v-if="row.index_state == 1">正在创建</span>
            <span v-if="row.index_state == 2">正在删除</span>
            <span v-if="row.index_state == 3">创建完成</span>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          align="center"
          width="230"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="{ row, $index }">
            <el-button
              v-if="row.status != 'deleted'"
              size="mini"
              type="danger"
              @click="handleDeleteModelIndex(row, $index)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog v-el-drag-dialog :close-on-click-modal="false"
        :title="textMap[dialogStatus]"
        :visible.sync="dialogModelIndexFormVisible"
        append-to-body
      >
        <el-form
          ref="dataForm"
          :rules="rules"
          :model="temp"
          label-position="left"
          label-width="120px"
          style="width: 400px; margin-left: 50px"
        >
          <el-form-item label="索引名称" prop="index_name">
            <el-input
              v-model="temp.index_name"
              onkeyup="value=value.replace(/[\u4e00-\u9fa5]/g,'')"
            />
          </el-form-item>
          <el-form-item label="索引类型" prop="index_type">
            <el-select v-model="temp.index_type" placeholder="请选择类型">
              <el-option
                v-for="at in modelIndexType"
                :key="at.key"
                :label="at.display_name"
                :value="at.key"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="索引字段">
            <el-checkbox-group v-model="checkedAttrs">
              <el-checkbox v-for="c in relAttrs" :label="c.key" :key="c.key">{{
                c.label
              }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" @click="dialogModelIndexFormVisible = false">
            取消
          </el-button>
          <el-button size="mini" type="primary" @click="createData()"> 提交 </el-button>
        </div>
      </el-dialog>
    </div>
  </el-dialog>
</template>

<script>
import {
  getModelIndexList,
  getAttrTressByModelCode,
  addContentObjectIndex,
  delContentObjectIndex,
} from "@/api/contentObjectManage";

import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import {checkEnglish } from '@/utils/validate.js'

import global from "../../../store/global.js";
const modelIndexType = [
  { key: "1", display_name: "普通索引" },
  { key: "2", display_name: "唯一索引" },
];

export default {
  name: "ComplexTable",
  components: { Pagination },
  directives: { waves,elDragDialog },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
  },
  props: {
    modelCode: {
      required: false,
      type: String,
    },
  },
  data() {
    return {
      dialogFormVisible: false,
      tableKey: 0,
      modelIndexlist: [],
      total: 0,
      titleaaa: "新增索引",
      listLoading: false,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
      },
      importanceOptions: [1, 2, 3],
      relAttrs: [],
      checkedAttrs: [],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        type: "",
        attributeCode: "",
        status: "published",
      },
      loadAttributeTypes: [],
      modelIndexType: modelIndexType,
      dialogModelIndexFormVisible: false,
      dialogStatus: "",
      textMap: {
        update: "修改属性",
        create: "新增属性",
      },
      dialogPvVisible: false,
      pvData: [],
      rules: {
        index_name: [
          { required: true, message:global.regexIndexText,pattern:global.regexIndex, trigger: "change" },
        ],
        index_type: [
          { required: true, message: "索引类型必输", trigger: "change" },
        ],
        checkedAttrs: [
          { required: true, message: "索引字段必输", trigger: "change" },
        ],
      },
      downloadLoading: false,
      attribute_code_status: false,
    };
  },

  created() {
    //this.getModelIndexList();
  },
  computed: {},
  methods: {
    show() {
      this.dialogFormVisible = true;
    },
    hide() {
      this.dialogFormVisible = false;
    },
    getModelIndexList() {
      this.listLoading = true;
      getModelIndexList(this.modelCode).then((response) => {
        if (response.root == "") {
          this.modelIndexlist = [];
        } else {
          this.modelIndexlist = response.root;
        }

        this.total = Number(response.totalProperty);

        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },

    handleDeleteModelIndex(row, index) {
      this.openDelConfirm().then(() => {
        delContentObjectIndex(row).then((response) => {
          this.getModelIndexList();
          this.$notify({
              title: "Success",
              message: "Delete Successfully",
              type: "success",
              duration: 2000,
            });
        });
      })    
    },
    openDelConfirm(){
      return this.$confirm(`是否确定删除？`,'提示',{
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      })
    },
    handleCreateModelIndex() {
      getAttrTressByModelCode(this.modelCode).then((response) => {
        let msg = response.msg;
        this.relAttrs = [];
        msg.map((item) => {
          this.relAttrs.push({ key: item.id, label: item.text });
        });

        this.resetTemp();
        this.dialogModelIndexFormVisible = true;
        this.$nextTick(() => {
          this.$refs["dataForm"].clearValidate();
        });

        // setTimeout(() => {
        //   this.listLoading = false;
        // }, 1 * 100);
      });
    },
    handleclear() {
      this.listQuery.attributeCode = "";
      this.listQuery.attributeName = "";
    },
    handleFilter() {
      this.listQuery.page = 1;
      this.getModelIndexList();
    },
    sortChange(data) {
      const { prop, order } = data;
      alert(prop);
      if (prop === "attribute_code") {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === "ascending") {
        this.listQuery.sort = "+attribute_code";
      } else {
        this.listQuery.sort = "-attribute_code";
      }
      this.handleFilter();
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
      };
    },
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogModelIndexFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    createData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          // dataForm attribute_code
          this.temp.id = parseInt(Math.random() * 100) + 1024; // mock a id
          this.temp.modelCode = this.modelCode;

          if (this.checkedAttrs == null || this.checkedAttrs.length == 0) {
            alert("索引字段必选");
            return;
          }
          if(!checkEnglish(this.temp.index_name)){
            alert("索引名称不能有中文");
            return ;
          }
          if (this.temp.attributeCode == null) {
            this.temp.attributeCode = "";
          }
          for (var i = 0; i < this.checkedAttrs.length; i++) {
            if (this.temp.attributeCode.length > 0) {
              this.temp.attributeCode += ",";
            }
            this.temp.attributeCode += this.checkedAttrs[i];
          }

          addContentObjectIndex(this.temp).then(() => {
            // this.list.unshift(this.temp)
            this.getModelIndexList();
            this.dialogModelIndexFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Created Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },
    handleUpdate(row) {
      this.attribute_code_status = true;
      this.temp = Object.assign({}, row); // copy obj
      this.temp.timestamp = new Date(this.temp.timestamp);
      this.dialogStatus = "update";
      this.dialogModelIndexFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    updateData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp);
          tempData.timestamp = +new Date(tempData.timestamp); // change Thu Nov 30 2017 16:41:05 GMT+0800 (CST) to 1512031311464
          updateAttribte(tempData).then(() => {
            // const index = this.list.findIndex(v => v.attribute_code === this.temp.attribute_code)
            // this.list.splice(index, 1, this.temp)
            this.getModelIndexList();
            this.dialogModelIndexFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Update Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },

    handleDelete(row, index) {
      deleteAttr(row).then((response) => {
        this.getModelIndexList();
      });
    },

    formatJson(filterVal) {
      return this.list.map((v) =>
        filterVal.map((j) => {
          if (j === "timestamp") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort;
      return sort === `+${key}` ? "ascending" : "descending";
    },
    //   checkEnglish(a) {
    //   let reg = /^[a-zA-Z]+[a-zA-Z0-9_]*$/;
    //   if (!reg.test(a)) {
    //     return false;
    //   } else {
    //     return true;
    //   }
    // },
  },
};
</script>
