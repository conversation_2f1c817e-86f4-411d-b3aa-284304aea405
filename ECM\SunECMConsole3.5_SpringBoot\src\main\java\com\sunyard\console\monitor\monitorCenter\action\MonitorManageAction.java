package com.sunyard.console.monitor.monitorCenter.action;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.contentmodelmanage.bean.TreeBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONObject;

import com.sunyard.console.monitor.monitorCenter.bean.BatchTimeBean;
import com.sunyard.console.monitor.monitorCenter.bean.ServerBean;
import com.sunyard.console.monitor.monitorCenter.bean.ServerTaskBean;
import com.sunyard.console.monitor.monitorCenter.dao.MonitorManageDao;
import com.sunyard.console.monitor.monitorCenter.thread.DMEquInfoThread;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>Title: 监控管理Action</p>
 * <p>Description: 查询服务器硬件信息，任务信息，统计信息，网络流量等</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class MonitorManageAction extends BaseAction {
	private static final long serialVersionUID = -232412281113883860L;
	private  final static  Logger log = LoggerFactory.getLogger(MonitorManageAction.class);
	@Autowired
	private MonitorManageDao monitorManageDao;    //监控管理DAO
	private String start;    //分页起始参数
	private String limit;    //分页结束参事
	private String server_id;    //服务器ID
	private String group_id;// 服务器id
	private String group_name;// 服务器名称
	private String startTime;    //查询存储统计开始时间
	private String endTime;    //查询存储统计结束时间
	public String getGroup_id() {
		return group_id;
	}
	public void setGroup_id(String group_id) {
		this.group_id = group_id;
	}
	public String getGroup_name() {
		return group_name;
	}
	public void setGroup_name(String group_name) {
		this.group_name = group_name;
	}
	public MonitorManageDao getMonitorManageDao() {
		return monitorManageDao;
	}
	public void setMonitorManageDao(MonitorManageDao monitorManageDao) {
		this.monitorManageDao = monitorManageDao;
	}
	public String getStart() {
		return start;
	}
	public void setStart(String start) {
		this.start = start;
	}
	public String getLimit() {
		return limit;
	}
	public void setLimit(String limit) {
		this.limit = limit;
	}
	public String getServer_id() {
		return server_id;
	}
	public void setServer_id(String serverId) {
		server_id = serverId;
	}
	public String getStartTime() {
		return startTime;
	}
	public void setStartTime(String startTime) {
		this.startTime = startTime;
	}
	public String getEndTime() {
		return endTime;
	}
	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}
	
	
	/**
	 * 查询所有服务器信息
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getServerInfoAction.action")
	public String getServerInfo(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page = modelJson.getInt("page");
		int limit = modelJson.getInt("limit");

		int start = ((page-1) * limit);
		log.info("--getServerInfo(start)");
		String jsonStr = null;
		
		try {
			// 从内存中获取硬件信息
			Map<String, ServerBean> map = DMEquInfoThread.EquInfoMap;
			List<ServerBean> allServerList = new ArrayList<ServerBean>();
			List<ServerBean> serverList = new ArrayList<ServerBean>();
			
			if (map !=null) {
				log.debug( "--getServerInfo-->map:" + map);
				Iterator<String> it = map.keySet().iterator();
				while (it.hasNext()) {
					String key = it.next().toString();
					allServerList.add(map.get(key));
				}
				limit = limit + start;
				if(start > allServerList.size()){
					start = allServerList.size();
				}
				if(limit > allServerList.size()||limit < start){
					limit = allServerList.size();
				}
				serverList = allServerList.subList(start, limit);
				log.debug( "--getServerInfo-->serverList:" + serverList);
				log.debug( "--getServerInfo-->allServerList:" + allServerList);
				
				jsonStr = new JSONUtil().createJsonDataByColl(serverList,allServerList.size(),new ServerBean());
			} else {
				log.debug( "--getServerInfo-->map is null");
				JSONObject jsonResp = new JSONObject();
				jsonResp.put("success", false);
				jsonResp.put("message", "无服务器信息!!!");
				jsonStr = jsonResp.toString();
			}
			log.debug( "--getServerInfo-->获取服务器信息成功！");
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取服务器信息失败!!!");
			jsonStr = jsonResp.toString();
			log.error( "监控管理->查询所有服务器硬件信息失败->"+e.toString());
		}

		this.outJsonString(jsonStr);
		log.info( "--getServerInfo(over)");
		return null;
	}
	
	/**
	 * 查询服务器硬件详细信息
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getServerAction.action")
	public String getServerInfoById(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = modelJson.getString("server_id");
		log.info("--getServerInfoById(start)-->server_id:" + server_id);
		String jsonStr = null;
		try {
			String ServerInfoStr = monitorManageDao.getServerInfoListById(server_id);
			log.debug( "--getServerInfoById-->ServerInfoStr:" + ServerInfoStr);
			if (ServerInfoStr == null) {
				log.debug( "--getServerInfoById ServerInfoStr is null");
				JSONObject jsonResp = new JSONObject();
				jsonResp.put("success", false);
				jsonResp.put("message", "无服务器信息!!!");
				jsonStr = jsonResp.toString();
			} else {
				JSONObject jsonResp = new JSONObject();
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("root", ServerInfoStr);
				jsonStr = jsonResp.toString();;
			}
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取服务器硬件详细信息失败!!!");
			jsonStr = jsonResp.toString();
			log.error( "监控管理->查询服务器硬件详细信息失败->"+e.toString());
		}

		this.outJsonString(jsonStr);
		log.info( "--getServerInfoById(over)-->server_id:" + server_id);
		return null;
	}
	/**
	 * 查询内容存储统计 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getReportAction.action")
	public String getReport(String server_id, String startTime, String endTime) {
		log.info("--getReport(start)-->server_id:" + server_id);
		String jsonStr = null;
		try {
			Collection<BatchTimeBean> col = new LinkedList<BatchTimeBean>();
			Map<String, BatchTimeBean> map = monitorManageDao.queryBatch(server_id, startTime, endTime);
			log.debug( "--getReport-->map:" + map);
			if (map.size() != 0) {
				for (String str : map.keySet()) {
					col.add(map.get(str));
				}
				jsonStr = new JSONUtil().createJsonDataByColl(col, col.size(),
						new BatchTimeBean());
			} else {
				log.debug( "--getReport-->map size = 0");
				JSONObject jsonResp=new JSONObject();
				jsonResp.put("success", false);
				jsonResp.put("message", "无统计信息!!!");
				jsonStr = jsonResp.toString();
			}
			log.debug( "--getReport-->获取统计信息成功！");
		} catch (Exception e) {
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取统计信息失败!!!");
			jsonStr = jsonResp.toString();
			log.error( "监控管理->查询统计信息失败->"+e.toString());
			
		}

		this.outJsonString(jsonStr);
		log.info( "--getReport(over)-->server_id:" + server_id);
		return null;
	}
	/**
	 * 查询服务器任务信息
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getServerTaskAction.action")
	public String getServerTaskInfo(String server_id, int start, int limit) {
		log.info("--getServerTaskInfo(start)-->server_id:" + server_id);
		String jsonStr = null;
		try {
			List<ServerTaskBean> serverTaskList = monitorManageDao.getStrategByServerId(server_id, start, limit);
//			List<ServerTaskBean> serverTaskList = monitorManageDao.getStrategByServerId(server_id, Integer.valueOf(this.start), Integer.valueOf(this.limit));
			List<ServerTaskBean> allServerTaskList = monitorManageDao.getStrategByServerId(server_id);
			if(serverTaskList == null || allServerTaskList == null){
				log.debug( "--getServerTaskInfo-->serverTaskList is null or allServerTaskList is null");
				JSONObject jsonResp=new JSONObject();
				jsonResp.put("success", false);
				jsonResp.put("message", "无服务器任务信息!!!");
				jsonStr = jsonResp.toString();
			}else{
				jsonStr = new JSONUtil().createJsonDataByColl(serverTaskList, allServerTaskList.size(), new ServerTaskBean());
			}
			log.debug( "--getServerTaskInfo-->获取服务器任务信息成功！");
		} catch (Exception e) {
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取服务器任务信息失败!!!");
			jsonStr = jsonResp.toString();
			log.error( "监控管理->查询服务器任务信息失败->",e);
		}

		this.outJsonString(jsonStr);
		log.info( "--getServerTaskInfo(over)-->server_id:" + server_id);
		return null;
	}
	
	
	/**
	 *查询服务器网络流量
	 *@return  
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getNetFlowAction.action")
	public String getNetFlow(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = modelJson.getString("server_id");
		log.info("--getNetFlow(start)-->server_id:" + server_id);
		String jsonStr = null;
		try {
			String netFlowStr = monitorManageDao.getNetFlow(server_id);
			log.debug( "--getNetFlow-->netFlowStr:" + netFlowStr);
			JSONObject jsonResp=new JSONObject();
			if (netFlowStr == null) {
				JSONObject jsonX =new JSONObject();
				jsonX.put("rx","0");
				jsonX.put("tx","0");
				jsonStr = jsonX.toString();
				jsonResp.put("root", jsonStr);

			}else{
				jsonResp.put("success", true);
				jsonResp.put("root", netFlowStr);
			}
			jsonResp.put("code", 20000);
			jsonStr = jsonResp.toString();
			log.debug( "--getNetFlow-->查询服务器网络流量成功！");
		} catch (Exception e) {
			log.error( "监控管理->查询服务器网络流量失败->"+e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--getNetFlow(over)-->server_id:" + server_id);
		return null;
	}

	/**
	 * 获取所有服务器组id和组名(DM+UA)
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getAllGroupsAction.action", method = RequestMethod.POST)
	public String getAllServerGroup() {
		log.info("--getAllServerGroup(start)");
		String jsonStr = null;
		try {
			List<TreeBean> objectList = monitorManageDao.getAllServerGroup();
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug("--getAllServerGroup-->objectList:" + objectList);
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size, new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器组名称列表失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("监控管理->获取内容存储服务器组名称列表失败:" + e.toString(), e);

		}
		this.outJsonString(jsonStr);
		log.info("--getAllServerGroup(over)");
		return null;
	}

	/**
	 * 获取内容存储服务器组关联的内容对象id和名称
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getRelServersAction.action", method = RequestMethod.POST)
	public String getRelServers(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String group_id = modelJson.getString("group_id");
		log.info("--getRelContentServerTree(start)-->group_id:" + group_id);
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		try {
			List<TreeBean> attrList = monitorManageDao.getRelServersTree(group_id);
			if (attrList != null && attrList.size() > 0) {
				TreeBean tree = new TreeBean();
				jsonStr = tree.createContentServerJsonStr(attrList);
			}
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("root", jsonStr);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "展示已选择服务器树失败!!");
			// 记录日志
			log.error("内容存储服务器组管理->查询关联的服务器失败:" + e.toString(), e);

		}
		this.outJsonString(jsonResp.toString());
		log.info("--getRelContentServerTree(over)-->group_id:" + group_id);
		return null;
	}

	/**
	 * 获取内容存储服务器组关联的内容对象id和名称(包括UA)
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getRelContentsAction.action", method = RequestMethod.POST)
	public String getRelContents(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String group_id = modelJson.getString("group_id");
		log.info("--getRelContents(start)-->group_id:" + group_id);
		String jsonStr = null;
		try {
			List<TreeBean> objectList = monitorManageDao.getRelObjectsTree(group_id);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug("--getRelContents-->objectList:" + objectList);
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size, new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器关联内容对象失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("监控管理->获取内容存储服务器关联内容对象失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.debug("--getRelContents(over)");
		return null;

	}
}
