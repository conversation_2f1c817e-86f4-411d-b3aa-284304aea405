package com.sunyard.util.filelock;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.sunyard.ecm.server.cache.LazySingleton;
import com.sunyard.ecm.server.dao.SunECMDao;
import com.sunyard.util.TransOptionKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.common.Configuration;
import com.sunyard.util.DateUtil;
import com.sunyard.util.SpringUtil;

public class DatabaseLock implements Lock {
	private static Logger log = LoggerFactory.getLogger(DatabaseLock.class);
	private SunECMDao lockDao = (SunECMDao) SpringUtil.getSpringBean("sunECMDao");
	private String lockName;
	private boolean isLock = false;
    private  String serverId= LazySingleton.getInstance().load.getNodeInfoBean().getServer_id();
    private String groupID=LazySingleton.getInstance().load.getNodeInfoBean().getGroup_id();
    private  String  threadType;
	public DatabaseLock(String lockName,Class<?> threadType) {
		super();
		this.lockName = lockName+groupID;
		this.threadType=threadType.getSimpleName();
	}

	public boolean obtain() throws IOException {
		log.info("获取锁：目标锁LockName:" + lockName+",目标锁的server_id:"+this.serverId+",目标锁threadType:"+this.threadType);
		//String lockSql = "insert into group_lock(lock_name,server_id,thread_type)values(?,?,?)";
		String lockTime = DateUtil.get12bitDateStr();
		String lockSql = "insert into group_lock(lock_name,server_id,thread_type,lock_time)values(?,?,?,?)";
		String querySql="select LOCK_NAME ,SERVER_ID,THREAD_TYPE from group_lock where lock_name=?" ;
		List<Map<String, String>> record = lockDao.searchSql(querySql, new Object[] {lockName});
		if(record==null||record.isEmpty()){
		//	String result = lockDao.insertSql(lockSql, new Object[] {lockName,serverId,threadType});
			String result = lockDao.insertSql(lockSql, new Object[] {lockName,serverId,threadType,lockTime});
			if (TransOptionKey.SUCCESS.equals(result)) {
				log.info("获取锁成功：目标锁LockName:" + lockName+",目标锁的server_id:"+this.serverId+",目标锁threadType:"+this.threadType);
				isLock = true;
			} else {
				log.info("获取锁失败,该锁已被其他进程或线程获取,失败信息：" + result + ";锁记录:目标锁LockName:" + lockName+",目标锁的server_id:"+this.serverId+",目标锁threadType:"+this.threadType);
				isLock = false;
			}
		}else{
			Map<String, String> map=record.get(0);
			String serverId=map.get("SERVER_ID");
			String threadType=map.get("THREAD_TYPE");
			String lockName = map.get("LOCK_NAME");
			//String _30minbefore = DateUtil.getTimeBeforeX(30);//获取当前时间30分钟之前的时间
			String _30minbefore = Configuration.get("lockTime", "30");
			String newLockTime = DateUtil.get12bitDateStr();//获取当前时间
			//如果已经到了失效时间，则重新对该批次进行上锁，修改lock_time和server_id
			String unlockSql = "update group_lock set lock_time = ?, server_id = ? where lock_name = ? and server_id = ? and thread_type = ? and lock_time < ?";
			String result = lockDao.updateSql(unlockSql, new Object[] {newLockTime,this.serverId,lockName,serverId,threadType,_30minbefore});
			log.debug("记录的server_id:"+serverId+",记录的threadType:"+threadType+",数据库记录为："+map);
			if(!result.equals("0")){
				log.info("获取锁成功,锁记录:目标锁LockName:" + lockName+",目标锁的server_id:"+this.serverId+",目标锁threadType:"+this.threadType);
				isLock = true;
			}
			else{//如果没到失效时间则不能解锁
				log.info("获取锁失败,该锁已被其他进程或线程获取.锁记录:" + map);
				isLock = false;
			}
//			if(this.serverId.equals(serverId)&&this.threadType.equals(threadType)){
//				
//					log.info("获取锁成功,锁记录:目标锁LockName:" + lockName+",目标锁的server_id:"+this.serverId+",目标锁threadType:"+this.threadType);
//					isLock = true;
//				
//			
//			}else{
//				log.info("获取锁失败,该锁已被其他进程或线程获取.锁记录:" + map);
//				isLock = false;
//			}
		}
		
		return isLock;
	}

	public void unlock() {
		if (isLock) {
			log.info("移除锁记录：lockName:" + lockName+",serverID:"+this.serverId+",threadType:"+this.threadType);
			String unlockSql = "delete from group_lock where lock_name=? and server_id=? and thread_type=? ";
			String result = lockDao.deleteSql(unlockSql, new Object[] { lockName ,serverId,threadType});
			if (TransOptionKey.SUCCESS.equals(result)) {
				log.info("移除锁记录成功：lockName:" + lockName+",serverID:"+this.serverId+",threadType:"+this.threadType);
				isLock = false;
			} else {
				log.error("移除锁记录失败,失败信息为：" + result + ";锁记录为:" + lockName);
			    throw new RuntimeException(result);
			}
		}else{
			log.info("该对象没有持有锁：lockName:" + lockName+",serverID:"+this.serverId+",threadType:"+this.threadType);
		}
	}
}
