package com.sunyard.es.common;

import java.util.Properties;
import java.io.IOException;
import java.io.InputStream;
import org.apache.log4j.Logger;

public class ReadConfig {
	/**
	 * 日志打印
	 */
	private final static Logger LOGGER = Logger.getLogger(ReadConfig.class);

	/**
	 * 配置信息
	 */
	private static EsConfigBean configBean;

	static {
		initBean();
	}

	/**
	 * 获取配置信息
	 * 
	 * @return 返回config配置信息
	 */
	public static EsConfigBean getConfig() {
		return configBean;
	}

	/**
	 * 读取配置信息
	 */
	private static void initBean() {
		configBean = new EsConfigBean();
		Properties properties = new Properties();
		InputStream in = null;
		try {
			in = ReadConfig.class.getClassLoader().getResourceAsStream("esconf.properties");
			properties.load(in);
			configBean.setHostname(properties.getProperty("hostname"));
			configBean.setScheme(properties.getProperty("scheme"));
			int socketTimeout = 0;
			try {
				socketTimeout = Integer.parseInt(properties.getProperty("socketTimeout"));
				configBean.setSocketTimeout(socketTimeout);
			} catch (Exception e) {
//				configBean.setSocketTimeout(0);
				LOGGER.error("socketTimeout error", e);
			}
			int connectTimeout = 0;
			try {
				connectTimeout = Integer.parseInt(properties.getProperty("connectTimeout"));
				configBean.setConnectTimeout(connectTimeout);
			} catch (Exception e) {
//				configBean.setConnectTimeout(0);
				LOGGER.error("connectTimeout error", e);
			}
			int connectionRequestTimeout = 0;
			try {
				connectionRequestTimeout = Integer.parseInt(properties.getProperty("connectionRequestTimeout"));
				configBean.setConnectionRequestTimeout(connectionRequestTimeout);
			} catch (Exception e) {
//				configBean.setConnectionRequestTimeout(0);
				LOGGER.error("connectionRequestTimeout error", e);
			}
			int shards = 3;
			try {
				shards = Integer.parseInt(properties.getProperty("shards"));
				shards = shards < 3 ?3 : shards;
				configBean.setShards(shards);
			} catch (Exception e) {
				LOGGER.error("setshards=3", e);
				configBean.setShards(3);
			}
			int replicas = 1;
			try {
				replicas = Integer.parseInt(properties.getProperty("replicas"));
				replicas = replicas <= 0 ? 1 : replicas;
				configBean.setReplicas(replicas);
			} catch (Exception e) {
				LOGGER.error("setReplicas=1", e);
				configBean.setReplicas(1);
			}
		} catch (Exception e) {
			throw new RuntimeException("readConfig error------esconfig.properties", e);
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
					LOGGER.error("readConfig 关闭文件流失�?", e);
				}
			}
		}

	}
}
