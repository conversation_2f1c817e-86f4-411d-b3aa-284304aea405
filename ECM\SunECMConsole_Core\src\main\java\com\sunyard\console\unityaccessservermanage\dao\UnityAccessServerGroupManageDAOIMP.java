package com.sunyard.console.unityaccessservermanage.dao;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.contentmodelmanage.bean.TreeBean;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerGroupInfoBean;
import org.apache.commons.dbutils.DbUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 统一接入服务器组管理接口实现
 * 
 * <AUTHOR>
 * 
 */
@Repository("unityAccessServerGroupManageDao")
public class UnityAccessServerGroupManageDAOIMP
		implements
			UnityAccessServerGroupManageDAO {
	@Autowired
	private PageTool pageTool;
	@Autowired
	private DataFieldMaxValueIncrementer unityAccessServerGroupId;
	/**
	 * 日志对象
	 */
	private final static  Logger log = LoggerFactory
			.getLogger(UnityAccessServerGroupManageDAOIMP.class);

	public PageTool getPageTool() {
		return pageTool;
	}

	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}

	public DataFieldMaxValueIncrementer getUnityAccessServerGroupId() {
		return unityAccessServerGroupId;
	}

	public void setUnityAccessServerGroupId(
			DataFieldMaxValueIncrementer unityAccessServerGroupId) {
		this.unityAccessServerGroupId = unityAccessServerGroupId;
	}

	public List<UnityAccessServerGroupInfoBean> getUnityAccessServerGroupList(
			int groupId, String serverName, int start, int limit) {
		log.info( "--getUnityAccessServerGroupList(start)-->groupId:"+groupId+",serverName:"+serverName);
		List<UnityAccessServerGroupInfoBean> beanList = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT GROUP_ID, GROUP_NAME,IP,HTTP_PORT,SOCKET_PORT,REMARK FROM UNITY_ACCESS_SERVER_GROUP ");
		sql.append(" WHERE 1=1 ");
		List list=new ArrayList();
		if (groupId != 0) {
			sql.append(" AND  GROUP_ID=? ");
			list.add(groupId);
		}
		if (serverName != null && !"".equals(serverName)) {
			sql.append(" WHERE GROUP_NAME LIKE  '%'||?||'%' ");
			list.add(serverName);
		}
		try {
			log.debug( "--getUnityAccessServerGroupList-->sql："+sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(
					sql.toString(), start, limit),
					UnityAccessServerGroupInfoBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器组管理->获取统一接入服务器组列表失败!" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerGroupManageDAOIMP===>getUnityAccessServerGroupList--hasPage:"
							+ e.toString());
		}
		log.info( "--getUnityAccessServerGroupList(over)-->beanList:"+beanList);
		return beanList;
	}

	public List<UnityAccessServerGroupInfoBean> getUnityAccessServerGroupList(
			int groupId, String serverName) {
		log.info( "--getUnityAccessServerGroupList(start)-->groupId:"+groupId+",serverName:"+serverName);
		List<UnityAccessServerGroupInfoBean> beanList = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT GROUP_ID, GROUP_NAME,IP,HTTP_PORT,SOCKET_PORT,REMARK FROM UNITY_ACCESS_SERVER_GROUP ");
		sql.append(" WHERE 1=1 ");
		if (groupId != 0) {
			sql.append(" AND  GROUP_ID=").append(groupId);
		}
		if (serverName != null && !"".equals(serverName)) {
			sql.append(" WHERE GROUP_NAME LIKE '").append(serverName).append(
					"%'");
		}
		try {
			log.debug( "--getUnityAccessServerGroupList-->sql："+sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					UnityAccessServerGroupInfoBean.class);
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器组管理->获取统一接入服务器组列表失败!" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerGroupManageDAOIMP===>getUnityAccessServerList--hasPage:"
							+ e.toString());
		}
		log.info( "--getUnityAccessServerGroupList(over)-->beanList:"+beanList);
		return beanList;
	}
	//校验服务器组IP地址和端口唯一性
	public int checkServerGroupIPandPort(int groupId, String groupIp,
			int httpPort, int socketPort) {
		log.info( "--checkServerGroupIPandPort(start)-->groupId:"+groupId+",groupIp:"+groupIp+",httpPort:"+httpPort+",socketPort:"+socketPort);
		if (groupIp == null || groupIp.equals("")) {
			log.debug( "--checkServerGroupIPandPort-->groupIp is null");
			return 0;
		}
		StringBuffer sql = new StringBuffer();
		int count = 0;
		sql.append("SELECT count(1)  FROM UNITY_ACCESS_SERVER_GROUP ");
		sql.append(" WHERE IP=? AND (HTTP_PORT=? OR SOCKET_PORT=?)");
		List list=new ArrayList();
		list.add(groupIp);
		list.add(httpPort);
		list.add(socketPort);
		if (groupId != 0) {
			sql.append(" AND GROUP_ID!=? ");
			list.add(groupId);
		}
		try {
			log.debug( "--checkServerGroupIPandPort-->sql："+sql.toString());
			count = DataBaseUtil.SUNECM.queryInt(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器组管理->校验组ip和端口唯一性失败!" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerGroupManageDAOIMP===>checkGroupServerIPandPort:"
							+ e.toString());
		}
		log.info( "--checkServerGroupIPandPort(over)-->count:"+count);
		return count;
	}

	public boolean addUnityAccessServerGroup(
			UnityAccessServerGroupInfoBean bean, String serverIds) {
		log.info( "--addUnityAccessServerGroup(start)-->bean:"+bean+";serverIds:"+serverIds);
		if (bean == null) {
			log.debug( "--addUnityAccessServerGroup-->bean is null!");
			return false;
		}
		int group_id = unityAccessServerGroupId.nextIntValue();
		Collection<String> sqls = new ArrayList<String>();
		StringBuffer sql = new StringBuffer();
		sql
				.append("INSERT INTO UNITY_ACCESS_SERVER_GROUP( GROUP_ID,GROUP_NAME,IP,HTTP_PORT,SOCKET_PORT, REMARK)  VALUES(");
		sql.append(group_id).append(",'").append(bean.getGroup_name()).append(
				"','");
		sql.append(bean.getIp()).append("',").append(bean.getHttp_port())
				.append(",");
		sql.append(bean.getSocket_port()).append(",'").append(bean.getRemark())
				.append("')");
		sqls.add(sql.toString());// 增加服务器组SQL
		if (serverIds != null && !serverIds.equals("")) {
			List<String> serverIdList = StringUtil.stringToList(serverIds, ",");
			if (serverIdList != null) {
				for (int i = 0; i < serverIdList.size(); i++) {
					sql = new StringBuffer();
					sql.append("UPDATE UNITY_ACCESS_SERVER  SET GROUP_ID=")
							.append(group_id);
					sql.append("  WHERE SERVER_ID =").append(
							serverIdList.get(i));
					sqls.add(sql.toString());
				}
			}
		}
		Connection conn = null;
		try {
			// 取得连接
			conn = DataBaseUtil.SUNECM.getConnection();
			conn.setAutoCommit(false);// 手动控制事务
			log.debug( "--addUnityAccessServerGroup-->sql："+sqls.toString());
			// 执行SQL块
			DataBaseUtil.SUNECM.exceTrans(sqls, conn);
			DbUtils.commitAndClose(conn);
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器组管理->新增服务器组失败!" + e.toString(), e);
			// 事务回滚
			if (conn != null) {
				DbUtils.rollbackAndCloseQuietly(conn);
			}
			throw new DBRuntimeException(
					"ContentObjectManageDaoImpl===>addContentServerGroup:"
							+ e.toString());

		}
		log.info( "--addUnityAccessServerGroup(over)");
		return true;
	}

	public boolean updateUnityAccessServerGroup(
			UnityAccessServerGroupInfoBean bean, String serverIds) {
		log.info( "--SunECMConsole-->UnityAccessServerGroupManageDAOIMP-->updateUnityAccessServerGroup-->bean:"+bean+";serverIds:"+serverIds);
		if (bean == null) {
			log.debug( "--SunECMConsole-->UnityAccessServerGroupManageDAOIMP-->updateUnityAccessServerGroup-->bean is null!");
			return false;
		}
		Collection<String> sqls = new ArrayList<String>();
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE UNITY_ACCESS_SERVER_GROUP SET GROUP_NAME ='")
				.append(bean.getGroup_name());
		sql.append("',IP='").append(bean.getIp()).append("',HTTP_PORT=")
				.append(bean.getHttp_port());
		sql.append(",SOCKET_PORT=").append(bean.getSocket_port()).append(
				",REMARK='").append(bean.getRemark());
		sql.append("' WHERE GROUP_ID=").append(bean.getGroup_id());
		sqls.add(sql.toString());// 修改服务器组信息
		sql = new StringBuffer();
		sql.append("UPDATE  UNITY_ACCESS_SERVER SET GROUP_ID = 0");
		sql.append(" WHERE   GROUP_ID=").append(bean.getGroup_id());
		sqls.add(sql.toString());// 删除服务器组和服务器关联关系
		if (serverIds != null && !serverIds.equals("")) {
			List<String> serverIdList = StringUtil.stringToList(serverIds, ",");
			if (serverIdList != null) {
				for (int i = 0; i < serverIdList.size(); i++) {
					sql = new StringBuffer();
					sql.append("UPDATE UNITY_ACCESS_SERVER  SET GROUP_ID=")
							.append(bean.getGroup_id());
					sql.append("  WHERE SERVER_ID =").append(
							serverIdList.get(i));
					sqls.add(sql.toString());// 增加服务器组和服务器关联关系
				}
			}

		}
		Connection conn = null;
		try {
			// 取得连接
			conn = DataBaseUtil.SUNECM.getConnection();
			conn.setAutoCommit(false);// 手动控制事务
			log.debug( "--SunECMConsole-->UnityAccessServerGroupManageDAOIMP-->updateUnityAccessServerGroup-->sql："+sqls.toString());
			// 执行SQL块
			DataBaseUtil.SUNECM.exceTrans(sqls, conn);
			DbUtils.commitAndClose(conn);
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器组管理->修改服务器组失败!" + e.getMessage(),e);
			// 事务回滚
			if (conn != null) {
				DbUtils.rollbackAndCloseQuietly(conn);
			}
			throw new DBRuntimeException(
					"ContentObjectManageDaoImpl===>addContentServerGroup:"
							+ e.toString());
		}
		return true;
	}
	//查询被统一接入服务器组关联的统一接入服务器
	public List<TreeBean> getRelUnityAccessServerTree(int groupId) {
		log.info( "--getRelUnityAccessServerTree(start)-->groupId:"+groupId);
		if (groupId == 0) {
			log.debug( "--getRelUnityAccessServerTree-->groupId is null!");
			return null;
		}
		List<TreeBean> beanList = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append(
						"SELECT SERVER_ID id,SERVER_NAME  text FROM  UNITY_ACCESS_SERVER WHERE GROUP_ID=? ");
		List list=new ArrayList();
		list.add(groupId);
		try {
			log.debug( "--getRelUnityAccessServerTree-->sql："+sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					TreeBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器组管理->查询已关联的服务器失败!" + e.toString(), e);
			throw new DBRuntimeException(
					"ContentObjectManageDaoImpl===>getRelUnityAccessServerTree:"
							+ e.toString());
		}
		log.info( "--getRelUnityAccessServerTree(over)-->beanList:"+beanList);
		return beanList;
	}
	//查询未被统一接入服务器组关联的统一接入服务器树
	public List<TreeBean> getUnRelUnityAccessServerTree() {
		log.info( "--getUnRelUnityAccessServerTree(start)");
		List<TreeBean> beanList = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT SERVER_ID  id,SERVER_NAME  text FROM  UNITY_ACCESS_SERVER where GROUP_ID is null or  GROUP_ID =0");
		try {
			log.debug( "--getUnRelUnityAccessServerTree-->sql："+sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					TreeBean.class);
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器组管理->查询未关联的服务器失败!" + e.toString(), e);
			throw new DBRuntimeException(
					"ContentObjectManageDaoImpl===>getUnRelUnityAccessServerTree:"
							+ e.toString());
		}
		log.info( "--getUnRelUnityAccessServerTree(start)-->beanList:"+beanList);
		return beanList;
	}
	//检测该服务器组名称是否存在
	public int checkGroupName(int groupId, String groupName) {
		log.info( "--SunECMConsole-->UnityAccessServerGroupManageDAOIMP-->checkGroupName-->groupId:"+groupId+";groupName:"+groupName);
		StringBuffer sql = new StringBuffer();
		int count = 0;
		sql
				.append(
						"select count(1) from UNITY_ACCESS_SERVER_GROUP g where g.GROUP_NAME=? ");
		List list=new ArrayList();
		list.add(groupName);
		if (groupId != 0) {
			sql.append(" AND GROUP_ID!=? ");
			list.add(groupId);
		}
		try {
			log.debug( "--SunECMConsole-->UnityAccessServerGroupManageDAOIMP-->checkGroupName-->sql："+sql.toString());
			count = DataBaseUtil.SUNECM.queryInt(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器组管理->校验服务器组名称唯一性失败:" + e.getMessage(), e);
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>checkServerName:"
							+ e.getMessage());
		}
		return count;
	}
}
