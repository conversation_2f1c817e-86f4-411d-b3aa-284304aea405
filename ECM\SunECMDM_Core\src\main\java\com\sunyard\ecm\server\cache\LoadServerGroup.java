package com.sunyard.ecm.server.cache;

import com.sunyard.ecm.server.bean.GroupBean;
import com.sunyard.ecm.server.cache.wsclient.GetServiceConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务器组配置管理
 * <AUTHOR>
 * @version 1.0
 */
public class LoadServerGroup {
	private final static Logger log = LoggerFactory.getLogger(LoadServerGroup.class);
	//存放服务器所有组信息，对象key为服务器组的ID  value为服务器组对象Bean
	private Map<String, GroupBean> groupMap ;
	
	// 获取配置信息类
	private GetServiceConfig getConfig = new GetServiceConfig();
	
	/**
	 * 无参构造函数
	 * 实例该类后调用加载服务器组配置
	 */
	public LoadServerGroup(){
		load();
	}
	/**
	 * 加载服务器组配置
	 */
	public final void load() {
		groupMap = new HashMap<String, GroupBean>();
//		List<GroupBean> tempList = null;
		// getConfig.getServerGroup();
		// if(tempList != null && tempList.size() > 0){
		// for(GroupBean groupBean : tempList){
		// groupMap.put(groupBean.getGroup_id(), groupBean);
		// }
		// }
	}
	/**
	 * 更新服务器组配置信息
	 * @param groupBean
	 */
	public void updateServerGroup(GroupBean groupBean){
		if(groupMap == null){
			groupMap = new HashMap<String, GroupBean>();
		}
		groupMap.put(groupBean.getGroup_id(), groupBean);
	}
	/**
	 * 根据服务器组ID获取该配置信息
	 * @param groupID
	 * @return
	 */
	public GroupBean getServerGroupByGroupId(String groupID){
		if(groupMap == null){
			return null;
		}else{
			return groupMap.get(groupID);
		}
		
	}
	/**
	 * 获取所有的服务器组配置
	 * @return
	 */
	public Map<String, GroupBean> getAllServerGroup(){
		return groupMap;
	}
}