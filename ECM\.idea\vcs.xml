<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="VcsDirectoryMappings">
    <mapping directory="" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/SunECMConsole3.5_SpringBoot" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/SunECMConsole_Core" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/SunECMConsole_vue" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/SunECMDM3.5_SpringBoot" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/SunECMDM_Core" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/../ETL/SunETLConsole" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/../ETL/SunETLExecute" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/../前端/dopUnify" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/../前端/sunfa_vue" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/../前端/数字运营平台-非工程化前端" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/../后端/sunfa-parent" vcs="Git" />
    <mapping directory="$PROJECT_DIR$/../后端/数字运营平台-父工程-dop" vcs="Git" />
  </component>
</project>