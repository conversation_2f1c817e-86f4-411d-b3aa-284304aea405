package com.sunyard.console.safemanage.filter;

import com.sunyard.console.common.config.ReadConfig;
import com.sunyard.console.common.util.DateUtil;
import com.sunyard.console.common.util.RedisUtil;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.monitor.monitorCenter.singleton.LazySingleton;
import com.sunyard.console.safemanage.bean.UserInfoBean;
import com.sunyard.redis.RedisClient;
import org.apache.log4j.Logger;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

public class SessionManage {
	static String sessionType = ReadConfig.getConsoleConfigBean().getSessionType();
	final static Logger log = Logger.getLogger(SessionManage.class);

	/**
	 * 校验session有效，
	 * 
	 * @return true表示有效，false 表示无效
	 */
	public UserInfoBean getUserSession(String token, HttpServletRequest request) {
		if (sessionType.equals("1")) {
			// httpsession
			HttpSession session = request.getSession();
			log.debug("当前SESSION ID是[" + session.getId() + "]创建时间是[" + session.getCreationTime() + "]");

			UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
			if (user != null && user.getToken().equals(token)) {
				if (setSessionTime(session)) {
					return user;
				} else {
					return null;
				}

			} else {
				return null;
			}
		} else if (sessionType.equals("2")) {
			// redis
			String loginId = getTokenFromRedis(token);
			if (StringUtil.stringIsNull(loginId)) {
				return null;
			}
			UserInfoBean bean = new UserInfoBean();
			bean.setLogin_id(loginId);
			bean.setUser_name(loginId);
			return bean;
		} else {
			log.error("error session type");
			return null;
		}

	}

	public void addUserSession(UserInfoBean user) {
		log.debug("addSession");

		if (sessionType.equals("1")) {
			// httpsession
			HttpSession session = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
					.getRequest().getSession();
			log.debug("当前SESSION ID是[" + session.getId() + "]创建时间是[" + session.getCreationTime() + "]");

			Map<String, UserInfoBean> userSessionMap = LazySingleton.getUserSessionMap();

			session.setAttribute("loginUser", user);
			userSessionMap.put(user.getToken(), user);

		} else if (sessionType.equals("2")) {
			RedisUtil redisClient = new RedisUtil();
			Map<String, String> redis_map = new HashMap<String, String>();
			redis_map.put("loginUser", user.getLogin_id());
			redis_map.put("loginDateTime", DateUtil.getCurrentDateTime());

			redisClient.pushHMap(user.getToken(), redis_map,
					ReadConfig.getConsoleConfigBean().getMaxLoginSessionInterval());

		}
	}

	public void rmUserSession(String token) {
		log.debug("rmUserSession");
		if (sessionType.equals("1")) {
			HttpSession session = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes())
					.getRequest().getSession();
			Map<String, UserInfoBean> userSessionMap = LazySingleton.getUserSessionMap();
			userSessionMap.remove(token);
			session.removeAttribute("loginUser");
			session.invalidate();

		} else if (sessionType.equals("2")) {
			RedisClient redisClient = new RedisClient();
			redisClient.del(token);
		}

	}

	/*
	 * token : 用户的唯一标识 根据token操作redis数据
	 */
	/**
	 * 
	 * @param token
	 * @return true表示有，false表示沒有token
	 */
	public String getTokenFromRedis(String token) {
		if (StringUtil.stringIsNull(token)) {
			log.error("no token");
			return null;
		}
		log.info("checkredis  token ");
		RedisUtil rediUtil = new RedisUtil();
		Map<String, String> map = rediUtil.getHashMap(token);
		if (map == null || map.size() == 0) {
			log.info("no token");
			return null;
		} else {
			log.info("剩余时间:" + rediUtil.getRedisttl(token));
			rediUtil.setRedisTimeout(token, ReadConfig.getConsoleConfigBean().getMaxLoginSessionInterval());
			return map.get("loginUser");
		}
	}

	public boolean setSessionTime(HttpSession session) {
		log.debug("set session");
		Map<String, UserInfoBean> userSessionMap = LazySingleton.getUserSessionMap();
		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
		if (user != null) {

			String token = user.getToken();
			UserInfoBean sessionUser = userSessionMap.get(token);
			if (sessionUser != null) {
				String last = sessionUser.getPsw_mdf_date();
				log.debug("loginId:" + token + ",lastTime=" + last);
				try {
					last = DateUtil.getMDrqzhsti14(last,
							ReadConfig.getConsoleConfigBean().getMaxLoginSessionInterval());
				} catch (ParseException e1) {
					log.error("" + last, e1);
					last = DateUtil.getMDrqzhsti14();
				}
				log.debug("loginout time:" + last);
				boolean flag = DateUtil.compareTime(last, DateUtil.getMDrqzhsti14());
				if (flag) {
					// 表示该用户已经失效
					log.info("remove loginId:" + token + ",lastTime=" + last);
					userSessionMap.remove(token);
					return false;
				} else {
					log.debug("reset lastTime");
					sessionUser.setPsw_mdf_date(DateUtil.getMDrqzhsti14());
					return true;
				}
			} else {
				return false;
			}
		} else {
			return false;
		}
	}
}
