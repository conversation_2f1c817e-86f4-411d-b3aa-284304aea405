<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://dubbo.apache.org/schema/dubbo" xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       http://dubbo.apache.org/schema/dubbo
       http://dubbo.apache.org/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.sunyard"></context:component-scan>

    <dubbo:registry address="nacos://***********:8848"  check="false" />

    <!--    <dubbo:reference cluster="broadcast" id="SunEcmDubboDm" interface="com.sunyard.ws.internalapi.SunEcmAccess"  timeout="5000" check="false" version="1.0.0" group="ALL"/>-->
    <dubbo:reference  id="SunEcmDubboDm_SunECMDM127.0.0.18080" interface="com.sunyard.ws.internalapi.SunEcmAccess"  timeout="5000" check="false" version="1.0.0" group="1"/>
    <dubbo:reference  id="SunEcmDubboDm_SunECMDM127.0.0.18081" interface="com.sunyard.ws.internalapi.SunEcmAccess"  timeout="5000" check="false" version="1.0.0" group="2"/>

</beans>
