<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

	<bean id="batchDao" class="com.sunyard.ecm.server.dao.DMBatchDao">
		<property name="sunECMDao" ref="sunECMDao" />
	</bean>
	
	<bean id="esDao" class="com.sunyard.ecm.server.dao.DMEsDao">
		<property name="sunECMDao" ref="sunECMDao" />
	</bean>

	<bean id="dMDBDao" class="com.sunyard.ecm.server.dao.DMDBDaoImpl">
		<property name="sunECMDao" ref="sunECMDao" />
		<property name="batchDao" ref="batchDao" />
	</bean>

	<bean id="fileSystem" class="com.sunyard.ecm.server.fileSystem.DMFileSystem">
	</bean>
  	<bean id="tokenRelUserUtil" class="com.sunyard.util.TokenRelUserUtil" destroy-method="destroy" init-method="init">
		<property name="sunECMDao" ref="sunECMDao" />
	</bean>
	<bean id="migrateServerDao" class="com.sunyard.ecm.server.dao.DMMigrateServerDao">
		<property name="sunECMDao" ref="sunECMDao" />
	</bean>


	<bean id="indexCompressDao" class="com.sunyard.scheduler.dao2.IndexCompressDaoImpl">
		<property name="ecmDao" ref="sunECMDao" />
		<property name="pageTool" ref="pageTool" />
	</bean>

	<bean id="offlineDao" class="com.sunyard.scheduler.dao2.OfflineDaoImpl">
		<property name="ecmDao" ref="sunECMDao" />
		<property name="pageTool" ref="pageTool" />
	</bean>

	<!-- 断点文件保存方法 com.sunyard.ecm.server.breakpoint.DMBreakPointUtil 保存到批次同一目录，DM服务器组使用，批次保存位置可能是网络位置。 
		com.sunyard.ecm.server.breakpoint.TempFileBreakPointHandler 保存到配置文件中的配置项breakPoint。适用无网络磁盘，且CE存储无批次保存位置的情况。 -->
	<bean id="breakPointHandler" class="com.sunyard.ecm.server.breakpoint.DMBreakPointUtil">
	</bean>

	<bean id="checkDao" class="com.sunyard.ecm.server.dao.DMCheckDao">
		<property name="sunECMDao" ref="sunECMDao" />
	</bean>

	<bean id="loginDao" class="com.sunyard.ecm.server.dao.DMLoginDao">
	</bean>
	<!--URL加密解密及参数解析-->
 <bean id="urlUtils" class="com.sunyard.ecm.server.util.IurlUtilsImp"></bean>
 <!-- 接入第三方系统时，第三方系统的配置实现类 -->
 <bean id="extConfig" class="com.sunyard.ecm.server.cache.TestConfiguration" init-method="init" destroy-method="destroy"/>
	<!-- 内容迁移获取 -->
	<bean id="contentMigrateDao" class="com.sunyard.scheduler.dao.ContentMigrateDaoImp">
		<property name="pageTool" ref="pageTool"></property>
	</bean>
	<!-- 立即迁移DAO实现 -->
	<bean id="immedMigrateDao"
		class="com.sunyard.ecm.server.dm.batchmigrate.immedMigrateDaoImp">
	</bean>

	<!-- 内容清理DAO实现 -->
	<bean id="contentClearDao" class="com.sunyard.scheduler.dao.ContentClearDaoImpl">
		<property name="pageTool" ref="pageTool"></property>
	</bean>
	<!-- 离线内容清理DAO实现 -->
	<bean id="offlineClearDao" class="com.sunyard.scheduler.dao.OfflineClearDaoImpl">
		<property name="pageTool" ref="pageTool"></property>
	</bean>
	<bean id="fileEncryptAndDecryptDao" class="com.sunyard.ecm.server.encrypt.FileEncryptAndDecryptDaoImpl">
	</bean>
	<bean id="contentNearLineDao" class="com.sunyard.scheduler.dao.ContentNearLineDaoImpl">
		<property name="pageTool" ref="pageTool"></property>
		<property name="sunECMDao" ref="sunECMDao" />
	</bean>	
	<bean id="extReuest" class="com.sunyard.ecm.server.service.ExtRequestImpl">
	    <property name="sunECMDao" ref="sunECMDao" />
	</bean>

</beans>