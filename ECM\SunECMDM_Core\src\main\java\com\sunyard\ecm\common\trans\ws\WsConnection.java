package com.sunyard.ecm.common.trans.ws;

import com.sunyard.ecm.common.trans.AbstractConnection;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.context.Context;
import com.sunyard.ecm.server.context.ContextUtil;
import com.sunyard.exception.SunECMException;
import com.sunyard.util.TransOptionKey;
import com.sunyard.ws.comm.Base64Coder;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.mutable.MutableObject;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;

public class WsConnection extends AbstractConnection {
	private Map<String, String> inputParam;
	private MutableObject result;

	public WsConnection(Map<String, String> inputParam, MutableObject result)
			throws IOException {
		super();
		this.inputParam = inputParam;
		this.result = result;
		clientIp = inputParam.get("sunecm_system_remote_ip");//在WS接口类SunEcmAccessImpl中，设置了客户端IP
	}

	@Override
	protected boolean isLeagalMessage(String receive) {
		return true;
	}

	@Override
	protected Map<String, String> getInputParam(String[] content) {
		return inputParam;
	}

	@Override
	protected String getOption(String[] content) {
		return TransOptionKey.MESSAGE_PROCESS;// ws只处理信息
	}

	@Override
	public void needAlive() {
		super.alive = false;
	}

	public void close() {
		// do nothing
	}

	public String getMessages() throws IOException {
		return "use getInputParam";
	}

	public void reciveFile(long filesize, OutputStream out, FileBean fileBean)
			throws IOException {
		String fileStr = inputParam.get("FILE");
		byte[] fileBytes = Base64Coder.decode(fileStr.toCharArray());
		out.write(fileBytes);
		fileBean.setReceived((long) fileBytes.length);
	}

	public boolean sendMessages(String msg) throws IOException {
		this.result.setValue(msg);
		Context context = ContextUtil.getContext();
		context.sendRecord(msg);
		return true;
	}

	public boolean sendErrorMessages(int code,String msg) throws IOException {
		this.result.setValue(msg);
		Context context = ContextUtil.getContext();
		context.sendRecord(msg);
		return true;
	}

	public String getConnectionType() {
		return "WebService";
	}

	public void reciveFile(long filesize, OutputStream out, FileBean fileBean, String encodeLength)
			throws IOException, SunECMException {
		if (StringUtils.isEmpty(encodeLength) || encodeLength.equals("0") || "null".equals(encodeLength)) {
			reciveFile(filesize, out, fileBean);
			return;
		} else {
			throw new SunECMException("WS不支持加密模型上传，encodeLength：" + encodeLength);
		}
		
	}

	public String getHttpMessages() throws IOException {
		// http需要实现，Ws不需要
		return null;
	}


	@Override
	public Map<String, MultipartFile> getHttpFiles() {
		// http需要实现，Ws不需要
		return null;
	}

	public void httpReciveFile(long fileSize, OutputStream out, FileBean fileBean, String encodeLength)
			throws IOException, SunECMException {
		// http需要实现，Ws不需要
		
	}

	public void httpReciveFile(long fileSize, OutputStream out, FileBean fileBean) throws IOException {
		//http需要实现，Ws不需要
		
	}

	

	
}