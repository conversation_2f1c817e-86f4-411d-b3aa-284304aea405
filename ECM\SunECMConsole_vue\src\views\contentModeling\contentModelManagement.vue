<template>
  <el-container style="height: 100%; border: 1px solid #eee">
    <el-aside width="200px" style="background-color: rgb(238, 241, 246); height:100%">
      <el-input v-model="filterText"
      suffix-icon="el-icon-search"
      ></el-input>
      <el-tree
        :props="props"
        :data="signalList"
        :default-expand-all="true"
        @node-click="clickNode"
         :filter-node-method="filterNode"
        ref="tree"
      >
      </el-tree>
    </el-aside>

    <el-container>
      <el-header style="text-align: right; font-size: 12px">
        <el-button
          v-if="this.hasPerm('objectCreate')"
          v-waves
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="handleCreateModelObj"
        >
          新增模型
        </el-button>
        <el-button
          v-if="this.hasPerm('objectUpdate')"
          v-waves
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-edit"
          @click="handleUpdateModelObj"
        >
          修改模型
        </el-button>
        <el-button
          v-if="this.hasPerm('objectDelete')"
          v-waves
          class="filter-item"
          type="danger"
          size="mini"
          icon="el-icon-delete-solid"
          @click="handleDeleteObj"
        >
          删除模型
        </el-button>

        <el-button
          v-if="this.hasPerm('addSepTable')"
          v-waves
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-plus"
          @click="handleCreateModelObj_Index"
        >
          新增模型索引
        </el-button>
        <!-- <el-button
          v-if="this.hasPerm('objectPermission')"
          v-waves
          class="filter-item"
          type="primary"
          size="mini"
          icon="el-icon-edit"
          @click="handleCreateModelObj_Part"
        >
          配置文档对象
        </el-button> -->
      </el-header>

      <el-main>
        <el-table
          :key="tableKey"
          v-loading="listLoading"
          :data="attrList"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="属性代码" min-width="16%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.attribute_code }}</span>
            </template>
          </el-table-column>

          <el-table-column label="属性名称" min-width="16%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.attribute_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="属性类型" min-width="14%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.attribute_type_name }}</span>
            </template>
          </el-table-column>

          <el-table-column label="属性长度" min-width="12%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.attribute_length }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否为空" min-width="10%"  align="center">
            <template slot-scope="{ row }">
              <span v-if="row.attribute_isNull == 0">否</span>
              <span v-if="row.attribute_isNull == 1">是</span>
            </template>
          </el-table-column>

          <el-table-column label="默认值" min-width="15%"  align="center">
            <template slot-scope="{ row }">
              <span>{{ row.attribute_default }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          @pagination="getAttrListByModelCode"
        />
      </el-main>
    </el-container>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false"
      :title="model_textMap[model_dialogStatus]"
      :visible.sync="model_dialogFormVisible"
    >
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="模型代码" prop="model_code">
          <el-input
            v-model="temp.model_code"
            :disabled="model_code_status ? true : false"
            prop="model_code" onkeyup="value=value.replace(/[\u4e00-\u9fa5]/g,'')"
          />
        </el-form-item>
        <el-form-item label="模型名称" prop="model_name">
          <el-input v-model="temp.model_name" />
        </el-form-item>
        <el-form-item label="表空间" prop="table_space">
          <el-input v-model="temp.table_space" />
        </el-form-item>
        <el-form-item label="分表周期" prop="separate_table_days">
          <el-input v-model="temp.separate_table_days" type="number" min="0" step="1"/>
        </el-form-item>
        <el-form-item label="数据类型" prop="model_type">
          <el-select
            v-model="temp.model_type"
            placeholder="请选择类型"
            :disabled="model_code_status ? true : false"
            @change="doDocPart"
          >
            <el-option label="文档对象" value="1" />
            <el-option label="索引对象" value="0" />
          </el-select>
        </el-form-item>

          <el-form-item label="文档对象" prop="doc_part" 
              v-show="temp.model_type == '0' ? true : false">
          <el-select
            v-model="temp.doc_part"
            placeholder="请选择文档对象"
          >
            <el-option
              v-for="doc in loadDocParts"
              :key="doc.id"
              :label="doc.text"
              :value="doc.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="版本控制" prop="version_control">
          <el-select
            v-model="temp.version_control"
            placeholder="请选择状态"
            :disabled="model_code_status ? true : false"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="安全校验" prop="token_check">
          <el-select
            v-model="temp.token_check"
            placeholder="请选择是否安全验证"
          >
            <el-option label="是" value="1" />
            <el-option label="否" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="加密类型" prop="encode_type" v-if="showDocPart">
          <el-select
            v-model="temp.encode_type"
            placeholder="请选择加密类型"
            :disabled="model_code_status ? true : false"
            @change="showEncodeByteSize"
          >
            <el-option label="不加密" value="0" />
            <el-option label="部分加密" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item
          label="加密字节"
          prop="encodeByteSize"
          v-if="showencodeByteSize"
        >
          <el-input
            v-model="temp.encodeByteSize"
            :disabled="model_code_status ? true : false"
            type="number"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button  size="mini" @click="model_dialogFormVisible = false"> 取消 </el-button>

        <el-button size="mini" type="primary" @click="handleCreateModelObj_Attr()">
          下一步
        </el-button>
      </div>
    </el-dialog>

    <modelAttrRel
      ref="model_attr_ref"
      :modelCode="this.modelCode"
      :modelType="this.objModelType"
      :modelUpdateState="this.model_code_status"
      :objMsg="this.temp"
      v-on:test="handleUpdateModelObj"
      v-on:testt="handleCreateModelObj"
      v-on:getAllObj="getSignalList"
      v-on:resetModelForm="resetTemp"
    >
    </modelAttrRel>

    <modelIndexRel ref="model_index_rel" :modelCode="this.modelCode">
    </modelIndexRel>
    <modelDocRel
      ref="model_doc_rel"
      :modelCode="this.modelCode"
      v-on:getAllObj="getSignalList"
    >
    </modelDocRel>
  </el-container>
</template>

<script>
import {
  getContentObjectList,
  deleteModelObj,
  getContentObject,
  getAttrListByModelCode,
  getAllDocList
} from "@/api/contentObjectManage";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import modelAttrRel from "./dialog/model_attr_rel.vue";
import modelIndexRel from "./dialog/model_index_rel.vue";
import modelDocRel from "./dialog/model_doc_rel.vue";
import {checkEnglish } from '@/utils/validate.js'
import global from "../../store/global.js";

export default {
   watch: {
      filterText(val) {
        this.$refs.tree.filter(val);
      }
    },
  components: { Pagination, modelAttrRel, modelIndexRel, modelDocRel },
  directives: { waves,elDragDialog },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
  },

  data() {
    return {
      signalList: [{ label: "内容模型", id: "0", children: [] }],
      props: {},
      currLevel: "",
      currSignal: {},
      currChannel: {},
      // searchData: "",
      filterText:"",
      tableKey: 0,
      list: [
        {
          creation_date_column_name: "",
          model_code: "",
          model_name: "",
          version_control: "",
          encode_type: "",
          model_type: "",
          token_check: "",
          encodeByteSize: "",
          separate_table_days: "",
        },
      ],
      attrList: null,
          total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 10,
        modelCode: undefined,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: "+attribute_code",
      },
      importanceOptions: [1, 2, 3],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
        model_code: "",
        model_name: "",
        version_control: "",
        token_check: "",
        encodeByteSize: "",
        separate_table_days: "",
        table_space: "",
        model_type: "",
        doc_part:"",
      },
      loadAttributeTypes: [],
      loadDocParts: [],
      model_dialogFormVisible: false,
      model_dialogStatus: "",
      model_attr_dialogStatus: "",
      model_textMap: {
        update: "修改模型",
        create: "新增模型",
      },

      dialogPvVisible: false,
      modelCode: null,
      hasDoc: null,
      objModelType: "",
      f_obj_id: null,
      pvData: [],
      rules: {
        model_code: [
          { required: true, message: global.regexCodeText, pattern:global.regexCode, trigger: "blur" },
        ],
        model_name: [
          { required: true, message: global.regexNameText, pattern:global.regexName, trigger: "blur" },
        ],
        separate_table_days: [
          { required: true, message: "0为不分表", trigger: "blur" },
        ],
        model_type: [
          { required: true, message: "数据类型必选", trigger: "blur" },
        ],
        version_control: [
          { required: true, message: "版本控制必选", trigger: "blur" },
        ],
        token_check: [
          { required: true, message: "安全校验必选", trigger: "blur" },
        ],
        encode_type: [
          { required: true, message: "加密类型必选", trigger: "blur" },
        ],
        table_space:[
          { message: global.regexCodeText, pattern:global.regexCode,trigger: "blur" },
        ]
      },
      downloadLoading: false,
      model_code_status: false,
      notChoiceDataList: [],
      value: [1],
      choiceDataList: [],
      showDocPart: false,
      showencodeByteSize: false,
      renderFunc(h, option) {
        return (
          <span>
            {option.key} - {option.label}
          </span>
        );
      },
    };
  },
  created() {
    // 创建时初始化signalList的值，也可以是一个方法
    // 或者在某事件触发时改变signalList的值，树会随之改变
    this.getSignalList();
  },
  methods: {
        filterNode(value, data) {
        if (!value) return true;
        return data.label.indexOf(value) !== -1;
      },
    doDocPart(id) {
      if (id == 1) {
        //文档对象，显示某些文本框
        this.showDocPart = true;
        this.showEncodeByteSize(this.temp.encode_type);
      } else {
        this.showDocPart = false;
        this.showencodeByteSize = false;
      }
    },
    showEncodeByteSize(id) {
      if (id == 1) {
        //部分加密
        this.showencodeByteSize = true;
      } else {
        this.showencodeByteSize = false;
      }
    },

    getSignalList() {
    this.attrList =[];
      getContentObjectList().then((response) => {
        this.signalList[0].children = response.msg;
        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },
    getAttrListByModelCode() {
      this.listLoading = true;
      this.listQuery.modelCode = this.modelCode;
      getAttrListByModelCode(this.listQuery).then((response) => {
        this.attrList = response.msg.root;
        this.total = Number(response.msg.totalProperty);
        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },

    getDocList() {
      getAllDocList(this.modelCode).then((response) => {
        this.loadDocParts = response.msg;
        if(this.model_dialogStatus == "update"){//更新
        this.loadDocParts.unshift({id:"0",text: " "});
          this.temp.doc_part = "0";
          for(let item of this.loadDocParts){
            if(item.child){
              this.temp.doc_part = item.id;
            }
          }
        }
      });
    },

    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        separate_table_days:"0",
        model_type:"1",
        version_control:"0",
        encode_type:"0",
        token_check:"0",
        remark: "",
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
        doc_part:""
      };
    },

    handleCreateModelObj() {
      this.resetTemp();
      this.modelCode = "";
      this.objModelType = "";
      this.model_attr_dialogFormVisible = false;
      this.model_code_status = false;
      this.model_dialogStatus = "create";
      this.getDocList();
      this.model_dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    handleUpdateModelObj() {
      this.model_code_status = true;
      this.model_dialogStatus = "update";
      this.getContentObject();
    },
    getContentObject() {
      getContentObject(this.modelCode).then((response) => {
        let msg = response.msg;
        this.temp.model_code = msg.model_code;
        this.temp.model_name = msg.model_name;
        this.temp.model_type = msg.model_type;
        this.temp.separate_table_days = msg.separate_table_days;
        this.temp.version_control = msg.version_control;
        this.temp.token_check = msg.token_check;
        this.temp.encode_type = msg.encode_type;
        this.temp.encodeByteSize = msg.encodeByteSize;
        this.temp.creation_date_column = msg.creation_date_column;
        this.temp.finish_date_column = msg.finish_date_column;
        this.temp.table_space = msg.table_space;
        this.getDocList();
        this.model_dialogFormVisible = true;
        setTimeout(() => {
          // this.listLoading = false;
        }, 1 * 100);
      });
    },
    handleDeleteObj() {
      if (!this.hasDoc) {
        alert("该模型存在文档对象，请先删除关联关系");
        return;
      }
      if (this.f_obj_id != "0") {
        alert("该模型存在索引对象，请先删除关联关系");
        return;
      }
      this.openDelConfirm().then(() => {
        deleteModelObj(this.modelCode).then((response) => {
          this.getSignalList();
          this.$notify({
            title: "Success",
            message: "Delete Successfully",
            type: "success",
            duration: 2000,
          });
        });
      })
    },
    openDelConfirm(){
      return this.$confirm(`是否确定删除？`,'提示',{
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      })
    },
    handleCreateModelObj_Index() {
      this.$refs.model_index_rel.getModelIndexList();
      this.$refs.model_index_rel.show();
    },
    handleCreateModelObj_Attr() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          if(!checkEnglish(this.temp.model_code)){
            alert("模型代码不能有中文");
            return ;
          }
          this.objModelType = this.temp.model_type;
          this.model_dialogFormVisible = false;
          this.model_attr_dialogStatus = "create";
          this.$refs.model_attr_ref.getAllAttrsTreeByModelCode(this.modelCode);
          this.$refs.model_attr_ref.show();
        }
      });
    },
        clickNode(data, node, obj) {
      //点击节点触发,不同层级的level事件不同
      //可对应界面变化，比如通过v-if控制模块显隐
      this.modelCode = data.id;
      this.hasDoc = data.leaf;
      this.objModelType = data.modelType + "";
      this.f_obj_id = data.F_DOC_ID;
      this.getAttrListByModelCode();
    },

    handleCreateModelObj_Part() {
      if (this.objModelType == "1") {
        alert("文档对象无法配置文档");
        return;
      }
      this.$refs.model_doc_rel.getAllDocList();
      this.$refs.model_doc_rel.show();
    },
  },
};
</script>
<style>
.el-header {
  background-color: #b3c0d1;
  color: #333;
  line-height: 60px;
}

.el-aside {
  color: #333;
}
.el-tree {
  height: 100%;
}
.transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
}
</style>
