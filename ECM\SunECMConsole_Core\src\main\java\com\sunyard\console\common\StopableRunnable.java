package com.sunyard.console.common;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * 这是一个长时间运行的线程抽象类(while(true))，该类在不断重复执行{@link Runnable#run()}方法，
 * 直到被调用{@link StopableRunnable#stop()}。 
 * <p>
 * 对于传入到构造器中的{@link Runnable}实现，必须遵循以下规则以正确实现{@link StopableRunnable#stop()}。
 * <ul>
 * <li>while(true)的循环在该类已经实现，所以传入的<tt>runnable</tt>值需要实现循环的逻辑即可</li>
 * <li>传入的<tt>runnable</tt>的实现对于{@link InterruptedException}的处理必须转换为{@link RuntimeException}抛出，不得处理。</li>
 * </ul>
 * 
 * <AUTHOR>
 * 
 */
public class StopableRunnable implements Runnable, Stopable {
	private volatile boolean run = true;
	private Thread work;
	private  final static  Logger log = LoggerFactory.getLogger(StopableRunnable.class);
	private Runnable runnable;
	private long sleepTime;
	private TimeUnit unit;
	/**
	 * 构造器，指定循环睡眠时间。
	 * @param runnable
	 * @param sleepTime
	 * @param unit
	 */
	public StopableRunnable(Runnable runnable, long sleepTime, TimeUnit unit) {
		this.runnable = runnable;
		this.sleepTime = sleepTime;
		this.unit = unit;
	}

	public final void stop() {
		run = false;
		work.interrupt();
	}

	public final void run() {
		work = Thread.currentThread();
		while (isRun()) {
			try {
				runnable.run();
			} catch (Throwable e) {
				log.info("Process get a uncatched exception, stop .", e);
				stop();
			}
			try {
				unit.sleep(sleepTime);
			} catch (InterruptedException e) {
			}
		}
		log.info("stoped");
		work = null;
		clearInterrupt();
	}

	/**
	 * 清除interrupted标示。
	 */
	private void clearInterrupt() {
		Thread.interrupted();
	}

	public final boolean isRun() {
		return run;
	}
}
