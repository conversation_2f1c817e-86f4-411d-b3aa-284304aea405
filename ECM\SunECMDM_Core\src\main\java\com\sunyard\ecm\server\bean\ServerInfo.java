package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * 机构号、项类型、服务器组名对应关系表
 * 
 * <AUTHOR>
 * 
 */
@XStreamAlias("ServerInfo")
public class ServerInfo {
	@Override
	public String toString() {
		return "ServerInfo [ip=" + ip + ", socketPort=" + socketPort
				+ ", httpPort=" + httpPort + ", dmsName=" + dmsName + "]";
	}

	@XStreamAsAttribute
	private String ip;
	@XStreamAsAttribute
	private String socketPort;
	@XStreamAsAttribute
	private String httpPort;
	@XStreamAsAttribute
	private String dmsName;

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getSocketPort() {
		return socketPort;
	}

	public void setSocketPort(String socketPort) {
		this.socketPort = socketPort;
	}

	public String getHttpPort() {
		return httpPort;
	}

	public void setHttpPort(String httpPort) {
		this.httpPort = httpPort;
	}

	public String getDmsName() {
		return dmsName;
	}

	public void setDmsName(String dmsName) {
		this.dmsName = dmsName;
	}
}