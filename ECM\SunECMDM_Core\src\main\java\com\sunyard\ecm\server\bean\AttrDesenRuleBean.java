package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * <p>
 * Title: 属性脱敏规则bean
 * </p>
 * <p>
 * Description: 属性脱敏规则
 * </p>
 * <p>
 * Copyright: Copyright (c) 2022
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@XStreamAlias("AttrDesenRuleBean")
public class AttrDesenRuleBean {
	private String attribute_code;//属性代码
	private String attribute_name;//属性名称
	private String attribute_desenrule;//脱敏规则ID
	public String getAttribute_code() {
		return attribute_code;
	}
	public void setAttribute_code(String attribute_code) {
		this.attribute_code = attribute_code;
	}
	public String getAttribute_name() {
		return attribute_name;
	}
	public void setAttribute_name(String attribute_name) {
		this.attribute_name = attribute_name;
	}
	public String getAttribute_desenrule() {
		return attribute_desenrule;
	}
	public void setAttribute_desenrule(String attribute_desenrule) {
		this.attribute_desenrule = attribute_desenrule;
	}
}
