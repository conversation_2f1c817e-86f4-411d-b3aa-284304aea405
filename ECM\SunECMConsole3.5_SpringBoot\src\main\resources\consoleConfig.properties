#令牌有效时间	单位分
token_able_time =30
#SunEcmDM的工程名称
server_DM_Name =SunECMDM
#UnityAccess的工程名称
server_UA_Name =UnityAccess
#eclient删除批次的时候，设为false，需要2个用户同意才可以删除，否则，表示删除只需要一个用户同意就可以
one_user_can_deleteBatch=true
#SunScan页面
otherConsoleUrl=http://127.0.0.1:8080/sunscan/aps/sunscan.jsp
#offlineLine页面
offLineConsoleUrl=http://127.0.0.1:8080/sunscan/aps/sunscan.jsp
#硬件信息获取频率，单位为秒
monitorTime=60
#最大下发线程数
maxIssueThreadSize=100
#用户密码过期最长时间（单位是天），即超过n天用户必须修改密码--如果为0表示不需要校验
logout_max_day=0
#用户密码未修改时间提醒（单位是天），即提前多少天提醒用户密码过期,--如果为0表示不需要校验
logout_remind_day=0
#海峡银行单点登录（SSO）配置参数，为true为海峡银行版本
haixiaSSO=false
#【用户登录的失败和用户密码修改失败】的次数（单位是次）
loginErrorCount=3
#【用户登录失败和重置密码】 重试n次间隔时间(单位为分钟，默认5分钟)
loginErrorInterval=1
#用户登录session的失效时间，单位为分钟）
maxLoginSessionInterval=30
#监控地址
monitAddress=127.0.0.1:8081
#分表模式(0为默认自动分表，1为手动分表)
separate_table_type=0
#session类型，1为httpsession，2为redis
sessionType=1
#此处为http端口
es_sunecmdmip=127.0.0.1:8081
#是否开启dubbo
dubboIsOn=false
