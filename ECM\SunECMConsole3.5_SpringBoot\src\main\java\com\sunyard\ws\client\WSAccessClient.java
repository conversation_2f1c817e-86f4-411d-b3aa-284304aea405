
package com.sunyard.ws.client;

import com.sunyard.console.common.config.ReadConfig;
import com.sunyard.console.common.util.SpringUtil;
import com.sunyard.ws.internalapi.SunEcmAccess;
import com.sunyard.ws.utils.WsFactory;


public class WSAccessClient {
    private WsFactory<SunEcmAccess> factory = new WsFactory();

    public WSAccessClient() {
    }

    public SunEcmAccess getAccessClient(String url, long timeout) {
        String[] str = url.split("/");
        String ip = str[2].split(":")[0];
        String httpport = str[2].split(":")[1];
        String projectName = str[3];
        String dubboIsOn = ReadConfig.getConsoleConfigBean().getDubboIsOn();
        SunEcmAccess wsInterface;
        if ("true".equals(dubboIsOn)) {
            wsInterface = (SunEcmAccess) SpringUtil.getSpringBean("SunEcmDubboDm_"+projectName+ip+httpport);
        } else {
            wsInterface = this.factory.getWsInterface(SunEcmAccess.class, url, timeout);
        }
        return wsInterface;
    }

}
