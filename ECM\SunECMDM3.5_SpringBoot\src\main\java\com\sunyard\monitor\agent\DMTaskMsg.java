package com.sunyard.monitor.agent;

import com.sunyard.ecm.server.bean.LifeCycleStrategyBean;
import com.sunyard.ecm.server.cache.LazySingleton;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: 获取DM任务信息代理</p>
 * <p>Description: 获取DM任务信息供监控服务端调用</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class DMTaskMsg {
	
	
//	public static void main(String[] args) {
//		Map<String, LifeCycleStrategyBean> map = LazySingleton.getInstance().lifeCycleStrategy.getLifeMap();
//		List<LifeCycleStrategyBean> list = new ArrayList<LifeCycleStrategyBean>();
//		
//		for(String m:map.keySet()){
//			list.add(map.get(m));
//			
//		}
//		for(LifeCycleStrategyBean l:list){
//			System.out.println(l.toString());
//		}
//	}
	/**
	 * 获取服务器任务信息
	 * @return 任务信息xml文件
	 */
	public static String getDMTaskMsg(){
		Map<String, LifeCycleStrategyBean> map = LazySingleton.getInstance().lifeCycleStrategy.getLifeMap();
		List<LifeCycleStrategyBean> list = new ArrayList<LifeCycleStrategyBean>();
		if(map !=null && map.size()>0){
			for(String m:map.keySet()){
				list.add(map.get(m));
				
			}
		}else{
			return null;
		}
		
		StringBuffer xml = new StringBuffer("<?xml version='1.0' encoding='UTF-8' ?><root>");
		
		for(LifeCycleStrategyBean l:list){
			xml.append("<task>");
			xml.append("<task_name>").append(l.getTask_name()).append("</task_name>");
			xml.append("<task_state>").append(l.getTask_state()).append("</task_state>");
			xml.append("<begin_time>").append(l.getBegin_time()).append("</begin_time>");
			xml.append("<end_time>").append(l.getEnd_time()).append("</end_time>");
			xml.append("</task>");
		}
		xml.append("</root>");
		
		return xml.toString();
	}
	
}