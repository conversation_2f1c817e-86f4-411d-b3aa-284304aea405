import request from '@/utils/request'
import EndUrl from './publicUrl.js'
export function getNearLineFailListAction(data) {
    var fail_time=''
    if(data.fail_time!=''){
        fail_time=data.fail_time+'T00:00:00'
    }
    const newdata={'start': data.start,'limit': data.limit,'content_id':data.content_id,'model_code':data.model_code,'fail_time':fail_time}
    return request({
      url: '/nearLineFailManage/getNearLineFailList'+EndUrl.EndUrl,
      method:'post',
      params: { data: newdata } 
    })
}
export function reNearLineFaileBatchAction(data) {
    const newdata={'reNearLineMsg': data}
    return request({
      url: '/nearLineFailManage/reNearLineFaileBatch'+EndUrl.EndUrl,
      method:'post',
      params: { data: newdata } 
    })
}