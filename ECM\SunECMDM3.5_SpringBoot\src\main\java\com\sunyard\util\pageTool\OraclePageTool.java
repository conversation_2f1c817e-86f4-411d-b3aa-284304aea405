package com.sunyard.util.pageTool;


/**
 * <p>Title: oracle数据库查询分页</p>
 * <p>Description: oracle数据库查询分页</p>
 * <p>Copyright: Copyright (c) 2010</p>
 * <p>Company: sunyard</p>
 * <p>20120418新增了对获取sql的前多少条方法的实现</p>
 * <AUTHOR>
 * @version 1.0
 */
public class OraclePageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
		int rownum = start + limit - 1;
		return "select * from (select page_table.*, rownum  row_num"
				+ " from (" + sql + ") page_table) where row_num between "
				+ start + " and " + rownum;
	}

	public String getTopSql(String sql , int top){
		return "select * from (" + sql + ") where ROWNUM <=" + top;
	}

	public String getRandSql(String sql, int top) {
		return "select * from (" + sql + " order by dbms_random.value ) where ROWNUM <=" + top;
	}
	public String getMaxVersionAndGroupInDMDB() {
		return "getMaxVersionAndGroupInDMDB";
	}


	public String getDistinctRandSql(String sql, int top) {
		return getRandSql(sql,top);
	}
}