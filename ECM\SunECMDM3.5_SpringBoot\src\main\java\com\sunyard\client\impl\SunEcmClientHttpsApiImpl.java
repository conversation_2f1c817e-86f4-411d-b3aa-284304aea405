package com.sunyard.client.impl;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import com.sunyard.ecm.server.bean.MigrateBatchBean;

import com.sunyard.util.TransOptionKey;
import com.sunyard.ws.client.WSConsoleClient;
import org.apache.http.client.ClientProtocolException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.client.bean.ClientHeightQuery;
import com.sunyard.client.bean.HttpConnType;
import com.sunyard.client.conn.HttpsConn;
import com.sunyard.es.util.EsBean;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.util.CodeUtil;
import com.sunyard.util.OptionKey;
import com.sunyard.ws.internalapi.SunEcmConsole;
import com.sunyard.ws.utils.XMLUtil;

public class SunEcmClientHttpsApiImpl extends AbstractSunECMClientApi{
	private final static  Logger log = LoggerFactory.getLogger(SunEcmClientHttpsApiImpl.class);
	protected HttpsConn conn;
	private String ip;
	private int httpsPort;
	private int connTimeOut = 30000;
	private int reqTimeOut = 30000;
	private String serverName = null;
	private String splitSym = TransOptionKey.SPLITSYM;
	

	public SunEcmClientHttpsApiImpl(String ip, int httpsPort, String serverName) {
		this.ip = ip;
		this.httpsPort = httpsPort;
		this.serverName = serverName;
	}

	/**
	 * 建立HTTP连接
	 * 
	 * @return 成功/失败
	 */
	protected boolean connectToHost(String type) throws SunECMException {
		log.info( "--connectToHost-->建立HTTP连接" + type);
		int connectretrytimes = 5;
		// 重复连接CONNECTRETRYTIMES
		for (int i = 0; i < connectretrytimes; i++) {
			if (newHost(type)) {
				return true;
			}
			// 等待200毫秒再下一次连接
			try {
				Thread.sleep(200);
			} catch (InterruptedException e) {
				log.debug(
						"--SunEcmClientHttpsApiImpl-->connectToHost-->当前连接次数："
								+ (i + 1));
			}
			if (i == connectretrytimes - 1) {
				log.warn(
						"--SunEcmClientHttpsApiImpl-->connectToHost-->HTTP连接主机："
								+ ip + " 端口:" + httpsPort + "失败");
				throw new SunECMException(
						SunECMExceptionStatus.CLIENT_CONNECT_TO_SERVER_ERROR,
						"--SunEcmClientHttpsApiImpl-->connectToHost-->HTTP连接主机："
								+ ip + " 端口:" + httpsPort + "失败");
			}

		}
		return false;
	}

	/**
	 * 建立连接
	 * 
	 * @return
	 */
	private boolean newHost(String type) {
		log.info( "--newHost-->新建连接");
		try {
			String url = "";
			if (type.equals(HttpConnType.MSG)) {
				url = createMsgURL(ip, httpsPort);
			} else {
				url = createFileURL(ip, httpsPort);
			}
			log.debug( "--newHost-->HTTPs连接地址：[" + url + "]");
			this.conn = new HttpsConn(url, connTimeOut, reqTimeOut);
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	public String checkIn(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--checkIn-->检入clientBatchBean");
		connectToHost(HttpConnType.MSG);
		String result = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = "OPTION=" + OptionKey.CHECKIN + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.info( "--checkIn-->检入发送的消息：" + sendMsg);
			String msg = conn.receiveMsg();
			log.info( "--checkIn-->检入返回的消息：" + msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = TransOptionKey.SUCCESS + splitSym;
				} else {
					result = TransOptionKey.FAIL;
				}
			} else {
				log.warn( "检入时发生异常...异常代码：" + msg);
				throw new SunECMException("检入时发生异常...异常代码：" + msg);

			}
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--checkIn(over)-->检入" + result);
		return result;
	}

	public String checkOut(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--checkOut-->检出 clientBatchBean");
		connectToHost(HttpConnType.MSG);
		String result = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = "OPTION=" + OptionKey.CHECKOUT + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug( "--checkOut-->检出发送的消息：" + sendMsg);
			String msg = conn.receiveMsg();
			log.debug( "--checkOut-->检出返回的消息：" + msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = TransOptionKey.SUCCESS + splitSym + strArray[2];
				} else {
					result = TransOptionKey.FAIL + splitSym + strArray[2];
				}
			} else {
				throw new SunECMException("检出时发生异常...异常代码：" + msg);
			}
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--checkOut-->检出(over)-->result:" + result);
		return result;
	}

	public String createContentID(ClientBatchBean clientBatchBean, String groupName)
			throws SunECMException, IOException {
		log.info("--createID-->申请批次号");
		connectToHost(HttpConnType.MSG);
		String result = "";
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = "OPTION=" + OptionKey.CREATEID + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",GROUPNAME=" + groupName;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			String msg = conn.receiveMsg();
			log.debug("--createContentID-->申请批次号时返回的信息：" + msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = msg;
				} else {
					result = TransOptionKey.FAIL;
				}
			} else {
				log.warn("--createContentID-->申请批次号时发生异常...异常代码：" + msg);
				throw new SunECMException("--createContentID-->申请批次号时发生异常...异常代码：" + msg);

			}
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info("--createContentID-->申请批次号-->result:" + result);
		return result;
	}
	
	public String delete(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--delete-->删除 clientBatchBean");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = "OPTION=" + OptionKey.DEL + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
		    sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug( "--delete-->删除时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug( "--delete-->删除时返回的消息：" + resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--delete(over)");
		return resultStr;
	}

	public String getAllModelMsg_Client() throws Exception {
		log.info( "--getAllModelMsg_Client-->获取内容模型列表信息");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		try {
			String sendMsg = "OPTION=" + OptionKey.ALLMODELMSG;
			conn.sendMsg(sendMsg);
			log.debug(
					"--getAllModelMsg_Client-->获取内容模型列表信息时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug(
					"--getAllModelMsg_Client-->获取内容模型列表信息时返回的消息：" + resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--getAllModelMsg_Client(over)");
		return resultStr;
	}

	public String getContentServerInfo_Client() throws Exception {
		log.info( "--getContentServerInfo_Client-->获取所有服务器信息");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		try {
			String sendMsg = "OPTION=" + OptionKey.GET_NODE;
			conn.sendMsg(sendMsg);
			log.debug(
					"--getContentServerInfo_Client-->获取所有服务器时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug(
					"--getContentServerInfo_Client-->获取所有服务器时返回的消息："
							+ resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info(
				"--getContentServerInfo_Client-->获取所有服务器信息(over)");
		return resultStr;
	}

	public String getModelTemplate_Client(String[] modeCodes) throws Exception {
		log.info( "--getModelTemplate_Client-->获取内容模型模版");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		try {
			StringBuffer modeNamesStr = new StringBuffer();
			for (String modeCode : modeCodes) {
				modeNamesStr.append(modeCode).append(
						TransOptionKey.MODESPLITSYM);
			}
			String sendMsg = "OPTION=" + OptionKey.METATEMPLATE + ",MODENAMES="
					+ modeNamesStr.toString();
			conn.sendMsg(sendMsg);
			log.debug(
					"--getModelTemplate_Client-->获取内容模型模版时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug(
					"--getModelTemplate_Client-->获取内容模型模版时返回的消息：" + resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--getModelTemplate_Client-->获取内容模型模版(over)");
		return resultStr;
	}

	public String getPermissions_Client(String userName, String passWord)
			throws Exception {
		log.info( "--getPermissions_Client-->获得内容模型权限获取 username:"
				+ userName);
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		try {
			String sendMsg = "OPTION=" + OptionKey.PERMISSION + ",USERNAME="
					+ userName + ",PASSWORD=" + CodeUtil.encode(passWord);
			conn.sendMsg(sendMsg);
			log.debug( "--getPermissions_Client-->获得内容模型权限时发送的消息："
					+ sendMsg);
			resultStr = conn.receiveMsg();
			log.debug( "--getPermissions_Client-->获得内容模型权限时返回的消息："
					+ resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--getPermissions_Client-->获得内容模型权限获取(over)");
		return resultStr;
	}

	public String getToken(String ipAddress, String tokenCheckValue,
			String userName, String operationCode) throws Exception {
		log.info( "--getToken-->获得令牌");
		String token = TransOptionKey.FAIL;
		WSConsoleClient consoleClient = new WSConsoleClient();
		SunEcmConsole console = consoleClient.getEcmConsoleClient(ipAddress
				+ "/webservices/WsInterface", 300000);
		token = console.getToken(tokenCheckValue, userName, operationCode);
		log.info( "--getToken-->获得令牌 token:" + token);
		return token;
	}

	public String heightQuery(ClientHeightQuery heightQuery, String dmsName)
			throws Exception {
		log.info( "--heightQuery-->高级查询");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(heightQuery);
		try {
			log.debug(XMLUtil.bean2XML(heightQuery));
			String sendMsg = "OPTION=" + OptionKey.HEIGHT_QUERY + ",XML="
					+ XMLUtil.bean2XML(heightQuery) + ",DMSNAME=" + dmsName;
			sendMsg=createHeightQuerySendMsgbyToken(heightQuery, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug( "--heightQuery-->高级查询时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug( "--heightQuery-->高级查询时返回的消息：" + resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--heightQuery-->高级查询(over)");
		return resultStr;
	}

	public String inquireDMByGroup(String userName, String groupName)
			throws Exception {
		log.info( "--inquireDMByGroup-->向统一接入问询内容存储服务器的地址");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		try {
			String sendMsg = "OPTION=" + OptionKey.INQUIREDM + ",USERNAME="
					+ userName + ",DMSNAME=" + groupName;
			conn.sendMsg(sendMsg);
			log.debug( "--inquireDMByGroup-->查询内容存储服务器的地址时发送的消息："
					+ sendMsg);
			resultStr = conn.receiveMsg();
			log.debug( "--inquireDMByGroup-->查询内容存储服务器的地址时返回的消息："
					+ resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--inquireDMByGroup-->向统一接入问询内容存储服务器的地址(over)");
		return resultStr;
	}

	public String login(String userName, String password) throws Exception {
		password = CodeUtil.encode(password);
		log.info( "--login-->登录" + "userName" + userName + "password" + password);
		connectToHost(HttpConnType.MSG);
		String resultMsg = TransOptionKey.FAIL;
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append("OPTION=" + OptionKey.LOGIN + ",USERNAME=").append(
					userName).append(",PASSWORD=").append(password);
			conn.sendMsg(sbuf.toString());
			log.debug( "--login-->登录时发送的消息：" + sbuf.toString());
			String msg = conn.receiveMsg();
			log.debug( "--login-->登录时返回的消息：" + msg);

			// 服务端异常有可能返回NULL判断为空时提示客户端登陆失败
			if (msg == null || "null".equals(msg)) {
				log.warn( "--login-->登陆失败-->msg=" + msg);
				throw new SunECMException("--login-->登陆失败");
			}

			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					resultMsg = TransOptionKey.SUCCESS;
				} else {
					log.warn( "登陆失败");
//					throw new SunECMException("登陆失败");
				}
			}
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--login-->登录(over)-->resultMsg:" + resultMsg);
		return resultMsg;
	}

	public String logout(String userName) throws Exception {
		log.info( "--logout-->登出" + "userName" + userName);
		connectToHost(HttpConnType.MSG);
		String resultMsg = TransOptionKey.FAIL;
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append("OPTION=" + OptionKey.LOGOUT + ",USERNAME=").append(
					userName);
			conn.sendMsg(sbuf.toString());
			log.debug( "--logout-->登出时发送的消息：" + sbuf.toString());
			String msg = conn.receiveMsg();
			log.debug( "--logout-->登出时返回的消息：" + msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					resultMsg = TransOptionKey.SUCCESS;
				} else {
					log.warn( "登出异常");
					throw new SunECMException("登出异常");
				}
			}
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--logout-->登出(over)");
		return resultMsg;
	}

	public String operAnnotation(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--operAnnotation-->批注操作 clientBatchBean");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		try {
			String sendMsg = "OPTION=" + OptionKey.A_OR_U_ANNOTATION + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			conn.sendMsg(sendMsg);
			log.debug( "--operAnnotation-->批注操作时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug( "--operAnnotation-->批注操作时返回的消息："
					+ resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--operAnnotation-->批注操作(over)");
		return resultStr;
	}

	public String queryAnnotation(ClientBatchBean clientBatchBean,
			String dmsName) throws Exception {
		log.info( "--queryAnnotation-->批注查询");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		try {
			String sendMsg = "OPTION=" + OptionKey.ANNOTATION + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			conn.sendMsg(sendMsg);
			log.debug("--queryAnnotation-->批注查询时发送的消息：" + sendMsg);
			
			resultStr = conn.receiveMsg();
			log.debug( "--queryAnnotation-->批注查询时返回的消息："
					+ resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--queryAnnotation-->批注查询(over)");
		return resultStr;
	}

	public String queryBatch(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--queryBatch-->查询");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = "OPTION=" + OptionKey.QUERY + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);	
			conn.sendMsg(sendMsg);
			log.debug( "--queryBatch-->查询时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug( "--queryBatch-->查询时返回的消息：" + resultStr);
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--queryBatch-->查询(over)");
		return resultStr;
	}

	public String update(ClientBatchBean clientBatchBean, String dmsName,
			boolean isAutoCheck) throws Exception {
		log.info( "--update-->更新");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		if (!checkFileExist(clientBatchBean)) {
			return TransOptionKey.FAIL+TransOptionKey.SPLITSYM+SunECMExceptionStatus.FILE_NOT_FOUND;  //文件不存在
		}
		// 添加MD5码
		MD5Util.addBatchMD5Code(clientBatchBean);
		if (isAutoCheck) {
			//保存之前的password
			String OldPassWord=clientBatchBean.getPassWord();
			
			String checkOutMsg = checkOut(clientBatchBean, dmsName);
			if (checkOutMsg.split(splitSym)[0].equals(TransOptionKey.SUCCESS)) {
				clientBatchBean.setCheckToken(checkOutMsg.split(splitSym)[1]);
				log.debug( "--update-->批次["
						+ clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出成功...");
			} else {
				log.warn( "批次["
						+ clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出失败:" + checkOutMsg);
				throw new SunECMException("批次["
						+ clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出失败:" + checkOutMsg);
			}
			try {
				//重置之前的password
				clientBatchBean.setPassWord(OldPassWord);
				resultStr = update(clientBatchBean, dmsName);
				log.debug( "--update-->自动检入检出更新返回结果：" + resultStr);
			} finally {
				//重置之前的password
				clientBatchBean.setPassWord(OldPassWord);
				String checkInMsg = checkIn(clientBatchBean, dmsName);
				if (!checkInMsg.split(splitSym)[0]
						.equals(TransOptionKey.SUCCESS)) {
					log.warn( "批次["
							+ clientBatchBean.getIndex_Object().getContentID()
							+ "]自动检入失败:" + checkInMsg);
					throw new SunECMException("批次["
							+ clientBatchBean.getIndex_Object().getContentID()
							+ "]自动检入失败:" + checkInMsg);
				}
			}
			log.debug( "--update-->批次["
					+ clientBatchBean.getIndex_Object().getContentID()
					+ "]自动检入成功...");
		} else {
			resultStr = update(clientBatchBean, dmsName);
			log.debug( "--update-->手动检入检出更新返回结果：" + resultStr);
		}
		log.info( "--update-->更新(over)");
		return resultStr;
	}

	private String update(ClientBatchBean clientBatchBean, String dmsName)
			throws SunECMException, IOException {
		log.info( "--update-->更新");
		String resultStr = TransOptionKey.FAIL;
		log.debug( "--update-->更新时的报文:"
				+ XMLUtil.bean2XML(clientBatchBean));
		connectToHost(HttpConnType.MSG);
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg="OPTION=" + OptionKey.UPDATE + ",START=START,XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);	
			String msg = conn.receiveMsg();
			log.debug( "--update-->更新前建立连接时服务端返回：" + msg);
			String[] msgArray = msg.split(splitSym);
			if (msgArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (clientBatchBean.isBreakPoint()) {
					sendBreakClientBatchFileBean(clientBatchBean
							.getDocument_Objects(), clientBatchBean
							.getIndex_Object().getContentID(), clientBatchBean
							.getModelCode());
				} else {
					sendClientBatchFileBean(clientBatchBean
							.getDocument_Objects(), clientBatchBean
							.getIndex_Object().getContentID());
				}
				connectToHost(HttpConnType.MSG);
				conn.sendMsg("OPTION=" + OptionKey.UPDATE
						+ ",START=END,CONTENTID="
						+ clientBatchBean.getIndex_Object().getContentID()
						+ ",DMSNAME=" + dmsName);
				msg = conn.receiveMsg();
				log.debug( "--update-->更新文件上传完成后服务端返回：" + msg);
				String[] strArray = msg.split(splitSym);
				if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
					if (strArray[1].equals(TransOptionKey.SUCCESS)) {
						resultStr = TransOptionKey.SUCCESS;
					}
				} else {
					resultStr += TransOptionKey.SPLITSYM + msg;
				}
			} else {
				resultStr = msg;
			}
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "update-->更新(over)-->resultStr=" + resultStr);
		return resultStr;
	}

	public String upload(ClientBatchBean clientBatchBean, String dmsName)
			throws Exception {
		log.info( "--upload-->上传");
		if (!checkFileExist(clientBatchBean)) {
			return TransOptionKey.FAIL;
		}
		CodeUtil.encodeInBean(clientBatchBean);
		// 添加MD5码
		MD5Util.addBatchMD5Code(clientBatchBean);
		// 上传开始
		log.debug( "--upload-->上传开始");
		String reslutMsg = TransOptionKey.FAIL;
		StringBuffer uploadMsg = new StringBuffer();
		uploadMsg.append(
				"OPTION=" + OptionKey.UPLOAD + ",START=START,XML="
						+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME="
						+ dmsName);
		uploadMsg.append(createBatchSendMsgbyToken(clientBatchBean, uploadMsg.toString()));
		// HTTP传递信息
		connectToHost(HttpConnType.MSG);
		try {
			log.debug( "--upload-->上传文件前发送的信息："
					+ uploadMsg.toString());
			conn.sendMsg(uploadMsg.toString());
			String returnMsg = conn.receiveMsg();        
			log.debug( "--upload-->上传文件前返回的信息：" + returnMsg);
			String[] params = returnMsg.split(splitSym);
			String contentID = TransOptionKey.FAIL; // 内容ID初始化
			if (!params[0].equals(TransOptionKey.SERVER_OK)) {
				//此时生成批次号失败
				return  TransOptionKey.FAIL + TransOptionKey.SPLITSYM
				+ returnMsg;
				
			}
			
			if (params[0].equals(TransOptionKey.SERVER_OK)) {
				contentID = params[1];
				reslutMsg = TransOptionKey.FAIL + TransOptionKey.SPLITSYM
						+ contentID;
				clientBatchBean.getIndex_Object().setContentID(contentID);
				// 根据是否断点续传进行文件的上传
				long start = System.currentTimeMillis();
				if (clientBatchBean.isBreakPoint()) {
					sendBreakClientBatchFileBean(clientBatchBean
							.getDocument_Objects(), contentID, clientBatchBean
							.getModelCode());
				} else {
					sendClientBatchFileBean(clientBatchBean
							.getDocument_Objects(), contentID);
				}
				long end = System.currentTimeMillis();
				log.debug( "--upload-->上传文件耗时毫秒：" + (end - start));
				
				
			}
			connectToHost(HttpConnType.MSG);
			String sendMsg = "OPTION=" + OptionKey.UPLOAD
					+ ",START=END,CONTENTID="
					+ clientBatchBean.getIndex_Object().getContentID()
					+ ",DMSNAME=" + dmsName;
			conn.sendMsg(sendMsg);
			log.debug("--SunEcmClientHttpsApiImpl-->upload-->上传文件后发送的消息"
					+ sendMsg);
			returnMsg = conn.receiveMsg();
			log.debug(
					"--SunEcmClientHttpsApiImpl-->upload-->上传文件后返回的消息"
							+ returnMsg);
			String[] strArray = returnMsg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					reslutMsg = TransOptionKey.SUCCESS
							+ TransOptionKey.SPLITSYM + strArray[2];
				}
			} else {
				reslutMsg += TransOptionKey.SPLITSYM + returnMsg;
			}
		} finally {
			// conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.info( "--upload-->上传(over)-->reslutMsg=" + reslutMsg);
		return reslutMsg;
	}

	private void sendClientBatchFileBean(
			List<ClientBatchFileBean> ClientBatchFileBeans, String contentID)
			throws UnsupportedEncodingException, ClientProtocolException,
			IOException, SunECMException {
		log.debug( "--sendClientBatchFileBean-->上传文件"
				+ "contentID:" + contentID);
		for (ClientBatchFileBean batchFileBean : ClientBatchFileBeans) {
			List<ClientFileBean> fileBeans = batchFileBean.getFiles();
			for (ClientFileBean fileBean : fileBeans) {
				if (fileBean.getFileName() != null) {
					String url = createFileURL(ip, httpsPort);
					log.debug(
							"--SunEcmClientHttpsApiImpl-->sendClientBatchFileBean-->上传文件的地址："
									+ url);
					conn = new HttpsConn(url, connTimeOut, reqTimeOut);
					conn.sendFileData(fileBean.getFileName(), contentID, null);
				}
			}
		}
	}

	private void sendBreakClientBatchFileBean(
			List<ClientBatchFileBean> ClientBatchFileBeans, String contentID,
			String modeCode) throws UnsupportedEncodingException, IOException,
			SunECMException {
		log.debug( "--sendBreakClientBatchFileBean-->断点上传文件"
				+ "contentID:" + contentID);
		conn.sendMsg("OPTION=" + OptionKey.BREAK_POINT + ",CONTENTID="
				+ contentID + ",MODECODE=" + modeCode);
		String filesStr = conn.receiveMsg().split(splitSym)[1];
		log.debug(
				"--SunEcmClientHttpsApiImpl-->sendBreakClientBatchFileBean-->断点续传返回消息："
						+ filesStr);
		List<ClientFileBean> completeFiles = XMLUtil.xml2list(filesStr,
				ClientFileBean.class);
		// 统计所有的待上传文件
		List<ClientFileBean> totalFileBean = new ArrayList<ClientFileBean>();
		for (ClientBatchFileBean batchFileBean : ClientBatchFileBeans) {
			List<ClientFileBean> fileBeans = batchFileBean.getFiles();
			for (ClientFileBean fileBean : fileBeans) {
				if (fileBean.getFileName() != null) {
					totalFileBean.add(fileBean);
				}
			}
		}
		// 将不属于已上传的文件上传
		if (completeFiles.size() != 0) {
			List<ClientFileBean> breakFileBean=new ArrayList<ClientFileBean>();
			for (ClientFileBean fileBean : totalFileBean) {
				boolean flag=true;
				for (ClientFileBean complteFile : completeFiles) {
					if (fileBean.getFileName().equals(complteFile.getOrigName())) {
						flag=false;
						Long l=new File(fileBean.getFileName()).length();
						if(complteFile.getReceived()!=null&&!l.equals(complteFile.getReceived())){
							breakFileBean.add(fileBean);
						}
					}
				}
				if(flag){
					breakFileBean.add(fileBean);
				}
			}
			for (ClientFileBean fileBean : breakFileBean) {
				conn.sendFileData(fileBean.getFileName(), contentID,
						TransOptionKey.FILE_RECIEVE + TransOptionKey.SPLITSYM);
			}
		} else {
			sendClientBatchFileBean(ClientBatchFileBeans, contentID);
		}
	}

	/**
	 * 校验文件是否存在,并统计文件数
	 * 
	 * @param clientBatchBean
	 * @return
	 */
	private boolean checkFileExist(ClientBatchBean clientBatchBean) {
		log.info( "--checkFileExist-->校验文件是否存在,并统计文件数");
		boolean flag = false; // 是否自动统计批次下的文件数
		if (clientBatchBean.getIndex_Object().getAmount() == null
				|| clientBatchBean.getIndex_Object().getAmount().equals("")) {
			flag = true;
		}
		int totalFile = 0;
		// 校验文件是否存在
		List<ClientBatchFileBean> batchFileBeans = clientBatchBean
				.getDocument_Objects();
		for (ClientBatchFileBean clientBatchFileBean : batchFileBeans) {
			List<ClientFileBean> files = clientBatchFileBean.getFiles();
			for (ClientFileBean clientFileBean : files) {
				if (clientBatchFileBean.getFilePartName().equals(
						clientBatchBean.getModelCode())) {
					clientBatchBean.getIndex_Object().setCustomMap(
							clientFileBean.getOtherAtt());
				}
				if (clientFileBean.getFileName() != null) {
					File file = new File(clientFileBean.getFileName());
					if (!file.exists() || file.isDirectory()) {
						log.debug( "--checkFileExist-->"
								+ file.getPath() + "文件不存在");
						return false;
					} else {
						if (flag) {
							totalFile++;
						}
					}
				}
				if(clientFileBean.getProtocol()!=null&&flag){
					totalFile++;
				}
			}
		}
		if (flag) {
			clientBatchBean.getIndex_Object().setAmount(
					String.valueOf(totalFile));
		}
		return true;
	}

	private String createMsgURL(String ip, int httpsPort) {
		log.info( "--createMsgURL-->建立消息连接");
		String url = "https://" + ip + ":" + httpsPort + "/" + serverName
				+ "/servlet/httpReqDispacher";
		return url;
	}

	private String createFileURL(String ip, int httpsPort) {
		log.info( "--createFileURL-->建立文件连接");
		String url = "https://" + ip + ":" + httpsPort + "/" + serverName
				+ "/servlet/httpFileReceive";
		return url;
	}

	public String immedMigrate(MigrateBatchBean migrateBatchBean, String dmsName)
			throws Exception {
		log.info( "--immedMigrate-->立即迁移");
		connectToHost(HttpConnType.MSG);
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(migrateBatchBean);
		try {
			String sendMsg = "OPTION=" + OptionKey.IMMEDIATEMIGRATE + ",XML="
					+ XMLUtil.bean2XML(migrateBatchBean) + ",DMSNAME="
					+ dmsName;
			sendMsg=createImmigrateBatchSendMsgbyToken(migrateBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug(
					"--SunEcmClientHttpsApiImpl-->immedMigrate-->立即迁移发送的消息："
							+ sendMsg);
			resultStr = conn.receiveMsg();
			log.debug(
					"--SunEcmClientHttpsApiImpl-->immedMigrate-->立即迁移返回的消息："
							+ resultStr);
		} finally {
			conn.destroy();
		}
		log.info( "--immedMigrate-->立即迁移(over)");
		return resultStr;
	}

	public String queryNodeInfoByGroupIdAndInsNo(String itemType, String insNo)
			throws Exception {
		log.info( "--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息,"
				+ "modelCode" + itemType + "insNo" + insNo);
		connectToHost(HttpConnType.MSG);
		String resultStr =null;
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append(
					"OPTION=" + OptionKey.QUERY_NODEINFO_BY_GROUPID_AND_INSNO
							+ ",MODELCODE=").append(itemType).append(",INSNO=")
					.append(insNo);
			conn.sendMsg(sbuf.toString());
			log.debug( "--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息"
					+ sbuf.toString());
			resultStr = conn.receiveMsg();
			log.debug( "--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息" + resultStr);

		} finally {
			conn.destroy();
		}
		log.info( "--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息(over)-->resultStr:"+resultStr);

		return resultStr;
	}

	public String createUserToken(String username, String password) throws Exception {
		log.info("开始获取用户令牌");
		String result = TransOptionKey.FAIL;
		try {
			connectToHost(HttpConnType.MSG);
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.CREATEUSERTOKEN
					+ ",USERNAME=" + username + ",PASSWORD=" + CodeUtil.encode(password);
			conn.sendMsg(sendMsg);
			log.debug("--createUserToken-->获取用户令牌时发送的消息：" + sendMsg);
			String res = conn.receiveMsg();
			log.debug("--createUserToken-->获取用户令牌时返回的消息：" + res);

			// 服务端异常有可能返回NULL判断为空时提示客户端登陆失败
			if (res == null || "null".equals(res)) {
				log.warn("--login-->获取用户令牌失败-->msg=" + res);
				throw new SunECMException("--createUserToken-->获取用户令牌失败");
			}

			String[] strArray = res.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = TransOptionKey.SUCCESS + splitSym + strArray[2];
				} else {
					result = TransOptionKey.FAIL + splitSym + strArray[2];
				}
			} else {
				throw new SunECMException("获取用户令牌时发生异常...异常代码：" + res);
			}
		} finally {
			conn.destroy();
		}
		return result;
	}
	
	public String copyBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	public String immedCopyBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}

	public String otherRuquest(ClientBatchBean clientBatchBean, String requestName) throws Exception {

		log.debug("--第三方请求-->");
		connectToHost(HttpConnType.MSG);
		String resultStr = "";
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + "OTHERREQUEST" + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",REQUESTNAME=" + requestName;
			conn.sendMsg(sendMsg);
			log.debug("--otherRuquest-->第三方请求-发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--otherRuquest--第三方请求->返回的消息：" + resultStr);
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.debug("--第三方请求--->(over)");
		return resultStr;
	
	}
	
	public String uploadByStream(ClientBatchBean clientBatchBean, String groupName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public String queryESByECM_DOC_ID(String indexName, String ecm_doc_id) throws Exception {
		return null;
		
	}
	@Override
	public String queryESByBool(EsBean esBean) throws Exception {
		return null;
	}
	@Override
	public String uploadESTag(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
	@Override
	public String updateESTag(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		// TODO Auto-generated method stub
		return null;
	}
}