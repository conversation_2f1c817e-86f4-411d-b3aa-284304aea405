package com.sunyard.console.poolmanage.bean;

import java.util.List;

/**
 * <p>
 * Title: 内容对象配置信息bean
 * </p>
 * <p>
 * Description: 属性信息
 * </p>
 * <p>
 * Copyright: Copyright (c) 2017
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR> @version 1.0
 */

public class ContentObjBean {
	private String MODEL_CODE;//内容对象代码
	private List<ModelRelSDBConfigBean> modelRelSDBConfigBeanList;//参数配置信息
	private String MODEL_NAME;//内容对象名称
	
	public String getMODEL_CODE() {
		return MODEL_CODE;
	}
	public void setMODEL_CODE(String mODEL_CODE) {
		MODEL_CODE = mODEL_CODE;
	}
	public String getMODEL_NAME() {
		return MODEL_NAME;
	}
	public void setMODEL_NAME(String mODEL_NAME) {
		MODEL_NAME = mODEL_NAME;
	}
	public List<ModelRelSDBConfigBean> getModelRelSDBConfigBeanList() {
		return modelRelSDBConfigBeanList;
	}
	public void setModelRelSDBConfigBeanList(
			List<ModelRelSDBConfigBean> modelRelSDBConfigBeanList) {
		this.modelRelSDBConfigBeanList = modelRelSDBConfigBeanList;
	}


	
	}
	
	
	
	
	
	

