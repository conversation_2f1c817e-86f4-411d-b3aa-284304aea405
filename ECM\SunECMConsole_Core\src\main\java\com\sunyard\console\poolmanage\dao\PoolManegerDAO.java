package com.sunyard.console.poolmanage.dao;

import com.sequoiadb.datasource.DatasourceOptions;
import com.sequoiadb.net.ConfigOptions;
import com.sunyard.console.poolmanage.bean.PoolInfoBean;

import java.util.List;

public interface PoolManegerDAO {
	/**
	 * 分页查询连接池信息
	 * @param pool_id
	 * @param i
	 * @param limit
	 * @return
	 */
	public List<PoolInfoBean> getContentpoolList(int pool_id, int i, int limit);	
	/**
	 * 查询连接池信息
	 * @param pool_id
	 * @return
	 */
	public List<PoolInfoBean> getContentpoolList(int pool_id);
	
	/**
	 * 查询连接池信息(已激活)
	 * @param 
	 * @return
	 */
	public List<PoolInfoBean> getContentpoolList();
	/**
	 * 添加连接池
	 * @param bean
	 * @return
	 */
	public int addContentPool(PoolInfoBean bean);
	/**
	 * 修改连接池
	 * @param bean
	 * @return
	 */
	public int updateContentPool(PoolInfoBean bean);
	/**
	 * 禁用连接池
	 * @param sdb_connection_ids
	 * @return
	 */
	public boolean stopContentPool(String sdb_connection_ids);
	/**
	 * 激活连接池
	 * @param sdb_connection_ids
	 * @return
	 */
	public boolean startContentPool(String sdb_connection_ids);
	/**
	 * 测试连接池
	 * @param urls
	 * @param nwOpt
	 * @param dsOpt
	 * @return
	 */
	public boolean testContentPool(String urls, String password,
			String username, ConfigOptions nwOpt, DatasourceOptions dsOpt);

		

}
