package com.sunyard.console.safemanage.dao;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.configmanager.wsserviceutil.TokenUtil;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.safemanage.bean.NewTokenInfoBean;
import com.sunyard.console.safemanage.bean.StateTokenBean;
import com.sunyard.console.safemanage.bean.TokenInfoBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;


/**
 * <p>Title: 可申请动态令牌机器管理数据库操作接口类</p>
 * <p>Description: 定义机器管理中各类数据库操作的方法</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Repository("tmdao")
public class TokenManageDAOIMP implements TokenManageDAO {
	@Autowired
	private PageTool pageTool;
	private  final static Logger log = LoggerFactory.getLogger(TokenManageDAOIMP.class);
	public PageTool getPageTool() {
		return pageTool;
	}

	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}

	/**
	 * 增加新的令牌机器
	 * @param tib
	 */
	public boolean addServer(TokenInfoBean token) {
		log.info( "--addServer(start)-->token:" + token);
		StringBuffer sql = new StringBuffer();
		sql.append("INSERT INTO APPLY_TOKEN_SERVER (IP,SERVER_INFO");
		sql.append(")VALUES(");
		sql.append("'").append(token.getIp()).append("','").append(token.getServer_info()).append("')");
		log.debug( "--addServer-->sql:" + sql.toString());
		try{
			DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
		}catch(Exception e){
			log.error( "可申请动态令牌机器管理->增加新的令牌机器失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>addServer:" + e.toString());
		}
		log.info( "--addServer(over)");
		return true;
	}
	
	/**
	 * 删除令牌机器
	 * @param ip_s
	 * @return
	 */
	public boolean deleteServer(String ip_s) {
		log.info( "--deleteServer(start)-->ip_s:" + ip_s);
		StringBuffer ips = new StringBuffer();
		String[] ip_t=ip_s.split(",");
		for(int i = 0;i < ip_t.length;i++){
			if(i>0)
				ips.append(",");
			ips.append("'").append(ip_t[i]).append("'");
		}
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM APPLY_TOKEN_SERVER WHERE IP IN (").append(ips.toString()+")");
		log.debug( "--deleteServer-->sql:" + sql.toString());
		try{
			DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
		}catch(Exception e){
			log.error( "可申请动态令牌机器管理->删除令牌机器失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>deleteServer:" + e.toString());
		}
		log.info( "--deleteServer(over)");
		return true;
	}

	/**
	 * 获取token列表
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<TokenInfoBean> getServerList(int start, int limit) {
		log.info( "--getServerList(start)-->start:" + start + ";limit:" + limit);
		List<TokenInfoBean>	tokenInfos	= null;
		StringBuffer		sql			= new StringBuffer();
		sql.append("SELECT T1.IP,T1.SERVER_INFO FROM APPLY_TOKEN_SERVER T1 ORDER BY T1.IP");
		try{
			log.debug( "--getServerList-->sql:"+sql);
			tokenInfos = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(sql.toString(), start, limit), TokenInfoBean.class);
			sql = null;
		}catch(Exception e){
			log.error( "可申请动态令牌机器管理->获取令牌机器列表失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>getServerList:" + e.toString());
		}
		log.info( "--getServerList(over)-->tokenInfos:"+tokenInfos);
		return tokenInfos;
	}

	/**
	 * 获取所有的token列表
	 * @return
	 */
	public List<TokenInfoBean> getAllServerList() {
		log.info( "--getAllServerList(start)");
		List<TokenInfoBean>	tokenInfos	= null;
		StringBuffer		sql			= new StringBuffer();
		sql.append("SELECT T1.IP,T1.SERVER_INFO FROM APPLY_TOKEN_SERVER T1 ORDER BY T1.IP");
		try{
			log.debug( "--getAllServerList-->sql:"+sql);
			tokenInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), TokenInfoBean.class);
			sql = null;
		}catch(Exception e){
			log.error( "可申请动态令牌机器管理->获取所有令牌机器列表失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>getAllServerList:" + e.toString());
		}
		log.info( "--getAllServerList(over)-->tokenInfos:"+tokenInfos);
		return tokenInfos;
	}
	
	/**
	 * 检查ip是否存在
	 * @param ip
	 * @return
	 */
	public int checkServerIp(String ip) {
		log.info( "--checkServerIp(start)-->ip:" + ip);
		int count = 0;
		StringBuffer sql = new StringBuffer();
		
		sql.append("SELECT COUNT(*) ");
		sql.append("FROM APPLY_TOKEN_SERVER ");
		sql.append("WHERE IP ='").append(ip).append("'");
		log.debug( "--checkServerIp-->sql:" + sql.toString());
		try{
			count = DataBaseUtil.SUNECM.queryInt(sql.toString());
			sql = null;
		}catch(Exception e){
			log.error( "可申请动态令牌机器管理->检查ip是否存在失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>checkServerIp:" + e.toString());
		}
		log.info( "--checkServerIp(over)-->count:" + count);
		return count;
	}
	/**
	 * 获取机器列表
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<StateTokenBean> getTokenList(int start, int limit){
		log.info( "--getTokenList(start)-->start:" + start + ";limit:" + limit);
		List<StateTokenBean>	tokenInfos	= null;
		StringBuffer		sql			= new StringBuffer();
		sql.append("SELECT BINDING_IP,TOKEN_CODE FROM TOKEN_TABLE WHERE BINDING_IP <> '' OR BINDING_IP IS NOT NULL ORDER BY BINDING_IP");
		try{
			log.debug( "--SunECMConsole-->TokenManageDAOIMP-->getTokenList-->sql:" + sql.toString());
			tokenInfos = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(sql.toString(), start, limit), StateTokenBean.class);
			sql = null;
		}catch(Exception e){
			log.error( "静态令牌管理->获取静态令牌列表失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>getTokenList:" + e.toString());
		}
		log.info( "--getTokenList(over)-->tokenInfos:"+tokenInfos);
		return tokenInfos;
	}

	/**
	 * 获取所有机器列表
	 * @return
	 */
	public List<StateTokenBean> getAllTokenList(){
		log.info( "--getAllTokenList(start)");
		List<StateTokenBean>	tokenInfos	= null;
		StringBuffer		sql			= new StringBuffer();
		sql.append("SELECT BINDING_IP,TOKEN_CODE FROM TOKEN_TABLE ORDER BY BINDING_IP");
		try{
			log.debug( "--getAllTokenList-->sql:" + sql.toString());
			tokenInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), StateTokenBean.class);
			sql = null;
		}catch(Exception e){
			log.error( "静态令牌管理->获取静态令牌列表失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>getAllTokenList:" + e.toString());
		}
		log.info( "--getAllTokenList(over)-->tokenInfos:"+tokenInfos);
		return tokenInfos;
	}
	/**
	 * 申请静态令牌
	 * @return
	 */
	public boolean applicationToken(String ip){
		log.info( "--applicationToken(start)-->ip:" + ip);
		StringBuffer sql = new StringBuffer();
		String token_code = TokenUtil.getTokenCode();
		sql.append("INSERT INTO TOKEN_TABLE (TOKEN_CODE,BINDING_IP )VALUES( ");
		sql.append("'").append(token_code).append("','").append(ip).append("')");
		log.debug( "--applicationToken-->sql:" + sql.toString());
		try{
			DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
		}catch(Exception e){
			log.error( "令牌管理->申请静态令牌失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>applicationToken:" + e.toString());
		}
		log.info( "--applicationToken(over)-->ip:" + ip);
		return true;
	}
	/**
	 * 删除静态令牌
	 * @return
	 */
	public boolean delToken(String ip){
		log.info( "--delToken(start)-->ip:" + ip);
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM TOKEN_TABLE WHERE binding_ip ='").append(ip).append("'");
		log.debug( "--delToken-->sql:" + sql.toString());
		try{
			DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
		}catch(Exception e){
			log.error( "令牌管理->删除静态令牌失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>delToken:" + e.toString());
		}
		log.info( "--delToken(over)");
		return true;
	}
	/**
	 * 校验IP是否唯一
	 * @param value
	 * @return
	 */
	public int checkBindIp(String binding_ip) {
		log.info( "--checkBindIp(start)-->binding_ip:" + binding_ip);
		if (binding_ip == null || binding_ip.equals("")) {
			return 0;
		}
		int count = 0;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT count(*) FROM TOKEN_TABLE  where BINDING_IP ='")
		.append(binding_ip).append("'");
		log.debug( "--checkBindIp-->sql:" + sql.toString());
		try {
			count = DataBaseUtil.SUNECM.queryInt(sql.toString());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "令牌管理->校验路径名称是否存在失败:" + e.getMessage());
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>checkBindIp:"
							+ e.getMessage());
		}
		log.info( "--checkBindIp(over)-->count:" + count);
		return count;
	}

	public List<NewTokenInfoBean> getTokenListVue(int start, int limit) {
		log.info( "--getAllTokenListVue(start)");
		List<NewTokenInfoBean>	newTokenInfos	= null;
		List<NewTokenInfoBean>	newTokenInfos2	= null;
		StringBuffer		sql			= new StringBuffer();
		sql.append("SELECT T1.IP,T1.SERVER_INFO FROM APPLY_TOKEN_SERVER T1 ORDER BY T1.IP");

		StringBuffer		sql2			= new StringBuffer();
		sql2.append("SELECT BINDING_IP IP,SERVER_INFO,TOKEN_CODE FROM TOKEN_TABLE WHERE BINDING_IP <> '' OR BINDING_IP IS NOT NULL ORDER BY BINDING_IP");

		try{
			log.debug( "--getAllTokenListVue-->sql:"+sql+",sql2"+sql2);
			newTokenInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), NewTokenInfoBean.class);
			newTokenInfos2 = DataBaseUtil.SUNECM.queryBeanList(sql2.toString(), NewTokenInfoBean.class);
			for(NewTokenInfoBean new1:newTokenInfos){
				new1.setIsTrend("1");
			}
			for(NewTokenInfoBean new2:newTokenInfos2){
				new2.setIsTrend("0");
			}
			newTokenInfos.addAll(newTokenInfos2);
			Collections.sort(newTokenInfos, new Comparator<NewTokenInfoBean>() {
		            public int compare(NewTokenInfoBean o1, NewTokenInfoBean o2) {
		                return o1.getIp().compareTo(o2.getIp());
		            }
		    });
			sql = null;
			if(newTokenInfos.size()>limit){
				if(newTokenInfos.size()<=(start+limit)){
					newTokenInfos = newTokenInfos.subList(start,(newTokenInfos.size()));
				}else{
					newTokenInfos = newTokenInfos.subList(start, start+limit);
				}
			}
		}catch(Exception e){
			log.error( "令牌管理->获取所有令牌列表失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>getAllTokenListVue:" + e.toString());
		}
		log.info( "--getAllServerList(over)-->newTokenInfos:"+newTokenInfos);
		return newTokenInfos;	
	}

	public List<NewTokenInfoBean> getAllTokenListVue() {
		log.info( "--getAllTokenListVue(start)");
		List<NewTokenInfoBean>	newTokenInfos	= null;
		List<NewTokenInfoBean>	newTokenInfos2	= null;
		StringBuffer		sql			= new StringBuffer();
		sql.append("SELECT T1.IP,T1.SERVER_INFO FROM APPLY_TOKEN_SERVER T1 ORDER BY T1.IP");
		StringBuffer		sql2			= new StringBuffer();
		sql2.append("SELECT BINDING_IP IP,SERVER_INFO,TOKEN_CODE FROM TOKEN_TABLE WHERE BINDING_IP <> '' OR BINDING_IP IS NOT NULL ORDER BY BINDING_IP");
		
		try{
			log.debug( "--getAllServerList-->sql"+sql+",sql2"+sql2);
			newTokenInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), NewTokenInfoBean.class);
			newTokenInfos2 = DataBaseUtil.SUNECM.queryBeanList(sql2.toString(), NewTokenInfoBean.class);
			for(NewTokenInfoBean new1:newTokenInfos){
				new1.setIsTrend("1");
			}
			for(NewTokenInfoBean new2:newTokenInfos2){
				new2.setIsTrend("0");
			}
			newTokenInfos.addAll(newTokenInfos2);
			sql = null;
		}catch(Exception e){
			log.error( "令牌管理->获取所有令牌列表失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>getAllTokenListVue:" + e.toString());
		}
		log.info( "--getAllServerList(over)-->newTokenInfos:"+newTokenInfos);
		return newTokenInfos;	
	}
	
	/**
	 * 申请静态令牌
	 * @return
	 */
	public boolean applicationTokenVue(String ip,String server_info){
		log.info( "--applicationTokenVue(start)-->ip:" + ip +",server_info:" + server_info);
		StringBuffer sql = new StringBuffer();
		String token_code = TokenUtil.getTokenCode();
		sql.append("INSERT INTO TOKEN_TABLE (TOKEN_CODE,BINDING_IP,SERVER_INFO)VALUES( ");
		sql.append("'").append(token_code).append("','").append(ip).append("','").append(server_info).append("')");
		log.debug( "--applicationTokenVue-->sql:" + sql.toString());
		try{
			DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
		}catch(Exception e){
			log.error( "令牌管理->申请静态令牌失败->"+e.toString(),e);
			throw new DBRuntimeException(
					"TokenManageDAOIMP===>applicationToken:" + e.toString());
		}
		log.info( "--applicationTokenVue(over)-->ip:" + ip);
		return true;
	}
}
