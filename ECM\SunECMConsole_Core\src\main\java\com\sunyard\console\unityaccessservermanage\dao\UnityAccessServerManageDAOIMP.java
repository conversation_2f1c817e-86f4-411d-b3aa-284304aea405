package com.sunyard.console.unityaccessservermanage.dao;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerInfoBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * 统一接入服务器管理接口实现
 * 
 * <AUTHOR>
 * 
 */
@Repository("unityAccessServerManageDao")
public class UnityAccessServerManageDAOIMP
		implements
			UnityAccessServerManageDAO {
	@Autowired
	private PageTool pageTool;
	@Autowired
	private DataFieldMaxValueIncrementer unityAccessServerId;
	/**
	 * 日志对象
	 */
	private final static  Logger log = LoggerFactory.getLogger(UnityAccessServerManageDAOIMP.class);

	public PageTool getPageTool() {
		return pageTool;
	}

	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}

	public DataFieldMaxValueIncrementer getUnityAccessServerId() {
		return unityAccessServerId;
	}

	public void setUnityAccessServerId(
			DataFieldMaxValueIncrementer unityAccessServerId) {
		this.unityAccessServerId = unityAccessServerId;
	}
	
	public List<UnityAccessServerInfoBean> getUnityAccessServerList(
			int serverId, String serverName, int start, int limit) {
		log.info( "--getUnityAccessServerList(start)-->serverId:"+serverId+",serverName:"+serverName);
		List<UnityAccessServerInfoBean> beanList = null;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT     s.SERVER_ID,s.SERVER_NAME,s.SERVER_IP,  s.HTTP_PORT, s.SOCKET_PORT,s.REMARK,s.GROUP_ID,s.STATE ,s.trans_protocol,g.GROUP_NAME,g.IP,s.HTTPS_PORT ");
		sql.append(" FROM     UNITY_ACCESS_SERVER s ");
		sql.append(" left join UNITY_ACCESS_SERVER_GROUP g  on s.GROUP_ID=g.GROUP_ID ");
		List list=new ArrayList();
		
		if (serverId != 0) {
			sql.append(" AND  s.SERVER_ID=? ");
			list.add(serverId);
		}
		if (serverName != null && !"".equals(serverName)) {
			sql.append(" WHERE s.SERVER_NAME LIKE  '%'||?||'%' ");
			list.add(serverName);
		}
		try {
			log.debug( "--getUnityAccessServerList-->sql："+sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(
					sql.toString(), start, limit),
					UnityAccessServerInfoBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器管理->查询服务器失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>getUnityAccessServerList--hasPage:"
							+ e.toString());
		}
		log.info( "--getUnityAccessServerList(over)-->beanList:"+beanList);
		return beanList;
	}

	public List<UnityAccessServerInfoBean> getUnityAccessServerList(
			int serverId, String serverName) {
		log.info( "--getUnityAccessServerList(start)-->serverId:"+serverId+",serverName:"+serverName);
		List<UnityAccessServerInfoBean> beanList = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT     s.SERVER_ID,s.SERVER_NAME,s.SERVER_IP,  s.HTTP_PORT, s.SOCKET_PORT,s.REMARK,s.GROUP_ID,s.STATE ,s.trans_protocol,g.GROUP_NAME,g.IP,s.HTTPS_PORT ");
		sql.append(" FROM     UNITY_ACCESS_SERVER s ");
		sql
				.append(" left join UNITY_ACCESS_SERVER_GROUP g  on s.GROUP_ID=g.GROUP_ID WHERE 1=1 ");
		List list=new ArrayList();
		
		if (serverId != 0) {
			sql.append(" AND  s.SERVER_ID=? ");
			list.add(serverId);
		}
		if (serverName != null && !"".equals(serverName)) {
			sql.append(" AND s.SERVER_NAME LIKE  '%'||?||'%' ");
			list.add(serverName);
		}
		try {
			log.debug( "--getUnityAccessServerList-->sql："+sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					UnityAccessServerInfoBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器管理->查询服务器失败:" + e.toString());
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>getUnityAccessServerList--hasPage:"
							+ e.toString());
		}
		log.info( "--getUnityAccessServerList(over)-->beanList:"+beanList);
		return beanList;
	}
	//校验服务器IP地址和端口唯一性
	public int checkServerIPandPort(int serverId, String serverIp,
			int httpPort, int socketPort) {
		log.info( "--checkServerIPandPort(start)-->serverId:"+serverId+",serverIp:"+serverIp+",httpPort:"+httpPort+",socketPort:"+socketPort);
		if (serverIp == null || serverIp.equals("")) {
			return 0;
		}
		StringBuffer sql = new StringBuffer();
		int count = 0;
		List list=new ArrayList();
		sql.append("SELECT count(1)  FROM UNITY_ACCESS_SERVER ");
		sql.append(" WHERE SERVER_IP=? AND (HTTP_PORT=? OR SOCKET_PORT=?)");
		list.add(serverIp);
		list.add(httpPort);
		list.add(socketPort);
		if (serverId != 0) {
			sql.append(" AND SERVER_ID!=? ");
			list.add(serverId);
		}
		try {
			log.debug( "--checkServerIPandPort-->sql："+sql.toString());
			count = DataBaseUtil.SUNECM.queryInt(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器管理->校验ip和端口唯一性失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"getActiveNodes===>checkServerIPandPort:" + e.toString());
		}
		log.info( "--checkServerIPandPort(over)-->count:"+count);
		return count;
	}
	//新增统一接入服务器
	public int addUnityAccessServer(UnityAccessServerInfoBean bean) {
		if (bean == null) {
			return 0;
		}
		int server_id = unityAccessServerId.nextIntValue();
		StringBuffer sql = new StringBuffer();
		sql
				.append("INSERT INTO  UNITY_ACCESS_SERVER( SERVER_ID,SERVER_NAME, SERVER_IP, HTTP_PORT,SOCKET_PORT,HTTPS_PORT,REMARK,GROUP_ID,trans_protocol,STATE) VALUES("
						+ "?,?,?,?,?,?,?,?,?,?)");
		List list=new ArrayList();
		list.add(server_id);
		list.add(bean.getServer_name());
		list.add(bean.getServer_ip());
		list.add(bean.getHttp_port());
		list.add(bean.getSocket_port());
		list.add(bean.getHttps_port());
		list.add(bean.getRemark());
		list.add(bean.getGroup_id());
		list.add(bean.getTrans_protocol());
		list.add(bean.getState());

		try {
			log.debug( "--addUnityAccessServer-->sql："+sql.toString());
			int result = DataBaseUtil.SUNECM.update(sql.toString(),list.toArray());
			sql = null;
			if (result > 0) {
				return server_id;
			}
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器管理->新增服务器失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>addUnityAccessServer:"
							+ e.toString());
		}
		log.info( "--addUnityAccessServer(over)");
		return 0;
	}
	//修改统一接入服务器
	public int updateUnityAccessServer(UnityAccessServerInfoBean bean) {
		if (bean == null) {
			return 0;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE UNITY_ACCESS_SERVER SET SERVER_NAME='").append(
				bean.getServer_name());
		sql.append("', SERVER_IP='").append(bean.getServer_ip()).append(
				"',HTTP_PORT =").append(bean.getHttp_port());
		sql.append(",SOCKET_PORT=").append(bean.getSocket_port()).append(
				",REMARK='").append(bean.getRemark());
		sql.append("',GROUP_ID=").append(bean.getGroup_id()).append(",STATE=")
				.append(bean.getState()).append(",HTTPS_PORT=").append(bean.getHttps_port());
		sql.append(", trans_protocol='").append(bean.getTrans_protocol()).append("' ");
		sql.append(" WHERE SERVER_ID=").append(bean.getServer_id());
		try {
			log.debug( "--updateUnityAccessServer-->sql："+sql.toString());
			int result = DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
			if (result > 0) {
				return bean.getServer_id();
			}
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器管理->修改服务器失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>updateContentServer:"
							+ e.toString());
		}
		log.info( "--updateUnityAccessServer(over)");
		return 0;
	}
	//启用服务器
	public boolean startUnityAccessServer(String serverIds) {
		log.info( "--startUnityAccessServer(start)-->serverIds:"+serverIds);
		if (serverIds == null || "".equals(serverIds)) {
			log.debug( "--startUnityAccessServer-->serverIds is null");
			return false;
		}
		StringBuffer sql = new StringBuffer();
		String[] ids = serverIds.split(",");
		if (ids.length == 0) {
			return false;
		}
		sql
				.append("UPDATE UNITY_ACCESS_SERVER  s SET    s.STATE = 1 WHERE s.STATE=0 ");
		sql.append("AND (s.SERVER_ID =? ");
		for (int i = 1; i < ids.length; i++) {
			sql.append(" OR s.SERVER_ID =? ");
		}
		sql.append(")");
		try {
			log.debug( "--startUnityAccessServer-->sql："+sql.toString());
			int result = DataBaseUtil.SUNECM.update(sql.toString(),ids);
			sql = null;
			if (result > 0) {
				return true;
			}
		} catch (Exception e) {
			log.error( "统一接入服务器管理->启用服务器失败:" + e.toString(),e);
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>startUnityAccessServer:"
							+ e.toString());
		}
		log.info( "--startUnityAccessServer(over)");
		return false;
	}
	//禁用服务器
	public boolean stopUnityAccessServer(String serverIds) {
		log.info( "--stopUnityAccessServer(start)-->serverIds:"+serverIds);
		if (serverIds == null || "".equals(serverIds)) {
			log.debug( "--stopUnityAccessServer-->serverIds is null");
			return false;
		}
		StringBuffer sql = new StringBuffer();
		String[] ids = serverIds.split(",");
		if (ids.length == 0) {
			return false;
		}
		sql
				.append("UPDATE UNITY_ACCESS_SERVER  s SET    s.STATE = 0 WHERE s.STATE=1 ");
		sql.append("AND (s.SERVER_ID =? ");
		for (int i = 1; i < ids.length; i++) {
			sql.append(" OR s.SERVER_ID =? " );
		}
		sql.append(")");
		try {
			log.debug( "--stopUnityAccessServer-->sql："+sql.toString());
			int result = DataBaseUtil.SUNECM.update(sql.toString(),ids);
			sql = null;
			if (result > 0) {
				return true;
			}
		} catch (Exception e) {
			log.error( "统一接入服务器管理->禁用服务器失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>stopUnityAccessServer:"
							+ e.toString());
		}
		return false;
	}

	public List<UnityAccessServerInfoBean> getUnityAccessServerList(
			int serverId, boolean state) {
		log.info( "--getUnityAccessServerList(start)-->serverId:"+serverId+",state:"+state);
		List<UnityAccessServerInfoBean> beanList = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT    SERVER_ID,    SERVER_NAME,    SERVER_IP,    HTTP_PORT,    SOCKET_PORT,HTTPS_PORT,");
		sql
				.append(" REMARK,    GROUP_ID,  TRANS_PROTOCOL,  STATE FROM    UNITY_ACCESS_SERVER WHERE 1=1 ");

		if (serverId != 0) {
			sql.append(" AND SERVER_ID=").append(serverId);

		}
		if (state) {
			sql.append(" AND UNITY_ACCESS_SERVER.STATE=1");
		}
		try {
			log.debug( "--getUnityAccessServerList-->sql："+sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					UnityAccessServerInfoBean.class);
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器管理->获取服务器失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>getStartUnityAccessServerList:"
							+ e.toString());
		}
		log.info( "--getUnityAccessServerList(over)-->beanList:"+beanList);
		return beanList;
	}
	//检测该服务器名称是否存在
	public int checkServerName(int serverId, String serverName) {
		log.info( "--checkServerName(start)-->serverId:"+serverId+",serverName:"+serverName);
		StringBuffer sql = new StringBuffer();
		int count = 0;
		sql
				.append(
						"select count(1) from UNITY_ACCESS_SERVER u where u.SERVER_NAME='")
				.append(serverName).append("'");
		List list=new ArrayList();
		if (serverId != 0) {
			sql.append(" AND SERVER_ID!=?");
			list.add(serverId);
		}
		try {
			log.debug( "--checkServerName-->sql："+sql.toString());
			count = DataBaseUtil.SUNECM.queryInt(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "统一接入服务器管理->校验服务器名称唯一性失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"UnityAccessServerManageDAOIMP===>checkServerName:"
							+ e.getMessage());
		}
		log.info( "--checkServerName(over)-->count:"+count);
		return count;
	}
}
