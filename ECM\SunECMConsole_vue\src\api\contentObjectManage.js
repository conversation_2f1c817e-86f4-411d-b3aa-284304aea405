import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function addContentObjectAttrsRel(data) {
  const newdata=data;
  newdata.objMsg.attribute_ids = newdata.attributeCode
  newdata.objMsg.modelName = encodeURI(newdata.objMsg.model_name)
  return request({
    url: '/contentModelManage/addContentObjectAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      data: newdata.objMsg
    }
  })
}
export function updateContentObjectAttrsRel(data) {
  const newdata=data;
  newdata.objMsg.attribute_ids = newdata.attributeCode
  newdata.objMsg.modelName = encodeURI(newdata.objMsg.model_name)
  return request({
    url: '/contentModelManage/updateContentObjectAction'+EndUrl.EndUrl,

    method: 'post',
    params: {
      data: newdata.objMsg
    }
  })
}
export function updateContentObjectPartRel(data) {
  return request({
    url: '/contentModelManage/updateContentObjectPartAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      data: data
    }
  })
}
export function addContentObjectIndex(data) {
  return request({
    url: '/contentModelManage/addContentObjectIndexAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      data: data
    }

  })
}
export function delContentObjectIndex(data) {
  return request({
    url: '/contentModelManage/delContentObjectIndexAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      data: data
    }

  })
}
export function getAttrTressByModelCode(data) {
  return request({
    url: '/contentModelManage/getRelAttributesTreeAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'model_code': data }

  })
}
export function getAllAttrsTreeByModelCode(data) {
  return request({
    url: '/contentModelManage/getAllAttributesTreeAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'model_code': data }
  })
}
export function getContentObjOffDate(data) {
  return request({
    url: '/contentModelManage/getContentObjOffDateAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'model_code': data }

  })
}
export function getAttrListByModelCode(data) {
  data.show = '0'
  return request({
    url: '/contentModelManage/getAttributeByobjectAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: data }

  })
}

export function getModelIndexList(data) {
  return request({
    url: '/contentModelManage/getIndexByObjectAction'+EndUrl.EndUrl,
    method: 'post',
    // data: data
    params: { 'model_code': data }
  })
}
export function getContentObject(data) {
  return request({
    url: '/contentModelManage/getContentObjectAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'model_code': data }

  })
}
export function getContentObjectList() {
  return request({
    url: '/contentModelManage/getContentObjectListAction'+EndUrl.EndUrl,
    method: 'post'
  })
}
export function deleteModelObj(modelCode) {
  return request({
    url: 'contentModelManage/delContentObjectAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'model_code': modelCode }
  })
}

export function getAllDocList(modelCode) {
  return request({
    url: '/contentModelManage/getDocPartObjectListAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'model_code': modelCode }
  })
}                                                                                     