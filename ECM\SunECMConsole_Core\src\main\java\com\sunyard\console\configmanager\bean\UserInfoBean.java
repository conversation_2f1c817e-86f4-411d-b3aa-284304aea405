package com.sunyard.console.configmanager.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

import java.util.List;

/**
 * <p>
 * Title:xstream 用户权限信息bean
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("UserInfoBean")
public class UserInfoBean {
	@XStreamAsAttribute
	private String LOGIN_ID;// 登录名

	@XStreamAsAttribute
	private List<ModelPermissionBean> MODEL_PERMISS_LIST;// 操作权限

	public String getLogin_id() {
		return LOGIN_ID;
	}

	public void setLogin_id(String loginId) {
		LOGIN_ID = loginId;
	}

	public List<ModelPermissionBean> getModelPermissList() {
		return MODEL_PERMISS_LIST;
	}

	public void setModelPermissList(List<ModelPermissionBean> modelPermissList) {
		this.MODEL_PERMISS_LIST = modelPermissList;
	}

	public String toString(){
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append("LOGIN_ID:").append(LOGIN_ID);
		sBuilder.append(";MODEL_PERMISS_LIST:").append(MODEL_PERMISS_LIST);
		return sBuilder.toString();
	}
}
