package com.sunyard.console.safemanage.dao;

import com.sunyard.console.safemanage.bean.NewTokenInfoBean;
import com.sunyard.console.safemanage.bean.StateTokenBean;
import com.sunyard.console.safemanage.bean.TokenInfoBean;

import java.util.List;


/**
 * <p>Title: 可申请动态令牌机器管理数据库操作接口类</p>
 * <p>Description: 定义机器管理中各类数据库操作的方法</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public interface TokenManageDAO {
	/**
	 * 增加新的令牌机器
	 * @param token
	 */
	public boolean addServer(TokenInfoBean token);

	/**
	 * 删除令牌机器
	 * @param ip_s
	 * @return
	 */
	public boolean deleteServer(String ip_s);

	/**
	 * 获取机器列表
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<TokenInfoBean> getServerList(int start, int limit);

	/**
	 * 获取所有机器列表
	 * @return
	 */
	public List<TokenInfoBean> getAllServerList();
	
	/**
	 * 检查ip是否存在
	 * @param ip
	 * @return
	 */
	public int checkServerIp(String ip);
	
	/**
	 * 获取机器列表
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<StateTokenBean> getTokenList(int start, int limit);

	/**
	 * 获取所有机器列表
	 * @return
	 */
	public List<StateTokenBean> getAllTokenList();
	/**
	 * 申请静态令牌
	 * @return
	 */
	public boolean applicationToken(String ip);
	/**
	 * 删除静态令牌
	 * @return
	 */
	public boolean delToken(String ip);
	/**
	 * 校验IP是否唯一
	 * @param binding_ip
	 * @return
	 */
	public int checkBindIp(String binding_ip);

	public List<NewTokenInfoBean> getTokenListVue(int start, int limit);

	public List<NewTokenInfoBean> getAllTokenListVue();

	public boolean applicationTokenVue(String ip, String server_info);
}
