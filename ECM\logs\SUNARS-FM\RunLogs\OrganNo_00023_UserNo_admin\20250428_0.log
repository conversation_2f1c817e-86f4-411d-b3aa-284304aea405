2025-04-28 10:44:00.531 [OrganNo_00023_UserNo_admin] [2372210ecd0b381b/1f0dd374187c9888] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-28 10:44:00.607 [OrganNo_00023_UserNo_admin] [2372210ecd0b381b/15e1a82be8d4b2c9] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-28 10:44:00.607 [OrganNo_00023_UserNo_admin] [2372210ecd0b381b/15e1a82be8d4b2c9] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-28 10:44:00.636 [OrganNo_00023_UserNo_admin] [2372210ecd0b381b/15e1a82be8d4b2c9] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-28 10:44:00.637 [OrganNo_00023_UserNo_admin] [2372210ecd0b381b/15e1a82be8d4b2c9] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-28 10:44:00.638 [OrganNo_00023_UserNo_admin] [2372210ecd0b381b/15e1a82be8d4b2c9] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-28 10:44:00.664 [OrganNo_00023_UserNo_admin] [2372210ecd0b381b/15e1a82be8d4b2c9] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-28 10:44:00.733 [OrganNo_00023_UserNo_admin] [2372210ecd0b381b/1f0dd374187c9888] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-28 10:44:00.812 [OrganNo_00023_UserNo_admin] [1d2c59f2fefd50bc/4cd15774f1150909] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-28 10:44:00.830 [OrganNo_00023_UserNo_admin] [1d2c59f2fefd50bc/d6beb9dac38fce2c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-04-28 10:44:00.831 [OrganNo_00023_UserNo_admin] [1d2c59f2fefd50bc/d6beb9dac38fce2c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String)
2025-04-28 10:44:00.860 [OrganNo_00023_UserNo_admin] [1d2c59f2fefd50bc/d6beb9dac38fce2c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-04-28 10:44:00.861 [OrganNo_00023_UserNo_admin] [1d2c59f2fefd50bc/d6beb9dac38fce2c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-04-28 10:44:00.861 [OrganNo_00023_UserNo_admin] [1d2c59f2fefd50bc/d6beb9dac38fce2c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 15(Integer)
2025-04-28 10:44:00.890 [OrganNo_00023_UserNo_admin] [1d2c59f2fefd50bc/d6beb9dac38fce2c] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 4
2025-04-28 10:44:00.963 [OrganNo_00023_UserNo_admin] [1d2c59f2fefd50bc/4cd15774f1150909] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"46e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"15370-中国银行四川省分行支付清算部",
				"oganNo":"15370",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行支付清算部",
				"siteNo":"15370",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250424171527249104",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042400023001",
				"batchId":"20250424171527249104",
				"businessDate":"20250424",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400424",
				"endBusiDate":"20250424",
				"id":"44d886a7e957423c82cf873d9b842b53",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250424",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		],
		"totalCount":4,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-28 10:44:09.388 [OrganNo_00023_UserNo_admin] [c60598c6b79afd8c/b963a268aaf15481] [http-nio-9009-exec-53] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-28 10:44:09.393 [OrganNo_00023_UserNo_admin] [c60598c6b79afd8c/c5a3e086280ef071] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-28 10:44:09.394 [OrganNo_00023_UserNo_admin] [c60598c6b79afd8c/c5a3e086280ef071] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-28 10:44:09.423 [OrganNo_00023_UserNo_admin] [c60598c6b79afd8c/c5a3e086280ef071] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-28 10:44:09.423 [OrganNo_00023_UserNo_admin] [c60598c6b79afd8c/c5a3e086280ef071] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-28 10:44:09.423 [OrganNo_00023_UserNo_admin] [c60598c6b79afd8c/c5a3e086280ef071] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-28 10:44:09.451 [OrganNo_00023_UserNo_admin] [c60598c6b79afd8c/c5a3e086280ef071] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-28 10:44:09.521 [OrganNo_00023_UserNo_admin] [c60598c6b79afd8c/b963a268aaf15481] [http-nio-9009-exec-53] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-28 10:44:09.581 [OrganNo_00023_UserNo_admin] [09ab75cb391c8d3f/b332e01522893d90] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-04-28 10:44:09.591 [OrganNo_00023_UserNo_admin] [09ab75cb391c8d3f/02a82145ebafc099] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-04-28 10:44:09.592 [OrganNo_00023_UserNo_admin] [09ab75cb391c8d3f/02a82145ebafc099] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin(String)
2025-04-28 10:44:09.623 [OrganNo_00023_UserNo_admin] [09ab75cb391c8d3f/02a82145ebafc099] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-04-28 10:44:09.624 [OrganNo_00023_UserNo_admin] [09ab75cb391c8d3f/02a82145ebafc099] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-04-28 10:44:09.625 [OrganNo_00023_UserNo_admin] [09ab75cb391c8d3f/02a82145ebafc099] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin(String), 15(Integer)
2025-04-28 10:44:09.653 [OrganNo_00023_UserNo_admin] [09ab75cb391c8d3f/02a82145ebafc099] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 2
2025-04-28 10:44:09.722 [OrganNo_00023_UserNo_admin] [09ab75cb391c8d3f/b332e01522893d90] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025042400023001",
				"baleState":"1",
				"id":"5289ce87003d45fc8202a33e783ad8f2",
				"sendDate":"20250424",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
