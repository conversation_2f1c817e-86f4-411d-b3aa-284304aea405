package com.sunyard.console.common.database;

import com.sunyard.console.contentmodelmanage.bean.IndexInfoBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

@ConditionalOnProperty(prefix = "pageTool",name = "Type", havingValue = "postgrePageTool")
@Service("pageTool")
public class PostgreSQLPageTool implements PageTool {


    public String getPageSql(String sql, int start, int limit) {
        return sql+" limit "+limit+ " offset "+(start-1);
    }

    public String getTableSpaceName(String sql, String tableSpaceName) {
        return sql;
    }

    public String getOnlyOneSql(String sql,String tableName) {
        return sql +" limit 1 ";
    }

    @Override
    public String delIndexSql(IndexInfoBean bean) {
        return "DROP INDEX " + bean.getTable_name() + "_" + bean.getIndex_name();
    }


}
