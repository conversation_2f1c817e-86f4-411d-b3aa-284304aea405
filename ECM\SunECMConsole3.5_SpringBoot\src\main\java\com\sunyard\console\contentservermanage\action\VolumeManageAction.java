package com.sunyard.console.contentservermanage.action;


import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.configmanager.wsserviceutil.WsBeanInterface;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.sunyard.console.contentservermanage.bean.VolumeInfoBean;
import com.sunyard.console.contentservermanage.dao.ContentServerGroupManageDAO;
import com.sunyard.console.contentservermanage.dao.VolumeManageDAO;
import com.sunyard.console.monitor.statusManage.dao.StatusManageDao;
import com.sunyard.console.threadpoool.IssueUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>Title: 存储卷管理Action</p>
 * <p>Description: 处理卷信息管理中的跳转</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class VolumeManageAction extends BaseAction {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private  final static Logger log = LoggerFactory.getLogger(VolumeManageAction.class);
	@Autowired
	VolumeManageDAO vmdao;
	@Autowired
	WsBeanInterface wsclient;
	@Autowired
	ContentServerGroupManageDAO contentServerGroupManageDao;
	@Autowired
	StatusManageDao smdao;
	
	public StatusManageDao getSmdao() {
		return smdao;
	}

	public void setSmdao(StatusManageDao smdao) {
		this.smdao = smdao;
	}

//	public ContentServerGroupManageDAO getCsgmdao() {
//		return csgmdao;
//	}
//
//	public void setCsgmdao(ContentServerGroupManageDAO csgmdao) {
//		this.csgmdao = csgmdao;
//	}

	public VolumeManageDAO getVmdao() {
		return vmdao;
	}

	public void setVmdao(VolumeManageDAO vmdao) {
		this.vmdao = vmdao;
	}
	
	public WsBeanInterface getWsclient() {
		return wsclient;
	}

	public void setWsclient(WsBeanInterface wsclient) {
		this.wsclient = wsclient;
	}

	private String volume_id;			//卷id
	private String volume_name;			//卷名称
	private String root_path;			//存储标识
	private String volume_remark;		//备注
	private String volume_state;		//状态
	private String save_path;			//存储路径
	private String path_rule;			//目录规则
	private String dir_number;			//最大目录数
	private String path_number;			//目录层数
	private String server_group_id;		//所属服务器组编号
	private int start;
	private int limit;
	private String optionFlag;
	private String c_volume_id;
	

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	public String getC_volume_id() {
		return c_volume_id;
	}

	public void setC_volume_id(String c_volume_id) {
		this.c_volume_id = c_volume_id;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getVolume_id() {
		return volume_id;
	}

	public void setVolume_id(String volume_id) {
		this.volume_id = volume_id;
	}

	public String getVolume_name() {
		return volume_name;
	}

	public void setVolume_name(String volume_name) {
		this.volume_name = volume_name;
	}

	public String getRoot_path() {
		return root_path;
	}

	public void setRoot_path(String root_path) {
		this.root_path = root_path;
	}

	public String getVolume_remark() {
		return volume_remark;
	}

	public void setVolume_remark(String volume_remark) {
		this.volume_remark = volume_remark;
	}

	public String getVolume_state() {
		return volume_state;
	}

	public void setVolume_state(String volume_state) {
		this.volume_state = volume_state;
	}

	public String getSave_path() {
		return save_path;
	}

	public void setSave_path(String save_path) {
		this.save_path = save_path;
	}

	public String getPath_rule() {
		return path_rule;
	}

	public void setPath_rule(String path_rule) {
		this.path_rule = path_rule;
	}

	public String getDir_number() {
		return dir_number;
	}

	public void setDir_number(String dir_number) {
		this.dir_number = dir_number;
	}

	public String getPath_number() {
		return path_number;
	}

	public void setPath_number(String path_number) {
		this.path_number = path_number;
	}



	public String getServer_group_id() {
		return server_group_id;
	}

	public void setServer_group_id(String server_group_id) {
		this.server_group_id = server_group_id;
	}

	/**
	 * 查询指定服务器组下内容存储卷
	 *
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/volumeInfoSearchAction.action", method = RequestMethod.POST)
	public String volumeInfoSearch(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_group_id = (String) modelJson.getOrDefault("server_group_id", "");
		String volume_name = (String) modelJson.getOrDefault("volume_name", "");
		int start = modelJson.getInt("start");
		int limit = modelJson.getInt("limit");
		start = (start-1) * limit;
		try {
			volume_name = URLDecoder.decode(volume_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode volumeInfoSearch fields error, group_name=" + volume_name);
		}
		log.info("--volumeInfoSearch(start)-->server_group_id:" + server_group_id + ",volume_name[" + volume_name + "]");
		String jsonStr = null;
		JSONObject jsonResp=new JSONObject();
		JSONArray jsonArray=new JSONArray();
		Object obj=new VolumeInfoBean();
		try{
			if(!StringUtil.stringIsNull(volume_name)){
				volume_name=volume_name.trim();
			}
			List<VolumeInfoBean> volumeInfoList = vmdao.searchVolumeInfoList(volume_name,server_group_id, start+1, limit);
			List<VolumeInfoBean> volumeInfoAllList = vmdao.searchVolumeAllList(volume_name,server_group_id);
			if (volumeInfoList != null && volumeInfoList.size() > 0) {
				Iterator iter = volumeInfoList.iterator();
				JSONObject jsonObj = null;
				Map map = null;
				Set keySet = null;
				Iterator keyIter = null;
				Object key = null;
				while(iter.hasNext()){
					obj = (Object) iter.next();
					map = BeanUtils.describe(obj);
					if(log.isDebugEnabled()){
						log.debug( "==>>jsonStr："+map.toString());
					}
					
					keySet = map.keySet();
					keyIter = keySet.iterator();
					jsonObj = new JSONObject();
					while(keyIter.hasNext()){
						key = keyIter.next();
						jsonObj.put(key, map.get(key));
					}
					jsonArray.add(jsonObj);
				}	
			}
			jsonResp.put("totalProperty",volumeInfoAllList.size()+"");//总行数
			jsonResp.put("root", jsonArray);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonStr = jsonResp.toString();
//			jsonStr = new JSONUtil().createJsonDataByColl(volumeInfoList,volumeInfoAllList.size(),new VolumeInfoBean());
		}catch(Exception e){
		    jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取存储卷信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "存储卷管理->查询存储卷失败->"+e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--volumeInfoSearch(over)");
		return null;
	}
	
	/**
	 * 增加、修改卷
	 *
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/configVolumeAction.action", method = RequestMethod.POST)
	public String configVolume(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_group_id = (String) modelJson.getOrDefault("server_group_id", "");
		String dir_number = (String) modelJson.getOrDefault("dir_number", "");
		String path_number = (String) modelJson.getOrDefault("path_number", "");
		String path_rule = (String) modelJson.getOrDefault("path_rule", "");
		String root_path = (String) modelJson.getOrDefault("root_path", "");
		String save_path = (String) modelJson.getOrDefault("save_path", "");
		String volume_id = (String) modelJson.getOrDefault("volume_id", "");
		String volume_name = (String) modelJson.getOrDefault("volume_name", "");
		String volume_remark = (String) modelJson.getOrDefault("volume_remark", "");
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");
		String c_volume_id = (String) modelJson.getOrDefault("c_volume_id", "");
		log.info("--configVolume(start)-->server_group_id:" + server_group_id);
		try {
			volume_name = URLDecoder.decode(volume_name, "utf-8");
			volume_remark = URLDecoder.decode(volume_remark, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode VolumeManage fields error, volume_name=" + volume_name
					+ ", volume_remark=" + volume_remark+ ", save_path=" + save_path, e1);
		}
		VolumeInfoBean volume = new VolumeInfoBean();
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		volume.setDir_number(dir_number);
		volume.setPath_number(path_number);
		volume.setPath_rule(path_rule);
		volume.setRoot_path(root_path);
		volume.setSave_path(save_path);
		volume.setServer_group_id(server_group_id);
		volume.setVolume_id(volume_id);
		volume.setVolume_name(volume_name);
		volume.setVolume_remark(volume_remark);
		volume.setVolume_state("1");
		
		try{
			log.debug( "--configVolume-->optionFlag:" + optionFlag);
			if(optionFlag.equals("create1")){
				c_volume_id = vmdao.addVolume(volume);
				volume.setVolume_id(c_volume_id);
			}else if(optionFlag.equals("update1")){
				volume.setVolume_id(c_volume_id);
				vmdao.modifyVolume(volume);
			}
			//创建日志下发策略
			//ConsoleThreadPool.getThreadPool().submit(new Thread(new SendVolumeThread(volume,optionFlag)));
			IssueUtils.IssueInfoToDM(contentServerGroupManageDao.getContentServerByGroupId(volume.getServer_group_id()));
			jsonResp.put("success", true);
			jsonResp.put("message", "配置存储卷成功!!");
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();	
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "配置存储卷失败!!");
			jsonStr = jsonResp.toString();
			log.error( "存储卷管理->配置卷->"+e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--configVolume(over)-->server_group_id:" + server_group_id);
		return null;
	}

}

