package com.sunyard.console.tagmanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.DateUtil;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.common.util.TagStateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.sunyard.console.common.config.ReadConfig;
import com.sunyard.console.singletonManage.dao.SingletonManageDAOIMP;
import com.sunyard.console.tagmanage.bean.TagInfoBean;
import com.sunyard.console.tagmanage.bean.TagRelAttTypeDefineBean;
import com.sunyard.console.tagmanage.bean.TagRelAttributeBean;
import com.sunyard.console.tagmanage.bean.TagRelModelBean;
import com.sunyard.console.tagmanage.dao.TagManageDAO;
import com.sunyard.console.threadpoool.IssueUtils;
import com.sunyard.util.StringUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
@Controller
public class TagManageAction extends BaseAction {
	@Autowired
	TagManageDAO tagManageDao;

	/**
	 * 日志对象
	 */
	private final static Logger log = LoggerFactory.getLogger(TagManageAction.class);
	
	public TagManageDAO getTagManageDao() {
		return tagManageDao;
	}

	public void setTagManageDao(TagManageDAO tagManageDao) {
		this.tagManageDao = tagManageDao;
	}

	/**
	 * 得到标签信息
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/getTagsAction.action", method = RequestMethod.POST)
	public String getTags(String data){
		JSONObject modelJson = JSONObject.fromObject(data);
		String tag_name = (String) modelJson.getOrDefault("tag_name", "");
		String tag_code = (String) modelJson.getOrDefault("tag_code", "");
		int page = modelJson.getInt("page");
		int limit = modelJson.getInt("limit");
		try {
			tag_name = URLDecoder.decode(tag_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode tag_name fields error, role_name=" + tag_name,e1);
		}
		int start = (page-1) * limit;
		log.info( "--getTags(start)-->tag_name:" + tag_name + "tag_code:" + tag_code + "start:" + start + ";limit:" + limit);
		String jsonStr = null;

		try {
			List<TagInfoBean> tagInfoList = tagManageDao.getTag(tag_code, tag_name,start + 1, limit);
			List<TagInfoBean> AllInfoList = tagManageDao.getTag(tag_code, tag_name);

			int size = 0;
			if (AllInfoList != null && AllInfoList.size() > 0) {
				size = AllInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					tagInfoList, size, new TagInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取标签信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "标签管理->查询标签列表失败：" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getTags(over)" );
		return null;
	}
	
	/**
	 * 增加或修改标签及关联属性
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/updateTagAttrsAction.action", method = RequestMethod.POST)
	public String updateTagAttrs(String data) {		
		JSONObject modelJson = JSONObject.fromObject(data);
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");
		String tag_code = (String) modelJson.getOrDefault("tag_code", "");
		String tag_name = (String) modelJson.getOrDefault("tag_name", "");
		String attrNames = (String) modelJson.getOrDefault("attrNames", "");
		String attrTypes = (String) modelJson.getOrDefault("attrTypes", "");
		String attrIndexs = (String) modelJson.getOrDefault("attrIndexs", "");
		String attrAboves = (String) modelJson.getOrDefault("attrAboves", "");
//		String check_ids = (String) modelJson.getOrDefault("check_ids", "");
		String tag_state = tagManageDao.getTagState(tag_code);

		try {
			tag_name = URLDecoder.decode(tag_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode tag_name fields error, role_name=" + tag_name,e1);
		}
		log.info( "--updateTagAttrs(start)-->tag_code:" + tag_code + "tag_name:" + tag_name + "tag_state:" + tag_state);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		TagInfoBean tagBean = new TagInfoBean();
		tagBean.setTag_code(tag_code);
		tagBean.setTag_name(tag_name);
		tagBean.setTag_state(tag_state);

		log.debug( "--updateTagAttrs-->tagBean:" + tagBean);
		try {
//			List<TagRelAttributeBean> attrslist = tagManageDao.getAttrByID(attr_ids);
			
			List<TagRelAttributeBean> attrslist = formatAttrBean(attrNames,attrTypes,attrIndexs,attrAboves);
			log.debug( "--updateTagAttrs-->optionFlag:" + optionFlag );
			boolean result = false;
			if (optionFlag != null && optionFlag.equals("create")) {// 新增标签
				attrslist = addSysAttrs(attrslist);
//				check_ids+=",MODEL_CODE,CONTENT_ID,BUSI_START_DATE,GROUP_ID,STATE,FILE_NO";
				result = tagManageDao.addTag(tagBean,attrslist);
			} else if (optionFlag != null && optionFlag.equals("update")) {// 修改标签
//				if(tag_state.equals(TagStateUtil.Already_Synchronized)){//已同步
//					log.error("updateTag when tag is already synchronized");
//					result = false;
//				}else{
					result = tagManageDao.updateTag(tagBean,attrslist);
//				}
			}
			log.debug( "--updateTagAttrs-->result:" + result );
			if (result) {
				jsonResp.put("success", true);
				jsonResp.put("message", "配置标签成功!!");
				jsonResp.put("code", 20000);
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "配置标签失败!!");
			}
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置标签失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "标签管理->配置标签失败!" + e.toString(), e);
		}
			
		log.info( "--updateTagAttrs(over)-->tag_code:" + tag_code);
		this.outJsonString(jsonStr);
		return null;
	}
	
	private List<TagRelAttributeBean> formatAttrBean(String attrNames, String attrTypes, String attrIndexs,String attrAboves) {
		log.info( "--formatAttrBean(start)-->attrNames:" + attrNames + "attrTypes:" + attrTypes + "attrIndexs:" + attrIndexs + "attrAboves:" + attrAboves);
		if(attrNames.equals("") || attrNames == null) {
			return null;
		}
		String[] names = attrNames.split(",");
		String[] types = attrTypes.split(",");
		String[] indexs = attrIndexs.split(",");
		String[] aboves = attrAboves.split(",");
		List<TagRelAttributeBean> attrs = new ArrayList<TagRelAttributeBean>();
		for(int i=0;i<names.length;i++) {
			TagRelAttributeBean tag = new TagRelAttributeBean();
			tag.setAttribute_code(names[i]);
			tag.setAttribute_type(types[i]);
			tag.setAttribute_isindex(indexs[i]);
			tag.setAttribute_motel_type(3);
			tag.setAttribute_above(Integer.valueOf(aboves[i]));
			attrs.add(tag);
		}
		log.info( "--formatAttrBean(end)-->attrs:" + attrs);
		return attrs;
	}

	/**
	 * 删除标签
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/delTagAction.action", method = RequestMethod.POST)
	public String delTag(String data) {
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try {
			JSONObject modelJson = JSONObject.fromObject(data);
			String tag_code = (String) modelJson.getOrDefault("tag_code", "");
			String tag_state = tagManageDao.getTagState(tag_code);

			log.info("--delTag(start)-->tag_code:" + tag_code +",tag_state:" + tag_state);
			String msg = "true";
			if(tag_state.equals(TagStateUtil.Already_Synchronized)||tag_state.equals(TagStateUtil.Synchronized_Modify)){//已同步
				SingletonManageDAOIMP dao = new SingletonManageDAOIMP();
				msg = dao.deleteEsIndexToSunECMDM(ReadConfig.getConsoleConfigBean().getEs_sunecmdmip(), tag_code);
			}else {
				log.info("es未同步，直接删除数据库");
			}
			log.info("deleteEsIndexToSunECMDM[" + msg + "]");
			if (Boolean.TRUE.toString().equals(msg)) {
				boolean result = tagManageDao.delTag(tag_code);
				jsonResp.put("success", result);
				if (result) {
					log.debug("--delTag-->删除标签成功!!-->tag_code:" + tag_code);
					jsonResp.put("message", "删除标签成功!!");
					jsonResp.put("code", 20000);
				} else {
					log.debug("--delTag-->删除标签失败!!-->tag_code:" + tag_code);
					jsonResp.put("message", "删除标签失败!!");
				}
				jsonStr = jsonResp.toString();

			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", msg);
				jsonStr = jsonResp.toString();
				log.error("--delTag-->ES删除标签失败!!");
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "删除标签失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("标签管理->删除标签失败！" ,e);
		}
		this.outJsonString(jsonStr);
		log.info("--delTag(over)");
		return null;
	}
	
	/**
	 * 获取标签已有自定义属性
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/getExistAttrsTreeAction.action", method = RequestMethod.POST)
	public String getExistAttrsTree(String tag_code) {
		log.info("--getExistAttrsTree(start)-->tag_code:" + tag_code);
		JSONObject jsonObj = null;
		JSONArray jsonArray=new JSONArray();
		JSONObject jsonResp = new JSONObject();
		try{
			List<TagRelAttributeBean> attrslist = tagManageDao.getExistAttrsTree(tag_code);
			for(TagRelAttributeBean attr:attrslist){		
				jsonObj = new JSONObject();
				jsonObj.put("id",attr.getAttribute_code());
				jsonObj.put("text",attr.getAttribute_name()+"("+attr.getAttribute_code()+")");
				jsonObj.put("type",attr.getAttribute_type());
				jsonObj.put("index",attr.getAttribute_isindex());
				jsonObj.put("above",attr.getAttribute_above());
				jsonArray.add(jsonObj);
			}
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonArray);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取属性信息失败!!");
			log.error( "标签管理->获取标签已有自定义属性失败->",e);
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getExistAttrsTree(over)-->tag_code:"+tag_code);
		return null;
	}
	
	/**
	 * 获取标签未有自定义属性
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/getNotExistAttrsTreeAction.action", method = RequestMethod.POST)
	public String getNotExistAttrsTree(String tag_code) {
		log.info("--getNotExistAttrsTree(start)-->tag_code:" + tag_code);
		JSONObject jsonObj = null;
		JSONArray jsonArray=new JSONArray();
		JSONObject jsonResp = new JSONObject();
		try{
			List<TagRelAttributeBean> attrsList = tagManageDao.getNotExistAttrsTree(tag_code);
			for(TagRelAttributeBean attr:attrsList){
				jsonObj = new JSONObject();
				jsonObj.put("id",attr.getAttribute_code());
				jsonObj.put("text",attr.getAttribute_name()+"("+attr.getAttribute_code()+")");
				jsonArray.add(jsonObj);
			}
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonArray);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取属性信息失败!!");
			log.error( "标签管理->获取标签未有自定义属性失败->",e);
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getNotExistAttrsTree(over)-->tag_code:"+tag_code);
		return null;
	}

	private List<TagRelAttributeBean> addSysAttrs(List<TagRelAttributeBean> relAttrs) {
		//添加系统字段ecm_doc_id,model_code,busi_start_date,file_no,part_code,es_state
		String[] code = new String[]{"ECM_DOC_ID","MODEL_CODE","CONTENT_ID","ES_QUERY_TIME","GROUP_ID","FILE_NO","PART_CODE","ES_STATE"};
		String[] name = new String[]{"标签主键ID","模型代码","批次号","查询时间","组ID","文件file_no","文档对象代码","ES状态"};
		String[] index = new String[]{"1","1","1","1","1","1","0","1"};
		String[] type = new String[]{"keyword","keyword","keyword","date","keyword","keyword","keyword","keyword"};
		int[] motel = new int[]{3,3,3,3,3,2,2,3};
		if(relAttrs == null) {
			relAttrs = new ArrayList<TagRelAttributeBean>();
		}
		for(int i=0;i<code.length;i++){
			TagRelAttributeBean attr = new TagRelAttributeBean();
			attr.setAttribute_code(code[i]);
			attr.setAttribute_name(name[i]);
			attr.setAttribute_type(type[i]);
			attr.setAttribute_isindex(index[i]);
			attr.setAttribute_motel_type(motel[i]);
			relAttrs.add(attr);
		}
		return relAttrs;
	}

	/**
	 * 获取标签已关联模型
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/getRelContentObjectTreeAction.action", method = RequestMethod.POST)
	public String getRelContentObject(String tag_code) {
		log.info("--getRelContentObject(start)-->tag_code:" + tag_code);
		String jsonStr = null;
		try{
			List<TagRelModelBean> objectList = tagManageDao.getRelContentObject(tag_code);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getRelContentObject-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TagRelModelBean());
		}catch(Exception e){
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取模型信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "标签管理->获取标签已关联模型失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getRelContentObject(over)-->tag_code:"+tag_code);
		return null;
	}
	
	/**
	 * 获取标签未关联模型
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/getUnRelContentObjectTreeAction.action", method = RequestMethod.POST)
	public String getUnRelContentObject(String tag_code) {
		log.info("--getUnRelContentObject(start)-->tag_code:" + tag_code);
		String jsonStr = null;
		try{
			List<TagRelModelBean> objectList = tagManageDao.getUnRelContentObject(tag_code);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getUnRelContentObject-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TagRelModelBean());
		}catch(Exception e){
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取模型信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "标签管理->获取标签未关联模型失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getUnRelContentObject(over)-->tag_code:"+tag_code);
		return null;
	}
	
	/**
	 * 创建es索引
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/createEsIndexToSunECMDM.action", method = RequestMethod.POST)
	public String createEsIndexToSunECMDM(String tag_code) {
		log.info("--createEsIndexToSunECMDM(start)-->tag_code:" + tag_code);
		String jsonStr = null;
		try {
			SingletonManageDAOIMP dao = new SingletonManageDAOIMP();
			String tag_state = tagManageDao.getTagState(tag_code);
			List<TagRelAttributeBean> attrList = tagManageDao.getRelAttr(tag_code,tag_state);
			JSONObject jsonResp = new JSONObject();
			if (attrList != null&&attrList.size()>0) {
				JSONObject mm  = null;
				jsonResp.put("indexName", tag_code);
				jsonResp.put("tagState", tag_state);
				for (TagRelAttributeBean bean : attrList) {
					 mm = new JSONObject();
					mm.put("ATTR_TYPE", bean.getAttribute_type());
					mm.put("IS_INDEX", bean.getAttribute_isindex());
					mm.put("ATTR_STATE", bean.getState());
					mm.put("ATTR_LENGTH", bean.getAttribute_above());
					jsonResp.put(bean.getAttribute_code().toUpperCase(), mm);
				}
				String result=dao.createEsIndexToSunECMDM(ReadConfig.getConsoleConfigBean().getEs_sunecmdmip(), jsonResp.toString());
				if (Boolean.TRUE.toString().equals(result)) {
					//更改数据库状态
					tagManageDao.changeSynState(tag_code);
					jsonResp.put("success", true);
					jsonResp.put("message", result);
					jsonResp.put("code", 20000);
					jsonStr = jsonResp.toString();
				} else {
					jsonResp.put("success", false);
					jsonResp.put("message", result);
					jsonStr = jsonResp.toString();
				}
			}else {
				jsonResp.put("success", false);
				jsonResp.put("message", "标签索引属性为空");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "createEsIndexToSunECMDM error");
			jsonStr = jsonResp.toString();
			log.error("标签管理->createEsIndexToSunECMDM失败->", e);
		}
		this.outJsonString(jsonStr);
		log.info("--createEsIndexToSunECMDM(over)");
		return null;
	}	
	
	/**
	 * 增加或修改标签关联模型
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/updateTagModelsAction.action", method = RequestMethod.POST)
	public String updateTagModels(String data) {		
		JSONObject modelJson = JSONObject.fromObject(data);
		String tag_code = (String) modelJson.getOrDefault("tag_code", "");
		String model_ids = (String) modelJson.getOrDefault("model_ids", "");

		log.info( "--updateTagModels(start)-->tag_code:" + tag_code + "model_ids:" + model_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		TagInfoBean tagBean = new TagInfoBean();
		tagBean.setTag_code(tag_code);
		tagBean.setModel_ids(model_ids);

		log.debug( "--updateTagModels-->tagBean:" + tagBean);
		try {
			boolean result = false;
			result = tagManageDao.updateTagModels(tagBean);
			log.debug( "--updateTagModels-->result:" + result );
			if (result) {
				jsonResp.put("success", true);
				jsonResp.put("message", "标签关联模型成功!!");
				jsonResp.put("code", 20000);
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "标签关联模型失败!!");
			}
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "标签关联模型失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "标签管理->标签关联模型失败!" + e.toString(), e);
		}
			
		log.info( "--updateTagModels(over)-->tag_code:" + tag_code);
		this.outJsonString(jsonStr);
		return null;
	}
	
	/**
	 * 得到标签关联模型信息
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/getTagModelAction.action", method = RequestMethod.POST)
	public String getTagModel(String data){
		JSONObject modelJson = JSONObject.fromObject(data);
		String tag_code = (String) modelJson.getOrDefault("tag_code", "");
		int page = modelJson.getInt("page");
		int limit = modelJson.getInt("limit");
		int start = (page-1) * limit;
		log.info( "--getTagModel(start)-->tag_code:" + tag_code + "start:" + start + ";limit:" + limit);
		String jsonStr = null;

		try {
			List<TagRelModelBean> modelList = tagManageDao.getRelContentObject(tag_code,start + 1, limit);
			List<TagRelModelBean> allModelList = tagManageDao.getRelContentObject(tag_code);

			int size = 0;
			if (allModelList != null && allModelList.size() > 0) {
				size = allModelList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					modelList, size, new TagRelModelBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取标签信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "标签管理->查询标签列表失败：" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getTagModel(over)" );
		return null;
	}
	/**
	 * 启用、禁用标签关联模型
	 * @return 
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/modifyModelRelStateAction.action", method = RequestMethod.POST)
	public String modifyModelRelState(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String tag_code = (String) modelJson.getOrDefault("tag_code", "");
		String model_code = (String) modelJson.getOrDefault("model_code", "");
		String state = (String) modelJson.getOrDefault("state", "");

		log.info( "--modifyModelRelState(start)-->tag_code:"+tag_code+";model_code:"+model_code+";state:"+state);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try{
			tagManageDao.modifyModelRelState(tag_code, model_code,state);
			jsonResp.put("success", true);
			if(Integer.valueOf(state)==1){
				jsonResp.put("message", "启用标签关联模型关系成功!!");
				jsonResp.put("code", 20000);
			}else{
				jsonResp.put("message", "禁用标签关联模型关系成功!!");
				jsonResp.put("code", 20000);
			}
			IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
			IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			jsonStr = jsonResp.toString();
			log.debug( "--modifyModelRelState-->启用/禁用标签关联模型关系成功!!");
			
		}catch(Exception e){
			jsonResp.put("success", false);
			if(Integer.valueOf(state)==1){
				jsonResp.put("message", "启用标签关联模型关系失败!!");
				log.error( "用户管理->启用标签关联模型关系失败->"+e.toString());
			}else{
				jsonResp.put("message", "禁用标签关联模型关系失败!!");
				log.error( "用户管理->禁用标签关联模型关系失败->",e);
			}
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--modifyModelRelState(over)");
		return null;
	}

	/**
	 * 查看es信息
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/searchEsFromDMAction.action", method = RequestMethod.POST)
	public String searchEsFromDM(String tag_code) {
		log.info("--searchEsFromDM(start)-->tag_code:" + tag_code);
		String jsonStr = null;
		try {
			SingletonManageDAOIMP dao = new SingletonManageDAOIMP();
			JSONObject jsonResp = new JSONObject();
			if (!StringUtil.stringIsNull(tag_code)) {
				String result = dao.searchEsFromDM(ReadConfig.getConsoleConfigBean().getEs_sunecmdmip(),tag_code);
				JSONObject jsonObj = JSONObject.fromObject(result);
				if (jsonObj.getBoolean("SUCCESS")) {//创建成功
					jsonResp = FormatESInfo(jsonObj,tag_code);
					jsonStr = jsonResp.toString();
				} else {
					jsonResp.put("success", false);
					jsonResp.put("message", jsonObj.get("MSG"));
					jsonStr = jsonResp.toString();
				}
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "es索引code为空");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "searchEsFromDM error");
			jsonStr = jsonResp.toString();
			log.error("标签管理->searchEsFromDM失败->", e);
		}
		this.outJsonString(jsonStr);
		log.info("--searchEsFromDM(over)");
		return null;
	}
	
	/**
	 * 取ES属性定义类型
	 * 
	 * @return
	 */
	@RequestMapping("/tagManage/getESAttrTypeAction.action")
	@ResponseBody
	public String getESAttrType() {
		log.info("--getESAttrType(start)");
		String jsonStr = null;

		try {
			List<TagRelAttTypeDefineBean> attibuteTypeList = tagManageDao.getAttributeTypeList();
			if (attibuteTypeList == null) {
				attibuteTypeList = new ArrayList<TagRelAttTypeDefineBean>();
			} else {
				log.debug("--getESAttrType-->attibuteTypeList:" + attibuteTypeList);
			}
			int size = 0;
			if (attibuteTypeList.size() > 0) {
				size = attibuteTypeList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(attibuteTypeList, size, new TagRelAttTypeDefineBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取属性类型失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("标签管理->获取ES属性类型失败！" + e.toString());
			log.error("Exception:", e);
		}
		this.outJsonString(jsonStr);
		log.info("--getESAttrType(over)");
		return null;
	}
	
	private JSONObject FormatESInfo(JSONObject jsonObj, String tag_code) {
		JSONObject map = jsonObj.getJSONObject("MAPPING");
	    JSONObject pro = map.getJSONObject("properties");
		JSONObject setting = jsonObj.getJSONObject("SETTING");
		JSONObject set = setting.getJSONObject(tag_code);

		JSONObject newJ = new JSONObject();
		JSONObject jsonPro = null;
		JSONArray jsonArray=new JSONArray();
		
        Iterator iterator = pro.keys();
        int size = 0;
	    while (iterator.hasNext()) {
	    	jsonPro = new JSONObject();
	        String json_key = (String) iterator.next();
	        JSONObject json_value = pro.getJSONObject(json_key);
	        String index;
	        if(json_value.get("index") == null) {
	        	index = "1";
	        }else{
	        	index = "0";
	        }
	        jsonPro.put("AttrCode", json_key);
	        jsonPro.put("AttrType", json_value.get("type"));
	        jsonPro.put("AttrIndex", index);
	        size++;
	        jsonArray.add(jsonPro);
	    } 
	    
	    JSONObject jsonSet = new JSONObject();
		Iterator setIter = set.keys();
	    while (setIter.hasNext()) {
	        String json_key = (String) setIter.next();
	        String json_value = (String) set.get(json_key);
	        if(json_key.equals("index.creation_date")){//改变时间格式
	        	json_value = DateUtil.timeStamp2Date(json_value, "yyyy-MM-dd HH:mm:ss");
	        }
	        jsonSet.put(json_key, json_value);
	    }  
	    newJ.put("Attr", jsonArray);
	    newJ.put("AttrSize", size);
	    newJ.put("Setting", jsonSet);
	    newJ.put("success", true);
	    newJ.put("code", 20000);
	    return newJ;
	}

	/**
	 * 标签名及标签代码唯一校验
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/tagManage/checkTagNameAction.action", method = RequestMethod.POST)
	public String checkTagName(String data){
		JSONObject modelJson = JSONObject.fromObject(data);
		String tag_name = (String) modelJson.getOrDefault("tag_name", "");
		String tag_code = (String) modelJson.getOrDefault("tag_code", "");
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");

		log.info( "--checkTagName(start)-->tag_code:"+tag_code+",tag_name:"+tag_name);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int nameCount = 0;
		int codeCount = 0;
		
		try {
			if(tag_code!=null){
				tag_code=tag_code.toLowerCase();
			}
			try {
				tag_name = URLDecoder.decode(tag_name, "utf-8");
			} catch (UnsupportedEncodingException e1) {
				log.error("decode tag_name fields error, role_name=" + tag_name,e1);
			}
			if (optionFlag != null && optionFlag.equals("create")) {// 新增标签
				codeCount = tagManageDao.checkTagCode(tag_code);
				tag_code = "";
			}
			nameCount = tagManageDao.checkTagName(tag_code,tag_name);
		} catch (Exception e) {
			nameCount = -1;
			// 记录日志
			log.error( "标签管理->校验该标签代码或名称是否存在失败!" + e.getMessage());
		}
		log.debug( "--checkTagName-->nameCount:"+nameCount+",codeCount:"+codeCount);
		if (nameCount == 0 && codeCount == 0) {
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
		} else if (nameCount > 0 || codeCount > 0) {
			jsonResp.put("success", false);
			jsonResp.put("reason", "标签代码或名称已经被使用!!");
			jsonResp.put("message","标签代码或名称已经被使用!!");
		} else {
			jsonResp.put("success", true);
			jsonResp.put("message","检验失败!!");
			jsonResp.put("reason", "检验失败!!");
		}
		jsonStr = jsonResp.toString();
		this.outJsonString(jsonStr);
		log.info( "--checkTagName(over)");
		return null;
	}
}
