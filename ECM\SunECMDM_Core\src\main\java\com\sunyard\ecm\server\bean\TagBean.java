package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

import java.util.HashMap;

/**
 * 服务端标签类，索引和文档中设置标签公用
 * <AUTHOR>
 *
 */
@XStreamAlias("TagBean")
public class TagBean {
	@XStreamAsAttribute
	private String TAG_CODE;// 标签模型名
	
	/**
	 * 操作类型：ecm上传(默认只有ADD标签，可以不设置),ecm更新(需要设置ADD,UPDATE,DEL,未设置或者设置不正确则不调用ESAPI)
	 * 
	 */
	@XStreamAsAttribute
	private String OPTYPE;

	public String getOPTYPE() {
		return OPTYPE;
	}
	public void setOPTYPE(String oPTYPE) {
		OPTYPE = oPTYPE;
	}
	private HashMap<String, Object> TAG_MAP;//标签自定义属性
	
	/**
	 * 获取标签名
	 */
	public String getTAG_CODE() {
		return TAG_CODE;
	}
	/**
	 * 设置标签名
	 * 
	 */
	public void setTAG_CODE(String tAG_CODE) {
		TAG_CODE = tAG_CODE;
	}
	/**
	 * 获取标签自定义集合
	 */
	public HashMap<String, Object> getTAG_MAP() {
		return TAG_MAP;
	}
	/**
	 * 设置标签自定义集合
	 */
	public void setTAG_MAP(HashMap<String, Object> tAG_MAP) {
		TAG_MAP = tAG_MAP;
	}
}
