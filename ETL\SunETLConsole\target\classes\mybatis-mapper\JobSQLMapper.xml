<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.JobSQLDao">
	
	<resultMap id="JobSQL" type="com.xxl.job.admin.core.model.JobSql" >
		<result column="id" property="id" />
		<result column="SQL_VALUE" property="sqlParamValue" />
	    <result column="SQL_DEC" property="sqlDec" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.SQL_VALUE,
		t.SQL_DEC
	</sql>
	
	<select id="pageList" parameterType="java.util.HashMap" resultMap="JobSQL">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_JOB_SQL t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="id gt 0">
				AND t.id = #{id}
			</if>
		</trim>
		ORDER BY id
	</select>




	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM QRTZ_JOB_SQL t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="id gt 0">
				AND t.id = #{id}
			</if>
		</trim>
	</select>

	<insert id="save" parameterType="com.xxl.job.admin.core.model.JobSql" useGeneratedKeys="false" keyProperty="id" >
		INSERT INTO QRTZ_JOB_SQL (
			ID,SQL_VALUE, SQL_DEC
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{sqlParamValue},
			#{sqlDec}
		)
	</insert>

	
	<update id="update" parameterType="com.xxl.job.admin.core.model.JobSql" >
		UPDATE QRTZ_JOB_SQL
		SET 
			SQL_VALUE = #{sqlParamValue},
			SQL_DEC = #{sqlDec}
		WHERE id = #{id}
	</update>
	
	<delete id="delete" parameterType="java.util.HashMap">
		DELETE
		FROM QRTZ_JOB_SQL
		WHERE id = #{id}
	</delete>



</mapper>