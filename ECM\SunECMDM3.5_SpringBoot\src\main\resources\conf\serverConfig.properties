#管理控制中心WebService地址
#consoleServer=http://************:8080/SunECMConsole/webservices/WsInterface
consoleServer=http://127.0.0.1:8081/SunECMConsole/webservices/WsInterface

#统一接入WebService地址
#accessServer=http://************:8080/UnityAccess/webservices/WsInterface

#本机IP
#localIP=*************
localIP=127.0.0.1
#网卡类型选取，在localIP为空的情况下设置,例如 bond0，bond1，适应于多网卡
NIC_Type=
#工程名称UnityAccess
localName=SunECMDM
#SunEcmDM的工程名称
server_DM_Name =SunECMDM
#socket端口
socketEnable=true
socketPort=8025
#socket连接次数
connectretrytimes=1
#数据库类型，从
DBType=oracle
#socket服务端  消息报文的编码方式。文件不需要编码方式。
socketStreamCharSet=UTF-8
#socket连接超时（0为不超时，单位为秒）
sockettimeout=50
#接收socket缓存大小
sockereceivetbuffersize=65535
#发送socket缓存大小
socketsendbuffersize=65535
#socket线程池大小
socketMaxThreadPool=200
#文件 socket接收和保存使用的buffer数组大小。需要考虑socket接收缓存和文件系统要求。
fileBufferSize=65535


#是否启动ssl socket
sslSocketEnable = false
sslProtocol=SSL
#
keystoreFile=/sunecm.keystore
keystorePass=123456
sslSocketPort=8026

#session失效时间(单位为秒)
maxSessionInterval=1500
#查询影像时候是url否一直有效 true一直有效(true 或  false)
isOpenSession=false

#断点文件保存位置
breakPoint=e:/breakpoint/

#是否MD5校验，1代表校验，0代表不校验
isMD5Check=0

##迁移时放任务的队列长度
queuelength=80
##排程所开启的线程数
threadsum=5
#立即迁移任务尝试次数 
migrateCount=5

#下拉操作查询，0原全版本查询，1直接查询数据库
downloadquery=1
#下拉操作的重试次数
downloadcount=5
#批次复制操作的重试次数
copyCount=5
#是否开启ES排程
IsOpenEsMainThread=false
#ES排程所开启的线程数
Esthreadsum=10
#ES上传操作的重试次数
esCount=5
##生命周期变更开启的线程数
#lifeCycleThreadSum=5
#内容清理失败的次数上限(不能小于2)
clearFailCount=3
#内容清理MD5校验是否开启
clear.md5check=true

##批次检入失效时间(单位为分)
batchSession =30
##迁移使用什么协议传输(socket   或   http,https)
agreement =socket
migratetempPath=F:/DM/TMP/migrate
#是否开启缩略图 true 或 false
isThumbnail=false
#离线后数据的清理方式,是否只进行物理清理,true 为只删除文件，false为删除文件和记录
onlyPhysicalClear= false
#上传密钥key文件位置
keyFilePath=key.obj
#数据库锁超时时间（分钟）
lockTime=120
#文件数量管理功能开关，0关闭，1开启
FileNumSwitch=0
#文件数量默认值，Console配置参数管理若没有配置MaxFileNums则采用此项
MaxFileNums=1000
#版本数量管理功能开关，0关闭，1开启
VersionSwitch=0
#文件数量默认值，Console配置参数管理若没有配置MaxFileNums则采用此项
MaxVersionNums=1000

##=============WAS集群====================
##是否使用WAS集群部署DM，如果为true则表示使用，如果为false则表示不使用
wasCluster = false
##使用WAS集群时，需要为临时保存批次或文档信息提供存储位置
tempPath = F:/DM/TMP/
##===========离线系统所需的配置============
#离线服务器所在的ip地址
offlineIP=************
#离线工程名
offlineName=SunECMOffline
#离线服务http端口
offlinePort=8080
#离线socket端口
offlineSocket=8024
#离线开始日期
beginOfflineDate=20200915
#监控dm的心跳配置，监控活动的dm是否死亡，单位是秒
client.dm.heartbeat.alive=60
#监控dm的心跳配置，监控死亡的dm是否活动，单位是秒
client.dm.heartbeat.dead=300
#记录文件上传和删除的跟路径
fileRecordPath=E:/log/fenhang
#批次号生成规则:20170203_BFD93A50-D8EC-71C0-028C-39105079F731  20170203_BFD93A50-D8EC-71C0-028C-39105079F731-2 20170203_456_123_BFD93A50-D8EC-71C0-028C-39105079F731-2
#为1标识是[日期_+guid], 为2表示是[日期_+guid_+serverid],为3表示是[ 日期_+path_+guid_+serverid],默认是3
contentIdRule=3
#//默认的总行服务器组名，九江银行根据机构号查询服务器信息需要
#defaultDmsName=false
#是否开启ip转换，开启hosts文件里面的cip和ip之间的映射关系，默认不开启为false,开启为true
openChanageIp=false
#dm重新加载内存信息（0 40 23 * * ?）
resetinfotimer = false
#ua高级查询是否查询第三方存储
ua.heightquery.queryext =false
#配置ua的ip和http端口
UAURL=http://127.0.0.1:8080/UnityAccess
#是否开启referer验证,不写默认为false不开启
isReferer=false
#referer白名单 例如： SunECM,SunScan 以逗号做间隔
whiteList=SunECM
#是否开启redis统计
writeReport=false
#加密算法
algorithm=DESede
isOpenOffline=true
#是否是SCM存储
isScm=false
#SCM存储->断点续传的模型 模型名=true
#AA=true
#校验es的模型关系
es_check_model=true
#校验es的属性关系
es_check_attr=true
#dubbo开关
#dubboIsOn=false