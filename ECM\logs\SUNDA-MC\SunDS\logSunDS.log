2025-07-25 01:09:43.106 [OrganNo_00023_UserNo_admin] [6055a84d5d734375/ea116ba4f5072005] [http-nio-9058-exec-23] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessGrade":0,
			"businessNo":0,
			"parentBusiness":"200001"
		}
	],
	"sysMap":{
		"operType":"OP004"
	}
}
2025-07-25 01:09:43.221 [OrganNo_00023_UserNo_admin] [6055a84d5d734375/ea116ba4f5072005] [http-nio-9058-exec-23] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"list":[
			{
				"BUSINESS_ID":"WYLB00001",
				"BUSINESS_NAME":"结算账户管理",
				"BUSINESS_DESC":"结算账户管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":3
			},
			{
				"BUSINESS_ID":"WYLB00002",
				"BUSINESS_NAME":"内部账户及科目管理",
				"BUSINESS_DESC":"内部账户及科目管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":4
			},
			{
				"BUSINESS_ID":"WYLB00003",
				"BUSINESS_NAME":"支付结算业务管理",
				"BUSINESS_DESC":"支付结算业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":5
			},
			{
				"BUSINESS_ID":"WYLB00004",
				"BUSINESS_NAME":"信贷业务管理",
				"BUSINESS_DESC":"信贷业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":6
			},
			{
				"BUSINESS_ID":"WYLB00005",
				"BUSINESS_NAME":"现金及重空管理",
				"BUSINESS_DESC":"现金及重空管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":7
			},
			{
				"BUSINESS_ID":"WYLB00006",
				"BUSINESS_NAME":"资金异动管理",
				"BUSINESS_DESC":"资金异动管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":8
			},
			{
				"BUSINESS_ID":"WYLB00007",
				"BUSINESS_NAME":"个人业务管理",
				"BUSINESS_DESC":"个人业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":9
			},
			{
				"BUSINESS_ID":"WYLB00008",
				"BUSINESS_NAME":"其他",
				"BUSINESS_DESC":"其他",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":10
			},
			{
				"BUSINESS_ID":"WYLB00009",
				"BUSINESS_NAME":"机构及柜员管理",
				"BUSINESS_DESC":"机构及柜员管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":11
			}
		]
	},
	"retMsg":"树初始化成功"
}
2025-07-25 01:09:43.222 [] [6055a84d5d734375/ea116ba4f5072005] [http-nio-9058-exec-23] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作业务条线 操作方法: 对树进行加载!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 128 毫秒！
2025-07-25 01:09:43.727 [OrganNo_00023_UserNo_admin] [facc3c2742f3baaf/e2f93549815ceff0] [http-nio-9058-exec-24] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessLine":"",
			"modelType":"0",
			"riskLevel":""
		}
	],
	"sysMap":{
		"oper_type":"getUserModelInfos"
	}
}
2025-07-25 01:09:43.772 [OrganNo_00023_UserNo_admin] [facc3c2742f3baaf/e2f93549815ceff0] [http-nio-9058-exec-24] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"userModelInfos":[
			
		]
	},
	"retMsg":"查询成功"
}
2025-07-25 01:09:43.773 [] [facc3c2742f3baaf/e2f93549815ceff0] [http-nio-9058-exec-24] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询模型 操作方法: 获取用户模型!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:43 操作结束时间: 2025-07-25 01:09:43!总共花费时间: 62 毫秒！
