<template>
  <div class="app-container">
      <el-button
        size="mini"
        type="primary"
        @click="getList()"
      >
        刷新网点信息
      </el-button>

     <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >   
      <el-table-column label="网点ID"   v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.server_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="网点名称"   min-width="20%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="服务器类型"  min-width="20%" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.server_type == '1'">内容存储</span>
          <span v-if="row.server_type == '2'">统一接入</span>
        </template>
      </el-table-column>

      <el-table-column label="网点状态" min-width="10%" align="center">
        <template slot-scope="{ row }" >
          <span v-if="row.state == '1'">活动</span>
          <span v-if="row.state == '0'">停用</span>
        </template>
      </el-table-column>

       <el-table-column label="内存使用率" min-width="20%" align="center">
        <template slot-scope="{ row }" >
           <span>{{ row.mem_rate }}</span>
        </template>
      </el-table-column>

        <el-table-column label="最大硬盘使用率" min-width="20%" align="center">
        <template slot-scope="{ row }" >
           <span>{{ row.disk_rate }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        min-width="20%"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button
            size="mini"
            type="primary"
            @click="checkNetNode(row)"
          >
            查看网络节点信息
          </el-button>

          <el-button
            size="mini"
            type="primary"
            @click="checkNetFlow(row)"
          >
            查看网络流量图
          </el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="title1" :visible.sync="dialogFormVisible">
      <el-form :inline="true">
        <el-form-item
          v-for="item in listData"
          :key="item.name"
          :label="item.title + ':'"
          style="margin-bottom: 2"
        >
          [<span style="color: blue">{{ pvData[item.name] }}
            </span>]
        </el-form-item>
      </el-form>
      <!-- <div v-for="(item,index) in chartList" :key="index"> -->
        <el-row :gutter="32">
          <el-col :xs="24" :sm="24" :lg="10">
            <!-- <div :id="`chart${index}`" :style="{height:height,width:width,padding: padding}"> -->
              <div :id="canvas2" :style="{height:height,width:width,padding: padding}">
            </div>
          </el-col>
        </el-row>
      <!-- </div> -->
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="title2" :visible.sync="dialogNetVisible" :width="diawidth" @close="closeDialog">
        <div :id="canvas"  :style="{height:height1,width:width1,padding: padding}">
        <!-- <div :id="chart"  :style="{height:height1,width:width1,padding: padding}"> -->
        </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogNetVisible = false"> 取消 </el-button>
      </div>
    </el-dialog>

   </div>
</template>

<script>
import {getServerInfo,getServer,getNetFlow} from '@/api/monitorManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import * as echarts from 'echarts'

export default {
  name: 'ComplexTable',
  components: { Pagination },
  directives: { waves,elDragDialog },
  filters: {
  },
  props: {
    canvas: {
      type: String,
      default: 'chart'
    },
    canvas2: {
      type: String,
      default: 'chart1'
    },
    width: {
      type: String,
      default: '600px'
    },
    height: {
      type: String,
      default: '300px'
    },
    width1: {
      type: String,
      default: '800px'
    },
    diawidth: {
      type: String,
      default: '850px'
    },
    height1: {
      type: String,
      default: '400px'
    },
    padding: {
      type: String,
      default: '16px 16px'
    },
    padding1: {
      type: String,
      default: '16px 16px'
    }
  },
  data() {
    return {
      title1:'当前网络节点信息',
      title2:'网络流量折线图',
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        sort: '+server_id'
      },
      query: {
        server_id:''
      },
      listData: [
        { name: "cpu_used", title: "CPU使用率" },
        { name: "mem_rate", title: "内存使用率" },
        { name: "men_total", title: "内存总容量" },
        { name: "men_used", title: "内存已用数" },
        { name: "men_free", title: "内存可用数" }
      ],
      pvData: [],
      netData: [],
      chartList: [],
      importanceOptions: [1, 2, 3],
      sortOptions: [{ label: 'ID Ascending', key: '+server_id' }, { label: 'ID Descending', key: '-server_id' }],
      showReviewer: false,
      temp: {
        id: undefined,
        importance: 1,
        remark: '',
        timestamp: new Date(),
        title: '',
        type: '',
        status: 'published'
      },

      dialogFormVisible: false,
      dialogNetVisible: false,
      dialogStatus: '',
      downloadLoading: false,
      netChart:null,
      objs1:[],
      objs2:[]
    }
  },

  created() {
    this.getList()
  },

  beforeDestroy(){
    clearInterval(this.event);
    this.event=null;
  },

  methods: {
    getList() {
      this.listLoading = true
      getServerInfo(this.listQuery).then(response => {
        this.list = response.root
        this.total = Number(response.totalProperty)
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
    },

    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    closeDialog() {
      clearInterval(this.event);
      this.event=null;
      this.dialogNetVisible = false;
    },
    
    sortChange(data) {
      const { prop, order } = data
      alert(prop)
      if (prop === 'server_id') {
        this.sortByID(order)
      }
    },
    sortByID(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+server_id'
      } else {
        this.listQuery.sort = '-server_id'
      }
      this.handleFilter()
    },

    checkNetNode(row) {
      if (row.server_type == 1) {
					if (row.state == 1) {
            this.showNetNode(row);
					} else {
						alert("服务器停用无法查看详细信息");
					}
				} else {
					alert("统一接入服务器无详细信息");
				}
    },

    showNetNode(row){
      this.serverQuery.server_id = row.server_id;
      getServer(this.serverQuery).then((response) => {
        this.pvData = response.root
        this.dialogFormVisible = true;
        this.chartList = this.pvData.disk;
        if (this.pvData.disk_count > 0) {
          this.$nextTick(() => {
            this.initChart()
          })
        }
     });
    },
       initChart() {
          const myChart = echarts.init(document.getElementById(this.canvas2));
            let subts = [];
            for(let index=0;index<this.pvData.disk_count;index++){
              let subt;
              if(index == this.pvData.disk_count-1 ){
                subt = '总';
              }else{
                subt = this.pvData.disk[index].volume;
              }
              subts[index]= subt + '磁盘';
            }
            let option = {
              baseOption: {
					        animationDurationUpdate: 1000,
					        animationEasingUpdate: 'quinticInOut',
					        timeline: {
					            axisType: 'category',
					            orient: 'vertical',
					            autoPlay: true,
					            inverse: true,
					           // playInterval: 5000,
					            left: null,
					            right: 30,
					            top: 20,
					            bottom: 20,
					            width: 40,
					            height: null,
					            label: {
					                normal: {
                            	position:'left',
					                    textStyle: {
                                	fontSize : 15,
					                        color: '#3C3C3C'
					                    }
					                },
					                emphasis: {
					                    textStyle: {
                                	fontSize : 15,
					                        color: '#005757'
					                    }
					                }
					            },
					            symbol: 'none',
					            lineStyle: {
					                color: '#555'
					            },
					            checkpointStyle: {
					                color: '#bbb',
					                borderColor: '#777',
					                borderWidth: 1
					            },
					            controlStyle: {
					                showNextBtn: false,
					                showPrevBtn: false,
					                normal: {
					                    color: '#666',
					                    borderColor: '#666'
					                },
					                emphasis: {
					                    color: '#aaa',
					                    borderColor: '#aaa'
					                }
					            },
					            data: subts
					        },
					        color: ['#ec4863','#ffd285', '#ff733f'],
					    },
					    options: []
            }
            for(let index=0;index<this.pvData.disk_count;index++){
              let used = this.pvData.disk[index].used.replace('MB','');
              let free = this.pvData.disk[index].free.replace('MB','');
                option.options.push({
                  title: {
                    text: subts[index]+'信息',
                    left: 'center'
                  },
                  tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b} : {c}MB ({d}%)'

                  },
                  legend: {
                    orient: 'vertical',
                    left: 'left'
                  },
                  series: [
                    {
                      name: '磁盘信息',
                      type: 'pie',
                      radius: '50%',
                      data: [
                        { value: used, name: '已使用' },
                        { value: free, name: '未使用' }
                      ],
                      emphasis: {
                        itemStyle: {
                          shadowBlur: 10,
                          shadowOffsetX: 0,
                          shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                      }
                    }
                  ]
                })
            }
          myChart.setOption(option);
       },
    // initChart() {
    //   for(let index=0;index<this.pvData.disk_count;index++){
    //     const myChart = echarts.init(document.getElementById(`chart${index}`));
    //     let subt;
    //     if(index == this.pvData.disk_count-1 ){
    //       subt = '总';
    //     }else{
    //       subt = this.pvData.disk[index].volume;
    //     }
    //     let used = this.pvData.disk[index].used.replace('MB','');
    //     let free = this.pvData.disk[index].free.replace('MB','');
    //     let option = {
    //         title: {
    //           text: '磁盘'+subt+'信息',
    //           left: 'center'
    //         },
    //         tooltip: {
    //           trigger: 'item',
    //           formatter: '{a} <br/>{b} : {c} ({d}%)'

    //         },
    //         legend: {
    //           orient: 'vertical',
    //           left: 'left'
    //         },
    //         series: [
    //           {
    //             name: '磁盘信息',
    //             type: 'pie',
    //             radius: '50%',
    //             data: [
    //               { value: used, name: '已使用' },
    //               { value: free, name: '未使用' }
    //             ],
    //             emphasis: {
    //               itemStyle: {
    //                 shadowBlur: 10,
    //                 shadowOffsetX: 0,
    //                 shadowColor: 'rgba(0, 0, 0, 0.5)'
    //               }
    //             }
    //           }
    //         ]
    //     };
    //     myChart.setOption(option);
    //   }
    // },

    checkNetFlow(row) {
      if (row.server_type == 1) {
					if (row.state == 1) {
            this.query.server_id = row.server_id;
            this.showNetFlow();
					} else {
						alert("服务器停用无法查看详细信息");
					}
				} else {
					alert("统一接入服务器无详细信息");
				}
    },

    showNetFlow(){
      clearInterval(this.event);
      this.event=null;
      this.objs1=[];
      this.objs2=[];
      this.dialogNetVisible = true;
      this.$nextTick(() => {
        this.showCharts()
      })
      this.timerEvent();
      this.event = setInterval(this.timerEvent, 3000);
    },

    timerEvent(){
      getNetFlow(this.query).then((response) => {
        this.netData = response.root;
        let time = new Date().getTime();

        let point1 = [ time,Number(this.netData.rx) ];//下载
        let point2 = [ time,Number(this.netData.tx) ];//上传
        this.objs1.push(point1);
        this.objs2.push(point2);
        if (this.objs1.length > 10) {
          this.objs1.shift();
          this.objs2.shift();
        }
				this.netChart.setOption({
          series : [ {
						data : this.objs1
					}, {
						data : this.objs2
					} ]
				});
     });
    },

    showCharts(){
      this.netChart = echarts.init(document.getElementById(this.canvas));

      let option1 = {
        title: {
          text: '网络流量监控图'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['下载流量', '上传流量']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis : [ {
          type : 'time',
          scale : true,
          splitLine : {
            show : false
          }
        }],
        
        yAxis: {
          type: 'value',
          name : '(KB/S)',

        },
        series: [
          {
            name: '下载流量',
            type: 'line',
            stack: 'Total',
            data: this.objs1
          },
          {
            name: '上传流量',
            type: 'line',
            stack: 'Total',
            data: this.objs2
          }
        ]
      };
      this.netChart.setOption(option1);
    },
    formatJson(filterVal) {
      return this.list.map(v => filterVal.map(j => {
        if (j === 'timestamp') {
          return parseTime(v[j])
        } else {
          return v[j]
        }
      }))
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort
      return sort === `+${key}` ? 'ascending' : 'descending'
    }
  }
}
</script>
