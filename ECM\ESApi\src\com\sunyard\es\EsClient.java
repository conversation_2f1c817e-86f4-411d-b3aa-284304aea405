package com.sunyard.es;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig.Builder;
import org.apache.log4j.Logger;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.mapping.put.PutMappingRequest;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.RestClientBuilder.RequestConfigCallback;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.elasticsearch.common.compress.CompressedXContent;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sunyard.es.common.EsConfigBean;
import com.sunyard.es.common.ReadConfig;
import com.sunyard.es.util.*;
import net.sf.json.JSONObject;



public class EsClient {
	private final static Logger log = Logger.getLogger(EsClient.class);

	private RestClient restClient;
	private RestHighLevelClient restHeighlevelClient;
	private RestClientBuilder builder;
	private static EsConfigBean configBean;
	private static final String ECM_DOC_ID = "ECM_DOC_ID";
	private static final String ECM_DOC_VERSION = "ECM_DOC_VERSION";
	private static final String ECM_DOC_STATUS = "ES_STATE";
	private static final String ECM_DOC_MIN = "MIN";
	private static final String ECM_DOC_MAX = "MAX";
	private static HttpHost[] httpHostArray;
	private static int shards;
	private static int replicas;

	public RestHighLevelClient getRestHeighlevelClient() {
		return restHeighlevelClient;
	}

	public void setRestHeighlevelClient(RestHighLevelClient restHeighlevelClient) {
		this.restHeighlevelClient = restHeighlevelClient;
	}

	public EsClient() {
		init();
	}

	/**
	 * 初始化es
	 */
	public void init() {
		if (configBean == null) {
			configBean = ReadConfig.getConfig();
			// 获取分片,副本数量
			shards = configBean.getShards();
			replicas = configBean.getReplicas();
			// 解析hostlist配置信息
			String[] split = configBean.getHostname().split(",");
			// 创建HttpHost数组，其中存放es主机和端口的配置信息
			httpHostArray = new HttpHost[split.length];
			for (int i = 0; i < split.length; i++) {
				String item = split[i];
				httpHostArray[i] = new HttpHost(item.split(":")[0], Integer.parseInt(item.split(":")[1]),
						configBean.getScheme());
			}
		}
		builder = RestClient.builder(httpHostArray);
		restClient = builder.build();
		restHeighlevelClient = new RestHighLevelClient(builder);
		builder.setRequestConfigCallback(new RequestConfigCallback() {

			@Override
			public Builder customizeRequestConfig(Builder param1Builder) {
				param1Builder.setSocketTimeout(configBean.getSocketTimeout());
				param1Builder.setConnectTimeout(configBean.getConnectTimeout());
				param1Builder.setConnectionRequestTimeout(configBean.getConnectionRequestTimeout());
				return param1Builder;
			}
		});
	}

	// 多线程客户端
	private Runnable createTask(final int taskID) {
		return new Runnable() {
			public void run() {
				for (int i = 0; i < 1; i++) {
					EsClient client = new EsClient();
					log.info("begin");
					log.info(client.SearchAllDoc("a0", 0, 100));
//					client.CloseClient();
				}

			}
		};
	}

	public static void main(String[] args) {
		EsClient client = new EsClient();
//		for (int i = 0; i < 1; i++) {
//			log.info("bwj"+i);
//			new Thread(client.createTask(i)).start();
//
//			}
//		String aa;
		SimpleDateFormat sm = new SimpleDateFormat("yyyyMMdd");

		try {
			String indexName = "product";
//			
			EsBean bean = new EsBean();
			bean.setIndexName(indexName);
			Map<String, Object> range = new HashMap<String, Object>();
			Map<String, Object> min = new HashMap<String, Object>();
			min.put("MIN", 25);
			range.put("SALES", min);

//			min.put(ECM_DOC_MAX, 9);
//			min.put(ECM_DOC_MIN, "20220108");
//			min.put(ECM_DOC_MAX, "20220102");
//			range.put("BUSI_START_DATE", min);
//			range.put("size", min);
			bean.setRange(range);
			Map<String, String> must = new HashMap<String, String>();
			must.put("CONTENT_ID", "202204_108_AC191BC1-C3B9-0C83-FE5E-325098BD104A-1");
//			must.put("ECM_DOC_ID", "test0");
			bean.setPageNo(0);
			bean.setPageSize(10);
			bean.setMust(must);
//			bean.setShould(must);
			log.info(client.MatchSearchDoc(bean));
//			log.info("begin to searchAllDoc---");
//			log.info(client.SearchAllDoc(indexName, 0, 10));
//			client.DeleteIndex(indexName);
//			log.info(res);
//			log.info(client.SearchIndex(indexName));
//			log.info(client.SearchIndex(indexName));
//			String id = "20220407_16_71_629E5FAB-0ABA-33D0-DBB4-F8232C5D1B95-1_1_2995AD96-13D7-A295-3F92-AC2568012270";
//	client.SearchDoc(indexName, id,"1");
//			client.LogicalDeleteDoc(indexName, id);
//			client.LogicalDeleteDoc(indexName, "20220330_13_42_788BD487-3253-379B-333C-C197E2045428-1_1_CD4B67B1-DABC-8824-A943-56DC4A96D854");
//          Map<String, Object> resultMap = bb.getMap();
//			log.info(resultMap);
//			Iterator<String> it = resultMap.keySet().iterator();
//			while (it.hasNext()) {
//				String key = it.next();
//				Map<String, String> mm = (Map<String, String>) resultMap.get(key);
//				Iterator<String> ii = mm.keySet().iterator();
//				log.info(key + "-" + resultMap.get(key));
//				while (ii.hasNext()) {
//					String aasd = ii.next();
//					log.info(aasd + "-" + mm.get(aasd));
//				}

//			}
//			
//			
			List<EsMappingBean> mappingList = new ArrayList<EsMappingBean>();
			EsMappingBean mappingBean1 = new EsMappingBean();
			mappingBean1.setData("ECM_DOC_ID");
			mappingBean1.setType("keyword");
			mappingBean1.setLength(10);

			mappingBean1.setIsIndex("true");
			EsMappingBean mappingBean2 = new EsMappingBean();
			mappingBean2.setData("MODEL_CODE");
			mappingBean2.setType("text");
			mappingBean1.setLength(20);
			mappingBean1.setIsIndex("true");
			EsMappingBean mappingBean3 = new EsMappingBean();
			mappingBean3.setData("CONTENT_ID");
			mappingBean3.setType("keyword");
			EsMappingBean mappingBean4 = new EsMappingBean();
			mappingBean4.setData("BUSI_START_DATE");
			mappingBean4.setType("date");
			EsMappingBean mappingBean5 = new EsMappingBean();
			mappingBean5.setData("age");
			mappingBean5.setType("integer");
			EsMappingBean mappingBean6 = new EsMappingBean();
			mappingBean6.setData("size");
			mappingBean6.setType("float");
			mappingList.add(mappingBean1);
			mappingList.add(mappingBean2);
			mappingList.add(mappingBean3);
			mappingList.add(mappingBean4);
			mappingList.add(mappingBean5);
			mappingList.add(mappingBean6);
//			Result<String> aa = client.CreateIndex(indexName, mappingList);

//		log.info(client.SearchIndex(indexName));
//			client.PhysicalDeleteDoc(indexName, "test0");
//			client.DeleteIndex(indexName);
			List<HashMap<String, Object>> docDatas = new ArrayList<HashMap<String, Object>>();
			HashMap<String, Object> map = null;
			for (int i = 0; i < 10; i++) {
				map = new HashMap<String, Object>();
				map.put("ECM_DOC_ID", "test-" + i);
//				map.put("MODEL_CODE", "20120922");
				map.put("BUSI_START_DATE", "2022010"+i);
				map.put("age", i);
				map.put("size", 120+i);
				docDatas.add(map);
			}

//			 client.bulkAddDoc(indexName, docDatas);
//			client.PhysicalDeleteDoc(indexName, "test0");
//			
//			client.DeleteIndex(indexName);

//			String js = client.SearchIndex(indexName);
//			log.info(js);
//			JSONObject jjj = JSONObject.fromObject(js);
//			Iterator it = jjj.keys();
//			while (it.hasNext()) {
//				String key = (String) it.next();
//				log.info(key + "-" + jjj.get(key));
//				if (key.equals("MAPPING")) {
//					jjj.get("MAPPING");
//				}
//
//			}
//			log.info(js);

//			Result<String> res = client.bulkAddDoc(indexName, docDatas);

//			Result aa = client.SearchDoc(indexName, "test0");
//			if(aa.getMessage().equals(MsgCode.FALSE.getValue())) {
//				log.warn("is false");
//				log.warn(aa.getData());
//			}else {
//				log.info( aa.getData());
//			}
//			Result<String> aa = client.SearchAllDoc("kehu", 0, 10);
//			log.info(aa);
//			client.SearchDoc("user", "sunecmid");
//			aa = client.SearchAllDoc("user", 1, 20);
//			log.info(aa.getData());

//			log.info(aa.getData());
//			client.SearchDoc("user", "sunecmid");
//			client.DeleteDoc(indexName, "test0");
//			client.UploadDoc("user", map);
//			res.getMessage();
//			log.info(res);

//			Result<String> aa = client.CreateIndex("ecm13");
//			aa = client.DeleteIndex("ecm13");
//			aa = client.SearchIndex("usdder");
//			log.info(aa);
//			aa = client.SearchAllDoc("kehu", 0, 10);
//			log.info(aa);
//			String docId="20220316_79_14_0253B568-F828-36E5-9A35-372FFFF57909-1";
			 map = new HashMap();
			map.put(ECM_DOC_ID,"test-9");
			map.put(ECM_DOC_VERSION, "1");
			map.put("age", 99);
			
//			client.UpdateDoc(indexName, map);
//			Result aa = client.SearchDoc("kehu",docId);
//			log.info(aa);
//			String docId = "202204_108_3ABA8422-B036-F5D1-E2D9-48975529C60B-1_1";
//			log.info(client.SearchDoc(indexName, docId, "1"));

//		if(esBean.getMustNot()==null) {
//			Map addmap = new HashMap();
//			esBean.setMustNot(addmap);
//		}
//		esBean.getMustNot().put(ECM_DOC_STATUS,"0");
//			List<EsMappingBean> list = new ArrayList();
//			EsMappingBean a = new EsMappingBean();
//			a.setData("add");
//			a.setIsIndex("false");
//			a.setType("text");
//			list.add(a);
//			client.updateIndexMapping("aa", list);
			// String aa = client.SearchIndex("aa");
//			log.info(aa);

		} catch (Exception e) {
			log.error("", e);
		} finally {
			client.CloseClient();
		}
	}

	/**
	 * 关闭客户端
	 * 
	 * @return
	 */
	public void CloseClient() {
		// 获取客户端对象
		try {
			restHeighlevelClient.close();
			restClient.close();
		} catch (Exception e) {
			log.error("CloseClient error", e);
		}
	}

	/**
	 * 创建映射 indexName 索引名 list映射信息
	 * 
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public Result<String> updateIndexMapping(String indexName, List<EsMappingBean> list) {
		log.info("AddMapping->" + indexName + "]list="+list);
		Result<String> result = new Result<String>();
		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引不能为空!");
			log.error(indexName + "索引不能为空!");
			return result;
		}
		if (IsEmpty.isEmpty(list)) {
			log.warn("list is null");
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "创建映射信息为空!");
			return result;
		}
		// 判断索引是否存在,映射必须在存在的索引下才能创建
		try {
			if (!CheckIndexExist(indexName)) {
				result.setMessage("false");
				result.setData(indexName + "索引不存在!");
				log.error(indexName + "索引不存在!");
				return result;
			}
			XContentBuilder builder = GetMapping(list);
			PutMappingRequest request = new PutMappingRequest(indexName);
			request.type("_doc").source(builder);
			AcknowledgedResponse createMappingResponse = restHeighlevelClient.indices().putMapping(request,
					RequestOptions.DEFAULT);
			// 查看是否响应
			if (createMappingResponse.isAcknowledged()) {
				result.setMessage(MsgCode.SUCCESS.getValue());
				result.setData(indexName + "映射创建成功!");
				log.info("AddMapping success");
				return result;
			} else {
				result.setMessage(MsgCode.FALSE.getValue());
				result.setData(indexName + "映射创建失败!");
				log.error(indexName + "映射创建失败!");
				return result;
			}
		} catch (ElasticsearchException e1) {
			log.error("updateIndexMapping error", e1);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e1.getDetailedMessage());
			return result;

		} catch (Exception e) {
			log.error(indexName + "映射创建失败!", e);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e.getMessage());
			return result;
		}
	}

	/**
	 * 创建索引 indexName 索引名 list映射信息 shards 分片数(只能设置一次,主要作用是扩容)
	 * replicas分片数(可以多次设置,主要作用是备份)
	 * 
	 * @return
	 */
	@SuppressWarnings("deprecation")
	public Result<String> CreateIndex(String indexName, List<EsMappingBean> list) {
		log.info("CreateIndex->" + indexName + "]list="+list);
		Result<String> result = new Result<String>();
		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			log.error(indexName + "索引不能为空!");
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引不能为空!");
			return result;
		}
		if (IsEmpty.isEmpty(list)) {
			log.warn("list is null");
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "创建索引信息为空!");
			return result;
		}
		// 判断索引是否存在,防止空索引覆盖有数据索引
		try {
			if (CheckIndexExist(indexName)) {
				log.error(indexName + "索引重复创建!");
				result.setMessage("false");
				result.setData(indexName + "索引重复创建!");
				return result;
			}
			CreateIndexResponse createIndexResponse = null;
			CreateIndexRequest request = null;
			// 设置映射
			XContentBuilder builder = GetMapping(list);
			request = new CreateIndexRequest(indexName);
			request.mapping("_doc", builder);
			// 设置分片和副本
			request.settings(Settings.builder().put("index.number_of_shards", shards)// 分片数
					.put("index.number_of_replicas", replicas) // 副本数
			);
			createIndexResponse = restHeighlevelClient.indices().create(request, RequestOptions.DEFAULT);
			// 查看是否响应
			if (createIndexResponse.isAcknowledged()) {
				result.setMessage(MsgCode.SUCCESS.getValue());
				result.setData(indexName + "索引创建成功!");
				log.info("CreateIndex success");
				return result;
			} else {
				result.setMessage(MsgCode.FALSE.getValue());
				result.setData(indexName + "索引创建失败!");
				log.error(indexName + "索引创建失败!");
				return result;
			}
		} catch (ElasticsearchException e1) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e1.getDetailedMessage());
			log.error(indexName + "索引创建失败!", e1);
			return result;

		} catch (Exception e) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引创建失败!");
			log.error(indexName + "索引创建失败!", e);
			return result;
		}
	}

	/**
	 * 删除索引 indexName 索引名
	 * 
	 * @return
	 */
	public Result<String> DeleteIndex(String indexName) {
		log.info("DeleteIndex["+indexName+"]");
		Result<String> result = new Result<String>();
		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引不能为空!");
			log.error(indexName + "索引不能为空!");
			return result;
		}
		try {

			// 此处应该有判断索引下面是否有文档，如果有拒绝删除
			String msg = CheckDocExist(indexName);
			if ("true".equals(msg)) {
				log.warn(indexName + "该索引存在数据,不能删除!");
				result.setMessage("false");
				result.setData(indexName + "该索引存在数据,不能删除!");
				return result;
			} else if ("false".equals(msg)) {
				// 删除索引操作
				log.info("begin to delete index[" + indexName + "]");
				DeleteIndexRequest response = new DeleteIndexRequest(indexName);
				AcknowledgedResponse delete = restHeighlevelClient.indices().delete(response, RequestOptions.DEFAULT);
				if (delete.isAcknowledged()) {
					log.info("delete success");
					result.setMessage(MsgCode.SUCCESS.getValue());
					result.setData(indexName + "索引删除成功!");
				} else {
					log.error(indexName + "索引删除失败!");
					result.setMessage(MsgCode.FALSE.getValue());
					result.setData(indexName + "索引删除失败!");
					return result;
				}
			} else {
				log.info("msg["+msg);
				result.setMessage("false");
				result.setData(msg);
				return result;
			}
		} catch (ElasticsearchException e1) {
			log.error(indexName + "索引删除失败!", e1);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e1.getDetailedMessage());
			return result;
		} catch (Exception e) {
			log.error(indexName + "索引删除失败!", e);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e.getMessage());
			return result;
		}

		return result;
	}

	/**
	 * 查询索引 indexName 索引名
	 * 
	 * @return
	 */
	public String SearchIndex(String indexName) {
		log.info("SearchIndex["+indexName);
		// 判断是否为空
		JSONObject jsonObj = new JSONObject();
		if (IsEmpty.isEmpty(indexName)) {
			log.error(indexName + "索引不能为空!");
			jsonObj.put("SUCCESS", false);
			jsonObj.put("MSG", indexName + "索引不能为空!");
			return jsonObj.toString();
		}
		try {
			GetIndexRequest request = new GetIndexRequest(indexName);
			GetIndexResponse response = restHeighlevelClient.indices().get(request, RequestOptions.DEFAULT);
			CompressedXContent mapping = response.getMappings().get(indexName).source();
			jsonObj.put("SUCCESS", true);
			jsonObj.put("MAPPING", mapping.toString());
			jsonObj.put("SETTING", response.getSettings().toString());
			log.info("SearchIndex over");
		} catch (ElasticsearchException e1) {
			log.error("", e1);
			jsonObj.put("SUCCESS", false);
			if (ESExceptionStatus.NOT_FIND.equals(e1.status().name())) {
				jsonObj.put("MSG", indexName + ",索引不存在");
			} else {
				jsonObj.put("MSG", e1.getDetailedMessage());
			}
		} catch (Exception e) {
			log.error("error", e);
			jsonObj.put("SUCCESS", false);
			jsonObj.put("MSG", e.getMessage());
		}
		return jsonObj.toString();
	}

	/**
	 * 验证索引是否存在 indexName 索引名
	 * 
	 * @return
	 */
	private boolean CheckIndexExist(String indexName) {
		log.info("CheckIndexExist[" + indexName + "]");
		GetIndexRequest request = new GetIndexRequest(indexName);
		boolean exist;
		try {
			exist = restHeighlevelClient.indices().exists(request, RequestOptions.DEFAULT);
		} catch (Exception e) {
			log.error("", e);
			return false;
		}
		return exist;
	}

	/**
	 * 创建文档 indexName 索引名 map文档数据
	 * 
	 * @return
	 */
	public Result<String> UploadDoc(String indexName, Map<String, String> map) {
		log.info("UploadDoc");
		Result<String> result = new Result<String>();
		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引不能为空!");
			log.error(indexName + "索引不能为空!");
			return result;
		}
		if (IsEmpty.isEmpty(map)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData("文档数据不能为空!");
			log.error("文档数据不能为空!");
			return result;
		}
		// 新增文档 - 请求对象
		IndexRequest request = new IndexRequest();
		String docId = null;
		try {
			log.info("UploadDoc[" + map);
			docId = (String) map.get(ECM_DOC_ID);
			request.index(indexName).id(docId);
			// 创建数据对象
			ObjectMapper objectMapper = new ObjectMapper();
			String productJson = null;
			productJson = objectMapper.writeValueAsString(map);
			// 添加文档数据，数据格式为 JSON 格式
			request.source(productJson, XContentType.JSON);
			// 客户端发送请求，获取响应对象
			IndexResponse response = restHeighlevelClient.index(request, RequestOptions.DEFAULT);
			result.setMessage(MsgCode.SUCCESS.getValue());
			result.setData(response.getId());
			log.info("UploadDoc over");
		} catch (ElasticsearchException e1) {
			log.error(docId + "文档创建失败!", e1);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e1.getDetailedMessage());
		} catch (Exception e) {
			log.error(docId + "文档创建失败!", e);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e.getMessage());
		}
		return result;
	}

	/**
	 * 批量创建文档 indexName 索引名 docDatas 多条文档数据
	 * 
	 * @return
	 * @throws Exception
	 */
	public String bulkAddDoc(String indexName, List<HashMap<String, Object>> docDatas) throws Exception {
		log.info("indexName" + indexName);
		JSONObject json = new JSONObject();

		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			log.info(indexName + "索引不能为空!");
			json.put("SUCCESS", false);
			json.put("MSG", indexName + "索引不能为空!");
			return json.toString();
		}
		if (IsEmpty.isEmpty(docDatas)) {
			log.info("文档数据不能为空!");
			json.put("SUCCESS", false);
			json.put("MSG", "文档数据不能为空!");
			return json.toString();
		}
		try {
			BulkRequest bulkRequest = new BulkRequest();
			List<IndexRequest> list = generateRequestList(indexName, docDatas);
			for (IndexRequest re : list) {
				bulkRequest.add(re);
			}
			BulkResponse ss = restHeighlevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
			BulkItemResponse[] results = ss.getItems();
			boolean success = true;
			List<String> errorList = new ArrayList<String>();
			for (BulkItemResponse bi : results) {
				if (bi.getFailureMessage() != null) {
					log.error(bi.getFailureMessage());
					errorList.add(bi.getFailureMessage());
					success = false;
				}
			}
			if (!success) {
				json.put("MSG", errorList.toString());
			}
			json.put("SUCCESS", success);
			log.info("bulkAddDoc over");
		} catch (Exception e) {
			log.error("bulkAddDoc error", e);
			throw e;
		}
		return json.toString();
	}

	/**
	 * 将indexRequest格式的文档数据存入集合 indexName 索引名 docDatas 多条文档数据
	 * 
	 * @return
	 * @throws Exception
	 */
	private List<IndexRequest> generateRequestList(String indexName, List<HashMap<String, Object>> docDatas)
			throws Exception {
		if (docDatas == null || docDatas.size() == 0) {
			log.error("dodDatas is null");
			return null;
		}
		List<IndexRequest> requestList = new ArrayList<IndexRequest>();
		for (int i = 0; i < docDatas.size(); i++) {
			requestList.add(generateNewRequest(indexName, docDatas.get(i)));
		}
		return requestList;
	}

	/**
	 * 将文档数据封装为IndexRequest格式 indexName 索引名
	 * 
	 * @return
	 * @throws Exception
	 */
	private IndexRequest generateNewRequest(String indexName, HashMap<String, Object> hashMap) throws Exception {
		IndexRequest request = new IndexRequest();
		String productJson = null;
		try {
			String ecm_doc_id = (String) hashMap.get(ECM_DOC_ID);
			if (IsEmpty.isEmpty(ecm_doc_id)) {
				log.error("ECM_DOC_ID IS NULL");
				throw new Exception("ECM_DOC_ID IS NULL[" + hashMap + "]");
			}
			request.opType(DocWriteRequest.OpType.INDEX);
			request.index(indexName).id(ecm_doc_id);
			ObjectMapper objectMapper = new ObjectMapper();
			productJson = objectMapper.writeValueAsString(hashMap);
			log.info("json->" + productJson);
			request.source(productJson, XContentType.JSON);
		} catch (Exception e) {
			log.error("", e);
			throw e;
		}
		return request;
	}

	/**
	 * 修改文档 indexName 索引名 map文档数据
	 * 
	 * @return
	 * @throws Exception
	 */
	public Result<String> UpdateDoc(String indexName, Map map) throws Exception {
		Result<String> result = new Result<String>();
		// 判断是否为空
		try {
			log.info("indexName=" + indexName + "," + map);
			result = checkUpdateParames(indexName, map, result);
			if (!result.isSuccess()) {
				log.error(result.getData());
				return result;
			}

			// 修改文档 - 请求对象
			UpdateRequest request = new UpdateRequest();
			// 配置修改参数
			request.index(indexName).id((String) map.get(ECM_DOC_ID));
			// 移出传入的版本号
			map.remove(ECM_DOC_VERSION);
			// 设置请求体，对数据进行修改
			request.doc(map);
			// 客户端发送请求，获取响应对象
			restHeighlevelClient.update(request, RequestOptions.DEFAULT);
			result.setMessage(MsgCode.SUCCESS.getValue());
			result.setData("文档修改成功!");
			log.info(result);
			return result;
		} catch (Exception e) {
			log.error("文档修改失败!", e);
			throw e;
		}
	}

	private Result<String> checkUpdateParames(String indexName, Map<String, Object> map, Result<String> result) {
		log.debug("checkUpdateParames");
		result.setMessage(MsgCode.FALSE.getValue());
		if (IsEmpty.isEmpty(indexName)) {
			result.setData(indexName + "索引不能为空!");
			return result;
		}
		if (IsEmpty.isEmpty(map)) {
			result.setData("map数据不能为空!");
			return result;
		}
		String docId = (String) map.get(ECM_DOC_ID);
		if (IsEmpty.isEmpty(docId)) {
			result.setData(ECM_DOC_ID + "不能为空!");
			return result;
		}
		if (!CheckIndexExist(indexName)) {
			result.setData(indexName + "索引不存在!");
			return result;
		}
		// 获取上传前文档的版本
		String version = (String) map.get(ECM_DOC_VERSION);
		if (IsEmpty.isEmpty(version) || version.equals("-1")) {
			result.setData("文档版本不能为空!");
			return result;
		}
		if (!VersionController(indexName, docId, version)) {
			result.setData("文档版本不正确!");
			return result;
		}
		result.setSuccess(true);
		return result;
	}

	/**
	 * 物理删除文档
	 * 
	 * @return
	 */
	public Result<String> PhysicalDeleteDoc(String indexName, String docId) {
		Result result = new Result();
		log.info("PhysicalDeleteDoc" + indexName + ",docid[" + docId + "]");
		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引不能为空!");
			log.error(indexName + "索引不能为空!");
			return result;
		}
		if (IsEmpty.isEmpty(docId)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(ECM_DOC_ID + "文档id不能为空!");
			log.error(ECM_DOC_ID + "文档id不能为空!");
			return result;
		}
		try {
			// 创建请求对象
			DeleteRequest request = new DeleteRequest().index(indexName).id(docId);
			// 客户端发送请求，获取响应对象
			DeleteResponse response = restHeighlevelClient.delete(request, RequestOptions.DEFAULT);
			result.setMessage(MsgCode.SUCCESS.getValue());
			result.setData(docId + "-" + response.getResult());
			log.info(response.getResult());
		} catch (ElasticsearchException e1) {
			log.error(docId + "文档物理删除失败!", e1);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e1.getDetailedMessage());
			return result;
		} catch (Exception e) {
			log.error(docId + "文档物理删除失败!", e);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e.getMessage());
			return result;
		}
		log.info("PhysicalDeleteDoc over");
		return result;
	}

	/**
	 * 逻辑删除文档
	 * 
	 * @return
	 */
	public Result<String> LogicalDeleteDoc(String indexName, String docId) {
		Result result = new Result();
		log.info("LogicalDeleteDoc" + indexName + ",docid[" + docId + "]");
		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引不能为空!");
			log.error(indexName + "索引不能为空!");
			return result;
		}
		if (IsEmpty.isEmpty(docId)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(ECM_DOC_ID + "文档id不能为空!");
			log.error(ECM_DOC_ID + "文档id不能为空!");
			return result;
		}
		try {
			// 修改文档 - 请求对象
			UpdateRequest request = new UpdateRequest();
			// 配置修改参数
			Map map = new HashMap();
			map.put(ECM_DOC_STATUS, "0");
			request.index(indexName).id(docId);
			request.doc(map);
			restHeighlevelClient.update(request, RequestOptions.DEFAULT);
			result.setMessage(MsgCode.SUCCESS.getValue());
			result.setData("文档逻辑删除成功!");
		} catch (ElasticsearchException e1) {
			log.error(docId + "文档逻辑删除失败!", e1);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e1.getDetailedMessage());
			return result;
		} catch (Exception e) {
			log.error(docId + "文档逻辑删除失败!", e);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(e.getMessage());
			return result;
		}
		log.info("LogicalDeleteDoc over");
		return result;
	}

	/**
	 * 分页查询所有文档 pageNo 起始页 pageSize 每页数据条数
	 * 
	 * @return
	 */
	private Result<String> SearchAllDoc(String indexName, int pageNo, int pageSize) {
		SearchRequest request = new SearchRequest();
		Result result = new Result();
		log.info("indexName[" + indexName + ",pageNo[" + pageNo + ",pageSizer[" + pageSize + "]");
		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引不能为空!");
			log.error(indexName + "索引不能为空!");
			return result;
		}
		try {
			request.indices(indexName);
			SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
			// 查询所有数据
			sourceBuilder.query(QueryBuilders.matchAllQuery());
			// 分页查询,设置默认值,防止删除最后一页数据时,页码不及时改变
			pageNo = pageNo == 0 ? 0 : pageNo;
			pageSize = pageSize == 0 ? 10 : pageSize;
			// 当前页其实索引(第一条数据的顺序号)，from
			sourceBuilder.from(pageNo);
			// 每页显示多少条 size
			sourceBuilder.size(pageSize);
			request.source(sourceBuilder);
			SearchResponse response = null;
			log.info("begin to search");
			response = restHeighlevelClient.search(request, RequestOptions.DEFAULT);
			// 创建一个集合,用来存储es中查询到的数据
			List list = new ArrayList();
			// 查询匹配
			SearchHits hits = response.getHits();
			for (SearchHit hit : hits) {
				list.add(hit.getSourceAsString());
			}
			result.setMessage(MsgCode.SUCCESS.getValue());
			result.setData(list);
			log.info(list.size() + "-" + list);
		} catch (Exception e) {
			log.error("error", e);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData("文档查询失败!");
			return result;
		}
		// 查询匹配
		return result;

	}

	/**
	 * 查询文档
	 * 
	 * @param indexName 索引名称
	 * @param docId     文档id
	 * @param state     是否查询ECM_DOC_STATUS为1（未被删除），0为不查询，1为查询
	 * @return
	 */
	public Result SearchDoc(String indexName, String docId, String state) {
		log.info("indexName[" + indexName + ",docid[" + docId + "]");
		Result result = new Result();
		// 判断是否为空
		if (IsEmpty.isEmpty(indexName)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(indexName + "索引不能为空!");
			log.error(indexName + "索引不能为空!");
			return result;
		}
		if (IsEmpty.isEmpty(docId)) {
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(ECM_DOC_ID + "文档id不能为空!");
			log.error(ECM_DOC_ID + "文档id不能为空!");
			return result;
		}
		try {
			GetRequest request = new GetRequest().index(indexName).id(docId);
			GetResponse response = restHeighlevelClient.get(request, RequestOptions.DEFAULT);
			log.info(response);
			result.setMessage(Boolean.toString(response.isExists()));
			if (response.isExists()) {
				if (state.equals("1") && response.getSourceAsMap().get(ECM_DOC_STATUS).equals("0")) {
					log.info("该文档已经被逻辑删除");
					result.setMessage(MsgCode.FALSE.getValue());
					result.setData("该文档已经被逻辑删除!");
					return result;
				}
				Map<String, Object> map = response.getSourceAsMap();
				map.put(ECM_DOC_VERSION, response.getVersion() + "");
				result.setData(map);
			}
			log.info("SearchDoc over");
			return result;
		} catch (ElasticsearchException e1) {
			log.error("", e1);
			if (ESExceptionStatus.NOT_FIND.equals(e1.status().name())) {
				result.setData(indexName + "索引不存在");
			} else {
				result.setData(e1.getDetailedMessage());
			}
			result.setMessage(MsgCode.FALSE.getValue());
			return result;
		} catch (Exception e) {
			log.error("", e);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(docId + "文档查询失败!");
			return result;
		}
	}

	/**
	 * 多条件查询文档 esBean 查询条件 pageNo 页面 pageSize 页面大小 第一页pageNo为0，
	 * 
	 * @return
	 * @throws Exception
	 */
	public EsBean MatchSearchDoc(EsBean esBean) throws Exception {
		log.info("data" + esBean);
		EsBean returnBean = new EsBean();
		Map<String, Object> map = new HashMap<String, Object>();
		// 获取json对象中的indexName,pageNo,pageSize
		try {
			SearchRequest request = new SearchRequest();
			request.indices(esBean.getIndexName());
			SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
			/**
			 * bool查询,将组装查询条件
			 */
			BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
			// 范围查询
			if (!IsEmpty.isEmpty(esBean.getRange())) {
				// 第一个key为查询的字段
				String key = esBean.getRange().keySet().iterator().next();
				RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery(key);
				// range里面包含大于,小于的具体值
				Map range = (Map) esBean.getRange().get(key);
				// 判断是否为空
				if (range.get(ECM_DOC_MIN) != null) {
					rangeQueryBuilder.gte(range.get(ECM_DOC_MIN));
				}
				if (range.get(ECM_DOC_MAX) != null) {
					rangeQueryBuilder.lte(range.get(ECM_DOC_MAX));
				}
				boolQueryBuilder.must(rangeQueryBuilder);
			}
			// 必须包含
			 if (!IsEmpty.isEmpty(esBean.getMust())) {
				Iterator<String> it = esBean.getMust().keySet().iterator();
				while (it.hasNext()) {
					String key = it.next();
					boolQueryBuilder.must(QueryBuilders.matchQuery(key, esBean.getMust().get(key)));
				}
				// 一定不含
			}  if (!IsEmpty.isEmpty(esBean.getMustNot())) {
				Iterator<String> it = esBean.getMustNot().keySet().iterator();
				while (it.hasNext()) {
					String key = it.next();
					boolQueryBuilder.mustNot(QueryBuilders.matchQuery(key, esBean.getMustNot().get(key)));
				}
				// 可能包含
			}  if (!IsEmpty.isEmpty(esBean.getShould())) {
				Iterator<String> it = esBean.getShould().keySet().iterator();
				while (it.hasNext()) {
					String key = it.next();
					boolQueryBuilder.should(QueryBuilders.matchQuery(key, esBean.getShould().get(key)));
				}
			}
			searchSourceBuilder.query(boolQueryBuilder);
			// 分页查询,设置默认值,防止删除最后一页数据时,页码不及时改变
			int pageNo = esBean.getPageNo();
			int pageSize = esBean.getPageSize();
			pageNo = pageNo <= 0 ? 0 : pageNo;
			pageSize = pageSize <= 0 ? 10 : pageSize;
			searchSourceBuilder.from(pageNo);
			searchSourceBuilder.size(pageSize);
			request.source(searchSourceBuilder);
			SearchResponse response = restHeighlevelClient.search(request, RequestOptions.DEFAULT);
			// 创建一个集合,用来存储es中查询到的数据
			SearchHits hits = response.getHits();
			// 创建一个Map容器,里面装map
			for (SearchHit hit : hits) {
				map.put(hit.getId(), hit.getSourceAsMap());
			}
			returnBean.setIndexName(esBean.getIndexName());
			log.info("MatchSearchDoc over");
			log.info(returnBean);
		} catch (ElasticsearchException e1) {
			log.error("MatchSearchDoc", e1);
			map.put("FAIL_MSG", e1.getDetailedMessage());

		} catch (Exception e) {
			log.error("MatchSearchDoc", e);
			map.put("FAIL_MSG", e.getMessage());
		}
		returnBean.setMap(map);
		// 获取到结果
		return returnBean;
	}

	/**
	 * 获取文档版本
	 * 
	 * @return
	 */
	public synchronized Result<String> GetVersion(String indexName, String docId) {
		Result<String> result = new Result<String>();
		// 1.创建请求对象
		GetResponse response = null;
		try {
			log.info("GetVersion");
			GetRequest request = new GetRequest().index(indexName).id(docId);
			// 2.客户端发送请求，获取响应对象
			response = restHeighlevelClient.get(request, RequestOptions.DEFAULT);
			log.info("over");
		} catch (Exception e) {
			log.error("查询文件版本失败!", e);
			result.setMessage(MsgCode.FALSE.getValue());
			result.setData(docId + "文档查询失败!");
			return result;
		}
		result.setMessage(MsgCode.SUCCESS.getValue());
		result.setData(response.getVersion() + "");
		return result;
	}

	/**
	 * 判断文档是否可以修改 indexName 索引名 docId 文档id version 版本号
	 * 
	 * @return
	 */
	private boolean VersionController(String indexName, String docId, String version) {
		// 创建原子类
		Result<String> doc = GetVersion(indexName, docId);
		String preVersion = doc.getData() + "";
		// 比较并交换,判断俩个版本是否一致
		if (Integer.valueOf(preVersion) == Integer.valueOf(version)) {
			return true;
		} else {
			log.error("es version=" + doc.getData() + ",client version=" + version);
		}
		return false;
	}

	/**
	 * 判断索引下是否含有文档
	 * 
	 * @return false表示没有文档，返回“true”表示有文档或报错无法删除，“返回NOT_FOUND表示索引不存在
	 */
	private String CheckDocExist(String indexName) {
		SearchRequest request = new SearchRequest();
		try {
			log.info("CheckDocExist" + indexName);
			request.indices(indexName);
			SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
			// 查询所有数据
			sourceBuilder.query(QueryBuilders.matchAllQuery());
			// 由于仅仅是判断有无数据,页码和大小写死
			sourceBuilder.from(0);
			sourceBuilder.size(1);
			request.source(sourceBuilder);
			SearchResponse response = restHeighlevelClient.search(request, RequestOptions.DEFAULT);
			SearchHits hits = response.getHits();
			log.info("CheckDocExist over");
			if (hits.getTotalHits().value == 0) {
				return "false";
			} else {
				return "true";
			}
		} catch (ElasticsearchException e1) {
			log.error("", e1);
			return e1.getDetailedMessage();

		} catch (Exception e) {
			log.error("", e);
			return "error";
		}
	}

	/**
	 * 拼接字段的映射 EsMappingBean 映射参数
	 * 
	 * @throws Exception
	 */
	private XContentBuilder GetMapping(List<EsMappingBean> list) throws Exception {
		XContentBuilder builder = null;
		try {
			builder = XContentFactory.jsonBuilder();
			// 设置type和properties(默认)
			builder.startObject().startObject("properties");
			// 设置字段类型
			for (int i = 0; i < list.size(); i++) {
				// 如果字段类型为keyword,text则可以设置长度
				if (list.get(i).getType().equals("keyword") && list.get(i).getLength() != null
						&& list.get(i).getLength() != 0) {
					builder.startObject(list.get(i).getData()).field("type", "keyword")
							.field("ignore_above", list.get(i).getLength())
							.field("index", list.get(i).getIsIndex() == null ? "true" : list.get(i).getIsIndex())
							.endObject();
					// 如果字段类型不为keyword,text则不可以设置长度
				} else if (list.get(i).getType().equals("date")) {
					builder.startObject(list.get(i).getData()).field("type", "date")
//					"gdg20120101"
							.field("format", "yyyyMMdd")
							.field("index", list.get(i).getIsIndex() == null ? "true" : list.get(i).getIsIndex())
							.endObject();
				} else {
					builder.startObject(list.get(i).getData())
							.field("type", list.get(i).getType() == null ? "keyword" : list.get(i).getType())
							.field("index", list.get(i).getIsIndex() == null ? "true" : list.get(i).getIsIndex())
							.endObject();
				}

			}
			// 设置对应的结束
			builder.endObject();
			builder.endObject();
		} catch (Exception e) {
			log.error("拼接字段的映射失败!", e);
			throw e;
		}
		return builder;
	}

}
