package com.sunyard.console.poolmanage.action;

import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.Constant;
import com.sunyard.console.common.util.JSONUtil;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.jasypt.util.text.BasicTextEncryptor;

import com.sequoiadb.datasource.ConnectStrategy;
import com.sequoiadb.datasource.DatasourceOptions;
import com.sequoiadb.net.ConfigOptions;
import com.sunyard.console.contentservermanage.action.ContentServerManageAction;
import com.sunyard.console.poolmanage.bean.PoolInfoBean;
import com.sunyard.console.poolmanage.dao.PoolManegerDAO;
import com.sunyard.console.threadpoool.IssueUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;

/**
 * 连接池配置
 * 
 * <AUTHOR>
 * 
 */
@Controller
public class PoolManagerAction extends BaseAction {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private int state;// 状态
	private ConfigOptions nwOpt = new ConfigOptions(); // 定义连接选项
	private DatasourceOptions dsOpt = new DatasourceOptions(); // 定义连接池选项
	@Autowired
	private PoolManegerDAO poolManegerDAO;
	private int sdb_connection_id;// 连接池id
	private String urls;// 连接池地址
	private String so_maxidenum;// 保留连接数量
	private String co_connecttimeout;// 失败超时时间
	private String so_maxconnectionnum;// 最大连接数
	private String co_maxautoconnectretrytime;// 失败重试时间
	private String so_deltainccount;// 增加连接数
	private String so_abandontime;// 连接存活时间
	private String so_recheckcycleperiod;// 清除周期
	private String optionFlag;// 判断新增修改
	private String sdb_connection_ids;// 多个连接池id
	private String poolname;// 连接池名称
	private String so_syncCoordInterval;// 同步周期
	private int validateConnection;// 连接出池时，是否检测连接的可用性，默认不检测。
	private int start;
	private int limit;
	private String username;// 用户名
	private String password;// 登陆密码

	public String getSo_syncCoordInterval() {
		return so_syncCoordInterval;
	}

	public void setSo_syncCoordInterval(String so_syncCoordInterval) {
		this.so_syncCoordInterval = so_syncCoordInterval;
	}

	public int getValidateConnection() {
		return validateConnection;
	}

	public void setValidateConnection(int validateConnection) {
		this.validateConnection = validateConnection;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public ConfigOptions getNwOpt() {
		return nwOpt;
	}

	public void setNwOpt(ConfigOptions nwOpt) {
		this.nwOpt = nwOpt;
	}

	public DatasourceOptions getDsOpt() {
		return dsOpt;
	}

	public void setDsOpt(DatasourceOptions dsOpt) {
		this.dsOpt = dsOpt;
	}

	public String getPoolname() {
		return poolname;
	}

	public void setPoolname(String poolname) {
		this.poolname = poolname;
	}

	public String getSdb_connection_ids() {
		return sdb_connection_ids;
	}

	public void setSdb_connection_ids(String sdb_connection_ids) {
		this.sdb_connection_ids = sdb_connection_ids;
	}

	public int getSdb_connection_id() {
		return sdb_connection_id;
	}

	public void setSdb_connection_id(int sdb_connection_id) {
		this.sdb_connection_id = sdb_connection_id;
	}

	public String getUrls() {
		return urls;
	}

	public void setUrls(String urls) {
		this.urls = urls;
	}

	public String getSo_maxidenum() {
		return so_maxidenum;
	}

	public void setSo_maxidenum(String so_maxidenum) {
		this.so_maxidenum = so_maxidenum;
	}

	public String getCo_connecttimeout() {
		return co_connecttimeout;
	}

	public void setCo_connecttimeout(String co_connecttimeout) {
		this.co_connecttimeout = co_connecttimeout;
	}

	public String getSo_maxconnectionnum() {
		return so_maxconnectionnum;
	}

	public void setSo_maxconnectionnum(String so_maxconnectionnum) {
		this.so_maxconnectionnum = so_maxconnectionnum;
	}

	public String getCo_maxautoconnectretrytime() {
		return co_maxautoconnectretrytime;
	}

	public void setCo_maxautoconnectretrytime(String co_maxautoconnectretrytime) {
		this.co_maxautoconnectretrytime = co_maxautoconnectretrytime;
	}

	public String getSo_deltainccount() {
		return so_deltainccount;
	}

	public void setSo_deltainccount(String so_deltainccount) {
		this.so_deltainccount = so_deltainccount;
	}

	public String getSo_abandontime() {
		return so_abandontime;
	}

	public void setSo_abandontime(String so_abandontime) {
		this.so_abandontime = so_abandontime;
	}

	public String getSo_recheckcycleperiod() {
		return so_recheckcycleperiod;
	}

	public void setSo_recheckcycleperiod(String so_recheckcycleperiod) {
		this.so_recheckcycleperiod = so_recheckcycleperiod;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}
	private  final static Logger log = LoggerFactory.getLogger(ContentServerManageAction.class);

	public PoolManegerDAO getPoolManegerDAO() {
		return poolManegerDAO;
	}

	public void setPoolManegerDAO(PoolManegerDAO poolManegerDAO) {
		this.poolManegerDAO = poolManegerDAO;
	}

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	/**
	 * 分页查询连接池list，将结果集组合成jsonStr，返回给页面
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/getContentpoolListAction.action", method = RequestMethod.POST)
	public String getContentpoolList(HttpServletRequest servletRequest,int start,int limit) {
		log.info("--getContentpoolList(start)-->sdb_connection_id:"
				+ sdb_connection_id);
		String sdb_connection_id_str = servletRequest.getParameter("sdb_connection_id");
		int sdb_connection_id =0;
		if (StringUtils.isNotEmpty(sdb_connection_id_str)){
			sdb_connection_id = Integer.parseInt(sdb_connection_id_str);
		}
		String jsonStr = null;
		try {
			// 分页查询连接池信息
			List<PoolInfoBean> contentPoolInfoList = poolManegerDAO
					.getContentpoolList(sdb_connection_id, start + 1, limit);

			// 查询所有连接池信息
			List<PoolInfoBean> AllInfoList = poolManegerDAO
					.getContentpoolList(sdb_connection_id);
			int size = 0;
			if (AllInfoList != null && AllInfoList.size() > 0) {
				// 得到连接池的数量
				size = AllInfoList.size();
			}
			// 把连接池信息集合转化成json字符串
			jsonStr = new JSONUtil().createJsonDataByColl(contentPoolInfoList,
					size, new PoolInfoBean());
		} catch (Exception e) {
			// 封装查询失败信息
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取连接池失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("连接池配置->查询连接池列表失败：" + e.toString(), e);
		}
		// 输出查询结果
		this.outJsonString(jsonStr);
		log.info("--getContentPoolList(over)");
		return null;

	}

	/**
	 * 新增修改连接池
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/addContentpoolAction.action", method = RequestMethod.POST)
	public String addContentpool(HttpServletRequest servletRequest,String username, String password, String so_maxconnectionnum,
								 String urls, int state, String so_maxidenum, String so_deltainccount, String so_abandontime,
								 int validateConnection, String so_recheckcycleperiod, String co_connecttimeout, String co_maxautoconnectretrytime,
								 String so_syncCoordInterval, String poolname, String optionFlag) {
		log.info("--addContentpool(start)-->:");
		String sdb_connection_id_str = servletRequest.getParameter("sdb_connection_id");
		int sdb_connection_id =0;
		if (StringUtils.isNotEmpty(sdb_connection_id_str)){
			sdb_connection_id = Integer.parseInt(sdb_connection_id_str);
		}
		JSONObject jsonResp = new JSONObject();
		BasicTextEncryptor cryptor = new BasicTextEncryptor();
		cryptor.setPassword(Constant.ENCRYPTED_ORDER);
		String usern = cryptor.encrypt(username);
		String passw = cryptor.encrypt(password);

		String jsonStr = null;
		// 创建连接池信息对象
		PoolInfoBean bean = new PoolInfoBean();
		bean.setSdb_connection_id(sdb_connection_id);// 连接池id
		bean.setSo_maxconnectionnum(so_maxconnectionnum);// 最大连接数
		bean.setUrls(urls);// 连接池地址
		bean.setState(state);// 状态
		bean.setSo_maxidenum(so_maxidenum);// 保留连接数量
		bean.setSo_deltainccount(so_deltainccount);// 增加连接数
		bean.setSo_abandontime(so_abandontime);// 连接存活时间
		bean.setValidateConnection(validateConnection);// 初始连接数
		bean.setSo_recheckcycleperiod(so_recheckcycleperiod);// 清除周期
		bean.setCo_connecttimeout(co_connecttimeout);// 失败超时时间
		bean.setCo_maxautoconnectretrytime(co_maxautoconnectretrytime);// 失败重试时间
		bean.setSo_syncCoordInterval(so_syncCoordInterval);// 同步周期
		bean.setPoolname(poolname);// 连接池名称
		bean.setPassword(passw);// 登陆密码
		bean.setUsername(usern);// 用户名

		try {
			log.debug("--addContentpool-->optionFlag:" + optionFlag);
			int result = 0;
			// 判断新增还是更新
			if (optionFlag != null && optionFlag.equals("create1")) {// 新增连接池信息
				// 开始新增连接池
				result = poolManegerDAO.addContentPool(bean);
			} else if (optionFlag != null && optionFlag.equals("update1")) {// 修改连接池信息
				// 开始更新连接池
				result = poolManegerDAO.updateContentPool(bean);
			}
			log.debug("--addContentServer-->result:" + result);
			// 查询已激活的连接池
			if (result != 0) {
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				//new Thread(new SendContentSdbPoolThread()).start();
				log.debug("--addContentpool-->" + "新增修改连接池下发成功");
				// 封装成功的信息
				jsonResp.put("success", true);
				jsonResp.put("message", "连接池配置成功!!");

			} else {
				// 封装失败的信息
				jsonResp.put("success", false);
				jsonResp.put("message", "连接池配置失败!!");
			}
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			// 封装失败信息
			jsonResp.put("success", false);
			jsonResp.put("message", "连接池配置失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("连接池配置->新增连接池失败：" + e.toString(), e);

		}
		// 数据输出到页面
		this.outJsonString(jsonStr);
		log.info("--addContentPool(over)");

		return null;

	}

	/**
	 * 禁用连接池
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/stopContentpoolAction.action", method = RequestMethod.POST)
	public String stopContentpool(String sdb_connection_ids) {
		log.info("--stopContentpool(start)-->sdb_connection_ids:"
				+ sdb_connection_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			// 开机禁用连接池
			boolean result = poolManegerDAO.stopContentPool(sdb_connection_ids);
			log.debug("--stopContentServer-->result:" + result);
			if (result) {
				// 封装成功的信息
				jsonResp.put("success", true);
				jsonResp.put("message", "停用连接池成功!!");
				jsonStr = jsonResp.toString();
			} else {
				// 封装失败的信息
				jsonResp.put("success", false);
				jsonResp.put("message", "停用连接池失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			// 封装失败的信息
			jsonResp.put("success", false);
			jsonResp.put("message", "停用连接池失败!!");
			jsonStr = jsonResp.toString();
			log.error("连接池管理->禁用连接池失败：" + e.toString(), e);

		}
		// 数据输出到页面
		this.outJsonString(jsonStr);
		log.debug("--stopContentPool(over)");
		return null;
	}

	/**
	 * 激活连接池
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/startContentPoolAction.action", method = RequestMethod.POST)
	public String startContentpool(String sdb_connection_ids) {
		log.info("--startContentpool-->sdb_connection_ids:"
				+ sdb_connection_ids);

		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			// 开始激活连接池
			boolean result = poolManegerDAO
					.startContentPool(sdb_connection_ids);
			log.debug("--startContentPool-->result:" + result);
			if (result) {
				// 封装成功信息
				jsonResp.put("success", true);
				jsonResp.put("message", "启用连接池成功!!");
				jsonStr = jsonResp.toString();
			} else {
				// 封装失败信息
				jsonResp.put("success", false);
				jsonResp.put("message", "启用连接池失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			// 封装失败信息
			jsonResp.put("success", false);
			jsonResp.put("message", "启用连接池失败!!");
			jsonStr = jsonResp.toString();
			log.error("连接池管理->启用连接池失败：" + e.toString(), e);

		}
		// 数据输出到页面
		this.outJsonString(jsonStr);
		log.info("--startContentPool(over)");
		return null;
	}

	/**
	 * 测试连接池
	 * 
	 * @return
	 */
	@RequestMapping(value = "/poolManage/testContentpoolAction.action", method = RequestMethod.POST)
	public String testContentpool(String username,String password,String so_maxconnectionnum,
								  String urls,String so_maxidenum,String so_deltainccount,String so_abandontime,
								  int validateConnection,String so_recheckcycleperiod,String co_connecttimeout,String co_maxautoconnectretrytime,
								  String so_syncCoordInterval) {
		log.info("--testContentpool(start)-->urls" + urls);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		nwOpt.setConnectTimeout(Integer.parseInt(co_connecttimeout)); // 设置若连接失败，超时时间（ms）
		nwOpt.setMaxAutoConnectRetryTime(Integer
				.parseInt(co_maxautoconnectretrytime));// 设置若连接失败，重试时间
		dsOpt.setMaxCount(Integer.parseInt(so_maxconnectionnum)); // 设置连接池最大连接数
		dsOpt.setDeltaIncCount(Integer.parseInt(so_deltainccount)); // 当池中没有可用连接时，增加连接的数量
		dsOpt.setMaxIdleCount(Integer.parseInt(so_maxidenum)); // 周期清理多余的空闲连接时，应保留连接的数量
		dsOpt.setKeepAliveTimeout(Integer.parseInt(so_abandontime)); // 连接存活时间，当连接空闲时间超过连接存活时间，将被连接池丢弃
		dsOpt.setCheckInterval(Integer.parseInt(so_recheckcycleperiod)); // 清除多余空闲连接的周期
		dsOpt.setSyncCoordInterval(Integer.parseInt(so_syncCoordInterval)); // 向catalog同步coord地址的周期。单位:毫秒。
		// 0表示不同步
		dsOpt.setValidateConnection(validateConnection == 0 ? false : true); // 连接出池时，是否检测连接的可用性，默认不检测。
		dsOpt.setConnectStrategy(ConnectStrategy.BALANCE); // 默认使用coord地址负载均衡的策略获取连接。
		try {
			// 开始测试连接池
			boolean result = poolManegerDAO.testContentPool(urls, password,
					username, nwOpt, dsOpt);
			log.debug("--testContentPool-->result:" + result);
			if (result) {
				// 封装成功的信息
				jsonResp.put("success", true);
				jsonResp.put("message", "测试连接池成功!!");
				jsonStr = jsonResp.toString();
			} else {
				// 封装失败的信息
				jsonResp.put("success", false);
				jsonResp.put("message", "测试连接池失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			// 封装失败的信息
			jsonResp.put("success", false);
			jsonResp.put("message", "测试连接池失败!!");
			jsonStr = jsonResp.toString();
			log.error("连接池管理->测试连接池失败：" + e.toString(), e);

		}
		// 数据输出到页面
		this.outJsonString(jsonStr);
		log.info("--testContentPool(over)-->" + urls);
		return null;

	}
}
