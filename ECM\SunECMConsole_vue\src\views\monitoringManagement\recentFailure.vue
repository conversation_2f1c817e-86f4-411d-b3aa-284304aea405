<template>
    <div class="app-container">
        <div class="filter-container">
           批次号:
           <el-input
              v-model="listQuery.content_id"
              placeholder="批次号"
              style="width: 200px"
              class="filter-item"
              @keyup.enter.native="handleFilter"
            />
            模型代码:
            <el-input
              v-model="listQuery.model_code"
              placeholder="模型代码"
              style="width: 200px"
              class="filter-item"
              @keyup.enter.native="handleFilter"
            />
            失败时间:
            <el-date-picker v-model="listQuery.fail_time" type="date" placeholder="选择日期" @change="dataSearch" value-format="yyyy-MM-dd"></el-date-picker>
            <el-button
              v-waves
              class="filter-item"
              type="primary"
              icon="el-icon-search"
              size="mini"
              plain 
              round 
              @click="handleFilter"
            >
              查询
            </el-button>
            <el-button
              v-waves
              class="filter-item"
              type="warning"
              icon="el-icon-delete-solid"
              size="mini"
              plain 
              round 
              @click="handleclear"
            >
              清空
            </el-button>
        </div>
        <el-table
        :key="tableKey"
        v-loading="listLoading"
        :data="list"
        border
        fit
        highlight-current-row
        style="width: 100%"
        >
          <el-table-column label="批次号" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.content_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模型代码" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.model_code }}</span>
            </template>
          </el-table-column>
          <el-table-column label="版本号" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.version }}</span>
            </template>
          </el-table-column>
          <el-table-column label="近线失败时间" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.fail_time }}</span>
            </template>
          </el-table-column>
          <el-table-column label="失败信息" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.error_case }}</span>
            </template>
          </el-table-column>
          <el-table-column label="批次表名" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.table_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="组id" style="width: 100%" align="center" v-if="false">
            <template slot-scope="{ row }">
            <span>{{ row.group_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="last_time" style="width: 100%" align="center" v-if="false">
            <template slot-scope="{ row }">
            <span>{{ row.last_time }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            style="width: 100%"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="{ row, $index }">
               <el-button type="primary" icon="el-icon-upload" size="mini" @click="handleReNearLineFaileBatch(row)"  v-if="hasPerm('reNearLine')">
                  重新近线
                </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.start"
          :limit.sync="listQuery.limit"
          @pagination="getList"
        />

        <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="提示信息" :visible.sync="warmVisiblePage" >
            确定进行重新近线吗?
            <div slot="footer" class="dialog-footer">
              <el-button @click="warmVisiblePage = false"> 取消 </el-button>
              <el-button type="primary" @click="reNearLineFaileBatch()">提交</el-button>
            </div>
        </el-dialog>
        <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="提示信息" :visible.sync="warmVisiblePage2" >
            重新近线成功!!
            <div slot="footer" class="dialog-footer">
              <el-button @click="reNearLineSucess()"> 确定 </el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getNearLineFailListAction,reNearLineFaileBatchAction  } from '@/api/nearLineFailManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
export default {
 name: "ComplexTable",
 components: { Pagination },
 directives: { waves,elDragDialog },
 data() {
  return {
     tableKey: 0,
     list: null,
     total: 0,
     listLoading: true,
     listQuery: {
         start: 1,
         limit: 20,
         content_id:'',
         model_code:'',
         fail_time:''
     },
     warmVisiblePage:false,
     warmVisiblePage2:false,
     reNearLineMsg:''
  }
 },
 created() {
  this.initData({});
  this.getList()
 },
 methods: {
   async initData(data) {
      //获取当前时间
      var now   = new Date();
      var monthn = now.getMonth()+1;
      var yearn  = now.getFullYear();
      var dayn = now.getDate();
      this.selectDay = yearn+"-"+monthn+"-"+dayn;

      this.selectUser = parseInt(sessionStorage.getItem("userid"));
      this.getListByDay();
    },
   dataSearch() {
     this.getListByDay();
   },
   async getListByDay(data) {

   },
   getList() {
      this.listLoading = true
      getNearLineFailListAction(this.listQuery).then(response => {
        this.list = response.root
        this.total = Number(response.totalProperty)
        setTimeout(() => {
          this.listLoading = false
        }, 1 * 100)
      })
   },
   handleclear() {
      this.listQuery.content_id = "";
      this.listQuery.model_code = "";
      this.listQuery.fail_time = "";
    },
    handleFilter() {
      this.listQuery.start = 1
      this.getList()
    },
    handleReNearLineFaileBatch(row){
       this.reNearLineMsg=''
       this.warmVisiblePage=true
       this.reNearLineMsg=row.content_id+'>>'+row.group_id+'>>'+row.version+'>>'+row.table_name+';'
    },
    reNearLineFaileBatch(){
        reNearLineFaileBatchAction(this.reNearLineMsg).then(response => {
              this.warmVisiblePage2=true
        })
    },
    reNearLineSucess(){
        this.warmVisiblePage=false
        this.warmVisiblePage2=false
        this.listQuery.start = 1
        this.getList()
    }

 }

}
</script>

<style scoped>

</style>
