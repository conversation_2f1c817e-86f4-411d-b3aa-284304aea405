package com.sunyard.console.unityaccessservermanage.thread;

import com.sunyard.console.common.util.ServerType;
import com.sunyard.console.common.util.SpringUtil;
import com.sunyard.console.configmanager.bean.NodeInfo;
import com.sunyard.console.configmanager.wsserviceutil.WsBeanInterface;
import com.sunyard.console.contentmodelmanage.thread.SendContentModelThread;
import com.sunyard.console.monitor.statusManage.bean.ConfigInfoSynchroBean;
import com.sunyard.console.monitor.statusManage.dao.StatusManageDao;
import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerInfoBean;
import com.sunyard.console.unityaccessservermanage.dao.UnityAccessServerManageDAO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Title: 下发统一接入服务器信息线程
 * </p>
 * <p>
 * Description: 将统一接入服务器信息下发给所有启用的统一接入服务器
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SendUnityAccessServerThread implements Runnable {
	/**
	 * 统一接入服务器id
	 */
	private int id;
	/**
	 * 下发类型，1：新增 2：删除 3：修改，4：启用 5:禁用
	 */
	private int modify_type;
	/**
	 * 日志对象
	 */
	private final static  Logger log = LoggerFactory.getLogger(SendUnityAccessServerThread.class);
	/**
	 * 下发统一接入服务器线程
	 * 
	 * @param id
	 *            服务器id
	 * @param modify_type
	 *            下发类型，1：新增 2：删除 3：修改，4：启用,5:禁用
	 */
	public SendUnityAccessServerThread(int id, int modify_type) {
		this.id = id;
		this.modify_type = modify_type;
	}

	public void run() {
		log.info( "---->run start modify_type:" + modify_type + ";id:" + id);
		if (modify_type == 4 || modify_type == 5) {
			sendStartOrStopServerInfo(id, modify_type);
		} else {
			sendServerInfo(id, modify_type);
		}
		log.info( "--run end modify_type:" + modify_type + ";id:" + id);
	}

	/**
	 * 下发”新增“，“修改”，统一接入服务器信息到所有启用的统一接入服务器
	 * 
	 * @param id
	 *            服务器id
	 * @param modify_type
	 *            下发类型，1：新增， 2：删除 3：修改，4：启用或禁用
	 * @return
	 */
	public void sendServerInfo(int id, int modify_type) {
		log.info( "--sendServerInfo(start)-->id:" + id+";modify_type:"+modify_type);
		// 从spring获取ContentServerManageDAO类 注入
		UnityAccessServerManageDAO unityAccessServerManageDao = (UnityAccessServerManageDAO) SpringUtil.ctx
				.getBean("unityAccessServerManageDao");
		// 从spring获取wsUnityAccess类 注入
		WsBeanInterface wsUnityAccess = (WsBeanInterface) SpringUtil.ctx
				.getBean("wsUnityAccess");
		StatusManageDao statusManageDao = (StatusManageDao) SpringUtil.ctx
				.getBean("statusManageDao");

		// 获取该服务器的信息
		List<UnityAccessServerInfoBean> myServerList = unityAccessServerManageDao
				.getUnityAccessServerList(id, null);
		if (myServerList == null || myServerList.size() == 0) {
			return;
		}

		List<NodeInfo> sendBeanList = new ArrayList<NodeInfo>();
		UnityAccessServerInfoBean bean = myServerList.get(0);
		if (bean == null) {
			return;
		}
		NodeInfo sendBean = new NodeInfo();
		sendBean.setHttp_port(Integer.toString(bean.getHttp_port()));
		sendBean.setRemark(bean.getRemark());
		sendBean.setServer_id(Integer.toString(bean.getServer_id()));
		sendBean.setServer_ip(bean.getServer_ip());
		sendBean.setServer_name(bean.getServer_name());
		sendBean.setSocket_port(Integer.toString(bean.getSocket_port()));
		sendBean.setState(Integer.toString(bean.getState()));
		sendBeanList.add(sendBean);
		log.debug( "--sendServerInfo-->sendBeanList:"+sendBeanList);
		List<UnityAccessServerInfoBean> unityServerList = unityAccessServerManageDao
				.getUnityAccessServerList(bean.getServer_id(), true);
		if (unityServerList == null) {
			log.debug( "--sendServerInfo-->unityServerList is null!");
			return;
		}
		log.debug( "--sendServerInfo-->unityServerList:"+unityServerList);
		List<String[]> list = null;
		for (int i = 0; i < unityServerList.size(); i++) {
			UnityAccessServerInfoBean server = unityServerList.get(i);
			ConfigInfoSynchroBean conBean = new ConfigInfoSynchroBean();
			conBean.setConfig_type("7");// 统一接入服务器
			conBean.setNode_id(server.getServer_id());
			conBean.setConfig_code(Integer.toString(id));
			if (modify_type == 1) {
				conBean.setRemark("新增统一接入服务器信息");
			} else {
				conBean.setRemark("修改统一接入服务器信息");
			}
			conBean.setModify_type(Integer.toString(modify_type));
			conBean.setConfig_table("UNITY_ACCESS_SERVER");// 统一接入服务器表
			conBean.setTarget_server_type(2);
			try {
				list = new ArrayList<String[]>();
				Map<String, String> map = SendContentModelThread.getUnityAccessTransPort(server);
				String[] s = new String[]{server.getServer_ip(),map.get("sendPort"), server.getServer_id() + "",map.get("type"),
						ServerType.UA};
				list.add(s);
				Map m = wsUnityAccess.setNodeInfo(sendBeanList, list);
				String isSuccess = (String) m.get(Integer.toString(server
						.getServer_id()));
				conBean.setSynchro_status(isSuccess);
				log.info( "--sendServerInfo(success)-->server info::" + list);
			} catch (Exception e) {
				//记录日志
				log.error( "下发统一接入服务器信息失败:" + e.toString(), e);
				// 记录下发信息
				conBean.setSynchro_status("0");
			} finally {
				statusManageDao.addSyncStatusInfo(conBean);
				log.debug( "--sendServerInfo-->sendBean:"+sendBean);
			}
		}
	}


	/**
	 * 下发"启用","禁用"服务器信息到所有启用的服务器和启用的统一接入服务器
	 * 
	 * @param id
	 *            服务器id
	 * @param state
	 *            启用禁用标识，0为禁用，1为启用
	 * @return
	 */
	public void sendStartOrStopServerInfo(int id, int modifyType) {
		log.info( "--sendStartOrStopServerInfo(start)-->id:" + id+";modify_type:"+modify_type);
		// 从spring获取ContentServerManageDAO类 注入
		UnityAccessServerManageDAO unityAccessServerManageDao = (UnityAccessServerManageDAO) SpringUtil.ctx
				.getBean("unityAccessServerManageDao");
		// 从spring获取WSClient类 注入
		WsBeanInterface wsUnityAccess = (WsBeanInterface) SpringUtil.ctx
				.getBean("wsUnityAccess");
		StatusManageDao statusManageDao = (StatusManageDao) SpringUtil.ctx
				.getBean("statusManageDao");

		// 获取所有启用的统一接入服务器列表
		List<UnityAccessServerInfoBean> unityServerList = unityAccessServerManageDao
				.getUnityAccessServerList(id, true);
		log.debug( "--sendStartOrStopServerInfo-->unityServerList:"+unityServerList);
		List<String[]> list = null;
		StringBuffer sendMsg = new StringBuffer();
		int state = modifyType == 4 ? 1 : 0;
		// 统一接入下发状态改变需要和内容服务器区别，所以在serverId前加UA
		sendMsg.append("UA"+id).append(":").append(state);

		for (int i = 0; i < unityServerList.size(); i++) {
			UnityAccessServerInfoBean server = unityServerList.get(i);
			ConfigInfoSynchroBean conBean = new ConfigInfoSynchroBean();
			conBean.setConfig_type("7");// 统一接入服务器
			conBean.setNode_id(server.getServer_id());
			conBean.setConfig_code(Integer.toString(server.getServer_id()));
			if (state == 0) {
				conBean.setRemark("禁用统一接入服务器信息");

			} else {
				conBean.setRemark("启用统一接入服务器信息");
			}
			conBean.setModify_type(Integer.toString(modify_type));
			conBean.setConfig_table("UNITY_ACCESS_SERVER");// 统一接入服务器表
			conBean.setTarget_server_type(2);
			try {
				list = new ArrayList<String[]>();
				Map<String, String> map = SendContentModelThread.getUnityAccessTransPort(server);
				String[] s = new String[]{server.getServer_ip(),map.get("sendPort"), server.getServer_id() + "",map.get("type")};
				list.add(s);
				Map m = wsUnityAccess.setContentServerStatus(
						sendMsg.toString(), list);
				String isSuccess = (String) m.get(Integer.toString(server
						.getServer_id()));
				conBean.setSynchro_status(isSuccess);
				log.info( "--sendStartOrStopServerInfo(success)-->server info::" + list);
			} catch (Exception e) {
				log.error( "下发统一接入服务器信息失败:" + e.toString(), e);
				// 记录下发信息
				conBean.setSynchro_status("0");
			} finally {
				statusManageDao.addSyncStatusInfo(conBean);
				log.debug( "--sendStartOrStopServerInfo-->conBean:"+conBean);
			}
		}
	}
}
