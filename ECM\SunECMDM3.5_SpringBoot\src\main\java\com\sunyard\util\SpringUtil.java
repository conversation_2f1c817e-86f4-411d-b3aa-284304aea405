package com.sunyard.util;

import org.springframework.context.ApplicationContext;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;

import com.sunyard.ecm.server.ServiceLocator;

/**
 * spring工具类，获取spring中定义的bean对象
 * 
 * <AUTHOR>
 * 
 */
public class SpringUtil {

	/**
	 * 根据BEAN名称得到相应的服务类
	 * 
	 * @param name
	 * @return
	 */
	public static DataFieldMaxValueIncrementer getSequence(String name) {
		return (DataFieldMaxValueIncrementer) ServiceLocator.getInstance()
				.getServiceClass(name);
	}

	/**
	 * 获取Spring的Bean
	 * 
	 * @param name
	 * @return
	 */
	public static Object getSpringBean(String name) {
		return ServiceLocator.getInstance().getServiceClass(name);
	}



}