package com.sunyard.client;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipException;

//import org.junit.Test;
import com.sunyard.client.impl.SunEcmClientHttpApiImpl;
import com.sunyard.client.impl.SunEcmClientSocketApiImpl;
import com.sunyard.ecm.server.bean.BatchBean;
import com.sunyard.ecm.server.bean.BatchFileBean;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.bean.MigrateBatchBean;
import com.sunyard.util.*;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.bean.ClientAnnotationBean;
import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientBatchIndexBean;
import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.client.bean.ClientHeightQuery;
import com.sunyard.client.bean.ClientTagBean;
import com.sunyard.client.bean.OperPermission;
import com.sunyard.es.util.EsBean;
import com.sunyard.ws.utils.XMLUtil;
//import org.springframework.cloud.client.ServiceInstance;
//import org.springframework.cloud.client.discovery.DiscoveryClient;


/**
 * 客户端使用示例
 *
 * <AUTHOR>
 *
 */
public class Client {
	private final static Logger log = LoggerFactory.getLogger(Client.class);
//
	String ip = "127.0.0.1";
	int socketPort = 8023;
	String groupName = "分行组"; // 内容存储服务器组名
//	String ip = "************";
//	int socketPort = 8023;
//	String groupName = "group_198";
	int httpPort = 8082;
	String serverName = "SunECMDM"; // 连接的服务工程名称
	// String serverName = "UnityAccess"; // 连接的服务工程名称
	// String groupName = "localGroup"; // 内容存储服务器组名
	// String groupName = "fatherGroup"; // 内容存储服务器组名

	// SOCKET接口
	SunEcmClientApi clientApi = new SunEcmClientSocketApiImpl(ip, socketPort);
//    SunEcmClientApi clientApi = new SunEcmClientHttpApiImpl(ip,httpPort,serverName);
//	SunEcmClientApi clientApi = new SunEcmClientWebserviceApiImpl(ip, httpPort, serverName)

//	String BEGIN_COLUMN = "START_TIME";
	 String BEGIN_COLUMN = "START_TIME";
	static String BEGIN_VALUE = "20220905";
	// 下载文件的路径

	static String DOWN_LOAD_FILE_PATH = "D://2/queryFromOffline/" + BEGIN_VALUE + "/";
	// HTTP接口
	// SunEcmClientApi clientApi = new SunEcmClientHttpApiImpl(ip, httpPort,
	// serverName);
	// WEBSERVICE接口
	// SunEcmClientApi clientApi = new SunEcmClientWebserviceApiImpl(ip,
	// httpPort, serverName);

	// =========================批次信息设定=========================

//	String modelCode = "TEST_P"; // 内容模型代码
//	String filePartName = "TEST_V"; // 文档部件模型代码
//	 String modelCode = "BB"; // 内容模型代码
//	 String filePartName = "BB_PART"; // 文档部件模型代码
	String modelCode = "DOC"; // 内容模型代码
	String filePartName = "DOC_PART"; // 文档部件模型代码
	// String modelCode = "AAS"; // 内容模型代码
//	String filePartName = "ASS_PART"; // 文档部件模型代码
	String userName = "admin";
	String passWord = "111";
	static String contentID = "2021_25_CB05CC61-CB11-6284-114D-B17C252BFA0C-61"; // 8位日期+随机路径+36位GUID+内容存储服务器ID
	String fileNO1 = "FF1C004F-08FD-88C2-6C8D-2706EC878EAF";
	String fileNO2 = "";
	String fileNO3 = "381CA442-A7DC-1D11-59C0-9B493C997167";

	String annoID = "92E0A6BC-94CA-2F47-3EF3-A57FB34A69B5";

	String checkToken = "23ac283f5c07646d4d0c"; // 检入检出随机数

	String token_check_value = "1234567890";
	String tokenCode = "2AFA9EFA0E2B3142BB36FB35C91E0BA4";
	String insNo = "aag";

	// =========================批次信息设定=========================

	public String getContentID() {
		return contentID;
	}

	public void setContentID(String contentID) {
		this.contentID = contentID;
	}

//	@Resource
//	private DiscoveryClient discoveryClient;

	/**
	 * 根据注册的用户名来获取ip
	 *
	 * @return
	 * @throws
	 */
//	@Test
//	public void getIp() {
//		List<ServiceInstance> serviceInstanceList = discoveryClient.getInstances("SunECMConsole");
//		for(ServiceInstance serviceInstance:serviceInstanceList){
//			log.info(serviceInstance.getHost());
//			log.info(serviceInstance.getPort()+"");
//			log.info(serviceInstance.getServiceId());
//		}
//	}




	/**
	 * 登陆
	 *
	 * @return
	 * @throws Exception
	 */
    @Test
	public void login() {
		try {
			String resultMsg = clientApi.login(userName, passWord);
			log.info("#######登陆返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 登出
	 *
	 * @param
	 * @return
	 * @throws Exception
	 */
	public void logout() {
		try {
			String resultMsg = clientApi.logout(userName);
			log.debug("#######登出返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 检出----------------------------------------------------------------------
	 * -------
	 *
	 * @return
	 * @throws Exception
	 */
	public void checkOut() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);
		clientBatchBean.getIndex_Object().addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
		clientBatchBean.getIndex_Object().setContentID(contentID);

		try {
			checkToken = clientApi.checkOut(clientBatchBean, groupName);
			String[] result = checkToken.split("<<::>>");
			checkToken = result[1];
			log.debug("#######检出返回的信息[" + checkToken + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 检入---------------------------------------------------------------------- --
	 *
	 * @return
	 * @throws Exception
	 */
	public void checkIn() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);
		clientBatchBean.getIndex_Object().addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
		clientBatchBean.getIndex_Object().setContentID(contentID);
		clientBatchBean.setCheckToken(checkToken);

		try {
			String resultMsg = clientApi.checkIn(clientBatchBean, groupName);
			log.debug("#######检入返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 申请批次号----------------------------------------------------------------------
	 * --
	 *
	 * @return
	 * @throws Exception
	 */
	public void createContentID() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);

		try {
			String resultMsg = clientApi.createContentID(clientBatchBean, groupName);
			log.debug("#######检入返回的信息[" + resultMsg + "]#######");
			System.out.println(resultMsg);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

    @Test
	public void uploadExample() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);
		clientBatchBean.setBreakPoint(false); // 是否作为断点续传上传
		clientBatchBean.setOwnMD5(true); // 是否为批次下的文件添加MD5码

		// 若内容模型配置有安全校验
//		 clientBatchBean.setToken_check_value(token_check_value);
//		clientBatchBean.setToken_code(tokenCode);

		// =========================设置索引对象信息开始=========================
		ClientBatchIndexBean clientBatchIndexBean = new ClientBatchIndexBean();
//		clientBatchIndexBean.setContentID("20220412_83_42_49BB6A9C-A4A4-024C-8402-00450A86DECA-1");
		// clientBatchIndexBean.setContentID(contentID);
		// clientBatchIndexBean.setVersion("1");
		// 索引自定义属性
//		clientBatchIndexBean.addCustomMap("BUSI_SERIAL_NO", UUID.randomUUID().toString());
		clientBatchIndexBean.addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
		/**
		 * 设置标签开始
		 */
//		HashMap<String,Object> m = new HashMap<String,Object>();
//		m.put("sfz", "sfz01");
//		m.put("lsh1", "1.3");
//		ClientTagBean clientTagBean1 = new ClientTagBean();
//		clientTagBean1.setTAG_CODE("user");
//		clientTagBean1.setTAG_MAP(m);
//		clientBatchIndexBean.addTagBeanList(clientTagBean1);

//		HashMap<String,Object> m1 = new HashMap<String,Object>();
//		m1.put("sfz", "sfz01");
//		m1.put("lsh", "1.3");
//		ClientTagBean clientTagBean2 = new ClientTagBean();
//		clientTagBean2.setTAG_CODE("kehu");
//		clientTagBean2.setTAG_MAP(m1);
//		clientBatchIndexBean.addTagBeanList(clientTagBean2);
//
		//设置标签结束

		// =========================设置索引对象信息结束=========================

		// =========================设置文档部件信息开始=========================
		ClientBatchFileBean clientBatchFileBeanA = new ClientBatchFileBean();
		clientBatchFileBeanA.setFilePartName(filePartName);
		// ClientBatchFileBean clientBatchFileBeanB = new ClientBatchFileBean();
		// clientBatchFileBeanB.setFilePartName("LINK_IMG_B");
		// =========================设置文档部件信息结束=========================

		// =========================添加文件=========================

//		clientBatchIndexBean.setAmount("1");
//		for (int i = 1; i < 4; i++) {
		ClientFileBean fileBean1 = new ClientFileBean();
		fileBean1.setFileName("C:\\Users\\<USER>\\Desktop\\60k.jpg");
		fileBean1.setFileFormat("txt");
//		ClientFileBean fileBean2 = new ClientFileBean();
//		fileBean2.setFileName("C:\\Users\\<USER>\\Desktop\\60k2.jpg");
//		fileBean2.setFileFormat("jpg");
		/**
		 * 设置标签开始
		 */
//		HashMap<String,Object> m3=new HashMap<String,Object>();
//		m3.put("fileA", "fileA01");
//		m3.put("fileB", "filsB01");
//		HashMap<String,Object> m4=new HashMap<String,Object>();
//		m4.put("fileA", "fileA01");
//		m4.put("fileB", "filsB01");
//		ClientTagBean clientTagBean3 = new ClientTagBean();
//		ClientTagBean clientTagBean4 = new ClientTagBean();
//		clientTagBean3.setTAG_CODE("index1");
//		clientTagBean3.setTAG_MAP(m3);
//		clientTagBean4.setTAG_CODE("index2");
//		clientTagBean4.setTAG_MAP(m4);
//		fileBean1.addTagBeanList(clientTagBean3);
//		fileBean1.addTagBeanList(clientTagBean4);
		clientBatchFileBeanA.addFile(fileBean1);

//		fileBean2.addTagBeanList(clientTagBean3);
//		fileBean2.addTagBeanList(clientTagBean4);
//		clientBatchFileBeanA.addFile(fileBean2);
//		}
		// =========================添加文件=========================
		clientBatchBean.setIndex_Object(clientBatchIndexBean);
		clientBatchBean.addDocument_Object(clientBatchFileBeanA);
		// clientBatchBean.addDocument_Object(clientBatchFileBeanB);
		try {
			String resultMsg = clientApi.upload(clientBatchBean, groupName);
			// log.debug("#######上传批次返回的信息[" + resultMsg + "]#######");
			System.out.println("#######上传批次返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
    /**
		 * 更新时需要注明版本号则表示自第几个版本更新,
		 * ------------------------------------------------------- 没有版本控制则无需注明
		 *
		 */
		@Test
		public void updateExample() {
			ClientBatchBean clientBatchBean = new ClientBatchBean();
			clientBatchBean.setModelCode(modelCode);
			clientBatchBean.setUser(userName);
			clientBatchBean.setPassWord(passWord);
			clientBatchBean.getIndex_Object().setContentID("20220419_3_30_4EB9EF90-72B9-E0AD-E7C8-035DF9C24436-1");
			clientBatchBean.setOwnMD5(true);
			// clientBatchBean.getIndex_Object().addCustomMap("START_TIME",
			// "20130824");
			clientBatchBean.getIndex_Object().addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
			HashMap<String,Object> m = new HashMap<String,Object>();
			m.put("sfz", "sfz02");
			m.put("lsh", "1.5");
			m.put("ECM_DOC_VERSION", "1");
			ClientTagBean clientTagBean1 = new ClientTagBean();
			clientTagBean1.setOPTYPE(EsOptionKey.ES_UPDATE);
			clientTagBean1.setTAG_CODE("user");
			clientTagBean1.setTAG_MAP(m);
			clientBatchBean.getIndex_Object().addTagBeanList(clientTagBean1);

			HashMap<String,Object> m1 = new HashMap<String,Object>();
			m1.put("sfz", "sfz02");
			m1.put("lsh", "1.5");
			m1.put("ECM_DOC_VERSION", "1");
			ClientTagBean clientTagBean2 = new ClientTagBean();
			clientTagBean2.setOPTYPE(EsOptionKey.ES_UPDATE);
			clientTagBean2.setTAG_CODE("kehu");
			clientTagBean2.setTAG_MAP(m1);
			clientBatchBean.getIndex_Object().addTagBeanList(clientTagBean2);
			// clientBatchBean.getIndex_Object().setVersion("1");
	//		 clientBatchBean.getIndex_Object().addCustomMap("ID_CARD","AAA");
			// clientBatchBean.getIndex_Object().setAmount("0");
			// 若内容模型配置有安全校验
			// clientBatchBean.setToken_check_value(token_check_value);
			// clientBatchBean.setToken_code(tokenCode);

			ClientBatchFileBean batchFileBean = new ClientBatchFileBean();
			batchFileBean.setFilePartName(filePartName);

			// // 新增一个文件
			ClientFileBean fileBean1 = new ClientFileBean();
			fileBean1.setOptionType(OptionKey.U_ADD);
			fileBean1.setFileName("C:\\Users\\<USER>\\Desktop\\60k.jpg");
			fileBean1.setFileFormat("jpg");
	//		batchFileBean.addFile(fileBean1);
			// //
			//
			// // 替换一个文件
			 ClientFileBean fileBean2 = new ClientFileBean();
			 fileBean2.setOptionType(OptionKey.U_REPLACE);
			 fileBean2.setFileNO("896AEFE5-704F-58DF-07A3-BC1D1C278336");
			 fileBean2.setFileName("C:\\Users\\<USER>\\Desktop\\60k.jpg");
			 fileBean2.setFileFormat("jpg");
			 ClientTagBean clientTagBean3 = new ClientTagBean();
			 clientTagBean3.setOPTYPE(EsOptionKey.ES_ADD);
			 clientTagBean3.setTAG_CODE("index1");
			 HashMap<String,Object> m3 = new HashMap<String,Object>();
			 m3.put("fileA", "fileA02");
		     m3.put("fileB", "filsB02");
	//		 m3.put("VERSION", "1");
			 clientTagBean3.setTAG_MAP(m3);
			 fileBean2.addTagBeanList(clientTagBean3);

			 ClientTagBean clientTagBean4 = new ClientTagBean();
			 clientTagBean4.setOPTYPE(EsOptionKey.ES_ADD);
			 clientTagBean4.setTAG_CODE("index2");
			 HashMap<String,Object> m4 = new HashMap<String,Object>();
			 m4.put("fileA", "fileA02");
		     m4.put("fileB", "filsB02");
		     clientTagBean4.setTAG_MAP(m4);
		     fileBean2.addTagBeanList(clientTagBean4);
			 batchFileBean.addFile(fileBean2);

			 ClientFileBean fileBean3 = new ClientFileBean();
			 fileBean3.setOptionType(OptionKey.U_REPLACE);
			 fileBean3.setFileNO("B731772E-414A-428D-A915-D2261CAF0B14");
			 fileBean3.setFileName("C:\\Users\\<USER>\\Desktop\\60k2.jpg");
			 fileBean3.setFileFormat("jpg");


	//		 m3.put("VERSION", "1");

			 fileBean3.addTagBeanList(clientTagBean3);
			 fileBean3.addTagBeanList(clientTagBean4);
			 batchFileBean.addFile(fileBean3);

			// 删除一个文件
			ClientFileBean clientFileBean3 = new ClientFileBean();
			clientFileBean3.setOptionType(OptionKey.U_DEL);
			clientFileBean3.setFileNO("FCD645B8-4E0E-4B2C-A166-24652348B6C5");
	//		batchFileBean.addFile(clientFileBean3);
			//
			//
			// // // 修改文档部件字段
			 ClientFileBean clientFileBean4 = new ClientFileBean();
			 clientFileBean4.setOptionType(OptionKey.U_MODIFY);
			 clientFileBean4.setFileNO("CD4B67B1-DABC-8824-A943-56DC4A96D854");
	//		 clientFileBean.addOtherAtt("START_DATE", "66");
	//		 batchFileBean.addFile(clientFileBean4);
			// //
			clientBatchBean.addDocument_Object(batchFileBean);
			try {
	//			String resultMsg = clientApi.update(clientBatchBean, groupName, true);

				String resultMsg = clientApi.update(clientBatchBean, null, true);
				System.out.println(resultMsg);
				log.debug("#######更新批次返回的信息[" + resultMsg + "]#######");
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	@Test
	public void queryExample() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);
		clientBatchBean.setDownLoad(false);
		clientBatchBean.getIndex_Object().setContentID("20220922_40_1_5E1C36EB-CC1A-5C22-9ED7-4FCFDC32C22C-1");
		clientBatchBean.getIndex_Object().addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
		// clientBatchBean.getIndex_Object().setVersion("0");
		ClientBatchFileBean documentObjectB = new ClientBatchFileBean();
		documentObjectB.setFilePartName(filePartName); // 要查询的文档部件名
		// documentObjectB.addFilter("FILE_NO",
		// "D02716BB-F9CA-3F85-477D-25069D8CCF6B"); // 增加过滤条件
		// documentObjectB.addFilter("SQL_FILTER",
		// " FILE_NO='D02716BB-F9CA-3F85-477D-25069D8CCF6B' or FILE_NO
		// ='D02716BB-F9CA-3F85-477D-25069D8CCF6B' ");

		clientBatchBean.addDocument_Object(documentObjectB);

		try {
			String resultMsg = clientApi.queryBatch(clientBatchBean, groupName);
			log.debug("#######查询批次返回的信息[" + resultMsg + "]#######");
			System.out.println("#######查询批次返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 高级搜索调用示例 -------------------------------------------------------
	 * 最后结果为组上最大版本的批次号
	 */
//	@Test
	public void heightQueryExample() {
		ClientHeightQuery heightQuery = new ClientHeightQuery();
		heightQuery.setUserName(userName);
		heightQuery.setPassWord(passWord);
		heightQuery.setLimit(10);
		heightQuery.setPage(1);
		heightQuery.setModelCode(modelCode);
//		heightQuery.addCustomAtt("TEST", "1");
		heightQuery.addCustomAtt("BUSI_START_DATE", "20201104");
		heightQuery.addCustomAtt("CONTENT_ID", "202011_15_5_93AF1305-909E-7D62-B8E2-CED4D10ECB52-1");
		heightQuery.setNEED_OFFLINE("1");
		// heightQuery.addCustomAtt("BUSI_SERIAL_NO", "2013092701");
		// heightQuery.addfilters("VARCHARTYPE='varchartype'");
		try {
			String resultMsg = clientApi.heightQuery(heightQuery, groupName);
			System.out.println(resultMsg);
			log.info("#######调用高级搜索返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 删除接口调用示例 -------------------------------------------------------
	 */
//	@Test
	public void deleteExample() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setPassWord(passWord);
		clientBatchBean.setUser(userName);
		clientBatchBean.getIndex_Object().setContentID("2020_18_288ED4D8-C62C-D3A8-7849-38F8C566E5E2-1");
		clientBatchBean.getIndex_Object().addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
		// 若内容模型配置有安全校验
		// clientBatchBean.setToken_check_value(token_check_value);
		// clientBatchBean.setToken_code(tokenCode);
		System.out.println(XMLUtil.bean2XML(clientBatchBean));

		try {
			String resultMsg = clientApi.delete(clientBatchBean, groupName);
			// log.debug("#######删除批次返回的信息[" + resultMsg + "]#######");
			System.out.println("#######删除批次返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 新增批注
	 *
	 * @throws Exception
	 */
	public void operAnnotation() {
		// 设定批次信息
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.getIndex_Object().addCustomMap("BUSI_START_DATE", "20120929");
		clientBatchBean.getIndex_Object().setContentID(contentID);
		// 若内容模型配置有安全校验
		clientBatchBean.setToken_check_value(token_check_value);
		clientBatchBean.setToken_code(tokenCode);
		// 设定文档部件信息
		ClientBatchFileBean batchFileBean = new ClientBatchFileBean();
		batchFileBean.setFilePartName(filePartName);

		// 设定文件信息
		ClientFileBean clientFileBean = new ClientFileBean();
		clientFileBean.setFileNO("1BF8004E-957F-EE07-20E3-D6066AF85B99");

		// 追加批注信息
		ClientAnnotationBean annotationBean = new ClientAnnotationBean();
		annotationBean.setAnnotation_id(annoID);
		annotationBean.setAnnotation_flag(OptionKey.U_ADD);
		annotationBean.setAnnotation_x1pos("1");
		annotationBean.setAnnotation_y1pos("1");
		annotationBean.setAnnotation_x2pos("100");
		annotationBean.setAnnotation_y2pos("5");
		annotationBean.setAnnotation_content("内容模型批注");
		annotationBean.setAnnotation_color("red");

		clientFileBean.addAnnoList(annotationBean); // 批注关联文件
		batchFileBean.addFile(clientFileBean); // 文件关联文档部件
		clientBatchBean.addDocument_Object(batchFileBean); // 文档部件关联批次

		try {
			String resultMsg = clientApi.operAnnotation(clientBatchBean, groupName);
			log.debug("#######批注操作返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 查询批注
	 *
	 * @throws Exception
	 */
	public void queryAnnotation() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.getIndex_Object().setVersion("1");
		clientBatchBean.getIndex_Object().addCustomMap("START_TIME", "20120612");
		clientBatchBean.getIndex_Object().setContentID(contentID);

		// 若内容模型配置有安全校验
		clientBatchBean.setToken_check_value(token_check_value);
		clientBatchBean.setToken_code(tokenCode);

		// 设定文档部件信息
		ClientBatchFileBean batchFileBean = new ClientBatchFileBean();
		batchFileBean.setFilePartName(filePartName);

		// 设定文件信息
		ClientFileBean clientFileBean = new ClientFileBean();
		clientFileBean.setFileNO(fileNO1);

		batchFileBean.addFile(clientFileBean); // 文件关联文档部件
		clientBatchBean.addDocument_Object(batchFileBean); // 文档部件关联批次

		try {
			String resultMsg = clientApi.queryAnnotation(clientBatchBean, groupName);
			log.debug("#######查询批注返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 客户端获得内容模型权限获取 -------------------------------------------------------
	 */
	public void getPermissions_Client() {
		try {
			String resultMsg = clientApi.getPermissions_Client(userName, passWord);
			log.debug("#######客户端获得内容模型权限获取返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 客户端获取所有服务器信息
	 *
	 */
	public void getContentServerInfo_Client() {
		try {
			String resultMsg = clientApi.getContentServerInfo_Client();
			log.debug("#######客户端获取所有服务器返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 客户端获得获取内容模型列表信息
	 */
	public void getAllModelMsg_Client() {
		try {
			String resultMsg = clientApi.getAllModelMsg_Client();
			log.debug("#######客户端获得获取内容模型列表返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 客户端获取内容模型模版
	 */
	public void getModelTemplate_Client() {

		String[] modeCodes = { modelCode };
		try {
			String resultMsg = clientApi.getModelTemplate_Client(modeCodes);
			log.debug("#######客户端获取内容模型模版返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 客户端获取内容存储服务器信息
	 */
	public void inquireDMByGroup() {
		try {
			String resultMsg = clientApi.inquireDMByGroup(userName, groupName);
			log.debug("#######客户端获取内容存储服务器信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 客户端获取令牌，调用WEBSERVICE
	 */
	public void getToken() {
		try {
			String oper = OperPermission.ADD + "," + OperPermission.UPD + "," + OperPermission.DEL + ","
					+ OperPermission.QUY + "," + OperPermission.MAK;

			String resultMsg = clientApi.getToken("http://172.16.3.32:9080/SunECMConsole", token_check_value, userName,
					oper);
			log.debug("#######客户端获取令牌返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void queryAndDownload() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);
		// clientBatchBean.getIndex_Object().setVersion("2");
		clientBatchBean.getIndex_Object().setContentID("2020_28_50_384B6DE1-78F3-FAFC-FF4B-50A84544DFD0-2");
		clientBatchBean.getIndex_Object().addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
		clientBatchBean.setDownLoad(true);
		// 若内容模型配置有安全校验
		clientBatchBean.setToken_check_value(token_check_value);
		clientBatchBean.setToken_code(tokenCode);

		try {
			String resultMsg = clientApi.queryBatch(clientBatchBean, groupName);
			log.info("#######查询批次返回的信息[" + resultMsg + "]#######");

			String batchStr = resultMsg.split(TransOptionKey.SPLITSYM)[1];

			List<BatchBean> batchBeans = XMLUtil.xml2list(XMLUtil.removeHeadRoot(batchStr), BatchBean.class);
			for (BatchBean batchBean : batchBeans) {
				List<BatchFileBean> fileBeans = batchBean.getDocument_Objects();
				for (BatchFileBean batchFileBean : fileBeans) {
					List<FileBean> files = batchFileBean.getFiles();
					for (FileBean fileBean : files) {
						String urlStr = fileBean.getUrl();
						String fileName = fileBean.getFileNO() + "-" + Thread.currentThread().getId() + "."
								+ fileBean.getFileFormat();
						log.debug("#######文件访问链接为[" + urlStr + "], 文件名为[" + fileName + "]#######");
						// 调用下载文件方法
						receiveFileByURL(urlStr, fileName, contentID);
					}
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 将文件下载到DOWN_LOAD_FILE_PATH路径下
	 *
	 * @param urlStr
	 * @param fileName
	 * @param contentID 批次号
	 */
	private void receiveFileByURL(String urlStr, String fileName, String contentID) {
		String path = DOWN_LOAD_FILE_PATH + "/" + contentID + "/";
		File file = new File(path + fileName);
		File pareFile = file.getParentFile();
		if (!pareFile.exists()) {
			log.info("no parefile ,begin to create mkdir,path=" + pareFile.getPath());
			pareFile.mkdirs();
		}

		URL url;
		InputStream in = null;
		FileOutputStream fos = null;
		try {
			url = new URL(urlStr);
			in = url.openStream();
			fos = new FileOutputStream(file);
			if (in != null) {
				byte[] b = new byte[1024];
				int len = 0;
				while ((len = in.read(b)) != -1) {
					fos.write(b, 0, len);
				}
			}
		} catch (FileNotFoundException e) {
			log.error("unitedaccess http -- GetFileServer: " + e.toString());
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				if (in != null) {
					in.close();
				}
				if (fos != null) {
					fos.close();
				}
			} catch (IOException e) {
				log.error("unitedaccess http -- GetFileServer: " + e.toString());
			}
		}
	}

	/**
	 * 客户端调用立即迁移接口 -------------------------------------------------------
	 */
	public void immedMigrate(String batchId) {
		MigrateBatchBean m = new MigrateBatchBean();
		m.setMigrate(true);
		m.setModelCode(modelCode);
		m.setUser(userName);
		m.setPassWord(passWord);
		m.getIndex_Object().setContentID(batchId);
		m.getIndex_Object().addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
		try {
			String resultMsg = clientApi.immedMigrate(m, groupName);
			log.debug("#######返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 批次复制接口调用示例 -------------------------------------------------------
	 */
//	@Test
	public void copyBatch() {
		String resultMsg = null;
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);
		clientBatchBean.getIndex_Object().setContentID("2019_54_65667DCD-6276-BAC2-5CD7-DBDA137EC5AE-1");
		clientBatchBean.getIndex_Object().addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
		// COPY_NEW_START_DATE为新的业务开始时间
		clientBatchBean.getIndex_Object().addCustomMap("COPY_NEW_START_DATE", "20190910");
		// COPY_NEW_CONTENTID为设置的新的CONTENTID(默认不设置，只在contentIdRule为3时起效)
//		clientBatchBean.getIndex_Object().addCustomMap("COPY_NEW_CONTENTID", "20190422-1AA5FF51-1D3C-CC16-0C89-9604AE34CD40-1");
		// COPY_NEW_MODELCODE为复制到新的模型名，COPY_NEW_FILEPARTNAME为复制到新的文档部件名
		// 不设置时为复制到同一模型
		clientBatchBean.getIndex_Object().addCustomMap("COPY_NEW_MODELCODE", "BB");
		clientBatchBean.getIndex_Object().addCustomMap("COPY_NEW_FILEPARTNAME", "BB_PART");

		clientBatchBean.getIndex_Object().setVersion("0");
		ClientBatchFileBean documentObjectB = new ClientBatchFileBean();
		documentObjectB.setFilePartName("AA_PART");// 查询时设置的文档部件名
//
		clientBatchBean.addDocument_Object(documentObjectB);

//		ClientFileBean file = new ClientFileBean();
//		file.setFileNO("C3C66D32-51BA-20E5-B7CB-10FD16A760B0");
//		file.addOtherAtt("START_TIME", "20190412");
//		documentObjectB.addFile(file);

		try {
			resultMsg = clientApi.copyBatch(clientBatchBean, groupName);
			log.info("#######批次复制返回的信息[" + resultMsg + "]#######");
			System.out.println("#######批次复制返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void queryNodeInfoByGroupIdAndInsNo() {
		try {
			String resultMsg = clientApi.queryNodeInfoByGroupIdAndInsNo(modelCode, "a");
			log.debug("#######返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	/**
	 * {test0={FILE_NO=20200101, ECM_DOC_ID=test0, CONTENT_ID=XDYX, GROUP_ID=CID0},
	 * test1={FILE_NO=20200101, ECM_DOC_ID=test1, CONTENT_ID=XDYX, GROUP_ID=CID1}}
	 *
	 */
	public void queryESByBool() {
		try {
			EsBean esBean = new EsBean();
			esBean.setIndexName("aag");
			esBean.setPageNo(0);
			esBean.setPageSize(10);
			Map<String, String> must=new HashMap<String, String>();
			must.put("BUSI_START_DATE", "20200101");
			must.put("MODEL_CODE", "XDYX");
			esBean.setMust(must);
//			esBean.setMustNot(mustNot);

			String result = clientApi.queryESByBool(esBean);
			String batchStr = result.split(TransOptionKey.SPLITSYM)[1];
			log.info(batchStr);
			EsBean bb = XMLUtil.xml2Bean(batchStr, EsBean.class);
			// 格式为 {test0={FILE_NO=20200101, ECM_DOC_ID=test0, CONTENT_ID=XDYX,
			// GROUP_ID=CID0}, test1={FILE_NO=20200101, ECM_DOC_ID=test1, CONTENT_ID=XDYX,
			// GROUP_ID=CID1}}
			Map<String, Object> resultMap = bb.getMap();
			if (resultMap != null && resultMap.size() > 0) {
				String errorMsg = (String) resultMap.get("FAIL_MSG");
				if (StringUtil.stringIsNull(errorMsg)) {
					Iterator<String> it = resultMap.keySet().iterator();
					while (it.hasNext()) {
						String key = it.next();
						Map<String, String> mm = (Map<String, String>) resultMap.get(key);
						Iterator<String> ii = mm.keySet().iterator();
						log.info(key + "------------------------" + resultMap.get(key));
						while (ii.hasNext()) {
							String aasd = ii.next();
							log.info(aasd + "-" + mm.get(aasd));
						}
					}
				} else {
					log.error(errorMsg);
				}
			}
		} catch (Exception e) {
			log.error("queryESByBool error", e);
		}
	}
//    @Test
    public void uploadESDoc() {

		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);
		// =========================设置索引对象信息开始=========================
		ClientBatchIndexBean clientBatchIndexBean = new ClientBatchIndexBean();

		clientBatchIndexBean.setContentID("20220420_58_45_A2A6D4EA-AC86-ACFD-AF0A-FC95B9127CD5-3");
		clientBatchIndexBean.addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);

		/**
		 * 设置标签开始
		 */
		HashMap<String,Object> m = new HashMap<String,Object>();
		m.put("sfz", "sfz01");
		m.put("lsh", "ff1.3");
		ClientTagBean clientTagBean1 = new ClientTagBean();
		clientTagBean1.setTAG_CODE("user");
		clientTagBean1.setTAG_MAP(m);
		clientBatchIndexBean.addTagBeanList(clientTagBean1);

		HashMap<String,Object> m1 = new HashMap<String,Object>();
		m1.put("sfz", "sfz01");
		m1.put("lsh", "1.3");
		ClientTagBean clientTagBean2 = new ClientTagBean();
		clientTagBean2.setTAG_CODE("kehu");
		clientTagBean2.setTAG_MAP(m1);
		clientBatchIndexBean.addTagBeanList(clientTagBean2);
		// 设置标签结束
		// =========================设置索引对象信息结束=========================

		// =========================设置文档部件信息开始=========================
		ClientBatchFileBean clientBatchFileBeanA = new ClientBatchFileBean();
		clientBatchFileBeanA.setFilePartName(filePartName);
		ClientFileBean fileBean1 = new ClientFileBean();
		fileBean1.setFileNO("815C16B6-95F9-7469-348C-34F560E4C89A");
		ClientFileBean fileBean2 = new ClientFileBean();
		fileBean2.setFileNO("9FD21675-E2E4-5A75-7DEF-0B570733A839");
		HashMap<String,Object> m3=new HashMap<String,Object>();
		m3.put("fileA", "fileA01");
		m3.put("fileB", "filsB01");
		HashMap<String,Object> m4=new HashMap<String,Object>();
		m4.put("fileA", "fileA01");
		m4.put("fileB", "filsB01");
		ClientTagBean clientTagBean3 = new ClientTagBean();
		ClientTagBean clientTagBean4 = new ClientTagBean();
		clientTagBean3.setTAG_CODE("index1");
		clientTagBean3.setTAG_MAP(m3);
		clientTagBean4.setTAG_CODE("index2");
		clientTagBean4.setTAG_MAP(m4);
		fileBean1.addTagBeanList(clientTagBean3);
		fileBean1.addTagBeanList(clientTagBean4);
		clientBatchFileBeanA.addFile(fileBean1);

		fileBean2.addTagBeanList(clientTagBean3);
		fileBean2.addTagBeanList(clientTagBean4);
		clientBatchFileBeanA.addFile(fileBean2);
		// 设置标签结束
		clientBatchFileBeanA.addFile(fileBean1);
		clientBatchBean.setIndex_Object(clientBatchIndexBean);
		clientBatchBean.addDocument_Object(clientBatchFileBeanA);
		try {
			String resultMsg = clientApi.uploadESTag(clientBatchBean, groupName);
			System.out.println(resultMsg);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	public void queryESByecmid() {
		try {
			log.info(clientApi.queryESByECM_DOC_ID("aag",
					"2021_25_CB05CC61-CB11-6284-114D-B17C252BFA0C-61_1_9449D071-ABF9-EAED-10BB-703C64498330"));
		} catch (Exception e) {
			log.error("", e);
		}
	}
//	@Test
	public void updateESTag() {

		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode(modelCode);
		clientBatchBean.setUser(userName);
		clientBatchBean.setPassWord(passWord);
		// =========================设置索引对象信息开始=========================
		ClientBatchIndexBean clientBatchIndexBean = new ClientBatchIndexBean();
		clientBatchBean.getIndex_Object().setContentID("20220418_44_57_5B156E89-CF17-DBC8-DBFC-0D106799B55B-1");
		clientBatchIndexBean.addCustomMap(BEGIN_COLUMN, BEGIN_VALUE);
//		clientBatchIndexBean.addCustomMap("GID", "1");


		/**
		 * 设置索引标签开始
		 */
		HashMap<String, Object> m = new HashMap<String, Object>();
		m.put("sfz", "sfz01");
		m.put("lsh", "lsh01");
		HashMap<String, Object> m1 = new HashMap<String, Object>();
		m1.put("sfz", "sfz01");
		m1.put("lsh", "lsh01");
		ClientTagBean clientTagBean1 = new ClientTagBean();
		ClientTagBean clientTagBean2 = new ClientTagBean();
		clientTagBean1.setOPTYPE(EsOptionKey.ES_ADD);
		clientTagBean2.setOPTYPE(EsOptionKey.ES_ADD);
		clientTagBean1.setTAG_CODE("user");
		clientTagBean2.setTAG_CODE("kehu");
		clientTagBean1.setTAG_MAP(m);
		clientTagBean2.setTAG_MAP(m1);
		clientBatchIndexBean.addTagBeanList(clientTagBean1);
		clientBatchIndexBean.addTagBeanList(clientTagBean2);
		// 设置标签结束
		// =========================设置索引对象信息结束=========================

		// =========================设置文档部件信息开始=========================
		ClientBatchFileBean clientBatchFileBeanA = new ClientBatchFileBean();
		clientBatchFileBeanA.setFilePartName(filePartName);
		ClientFileBean fileBean1 = new ClientFileBean();
		fileBean1.setFileNO("B29C6B9C-2804-D739-A922-163380CCE850");
		/**
		 * 设置标签开始
		 */
		HashMap<String, Object> m3 = new HashMap<String, Object>();
		m3.put("fileA", "fileA01");
		m3.put("fileB", "filsB01");
		HashMap<String, Object> m4 = new HashMap<String, Object>();
		m4.put("fileA", "fileA01");
		m4.put("fileB", "filsB01");
		ClientTagBean clientTagBean3 = new ClientTagBean();
		ClientTagBean clientTagBean4 = new ClientTagBean();
		clientTagBean3.setOPTYPE(EsOptionKey.ES_ADD);
		clientTagBean4.setOPTYPE(EsOptionKey.ES_ADD);
		clientTagBean3.setTAG_CODE("index1");
		clientTagBean3.setTAG_MAP(m3);
		clientTagBean4.setTAG_CODE("index2");
		clientTagBean4.setTAG_MAP(m4);

		fileBean1.addTagBeanList(clientTagBean3);
		fileBean1.addTagBeanList(clientTagBean4);
		// 设置标签结束
		clientBatchFileBeanA.addFile(fileBean1);
		clientBatchBean.setIndex_Object(clientBatchIndexBean);
		clientBatchBean.addDocument_Object(clientBatchFileBeanA);
		try {
			String resultMsg = clientApi.updateESTag(clientBatchBean, groupName);
			System.out.println(resultMsg);
		} catch (Exception e) {
			log.error("",e);
		}



	}

	// 多线程客户端
	private Runnable createTask(final int taskID) {
		return new Runnable() {
			public void run() {
				System.out.println("现在进行第[" + taskID + "]个任务");
				// getToken();
				// checkOut(); //88d29fee522dbf173337
//				login();
				// logout();

				for (int i = 0; i < 1; i++) {
//					queryESByecmid();
//					queryESByBool();
					// queryNodeInfoByGroupIdAndInsNo();
					// updateExample();
					uploadExample();
//					 queryExample(contentID);
					// inquireDMByGroup();
				}
				// allQuery();
//				allUpload();
//				log.info(20 * 1024l * 1024l);
//				getAllFilesSizeOfFolder();

				// checkIn();
				// getModelTemplate_Client();

				// queryAndDownload();

				// immedMigrate(contentID);
				// heightQueryExample();
				// operAnnotation();
				// queryAnnotation();
				// deleteExample();
				// getAllModelMsg_Client();
				// getContentServerInfo_Client();
				// getModelTemplate_Client();
				// getPermissions_Client();
				// logout();

			}
		};
	}

	public void allQuery() {

		String contentid_8 = ("2016_97_B622919A-DC4A-CBDF-A23A-751DBF134104-22,2016_365_6B3BD222-FD1F-3C3B-9EF7-60715C1EEF15-22,2016_193_A68B064E-412C-DBB6-6AE5-B20C72D8FA13-22,2016_206_A7230658-C879-D17D-A7C9-54169FBE787B-22,2016_697_3AC09456-6B50-C04D-589C-DAD3E2C2C01E-22,2016_56_77E4A310-7E44-A074-0D1B-08006BDA8526-22,2016_578_01E2ECC0-C20E-F8B2-87F9-88D78EB72F91-22,2016_582_99FEF1AE-FF7E-3C14-F91E-4C1120BDA59C-22,2016_255_28F9642D-38DA-BA72-BAAB-7DBAA230DE3F-22,2016_563_782FA155-6F9C-3406-100D-378EB2CF0C34-22,2016_651_F4381E78-986C-86EA-FA56-1D771FABA640-22,2016_209_DC21BB28-A944-8AFD-AE8A-5D282F5AEFFB-22,2016_614_8B6DDB5E-93FC-B3FA-3F92-8301814E7228-22,2016_979_FAE25F44-4CC8-30D6-0DB1-083D75480EA1-22,2016_618_ECB43688-D416-4DC2-75B9-12C459BDCD00-22,2016_362_35738454-D542-BDC4-0E4E-9165303FB53F-22,2016_334_92F19C9F-EE67-0010-29E6-1469B7D8E62B-22,2016_187_181A07F2-6105-2606-803E-B9B8B34907B2-22,2016_733_BE630E17-5A1A-2CCC-5AB7-8B6DACF9B674-22,2016_326_669AFE1F-9F47-B098-6647-AA23E60F8C4A-22,2016_187_31B67388-B41A-5995-D156-96D9C1DBAD59-22,2016_306_2B35A103-0B1F-7ABD-1C15-DFBDF921BB39-22,2016_994_DDA247D0-C59B-E7BF-78CE-DC73595AD730-22,2016_162_B2BE6A51-E0EA-3CD0-D430-F08E7BA4B8FF-22,2016_174_08236F90-D430-E699-1A53-1DD61AF6E92B-22,2016_143_A62B3B0C-69F0-E6D4-02F5-7A9F980C94D4-22,2016_125_F4DBAA6C-C92A-F331-3CB1-0CEFEE325D9F-22,2016_662_3CFA2A40-8545-CF6D-BAC9-9048EA8AFB0B-22,2016_549_89763594-815F-637E-4E43-AB54AB036A32-22,2016_13_E2EEC546-0603-7FFC-21CD-2EC0DF961FE7-22,2016_933_8E61F6F1-E75D-F8DD-4BE2-031733703B2E-22,2016_28_FFEE0311-85AE-3AFC-F48C-97951C1D7DE6-22,2016_36_BD9CD3DE-1B91-A4AB-2965-180859B7DB93-22,2016_215_17AE21E7-DBC2-50D9-1959-2667C31809EC-22,2016_877_1C6CE488-6690-1255-639C-B8CBE868A860-22,2016_701_DAAD032D-E953-18C2-A9A4-4AF57CED0AA6-22,2016_325_6653D633-C9F4-167F-0601-1DC047A8DF2C-22,2016_43_9C19A9DE-4320-3810-16D7-AF638537CFF5-22,2016_136_CA92C89E-F7DC-6DCD-EE09-035C8F7646C3-22,2016_624_4D0ED516-0F2E-2CA7-ECE7-5977CF3AB1E5-22,2016_269_AE7A4178-A820-45C3-C0E0-25D2A1F796EC-22,2016_84_5CD2353C-A072-1554-E89F-1B0A3E1F9581-22,2016_310_81DF44BC-38F7-5B54-4031-B4B0396E57AC-22,2016_680_03E45D34-2894-E62C-62E6-A8FCD25876EC-22,2016_266_13E2EADF-4B8B-82C9-92EC-86409CF2F05B-22,2016_812_A38D724C-F215-EF12-9D1B-CEF595359254-22,2016_561_AC0DC160-0BF6-D278-7A77-6C7F0B10E7F2-22,2016_815_73CE77ED-031B-76AE-6A30-D82C34EBD9DD-22,2016_893_BD46CFF0-240A-B7E7-B27E-C5735D235574-22,2016_690_F0D43243-0E2A-F8A8-002C-9F85A4130A87-22");
		String contentid_9 = ("2016_464_934E6DAA-546C-9DBC-2E55-FDCC80DBA5B8-22,2016_690_5D01551B-C5B6-D5C0-7AB3-D1F6A64BD7BE-22,2016_75_039A91DC-4164-ABE8-A76D-D1EB1E33EEB1-22,2016_114_9A8EB124-6572-6B79-4A7B-628C06456D60-22,2016_801_94790175-2A37-CF5C-28FE-490D96059C20-22,2016_88_A5C8A7F3-2746-1B2E-9E89-A5012E1A486D-22,2016_148_7F0CD47A-C9D2-85D8-1F35-332A06B924C9-22,2016_222_7848BAAF-3F5A-74FE-2C3E-D95F3F9FF95F-22,2016_713_F80EC64E-AC5E-0D53-F335-EB5B1B95D22A-22,2016_420_674F8D44-1C11-208C-B368-DC98621B642B-22,2016_658_0B881949-92B9-773F-EF87-B71409715C3D-22,2016_690_533118A2-D817-09D6-BE33-ABC9F95E1134-22,2016_529_F6EDFB4F-1C72-BD1C-3581-0D773BACE571-22,2016_56_6A1FFF0A-4FD9-0C1D-07EF-4BFC5EC4FB4B-22,2016_997_0C469C46-FB77-A55C-5890-74A2A00F8406-22,2016_665_2E2CF8B0-7261-C627-A8CA-17B7E72893A2-22,2016_430_8E323142-A49A-91FE-F136-2ED3910FCD63-22,2016_0_6B12FEA2-4F30-DC9E-1556-B141C5CC9629-22,2016_308_F509D3F5-D12A-4698-B374-0C5BFE1A779A-22,2016_700_F0A1D601-E964-5ADF-97EB-D47AE40D3ACF-22,2016_734_08C844DC-9C9C-A7A2-E5E9-FC512B230ED3-22,2016_276_B1926C30-C658-71D3-5088-CCCD0F70946E-22,2016_643_211542F1-E53F-71F1-A77A-0299B80834CC-22,2016_718_5EBA868E-85AC-5F8F-840D-76E2F6909FE1-22,2016_873_B929CE24-BB08-FA3D-CC4A-0A035BF8D79B-22,2016_527_2684219C-D197-94BF-C8B2-B5AD04A5BC45-22,2016_212_D3DE4269-CAC4-1748-42F5-C3CBE10CFA2B-22,2016_193_ACD22CB6-9F9A-1318-318F-00ABB8EF5760-22,2016_903_83289977-C9E6-8CC5-64AF-E86E006BDF65-22,2016_653_8123B2B8-DF97-9E73-C8AC-BD59AABA50D4-22,2016_86_A8F6B8F6-8671-3B3B-3BEE-B17159B5C3D0-22,2016_805_194FED2F-F081-795B-7CB4-D4C868AC49AC-22,2016_845_0632A7C6-BEA0-8F0D-9719-AC19A130A57F-22,2016_752_52B8AB0D-C8D5-2AA1-DEDA-C19B696DC14F-22,2016_828_83D4188A-E038-28FE-D092-BFED826944B1-22,2016_10_AED4982B-C5DC-E5E4-A41C-7E9A62BDA0E5-22,2016_388_FA4F4B7D-87BB-C3E9-E6A9-18EB74C16B80-22,2016_962_A87B2003-D73C-0D87-7E24-7E78B3458718-22,2016_137_18BBB265-F906-9D69-3C50-876075BA57E9-22,2016_548_32E160DC-F9D8-25B9-D57A-EB84A1739B78-22,2016_445_E319480E-29B4-262E-83A9-3CD71ED62A39-22,2016_349_6B839C45-F032-E5E5-D698-F89A2C67A3A4-22,2016_602_708C0014-E7FA-A1AE-EB3E-C7D93441B424-22,2016_121_B95E38C6-8738-D164-44C9-08A96D8F70CF-22,2016_930_C0AE2FAA-1985-2AC0-BDFF-92FF2DFCE887-22,2016_410_ECCF09A6-2827-CD79-98E4-E42F66CD1C94-22,2016_591_23DACCB9-6C1B-1305-5F55-596DF68EE7E8-22,2016_700_1AC1E7B5-92BB-A799-562A-9759FDCC9BEE-22,2016_104_E5FB5A6E-18CD-34B4-5C50-6D336125D602-22,2016_798_D63A390B-6826-E2C1-82D0-11FC59109579-22");

		String contentid_10 = ("2016_749_0FBD9A6E-5968-B0A8-5701-402E863967E4-22,2016_476_D8FF37BE-8696-E43B-52AE-B4E6EF14D83B-22,2016_291_D29196EF-0688-0EBB-18CF-BD0AB66B5456-22,2016_524_7E6EA2FD-7FA8-DB23-FBE3-59524F2E9909-22,2016_893_1F475B36-1AD4-8722-DE28-9ECB534E8543-22,2016_946_C46D06D7-216F-A783-1F11-D41C8F684E8F-22,2016_372_0EA169B5-0876-0447-EA34-55874AD575DB-22,2016_635_49D7F636-C042-1137-5009-20F4E1ED2BE3-22,2016_34_9BBB99B3-A998-6AFA-F1DA-8CE10DAE850D-22,2016_200_E4345833-8E17-044C-7DCC-C91F2FEB9101-22,2016_208_B23D3561-EBDA-36DB-9DF9-37C5E99CCDD2-22,2016_471_D31DC574-884E-300E-EC19-5ACC7135BB45-22,2016_403_CB155885-9421-D0DE-74B8-8298090CBAA8-22,2016_638_19C2F7B1-1258-E741-D96F-C03AFE78A667-22,2016_731_14F54404-5EC7-AED0-9721-3C03855DCDB8-22,2016_340_F470AF4F-3658-432E-2C80-EA8B00220090-22,2016_819_C4FADBE5-D023-55FD-8373-E72B88144B5E-22,2016_462_0A4000B6-D4F2-7E0D-2CAF-6797E2DAAB3D-22,2016_978_4ACE7D32-0D44-C0E4-9F18-985B770F6BE8-22,2016_822_99FCCA82-7345-F970-3AC2-68CFE2FE50C9-22,2016_248_0756E597-0CAC-6B84-C4D4-181EA4490899-22,2016_517_489FC531-8CB2-DA38-747E-82E52475EC9A-22,2016_565_D8444CC7-69B3-78E0-3E51-07EC49F734D4-22,2016_229_E076427B-E45A-6AF7-48B3-7C33220760BC-22,2016_267_D0F3FFBC-4650-5488-BA03-12E39FF7192D-22,2016_59_4C722C63-0A18-61BB-8D85-FE20479B2880-22,2016_295_FFA3FC50-D354-AD5B-1CE2-9C1D7EF3F81B-22,2016_537_5F792200-F6D9-19C4-9C5C-FECCADCB39BF-22,2016_541_76ED8E73-D18E-00F6-952B-C48E6B85BE9F-22,2016_707_35295FCE-03C6-C11F-A209-6825267CA4D4-22,2016_443_058722F2-BE5F-B65F-F509-982C6C61151B-22,2016_467_B039A756-AADE-281A-9B1F-B87808F4068E-22,2016_111_7413F44F-80AE-DFC0-EF16-1E46746F987B-22,2016_989_24E07CBE-B7A2-096E-1D9F-CFB4F002DBA3-22,2016_617_B75777C6-154B-890E-7CC4-3D5C76341BD5-22,2016_278_6A089D0B-C74E-CD50-C34B-D75160EA6A56-22,2016_46_4D886720-314F-0CF6-4D2F-4BF7377766B7-22,2016_375_34601A27-2129-ECE0-751A-B3E2E10A0D32-22,2016_965_7644C1FD-BE6A-C344-1DA8-84F7B2B8D306-22,2016_909_B429A034-B4EF-EA3C-6256-9455C2E3BE62-22,2016_600_99EC110A-C5AF-5A46-3A11-2D7C19439660-22,2016_238_99EE02C6-5E5D-ADE1-63C1-44F9DB14D311-22,2016_649_F9B3C8A4-9A49-7708-D6A2-8C032D5193F1-22,2016_153_54AA4AB4-84B7-66D1-6E1E-1EA0EB81EA7F-22,2016_204_F1ED50B2-485A-DA8C-C489-EA196BBAE608-22,2016_738_CFB3D077-03FD-226C-539B-73FB6DA2CC94-22,2016_314_A96AD271-34C8-F647-B36B-FDA0C25AA11B-22,2016_244_56AA4336-65CE-3DFA-FF5D-86746D3859EB-22,2016_19_3ECB9109-F159-9BB4-E145-304AF27CB28D-22,2016_628_7CF2D308-74F5-CA46-9F1F-AF42A2339D0F-22,2016_791_F2D5FA01-926D-DDB9-D51A-13594D0EC68A-22");
		String contentid_11 = ("2016_117_9E64F9E5-2782-EDD8-2483-F2E1E5C1A6A3-22,2016_331_4390FF4C-D5C7-AB92-23DA-7125CEB2EBC5-22,2016_809_2B3CAFEE-CD3C-6A6E-0F82-0B8F256EF28A-22,2016_917_D5289946-A058-3E62-C866-CDF6A71AD479-22,2016_250_DDE07A1A-FDFA-EB12-2787-A2CEC8644EA1-22,2016_81_33B415D6-6066-6407-E453-4ED4C02BD89F-22,2016_67_9A36F589-13D0-B549-662D-8B6B485379FA-22,2016_789_949E8E1E-5B04-15CD-2954-053D606D1E14-22,2016_503_A0218117-A7D3-CCC1-81CC-FE0CD19B80A0-22,2016_979_0E2CF359-D353-28D6-8EB1-15F1658AF5CD-22,2016_977_E1F127E2-E05A-AC54-3095-1EFC00F9BF15-22,2016_346_72843778-5E61-597C-D5E3-88B41E9BEC1C-22,2016_643_4E51EA74-73FC-B2B4-D81D-474F243113E5-22,2016_945_68C90FC4-7881-C2CB-4BC6-8CF1C2552AF1-22,2016_807_98793677-4F2C-2145-C9F5-373C94339B0C-22,2016_418_7C9FD7DE-67F1-469C-B3ED-D5596B3AD5AD-22,2016_489_60ECC747-28D2-B698-45D9-90B019838BE5-22,2016_881_C7296CD5-15E5-2B5C-0BDB-F62C66FCA0C0-22,2016_737_033D2EF3-B59B-88B3-AF88-BB084C154662-22,2016_101_48B0E15D-E2EE-9FB3-060C-733F7FE6C33C-22,2016_117_02EB1D4C-E4CB-410E-83B6-7EF0E86ACCDB-22,2016_575_639429EF-F8F4-B321-0FD2-5DD6F30FFC52-22,2016_204_D6059168-1A16-2553-E549-12BC9759BC33-22,2016_256_9CC2F370-8FE6-2491-F7E5-D00065F2D2EB-22,2016_262_FB3C3C59-9A31-E2C1-EC68-F4475973BD18-22,2016_582_BCCE6A06-3382-6892-A29B-C397EE42EE67-22,2016_878_7B0A8AE8-ED38-35D2-8C88-100F3608386D-22,2016_183_E96E7478-878B-C8C2-8D1F-DD12CE502CDF-22,2016_741_142D72DF-EDF1-F694-7079-76D9219F136A-22,2016_668_FB185D3A-0BAC-550E-36E0-64C485F3FC0B-22,2016_909_AF8F5CFD-7494-9361-8DEC-3A5CF2C0BF92-22,2016_158_7080A5EB-C0D0-3F7A-F205-E58AFF6C17F3-22,2016_976_EE07D8EF-008A-9D7B-5EE7-B8CE477FEAE7-22,2016_946_04439E06-3BDA-0D06-F135-5AD1FB3DD4B7-22,2016_858_93831E0A-2CB4-79EE-88B5-EA8E5B43946F-22,2016_717_A7F2004A-80D0-0F77-EF9D-386D25B1BF66-22,2016_614_22E00ED4-CE2C-C9A9-BD7B-D36A3698811C-22,2016_112_36D425A5-C91B-907C-2B7B-B2D4F66E71D4-22,2016_22_078C756B-A31A-97F7-3161-F8492FCA9CE7-22,2016_112_18F8B1D5-7294-D90E-7740-545F2BBA893D-22,2016_711_2DBEF7E0-A793-E44A-81D2-42A878B53447-22,2016_658_E6B1E0A1-AE8C-88F4-DB3C-7CE861084F2B-22,2016_146_06486FD7-9C4E-28F1-19A9-FDE6EEB03B2A-22,2016_582_661F53D4-E52B-5A95-55D3-769F92444D46-22,2016_144_4FB0DAB2-2737-9508-4E56-6A4652DB9F7B-22,2016_937_D515BACB-2067-25CF-0FD3-8EDD32EE85AC-22,2016_460_6C7AA354-94AF-3FC7-2C29-997C996E4D7A-22,2016_158_03D0C5C6-9621-1670-738B-432E1EAA560C-22,2016_137_FC0872EF-A42B-7707-8DB2-5BFFE8282141-22,2016_955_B431FA21-C728-55F1-EC4F-336C7D496799-22");
		String contentid_12 = ("2016_37_BE4CE672-5DF7-FA3D-C199-CF37B4EF8B8E-22,2016_239_77566BFE-0031-CBB1-902D-090CB5C1B312-22,2016_637_9ED6DFC6-DA03-89F3-A4CC-1511CEF688E5-22,2016_624_D6745C51-8F95-9455-FC34-87B5488874AB-22,2016_736_F720A2BC-BFA8-78F8-F013-BCEFAF492FC6-22,2016_653_975FF806-3A55-02DB-337A-813587ECE1DD-22,2016_84_0A957144-ED80-207A-F446-97535E25645A-22,2016_498_DAA6B4D4-11B1-A688-D6CB-2953854ADEEC-22,2016_532_EA53D20C-F2AD-720E-2F3C-A166B7E904E7-22,2016_983_8AB3FEFF-FCFE-D000-DDB9-48F2F34AD2B0-22,2016_893_C9469966-7ECF-3412-0CB7-246FA21C3784-22,2016_358_71D347EF-C044-7FD9-637A-1062A48F91C0-22,2016_376_5EBB8334-D3C8-FFA6-3066-246E170E1E8D-22,2016_991_CC42D490-C743-D6F7-2A25-39E1588B15C8-22,2016_611_CC4BB15F-EE3D-85CD-1038-B21F18D081FD-22,2016_6_A249C0CF-A57E-3160-E6CF-66FDE051A2FD-22,2016_250_2E38F075-4B59-F8D4-73A5-02F891649D08-22,2016_967_2825DEF1-28DB-DAE2-03B7-325AFEFC46CF-22,2016_414_02D26DCF-FBE6-80E7-E79B-082577250A59-22,2016_929_417602C1-923B-6142-5972-0FC9F3D9624A-22,2016_609_4385EC4D-7538-AC05-A500-540794101078-22,2016_197_C815444A-28EC-7550-641D-0069E5665955-22,2016_606_2EDAE6FD-F476-75F8-736D-A11132F52355-22,2016_909_E8843EF7-DB17-1844-3BE0-60AD609E1088-22,2016_273_BDEC2D8D-39C9-325C-ACB1-105F24CB82F8-22,2016_203_577E12D4-4466-5E6F-8C69-F2E299790FF3-22,2016_317_4D8AF5F0-7141-EFB9-45BB-90752C73E0DE-22,2016_649_D5AFE4BC-7A45-34D3-C2EB-6D4EDB2E7E6D-22,2016_613_E6003202-DBF7-3D2B-C2DC-81F5929DDB28-22,2016_139_98E408B0-9456-78E0-0D04-8600E1159E81-22,2016_597_98EE05DC-E673-AF0C-92AA-0CE91D78D0A2-22,2016_561_66D907FB-96A6-62EF-0695-156B513164C3-22,2016_161_6AF413FE-D57F-CCE2-071F-22152C10252E-22,2016_827_FC8486D8-EF23-AB82-2590-C297C4B60D37-22,2016_952_CBCB2313-F1E3-ABB7-33BE-D3C0485DBF57-22,2016_436_D2A5D690-9253-FF8C-516F-0A6C916513F8-22,2016_781_1A4DCC45-C379-BE1F-2529-623909D80E82-22,2016_4_A96EB291-6283-0BC9-2CA0-C2D11B0B44A4-22,2016_696_DDC9B263-E416-828B-8AAE-693DA930BF2A-22,2016_244_4C04AFA3-524D-14FB-2E15-C8222E81D16B-22,2016_568_404D83FA-4CEE-7640-B872-5EE832A16B0A-22,2016_967_D439A7F1-2C63-56CA-4063-72C62606F1CC-22,2016_295_31404C81-1327-9F59-C7EC-87B071FF0151-22,2016_744_32E254A7-2C30-DD5F-2BCD-A612E39EC4D0-22,2016_26_1EE9A8EF-D954-56FD-E67C-F9047B0A058F-22,2016_344_E0E98B21-2A90-6EA5-C166-C14536E3099B-22,2016_984_EF9D4D79-3E46-2905-59C0-AA09E449CE54-22,2016_719_EA73768E-A6D7-A73C-646B-297B4AEBD843-22,2016_767_7B566C99-579D-1517-1268-9C37479B8BA1-22,2016_88_76E86CAA-467E-72BD-E244-889FB8E28114-22");
		String contentid_13 = ("2016_195_A4C0FD50-E402-E45F-B792-37F7E4C28F29-22,2016_379_3F05A68A-CB3E-C813-B2D3-AAB6A16F3CEC-22,2016_71_5DFB5DCF-49B7-317D-F85B-BCD3A1320D32-22,2016_560_13AC6738-8709-7F96-1E45-317CED6339F5-22,2016_191_AF299193-DB52-F92E-940F-C3F92FC291E5-22,2016_772_616562C4-C237-F615-36C3-F13C791EA61F-22,2016_535_49CE1652-FD26-CDF1-2E38-F4F5A40F08C0-22,2016_151_942741FE-4862-2295-8157-9432D8595318-22,2016_160_2A5598AA-03FA-0CC4-B804-42C3303A0564-22,2016_939_441E0A9A-F243-3AC0-E4FE-1BB9848A405B-22,2016_409_5D795E20-F911-6F60-D061-15344BAB780C-22,2016_575_F318D8F8-E6B2-A87E-76C4-1D2E3F12B563-22,2016_345_36F1D77F-4197-CA29-FA59-0D9D893F9C2D-22,2016_6_135EA231-57B0-58CF-2359-CC6192DAACA3-22,2016_699_D6B9D81C-4348-5AD0-CA15-94E0EBEF0125-22,2016_953_BA3EB26F-01CB-37D4-5B32-C5F5E048C56E-22,2016_455_842FCBB2-ECF4-9171-8DC1-3373885C13A9-22,2016_566_87BFE208-BF0A-263C-F7E4-F5762DF3106D-22,2016_589_712B2408-4767-DF36-CAAD-638E681F24FD-22,2016_503_213251A0-8627-FF40-2D01-60D2A898A74B-22,2016_293_E1AE1AF4-3753-5CA9-FEE4-2F9DE03FD0FB-22,2016_813_E3754DC5-2029-4612-D23A-769AF07BA58C-22,2016_18_209292AF-3229-22D0-DF9F-8F89F4573153-22,2016_839_57301A0C-1690-0A7A-D6FD-F761D1F5E5D1-22,2016_373_4D186D0F-AD7E-0C4A-FEA3-90CCB38DAE07-22,2016_522_76FC544C-39DA-B681-E74E-1CB1172EBD05-22,2016_350_4EB3E472-ACF9-CD9C-2BB6-79A0E66DC036-22,2016_772_0E55656C-59CB-F4E4-4B80-D2CF218C9B74-22,2016_858_A24950CA-4D32-54E0-E8BD-EBC725C04437-22,2016_162_D39BA97F-3ED8-BF46-7A86-5313570CAFD8-22,2016_96_B4CA9383-C1C0-EB61-FD5F-730B7BA24CB3-22,2016_599_9BC81DF3-A766-3547-337C-B0C52324E9A4-22,2016_828_7E71CF5E-6D2A-7B02-011D-7EA008C0FD38-22,2016_824_18D1BCA9-75EC-B49E-B424-2985403B881B-22,2016_701_73AF40C8-F62B-BA90-B5E7-119DF167CFD4-22,2016_658_11523B2D-9B53-BA54-9967-54EADB8A59AD-22,2016_399_5A7359FB-7FE4-74EB-27E9-BF7475ECDD58-22,2016_430_96C5BA92-BDFC-C648-3960-6040B31E0B41-22,2016_551_5A2E7C5F-5008-644B-E5B7-347F61ED2E14-22,2016_548_06D463D4-CDDA-9EF5-8687-B5EF8C174085-22,2016_461_0F82122A-C472-CD34-8579-6F9A7B46CE57-22,2016_760_BF0FF2D9-4021-DFC1-22BA-7E9214144465-22,2016_974_D1919358-F4A7-8B08-14EB-0820B6800F2B-22,2016_753_F2A56CB9-85E1-735D-5DBF-66AB97B7D4E7-22,2016_480_78E22A0D-9A52-F7AF-F845-58D38D37E96D-22,2016_68_345C1982-A8CD-8795-7B75-A82BFC6DD677-22,2016_611_D630655B-813E-CA10-79A9-83B70C8C4EC1-22,2016_140_76B8DAF6-1E93-82CE-AA94-4F594264B17C-22,2016_777_A8ACC121-CB7D-4FEE-B614-6374B6D639E2-22,2016_327_8D2132E4-EA9A-C3E0-C337-9257FAC99F02-22");
		String contentid_14 = ("2016_241_F49C642A-435D-5F38-66B0-0DB6247C0A60-22,2016_519_2556A0E2-C18D-615B-2BD5-C1DB35FB2A60-22,2016_943_9919188C-9DD5-EFE8-10C7-EC49FA0E7328-22,2016_611_A23261A8-AA17-271B-2600-0A0E46739ACF-22,2016_131_D063B26D-D138-A039-71D0-CECC8A575DE5-22,2016_351_58D7C91D-D8C0-9535-3816-525F2EC693B8-22,2016_337_5AD9DFDD-4A9E-E1FF-243B-1BEF336551D1-22,2016_127_85168683-C570-5DEA-E271-76E7B29471AB-22,2016_403_3B0CC7BF-A801-4429-7C85-04B7DEB3A7CD-22,2016_487_17FFE455-13E2-527F-E35E-1E8045545565-22,2016_550_B41923D4-5E89-FCEF-279E-CD30577497BE-22,2016_639_84A6A97D-6CA3-63A4-458C-83AC53F84F8A-22,2016_155_02E9D80A-069C-DD39-D7B1-39FC857DE109-22,2016_442_6F0C8C17-AF5B-CBC2-E4F0-A7127CD04D31-22,2016_929_86421188-D61F-5539-B51F-B56F51C7352C-22,2016_680_EC1A1606-FBF3-4903-D108-B78E20DADD28-22,2016_360_DA36DEDA-2087-02DB-923B-A9617C73AF08-22,2016_705_9C4423DA-A9B4-4A5C-3197-5E61B1A5A535-22,2016_569_DF0077D7-97D7-C0A8-F24C-73E23D4FE495-22,2016_646_90373C47-AF3E-7DA3-CF16-2E2180099F7E-22,2016_471_432CB720-97B0-C91E-9AD7-0465B4D611ED-22,2016_642_1305175E-E5BD-E760-6D8D-4AC81BA77B7B-22,2016_123_1A4B8FAD-0B69-48EF-699C-56F869629AA4-22,2016_508_63E72F09-B433-6BF9-4739-38ED6E45F735-22,2016_923_47CC8D78-FDC0-820A-62C1-B7042AEAD1AA-22,2016_932_E407F025-D670-3C81-0300-A9FC786D70DC-22,2016_928_9A461D9A-52FE-5FC7-B3E4-4001BC490AD1-22,2016_759_D98A3EE5-96CD-49CB-AC23-D4CEF76D07F5-22,2016_875_74337CBE-3E15-5EA8-0FCD-8294F91C7AAD-22,2016_530_0336E6F3-F46D-3B21-5D38-71831BC7DEB3-22,2016_279_640E19F6-2D5A-D92A-72D9-547E11D748D8-22,2016_672_74BDCBF0-427D-5D12-544E-423D4B888027-22,2016_608_717FC2A7-B6AA-DFCF-423B-CB63D80E5B49-22,2016_934_F781B39E-7561-C6FD-F6A4-39ED1183B8FB-22,2016_177_6C968F87-E6BE-211C-F9CB-53E145862BE7-22,2016_53_7FDABDC6-580E-88D5-37EE-44BA73DD24AE-22,2016_619_2FE99A67-CBDD-F424-36FE-3121F0F2A9AD-22,2016_12_568D9C8E-6C1B-DDD0-BC6B-4570239C376A-22,2016_229_5673AD5A-ACFA-1FF4-356F-02625223D2D1-22,2016_393_0C29E2D8-E470-4C2A-EA19-F6545CD0530A-22,2016_152_750B82A7-0AD0-7737-4D9C-4F1975540E6D-22,2016_719_DB777FCD-9ED5-0D93-2439-EE0852EFD3F1-22,2016_285_D904EC36-9316-8118-250B-96D4DE0C285F-22,2016_334_82CE3A3F-85E4-EB3D-D202-20FE5E87E485-22,2016_973_DB6D6A4A-3E03-0206-6001-B4E6FA48B07B-22,2016_516_90680E6E-CA44-465A-02B7-A422C9E07DB1-22,2016_450_FD19FF52-156E-2C97-F147-4DB4111BAF3D-22,2016_94_6483AF37-E0E8-EFC2-7D40-E84E0162AE49-22,2016_742_69130383-EB03-7911-C9A1-309FD366F7F3-22,2016_506_20705C01-8B33-3D1D-78CA-0AEDFF0CFF56-22");

		String contentid_15 = ("2016_382_FFB4EC5D-622A-8ABA-7B0C-5AC03B4C9957-22,2016_79_6CE12BC5-24B0-4141-8F52-DF28E66BE6D3-22,2016_309_CE0DD225-C55A-64B2-0F9F-E6AA5A96FF55-22,2016_297_419691F0-F5F5-5E52-BE60-B85FB04DC104-22,2016_964_AA76C2E9-7932-41BA-4499-0D1074A82A95-22,2016_541_37BFA9D9-63CA-B5A1-0108-6A29B3316AC8-22,2016_833_323C19F7-2A68-2B33-F92D-772C83772C6C-22,2016_948_0A89605F-450E-3966-9F4C-AAA57006A8B9-22,2016_832_E0823CD6-6802-E818-17E2-2E813D96F5FB-22,2016_893_8E3ACDD6-9AD8-A4AD-8482-7FE227FFCE1A-22,2016_987_6288346F-029D-2A84-3CC0-6A9F16639BE2-22,2016_271_E734A661-0AB7-444D-F87B-162C16475E90-22,2016_662_1276A19F-3B53-5919-D473-F84366563816-22,2016_138_B78D8A57-D9DD-EC48-8171-FE8E92D8403C-22,2016_945_7A6F9A56-1C5C-86F4-0A19-E9A07559CD0E-22,2016_771_F7508665-B9FC-D274-8A19-689ADA4ADA4C-22,2016_839_61315024-818D-B51F-4953-A70A1EEDDA08-22,2016_935_69FC6242-5BA0-8647-39F9-93C247349DFA-22,2016_752_237387A7-8A1E-A1D6-5D9D-0EAF01A9D59E-22,2016_622_54AF15F6-3C49-44CD-334D-5590B8EBCDBB-22,2016_513_E6A6996B-8F0B-9362-BFC1-7A5994AEFC71-22,2016_798_75BA28F8-47A1-BF4F-2585-1A2B207717D0-22,2016_126_458DC2F6-0C10-94C3-968D-6B4614586747-22,2016_370_B3DE92EF-C046-2677-1FFA-DBB2804CA868-22,2016_263_5C7A7987-D986-F7E8-5C0C-AF9744FED30D-22,2016_4_BA04355C-D76E-1856-52DF-4269DF2A9EFF-22,2016_770_DEF77F03-7BF3-3F60-59B4-60E8177570E8-22,2016_874_F5717EAB-CC37-EAA3-69AE-F79F7497403D-22,2016_760_A90C884E-83F0-7785-37AD-68A8448F6C2F-22,2016_505_78FEE597-22EC-8B82-0A44-2BE5F6FB3A20-22,2016_158_867AEFFC-820E-8123-CFEB-7D235BFFC5D9-22,2016_645_B3E6F167-A0F5-5758-50DB-60B17971CBC3-22,2016_418_313130A5-F162-7990-8F52-9339AEBC689B-22,2016_634_3FCA8B82-0BC2-6968-35BB-DDAB468929D5-22,2016_576_25B0A930-D1C3-55D9-3E37-B45D745B82B9-22,2016_566_D59273B1-1BC1-8032-E09C-C26D3239BD8D-22,2016_688_7BD84D11-5D68-5123-B1E8-A4B92070E795-22,2016_973_399FB43C-FBF2-A91A-A53F-E5015786882A-22,2016_422_45CF4F05-AFFD-C7E1-8461-9825B03A003F-22,2016_911_F8245394-2703-F1CA-F806-109A159CD983-22,2016_716_63D77A9B-4079-6C69-0BDD-1CDC7AC9CD23-22,2016_645_2A450013-FE06-C066-4C44-4C481341AD09-22,2016_931_0A253E92-04C2-9A9B-4105-26CE0D56D7E0-22,2016_830_EFD6E283-46F1-766E-2ED4-A92F5D021B3C-22,2016_422_7D92B09A-016F-70B4-19A4-B262259277C4-22,2016_187_595B7983-02BB-C56A-D3C1-AD02EE4CB6CE-22,2016_4_1CFE1D9A-8B35-D120-4BA4-675E74B446BB-22,2016_183_A7C0221A-EBA8-9817-858F-8B7E54A1388D-22,2016_937_46655A46-C68E-BF92-335A-856C5B41E709-22,2016_496_1FAD5542-6C19-6A47-01B0-6DBA23BCE1B9-22");
		String contentid_16 = ("2016_981_0AD675E3-7A11-7F68-88E4-4FE21A06CBFB-22,2016_55_944F4FC3-7BD5-FC81-5EBC-377B4F4FBB58-22,2016_65_DB378A23-BDA0-249A-1F38-1F95F156E111-22,2016_648_1830DD71-C6DA-E912-1672-A74B1CABFD08-22,2016_234_FD167EBE-5306-B5A1-BFBC-7DA115608551-22,2016_948_3BF95A06-05FA-FE55-2858-CBCF27BF4907-22,2016_129_6EDF7FA3-5F0E-2969-2556-922AC34FA5BB-22,2016_797_73BC1B62-166A-AFF9-5240-35E2D49A4E09-22,2016_180_D42BC1D0-35D8-BFF8-535C-341D1175E011-22,2016_178_A7DC3416-DEE4-CC23-5F92-16690098D301-22,2016_23_720745B4-2011-A586-080B-7BE96ED67D8B-22,2016_139_0ED4265A-A667-7646-DFD2-484F28C97329-22,2016_363_B439B82A-A5FE-6B8D-45E9-60021AA9673B-22,2016_29_9EE51411-1463-5E40-B77C-B6C4DF3D1D9D-22,2016_742_6EA62B29-7B2C-FA5A-C906-450FD374B66B-22,2016_882_C1D937DE-3117-BEC0-6269-A3A002ED5218-22,2016_767_457B45FD-784C-54F4-2854-CAF86F7EA11A-22,2016_218_D3501A06-B56B-6B73-5B2A-1B40BA5157F1-22,2016_395_0DD7ED6D-F3C4-3E4D-E420-21138545022B-22,2016_372_33ACF748-AF9A-5209-2F40-527E827105B5-22,2016_659_94BD9D45-2D95-FFE1-6ECC-A48BB1AF0C24-22,2016_689_371ACDDF-18CD-BB9E-448D-DB7A590F85FB-22,2016_830_08327814-EAC4-07F0-1BE5-653D6AC0BFF2-22,2016_139_AE5DB809-FF7A-F2AD-BF2A-A972016305A1-22,2016_650_88445375-533B-C60C-F13C-D70739590236-22,2016_432_AAFA238B-E9F8-F14A-C1F4-4E1F664AF0C6-22,2016_738_DF21CA04-6A4B-7688-4508-7983937C8E19-22,2016_84_9C39DC01-153C-3F3B-3903-3668637819A3-22,2016_429_A4D04341-1D7E-DB4F-8DD7-EFF7EF7E7840-22,2016_685_17294567-2419-4E27-0569-EEDCE60D9124-22,2016_410_AFB3B6A6-1102-BC5B-B6DE-C349C744F23A-22,2016_177_A7703E4B-718D-0521-0A4D-F50A46CB6522-22,2016_994_6E2BDFAB-EFE1-296A-2FC0-2E0A72CE7957-22,2016_628_82035D2F-F4E5-F90B-5FFD-9180BFAFA8F1-22,2016_200_5FB04C74-4BDE-0153-4DF8-C322D2D37F85-22,2016_733_E2CBDE92-82A5-2B30-64B8-C42814A83481-22,2016_701_CC4016B2-3A4C-BF80-A667-73AB3F564D88-22,2016_341_3AB0F737-899F-347F-FAE1-66114232F432-22,2016_525_18F02B9F-F16B-04A4-ED2C-99B45C4A6493-22,2016_666_51C57C4D-BD97-AA61-1EF1-F40C88E3475F-22,2016_540_F1A21E7B-CC42-C38D-E44E-2A6A899D0161-22,2016_220_D7DC055C-0E5B-4B45-464A-73A9AEBE13E6-22,2016_389_69B410CA-5BE4-352F-1333-63C94AF59FBB-22,2016_406_99E9C407-5FC0-AB1B-0AD1-DB747CE9EC72-22,2016_855_67DA7AE9-619A-88C6-9632-9EF664E4A445-22,2016_0_CD2C1A30-37D9-1036-8B73-25A4494F0841-22,2016_47_7EAF9281-12FB-9F97-4928-C8D08E2C0F7A-22,2016_577_16626637-BF50-02FE-5089-7C5360620FFF-22,2016_820_B1495826-A729-3EEC-CAE4-8F8FA1D04A08-22,2016_612_939040C6-09C8-0630-B837-D078E9E00A88-22,2016_7_8442C3F6-486D-5226-0B76-E633EE3A1C30-22,2016_383_2F47ACDF-9016-1233-C2EB-9A2E18B51A96-22,2016_587_CFAE5A20-F7F0-3BD0-7430-FAD75CA30592-22,2016_520_0D6686CE-BC9F-461E-1D0B-FCAC2AFE4C60-22,2016_517_1EDD7858-9FDD-6E3E-EAAE-8C454A34CB64-22,2016_89_D643CD84-2A1D-C17A-1B18-4179F90D260D-22,2016_45_74D43B08-B900-DD41-041A-659432499D5D-22,2016_49_F4E42B10-525E-CDF1-C697-DB1796CD7C61-22,2016_328_C7EC675B-3B0C-7AFB-4AF2-F79F3FB5DF89-22,2016_684_D9D4971F-541D-3C48-8FE3-72B4B53E9686-22,2016_223_8B3F0899-F7E7-FBD5-03EE-66376AFBC240-22,2016_648_35FEB331-FAA4-2C43-122B-74C073A10721-22,2016_453_2BD5B405-7512-D1B6-7C25-3720F02CC2A0-22,2016_395_449C71C4-610D-7C2D-B65E-6918DAF43573-22,2016_323_C6683DEC-37F0-422F-DC0D-716888BB0132-22,2016_643_F6986FE5-2EDA-6DDC-47B4-1B70FC06F437-22,2016_983_7F048BB6-0F0E-C560-1B82-6399EDD89821-22,2016_268_BC22B2AC-42A9-F865-6B45-03936BC9630B-22,2016_459_EB3F4654-60F7-CA15-52FC-44F8654B1B5B-22,2016_512_20B67DEE-8C6C-B0BC-4332-A37BFB7BE394-22,2016_882_B61A7E94-BEEE-F431-674E-361A721DC558-22,2016_312_2F44D88A-ED52-CEF6-B6B3-E60935629F80-22,2016_699_9EA7D89D-A1BF-B803-9584-DE82FD2DD6D2-22,2016_389_BDADA8FA-54B5-145D-CE17-E4DD2E53F79B-22,2016_135_4FC0E8BB-D5E6-A4D6-FE92-6920E69C25D7-22,2016_578_55120B14-235D-AC53-CB6A-E35D6191B605-22,2016_862_3E78DF9B-E35E-2208-3F60-782207051F31-22,2016_675_58357CD2-25D6-8863-1991-7C039D416D52-22,2016_223_B10F40C8-CCAB-DF99-5D51-9C46B00472A6-22,2016_185_22DAE52A-CD28-44AA-7523-328CCB68BAB0-22");
		String contentid_17 = ("2016_21_9065C062-473E-EDA0-1211-932C6D80B780-22,2016_199_B96251C5-BD57-1F4D-4E68-5025C5A109D1-22,2016_614_B8093F46-15F8-CA46-9B3A-43BC8294321B-22,2016_18_249749FF-87ED-CFBD-5999-8A2ACEC1120B-22,2016_120_7FA4AC58-0357-DCE9-B0E4-3D3E54983D53-22,2016_905_3C94D1D8-9DA6-68C0-DEB1-50A5F03C5933-22,2016_990_903A36D1-AF9E-9FBF-1F61-4516D6F6CEEA-22,2016_618_19D64619-CFFE-1671-7873-DCFA69142791-22,2016_346_4EC1849E-6C1B-76F5-6400-F533DBA5CC44-22,2016_429_5C485AF4-E342-0066-C376-2E3F180FA40E-22,2016_173_5E5EBF5C-A335-E085-84B9-B0556448935F-22,2016_769_52FB6DDB-E1E0-2B41-E04C-E30D717F7A20-22,2016_376_FFAF4ADC-2FCA-FBB1-E5A4-5727C991E897-22,2016_851_A15252E3-B5A2-B1E2-1CB1-C678DC5F867C-22,2016_531_05B7A4C8-3401-A21A-CA42-E7511192B70D-22,2016_579_28599794-3472-C36B-CC72-A8E02A732B96-22,2016_314_9B457799-103C-5741-7D67-C7726076CF37-22,2016_810_7796EE70-363D-2E89-5C51-FEB5F089E67E-22,2016_303_C213C64E-3729-467E-75CC-8E83CA51E274-22,2016_89_E216CAD2-1F31-6307-0266-05DFEF341046-22,2016_718_A4AC3E6A-3F15-128B-0B00-311264E0416E-22,2016_926_BE3DD323-A049-723A-BAE8-396D2084484C-22,2016_407_3485F21F-3416-03B6-8F55-65344FAC45C2-22,2016_293_D4ACD72A-6515-9F99-FDE8-7B912CCC3D38-22,2016_485_A08ACCB3-6235-4D19-6BA1-443113065E55-22,2016_562_C1A6365D-4E96-80A1-FEEA-70B091C3CEA3-22,2016_315_2EBC4279-C537-6D82-AB58-99A106871AE6-22,2016_404_2500AD27-9CC7-47B5-F1C9-E59F5581AD9D-22,2016_44_EF70CB62-DBB6-EB38-E663-EBF257CE63D5-22,2016_52_7F380909-860A-5D2B-684A-DA1C83B42E72-22,2016_200_108920DF-83EC-BD0E-B5B9-E731A5D94120-22,2016_50_DDE113DF-9A67-853C-39C4-A27FC4340EB7-22,2016_272_E0B41567-90F5-DAFF-6388-4F035B88F16E-22,2016_46_087E4A53-0274-EC43-AFEB-5ABE690C520C-22,2016_525_34C032B7-0BDC-EA88-4602-EDB0F99ADAA8-22,2016_726_9B253547-EBEF-0D41-750E-7F9874327765-22,2016_358_927DA2EE-F985-C881-612E-ED27A3E0D367-22,2016_523_8994EAE2-6686-2E4C-0370-F5FB3E737D16-22,2016_333_28B3B616-BFDC-7149-A6E2-E6CDBC18D289-22,2016_257_2AD78C2C-79BE-3FB8-2A54-5BF9648221FF-22,2016_320_13585055-050C-1DA7-E3E5-A5F250970DAC-22,2016_161_2FE0FB91-BAAD-1331-4E39-58EB3645F186-22,2016_556_9430DC7E-8155-BD30-E63D-2B78AE6F8FAC-22,2016_153_5DC2748D-31B9-1910-380B-0581A191E211-22,2016_270_3DCB44C0-4EAC-1B2C-DEA1-27DDFD626346-22,2016_83_99C4E707-300A-D671-5682-1AE749CD408F-22,2016_401_09397B6B-2063-7B55-386B-8DDE23AF21DD-22,2016_485_43F7FAE8-3585-2F63-7284-C176C9DC2AFC-22,2016_567_5F2038F5-0FFC-9565-34D9-74DEB238EADB-22,2016_27_7197BBF5-D5EE-AEAF-D01D-2C71F1AED084-22,2016_293_2DDDB2E7-FAF9-231C-B24C-B1D10FF19ACB-22,2016_401_176BCBED-2795-939B-E6BE-E5C8716B7F31-22,2016_85_C6AA3562-64A4-B778-A8FF-C5F3F11C0B50-22,2016_382_50658A95-8E01-BEF7-102E-B1B5A97ABE75-22,2016_800_206101F6-99E0-4DE2-4437-F289CAE0C0DC-22,2016_334_F339D5F1-9F53-7101-6A6C-F8DFC5EED400-22,2016_910_F8C59C0B-58EC-A6DD-2039-579464EEAF58-22,2016_148_A327FFA0-F92B-1340-A63B-735F6282F5C6-22,2016_708_C374EAD0-6BBE-1818-5B98-64249D031553-22,2016_552_A5AD4EEA-2DD7-5A8F-2AD7-C951F9468EBC-22,2016_566_1BD7D944-CBDB-F986-652A-2A1897B71FF0-22,2016_562_B7427C46-D342-4C5A-F79C-F4B9F21F3676-22,2016_230_D7F573F5-E47C-0339-A89A-43A8130FB7DC-22,2016_927_82D9AC58-F419-80C9-7069-873F188EB125-22,2016_465_4669CBB0-878D-FB00-1B9E-038B696109B0-22,2016_956_E415F1DF-BC46-B68B-C32E-0BAF72DC87F6-22,2016_155_E52058CC-2235-FB30-01C8-3A6229CF89FF-22,2016_98_A1A4CD5A-8E25-D451-D133-EC9152BA4F8E-22,2016_655_4ABED40A-115D-F9EF-148D-DA6685065C3E-22,2016_593_AB21F390-D476-AD88-2614-B09381749001-22,2016_454_6E8D3BA0-3610-E2B2-9650-B9713F5A4C13-22,2016_147_DE503282-AE58-F003-63FF-39C1EA912C88-22,2016_955_59CD59B8-4583-5008-B25D-D11AF5D5EDB0-22,2016_107_8D08CB1B-AB76-03D8-4025-6E539A353B76-22,2016_111_DADE24DB-61E2-432C-F877-2E4552D5496E-22,2016_381_92078573-366A-63EB-9E6E-6FA88457C3A9-22,2016_772_618CEADA-73C2-F14F-FCE5-0E3BEDFACDC6-22,2016_892_C426995E-F730-1A9E-2B01-DCDE336160A1-22,2016_311_AE0C826B-EE9B-0D89-E8D5-63B692AAE573-22,2016_464_E1F65CFA-F899-5BFB-B343-0AFBA56D8919-22");
		String contentid_18 = ("2016_546_2BCC6F48-7DD1-D6F1-3170-EA3FD332D992-22,2016_648_9BA4A3E4-E261-1BFD-D6E9-44ED6AA4BB74-22,2016_813_9F5BBEBB-7748-70FD-6C79-6B54D08B537D-22,2016_799_99D5D94C-69B4-A243-B114-320958E75764-22,2016_318_050450C9-D302-9212-BA5A-F13C29B01FA3-22,2016_163_49829327-34BD-931C-483F-5FB59D9DB3CE-22,2016_509_EA04E549-9953-D13C-1965-39B5E1FBA489-22,2016_444_1578BB6D-9F39-CD08-78A4-0080E0BAE4DE-22,2016_354_FB07BB11-74FF-076A-5C9C-B7C9F3044413-22,2016_786_8CEC82B8-A550-873C-79DB-EF9E40CF65F3-22,2016_462_AD9DB106-9548-11F1-BDBF-C770D2E9913B-22,2016_105_93EB7287-AF40-1826-2322-01DA1E5413FF-22,2016_758_9A096581-612D-1724-03B8-BA81A7CDF2DD-22,2016_167_6A0A908F-D5D7-A335-79FE-29EE7F1DE280-22,2016_719_40C149CA-BE2C-7EE7-318A-B84059E4939C-22,2016_893_64A9014F-A3CD-143C-33DD-915F9471C168-22,2016_594_B204F038-DB87-86C3-79E0-FB74D34C642A-22,2016_124_9F41D533-704E-74D8-0F50-72293E62662D-22,2016_519_3FF13AF3-FFA4-06CD-B475-B0ACDD4B2FA4-22,2016_474_E3C51838-4958-0311-C7A0-29D9033C69C4-22,2016_374_C75CDF6E-BB35-7283-19BF-AC48F5F20BBA-22,2016_31_FF72F8EA-5A6D-88A9-C5E6-AD9050FB1FAA-22,2016_176_F96FC119-B1CB-FB93-B3FD-7026A6358DBE-22,2016_349_8EC5071F-9783-2FBB-C4A2-FA2D97CBDA78-22,2016_292_3759BB84-A2EB-327B-1488-D873478BE74E-22,2016_178_41200802-AD67-B4CF-1CF5-65DE82951A57-22,2016_722_D5D549BA-DD4A-6914-EA55-2688E4428C08-22,2016_983_571F113D-0F76-CF1C-06EC-906ABFD03A69-22,2016_305_34C4936D-9622-F19A-5F50-109EFA03EB7F-22,2016_621_074D09CD-89DA-B01E-C0F0-A4519727F8B3-22,2016_145_FD0F448C-5C23-3126-F173-5A65D405FF90-22,2016_593_A8D5EC71-A761-A3FB-FE85-F4A6F986161B-22,2016_390_49B5DC93-4692-B8F7-CF97-8D3069623CD1-22,2016_38_5527232A-FF66-48A0-0AF0-F6DA6F4FFD64-22,2016_107_8FEF574C-9875-8600-9144-60967F30D5F0-22,2016_96_78A9271A-6842-C9A9-5206-1AAE49326E56-22,2016_631_7380295A-7EE2-0287-A58C-D8C42F113009-22,2016_876_0E85D16B-68DC-E1EA-9286-1CE067F234AC-22,2016_91_367E1F52-6827-B984-21B3-7D2B016C67E6-22,2016_718_30C8A316-C4D3-7C14-4DDB-127080D688F4-22,2016_418_55348BD0-6427-8706-32D9-94B20D5D50BD-22,2016_401_286F4268-C48B-3D92-4FC8-9D186D010E3A-22,2016_29_26E3851F-E798-A26D-8EE0-8530842AB6BC-22,2016_771_C8E4FF74-995D-997C-8275-04DEB1D5C6DF-22,2016_841_B82865FB-D523-3DC5-453F-5C720312FA93-22,2016_829_A639335B-61BB-B157-B1AC-9E35FC2D8516-22,2016_86_E73AE9E1-3652-5B59-8471-20BE3ECEBDA1-22,2016_307_C8CB4803-90CC-5112-A3E6-691243B367B5-22,2016_925_93BAABEB-A3BF-561D-3E02-5C75D68D5AEB-22,2016_740_01DAE540-B3C8-C646-271C-DDF4597C6F8C-22,2016_825_247EE372-12C0-9ED3-AE63-CD44658BBCC0-22,2016_639_EB6EF298-DEE4-F2D3-E0E2-5948AAB377D9-22,2016_162_50A2623A-477C-4C87-EEF5-61CED02E01B5-22,2016_783_E737346F-B4B4-DB5E-20FE-33D2323AE5AD-22,2016_0_13CEF43C-18AE-7FED-008A-0EEAAFF7F24D-22,2016_539_47D4B348-1241-5CC2-C855-2C637FB55A71-22,2016_739_DC48F3BC-A816-E8C2-8AD5-B90D29AD42BE-22,2016_944_BFF4409F-6CB8-4A52-EB05-2601034D8E05-22,2016_999_856A3FEE-E185-EAD9-39BF-89D41B8EB6A1-22,2016_805_9C189059-B67C-6069-5976-63B4713FDABB-22,2016_307_220A44FF-961E-424D-3340-3B90CF9DD9B4-22,2016_228_D0B15444-5052-A3B5-C6BD-126D1798C1F2-22,2016_320_4BF04E37-6FC8-EF89-567B-B1D31EB0504D-22,2016_229_3841B695-2944-E723-9672-EB18DB34670B-22,2016_369_C635B5C7-AD69-2F74-FCFF-0C085371746E-22,2016_552_E94A76E7-8717-0A83-2F12-046BF1C17D80-22,2016_727_5A15C068-8E19-7344-A3C6-9E90A04729A5-22,2016_742_D1D00176-9DD1-FE20-3193-01969E8B2D6E-22,2016_184_3A8CDCE2-1707-D448-8FE5-E80DBD8A0722-22,2016_358_078FE5CB-EE06-ECAE-322C-73BF931974C2-22,2016_317_1D269E2D-F7CD-3F4C-AB19-21930D27B721-22,2016_320_079D6029-A677-11A6-0EB7-E0A73866FB93-22,2016_228_CC17AB68-4EF0-C727-3458-4AA0034C88DD-22,2016_773_69A458FD-8C3E-ADA8-F6D6-855AE275AE15-22,2016_979_32A877E6-02B4-7907-3D66-EE25D025C8AF-22,2016_944_21501DDE-FAC9-C9A6-BF8F-0AA0CAFC106C-22,2016_151_54D3BC68-DDAC-A264-5115-AE55F25A466C-22,2016_837_15082FA4-E853-4194-6136-F754EB23665C-22,2016_21_EC5BAFEB-813E-DB81-9691-74A4B2F885E1-22,2016_490_AF9FB7BE-785C-925D-4D0E-731FD46D1FE2-22");

		String contentid_23 = ("2016_907_906C63FC-EEEB-4198-A791-59BAE8E44571-22,2016_620_99911B64-0C05-8D77-90B4-87BAAE8F9EEC-22,2016_853_D8E9EAC0-DB7B-E3B2-7B58-5E6DE57A3971-22,2016_123_46A49E97-1A82-4A36-CF0C-B4CB8BF89ABF-22,2016_825_B01139DE-30BA-2E05-743A-B17139E0F53B-22,2016_490_F85FA869-2645-4ECC-CE61-795B2B9A31AD-22,2016_942_E3804850-D8CD-2A4C-3977-06B522C12358-22,2016_136_757D3660-1036-4C8B-3EF0-8A25CE518C28-22,2016_379_052642F6-BCC1-8A1C-BC94-77A587597A74-22,2016_505_1113A967-7817-0CA3-53B3-0602BCB3FC74-22,2016_163_CA679D50-CFAD-A0BD-DDFF-3D20EC706931-22,2016_730_08E9723C-C9E7-9D99-1A15-1FBEFA43D5BD-22,2016_152_86812E01-FCF6-32A8-03B9-171EA138776A-22,2016_948_5CBAFDD5-9BA4-3C25-A418-8999009AE2C2-22,2016_902_84E3D952-F667-BB1A-4B66-34656C70A9E4-22,2016_520_C1C96E53-F0D5-BCA2-889C-52674AC75163-22,2016_242_EB9D3B7D-8636-AB0D-7B88-88C12607A047-22,2016_419_BFAC1430-FBC9-7599-4EE6-27D33E930362-22,2016_416_B7A49659-B6BA-0F78-25D9-52CBA3B63142-22,2016_90_43B9590B-2D38-FA52-EF8D-F0C6487BE623-22,2016_85_C3C63832-01D1-AFE0-F8E8-44135E62AEF6-22,2016_507_88E30F3F-C8D6-5D3C-FCE2-D75E854123AB-22,2016_290_0A3E672D-2E3D-D280-7330-5F1962125EC6-22,2016_222_77D7DC5C-1077-F9CE-953C-003C1E668D23-22,2016_825_B99BBE21-3FC4-9C80-F6F5-FDD85EAB97FE-22,2016_681_A0C5588D-A656-1D81-A6AD-51C7F71BC8B5-22,2016_730_EC741AAE-834C-F58A-F856-53F1B9FC72DF-22,2016_339_5A484BDE-4FDD-31CF-65E5-0E4164DD96FF-22,2016_734_D8535216-D645-50EB-120B-08AEA7CF3873-22,2016_215_D6DC5E75-A6CE-5F03-8B03-B4C4B4C14CC3-22,2016_753_0047535E-C6D7-D718-6853-3C612A81A302-22,2016_301_B9D03F03-E6D6-A87C-FB61-3D25F40ED94A-22,2016_315_7BDFAF42-6D79-97D7-ACCC-D2AC9DBCF9AA-22,2016_525_DB7D4B6B-A525-2D55-8C7D-2C6A0224EAED-22,2016_811_16F4CE2F-60C2-D314-B3F0-0A296983BC58-22,2016_685_C8DFF17D-5D36-2726-EEC9-9AE0E1D4EDF1-22,2016_13_3739AA7A-712A-32CA-106F-CE3451BD7958-22,2016_837_B4AFE066-CE53-32BD-2916-9A10079C0E23-22,2016_750_8407D9AE-BD97-EAFA-92D3-CF0D6EF55BFC-22,2016_463_40E7CD3B-D5D9-9DF9-1F71-1C00DE23C509-22,2016_135_6EB74FEE-3D68-EDA7-DB4E-AA1FBA986F40-22,2016_963_C31A2ABF-319F-F409-3144-8C9FA8DB8711-22,2016_894_C1711D7B-13C9-1769-1804-2632FAD8477E-22,2016_513_00012260-F58A-E0C7-555F-0850FE0D43D9-22,2016_701_11DE43C3-E291-2128-BEA6-88E0513C899B-22,2016_853_74EC8722-597B-E3D1-97D9-93E378BF1289-22,2016_82_D1C5FE29-199B-943F-1837-86975FBF6632-22,2016_888_699F16FD-5D35-43EA-4910-00DA99AEB563-22,2016_946_85451F12-853F-B6FF-2954-3D7442B5047A-22,2016_683_4DEA8AB5-2003-B13C-0B31-3AB4846C72DE-22");
		String contentid_24 = ("2016_696_C2E687C6-1CFA-E469-BD63-280269DE3F77-22,2016_855_73E7C1C4-C409-21C4-40E9-6AC7E6C6FEFA-22,2016_170_EDBC7749-0C15-6464-6098-3D244BD1BDA2-22,2016_705_4B225EA2-F444-0C35-A6A0-2A13955ECEA2-22,2016_527_E2F5D53E-8812-A8A4-C32C-3C5F9B43C8BC-22,2016_362_68E3F5F5-0407-CB46-4FB1-14ADDACF7C98-22,2016_941_EA561C0A-4F51-EA91-29E8-73A8A4768E4B-22,2016_470_F24E7089-71C4-E094-50E3-FA3FE79EE634-22,2016_742_ADB41EBA-B695-58F2-E80D-09E4F085CD06-22,2016_86_5FC94310-3649-C52A-15D8-820145C9D0A5-22,2016_892_A6994E30-A94F-87CD-20E7-370C0875E80E-22,2016_886_33E3ED53-F63F-9F1C-C80D-613573EE01BB-22,2016_804_F75E3FDE-5E28-6314-2BC6-EF9E351C3816-22,2016_174_D5FF3DE2-EABC-C16D-0899-631078DBBD6B-22,2016_976_3A4D5AFC-848F-1527-335C-F4EF4C71D04F-22,2016_434_F93F07EC-61AA-7C38-48F5-4A412FF95026-22,2016_201_7EAB4076-4E1A-256B-7E55-9C12C33459A3-22,2016_145_F23885DE-6893-CEE9-7988-AA3D0A44E7F3-22,2016_454_77904238-895E-7684-2B7D-360C8E8D6C09-22,2016_24_5A63A8D4-7D15-55DB-6D45-B5225DEBE6B3-22,2016_760_51680B1A-956A-4125-D632-35ED69BA972C-22,2016_394_3F425918-F03D-67E1-21FE-5F8ACF1E9227-22,2016_15_0DA569A6-5413-E00C-6158-7FF9CF8F1AD7-22,2016_665_E9C9AD3B-170A-2614-12CE-FA76629FC6AB-22,2016_704_3C38F539-D818-2BB0-8E95-BA5E4C125CD9-22,2016_933_29248A85-9FCF-A4FC-67F5-E2F070542A6D-22,2016_505_0745D286-539B-5696-D577-928A37F10578-22,2016_36_BA38E56C-ADE6-D104-D13B-13637C22EAB4-22,2016_525_E9E644AE-76D8-28E3-2352-DDC8E1C2B7E8-22,2016_739_1D2AC1A2-6B7D-FCDC-6353-9219940813C6-22,2016_508_D067EB29-C2B5-308C-C5B3-46410D0C98AE-22,2016_835_28462510-9BB1-A923-1590-7C5CF8776967-22,2016_899_CAA7BCB6-E817-3B9A-E85C-F5D886ED919B-22,2016_887_C01BC2CF-F041-5AEE-88AB-A90A51A7D24E-22,2016_349_4C883323-E3C5-4A9E-96E7-353FAAAA5620-22,2016_830_B706CA91-C160-B760-E41E-938AAA7D81B7-22,2016_28_A2173B51-9BEF-0CAC-ADF0-85D580331E0F-22,2016_549_6705E21D-C145-475A-3A42-5153773DD52C-22,2016_512_EAD0DD20-A3EA-080A-AB83-05CC97BC0922-22,2016_777_452FCA5B-0396-7CC5-29EC-4C623D1131EF-22,2016_78_F9755809-EDE2-8EB6-C9AE-78DB8D31C99F-22,2016_172_15712A4E-C27D-861C-6ACA-EE5D52543891-22,2016_717_41789574-0D22-5885-F1A4-18DF01257B0C-22,2016_896_31E9F9E4-E273-0E33-F783-C9AB12D07059-22,2016_927_2BF7D690-B59E-F890-4F54-E290E5B43C93-22,2016_806_C1C4C7F3-8155-1AFD-797E-64AB6894F331-22,2016_110_2B150920-1B65-C3B3-5134-23A7E7D1C9CB-22,2016_624_1E0F8474-03C6-A241-8C52-1DE0C87D1E07-22,2016_374_67F8B6E6-849D-659C-65CC-853F829CC518-22,2016_685_4D957EE8-D34D-56E3-75C8-0FC102135352-22");
		String contentid_25 = ("2016_709_7098EE88-4E24-9688-5D8F-3831C9D11171-22,2016_493_1AE2BD70-2E6E-F8CE-FCAA-A5C5E0870567-22,2016_785_1F70EAA7-1332-6B52-2BC2-1D5E80987398-22,2016_674_A0CFDFE8-D12D-B7EA-5B13-00865088F12D-22,2016_769_3E32EE26-0B4A-D7FB-55D9-14EB39A4D67D-22,2016_270_F85B8633-CC05-1201-2236-FCC7FCE40B6C-22,2016_822_0090C52D-A0E3-030D-97CE-D5D805AC4917-22,2016_436_172CD736-1AEE-0D28-DC52-AD0BBACB3494-22,2016_593_6F675161-5F7A-4056-C766-306FC805651C-22,2016_319_98ADA694-80E3-9E9E-985F-83641B9AD076-22,2016_111_EA651372-E35E-C509-AA24-1445CBF9F297-22,2016_641_B35EF1A6-A69F-B616-7348-F1A388EA25C1-22,2016_583_31FC55B6-398A-D101-DE9C-E3DF0848C5CF-22,2016_712_8D18FED2-11B7-85A8-7D29-6774E8E5A84C-22,2016_4_6601A400-ECE8-F437-F54B-64B13B8AE460-22,2016_302_FA056AEF-7CB5-FCBB-CCCC-222C1F7D6054-22,2016_679_4F29E6AA-B665-ED18-C54C-ECA91E154550-22,2016_720_1F1CB15E-D272-36FA-0FA9-86D7E3C6EE4F-22,2016_113_EE919489-DF16-3919-A1E3-3D9FA465A3DC-22,2016_47_E02966CD-7E1F-BDCB-5EB2-EEC869F58FCA-22,2016_598_2834AF9F-1B91-48C5-1F21-5508A10E3510-22,2016_907_F69B6FCD-56E6-2EC1-6D15-6D9890D91B97-22,2016_68_79DCCDC5-F572-E9FE-3750-046A7901EC01-22,2016_48_E74FE4E4-D5A4-14A1-8515-725B14CB08A0-22,2016_952_56F9AEF3-AE00-1372-7BEA-98257DD551C8-22,2016_325_CE0A1807-9EBC-6C8E-114B-959930F5293F-22,2016_725_3B889141-A440-693E-677D-80CF9812998E-22,2016_844_97CAE5EF-4EC6-FB6C-9C4B-AC9A58E9FC7E-22,2016_115_AC8534C2-B8BE-4B05-34C4-916FFE8B4C17-22,2016_592_E3380D28-8565-6337-87F0-7C6FCC495C15-22,2016_527_601B5290-B2DB-BFA5-0D32-3B44A82761A3-22,2016_539_12E8A80B-5FF0-A389-365F-2691CA51536F-22,2016_727_377E1601-A068-CD2E-F370-2F1884E1EFB5-22,2016_479_4F30D292-025D-9218-4FB8-49DD872EBFEA-22,2016_166_263E7238-6E49-E8CD-AEB4-89F118E66C32-22,2016_478_EB0CC81F-CBF7-A83F-1AC5-9DBBB27C63A4-22,2016_445_25AA08AA-BEEC-B718-04D1-7A89F6144DFF-22,2016_45_73911662-0501-68A6-D95B-4012A31422B4-22,2016_56_4708C1C6-DEEE-C7D7-D52B-8E4940589723-22,2016_281_52E135C2-FF2A-B3DD-0FD8-A64B97F02B70-22,2016_266_77BAE4D7-FAC7-7D35-A896-EE069F43EFFB-22,2016_358_819B368C-D392-EE82-AEED-EF6F6B17366D-22,2016_842_57487EAD-EDE8-2F85-91BE-76C67FA4C2F5-22,2016_313_EA5A4F95-A117-0ACF-AAA2-D7DF2F2BC904-22,2016_65_955463C3-5FB5-F67F-1C5F-33EB456163AE-22,2016_454_907AB58E-3500-480C-BBD6-513D05D89DC7-22,2016_648_A41E5D7A-EE5A-02E8-02D2-6DDB584B50E0-22,2016_470_A8137736-D859-1B78-46A9-F283E0C08631-22,2016_335_30B9B7DF-2012-7DBA-E8E0-906B8154F669-22,2016_581_876F2C6C-D599-0658-7408-88CA0B4A1717-22");
		String contentid_26 = ("2016_756_DB5C565F-FCE4-1C58-AE8B-9D7A7AFD62D9-22,2016_973_7F8A6B9D-40C4-A1EB-FB57-6A7C239BC1DC-22,2016_209_8B3E7E90-5319-8F5E-0AFD-F5692F1C96C4-22,2016_682_E783DC02-3621-9BF5-E082-1BF3CD9E1732-22,2016_23_6E81F4DF-C4F8-4EE0-B371-4AED97805A08-22,2016_978_40BAADA0-5381-60F6-9C4E-64F5DED048DB-22,2016_70_BCF0E99D-FD46-E14B-53BD-B91A32B19FD2-22,2016_65_ADB715BE-444D-C49A-C0AF-69DC1160B4FD-22,2016_536_C9AEEF64-F3D7-5B06-3C27-D512E0A23C5E-22,2016_728_C92CC67B-2FBB-43E3-6F66-9F5B4DAF9FA4-22,2016_938_D87896C6-F356-BD27-6341-DDA1FC672890-22,2016_47_ADD39711-7D76-5B35-5268-360DE628A588-22,2016_531_564D1823-C1DC-973A-706B-0FF504462E47-22,2016_281_00FABA24-8ED3-AFCD-61C7-C2F34DB800BE-22,2016_37_308ECC4D-A428-7150-89CD-5300D0BD5BDB-22,2016_761_80D8698A-F8DC-0CDB-EF9E-6ED6C80D2E38-22,2016_362_E45C8480-6269-0341-12F5-41BB8CCCCEDE-22,2016_904_37DC481B-FB8C-F4D5-0A3D-CF898995DD83-22,2016_694_58DF1E98-F558-41B1-5254-4EFE90FD62D1-22,2016_905_A4755D84-6E7F-291B-D905-6AAAB159CF63-22,2016_737_CDACE8EA-9771-98EF-9B50-5AED3055D453-22,2016_869_E476AD49-2A7C-E59D-E2D9-656085B2D0FE-22,2016_782_9E0457FD-B1D3-B936-6CC4-FAB6025888F4-22,2016_493_7F109A1E-511C-94D0-C1F5-EE80E8D470C1-22,2016_994_CBFE092B-65D8-674C-0DEE-2F2F3168CA57-22,2016_995_D0DA63C6-8F73-CD4C-8143-9A0B9AB1FC30-22,2016_536_CE6C034E-0F12-DB33-78DF-FB0C516AFE38-22,2016_233_C291EC0E-0E0F-2DB3-FEA4-B3A297375D70-22,2016_883_E63B5566-0D7A-C20D-A569-61C0F0612754-22,2016_922_4AD983F0-868D-513E-D429-38F75D60DA45-22,2016_198_3503D946-7C3E-610B-6BD3-B99D5DCA3D71-22,2016_373_DAC8D44D-BD9C-421C-D79C-19879AC47ED4-22,2016_629_FFBB81AC-BBA1-2DE2-165B-BA9DC5186558-22,2016_617_09094F22-C96C-D0E2-D7D9-41C033763D28-22,2016_214_AD6E999B-8534-372D-F26E-A7C4B6DBE56B-22,2016_696_00DFE56A-4A69-971C-418D-5B6CD597D9AA-22,2016_574_9FDDA347-A055-CF55-D6AB-1478D79D97E9-22,2016_279_357D9D82-DE24-FEE3-EB19-F5467CFE90FE-22,2016_190_4AB00C3D-89F4-2CEA-1044-01688B572F78-22,2016_537_D93DAF63-B68A-2B9A-1900-A2B38D3507E4-22,2016_567_5AE6E765-0686-5084-0927-D156E5F1C1DF-22,2016_778_4FB383B0-2D76-266A-87B8-97BD57584618-22,2016_104_720AF981-A34E-DC56-8E21-194D3F787F9E-22,2016_43_3B39D2C0-C748-4FCA-C0F3-2E544FE2BE71-22,2016_826_7030F330-42C6-5C2C-7E35-B291A9D3B5F6-22,2016_373_AFAFAC91-7565-2457-12A2-FC3420EF3380-22,2016_428_6BCA9F4C-534B-5026-9E20-6FC048DE075B-22,2016_959_655FFFE5-537B-CEFE-ED3D-65BAAEE5124F-22,2016_208_40C43870-F9D5-9D16-6752-761FBC8E523E-22,2016_236_1BA9B657-03F3-E175-8CC0-06231134F39C-22");
		String contentp = "";
		String date = "201602";
		for (int j = 23; j < 27; j++) {
			switch (j) {
			case 8:
				contentp = contentid_8;
				date += "0";
			case 10:
				contentp = contentid_10;
				break;
			case 11:
				contentp = contentid_11;
				break;
			case 12:
				contentp = contentid_12;
				break;
			case 13:
				contentp = contentid_13;
				break;
			case 14:
				contentp = contentid_14;
				break;
			case 15:
				contentp = contentid_15;
				break;
			case 16:
				contentp = contentid_16;
				break;
			case 17:
				contentp = contentid_17;
				break;
			case 18:
				contentp = contentid_18;
				break;
			}
			String[] contentids = contentp.split(",");
			log.info("[" + BEGIN_VALUE + "]start");
			BEGIN_VALUE = date + j;
			DOWN_LOAD_FILE_PATH = "E://image/queryFromOffline/" + BEGIN_VALUE + "/";

			for (int i = 0; i < contentids.length; i++) {
				log.info("i=" + i);
				contentID = contentids[i];
				queryAndDownload();
			}
			log.info("[" + BEGIN_VALUE + "]end");
		}
	}

	public void allUpload() {
		for (int j = 01; j < 31; j++) {
			if (j >= 10) {
				BEGIN_VALUE = "201603" + j;
			} else {
				BEGIN_VALUE = "2016030" + j;
			}
			for (int i = 0; i < 100; i++) {
				uploadExample();
			}
		}

	}

	public static void main(String[] args) throws ZipException, IOException {
//		log.debug("--upload-->上传文件后发送报文耗时");
//		ZipFile zipFile = new ZipFile(new File("E://SunECMOffline/data/LS_DOC/1/20201028/4e/5e/202010_94_64_863F2740-90D8-479E-521F-BC59AB9F5FE0-1.zip"));
//		Enumeration<? extends ZipEntry> entrys = zipFile.entries();
//		while (entrys.hasMoreElements()) {
//			ZipEntry zipEntry = (ZipEntry) entrys.nextElement();
//			String zipName = zipEntry.getName();
//			if(zipName.startsWith("file/")){
//				InputStream in = zipFile.getInputStream(zipEntry);
//				String md5 = DigestUtils.md5Hex(in);
//				String fileNo = zipName.substring(5);
//			}
//
//
//			System.out.println(zipEntry.getName());
//		}
//		for(ZipEntry entry:zipFile.entries()){
//
		// }
//		 = zipFile.getEntry("file");
//		System.out.println(entry.isDirectory());

		// getAllFilesSizeOfFolder();
//		for (int i = 0; i < 50; i++) {
			Client client = new Client();
			client.uploadExample();
//			new Thread(client.createTask(i)).start();
//		}
//		String  fileStr =  "e:/image/2.jpg";
//		Random r =new Random();
//
//		File file = new File(fileStr);
//		while(file.exists()){
//			System.out.println(fileStr);
//			fileStr = "e:/image/"+r.nextInt(10)+".jpg";
//			file = new File(fileStr);
//		}
//		System.out.println(fileStr+"+++");
		// client.checkOut();
		// client.updateExample();
		// client.checkIn();
//		client.uploadExample();
		// client.getToken();
//		 client.heightQueryExample();
		// client.queryExample();
		// client.logout();
		// client.deleteExample();
		// client.operAnnotation();

		// String xml =
		// "<BatchBean modelCode=\"INDEXOBJ\"
		// token_code=\"431C3DCAF0C7302FA12ABB9D712CF0C0\"
		// token_check_value=\"123456789\" user=\"admin\" isBreakPoint=\"false\"
		// isOwnMD5=\"false\" isDownLoad=\"true\"><index_Object
		// contentID=\"20120917_555_169_A7484EA3-1875-D6B9-62F3-8E56A51469A8-2\"><customMap><START_TIME><string>20120803</string></START_TIME></customMap></index_Object></BatchBean>";
		// xml = xml.replaceAll("BatchBean", "BatchDownBean");
		// System.out.println(xml.indexOf("BatchBean"));
		// System.out.println(xml.indexOf("BatchBean") == -1);
		// BatchDownBean batchDownBean = XMLUtil.xml2Bean(xml,
		// BatchDownBean.class);
		//
		// System.out.println(batchDownBean.isDownLoad());
		// client.logout();
	}

	public static void getAllFilesSizeOfFolder() {
		for (int i = 0; i < 100; i++) {

			String filePath = "D:/juan_198/2019/" + i;

			File file = new File(filePath);
			long fileSize = 0;
			if (file.exists()) {
				File[] files = file.listFiles();
				for (File f : files) {
//					fileSize += f.length();
					System.out.println(f.length());
				}
			} else {
//				file.mkdirs();
			}
//			log.info("filePath=" + filePath + ",folderSize=" + fileSize);
		}
	}

	public void uploadByStreamExample() {
		ClientBatchBean clientBatchBean = new ClientBatchBean();
		clientBatchBean.setModelCode("DOC4");
		clientBatchBean.setUser("admin");
		clientBatchBean.setPassWord("111");
		clientBatchBean.setBreakPoint(false); // 是否作为断点续传上传
		clientBatchBean.setOwnMD5(true); // 是否为批次下的文件添加MD5码
		// 若内容模型配置有安全校验
//		 clientBatchBean.setToken_check_value(token_check_value);
//		clientBatchBean.setToken_code(tokenCode);

		// =========================设置索引对象信息开始=========================
		ClientBatchIndexBean clientBatchIndexBean = new ClientBatchIndexBean();
		// clientBatchIndexBean.setContentID("20200820_27_53_FC349026-08C7-D134-7FEF-B7477FCBEDD5-21");
		// clientBatchIndexBean.setContentID(contentID);
		// clientBatchIndexBean.setVersion("1");
		// 索引自定义属性
		// clientBatchIndexBean.addCustomMap("BUSI_SERIAL_NO", "2014040120");
		clientBatchIndexBean.addCustomMap("START_TIME", "20200820");
		clientBatchIndexBean.addCustomMap("LSH", "AAA");
		// clientBatchIndexBean.addCustomMap("LOAN", "打算多少电");
		// =========================设置索引对象信息结束=========================

		// =========================设置文档部件信息开始=========================
		FileMD5Verify fileMD5Verify = new FileMD5Verify();
//		//原图MD5
//		File file0=new File("C:\\Users\\<USER>\\Desktop\\png01.png");
//		String MD50=fileMD5Verify.getFileMD5String(file0);
		ClientBatchFileBean clientBatchFileBeanA = new ClientBatchFileBean();
		clientBatchFileBeanA.setFilePartName("DOC4_PART");
		// ClientBatchFileBean clientBatchFileBeanB = new ClientBatchFileBean();
		// clientBatchFileBeanB.setFilePartName("LINK_IMG_B")

		ClientFileBean fileBean1 = new ClientFileBean();
		File file = new File("C:\\Users\\<USER>\\Desktop\\200k.jpg");
		try {
			FileInputStream inputStream = new FileInputStream(file);
			fileBean1.setInputStream(inputStream);
		} catch (FileNotFoundException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		// 生成MD5
		String md5 = fileMD5Verify.getFileMD5String(file);
		fileBean1.setMd5Str(md5);
		clientBatchFileBeanA.addFile(fileBean1);

		// =========================设置文档部件信息结束=========================

		// =========================添加文件=========================
		clientBatchBean.setIndex_Object(clientBatchIndexBean);
		clientBatchBean.addDocument_Object(clientBatchFileBeanA);
		// clientBatchBean.addDocument_Object(clientBatchFileBeanB);
		try {
			String resultMsg = clientApi.uploadByStream(clientBatchBean, groupName);
			// log.debug("#######上传批次返回的信息[" + resultMsg + "]#######");
			System.out.println("#######上传批次返回的信息[" + resultMsg + "]#######");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
