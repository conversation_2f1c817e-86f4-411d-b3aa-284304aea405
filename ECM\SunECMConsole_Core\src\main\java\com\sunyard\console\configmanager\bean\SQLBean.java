package com.sunyard.console.configmanager.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;

/**
 * SQL对象
 * <AUTHOR>
 *
 */
@XStreamAlias("SQLBean")
public class SQLBean {
	// sql语句
	private String sql;
	// sql绑定参数
	private Object[] params;
	// 需要验证的影响行数
	private int affectNum;
	// 是否验证，默认不验证
	private boolean flag = false;
	// 参数为null时替换为"NULL"
	public static final String NULL = "NULL";
	
	public String getSql() {
		return sql;
	}
	public void setSql(String sql) {
		this.sql = sql;
	}
	public Object[] getParams() {
		return params;
	}
	public void setParams(Object[] params) {
		this.params = params;
	}
	public int getAffectNum() {
		return affectNum;
	}
	public void setAffectNum(int affectNum) {
		this.affectNum = affectNum;
	}
	public boolean isFlag() {
		return flag;
	}
	public void setFlag(boolean flag) {
		this.flag = flag;
	}
	public String toString(){
		StringBuffer sBuffer = new StringBuffer();
		sBuffer.append("sql=[" + sql + "], params=[");
		if(params != null){
			for(int i=0; i<params.length; i++){
				sBuffer.append(params[i]).append(",");
			}
		}
		sBuffer.append("],affectNum=[" + affectNum + "],flag=[" + flag + "]");
		return sBuffer.toString();
	}
}
