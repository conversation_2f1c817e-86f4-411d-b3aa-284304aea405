package com.sunyard.util;

/**
 * 索引对象系统默认字段
 * <AUTHOR>
 *
 */
public class IndexObjSystemDefaultColumn {
	/** 内容编号 **/
	public final static String CONTENT_ID = "CONTENT_ID";
	/** 修改时间 **/
	public final static String UPLOAD_TIME = "UPLOAD_TIME";
	/** 修改人 **/
	public final static String UPLOAD_USER = "UPLOAD_USER";
	/** 检入检出用户 **/
	public final static String CHECK_USER = "CHECK_USER";
	/** 检出标识 1检出表示该批次被锁定 0检入表示该批次解锁 **/
	public final static String CHECK_FLAG = "CHECK_FLAG";
	/** 检出时间 12位 分 **/
	public final static String CHECK_TIME = "CHECK_TIME";
	/** 服务器编号 **/
	public final static String SERVER_ID = "SERVER_ID";
	/** 所在服务器组 **/
	public final static String GROUP_ID = "GROUP_ID";
	/** 版本 **/
	public final static String VERSION = "VERSION";
	/** 内容状态  0不可用（表示逻辑删除）1可用**/
	public final static String CONTENT_STATUS = "CONTENT_STATUS";
	/** 批注路径 **/
	public final static String ANNO_PATH = "ANNO_PATH";
	/** 内容接收时间 **/
	public final static String RECEIVE_TIME = "RECEIVE_TIME";
	/** 归档标志 0未归档 1已归档**/
	public final static String IS_ACTIVE = "IS_ACTIVE";
	/** 基础版本 初始化为0 如果迁移到版本3则基础版本为3 **/
	public final static String BASE_VERSION = "BASE_VERSION";
	/** 迁移状态  1表示记录与文件都需要迁移 2已迁移 3记录待更新 **/
	public final static String MIGRATION_STATUS = "MIGRATION_STATUS";
	/** 清理失败次数 默认0**/
	public final static String CLEARFAIL_NUM = "CLEARFAIL_NUM";
	/**	 是否最新版本 */
	public final static String IS_LAST_VERSION="IS_LAST_VERSION";
	
	/**
	 * 获取系统默认字段
	 * @return
	 */
	public final static String[] getColumns() {
		String[] strs = {CONTENT_ID, UPLOAD_TIME, UPLOAD_USER, CHECK_USER,
				CHECK_FLAG, CHECK_TIME, SERVER_ID, GROUP_ID, VERSION,
				CONTENT_STATUS, ANNO_PATH, RECEIVE_TIME, IS_ACTIVE,
				MIGRATION_STATUS, BASE_VERSION,"NEAR_PATH", CLEARFAIL_NUM,IS_LAST_VERSION};
		return strs;
	}

	public final static String getColumnsString() {
		return  "CONTENT_ID, UPLOAD_TIME, UPLOAD_USER, CHECK_USER," +
				"CHECK_FLAG, CHECK_TIME, SERVER_ID, GROUP_ID, VERSION," +
				"CONTENT_STATUS, ANNO_PATH, RECEIVE_TIME, IS_ACTIVE," +
				"MIGRATION_STATUS, BASE_VERSION,NEAR_PATH, CLEARFAIL_NUM,IS_LAST_VERSION,";
	}
}