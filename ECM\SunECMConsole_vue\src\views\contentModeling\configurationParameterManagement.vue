<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.par_key"
        placeholder="参数项"
        style="width: 200px"
        class="filter-item"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-if="this.hasPerm('paramShowSearch')"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain
        round
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain
        round
        @click="handleclear"
      >
        清空
      </el-button>
      <el-button
        v-if="this.hasPerm('addParamShow')"
        class="filter-item"
        style="margin-left: 10px"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        添加配置项
      </el-button>
        <el-button
        v-if="this.hasPerm('conParamSearch')"
        class="filter-item"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="configParam"
        >
        配置参数
    </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >

      <el-table-column label="参数项ID" v-if="false">
        <template slot-scope="{ row }">
          <span>{{ row.par_show_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="参数项"  align="center" min-width="20%">
        <template slot-scope="{ row }">
          <span>{{ row.par_key }}</span>
        </template>
      </el-table-column>

      <el-table-column label="参数显示名" align="center" min-width="20%">
        <template slot-scope="{ row }">
          <span>{{ row.par_show_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="参数说明" min-width="10%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.par_remark }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="操作"
        width="250"
        align="left"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <el-button
            v-if="hasPerm('modifyParamShow')"
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
            style="margin-bottom: 5px; margin-left: 10px"
          >
            修改配置项
          </el-button>

          <el-button
            v-if="hasPerm('delParamShow')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="handleDelete(row, $index)"
            style="margin-bottom: 5px"
          >
            删除配置项
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
    
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="参数项" prop="par_key">
          <el-input v-model="temp.par_key" />
        </el-form-item>
        <el-form-item label="参数项显示名" prop="par_show_name">
          <el-input v-model="temp.par_show_name" />
        </el-form-item>
        <el-form-item label="参数说明" prop="par_remark">
          <el-input v-model="temp.par_remark" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false">
          取消
        </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="titleper2" :visible.sync="CONDialogVisible">
      <div class="app-container">
        <div class="filter-container">
        <el-input
            v-model="conlistQuery.par_key"
            placeholder="参数项"
            style="width: 200px"
            class="filter-item"
            @keyup.enter.native="paramSearch"
        />
        <el-select v-model="conlistQuery.group_id" placeholder="请选择存储服务器组" @change='getServerModelCode()'>
        <el-option
          v-for="item in storeGroups"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-select v-model="conlistQuery.model_code" placeholder="请选择内容对象">
        <el-option
          v-for="item in storeObj"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>

        <el-button
            v-if="this.hasPerm('conParamSearch')"
            v-waves
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            size="mini"
            plain
            round
            @click="getCONList"
        >
            查询
        </el-button>
        <el-button
            v-waves
            class="filter-item"
            type="warning"
            icon="el-icon-delete-solid"
            size="mini"
            plain
            round
            @click="paramClear"
        >
            清空
        </el-button>
        <el-button
            v-if="this.hasPerm('addConParam')"
            class="filter-item"
            style="margin-left: 10px"
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="paramAdd"
        >
            添加配置参数
        </el-button>
    </div>
        <el-table
          :key="tableKey2"
          v-loading="conlistLoading"
          :data="conlist"
          border
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column label="参数ID" v-if="false">
            <template slot-scope="{ row }">
              <span>{{ row.par_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="参数项" min-width="20%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.par_key }}</span>
            </template>
          </el-table-column>
            <el-table-column label="参数值" min-width="20%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.par_val }}</span>
            </template>
          </el-table-column>
            <el-table-column label="作用于全局" min-width="20%" align="center">
            <template slot-scope="{ row }">
             <span v-if="row.par_all == '1'">是</span>
             <span v-if="row.par_all == '0'">否</span>       
            </template>
          </el-table-column>
          <el-table-column label="服务器组" min-width="20%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.group_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="服务器" min-width="20%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.server_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="模型" min-width="20%" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.par_model }}</span>
            </template>
          </el-table-column>
          <el-table-column label="参数状态" min-width="20%" align="center">
            <template slot-scope="{ row }">
              <span v-if="row.par_state == '1'">启用</span>
              <span v-if="row.par_state == '0'">禁用</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            min-width="30%"
            class-name="small-padding fixed-width"
          >
        <template slot-scope="{ row }">
          <el-button
            v-if="hasPerm('modifyConParam')"
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="paramModify(row)"
            style="margin-bottom: 5px; margin-left: 10px"
          >
            修改
          </el-button>
          <el-button
            v-if="row.par_state == '0' && hasPerm('ableConParam')"
            size="mini"
            type="primary"
            icon="el-icon-open"
            @click="paramStart(row, $index)"
            style="margin-bottom: 5px"
          >
            启用
          </el-button>

          <el-button
            v-if="row.par_state == '1' && hasPerm('disableConParam')"
            size="mini"
            type="danger"
            icon="el-icon-turn-off"
            @click="paramStop(row, $index)"
            style="margin-bottom: 5px"
          >
            禁用
          </el-button>
        </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        v-show="contotal > 0"
        :total="contotal"
        :page.sync="conlistQuery.page"
        :limit.sync="conlistQuery.limit"
        @pagination="getCONList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="CONDialogVisible = false">
          取消
        </el-button>
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="contextMap[condialogStatus]" :visible.sync="conFormVisible">
      <el-form
        ref="condataForm"
        :rules="conrules"
        :model="con"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="参数项" prop="par_key">
            <el-select v-model="con.par_key" placeholder="请选择参数项" :disabled="condialogStatus == 'update'">
            <el-option
              v-for="item in parkeys"
                :key="item.par_key"
                :label="item.par_show_name"
                :value="item.par_key"
            />
          </el-select>        
          </el-form-item>
        <el-form-item label="参数值" prop="par_val">
          <el-input v-model="con.par_val" />
        </el-form-item>
        <el-form-item label="作用于全局" prop="par_all">
            <el-select v-model="con.par_all" @change='parAllChange()'>
            <el-option
              v-for="item in applyToAll"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
            </el-select>
        </el-form-item>
        <el-form-item label="作用于服务器组" prop="par_group" v-if='isParGroup'>
            <el-select v-model="con.par_group" @change='parGroupChange()'>
            <el-option
              v-for="item in applyToGroup"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
            </el-select>
        </el-form-item>
        <el-form-item label="作用于服务器" prop="par_server" v-if='isParServer'>
            <el-select v-model="con.par_server" @change='parServerChange()'>
            <el-option
              v-for="item in applyToServer"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
            </el-select>
        </el-form-item>
        <el-form-item label="作用于模型" prop="par_model" v-if='isParModel'>
            <el-select v-model="con.par_model" @change='parModelChange()'>
            <el-option
              v-for="item in applyToModel"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
            </el-select>
        </el-form-item>
        <el-form-item label="存储服务器组" prop="group_id" v-if='isGroup'>
            <el-select v-model="con.group_id" placeholder="请选择存储服务器组" @change='parGroupIdChange()'>
            <el-option
            v-for="item in constoreGroups"
            :key="item.id"
            :label="item.text_text"
            :value="item.id">
            </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="存储服务器" prop="server_id" v-if='isServer'>
            <el-select v-model="con.server_id" placeholder="请选择存储服务器">
            <el-option
            v-for="item in constoreServers"
            :key="item.id"
            :label="item.text_text"
            :value="item.id">
            </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="内容对象" prop="obj_id" v-if='isModel'>
            <el-select v-model="con.obj_id" placeholder="请选择内容对象">
            <el-option
            v-for="item in constoreObj"
            :key="item.id"
            :label="item.text_text"
            :value="item.id">
            </el-option>
        </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="par_state">
            <el-select v-model="con.par_state" placeholder="请选择状态">
            <el-option
              v-for="item in states"
                :key="item.key"
                :label="item.display_name"
                :value="item.key"
            />
            </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="conFormVisible = false">
          取消
        </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="condialogStatus === 'create' ? createCon() : updateCon()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {getParamShow,addParamShow,delParamShow,getConParam,getParKey,addConParam,startConParam,stopConParam} from "@/api/conParamManage";
import {getContentServerGroup, getRelContentServer, getUnRelOrRelContentObject} from '@/api/contentServerGroupManage'
import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import {parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination

import global from "../../store/global.js";

export default {
  name: "ComplexTable",
  components: { Pagination },
  directives: { waves,elDragDialog },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
  },

  data() {
    return {
      titleper2: "配置参数",
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        par_key: "",
        title: undefined,
        type: undefined,
        sort: "+par_key",
      },
      tableKey2: 0,
      conlistLoading: true,
      conlist: null,
      contotal: 0,
      conlistQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        title: undefined,
        type: undefined,
        model_code:"",
        group_id:"",
        sort: "+par_key",
      },
      parkeys : [],
      storeGroups : [],
      storeObj : [],
      constoreGroups : [],
      constoreServers : [],
      constoreObj : [],
      importanceOptions: [1, 2, 3],
      sortOptions: [
        { label: "ID Ascending", key: "+role_name" },
        { label: "ID Descending", key: "-role_name" },
      ],
      temp: {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
        par_remark:""
      },
      con: {
        id: undefined,
        importance: 1,
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
        group_id :"",
        server_id :"",
        obj_id :""
      },
      consubmit: {
        id: undefined,
        importance: 1,
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
        group_id :"",
        server_id :"",
        obj_id :""
      },
      dialogFormVisible: false,
      conFormVisible: false,
      CONDialogVisible: false,
      dialogStatus: "",
      textMap: {
        update: "修改配置项",
        create: "新增配置项",
      },
     condialogStatus: "",
      contextMap: {
        update: "修改配置参数",
        create: "新增配置参数",
      },
      states : [
          { key: '1', display_name: '启用' },
          { key: '0', display_name: '禁用' }
      ],
      applyToAll: [
          { key: '1', display_name: '是' },
          { key: '0', display_name: '否' }
      ],
      applyToGroup: [
          { key: '-1', display_name: '是' },
          { key: '0', display_name: '否' }
      ],
      applyToServer: [
          { key: '-1', display_name: '是' },
          { key: '0', display_name: '否' }
      ],
      applyToModel: [
          { key: '-1', display_name: '是' },
          { key: '0', display_name: '否' }
      ],
      isParGroup : false,    
      isParServer : false,      
      isParModel : false, 
      isGroup : false,    
      isServer : false,      
      isModel : false,      
      rules: {
        par_key: [
          { required: true, message: "参数项必输", trigger: "blur" },
        ],
        par_show_name: [
          { required: true, message: "参数项显示名必输", trigger: "blur" },
        ]
      },
      conrules: {
        par_key: [
          { required: true, message: "参数项必输", trigger: "blur" },
        ],
        par_val: [
          { required: true, message: "参数项必输", trigger: "blur" },
        ],
        par_state: [
          { required: true, message: "参数状态必输", trigger: "blur" },
        ]
      },
      downloadLoading: false,
    };
  },

  created() {
    this.getList();
  },

  methods: {
    getList() {
      this.listLoading = true;
      getParamShow(this.listQuery).then((response) => {
        this.list = response.root;
        this.total = Number(response.totalProperty);
        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },

    getCONList() {
      this.conlistLoading = true;
      getConParam(this.conlistQuery).then((response) => {
        this.conlist = response.root;
        this.contotal = Number(response.totalProperty);
        setTimeout(() => {
          this.conlistLoading = false;
        }, 1 * 100);
      });
    },

    handleclear() {
      this.listQuery.par_key = "";
    },

    paramClear() {
      this.conlistQuery.par_key = "";
      this.conlistQuery.group_id = "";
      this.conlistQuery.model_code = "";
    },

    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    sortChange(data) {
      const { prop, order } = data;
      alert(prop);
      if (prop === "par_key") {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === "ascending") {
        this.listQuery.sort = "+par_key";
      } else {
        this.listQuery.sort = "-par_key";
      }
      this.handleFilter();
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
        par_remark:""

      };
    },
    resetCon() {
      this.con = {
        id: undefined,
        importance: 1,
        remark: "",
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
        group_id :"",
        server_id :"",
        obj_id :""
      };
    },
    resetISFalse() {
        this.isParGroup = false;
        this.isParServer = false;
        this.isParModel = false;
        this.isGroup = false;
        this.isServer = false;
        this.isModel = false;    
    },
    handleCreate() {
      this.resetTemp();
      this.dialogStatus = "create";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    createData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          this.temp.id = parseInt(Math.random() * 100) + 1024; // mock a id
          this.temp.author = "vue-element-admin";
          this.temp.optionFlag = "createShow";
          this.temp.par_old_key = this.temp.par_key;
          addParamShow(this.temp).then(() => {
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Created Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },

    handleUpdate(row) {
      this.temp = Object.assign({}, row); // copy obj
      this.temp.timestamp = new Date(this.temp.timestamp);
      this.temp.par_old_key = this.temp.par_key;
      this.dialogStatus = "update";
      this.dialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    updateData() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          const tempData = Object.assign({}, this.temp);
          tempData.timestamp = +new Date(tempData.timestamp);
          tempData.optionFlag = "updateShow";
          addParamShow(tempData).then(() => {
            this.getList();
            this.dialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Update Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },

    handleDelete(row) {
      this.openDelConfirm(row.par_key).then(() => {
        delParamShow(row).then((response) => {
          this.getList();
        })
      })
    },

    openDelConfirm(name){
      return this.$confirm(`此操作会同步删除参数项 ${name} 关联的所有配置参数,是否确定删除？`,'提示',{
        confirmButtonText:'确定',
        cancelButtonText:'取消',
        type:'warning'
      })
    },

    paramAdd(){
      this.resetCon();
      this.condialogStatus = "create";
      this.conFormVisible = true;
      this.resetISFalse();
      this.getParkeys();
      this.$nextTick(() => {
        this.$refs["condataForm"].clearValidate();
      });
    },

    paramModify(row) {
      this.resetCon();
      this.oldcon = Object.assign({}, row);
      this.con.timestamp = new Date(this.oldcon.timestamp);
      this.isParGroup = true;
      this.isParServer = true;
      this.isParModel = true;
      this.isGroup = false;
      this.isServer = false;
      this.isModel = false;
      this.con.par_id = this.oldcon.par_id;
      this.con.par_key = this.oldcon.par_key;
      this.con.par_val = this.oldcon.par_val;
      this.con.par_all = this.oldcon.par_all;
      this.con.par_state = this.oldcon.par_state;
	  let par_group=this.oldcon.par_group;
	  if(par_group==null||par_group==''||par_group=='不关联'||par_group==0){
          this.con.par_group = "0";
	  }else{
          this.isGroup = true;
          this.getConGroups();
          this.con.group_id = this.oldcon.par_group;
          this.con.par_group = "-1";
	  }
		let par_server= this.oldcon.par_server;
		if(par_server==null||par_server==''||par_server=='不关联'||par_server==0){
            this.con.par_server = "0";
		}else{
            this.isServer = true;
            this.getConStoreServers(this.con.group_id);
            this.con.server_id = this.oldcon.par_server;
            this.con.par_server = "-1";
		}
		let par_model=this.oldcon.par_model;
		if(par_model==null||par_model==''||par_model=='不关联'||par_model==0){
            this.con.par_model = "0";
		}else{
            this.isModel = true;
            this.getConStoreObj(this.con.group_id);
            this.con.obj_id = this.oldcon.par_model;
            this.con.par_model = "-1";
		}
		if(this.con.par_all == 1){//作用于全局
            this.isParGroup = false;
            this.isParServer = false;
		}
		if(this.con.par_group == 0){//不作用于服务器组
            this.isParServer = false;
		}
      this.condialogStatus = "update";
      this.conFormVisible = true;
      this.$nextTick(() => {
        this.$refs["condataForm"].clearValidate();
      });
    },

    getParkeys(){
        getParKey().then(response => {
        this.parkeys = response.root;
      })
    },

    paramStart(row) {
      startConParam(row).then((response) => {
        this.getCONList();
      });
    },

    paramStop(row) {
      stopConParam(row).then((response) => {
        this.getCONList();
      });
    },

    configParam() {
      this.getCONList();
      this.getGroups();
      this.getServerModelCode("0");
      this.resetCon();
      this.CONDialogVisible = true;
    },

    getGroups(){
        getContentServerGroup().then(response => {
        this.storeGroups = response.root;
      })
    },
    getConGroups(){
        getContentServerGroup().then(response => {
        this.constoreGroups = response.root;
      })
    },

    getServerModelCode(mid){
        if(mid=="0"){
          this.conlistQuery.group_id = 0;
        }
        getUnRelOrRelContentObject(this.conlistQuery).then(response => {
          this.conlistQuery.model_code = "";
          this.storeObj = response.root
          if(this.conlistQuery.group_id==0){
            this.conlistQuery.group_id = "";
          }

        })
    },

    getConStoreServers(group_id){
        this.con.group_id = group_id;
        getRelContentServer(this.con).then(response => {
        this.constoreServers = response.root
        })
    },
    getConStoreObj(ggid){
        if(ggid.length==0){
          return;
        }
            this.con.group_id = ggid;
            getUnRelOrRelContentObject(this.con).then(response => {
            this.constoreObj = response.root
            })
    },

    checkCon(tmpcon){
        this.consubmit.par_key= tmpcon.par_key;
        this.consubmit.par_val= tmpcon.par_val;
        this.consubmit.par_all= tmpcon.par_all;
        this.consubmit.par_state= tmpcon.par_state;
        let par_group = tmpcon.par_group;
        let newpar_group = tmpcon.par_group;
        let newpar_server = tmpcon.par_server;
        if(tmpcon.par_all == 1){//作用于全局
        newpar_group = "0";
        newpar_server = "0";
        }else{
        if(newpar_group == -1){
            newpar_group = tmpcon.group_id;//取得组ID
            if(isNaN(parseInt(newpar_group) && par_group > 0)){//不是整数
                newpar_group = par_group;
            }
            if(newpar_server == -1){
                newpar_server = tmpcon.server_id;
                if(isNaN(parseInt(newpar_server) && tmpcon.par_server > 0)){//不是整数
                    newpar_server = tmpcon.par_server;
                }
            }else{
                newpar_server = "0";
            }
        }else{
            newpar_group = "0";
            newpar_server = "0";
        }
        }
        if(tmpcon.par_model == -1){
        this.consubmit.par_model = tmpcon.obj_id;
        }else{
        this.consubmit.par_model = "0";
        }

        this.consubmit.par_group = newpar_group;
        this.consubmit.par_server = newpar_server;
    },

    createCon() {
      this.$refs["condataForm"].validate((valid) => {
        if (valid) {
          this.con.id = parseInt(Math.random() * 100) + 1024; // mock a id
          this.con.author = "vue-element-admin";
          this.checkCon(this.con);
          this.consubmit.optionFlag = "createPar";
          addConParam(this.consubmit).then(() => {
            this.getCONList();
            this.conFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Created Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },

    updateCon() {
      this.$refs["condataForm"].validate((valid) => {
        if (valid) {
          const modify = Object.assign({}, this.con);
          modify.timestamp = +new Date(modify.timestamp);
          this.checkCon(modify);
          this.consubmit.optionFlag = "updatePar";
          this.consubmit.par_id = modify.par_id; 
          addConParam(this.consubmit).then(() => {
            this.getCONList();
            this.conFormVisible = false;
            this.$notify({
              title: "Success",
              message: "Update Successfully",
              type: "success",
              duration: 2000,
            });
          });
        }
      });
    },

    parAllChange(){
        if (this.con.par_all == 0) {// 不作用于全局
            this.isParGroup = true;
            this.isParServer = false;
            this.isParModel = true;
            this.isGroup = false;
            this.isServer = false;
            this.isModel = false;
            if(this.con.par_group == -1){//已选作用于服务器组
                this.isGroup = true;
            }
            if(this.con.par_server == -1){//已选作用于服务器
                this.isServer = true;
            }
            if(this.con.par_model == -1){//已选作用于模型
                this.isModel = true;
            }
        } else if (this.con.par_all == 1) {// 作用于全局
            this.isParGroup = false;
            this.isParServer = false;
            this.isParModel = true;
            this.isGroup = false;
            this.isServer = false;
            this.isModel = false;
            if(this.con.par_model == -1){//已选作用于模型
                this.isModel = true;
                this.con.obj_id = "";
                this.getConStoreObj("0");
            }
        }									
    },

    parGroupChange(){
        this.con.group_id = "";
        this.getConGroups();
        if (this.con.par_all == 0) {// 不作用于全局
            if (this.con.par_group == 0) {// 不作用于服务器组
                this.isParServer = false;
                this.isParModel = true;
                this.isGroup = false;
                this.isServer = false;
                this.isModel = false;
                if(this.con.par_model == -1){//已选作用于模型
                    this.isModel = true;
                }
            } else if (this.con.par_group == -1) {// 作用于服务器组
                this.isParServer = true;
                this.isParModel = true;
                this.isGroup = true;
                this.isServer = false;
                this.isModel = false;
                if(this.con.par_model == -1){//已选作用于模型
                    this.isModel = true;
                    this.con.obj_id = "";
                }
                if(this.con.par_server == -1){//已选作用于服务器
                    this.isServer = true;
                }
            }
        } else if (this.con.par_all == 1) {// 作用于全局
            this.isParGroup = false;
            this.isParServer = false;
            this.isParModel = true;
            this.isGroup = false;
            this.isServer = false;
            this.isModel = false;
        }
											
    },

    parServerChange(){
        this.con.server_id = "";
        if (this.con.par_group == -1) {// 作用于服务器组
            if(this.con.par_server == 0){//不作用于服务器
                this.isParModel = true;
                this.isGroup = true;
                this.isServer = false;
                this.isModel = false;
                if(this.con.par_model == -1){//已选作用于模型
                    this.isModel = true;
                }
            }else if(this.con.par_server == -1){//作用于服务器
                this.isParModel = true;
                this.isGroup = true;
                this.isServer = true;
                this.isModel = false;
                if(this.con.par_model == -1){//已选作用于模型
                    this.isModel = true;
                }
            }
        }
    },

    parModelChange(){
        this.con.obj_id = "";
        if (this.con.par_all == 1) {// 作用于全局
            if(this.con.par_model == -1){//作用于模型
                this.isGroup = false;
                this.isServer = false;
                this.isModel = true;
            }else if(this.con.par_model == 0){//不作用于模型
                this.isGroup = false;
                this.isServer = false;
                this.isModel = false;
            }
        } else if (this.con.par_all == 0) {//不作用于全局
            if(this.con.par_model == -1){//作用于模型
                this.isModel = true;
            }else if(this.con.par_model == 0){//不作用于模型
                this.isModel = false;
            }
        }
        let gid = this.con.group_id;
        let parAll = this.con.par_all;
        let parGroup = this.con.par_group;
        if(parAll == 1 || parGroup == 0){//作用于全局或不作用于服务器组
            gid = 0;
        }

        this.getConStoreObj(gid);	
										
    },
    parGroupIdChange(){
        let groupId = this.con.group_id;
        this.con.server_id = "";
        this.con.obj_id = "";
        let parAll = this.con.par_all;
        let parGroup = this.con.par_group;
        if(parAll == 1 || parGroup == 0){//作用于全局或不作用于服务器组
            groupId = 0;
        }

        if(groupId!=""){
            this.getConStoreServers(groupId);
            this.getConStoreObj(groupId);	
        }																					
    },

    formatJson(filterVal) {
      return this.list.map((v) =>
        filterVal.map((j) => {
          if (j === "timestamp") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort;
      return sort === `+${key}` ? "ascending" : "descending";
    }
  },
};
</script>

