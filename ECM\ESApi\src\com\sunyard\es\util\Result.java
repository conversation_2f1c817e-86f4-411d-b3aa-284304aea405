package com.sunyard.es.util;

public class Result<T> {
	
	 private String message;
	 private T data;
	 private boolean success;
	 
	public boolean isSuccess() {
		return success;
	}
	public void setSuccess(boolean success) {
		this.success = success;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public T getData() {
		return data;
	}
	public void setData(T data) {
		this.data = data;
    }
	@Override
	public String toString() {
		return "Result [message=" + message + ", data=" + data + "]";
	}
	
	
}
