import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function userInfoSearch(data) {
  const obj = {
  'user_id' : encodeURI(data.user_id),
  'user_name': encodeURI(data.user_name),
  'user_state': data.user_state,
  'role_id': data.role_id,
  'page': data.page,
  'limit': data.limit
}
  return request({
    url: '/safeManage/userInfoVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function addUserInfo(data) {
  let url = '/safeManage/configUserAction'+EndUrl.EndUrl;
    const obj = {
    'optionFlag': 'create1',
    'user_id' : encodeURI(data.user_id),
    'haixiaSSOUser': encodeURI(data.haixiaSSOUser),
    'user_password': data.user_password,
    'user_password2':data.user_password2,
    'user_city': encodeURI(data.user_city),
    'user_department': encodeURI(data.user_department),
    'user_name': encodeURI(data.user_name),
    'user_post': encodeURI(data.user_post),
    'ldap_code': data.ldap_code,
    'c_user_id': data.c_user_id
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function updateUserInfo(data) {
  let url = '/safeManage/configUserAction'+EndUrl.EndUrl;
    const obj =  {
      'optionFlag': 'update1',
      'user_id' : encodeURI(data.user_id),
      'haixiaSSOUser': encodeURI(data.haixiaSSOUser),
      'user_password': data.user_password,
      'user_password2':data.user_password2,
      'user_city': encodeURI(data.user_city),
      'user_department': encodeURI(data.user_department),
      'user_name': encodeURI(data.user_name),
      'user_post': encodeURI(data.user_post),
      'ldap_code': data.ldap_code,
      'c_user_id': data.c_user_id
    }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function stopUserState(data) {
  return request({
    url: '/safeManage/modifyUserStateAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_ids': data.login_id,
      'user_state' : "0"
    }
  })
}

export function startUserState(data) {
  return request({
    url: '/safeManage/modifyUserStateAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_ids': data.login_id,
      'user_state' : "1"
    }
  })
}

export function resetUserPwd(data) {
  return request({
    url: '/safeManage/resetUserPwdVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_id': data.user_id,
      'password' : data.user_password,
      'password2' : data.user_password2
    }
  })
}

export function modifyUserPwd(data) {
  return request({
    url: '/safeManage/modifyUserPwdAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_id': data.user_id,
      'password' : data.password
    }
  })
}

export function getUserCmodel(data) {
  return request({
    url: '/safeManage/getUserConferVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: data }
  })
}

export function configUserConfer(data) {
  return request({
    url: '/safeManage/configUserConferVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_id': data.user_id,
      'model_codes': data.model_code, 
      'permission_code': data.permission_code 
    }
  })
}

export function getExistPersTreeByUserId(data) {
  return request({
    url: '/safeManage/getUserExistComponentsVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_ids': data
    }
  })
}

export function getNotExistPersTreeByUserId(data) {
  return request({
    url: '/safeManage/getUserNotExistComponentsVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_ids': data
    }
  })
}

export function updateUserComponents(data) {
  return request({
    url: '/safeManage/updateUserComponentsVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_ids': data.objMsg.login_id,
      'componentIDs': data.componentIDs
    }
  })
}

export function getExistRolesTreeByUserId(data) {
  return request({
    url: '/safeManage/getUserExistRoles'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_ids': data
    }
  })
}

export function getNotExistRolesTreeByUserId(data) {
  return request({
    url: '/safeManage/getUserNotExistRoles'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'user_ids': data
    }  
  })
}

export function updateUserRoles(data) {
  const obj = {
    'user_ids': data.objMsg.login_id,
    'role_ids': data.role_ids
  }
  return request({
    url: '/safeManage/updateUserRoles'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }

  })
}
