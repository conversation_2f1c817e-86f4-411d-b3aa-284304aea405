package com.sunyard.console.offlineFailManage.action;

import java.util.ArrayList;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.configmanager.bean.OfflineCountBean;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.console.offlineFailManage.util.OfflineFailUtil;
import org.springframework.stereotype.Controller;

/**
 * 离线失败管理
 * 
 * <AUTHOR>
 *
 */
@Controller
public class OfflineFailManageAction extends BaseAction {
	private  final static Logger log = LoggerFactory.getLogger(OfflineFailManageAction.class);
	private String groupId;
	private String modelCode;
	private String beginDate;
	private String endDate;
	private int start;
	private int limit;
	private String startDates;
	private String contentIds;
	private String queryAbandon;
	private String type;
	/**
	 * 迁移标志位，8表示查询放弃离线批次，4表示查询离线失败批次
	 */
	private String migrationStatus;


	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getGroupId() {
		return groupId;
	}

	public String getContentIds() {
		return contentIds;
	}

	public void setContentIds(String contentIds) {
		this.contentIds = contentIds;
	}

	public String getQueryAbandon() {
		return queryAbandon;
	}

	public void setQueryAbandon(String queryAbandon) {
		this.queryAbandon = queryAbandon;
	}

	public String getStartDates() {
		return startDates;
	}

	public void setStartDates(String startDates) {
		this.startDates = startDates;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getModelCode() {
		return modelCode;
	}

	public void setModelCode(String modelCode) {
		this.modelCode = modelCode;
	}

	public String getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getMigrationStatus() {
		return migrationStatus;
	}

	public void setMigrationStatus(String migrationStatus) {
		this.migrationStatus = migrationStatus;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	/**
	 * 属性集查询action 将结果集组合成jsonStr，返回给页面
	 * 
	 * @return null
	 */
//	@ResponseBody
//	@RequestMapping(value = "/offlineFailManage/offlineFailManageAction.action", method = RequestMethod.POST)
	public String queryOfflineFailCount(String modelCode, String beginDate, String endDate, String groupId, String queryAbandon) {
		log.info("modelCode" + modelCode + ",beginDate" + beginDate + ",endDate" + endDate + ",groupId" + groupId + "，abandon" + queryAbandon + "]");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		if (StringUtil.stringIsNull(modelCode) || StringUtil.stringIsNull(groupId) || StringUtil.stringIsNull(beginDate) || StringUtil.stringIsNull(endDate)) {
			jsonResp.put("success", "false");
			jsonResp.put("message", "参数为空");
			jsonStr = jsonResp.toString();
			log.error("参数为空");
			return null;
		}
		// 日期格式2014-03-17T00:00:00
		String[] msgs = beginDate.split("-");
		beginDate = msgs[0] + msgs[1] + msgs[2].substring(0, 2);
		msgs = endDate.split("-");
		endDate = msgs[0] + msgs[1] + msgs[2].substring(0, 2);

		try {
			OfflineFailUtil util = new OfflineFailUtil();
			List<OfflineCountBean> list = util.getOfflineFailCount(groupId, modelCode, beginDate, endDate, Boolean.parseBoolean(queryAbandon));
			int size = 0;
			if (list != null && list.size() > 0) {
				size = list.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(list, size, OfflineCountBean.class);
		} catch (Exception e) {
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取离线失败数!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("获取离线失败数", e);
		}
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 属性集查询action 将结果集组合成jsonStr，返回给页面
	 * 
	 * @return null
	 */
//	@ResponseBody
//	@RequestMapping(value = "/offlineFailManage/getFailOfflineListAction.action", method = RequestMethod.POST)
	public String getFailOfflineList(String modelCode, String beginDate, String groupId, String type, int start, int limit) {
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			OfflineFailUtil util = new OfflineFailUtil();
			log.info("modelCode" + modelCode + ",beginDate" + beginDate + ",groupId" + groupId + "，type" + type + "]");
			
			List<OfflineCountBean> list = util.getFailOfflineList(groupId, modelCode, beginDate, null, type, start + 1, limit);
			int size = 0;
			if (list != null && list.size() > 0) {
				String sizeStr = list.get(0).getOfflineTotalCount();
				try {
					size = Integer.parseInt(sizeStr);
				} catch (Exception e) {
					log.error("sizeStr[" + sizeStr + "]");
					size = list.size();
				}
			}
			jsonStr = new JSONUtil().createJsonDataByColl(list, size, OfflineCountBean.class);
		} catch (Exception e) {
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取属性信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("属性集管理->获取属性列表失败" + e.toString());
			log.error("Exception:",e);
		}
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 放弃离线s
	 * 
	 * @return null
	 */
//	@ResponseBody
//	@RequestMapping(value = "/offlineFailManage/abandonOfflineByDateAction.action", method = RequestMethod.POST)
	public String abandonOfflineByDate(String modelCode, String beginDate, String groupId) {
		log.info("modelCode" + modelCode + ",beginDate" + beginDate + ",groupId" + groupId + "]");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try {
			OfflineFailUtil util = new OfflineFailUtil();
			util.abandonOfflineByDate(groupId, modelCode, startDates);
			jsonResp.put("success", true);
			jsonResp.put("message", "放弃离线成功!!");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", "false");
			jsonResp.put("message", "失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("abandonOfflineByDate", e);
		}
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 放弃离线s
	 * 
	 * @return null
	 */
//	@ResponseBody
//	@RequestMapping(value = "/offlineFailManage/abandonOfflineByContentIdAction.action", method = RequestMethod.POST)
	public String abandonOfflineByContentId(String modelCode, String beginDate, String groupId, String contentIds) {
		log.info("modelCode" + modelCode + ",beginDate" + beginDate + ",groupId" + groupId + "]");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try {
			if (contentIds != null) {
				List<OfflineCountBean> list = new ArrayList<OfflineCountBean>();
				// 解析contentid
				String[] contentId = contentIds.split(",");
				if (contentId != null && contentId.length > 0) {
					int len = contentId.length;
					for (int i = 0; i < len; i++) {
						OfflineCountBean bean = new OfflineCountBean();
						bean.setContentId(contentId[i]);
						bean.setStartDate(beginDate);
						list.add(bean);
					}
					OfflineFailUtil util = new OfflineFailUtil();
					// 按照批次放弃离线
					util.abandonOfflineByContentId(groupId, modelCode, list);
				} else {
					log.warn("contentids[" + contentIds + "]");
				}
			} else {
				log.warn("contentids is null");
			}
			jsonResp.put("success", true);
			jsonResp.put("message", "放弃离线批次成功");
		} catch (Exception e) {
			jsonResp.put("success", "false");
			jsonResp.put("message", "失败!!");
			// 记录日志
			log.error("abandonOfflineByContentId", e);
		}
		jsonStr = jsonResp.toString();
		this.outJsonString(jsonStr);
		return null;
	}
}
