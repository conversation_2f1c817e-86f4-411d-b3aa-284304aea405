package com.sunyard.console.monitor.nearLineFailManage.action;

import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.console.monitor.nearLineFailManage.bean.NearLineFailInfoBean;
import com.sunyard.console.monitor.nearLineFailManage.dao.NearLineFailManageDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>
 * Title: 迁移失败管理action
 * </p>
 * <p>
 * Description: 用于迁移失败的获取,以及将迁移失败的批次重新迁移action访问类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class NearLineFailManageAction extends BaseAction {
	// 批次号
	private String content_id;
	// 模型名
	private String model_code;
	// 失败时间
	private String fail_time;
	private String group_id;
	private String version;
	private String table_name;
	private String index_table_name;
	private String file_table_name;
	private String business_start_date;
	private String index_model_code;
	private String file_model_code;
	private String error_case;

	public String getIndex_table_name() {
		return index_table_name;
	}

	public void setIndex_table_name(String index_table_name) {
		this.index_table_name = index_table_name;
	}

	public String getFile_table_name() {
		return file_table_name;
	}

	public void setFile_table_name(String file_table_name) {
		this.file_table_name = file_table_name;
	}

	public String getBusiness_start_date() {
		return business_start_date;
	}

	public void setBusiness_start_date(String business_start_date) {
		this.business_start_date = business_start_date;
	}

	public String getIndex_model_code() {
		return index_model_code;
	}

	public void setIndex_model_code(String index_model_code) {
		this.index_model_code = index_model_code;
	}

	public String getFile_model_code() {
		return file_model_code;
	}

	public void setFile_model_code(String file_model_code) {
		this.file_model_code = file_model_code;
	}

	public String getError_case() {
		return error_case;
	}

	public void setError_case(String error_case) {
		this.error_case = error_case;
	}

	/**
	 * 记录要校验的信息，一个批次格式 content_id>>group_id>>version>>table_name 各个批次用;隔开
	 */
	private String reNearLineMsg;

	public String getGroup_id() {
		return group_id;
	}

	public void setGroup_id(String groupId) {
		group_id = groupId;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getTable_name() {
		return table_name;
	}

	public void setTable_name(String tableName) {
		table_name = tableName;
	}


	public String getReNearLineMsg() {
		return reNearLineMsg;
	}

	public void setReNearLineMsg(String reNearLineMsg) {
		this.reNearLineMsg = reNearLineMsg;
	}

	/**
	 * 分页记录开始位置
	 */
	private int start;
	/**
	 * 分页记录条数
	 */
	private int limit;
	private String value;
	/**
	 * 日志对象
	 */
	private  final static Logger log = LoggerFactory.getLogger(NearLineFailManageAction.class);

	/**
	 * 迁移失败的数据库操作对象
	 */
	@Autowired
	NearLineFailManageDao nearLineFailManageDao;

	public String getContent_id() {
		return content_id;
	}

	public void setContent_id(String contentId) {
		content_id = contentId;
	}

	public String getModel_code() {
		return model_code;
	}

	public void setModel_code(String modelCode) {
		model_code = modelCode;
	}

	public String getFail_time() {
		return fail_time;
	}

	public void setFail_time(String failTime) {
		fail_time = failTime;
	}

	public NearLineFailManageDao getNearLineFailManageDao() {
		return nearLineFailManageDao;
	}

	public void setNearLineFailManageDao(NearLineFailManageDao nearLineFailManageDao) {
		this.nearLineFailManageDao = nearLineFailManageDao;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}
	/**
	 * 迁移失败查询action 将结果集组合成jsonStr，返回给页面
	 * 
	 * @return null
	 */
	@ResponseBody
	@RequestMapping(value = "/nearLineFailManage/getNearLineFailList.action", method = RequestMethod.POST)
	public String getNearLineFailList(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String content_id = (String) modelJson.getOrDefault("content_id", "");
		String model_code = (String) modelJson.getOrDefault("model_code", "");
		String fail_time = (String) modelJson.getOrDefault("fail_time", "");
		int start = modelJson.getInt("start");
		int limit = modelJson.getInt("limit");
		start = (start-1) * limit;
		String jsonStr = null;
		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			String date = null;
			if (fail_time != null && !fail_time.equals("") && !fail_time.equals("null") && !fail_time.equals("NULL")) {
				// 日期格式2014-03-17T00:00:00
				String[] msgs = fail_time.split("-");
				date = msgs[0] + msgs[1] + msgs[2].substring(0, 2);
			}
			List<NearLineFailInfoBean> beanInfoList = nearLineFailManageDao.getNearLineFailList(model_code, content_id, date, start + 1, limit);
			List<NearLineFailInfoBean> AllBeanInfoList = nearLineFailManageDao.getNearLineFailList(model_code, content_id, date);
			int size = 0;
			if (AllBeanInfoList != null && AllBeanInfoList.size() > 0) {
				size = AllBeanInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(beanInfoList, size, new NearLineFailInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取迁移失败信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("获取迁移失败信息失败!!" + e.toString());
			log.error("Exception:",e);
		}
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 重新迁移设置
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/nearLineFailManage/reNearLineFaileBatch.action", method = RequestMethod.POST)
	public String reNearLineFaileBatch(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String reNearLineMsg = (String) modelJson.getOrDefault("reNearLineMsg", "");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try {
			boolean result = nearLineFailManageDao.reNearLineFaileBatch(reNearLineMsg);

			if (result) {// 成功
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);//TODO mock
				jsonResp.put("message", "重新近线成功!!");
				jsonStr = jsonResp.toString();
			} else {// 失败
				jsonResp.put("success", false);
				jsonResp.put("message", "重新近线失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "重新近线失败!!");
			jsonStr = jsonResp.toString();
			log.error(e.toString());
		}
		this.outJsonString(jsonStr);
		return null;
	}
}
