package com.xxl.job.admin.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * properties util
 * <AUTHOR> 2015-8-28 10:35:53
 */
@Component
public class PropertiesUtil implements ApplicationContextAware {
    private static Logger logger = LoggerFactory.getLogger(PropertiesUtil.class);
    private static ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        PropertiesUtil.applicationContext = applicationContext;
    }

    public static String getString(String key) {
        try {
            return applicationContext.getEnvironment().getProperty(key);
        } catch (Exception e) {
            logger.error("获取配置项失败: " + key, e);
            return null;
        }
    }
    public static String getString(String key, String defaultValue) {
        String value = getString(key);
        return value != null ? value : defaultValue;
    }
}
