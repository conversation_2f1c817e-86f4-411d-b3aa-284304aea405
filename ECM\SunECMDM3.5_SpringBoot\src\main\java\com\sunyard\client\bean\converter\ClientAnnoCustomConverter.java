package com.sunyard.client.bean.converter;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

import com.sunyard.ecm.server.bean.AnnotationBean;
import com.sunyard.ecm.server.bean.AttributeBean;
import org.apache.commons.collections.map.LinkedMap;

import com.sunyard.ws.utils.XMLUtil;
import com.thoughtworks.xstream.converters.Converter;
import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.io.HierarchicalStreamReader;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;

/**
 * 客户端批注转换
 * <AUTHOR>
 *
 */
public class ClientAnnoCustomConverter implements Converter{

	/**
	 * Marshal方法是用来将对象转换为XML的，他有三个参数： 
     *       1，我们准备转换的对象 value
     *       2，我们准备输出对象的writer 
     *       3，当前的marshaling context
	 */
	public void marshal(Object value, HierarchicalStreamWriter writer,
			MarshallingContext context) {
		map2xml(value, writer, context);
	}

	protected void map2xml(Object value, HierarchicalStreamWriter writer,
            MarshallingContext context) {
		Map map = (Map) value;
        for (Iterator<Entry<String, Object>> iterator = map.entrySet()
                .iterator(); iterator.hasNext();) {
            Entry<String, Object> entry = (Entry<String, Object>) iterator
                    .next();
            String key = (String) entry.getKey();
            Object subvalue = entry.getValue();
            writer.startNode(key);
            if (canConvert(subvalue.getClass())) {
                map2xml(subvalue, writer, context);
            } else {
            	writer.setValue(XMLUtil.bean2XML(subvalue));
            }
            writer.endNode();
        }
	}
	
	/**
	 * Unmarshal方法是用来将XML转换为对象，他有两个参数： 
     *       1，我们准备读取对象的reader 
     *       2，当前的marshaling context
	 */
	public Object unmarshal(HierarchicalStreamReader reader,
			UnmarshallingContext context) {
		Map<String, AnnotationBean> map = (Map<String, AnnotationBean>) populateMap(reader,
                context);
		return map;
	}
	
	protected Object populateMap(HierarchicalStreamReader reader,
            UnmarshallingContext context){
		Map<String, AnnotationBean> map = new HashMap<String, AnnotationBean>();
		String answer = "";
		AnnotationBean value = null;
		while (reader.hasMoreChildren()){
			reader.moveDown();
			answer = reader.getNodeName();
			value = convertAttributeBean(reader, context);
			reader.moveUp();
			map.put(answer, value);
		}
		return map;
	}
	
	protected AnnotationBean convertAttributeBean(HierarchicalStreamReader reader, UnmarshallingContext context){
		AnnotationBean annotationBean = new AnnotationBean();
		reader.moveDown();
		annotationBean = (AnnotationBean) context.convertAnother(annotationBean, AttributeBean.class);
		reader.moveUp();
		return annotationBean;
	}

	/**
	 * 转换类匹配
	 */
	public boolean canConvert(Class clazz) {
		return clazz.equals(Map.class)||clazz.equals(HashMap.class)||clazz.equals(LinkedMap.class);
	}
}