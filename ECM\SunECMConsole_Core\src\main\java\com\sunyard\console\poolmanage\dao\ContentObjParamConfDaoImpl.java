package com.sunyard.console.poolmanage.dao;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.poolmanage.bean.ModelRelSDBConfigBean;
import com.sunyard.console.process.exception.DBRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Title: 内容对象数据操作接口实现类
 * </p>
 * <p>
 * Description: 内容对象数据操作接口实现类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR> @version 1.0
 */
@Repository
public class ContentObjParamConfDaoImpl implements ContentObjParamConfDao {
	/**
	 * 数据库分页操作对象
	 */
	@Autowired
	private PageTool pageTool;
	/**
	 * 日志对象
	 */
	@Resource(name = "group_cmodel_rel_id_incrementer")
	private DataFieldMaxValueIncrementer group_cmodel_rel_id;
	private  final static Logger log = LoggerFactory.getLogger(ContentObjParamConfDaoImpl.class);

	public PageTool getPageTool() {
		return pageTool;
	}

	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}
	
	
	
	public DataFieldMaxValueIncrementer getGroup_cmodel_rel_id() {
		return group_cmodel_rel_id;
	}

	public void setGroup_cmodel_rel_id(
			DataFieldMaxValueIncrementer group_cmodel_rel_id) {
		this.group_cmodel_rel_id = group_cmodel_rel_id;
	}

	/**
	 * 查询业务参数配置列表（所有）
	 * @param contentName
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<ModelRelSDBConfigBean> contentObjConfList(String contentName){
		log.info( "--contentObjConfList(start)");
		List<ModelRelSDBConfigBean> modelRelSDBConfigBeanList = null;
		
		StringBuffer sql = new StringBuffer();

		sql.append("SELECT  S.REL_ID , S.GROUP_ID ,C.POOLNAME,S.MODEL_CODE,S.SDB_CONNECTION_ID,S.SDB_COLLECTIONSPACE,S.SDB_INDEX_COLLECTION,S.SDB_DOC_COLLECTION,S.SDB_FILE_COLLECTION,S.SPLIT_CYCLE,S.SPLIT_CYCLE_AUTO,S.SDB_FILE_CONNECTION_ID,S.SDB_FILE_COLLECTIONSPACE FROM  SDB_METADATA_RELATE S,CONTENT_POOL_INFO C WHERE S.SDB_CONNECTION_ID = C.SDB_CONNECTION_ID");
		if(!"".equals(contentName) && null!=contentName){
			sql.append(" AND S.MODEL_CODE='");
			sql.append(contentName+"'");
		}
		sql.append(" ORDER BY S.REL_ID DESC");
		try {
			log.debug( "--contentObjConfList-->sql:" + sql.toString());
			modelRelSDBConfigBeanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),ModelRelSDBConfigBean.class);
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "获取业务参数配置失败->" + e.toString(), e);
			throw new DBRuntimeException(
					"ContentObjParamConfDaojavaImpl===>contentObjConfList:"
							+ e.getMessage());
		}
		log.info( "--contentObjConfList(over)-->modelRelSDBConfigBeanList:"+modelRelSDBConfigBeanList);
		return modelRelSDBConfigBeanList;
	}
	
	
	/**
	 * 查询业务参数配置列表
	 * @param contentName
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<ModelRelSDBConfigBean> contentObjConfList(String contentName,int start, int limit){
		log.info( "--contentObjConfList(start)");
		List<ModelRelSDBConfigBean> modelRelSDBConfigBeanList = null;
		
		StringBuffer sql = new StringBuffer();

		sql.append("SELECT  S.REL_ID , S.GROUP_ID ,C.POOLNAME,S.MODEL_CODE,S.SDB_CONNECTION_ID,S.SDB_COLLECTIONSPACE,S.SDB_INDEX_COLLECTION,S.SDB_DOC_COLLECTION,S.SDB_FILE_COLLECTION,S.SPLIT_CYCLE,S.SPLIT_CYCLE_AUTO,S.SDB_FILE_CONNECTION_ID,S.SDB_FILE_COLLECTIONSPACE FROM  SDB_METADATA_RELATE S,CONTENT_POOL_INFO C WHERE S.SDB_CONNECTION_ID = C.SDB_CONNECTION_ID");
		if(!"".equals(contentName) && null!=contentName){
			sql.append(" AND S.MODEL_CODE='");
			sql.append(contentName+"'");
		}
		sql.append(" ORDER BY S.REL_ID DESC");
		try {
			log.debug( "--contentObjConfList-->sql:" + sql.toString());
			modelRelSDBConfigBeanList = DataBaseUtil.SUNECM.queryBeanList(pageTool
					.getPageSql(sql.toString(), start, limit),ModelRelSDBConfigBean.class);
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "获取业务参数配置失败->" + e.toString(), e);
			throw new DBRuntimeException(
					"ContentObjParamConfDaojavaImpl===>contentObjConfList:"
							+ e.getMessage());
		}
		log.info( "--contentObjConfList(over)-->modelRelSDBConfigBeanList:"+modelRelSDBConfigBeanList);
		return modelRelSDBConfigBeanList;
	}
	
	
	/**
	 * 业务参数配置(新增)
	 * @param modelRelSDBConfigBean
	 * @return
	 */
	public boolean contentObjConfAdd(ModelRelSDBConfigBean modelRelSDBConfigBean){
		log.info( "--contentObjConfAdd(start)--");
		boolean result = false;
		if (modelRelSDBConfigBean == null) {
			return result;
		}
		int id = group_cmodel_rel_id.nextIntValue();
		StringBuffer sql = new StringBuffer();
		sql
				.append("INSERT INTO  SDB_METADATA_RELATE( REL_ID,GROUP_ID,MODEL_CODE, SDB_CONNECTION_ID,SDB_COLLECTIONSPACE, SDB_INDEX_COLLECTION, SDB_DOC_COLLECTION,SDB_FILE_COLLECTION, SPLIT_CYCLE,SPLIT_CYCLE_AUTO,SDB_FILE_CONNECTION_ID,SDB_FILE_COLLECTIONSPACE) VALUES(");
		sql.append(id).append(",").append(modelRelSDBConfigBean.getGroup_id()).append(",'").append(modelRelSDBConfigBean.getModel_code())
				.append("',").append(modelRelSDBConfigBean.getSdb_connection_id()).append(",'").append(
						modelRelSDBConfigBean.getSdb_collectionspace()).append("','");
		sql.append(modelRelSDBConfigBean.getSdb_index_collection()).append("','").append(modelRelSDBConfigBean.getSdb_doc_collection())
				.append("','").append(modelRelSDBConfigBean.getSdb_file_collection()).append("','").append(
						modelRelSDBConfigBean.getSplit_cycle()).append("',").append(modelRelSDBConfigBean.getSplit_cycle_auto()).append(",").append(modelRelSDBConfigBean.getSDB_FILE_CONNECTION_ID()).append(",'").append(modelRelSDBConfigBean.getSDB_FILE_COLLECTIONSPACE()).append("')");

		try {
			log.debug( "--contentObjConfAdd-->sql:" + sql.toString());
			result = DataBaseUtil.SUNECM.update(sql.toString())>0;
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "内容对象参数配置->业务参数配置(新增)失败:" + e.toString(), e);

			throw new DBRuntimeException(
					"ContentObjParamConfDaoImpl===>contentObjConfAdd:"
							+ e.getMessage());
		}
		log.info( "--contentObjConfAdd(over)");
	
		return result;
	}
	
	/**
	 * 业务参数配置(修改)
	 * @param modelRelSDBConfigBean
	 * @return
	 */
	public boolean contentObjConfUpdate(ModelRelSDBConfigBean modelRelSDBConfigBean){
		log.info( "--contentObjConfUpdate(start)-->bean:" + modelRelSDBConfigBean.toString());
		boolean result = false;
		StringBuffer sql = new StringBuffer();
		sql
			.append("UPDATE SDB_METADATA_RELATE SET ").append("GROUP_ID= ").append(modelRelSDBConfigBean.getGroup_id())
			.append(", MODEL_CODE='")
			.append(modelRelSDBConfigBean.getModel_code())
			.append("',SDB_CONNECTION_ID=")
			.append(modelRelSDBConfigBean.getSdb_connection_id())
			.append(", SDB_COLLECTIONSPACE='")
			.append(modelRelSDBConfigBean.getSdb_collectionspace())
			.append("',SDB_INDEX_COLLECTION='")
			.append(modelRelSDBConfigBean.getSdb_index_collection())
			.append("',SDB_DOC_COLLECTION='")
			.append(modelRelSDBConfigBean.getSdb_doc_collection())
			.append("',SDB_FILE_COLLECTION='")
			.append(modelRelSDBConfigBean.getSdb_file_collection())
			.append("',SPLIT_CYCLE_AUTO=")
			.append(modelRelSDBConfigBean.getSplit_cycle_auto())
			.append(",SDB_FILE_CONNECTION_ID=")
			.append(modelRelSDBConfigBean.getSDB_FILE_CONNECTION_ID())
			.append(",SDB_FILE_COLLECTIONSPACE='")
			.append(modelRelSDBConfigBean.getSDB_FILE_COLLECTIONSPACE());
			if(modelRelSDBConfigBean.getSplit_cycle_auto()==0){
				sql.append("',SPLIT_CYCLE='");
			}else{
				sql.append("',SPLIT_CYCLE='")
				.append(modelRelSDBConfigBean.getSplit_cycle());
			}
				sql.append("' WHERE REL_ID = ")
				.append(modelRelSDBConfigBean.getRel_id());
		try {
			log.debug( "--contentObjConfUpdate-->sql:" + sql.toString());
			result = DataBaseUtil.SUNECM.update(sql.toString())>0;
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "内容对象参数配置->业务参数配置(修改)失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"ContentObjParamConfDaoImpl===>contentObjConfUpdate:"
							+ e.getMessage());
		}
		log.info( "--contentObjConfUpdate(over)");
		return result;
	}
	
}
