package com.sunyard.console.poolmanage.bean;


/**
 * <p>
 * Title: 属性信息bean
 * </p>
 * <p>
 * Description: 属性信息
 * </p>
 * <p>
 * Copyright: Copyright (c) 2017
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR> @version 1.0
 */
public class ModelRelSDBConfigBean {
	private int rel_id ;//主键
	private int group_id ;//sdb连接
	private String poolname;//连接池名称
	private String model_code;//元数据英文
	private int sdb_connection_id;//SDB连接配置表ID(不为空)
	private String sdb_collectionspace;//集合空间(不为空)
	private String sdb_index_collection;//索引对象集合(不为空)
	private String sdb_doc_collection;//文档对象集合(不为空)
	private String sdb_file_collection;//文件集合(不为空)
	private String split_cycle;//分表周期，针对文件集合
	private int split_cycle_auto;//是否自动按分表周期规则(不为空)
	private int SDB_FILE_CONNECTION_ID;//文件集合对应连接ID，一期不用
	private String SDB_FILE_COLLECTIONSPACE;//文件集合对应集合空间，一期不用
	private PoolInfoBean poolInfoBean;//连接池配置信息
	public int getRel_id() {
		return rel_id;
	}
	public void setRel_id(int rel_id) {
		this.rel_id = rel_id;
	}
	public int getGroup_id() {
		return group_id;
	}
	public void setGroup_id(int group_id) {
		this.group_id = group_id;
	}
	public String getModel_code() {
		return model_code;
	}
	public void setModel_code(String model_code) {
		this.model_code = model_code;
	}
	public int getSdb_connection_id() {
		return sdb_connection_id;
	}
	public void setSdb_connection_id(int sdb_connection_id) {
		this.sdb_connection_id = sdb_connection_id;
	}
	public String getSdb_collectionspace() {
		return sdb_collectionspace;
	}
	public void setSdb_collectionspace(String sdb_collectionspace) {
		this.sdb_collectionspace = sdb_collectionspace;
	}
	public String getSdb_index_collection() {
		return sdb_index_collection;
	}
	public void setSdb_index_collection(String sdb_index_collection) {
		this.sdb_index_collection = sdb_index_collection;
	}
	public String getSdb_doc_collection() {
		return sdb_doc_collection;
	}
	public void setSdb_doc_collection(String sdb_doc_collection) {
		this.sdb_doc_collection = sdb_doc_collection;
	}
	public String getSdb_file_collection() {
		return sdb_file_collection;
	}
	public void setSdb_file_collection(String sdb_file_collection) {
		this.sdb_file_collection = sdb_file_collection;
	}
	public String getSplit_cycle() {
		return split_cycle;
	}
	public void setSplit_cycle(String split_cycle) {
		this.split_cycle = split_cycle;
	}
	public int getSplit_cycle_auto() {
		return split_cycle_auto;
	}
	public void setSplit_cycle_auto(int split_cycle_auto) {
		this.split_cycle_auto = split_cycle_auto;
	}
	public int getSDB_FILE_CONNECTION_ID() {
		return SDB_FILE_CONNECTION_ID;
	}
	public void setSDB_FILE_CONNECTION_ID(int sDB_FILE_CONNECTION_ID) {
		SDB_FILE_CONNECTION_ID = sDB_FILE_CONNECTION_ID;
	}
	public String getSDB_FILE_COLLECTIONSPACE() {
		return SDB_FILE_COLLECTIONSPACE;
	}
	public void setSDB_FILE_COLLECTIONSPACE(String sDB_FILE_COLLECTIONSPACE) {
		SDB_FILE_COLLECTIONSPACE = sDB_FILE_COLLECTIONSPACE;
	}
	public String getPoolname() {
		return poolname;
	}
	public void setPoolname(String poolname) {
		this.poolname = poolname;
	}
	public PoolInfoBean getPoolInfoBean() {
		return poolInfoBean;
	}
	public void setPoolInfoBean(PoolInfoBean poolInfoBean) {
		this.poolInfoBean = poolInfoBean;
	}
	
	}
	
	
	
	
	
	

