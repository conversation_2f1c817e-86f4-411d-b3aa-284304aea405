package com.sunyard.util;
/**
 * <p>Title: 静态常量</p>
 * <p>Description: 自定义的静态常量值</p>
 * <p>Copyright: Copyright (c) 2010</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class OptionKey {
	/**
	 * 获取用户令牌
	 */
	public static String CREATEUSERTOKEN = "CREATETOKEN";
	/**
	 * 查询DM
	 */
	public static String INQUIREDM = "INQUIREDM";
	/**
	 * 批次上传
	 */
	public static String UPLOAD = "UPLOAD";
	
	/**
	 * 批次删除
	 */
	public static String DEL = "DEL";
	
	/**
	 * 获取服务器上所有的模型列表
	 */
	public static String ALLMODELMSG = "ALLMODELMSG";
	
	/**
	 * 逻辑删除
	 */
	public static String LOGICAL_DEL = "LOGICAL_DEL";
	
	/**
	 * 物理删除
	 */
	public static String PHYSICAL_DEL = "PHYSICAL_DEL";
	
	/**
	 * 批次查询
	 */
	public static String QUERY = "QUERY";
	
	/**
	 * 批次更新
	 */
	public static String UPDATE = "UPDATE";
	
	/**
	 * 更新时追加
	 */
	public static String U_ADD = "U_ADD";
	
	/**
	 * 更新时删除
	 */
	public static String U_DEL = "U_DEL";
	
	/**
	 * 更新时替换
	 */
	public static String U_REPLACE = "U_REPLACE";
	
	/**
	 * 更新时修改(无文件替换，只是修改基本信息)
	 */
	public static String U_MODIFY = "U_MODIFY";
	
	/**
	 * 元数据对象模板
	 */
	public static String METATEMPLATE = "METATEMPLATE";
	
	/**
	 * 文件业务类型
	 */
	public static String BUSITYPETEMPLATE = "BUSITYPETEMPLATE";
	
	/**
	 * 归档类型
	 */
	public static String ARCTYPETEMPLATE = "ARCTYPETEMPLATE";
	
	/**
	 * 资源目录对象
	 */
	public static String RESOURCEDIR = "RESOURCEDIR";
	
	/**
	 * 元数据表
	 */
	public static String METADATATABLE = "METADATATABLE";
	
	/**
	 * 批注
	 */
	public static String ANNOTATION = "ANNOTATION";
	
	/**
	 * 登录
	 */
	public static String LOGIN = "LOGIN";
	
	/**
	 * 登出
	 */
	public static String LOGOUT = "LOGOUT";
	
	/**
	 * 权限
	 */
	public static String PERMISSION = "PERMISSION";
	
	/**
	 * 检入
	 */
	public static String CHECKIN = "CHECKIN";
	
	/**
	 * 检出
	 */
	public static String CHECKOUT = "CHECKOUT";

	/**
	 * 申请批次号
	 */
	public static String CREATEID = "CREATEID";
	
	/**
	 * 资源目录下的批次
	 */
	public static String RESOURCEBATCH = "RESOURCEBATCH";
	/**
	 * 查询文件信息
	 */
	public static String QUERY_FILE = "QUERY_FILE";
	/**
	 * 高级查询
	 */
	public static String HEIGHT_QUERY = "HEIGHT_QUERY";
	/**
	 * 查询批次信息
	 */
	public static String QUERY_BATCH_INFO = "QUERY_BATCH_INFO";
	/**
	 * 各种批次查询
	 */
	public static String OTHER_BATCH_QUERY = "OTHER_BATCH_QUERY";
	/**
	 * 各种文件查询
	 */
	public static String OTHER_FILE_QUERY = "OTHER_FILE_QUERY";
	/**
	 * 根据文件类型查询
	 */
	public static String QUERY_BUSI_FILE = "QUERY_BUSI_FILE";
	/**
	 * 根据批次查询文件
	 */
	public static String QUERY_BATCH_FILE = "QUERY_BATCH_FILE";
	/**
	 * 文件类型的增、删、改
	 */
	public static String FILE_TYPE_HANDLE = "FILE_TYPE_HANDLE";
	/**
	 * 添加或更新批注
	 */
	public static String A_OR_U_ANNOTATION = "A_OR_U_ANNOTATION";
	/**
	 * 获取缓存节点
	 */
	public static String GET_NODE = "GET_NODE";
	/**
	 * 内容归档
	 */
	public static String FILING = "FILING";
	
	/**
	 * 获取批次ID
	 */
	public static String GET_BATCH_ID = "GET_BATCH_ID";
	
	/**
	 * 断点续传
	 */
	public static String BREAK_POINT = "BREAK_POINT";
	
	/**
	 * 根据文件信息查询文件
	 */
	public static String QUERY_FILE_BY_FILE_INFO = "QUERY_FILE_BY_FILE_INFO";
	
	/**
	 * 迁移标识
	 */
	public static String MIGRATE  = "MIGRATE";
	
	/**
	 * 立即迁移
	 */
	public static String IMMEDIATEMIGRATE = "IMMEDIATEMIGRATE";
	
	/**
	 * 根据项类型和机构号获取服务器组名
	 */
	public static String QUERY_NODEINFO_BY_GROUPID_AND_INSNO = "QUERY_NODEINFO_BY_GROUPID_AND_INSNO";
	
	public static String GET_MAXVERSION_GROUPID="getMaxVersionAndGroupId";
	
	public static String VERIFY_CONTENTID_IS_EXIST="verifyContentIDIsExist";
	
	public static String IS_OFFLINE="isOffline";
	
	public static String GET_HIGHTQUERY_COUNT="getHightQueryCount";
	
	public static String COPY_BATCH="COPY_BATCH";
	
	public static String IMMEDCOPY_BATCH="IMMEDCOPY_BATCH";
	public static String QUERY_ES_BY_ECM_DOC_ID="QUERY_ES_BY_ECM_DOC_ID" ;
	public static String QUERY_ES_BY_BOOL="QUERY_ES_BY_BOOL";
	public static String UPLOAD_ES_TAG="UPLOAD_ES_TAG";
	public static String UPDATE_ES_TAG="UPDATE_ES_TAG";
	/**
	 * 枚举类型
	 * <AUTHOR>
	 *
	 */
	public enum OptionKeys{CREATEUSERTOKEN,IMMEDIATEMIGRATE,INQUIREDM,ALLMODELMSG,UPLOAD,DEL,QUERY,UPDATE,U_ADD,U_DEL,U_REPLACE,U_MODIFY,METATEMPLATE,BUSITYPETEMPLATE,ARCTYPETEMPLATE,RESOURCEDIR,
			METADATATABLE,ANNOTATION,LOGIN,LOGOUT,PERMISSION,CHECKIN,CHECKOUT,RELATION,OTHER_QUERY,OTHER_BATCH_QUERY,OTHER_FILE_QUERY,
		FILE_TYPE_HANDLE,A_OR_U_ANNOTATION,GET_NODE, FILING, GET_BATCH_ID,BREAK_POINT,MIGRATE,HEIGHT_QUERY,QUERY_NODEINFO_BY_GROUPID_AND_INSNO,QUERY_ES_BY_ECM_DOC_ID,QUERY_ES_BY_BOOL,UPLOAD_ES_TAG,UPDATE_ES_TAG};
}