<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jaxws="http://cxf.apache.org/jaxws"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
		http://cxf.apache.org/jaxws 
		http://cxf.apache.org/schemas/jaxws.xsd"
       default-autowire="byName" default-lazy-init="true">

    <import resource="classpath:META-INF/cxf/cxf.xml"/>
    <import resource="classpath:META-INF/cxf/cxf-extension-soap.xml"/>
    <import resource="classpath:META-INF/cxf/cxf-servlet.xml"/>

    <jaxws:endpoint id="WsInterface" address="/WsInterface"
                    implementor="com.sunyard.console.configmanager.wsserviceimpl.SunEcmConsoleImpl"/>

</beans>