<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="SunARS-fm" />
        <module name="SunARS-file" />
        <module name="eureka" />
        <module name="aos-start" />
        <module name="sunfa" />
        <module name="feign-client" />
        <module name="common-extend" />
        <module name="SunECMDM_Core" />
        <module name="SunECMDM" />
        <module name="SunARS-supervise" />
        <module name="SunECMConsole" />
        <module name="SunARS-et" />
        <module name="SunARS-system" />
        <module name="sunfa-common" />
        <module name="flow" />
        <module name="sunfa-financial" />
        <module name="sunfa-seconddevelop" />
        <module name="unify" />
        <module name="sunfa-bankmodel" />
        <module name="SunECMConsole_Core" />
        <module name="SunARS-ModelRun" />
        <module name="sunfa-supervision" />
        <module name="SunARS-mc" />
        <module name="trt" />
        <module name="SunARS-common" />
        <module name="report" />
        <module name="config" />
        <module name="SunARS-risk" />
        <module name="sunfa-supervisiondevelop" />
        <module name="SunETLConsole" />
        <module name="SunETLExecute" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="ai" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="SunARS-ModelRun" options="-parameters" />
      <module name="SunARS-common" options="-parameters" />
      <module name="SunARS-et" options="-parameters" />
      <module name="SunARS-file" options="-parameters" />
      <module name="SunARS-fm" options="-parameters" />
      <module name="SunARS-mc" options="-parameters" />
      <module name="SunARS-risk" options="-parameters" />
      <module name="SunARS-supervise" options="-parameters" />
      <module name="SunARS-system" options="-parameters" />
      <module name="SunECMConsole" options="-parameters" />
      <module name="SunECMConsole_Core" options="-parameters" />
      <module name="SunECMDM" options="-parameters" />
      <module name="SunECMDM_Core" options="-parameters" />
      <module name="SunETLConsole" options="-parameters" />
      <module name="SunETLExecute" options="-parameters" />
      <module name="ai" options="-parameters" />
      <module name="aos-start" options="-parameters" />
      <module name="common-extend" options="-parameters" />
      <module name="config" options="-parameters" />
      <module name="dop" options="-parameters" />
      <module name="eureka" options="-parameters" />
      <module name="feign-client" options="-parameters" />
      <module name="flow" options="-parameters" />
      <module name="report" options="-parameters" />
      <module name="trt" options="-parameters" />
      <module name="unify" options="-parameters" />
    </option>
  </component>
</project>