2025-07-23 10:15:19.373 [OrganNo_00023_UserNo_admin] [299fa39633da409a/5e6cebca5463be2d] [http-nio-9009-exec-67] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-23 10:15:19.492 [OrganNo_00023_UserNo_admin] [299fa39633da409a/5e6cebca5463be2d] [http-nio-9009-exec-67] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:15:19.493 [] [299fa39633da409a/5e6cebca5463be2d] [http-nio-9009-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:15:19 操作结束时间: 2025-07-23 10:15:19!总共花费时间: 131 毫秒！
2025-07-23 10:15:19.540 [OrganNo_00023_UserNo_admin] [5d4ecfb9c3522de3/164c2dc1ae9a501d] [http-nio-9009-exec-68] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-23 10:15:19.584 [OrganNo_00023_UserNo_admin] [5d4ecfb9c3522de3/164c2dc1ae9a501d] [http-nio-9009-exec-68] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:15:19.585 [] [5d4ecfb9c3522de3/164c2dc1ae9a501d] [http-nio-9009-exec-68] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:15:19 操作结束时间: 2025-07-23 10:15:19!总共花费时间: 63 毫秒！
2025-07-23 10:31:11.413 [OrganNo_00023_UserNo_admin] [f630fc017ae99866/258d20942031feab] [http-nio-9009-exec-70] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"2"
	}
}
2025-07-23 10:31:11.476 [OrganNo_00023_UserNo_admin] [f630fc017ae99866/258d20942031feab] [http-nio-9009-exec-70] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:31:11.476 [] [f630fc017ae99866/258d20942031feab] [http-nio-9009-exec-70] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:31:11 操作结束时间: 2025-07-23 10:31:11!总共花费时间: 92 毫秒！
2025-07-23 10:31:14.865 [OrganNo_00023_UserNo_admin] [683c5893637dd7aa/65e51959cbe108d7] [http-nio-9009-exec-72] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"getUnscanCodeList"
	}
}
2025-07-23 10:31:14.885 [OrganNo_00023_UserNo_admin] [683c5893637dd7aa/65e51959cbe108d7] [http-nio-9009-exec-72] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"codeNo":"BG01",
				"codeType":"财务报告",
				"id":"8BAAF15146D3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"中期财务报告:本级及全辖月\\季\\半年财务报告",
				"codeNo":"BG02",
				"codeType":"财务报告",
				"id":"8BAAF15146D4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"下级机构上报的财务报告",
				"codeNo":"BG03",
				"codeType":"财务报告",
				"id":"8BAAF15146D5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"不定期报表",
				"codeNo":"BG04",
				"codeType":"财务报告",
				"id":"8BAAF15146D6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他财务报告",
				"codeNo":"BG05",
				"codeType":"财务报告",
				"id":"8BAAF15146D7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"特殊介质保存的会计资料",
				"codeNo":"JZ01",
				"codeType":"特殊介质",
				"id":"8BAAF15146D8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"codeType":"会计凭证",
				"id":"8BAAF15146D9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"其他会计凭证",
				"codeNo":"PZ02",
				"codeType":"会计凭证",
				"id":"8BAAF15146DAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"挂失登记及补发凭单收据",
				"codeNo":"QT01",
				"codeType":"其他",
				"id":"8BAAF15146DBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"存\\贷款开销户记录",
				"codeNo":"QT02",
				"codeType":"其他",
				"id":"8BAAF15146DCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"有权机关查询\\冻结\\扣划公函资料和登记簿",
				"codeNo":"QT03",
				"codeType":"其他",
				"id":"8BAAF15146DDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"帐销案存记录",
				"codeNo":"QT04",
				"codeType":"其他",
				"id":"8BAAF15146DEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"codeType":"其他",
				"id":"8BAAF15146DFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"codeType":"其他",
				"id":"8BAAF15146E0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案保管登记簿",
				"codeNo":"QT07",
				"codeType":"其他",
				"id":"8BAAF15146E1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"销毁清册及相关审批资料",
				"codeNo":"QT08",
				"codeType":"其他",
				"id":"8BAAF15146E2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"机构变动交接清册",
				"codeNo":"QT09",
				"codeType":"其他",
				"id":"8BAAF15146E3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"重要单证\\有价凭证\\印章的领发\\保管和缴销记录",
				"codeNo":"QT10",
				"codeType":"其他",
				"id":"8BAAF15146E4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计档案移交清册",
				"codeNo":"QT11",
				"codeType":"其他",
				"id":"8BAAF15146E5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计人员交接清册",
				"codeNo":"QT12",
				"codeType":"其他",
				"id":"8BAAF15146E6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计事后监督档案(含差错通知书)",
				"codeNo":"QT13",
				"codeType":"其他",
				"id":"8BAAF15146E7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计专用印章登记簿及印章处置清单",
				"codeNo":"QT14",
				"codeType":"其他",
				"id":"8BAAF15146E8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"codeType":"其他",
				"id":"8BAAF15146E9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行计息和核息清单及保证金利息清单",
				"codeNo":"QT16",
				"codeType":"其他",
				"id":"8BAAF15146EAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行、联行对帐单、确认函和查询查复书",
				"codeNo":"QT17",
				"codeType":"其他",
				"id":"8BAAF15146EBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部对帐记录及其他对帐资料",
				"codeNo":"QT18",
				"codeType":"其他",
				"id":"8BAAF15146ECE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"人民银行及同业对帐单",
				"codeNo":"QT19",
				"codeType":"其他",
				"id":"8BAAF15146EDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"对帐回单",
				"codeNo":"QT20",
				"codeType":"其他",
				"id":"8BAAF15146EEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部资金往来核算资料",
				"codeNo":"QT21",
				"codeType":"其他",
				"id":"8BAAF15146EFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"财会检查档案",
				"codeNo":"QT22",
				"codeType":"其他",
				"id":"8BAAF15146F0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案查阅登记簿和申请单",
				"codeNo":"QT23",
				"codeType":"其他",
				"id":"8BAAF15146F1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计档案拆封申请单",
				"codeNo":"QT24",
				"codeType":"其他",
				"id":"8BAAF15146F2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"计算机应用系统运行日志",
				"codeNo":"QT25",
				"codeType":"其他",
				"id":"8BAAF15146F3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计电算化系统开发的全套文档资料",
				"codeNo":"QT26",
				"codeType":"其他",
				"id":"8BAAF15146F4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计档案",
				"codeNo":"QT27",
				"codeType":"其他",
				"id":"8BAAF15146F5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"投资科目分户帐",
				"codeNo":"ZB01",
				"codeType":"会计账簿",
				"id":"8BAAF15146F6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"资本金、股金及股权明细",
				"codeNo":"ZB02",
				"codeType":"会计账簿",
				"id":"8BAAF15146F7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"明细账、分户帐、卡片账未销卡清单、帐户余额表及其他明细帐",
				"codeNo":"ZB03",
				"codeType":"会计账簿",
				"id":"8BAAF15146F8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"总帐：本级及全辖汇总日计表及其他形式总帐",
				"codeNo":"ZB04",
				"codeType":"会计账簿",
				"id":"8BAAF15146F9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"codeType":"会计账簿",
				"id":"8BAAF15146FAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"已处置固定资产卡片(按报废清理后计算保管期限)",
				"codeNo":"ZB06",
				"codeType":"会计账簿",
				"id":"8BAAF15146FBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计帐簿",
				"codeNo":"ZB07",
				"codeType":"会计账簿",
				"id":"8BAAF15146FCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			}
		]
	},
	"retMsg":""
}
2025-07-23 10:31:14.886 [] [683c5893637dd7aa/65e51959cbe108d7] [http-nio-9009-exec-72] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-23 10:31:14 操作结束时间: 2025-07-23 10:31:14!总共花费时间: 31 毫秒！
2025-07-23 10:31:15.040 [OrganNo_00023_UserNo_admin] [d4a2d5aad97bde49/cbcfeff74f2d1ee1] [http-nio-9009-exec-73] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"1"
	}
}
2025-07-23 10:31:15.086 [OrganNo_00023_UserNo_admin] [d4a2d5aad97bde49/cbcfeff74f2d1ee1] [http-nio-9009-exec-73] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"QT15-2025-000023-10-000002",
				"archState":"1",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"dayNum":1,
				"endDate":"20251231",
				"id":"0cc30aedfebc412db94252ea1d501cff",
				"keepYear":"10",
				"lateReason":"1123123",
				"registerDate":"20250717",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250721",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"5",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:31:15.086 [] [d4a2d5aad97bde49/cbcfeff74f2d1ee1] [http-nio-9009-exec-73] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:31:14 操作结束时间: 2025-07-23 10:31:15!总共花费时间: 177 毫秒！
2025-07-23 10:31:21.044 [OrganNo_00023_UserNo_admin] [1d56063230655e4c/856e761c946ee569] [http-nio-9009-exec-75] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"printCode",
		"ids":[
			"0cc30aedfebc412db94252ea1d501cff"
		]
	}
}
2025-07-23 10:31:21.077 [OrganNo_00023_UserNo_admin] [1d56063230655e4c/856e761c946ee569] [http-nio-9009-exec-75] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"barcodeList":[
			{
				"fileName":"QT15-2025-000023-10-000002.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/arch/QT15-2025-000023-10-000002/QT15-2025-000023-10-000002.png"
			}
		]
	},
	"retMsg":"打印档案准备完成"
}
2025-07-23 10:31:21.077 [] [1d56063230655e4c/856e761c946ee569] [http-nio-9009-exec-75] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 打印档案!请求IP地址: ************** 操作开始时间: 2025-07-23 10:31:21 操作结束时间: 2025-07-23 10:31:21!总共花费时间: 42 毫秒！
2025-07-23 10:48:18.845 [OrganNo_00023_UserNo_admin] [9efcf462d4fd2026/ad4ff9c73f66d108] [http-nio-9009-exec-76] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"getUnscanCodeList"
	}
}
2025-07-23 10:48:18.889 [OrganNo_00023_UserNo_admin] [9efcf462d4fd2026/ad4ff9c73f66d108] [http-nio-9009-exec-76] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"codeNo":"BG01",
				"codeType":"财务报告",
				"id":"8BAAF15146D3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"中期财务报告:本级及全辖月\\季\\半年财务报告",
				"codeNo":"BG02",
				"codeType":"财务报告",
				"id":"8BAAF15146D4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"下级机构上报的财务报告",
				"codeNo":"BG03",
				"codeType":"财务报告",
				"id":"8BAAF15146D5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"不定期报表",
				"codeNo":"BG04",
				"codeType":"财务报告",
				"id":"8BAAF15146D6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他财务报告",
				"codeNo":"BG05",
				"codeType":"财务报告",
				"id":"8BAAF15146D7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"特殊介质保存的会计资料",
				"codeNo":"JZ01",
				"codeType":"特殊介质",
				"id":"8BAAF15146D8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"codeType":"会计凭证",
				"id":"8BAAF15146D9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"其他会计凭证",
				"codeNo":"PZ02",
				"codeType":"会计凭证",
				"id":"8BAAF15146DAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"挂失登记及补发凭单收据",
				"codeNo":"QT01",
				"codeType":"其他",
				"id":"8BAAF15146DBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"存\\贷款开销户记录",
				"codeNo":"QT02",
				"codeType":"其他",
				"id":"8BAAF15146DCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"有权机关查询\\冻结\\扣划公函资料和登记簿",
				"codeNo":"QT03",
				"codeType":"其他",
				"id":"8BAAF15146DDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"帐销案存记录",
				"codeNo":"QT04",
				"codeType":"其他",
				"id":"8BAAF15146DEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"codeType":"其他",
				"id":"8BAAF15146DFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"codeType":"其他",
				"id":"8BAAF15146E0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案保管登记簿",
				"codeNo":"QT07",
				"codeType":"其他",
				"id":"8BAAF15146E1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"销毁清册及相关审批资料",
				"codeNo":"QT08",
				"codeType":"其他",
				"id":"8BAAF15146E2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"机构变动交接清册",
				"codeNo":"QT09",
				"codeType":"其他",
				"id":"8BAAF15146E3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"重要单证\\有价凭证\\印章的领发\\保管和缴销记录",
				"codeNo":"QT10",
				"codeType":"其他",
				"id":"8BAAF15146E4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计档案移交清册",
				"codeNo":"QT11",
				"codeType":"其他",
				"id":"8BAAF15146E5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计人员交接清册",
				"codeNo":"QT12",
				"codeType":"其他",
				"id":"8BAAF15146E6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计事后监督档案(含差错通知书)",
				"codeNo":"QT13",
				"codeType":"其他",
				"id":"8BAAF15146E7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计专用印章登记簿及印章处置清单",
				"codeNo":"QT14",
				"codeType":"其他",
				"id":"8BAAF15146E8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"codeType":"其他",
				"id":"8BAAF15146E9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行计息和核息清单及保证金利息清单",
				"codeNo":"QT16",
				"codeType":"其他",
				"id":"8BAAF15146EAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行、联行对帐单、确认函和查询查复书",
				"codeNo":"QT17",
				"codeType":"其他",
				"id":"8BAAF15146EBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部对帐记录及其他对帐资料",
				"codeNo":"QT18",
				"codeType":"其他",
				"id":"8BAAF15146ECE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"人民银行及同业对帐单",
				"codeNo":"QT19",
				"codeType":"其他",
				"id":"8BAAF15146EDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"对帐回单",
				"codeNo":"QT20",
				"codeType":"其他",
				"id":"8BAAF15146EEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部资金往来核算资料",
				"codeNo":"QT21",
				"codeType":"其他",
				"id":"8BAAF15146EFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"财会检查档案",
				"codeNo":"QT22",
				"codeType":"其他",
				"id":"8BAAF15146F0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案查阅登记簿和申请单",
				"codeNo":"QT23",
				"codeType":"其他",
				"id":"8BAAF15146F1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计档案拆封申请单",
				"codeNo":"QT24",
				"codeType":"其他",
				"id":"8BAAF15146F2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"计算机应用系统运行日志",
				"codeNo":"QT25",
				"codeType":"其他",
				"id":"8BAAF15146F3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计电算化系统开发的全套文档资料",
				"codeNo":"QT26",
				"codeType":"其他",
				"id":"8BAAF15146F4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计档案",
				"codeNo":"QT27",
				"codeType":"其他",
				"id":"8BAAF15146F5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"投资科目分户帐",
				"codeNo":"ZB01",
				"codeType":"会计账簿",
				"id":"8BAAF15146F6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"资本金、股金及股权明细",
				"codeNo":"ZB02",
				"codeType":"会计账簿",
				"id":"8BAAF15146F7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"明细账、分户帐、卡片账未销卡清单、帐户余额表及其他明细帐",
				"codeNo":"ZB03",
				"codeType":"会计账簿",
				"id":"8BAAF15146F8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"总帐：本级及全辖汇总日计表及其他形式总帐",
				"codeNo":"ZB04",
				"codeType":"会计账簿",
				"id":"8BAAF15146F9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"codeType":"会计账簿",
				"id":"8BAAF15146FAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"已处置固定资产卡片(按报废清理后计算保管期限)",
				"codeNo":"ZB06",
				"codeType":"会计账簿",
				"id":"8BAAF15146FBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计帐簿",
				"codeNo":"ZB07",
				"codeType":"会计账簿",
				"id":"8BAAF15146FCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			}
		]
	},
	"retMsg":""
}
2025-07-23 10:48:18.889 [] [9efcf462d4fd2026/ad4ff9c73f66d108] [http-nio-9009-exec-76] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-23 10:48:18 操作结束时间: 2025-07-23 10:48:18!总共花费时间: 66 毫秒！
2025-07-23 10:48:18.930 [OrganNo_00023_UserNo_admin] [cd0131718e7720f0/05f395f097ad445b] [http-nio-9009-exec-78] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"1"
	}
}
2025-07-23 10:48:18.977 [OrganNo_00023_UserNo_admin] [cd0131718e7720f0/05f395f097ad445b] [http-nio-9009-exec-78] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"QT15-2025-000023-10-000002",
				"archState":"1",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"dayNum":1,
				"endDate":"20251231",
				"id":"0cc30aedfebc412db94252ea1d501cff",
				"keepYear":"10",
				"lateReason":"1123123",
				"registerDate":"20250717",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250721",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"5",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:48:18.978 [] [cd0131718e7720f0/05f395f097ad445b] [http-nio-9009-exec-78] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:48:18 操作结束时间: 2025-07-23 10:48:18!总共花费时间: 66 毫秒！
2025-07-23 10:48:24.076 [OrganNo_00023_UserNo_admin] [9674824d2eeea748/9b61c77179207ea1] [http-nio-9009-exec-80] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"printCode",
		"ids":[
			"0cc30aedfebc412db94252ea1d501cff"
		]
	}
}
2025-07-23 10:48:24.117 [OrganNo_00023_UserNo_admin] [9674824d2eeea748/9b61c77179207ea1] [http-nio-9009-exec-80] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"barcodeList":[
			{
				"fileName":"QT15-2025-000023-10-000002.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/arch/QT15-2025-000023-10-000002/QT15-2025-000023-10-000002.png"
			}
		]
	},
	"retMsg":"打印档案准备完成"
}
2025-07-23 10:48:24.118 [] [9674824d2eeea748/9b61c77179207ea1] [http-nio-9009-exec-80] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 打印档案!请求IP地址: ************** 操作开始时间: 2025-07-23 10:48:24 操作结束时间: 2025-07-23 10:48:24!总共花费时间: 51 毫秒！
2025-07-23 10:49:04.913 [OrganNo_00023_UserNo_admin] [6915a22895635c07/d8060b4baa2f10aa] [http-nio-9009-exec-81] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-23 10:49:04.970 [OrganNo_00023_UserNo_admin] [6915a22895635c07/d8060b4baa2f10aa] [http-nio-9009-exec-81] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:49:04.970 [] [6915a22895635c07/d8060b4baa2f10aa] [http-nio-9009-exec-81] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:49:04 操作结束时间: 2025-07-23 10:49:04!总共花费时间: 77 毫秒！
2025-07-23 10:49:05.015 [OrganNo_00023_UserNo_admin] [bff193c8e8768ad3/ee968b742e5c7331] [http-nio-9009-exec-83] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-23 10:49:05.062 [OrganNo_00023_UserNo_admin] [bff193c8e8768ad3/ee968b742e5c7331] [http-nio-9009-exec-83] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:49:05.063 [] [bff193c8e8768ad3/ee968b742e5c7331] [http-nio-9009-exec-83] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:49:05 操作结束时间: 2025-07-23 10:49:05!总共花费时间: 60 毫秒！
2025-07-23 10:49:07.891 [OrganNo_00023_UserNo_admin] [8f5b3986e3e4ca28/8c91e94ed9bdd901] [http-nio-9009-exec-84] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"code":"20250100002"
	}
}
2025-07-23 10:49:07.964 [OrganNo_00023_UserNo_admin] [8f5b3986e3e4ca28/8c91e94ed9bdd901] [http-nio-9009-exec-84] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"root":[
			{
				"fileName":"20250100002.png",
				"src":"/barCodeImgs/trunk/20250100002.png"
			},
			{
				"fileName":"20250100003.png",
				"src":"/barCodeImgs/trunk/20250100003.png"
			},
			{
				"fileName":"20250100004.png",
				"src":"/barCodeImgs/trunk/20250100004.png"
			},
			{
				"fileName":"20250100005.png",
				"src":"/barCodeImgs/trunk/20250100005.png"
			},
			{
				"fileName":"20250100006.png",
				"src":"/barCodeImgs/trunk/20250100006.png"
			},
			{
				"fileName":"20250100007.png",
				"src":"/barCodeImgs/trunk/20250100007.png"
			},
			{
				"fileName":"20250100008.png",
				"src":"/barCodeImgs/trunk/20250100008.png"
			},
			{
				"fileName":"20250100009.png",
				"src":"/barCodeImgs/trunk/20250100009.png"
			},
			{
				"fileName":"20250100010.png",
				"src":"/barCodeImgs/trunk/20250100010.png"
			},
			{
				"fileName":"20250100011.png",
				"src":"/barCodeImgs/trunk/20250100011.png"
			}
		]
	},
	"retMsg":"打印箱号准备完成"
}
2025-07-23 10:49:07.964 [] [8f5b3986e3e4ca28/8c91e94ed9bdd901] [http-nio-9009-exec-84] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-23 10:49:07 操作结束时间: 2025-07-23 10:49:07!总共花费时间: 100 毫秒！
2025-07-23 10:57:28.529 [OrganNo_00023_UserNo_admin] [e87e860c5ba51e3d/9b908f9c8734ec12] [http-nio-9009-exec-86] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-23 10:57:28.593 [OrganNo_00023_UserNo_admin] [e87e860c5ba51e3d/9b908f9c8734ec12] [http-nio-9009-exec-86] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:57:28.593 [] [e87e860c5ba51e3d/9b908f9c8734ec12] [http-nio-9009-exec-86] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:57:28 操作结束时间: 2025-07-23 10:57:28!总共花费时间: 75 毫秒！
2025-07-23 10:57:28.654 [OrganNo_00023_UserNo_admin] [549be75e9119e399/5b6e94884b1660c5] [http-nio-9009-exec-87] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-23 10:57:28.698 [OrganNo_00023_UserNo_admin] [549be75e9119e399/5b6e94884b1660c5] [http-nio-9009-exec-87] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 10:57:28.699 [] [549be75e9119e399/5b6e94884b1660c5] [http-nio-9009-exec-87] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:57:28 操作结束时间: 2025-07-23 10:57:28!总共花费时间: 68 毫秒！
2025-07-23 10:57:31.098 [OrganNo_00023_UserNo_admin] [64e1cddf55fa77b5/58b31021f9d05d6b] [http-nio-9009-exec-88] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"code":"20250100002"
	}
}
2025-07-23 10:57:31.168 [OrganNo_00023_UserNo_admin] [64e1cddf55fa77b5/58b31021f9d05d6b] [http-nio-9009-exec-88] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"root":[
			{
				"fileName":"20250100002.png",
				"src":"/barCodeImgs/trunk/20250100002.png"
			},
			{
				"fileName":"20250100003.png",
				"src":"/barCodeImgs/trunk/20250100003.png"
			},
			{
				"fileName":"20250100004.png",
				"src":"/barCodeImgs/trunk/20250100004.png"
			},
			{
				"fileName":"20250100005.png",
				"src":"/barCodeImgs/trunk/20250100005.png"
			},
			{
				"fileName":"20250100006.png",
				"src":"/barCodeImgs/trunk/20250100006.png"
			},
			{
				"fileName":"20250100007.png",
				"src":"/barCodeImgs/trunk/20250100007.png"
			},
			{
				"fileName":"20250100008.png",
				"src":"/barCodeImgs/trunk/20250100008.png"
			},
			{
				"fileName":"20250100009.png",
				"src":"/barCodeImgs/trunk/20250100009.png"
			},
			{
				"fileName":"20250100010.png",
				"src":"/barCodeImgs/trunk/20250100010.png"
			},
			{
				"fileName":"20250100011.png",
				"src":"/barCodeImgs/trunk/20250100011.png"
			}
		]
	},
	"retMsg":"打印箱号准备完成"
}
2025-07-23 10:57:31.169 [] [64e1cddf55fa77b5/58b31021f9d05d6b] [http-nio-9009-exec-88] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-23 10:57:31 操作结束时间: 2025-07-23 10:57:31!总共花费时间: 91 毫秒！
2025-07-23 13:35:36.359 [OrganNo_00023_UserNo_admin] [f880abfce9b7fe92/ee3a06b95d2fe920] [http-nio-9009-exec-90] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-23 13:35:36.443 [OrganNo_00023_UserNo_admin] [f880abfce9b7fe92/ee3a06b95d2fe920] [http-nio-9009-exec-90] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 13:35:36.443 [] [f880abfce9b7fe92/ee3a06b95d2fe920] [http-nio-9009-exec-90] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 13:35:36 操作结束时间: 2025-07-23 13:35:36!总共花费时间: 93 毫秒！
2025-07-23 13:35:36.490 [OrganNo_00023_UserNo_admin] [fcf54a9134b6340b/1e7fd3f2d793deea] [http-nio-9009-exec-91] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-23 13:35:36.535 [OrganNo_00023_UserNo_admin] [fcf54a9134b6340b/1e7fd3f2d793deea] [http-nio-9009-exec-91] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 13:35:36.535 [] [fcf54a9134b6340b/1e7fd3f2d793deea] [http-nio-9009-exec-91] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 13:35:36 操作结束时间: 2025-07-23 13:35:36!总共花费时间: 68 毫秒！
2025-07-23 13:35:40.828 [OrganNo_00023_UserNo_admin] [d46f195174d08f1a/47ac0f4d2c9b05d3] [http-nio-9009-exec-93] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"code":"20250100002"
	}
}
2025-07-23 13:35:40.895 [OrganNo_00023_UserNo_admin] [d46f195174d08f1a/47ac0f4d2c9b05d3] [http-nio-9009-exec-93] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"root":[
			{
				"fileName":"20250100002.png",
				"src":"/barCodeImgs/trunk/20250100002.png"
			},
			{
				"fileName":"20250100003.png",
				"src":"/barCodeImgs/trunk/20250100003.png"
			},
			{
				"fileName":"20250100004.png",
				"src":"/barCodeImgs/trunk/20250100004.png"
			},
			{
				"fileName":"20250100005.png",
				"src":"/barCodeImgs/trunk/20250100005.png"
			},
			{
				"fileName":"20250100006.png",
				"src":"/barCodeImgs/trunk/20250100006.png"
			},
			{
				"fileName":"20250100007.png",
				"src":"/barCodeImgs/trunk/20250100007.png"
			},
			{
				"fileName":"20250100008.png",
				"src":"/barCodeImgs/trunk/20250100008.png"
			},
			{
				"fileName":"20250100009.png",
				"src":"/barCodeImgs/trunk/20250100009.png"
			},
			{
				"fileName":"20250100010.png",
				"src":"/barCodeImgs/trunk/20250100010.png"
			},
			{
				"fileName":"20250100011.png",
				"src":"/barCodeImgs/trunk/20250100011.png"
			}
		]
	},
	"retMsg":"打印箱号准备完成"
}
2025-07-23 13:35:40.896 [] [d46f195174d08f1a/47ac0f4d2c9b05d3] [http-nio-9009-exec-93] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-23 13:35:40 操作结束时间: 2025-07-23 13:35:40!总共花费时间: 75 毫秒！
2025-07-23 14:43:59.268 [OrganNo_00023_UserNo_admin] [701c983934463809/7d527972de7ab56f] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-23 14:43:59.637 [OrganNo_00023_UserNo_admin] [701c983934463809/7d527972de7ab56f] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 14:43:59.641 [] [701c983934463809/7d527972de7ab56f] [http-nio-9009-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 14:43:59 操作结束时间: 2025-07-23 14:43:59!总共花费时间: 438 毫秒！
2025-07-23 14:43:59.715 [OrganNo_00023_UserNo_admin] [671afe2f40edb301/e07e83c42508d840] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-23 14:43:59.775 [OrganNo_00023_UserNo_admin] [671afe2f40edb301/e07e83c42508d840] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-23 14:43:59.776 [] [671afe2f40edb301/e07e83c42508d840] [http-nio-9009-exec-2] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 14:43:59 操作结束时间: 2025-07-23 14:43:59!总共花费时间: 88 毫秒！
2025-07-23 14:44:01.632 [OrganNo_00023_UserNo_admin] [b2f51dc8ac2d0d3a/d397eb2e30a8e96c] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"code":"20250100002"
	}
}
2025-07-23 14:44:01.843 [OrganNo_00023_UserNo_admin] [b2f51dc8ac2d0d3a/d397eb2e30a8e96c] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"root":[
			{
				"fileName":"20250100002.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100002/20250100002.png"
			},
			{
				"fileName":"20250100003.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100003/20250100003.png"
			},
			{
				"fileName":"20250100004.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100004/20250100004.png"
			},
			{
				"fileName":"20250100005.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100005/20250100005.png"
			},
			{
				"fileName":"20250100006.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100006/20250100006.png"
			},
			{
				"fileName":"20250100007.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100007/20250100007.png"
			},
			{
				"fileName":"20250100008.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100008/20250100008.png"
			},
			{
				"fileName":"20250100009.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100009/20250100009.png"
			},
			{
				"fileName":"20250100010.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100010/20250100010.png"
			},
			{
				"fileName":"20250100011.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/trunk/20250100011/20250100011.png"
			}
		]
	},
	"retMsg":"打印箱号准备完成"
}
2025-07-23 14:44:01.844 [] [b2f51dc8ac2d0d3a/d397eb2e30a8e96c] [http-nio-9009-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-23 14:44:01 操作结束时间: 2025-07-23 14:44:01!总共花费时间: 238 毫秒！
