package com.sunyard.console.contentmodelmanage.action;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.servlet.http.HttpSession;
import javax.ws.rs.Consumes;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.DateUtil;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.contentmodelmanage.bean.*;
import com.sunyard.console.contentmodelmanage.dao.AttributeSetManageDaoImp;
import com.sunyard.console.contentmodelmanage.dao.ContentObjectManageDao;
import com.sunyard.console.contentmodelmanage.excel.ExcelUtil;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import com.sunyard.console.common.config.ReadConfig;
import com.sunyard.console.contentmodelmanage.excel.bean.ContentModelExcelBean;
import com.sunyard.console.contentmodelmanage.excel.bean.ResponseBean;
import com.sunyard.console.contentmodelmanage.excel.dao.ContentModelExcelUtil;
import com.sunyard.console.contentmodelmanage.util.ContentObjectManageUtil;
import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.safemanage.bean.UserInfoBean;
import com.sunyard.console.threadpoool.IssueUtils;
import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerInfoBean;

/**
 * <p>
 * Title: 内容对象管理action
 * </p>
 * <p>
 * Description: 用于管理内容对象信息的获取,以及增删改等操作的action访问类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class ContentObjectManageAction extends BaseAction {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 内容模型对象操作对象
	 */
	@Autowired
	ContentObjectManageDao contentObjectManageDao;
	/**
	 * 内容模型工具类对象
	 */
	@Autowired
	private ContentObjectManageUtil contentObjUtil = new ContentObjectManageUtil();
	/**
	 * 增加权限
	 */
	private String confer_add;
	/**
	 * 修改权限
	 */
	private String confer_modify;
	/**
	 * 删除权限
	 */
	private String confer_del;
	/**
	 * 查询权限
	 */
	private String confer_query;
	/**
	 * 批注权限
	 */
	private String confer_comment;
	/**
	 * 选择的用户列表
	 */
	private String user_ids;
	/**
	 * 选择的角色列表
	 */
	private String role_ids;
	/**
	 * 对象中文名
	 */
	private String model_name;
	/**
	 * 对象英文名
	 */
	private String model_code;
	/**
	 * 分表时间
	 */
	private int separate_table_days;
	/**
	 * 分表张数
	 */
	private int separate_table_num;
	/**
	 *手动续建分表时间
	 */
	private int newsep_table_days;
	/**
	 * 分动续建分表张数
	 */
	private int newsep_table_num;
	/**
	 * 父对象ID
	 */
	private String f_model_id;
	/**
	 * 定义时间
	 */
	private String model_defines_time;
	/**
	 * 定义人
	 */
	private String model_defines_person;
	/**
	 * 是否版本控制
	 */
	private int version_control;
	/**
	 * 存储（业务）数据的创建（或办理）时间的字段
	 */
	private String creation_date_column;//
	/**
	 * 存储（业务）数据的结束（或办结）时间的字段
	 */
	private String finish_date_column;//
	/**
	 * 内容类型
	 */
	private int model_type;
	/**
	 * 数据来源
	 */
	private int data_from;
	/**
	 * 是否全文检索
	 */
	private int text_search;
	/**
	 * 是否令牌控制
	 */
	private int token_check;
	/**
	 * 是否显示系统属性
	 */
	private int show;
	/**
	 * 选择的属性ID集
	 */
	private String attribute_ids;
	/**
	 * 选择的索引ID集
	 */
	private String index_ids;
	/**
	 * 远程校验字段值
	 */
	private String value;
	/**
	 * 子对象ID
	 */
	private String s_object_id;
	/**
	 * 内容模型分表开始时间
	 */
	private String begin_time;
	/**
	 * 内容模型分表结束时间
	 */
	private String end_time;
	/**
	 * 分页开始位置
	 */
	private int start;
	/**
	 * 分页总记录数
	 */
	private int limit;
	/**
	 * 日志对象
	 */
	private final static  Logger log = LoggerFactory.getLogger(ContentObjectManageAction.class);
	/**
	 * 表空间名称
	 */
	private String table_space;
	@Autowired
	private ContentModelExcelUtil contentModelExcelUtil;
	// 注意，file并不是指前端jsp上传过来的文件本身，而是文件上传过来存放在临时文件夹下面的文件
	private File file;
	// 提交过来的file的名字
	private String fileFileName;
	// 提交过来的file的MIME类型
	private String fileContentType;
	//用来放属性和属性是否可以修改的信息用:隔开，属性之间用逗号隔开 lsh:0,sfz:1 (0为可以修改，1为不可以修改) 
	private String attrids;	
	/**
	 * 需要加密的字节数
	 */
	private String encodeByteSize;
	/**
	 * 加密类型
	 */
	private String encode_type;
	
	public int getNewsep_table_days() {
		return newsep_table_days;
	}

	public void setNewsep_table_days(int newsep_table_days) {
		this.newsep_table_days = newsep_table_days;
	}

	public int getNewsep_table_num() {
		return newsep_table_num;
	}

	public void setNewsep_table_num(int newseparate_table_num) {
		this.newsep_table_num = newseparate_table_num;
	}

	public int getSeparate_table_num() {
		return separate_table_num;
	}

	public void setSeparate_table_num(int separate_table_num) {
		this.separate_table_num = separate_table_num;
	}

	public String getEncodeByteSize() {
		return encodeByteSize;
	}

	public void setEncodeByteSize(String encodeByteSize) {
		this.encodeByteSize = encodeByteSize;
	}

	public String getEncode_type() {
		return encode_type;
	}

	public void setEncode_type(String encode_type) {
		this.encode_type = encode_type;
	}

	public String getAttrids() {
		return attrids;
	}

	public void setAttrids(String attrids) {
		this.attrids = attrids;
	}

	public String getFileContentType() {
		return fileContentType;
	}

	public void setFileContentType(String fileContentType) {
		this.fileContentType = fileContentType;
	}

	public File getFile() {
		return file;
	}

	public void setFile(File file) {
		this.file = file;
	}


	public String getFileFileName() {
		return fileFileName;
	}

	public void setFileFileName(String fileFileName) {
		this.fileFileName = fileFileName;
	}

	public void setContentModelExcelUtil(ContentModelExcelUtil contentModelExcelUtil) {
		this.contentModelExcelUtil = contentModelExcelUtil;
	}

	public String getTable_space() {
		return table_space;
	}

	public void setTable_space(String table_space) {
		this.table_space = table_space;
	}

	public ContentObjectManageDao getContentObjectManageDao() {
		return contentObjectManageDao;
	}

	public int getShow() {
		return show;
	}

	public void setShow(int show) {
		this.show = show;
	}

	public void setContentObjectManageDao(
			ContentObjectManageDao contentObjectManageDao) {
		this.contentObjectManageDao = contentObjectManageDao;
	}

	public String getModel_name() {
		return model_name;
	}

	public String getCreation_date_column() {
		return creation_date_column;
	}

	public void setCreation_date_column(String creationDateColumn) {
		creation_date_column = creationDateColumn;
	}

	public String getFinish_date_column() {
		return finish_date_column;
	}

	public void setFinish_date_column(String finishDateColumn) {
		finish_date_column = finishDateColumn;
	}

	public void setModel_name(String modelName) {
		model_name = modelName;
	}

	public String getModel_code() {
		return model_code;
	}

	public void setModel_code(String modelCode) {
		model_code = modelCode;
	}

	public String getBegin_time() {
		return begin_time;
	}

	public void setBegin_time(String beginTime) {
		begin_time = beginTime;
	}

	public String getEnd_time() {
		return end_time;
	}

	public void setEnd_time(String endTime) {
		end_time = endTime;
	}

	public int getSeparate_table_days() {
		return separate_table_days;
	}

	public void setSeparate_table_days(int separateTableDays) {
		separate_table_days = separateTableDays;
	}

	public String getF_model_id() {
		return f_model_id;
	}

	public void setF_model_id(String fModelId) {
		f_model_id = fModelId;
	}

	public String getIndex_ids() {
		return index_ids;
	}

	public void setIndex_ids(String indexIds) {
		index_ids = indexIds;
	}

	public String getModel_defines_time() {
		return model_defines_time;
	}

	public void setModel_defines_time(String modelDefinesTime) {
		model_defines_time = modelDefinesTime;
	}

	public String getModel_defines_person() {
		return model_defines_person;
	}

	public void setModel_defines_person(String modelDefinesPerson) {
		model_defines_person = modelDefinesPerson;
	}

	public int getVersion_control() {
		return version_control;
	}

	public void setVersion_control(int versionControl) {
		version_control = versionControl;
	}

	public int getModel_type() {
		return model_type;
	}

	public void setModel_type(int modelType) {
		model_type = modelType;
	}

	public int getData_from() {
		return data_from;
	}

	public void setData_from(int dataFrom) {
		data_from = dataFrom;
	}

	public int getText_search() {
		return text_search;
	}

	public void setText_search(int textSearch) {
		text_search = textSearch;
	}

	public int getToken_check() {
		return token_check;
	}

	public void setToken_check(int tokenCheck) {
		token_check = tokenCheck;
	}

	public String getAttribute_ids() {
		return attribute_ids;
	}

	public void setAttribute_ids(String attributeIds) {
		attribute_ids = attributeIds;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getS_object_id() {
		return s_object_id;
	}

	public void setS_object_id(String sObjectId) {
		s_object_id = sObjectId;
	}

	public String getConfer_add() {
		return confer_add;
	}

	public void setConfer_add(String conferAdd) {
		confer_add = conferAdd;
	}

	public String getConfer_modify() {
		return confer_modify;
	}

	public void setConfer_modify(String conferModify) {
		confer_modify = conferModify;
	}

	public String getConfer_del() {
		return confer_del;
	}

	public void setConfer_del(String conferDel) {
		confer_del = conferDel;
	}

	public String getConfer_query() {
		return confer_query;
	}

	public void setConfer_query(String conferQuery) {
		confer_query = conferQuery;
	}

	public String getConfer_comment() {
		return confer_comment;
	}

	public void setConfer_comment(String conferComment) {
		confer_comment = conferComment;
	}

	public String getUser_ids() {
		return user_ids;
	}

	public void setUser_ids(String userIds) {
		user_ids = userIds;
	}

	public String getRole_ids() {
		return role_ids;
	}

	public void setRole_ids(String roleIds) {
		role_ids = roleIds;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	/**
	 * 查询可用内容对象树
	 * 
	 * @return
	 *  除已被关联的文档对象外的所有内容对象
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getAllRelContentObjectListAction.action")
	public String getAllRelContentObjectList(String model_code) {
		log.info( "--getAllRelContentObjectList(start)-->model_code:"+model_code );
		String jsonStr = "";

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<TreeBean> objectList = contentObjectManageDao
					.getAllRelContentObjectList();

			TreeBean tree = new TreeBean();
			jsonStr = tree.createJsonStr(objectList);
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "查询可用内容对象树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->查询可用内容对象树失败" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getAllRelContentObjectList(over)");
		return null;
	}
	/**
	 * 查询内容对象树
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getContentObjectListAction.action", method = RequestMethod.POST)
	public String getContentObjectList() {
//		log.info( "--getContentObjectList(start)-->model_code:" + model_code);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = "";
		try {
			List<TreeBean> objectList = contentObjectManageDao.getContentObjectTree();
			jsonStr	= TreeBean.getTreeString(objectList);
//			TreeBean tree = new TreeBean();
//			jsonStr = tree.createJsonStr(objectList);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("msg", jsonStr);

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "查询内容对象树失败!!");
			jsonStr = jsonResp.toString();
			
			// 记录日志
			log.error( "内容模型管理->查询内容对象树失败" + e.toString());
			log.error( e.toString());
		}
//		jsonStr="[{'id':'3','text':'BWJ_DOC_V','leaf':false,'expanded':true,'children':[{'id':'4','text':'BWJ_PART_V','leaf':true}]}]";
		this.outJsonString(jsonResp.toString());
		log.info( "--getContentObjectList(over)");
		return null;
	}
	
	/**
	 * 根据内容对象代码，查询内容对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getContentObjectAction.action", method = RequestMethod.POST)
	public String getContentObject(String model_code) {
		log.info( "--getContentObject(start)-->model_code:" + model_code);
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			ContentObjectInfoBean obj = contentObjectManageDao
					.getContentObjectByCode(model_code);
			if (obj != null) {
				log.debug( "--getContentObject-->obj:"+obj);
				obj.setHasValue(contentObjectManageDao.hasRecode(model_code));
				obj.setHasServerGroupRelate(contentObjectManageDao
						.hasServerGroupRelate(model_code));
				String fatherCode = contentObjectManageDao
						.getFatherContentObj(model_code);
				if (fatherCode != null && !fatherCode.equals("")) {
					obj.setHasFatherRelate(true);
				} else {
					obj.setHasFatherRelate(false);
				}

				AttributeSetManageDaoImp dao = new AttributeSetManageDaoImp();
				Map<String, String> m = dao.getAttrCodeNameMap();
				obj.setCreation_date_column_name(m.get(obj
						.getCreation_date_column()));
				obj.setFinish_date_column_name(m.get(obj
						.getFinish_date_column()));
				jsonStr = new JSONUtil().createJsonDataByBean(obj);
				jsonResp.put("code", 20000);
				jsonResp.put("msg", jsonStr);
				
			} else {
				jsonResp.put("result", "failure");
				jsonResp.put("failure", "true");
				jsonResp.put("message", "没有查询到相关记录");
				jsonStr = jsonResp.toString();
				log.debug( "--getContentObject-->没有查询到相关记录(not find row)-->model_code:" + model_code);
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "根据内容对象代码，查询内容对象失败!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->根据内容对象代码，查询内容对象失败!" + e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getContentObject-->(over)");
		return null;
	}
	/**
	 * 取得当前索引文档下所有的文档对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getSubOjbectListAction.action", method = RequestMethod.POST)
	public String getSubOjbectList(String model_code) {
		log.info( "--getSubOjbectList(start)-->model_code:" + model_code);
		String jsonStr = "";

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<ContentObjectInfoBean> list = contentObjectManageDao
					.getSubObjectList(model_code);
			int size = 0;
			if (list != null && list.size() > 0) {
				size = list.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(list, size,
					new ContentObjectInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "查询索引对象关联的文档对象失败!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->查询索引对象关联的文档对象失败!" + e.toString());
		}

		this.outJsonString(jsonStr);
		log.info( "--getSubOjbectList(over)-->model_code:" + model_code);
		return null;
	}

	/**
	 * 查询该内容对象关联的非系统属性树结构
	 * 
	 * @return由属性代码和名称组成的树
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getRelAttributesTreeAction.action", method = RequestMethod.POST)
	public String getRelAttributesTree(String model_code) {
		log.info( "--getRelAttributesTree(start)-->model_code:" + model_code);
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		
		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<TreeBean> attrList = contentObjectManageDao
					.getRelAttributesTree(model_code);
			attrList = contentObjectManageDao.isIndexAttr(attrList, model_code);
			if (attrList != null && attrList.size() > 0) {
				TreeBean tree = new TreeBean();
				jsonStr = tree.createJsonSelAttrTree(attrList);
			}
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonStr);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "查询内容对象关联的非系统属性树结构失败!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->查询内容对象关联的非系统属性树结构失败!" + e.toString(), e);
		}

		this.outJsonString(jsonResp.toString());
		log.info( "--getRelAttributesTree(over)");
		return null;
	}

	/**
	 * 查询该内容对象未关联的非系统属性树结构
	 * 
	 * @return由属性代码和名称组成的树
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getAllAttributesTreeAction.action", method = RequestMethod.POST)
	public String getAllAttributesTree(String model_code) {
		log.info("--getAllAttributesTree(start)-->model_code:" + model_code);
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<TreeBean> unRelList = contentObjectManageDao.getUnRelAttributesTree(model_code);
			List<TreeBean> relList = contentObjectManageDao.getRelAttributesTree(model_code);
			List<TreeBean> allList = new ArrayList<TreeBean>();
			if (unRelList != null && unRelList.size() > 0) {
				allList.addAll(unRelList);
			}
			if (relList != null && relList.size() > 0) {
				allList.addAll(relList);
			}

			if (allList != null && allList.size() > 0) {
				TreeBean tree = new TreeBean();
				jsonStr = tree.createJsonAllStr(allList);
				jsonResp.put("code", 20000);// TODO mock
				jsonResp.put("msg", jsonStr);
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "查询内容对象未关联的自定义属性树结构失败!");
			// 记录日志
			log.error("内容模型管理->查询内容对象未关联的自定义属性树结构失败!" + e.toString(), e);
		}

		this.outJsonString(jsonResp.toString());
		log.info("--getUnRelAttributesTree(over)-->model_code:" + model_code);
		return null;
	}
	/**
	 * 查询该内容对象未关联的非系统属性树结构
	 * 
	 * @return由属性代码和名称组成的树
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getUnRelAttributesTreeAction.action", method = RequestMethod.POST)
	public String getUnRelAttributesTree(String model_code) {
		log.info( "--getUnRelAttributesTree(start)-->model_code:" + model_code);
		String jsonStr = "";

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<TreeBean> attrList = contentObjectManageDao
					.getUnRelAttributesTree(model_code);
			if (attrList != null && attrList.size() > 0) {
				TreeBean tree = new TreeBean();
				jsonStr = tree.createJsonUnSelAttrTree(attrList);
			}
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "查询内容对象未关联的自定义属性树结构失败!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->查询内容对象未关联的自定义属性树结构失败!" + e.toString(), e);
		}

		this.outJsonString(jsonStr);
		log.info( "--getUnRelAttributesTree(over)-->model_code:" + model_code);
		return null;
	}
	/**
	 * 根据内容对象代码，查询与其相关联的属性列表， 如果show为0 说明不显示系统属性
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getAttributeByobjectAction.action", method = RequestMethod.POST)
	public String getAttributeByobject(String data) {
		JSONObject jsonResp = new JSONObject();
		String jsonStr = "";
		
		try {
			JSONObject modelJson = JSONObject.fromObject(data);
			model_code = modelJson.getString("modelCode").toUpperCase();
			int show_int = 0;
			String show = modelJson.getString("show");
			if (show != null) {
				show_int = Integer.parseInt(show);
			}
			int page_int = 0;
			String page = modelJson.getString("page");
			if (page != null) {
				page_int = Integer.parseInt(page);
			}
			int limit_int = 0;
			String limit = modelJson.getString("limit");
			if (limit != null) {
				limit_int = Integer.parseInt(limit);
			}
			start = (page_int-1) * limit_int;

			List<AttributeInfoBean> attrList = contentObjectManageDao.getAttributeByObject(model_code, show_int, start+1, limit_int);
			List<AttributeInfoBean> allList = contentObjectManageDao.getAttributeByObject(model_code, show_int);
			int size = 0;
			if (allList != null && allList.size() > 0) {
				size = allList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(attrList, size, new AttributeInfoBean());
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonStr);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "查询内容对象关联的属性!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容模型管理->查询内容对象关联的属性失败!" + e.getMessage(), e);
		}

		this.outJsonString(jsonResp.toString());
		log.info("--getAttributeByObject(over)-->model_code:" + model_code);
		return null;
	}

	/**
	 * 查询内容对象的索引信息
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getIndexByObjectAction.action")
	public String getIndexByObject(String model_code) {
		log.info( "getIndexByObject(start)-->model_code:" + model_code);
		String jsonStr = "";
		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<IndexInfoBean> list = contentObjectManageDao
					.getIndexByObject(model_code);
			if (list == null) {
				list = new ArrayList<IndexInfoBean>();
			} else {
				log.debug("--getIndexByObject-->list:" + list);
			}
			int size = 0;
			if (list != null && list.size() > 0) {
				size = list.size();
				for (IndexInfoBean index : list) {
					if (index.getIndex_type() == 2) {
						// 唯一索引，过滤掉系统属性字段
						index.setAttribute_code(index.getAttribute_code()
								.replaceAll(",VERSION,SERVER_ID", ""));
					}
				}
			}
			jsonStr = new JSONUtil().createJsonDataByColl(list, size,
					new AttributeInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "查询内容对象的索引信息!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->查询内容对象的索引信息失败!" + e.toString(), e);
		}

		this.outJsonString(jsonStr);
		log.info( "--getIndexByObject(over)-->model_code:" + model_code);
		return null;
	}

	/**
	 * 校验该内容对象代码是否存在
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/checkObjectCodeAction.action", method = RequestMethod.POST)
	public String checkObjectCode(String value) {
		log.info( "--checkObjectCode(start)-->value:" + value);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			if (value != null) {
				value = value.toUpperCase();
			}
			count = contentObjectManageDao.checkObjectCode(value);
			log.debug( "--checkObjectCode-->value:" + value + ";count" + count);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "内容模型管理->校验该内容对象代码是否存在失败!" + e.toString(), e);
		}
		if (count == 0) {
			jsonResp.put("valid", true);
			jsonResp.put("reason", true);
		} else if (count > 0) {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "内容对象代码已经被使用!!");
		} else {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "检验失败!!");
		}
		jsonResp.put("success", true);
		jsonStr = jsonResp.toString();
		this.outJsonString(jsonStr);
		log.info( "--checkObjectCode(over)");
		return null;
//
	}

	/**
	 * 增加内容对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/updateContentObjectPartAction.action", method = RequestMethod.POST)
	public String updateContentObjectPart(String data) {
		if (StringUtil.stringIsNull(data)) {
			log.error("无效参数");
			return null;
		}
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		// 取得session中的用户信息
		try {
//		HttpSession session = getSession();
//		UserInfoBean userBean = (UserInfoBean) session
//				.getAttribute("loginUser");
//		if (userBean == null) {
//			log.debug( "--addContentObject-->新增内容对象失败!!用户登陆已超时,请重新登陆");
//			jsonResp.put("success", false);
//			jsonResp.put("message", "新增内容对象失败!!用户登陆已超时,请重新登陆!");
//			jsonStr = jsonResp.toString();
//			this.outJsonString(jsonStr);
//			return null;
//		}
			log.info("updateContentObjectPart begin");
			JSONObject modelJson = JSONObject.fromObject(data);
			log.info(data);
			boolean result = contentObjectManageDao.updateContentObjectPart(modelJson.getString("modeCode"),
					(String) modelJson.getOrDefault("partCode", ""), modelJson.getBoolean("add"));

			// 新增成功
			if (result) {
				log.debug("配置文档成功!!");
				jsonResp.put("message", "配置文档成功!!");
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonStr = jsonResp.toString();
			} else {// 新增失败
				jsonResp.put("success", false);
				jsonResp.put("message", "配置文档失败!!");
				jsonStr = jsonResp.toString();
				log.debug("配置文档失败");
			}

		} catch (Exception e) {
			log.error("", e);
			jsonResp.put("success", false);
			jsonResp.put("message", "配置文档失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
		}
		log.info("updateContentObjectPart end");
		this.outJsonString(jsonStr);
		return null;
	}
	/**
	 * 增加内容对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/addContentObjectIndexAction.action", method = RequestMethod.POST)
	public String addContentObjectIndex(String data) {
		if (StringUtil.stringIsNull(data)) {
			log.error("无效参数");
			return null;
		}
		log.info("--addContentObject(start)-->model_code:" + model_code);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		// 取得session中的用户信息
		try {
//		HttpSession session = getSession();
//		UserInfoBean userBean = (UserInfoBean) session
//				.getAttribute("loginUser");
//		if (userBean == null) {
//			log.debug( "--addContentObject-->新增内容对象失败!!用户登陆已超时,请重新登陆");
//			jsonResp.put("success", false);
//			jsonResp.put("message", "新增内容对象失败!!用户登陆已超时,请重新登陆!");
//			jsonStr = jsonResp.toString();
//			this.outJsonString(jsonStr);
//			return null;
//		}
			JSONObject modelJson = JSONObject.fromObject(data);

			IndexInfoBean indexInfoBean = new IndexInfoBean();
			indexInfoBean.setMode_code(modelJson.getString("modelCode"));
			indexInfoBean.setAttribute_code(modelJson.getString("attributeCode"));
			indexInfoBean.setIndex_type(Integer.parseInt(modelJson.getString("index_type")));
			indexInfoBean.setIndex_name(modelJson.getString("index_name"));
			int count = contentObjectManageDao.checkIndexName(modelJson.getString("index_name"));
			boolean result;
			if (count < 1) {
				log.info("begin to create objindex");
				result = contentObjectManageDao.addContentObjectIndex(indexInfoBean);

			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "索引名称已经存在!!");
				this.outJsonString(jsonResp.toString());
				return null;

			}
			// 新增成功
			if (result) {
				jsonResp.put("message", "新增模型索引成功!!");
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);

				jsonStr = jsonResp.toString();
				log.debug("--addContentObject-->新增模型索引成功!!");
			} else {// 新增失败
				jsonResp.put("success", false);
				jsonResp.put("message", "新增模型索引失败!!");
				jsonStr = jsonResp.toString();
				log.debug("--addContentObject-->新增模型索引失败");
			}

		} catch (Exception e) {
			log.error("", e);
			jsonResp.put("success", false);
			jsonResp.put("message", "新增内容模型失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
		}
		log.info("--addContentObject(over)-->model_code:" + model_code);
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 增加内容对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/delContentObjectIndexAction.action", method = RequestMethod.POST)
    public String delContentObjectIndex(String data) {
        JSONObject jsonResp = new JSONObject();
        try {
            JSONObject modelJson = JSONObject.fromObject(data);
            IndexInfoBean bean = new IndexInfoBean();
            bean.setIndex_name(modelJson.getString("index_name"));
            bean.setMode_code(modelJson.getString("mode_code"));
            contentObjectManageDao.delContentObjectIndex(bean);
            jsonResp.put("message", "删除索引成功!!");
            jsonResp.put("success", true);
            jsonResp.put("code", 20000);
        } catch (Exception e) {
            log.error("", e);
            jsonResp.put("success", false);
            jsonResp.put("message", "删除索引失败!!");
        }
        return jsonResp.toString();
    }
	/**
	 * 增加内容对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/addContentObjectAction.action", method = RequestMethod.POST)
	public String addContentObject(String data) {
		log.info("--addContentObject(start)");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelCode = modelJson.getString("model_code").toUpperCase();
		if (contentObjectManageDao.checkObjectCode(modelCode) > 0) {
			jsonResp.put("success", false);
			jsonResp.put("message", "内容对象代码已经被使用!!");
			// 记录日志
			log.error("内容对象代码已经被使用->"+modelCode+"]");
			this.outJsonString(jsonResp.toString());
			return null;
		}

		try {
			UserInfoBean userBean = new UserInfoBean();
			userBean.setLogin_id("vue_temp");
			int model_type = Integer.parseInt(modelJson.getString("model_type"));
			ContentObjectInfoBean contentBean = new ContentObjectInfoBean();
			contentBean.setModel_code(modelJson.getString("model_code").toUpperCase());
			contentBean.setModel_name(URLDecoder.decode(modelJson.getString("modelName"), "utf-8"));
			contentBean.setSeparate_table_days(
					Integer.parseInt((String) modelJson.getOrDefault("separate_table_days", "0")));
			contentBean.setModel_defines_time(DateUtil.getDate());
			contentBean.setModel_type(Integer.parseInt(modelJson.getString("model_type")));
			contentBean.setToken_check(Integer.parseInt(modelJson.getString("token_check")));
			contentBean.setVersion_control(Integer.parseInt(modelJson.getString("version_control")));
			contentBean.setCreation_date_column((String) modelJson.getOrDefault("creation_date_column", ""));
			contentBean.setTable_space((String) modelJson.getOrDefault("table_space", ""));
			contentBean.setFinish_date_column((String) modelJson.getOrDefault("finish_date_column", ""));
			if (model_type == 1) {
				// 文档对象增加加密配置
				String encode_Type = (String) modelJson.getOrDefault("encode_type", "");
				contentBean.setEncode_type(encode_Type);
				if (encode_Type.equals("1")) {
					// 部分加密
					contentBean.setEncodeByteSize((String) modelJson.getOrDefault("encodeByteSize", ""));
				}
			}
			contentBean.setF_model_id("");
			// contentBean.setData_from(1);
			 contentBean.setSeparate_table_num(0);
			 contentBean.setS_object_id((String) modelJson.getOrDefault("doc_part", ""));
			// contentBean.setModel_defines_person(userBean.getLogin_id());

			log.debug("--addContentObject-->contentBean:" + contentBean);
			boolean result = contentObjectManageDao.addContentObject_VUE(contentBean,
					modelJson.getString("attribute_ids")); // attribute_ids
//			 boolean result=true;
			// 新增成功
			if (result) {
				jsonResp.put("message", "新增内容模型成功!!");
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonStr = jsonResp.toString();
				log.debug("--addContentObject-->新增内容模型成功");
			} else {// 新增失败
				jsonResp.put("success", false);
				jsonResp.put("message", "新增内容模型失败!!");
				jsonStr = jsonResp.toString();
				log.debug("--addContentObject-->新增内容模型失败");
			}

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "新增内容模型失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容模型管理->新增内容模型失败!" + e.toString(), e);
		}
		log.info("--addContentObject(over)-->model_code:" + model_code);
		this.outJsonString(jsonStr);
		return null;

	}
	
	/**
	 * 修改内容对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/updateContentObjectAction.action", method = RequestMethod.POST)
	public String updateContentObject(String data) {
		 log.info( "--updateContentObject(start)" );
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		// 取得session中的用户信息
		// HttpSession session = getSession();
		//
		// UserInfoBean userBean = (UserInfoBean) session
		// .getAttribute("loginUser");
		//
		// if (userBean == null) {
		// log.debug( "--updateContentObject-->修改内容对象失败!!用户登陆已超时,请重新登陆!");
		// jsonResp.put("success", false);
		// jsonResp.put("message", "修改内容对象失败!!用户登陆已超时,请重新登陆!");
		// jsonStr = jsonResp.toString();
		// this.outJsonString(jsonStr);
		// return null;
		// }
		try {
			JSONObject modelJson = JSONObject.fromObject(data);
			
			ContentObjectInfoBean contentBean = new ContentObjectInfoBean();
			model_code=modelJson.getString("model_code").toUpperCase();
			contentBean.setModel_code(model_code);
			contentBean.setModel_name(URLDecoder.decode(modelJson.getString("modelName"), "utf-8"));
			contentBean.setSeparate_table_days(Integer.parseInt(modelJson.getString("separate_table_days")));
			contentBean.setToken_check(Integer.parseInt(modelJson.getString("token_check")));
			contentBean.setModel_type(Integer.parseInt(modelJson.getString("model_type")));
			contentBean.setVersion_control(Integer.parseInt(modelJson.getString("version_control")));
			contentBean.setTable_space((String)modelJson.getOrDefault("table_space", ""));
			contentBean.setCreation_date_column((String)modelJson.getOrDefault("creation_date_column", ""));
			contentBean.setFinish_date_column((String)modelJson.getOrDefault("finish_date_column", ""));
			contentBean.setS_object_id((String) modelJson.getOrDefault("doc_part", ""));
//			contentBean.setF_model_id(f_model_id);
//			contentBean.setEncodeByteSize(modelJson.getString("encodeByteSize"));
//			contentBean.setEncode_type(modelJson.getString("encode_type"));
			log.debug("--updateContentObject-->contentBean:" + contentBean);

			boolean result = contentObjectManageDao.updateContentObject_VUE(contentBean, modelJson.getString("attribute_ids"));
			// 新增成功
			if (result) {
				jsonResp.put("message", "修改内容模型成功!!");
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonStr = jsonResp.toString();
				IssueUtils.IssueContentModelInfo(model_code);
			} else {// 新增失败
				jsonResp.put("success", false);
				jsonResp.put("message", "修改内容模型失败!!");
				jsonStr = jsonResp.toString();
				log.debug("--updateContentObject(修改内容模型失败!!)-->model_code:" + model_code);
			}

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "修改内容模型失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容模型管理->修改内容模型失败!" + e.toString(), e);
		}

		this.outJsonString(jsonStr);
		log.info("--updateContentObject(over)-->model_code:" + model_code);
		return null;

	}

	/**
	 * 删除内容对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/delContentObjectAction.action", method = RequestMethod.POST)
	public String delContentObject(String model_code) {
		log.info("--delContentObject-->model_code:" + model_code);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			String msg = null;
			if (contentObjectManageDao.hasRecode(model_code)) {
				msg = "无法删除有内容记录的模型";
			}
			if (contentObjectManageDao.hasServerGroupRelate(model_code)) {
				msg = "无法删除已关联内容存储服务器组的模型";
			}

			boolean result = false;
			List<UnityAccessServerInfoBean> unityServerList = null;
			List<ContentServerInfoBean> contentServerList = null;
			if (StringUtil.stringIsNull(msg)) {
				unityServerList = IssueUtils.getAliveUA();
				contentServerList = IssueUtils.getAliveByModelCodeDM(model_code);
				
				result = contentObjectManageDao.delContentObject(model_code);
			}
			if (result) {
				// 启用下发删除内容模型模板线程
				log.debug("--delContentObject-->删除内容模型成功，下发删除内容模型模板线程(model delete thread start)");
				jsonResp.put("message", "删除内容模型成功!!");
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonStr = jsonResp.toString();
				IssueUtils.IssueInfoToDM(contentServerList);
				IssueUtils.IssueInfoToUA(unityServerList);
			} else {
				log.debug("delContentObject error->"+msg);
				jsonResp.put("success", false);
				jsonResp.put("message", msg);
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "删除内容模型失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("内容模型管理->删除内容模型失败!" + e.toString(), e);
		}
		log.info("--delContentObject(over)");
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 查询未被关联的文档对象，或者该索引对象关联的文档对象
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getDocPartObjectListAction.action", method = RequestMethod.POST)
	public String getDocPartObjectList(String model_code) {
		log.info( "--getDocPartObjectList-->model_code:" + model_code);
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<TreeBean> list = contentObjectManageDao
					.getDocPartObjectList(model_code);
			if (list != null && list.size() > 0) {
				TreeBean tree = new TreeBean();
				jsonStr = tree.createJsonStrCheckBox(list);
			}
			jsonResp.put("msg", jsonStr);
			jsonResp.put("code", 20000);// TODO mock
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "获取内容对象列表失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->查询未被关联的文档对象，或者该索引对象关联的文档对象失败!" + e.toString(), e);
		}

		this.outJsonString(jsonResp.toString());
		log.info( "--getDocPartObjectList(over)-->model_code:" + model_code);
		return null;
	}

	/**
	 * 校验该索引名称是否存在
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/checkIndexNameAction.action", method = RequestMethod.POST)
	public String checkIndexName(String value) {
		log.info( "--checkIndexName-->value:" + value);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			count = contentObjectManageDao.checkIndexName(value);
			log.debug( "--checkIndexName-->value:" + value + ";count:" + count);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "内容模型管理->校验该索引名称是否存在失败!" + e.toString(), e);
		}
		if (count == 0) {
			jsonResp.put("valid", true);
			jsonResp.put("reason", true);
		} else if (count > 0) {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "内容对象代码已经被使用!!");
		} else {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "检验失败!!");
		}
		jsonResp.put("success", true);
		jsonStr = jsonResp.toString();
		this.outJsonString(jsonStr);
		log.info( "--checkIndexName(over)-->value:" + value);
		return null;

	}

	/**
	 * 获取用户操作内容对象的权限
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getUserConferByObjIdAction.action", method = RequestMethod.POST)
	public String getUserConferByObjId(String model_code, int start, int limit) {
		log.info( "--getUserConferByObjId(start)-->model_code:" + model_code);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<UserCModelRelInfoBean> beanList = contentObjectManageDao
					.getUserConferByObjId(model_code, "", start + 1, limit);
			if (beanList.isEmpty()) {
				jsonResp.put("success", false);
				jsonResp.put("message", "该用户暂时未授权");
				jsonStr = jsonResp.toString();
				log.debug( "--getUserConferByObjId-->model_code:" + model_code + ";该用户暂时未授权");
			} else {
				List<UserCModelRelInfoBean> allUserConferList = contentObjectManageDao
						.getUserConferByObjId(model_code, "");
				jsonStr = new JSONUtil().createJsonDataByColl(beanList,
						allUserConferList.size(), new UserCModelRelInfoBean());
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->获取用户操作内容对象的权限失败!" + e.toString());
		}

		this.outJsonString(jsonStr);
		log.info( "--getUserConferByObjId(over)-->model_code:" + model_code );
		return null;
	}

	/**
	 * 设置角色和用户对内容对象的操作权限
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/addConferAction.action", method = RequestMethod.POST)
	public String addConfer(String model_code, String confer_add, String confer_del, String confer_modify,
							String confer_query, String user_ids, String role_ids) {
		log.info( "--addConfer(start)-->model_code:" + model_code);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		// 取得session中的用户信息
		HttpSession session = getSession();

		UserInfoBean auserBean = (UserInfoBean) session
				.getAttribute("loginUser");

		if (auserBean == null) {
			jsonResp.put("success", false);
			jsonResp.put("message", "新增权限失败!!用户登陆已超时,请重新登陆!");
			jsonStr = jsonResp.toString();
			this.outJsonString(jsonStr);
			return null;
		}
		if (model_code != null) {
			model_code = model_code.toUpperCase();
		}
		// 增加，删除，修改，查询，（保留），批注
		StringBuffer permission = new StringBuffer();

		if (confer_add != null && confer_add.equals("true")) {
			permission.append("1");
		} else {
			permission.append("0");
		}
		if (confer_del != null && confer_del.equals("true")) {
			permission.append("1");
		} else {
			permission.append("0");
		}
		if (confer_modify != null && confer_modify.equals("true")) {
			permission.append("1");
		} else {
			permission.append("0");
		}
		if (confer_query != null && confer_query.equals("true")) {
			permission.append("1");
		} else {
			permission.append("0");
		}
		permission.append("1");// 保留地5位不用，设为1
//		if (confer_comment != null && confer_comment.equals("true")) {
//			permission.append("1");
//		} else {
		//批注取消，设为1
			permission.append("1");
//		}
		UserCModelRelInfoBean userBean = new UserCModelRelInfoBean();
		RoleCModelRelInfoBean RoleBean = new RoleCModelRelInfoBean();
		if (user_ids != null && !"".equals(user_ids)) {
			userBean.setModel_code(model_code);
			userBean.setPermission_code(permission.toString());

		} else {
			RoleBean.setModel_code(model_code);
			RoleBean.setPermission_code(permission.toString());
		}

		try {
			boolean result = false;
			if (user_ids != null && !"".equals(user_ids)) {
				result = contentObjectManageDao.addUserConfer(user_ids,
						userBean);
			} else {
				result = contentObjectManageDao.addRoleConfer(role_ids,
						RoleBean);
			}
			log.debug( "--addConfer-->model_code:" + model_code + ";result:" + result);
			// 新增成功
			if (result) {
				jsonResp.put("message", "配置权限成功!!");
				jsonResp.put("success", true);
				jsonStr = jsonResp.toString();
				IssueUtils.IssueContentModelInfo(model_code);
			} else {// 新增失败
				jsonResp.put("success", false);
				jsonResp.put("message", "配置权限失败!!");
				jsonStr = jsonResp.toString();
			}

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置权限失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->配置权限失败!" + e.toString(), e);
		}
		log.info( "--addConfer-->(over)-->model_code:" + model_code);
		this.outJsonString(jsonStr);
		return null;

	}

	/**
	 * 获取角色对内容对象的操作权限
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getRoleConferByObjIdAction.action", method = RequestMethod.POST)
	public String getRoleConferByObjId(String model_code, int start, int limit) {
		log.info( "--getRoleConferByObjId(start)-->model_code:" + model_code);
		String jsonStr = null;
		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<RoleCModelRelInfoBean> beanList = contentObjectManageDao
					.getRoleConferByObjId(model_code, "", start + 1, limit);
			List<RoleCModelRelInfoBean> allConferList = contentObjectManageDao
					.getRoleConferByObjId(model_code, "");
			int size = 0;
			if (allConferList != null && allConferList.size() > 0) {
				size = allConferList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(beanList, size,
					new RoleCModelRelInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->获取角色对内容对象的操作权限失败!" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getRoleConferByObjId(over)");
		return null;
	}

	/**
	 * 获取内容对象名称列表
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getObjectNameAction.action")
	public String getObjectName(String model_code) {
		log.info( "--getObjectName(start)-->model_code:" + model_code);
		String jsonStr = null;

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<TreeBean> objectList = contentObjectManageDao
					.getContentObjectList(model_code);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容对象名称列表");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->获取内容对象名称列表失败!" + e.toString());
		}

		this.outJsonString(jsonStr);
		log.info( "--getObjectName(over)-->model_code:" + model_code);
		return null;
	}

	/**
	 * 分页获取所有的历史表列表
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getHistoryTableAction.action", method = RequestMethod.POST)
	public String getHistoryTable(String data) {
        JSONObject modelJson = JSONObject.fromObject(data);
        String model_code = modelJson.getString("model_code");
        int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		log.info( "--getHistoryTable(start)-->model_code:" + model_code);
		String jsonStr = null;
		start = (page_int-1) * limit_int;

		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			List<ModelTableInfoBean> historyTableList = contentObjectManageDao
					.getModelTableList(model_code, start + 1, limit_int);
			List<ModelTableInfoBean> AllList = contentObjectManageDao
					.getModelTableList(model_code);
			int size = 0;
			if (AllList != null && AllList.size() > 0) {
				size = AllList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(historyTableList,
					size, new ModelTableInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "分页获取所有的历史表列表失败！");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->分页获取所有的历史表列表失败!" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getHistoryTable(over)-->model_code:" + model_code);
		return null;
	}

	/**
	 * 获取内容对象最开始一张历史表的开始时间
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getObjTableEndTimeAction.action", method = RequestMethod.POST)
	public String getObjTableEndTime(String model_code) {
		log.info( "--getObjTableEndTime(start)-->model_code:" + model_code);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		jsonResp.put("code", 20000);//TODO mock
		try {
			if (model_code != null) {
				model_code = model_code.toUpperCase();
			}
			ModelTableInfoBean bean = contentObjectManageDao
					.getObjTableEndTime(model_code);
			if (bean != null) {
				String endDay = "";
				try {
					endDay = DateUtil.getMDrqzhsti8(bean.getBegin_time(), -1);
				} catch (ParseException e) {
					log.error( "内容模型管理->获取结束时间失败!" + e.toString());
				}
				jsonResp.put("success", "true");
				jsonResp.put("tableEndTime", endDay);
			} else {
				jsonResp.put("success", "false");
				jsonResp.put("message", "获取结束时间失败");
			}
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取结束时间失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->获取结束时间失败!" + e.toString(), e);
		}
		log.info( "--getObjTableEndTime(over)-->model_code:" + model_code);
		return jsonStr;
	}

	/**
	 * 增加内容模型历史表
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/addHistoryTableAction.action", method = RequestMethod.POST)
	public String addHistoryTable(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
        String model_code = modelJson.getString("model_code");
        String end_time = modelJson.getString("end_time");
        String begin_time = modelJson.getString("begin_time");
		log.info( "--addHistoryTable(start)-->model_code:" + model_code);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		// 取得session中的用户信息
		HttpSession session = getSession();

		UserInfoBean userBean = (UserInfoBean) session
				.getAttribute("loginUser");

		if (userBean == null) {
			jsonResp.put("success", false);
			jsonResp.put("message", "新增历史表失败!!用户登陆已超时,请重新登陆!");
			jsonStr = jsonResp.toString();
			this.outJsonString(jsonStr);
			return null;
		}
		if (model_code != null) {
			model_code = model_code.toUpperCase();
		}
		ModelTableInfoBean bean = new ModelTableInfoBean();
		bean.setModel_code(model_code);
		bean.setBegin_time(begin_time);
		bean.setEnd_time(end_time);

		try {
			boolean result = contentObjectManageDao.addHistoryTable(bean);
			// 新增成功
			if (result) {
				// 启用新增模型表下发线程
			//	ConsoleThreadPool.getThreadPool().submit(new Thread(new SendModelTableThread(model_code, "1")));
				IssueUtils.IssueContentModelInfo(model_code);
				log.debug( "--addHistoryTable新增分表成功!(add model table thread start)-->model_code:" + model_code);
				jsonResp.put("message", "新增分表成功!!");
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonStr = jsonResp.toString();
			} else {// 新增失败
				jsonResp.put("success", false);
				jsonResp.put("message", "新增分表失败!!");
				jsonStr = jsonResp.toString();
				log.debug( "--addHistoryTable新增分表失败!-->model_code:" + model_code);
			}

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "新增历史表失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->新增历史表失败!" + e.toString(), e);
		}

		this.outJsonString(jsonStr);
		log.info( "--addHistoryTable(over)");
		return null;
	}
	/**
	 * 获取内容对象的业务开始时间和业务结束时间
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getContentObjOffDateAction.action", method = RequestMethod.POST)
	public String getContentObjOffDate(String model_code) {
		log.info( "--getContentObjOffDate(start)-->model_code:" + model_code);
		String jsonStr = null;

		try {
			ContentObjectInfoBean obj = contentObjectManageDao
					.getContentObjOffDate(model_code);
			List<TreeBean> objectList=new ArrayList<TreeBean>();
			
			if(obj!=null){
				TreeBean treeBean=new TreeBean();
				String createDateColumn=obj.getCreation_date_column();
				String finishDateColumn= obj.getFinish_date_column();
				if(createDateColumn==null||createDateColumn.equals("")||createDateColumn.equals("null")||createDateColumn.equals("NULL")){
					//不存在业务开始时间
				}else {
					treeBean=new TreeBean();
					treeBean.setId("CREATION_DATE_COLUMN");
					treeBean.setText_text("业务开始时间");
					objectList.add(treeBean);
				}
				if(finishDateColumn==null||finishDateColumn.equals("")||finishDateColumn.equals("null")||finishDateColumn.equals("NULL")){
					//不存在业务结束时间
				}else {
					treeBean=new TreeBean();
					treeBean.setId("FINISH_DATE_COLUMN");
					treeBean.setText_text("业务结束时间");
					objectList.add(treeBean);
				}
			}
			int size = 0;
			
			if (objectList != null && objectList.size() > 0) {
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容对象的业务开始时间和业务结束时间失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "获取内容对象的业务开始时间和业务结束时间,失败--" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--getSuperContentServerGroup(over)");
		return null;
	}
	
	@Consumes("multipart/form-data")
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/importContentObjAction.action")
	public String importContentObj(@RequestParam MultipartFile file) {
		log.info("fileName[" + file + "]");
		InputStream is=null;
		MultipartHttpServletRequest request = (MultipartHttpServletRequest) getRequest();
		String s=request.getParameter("image");
		try {
			 is = file.getInputStream();
			ContentModelExcelBean excelBean = ExcelUtil.readExecl(is, true);
			Map<String, List<ResponseBean>> mm = contentModelExcelUtil.importExcelBean(excelBean);
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", true);
			jsonResp.put("result", mm);
			getResponse().setContentType("text/html;charset=UTF-8");
			try {
				PrintWriter out = getResponse().getWriter();
				out.write(JSONObject.fromObject(jsonResp).toString());
				out.flush();
			} catch (IOException e) {
				log.error("error");
			}
			
		} catch (Exception e) {
			log.error("", e);
		} finally {
			if (is != null) {
				try {
					is.close();
				} catch (IOException e) {
					log.error("关闭文件流失败",e);
				}
			}
		}
		return null;
	}
	
	/**
	 * 获取用户已有权限
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/exportContentObjAction.action")
	public String exportContentObj(String model_code) {
		log.info("要导出的模型code[" + model_code + "]");
		if (StringUtil.stringIsNull(model_code)) {
			return null;
		}
		List<String> modelCodeList = new ArrayList<String>();
		String[] modelCodes = model_code.split(",");
		Collections.addAll(modelCodeList, modelCodes);
		try {
			ContentModelExcelBean excelBean = contentModelExcelUtil.exportExcelBean(modelCodeList);
			log.info("开始 将bean写入到excel中");
			InputStream fis = ExcelUtil.writeExcel(excelBean);  
			log.info("结束 将bean写入到excel中,准备输出文件到浏览器");
			byte[] buffer = new byte[fis.available()];  
			 fis.read(buffer);  
             fis.close(); 
			this.outExcelString(buffer, "SunECMExport_" + DateUtil.getMDrqzhsti14());
			log.info("结束 输出文件到浏览器");
		} catch (IOException e) {
			log.error("导出模型出错[" + model_code + "]", e);
		}

		return null;
	}
	
	/**
	 * 导出hbase建表SQL，model_code格式 索引对象（对象之间是逗号）-文档对象（对象直接是逗号）,例如 index1,index2-doc1,doc2，其中index1和index2为索引对象，doc1和doc2为文档对象
	 *s
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/exportHBASESQLAction.action", method = RequestMethod.POST)
	public String exportHBASESQL(String model_code) {
		log.info("导出hbase建表SQL,code[" + model_code + "]");
		if (StringUtil.stringIsNull(model_code)) {
			return null;
		}
		String[] modelCodes = model_code.split("-");
		String indexids="";
		String docids="";
		if (modelCodes == null || modelCodes.length == 0) {
			return null;
		} else if (modelCodes.length == 1) {
			indexids = modelCodes[0];
		} else if (modelCodes.length == 2) {
			indexids = modelCodes[0];
			docids = modelCodes[1];
		}
		StringBuffer sb=new StringBuffer();
		String[] indexid=indexids.split(",");
		for(int i=0;i<indexid.length;i++){
			sb.append("create \"").append(indexid[i]).append("\",\"BATCH\",\"BATCHFILE\";");
			sb.append("\r\n");
			sb.append("create \"").append(indexid[i]).append("_SECONDARY_INDEX\",\"SEARCH\";");
			sb.append("\r\n");
			
		}
		String [] docid=docids.split(",");
		for(int i=0;i<docid.length;i++){
			sb.append("create \"").append(docid[i]).append("\",\"INFO\",{NAME => \"FILE\", IS_MOB => true, MOB_THRESHOLD => 102400};");
			sb.append("\r\n");
		}
		log.info("准备写到流中"+sb.toString());
		try {
			this.outTxtString(sb.toString());
			log.info("结束 输出文件到浏览器");
		} catch (Exception e) {
			log.error("导出模型出错[" + model_code + "]", e);
		}

		return null;
	}
	
	/**
	 * 获取分表模式(0为默认自动分表，1为手动分表)
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/getSeparateTabTypeAction.action", method = RequestMethod.POST)
	public String getSeparateTabType() {
		log.info( "--getSeparateTabType(start)");
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		int separateTabType=ReadConfig.getConsoleConfigBean().getSeparate_table_type();
		log.info("--getSeparateTabType-->separateTabType:" + separateTabType);
		
		jsonResp.put("success", true);
		jsonResp.put("separateTabType", separateTabType);
		jsonStr = jsonResp.toString();
		this.outJsonString(jsonStr);
		
		log.info( "--getSeparateTabType(over)");
		return null;
	}
	
	/**
	 * 手动续建分表
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentModelManage/addSepTableAction.action", method = RequestMethod.POST)
	public String addSepTable(String model_code,int newsep_table_days,int newsep_table_num){
		log.info( "--addSepTable(start)-->model_code:" + model_code);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		// 取得session中的用户信息
		HttpSession session = getSession();
		UserInfoBean userBean = (UserInfoBean) session
				.getAttribute("loginUser");
		if (userBean == null) {
			log.debug( "--addContentObject-->手动续建分表失败!!用户登陆已超时,请重新登陆");
			jsonResp.put("success", false);
			jsonResp.put("message", "手动续建分表失败!!用户登陆已超时,请重新登陆!");
			jsonStr = jsonResp.toString();
			this.outJsonString(jsonStr);
			return null;
		}

		ContentObjectInfoBean contentObj = new ContentObjectInfoBean();
		if (model_code ==null) {
			log.error("模型传入为空:" + model_code + "]");
			jsonResp.put("success", false);
			jsonResp.put("message", "手动续建分表失败!!");
			jsonStr = jsonResp.toString();
			this.outJsonString(jsonStr);
			return null;
		}
		List <ContentObjectInfoBean> contentObjList = contentObjUtil
				.getContentObjectList(model_code.toUpperCase(Locale.ENGLISH));
		
		if (contentObjList == null || contentObjList.size() == 0) {
			// 未查到模型模版信息
			log.error("没有查到该模型的模版信息:" + model_code + "]");
			jsonResp.put("success", false);
			jsonResp.put("message", "手动续建分表失败!!");
			jsonStr = jsonResp.toString();
		} else {
			contentObj = contentObjList.get(0);
			if(contentObj.getSeparate_table_days() == 0){//不分表
				jsonResp.put("success", false);
				jsonResp.put("message", "分表周期为0，不可续建分表!!");
				jsonStr = jsonResp.toString();
				log.debug( "--addSepTable-->分表周期为0，不可续建分表");
			}else{
				contentObj.setNewsep_table_days(newsep_table_days);
				contentObj.setNewsep_table_num(newsep_table_num);
				log.debug( "--addSepTable-->contentObj:"+contentObj);
				try {
					 String result = contentObjUtil.createObjectTable(
							contentObj, false);
					// 新增成功
					if (result != null) {
						contentObj.setSeparate_table_days(newsep_table_days);
						contentObj.setNewsep_table_days(0);
						jsonResp.put("message", "手动续建分表成功!!");
						jsonResp.put("success", true);
						jsonStr = jsonResp.toString();
						log.debug( "--addSepTable-->手动续建分表成功");
					}else{
						// 新增失败
						jsonResp.put("success", false);
						jsonResp.put("message", "手动续建分表失败!!");
						jsonStr = jsonResp.toString();
						log.debug( "--addSepTable-->手动续建分表失败");
					}
				} catch (Exception e) {
					jsonResp.put("success", false);
					jsonResp.put("message", "手动续建分表失败!!");
					jsonStr = jsonResp.toString();
					// 记录日志
					log.error( "内容模型管理->手动续建分表失败!" + e.toString(), e);
				}
			}
		}
		log.info( "--addSepTable(over)-->model_code:" + model_code);
		this.outJsonString(jsonStr);
		return null;		
	}
}
