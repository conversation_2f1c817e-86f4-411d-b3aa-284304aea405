package com.sunyard.console.common.util;

import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;


public class RequestUtil {
	/**
	 * 新增权限、查看权限、删除权限、修改权限、打印权限、批注权限、管理员权限
	 * @param rights
	 * @return
	 */
	public static Map<String,String> getPermissions(String rights){
		if(StringUtils.isNotBlank(rights)){
			Map<String,String> map = new HashMap<String,String>(rights.length());
			for (int i = 0; i < rights.length(); i++) {
				map.put(Constant.SUN_PERMISSION_RIGHT_INDEX+(i+1), String.valueOf(rights.charAt(i)));
			}
			return map;
		}
		return null;
	}

}
	
