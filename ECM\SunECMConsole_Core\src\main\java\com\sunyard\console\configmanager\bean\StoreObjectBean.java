package com.sunyard.console.configmanager.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

//存储对象信息

/**
 * <p>
 * Title:xstream 存储对象信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("StoreObjectBean")
public class StoreObjectBean {
	@XStreamAsAttribute
	private String VOLUME_ID;//卷编号VOLUME_ID
	@XStreamAsAttribute
	private String VOLUME_NAME; // 卷名称VOLUME_NAME
	@XStreamAsAttribute
	private String SERVER_GROUP_ID;//所属服务器组编号SERVER_GROUP_ID
	@XStreamAsAttribute
	private String ROOT_PATH;// 存储标识ROOT_PATH
	@XStreamAsAttribute
	private String VOLUME_REMARK;//备注VOLUME_REMARK
	@XStreamAsAttribute
	private String VOLUME_STATUS;	// 卷状态VOLUME_STATUS
	
	@XStreamAsAttribute
	private String SAVE_PATH;// 存储路径SAVE_PATH
	
	@XStreamAsAttribute
	private String PATH_RULE;// 目录规则PATH_RULE
	
	@XStreamAsAttribute
	private String DIR_NUMBER;// 最大目录数DIR_NUMBER
	
	@XStreamAsAttribute
	private String PATH_NUMBER;// 目录层数PATH_NUMBER
	@XStreamAsAttribute
	private String OS;//操作系统
	public String getVolume_id() {
		return VOLUME_ID;
	}
	public void setVolume_id(String volumeId) {
		VOLUME_ID = volumeId;
	}
	public String getVolume_name() {
		return VOLUME_NAME;
	}
	public void setVolume_name(String volumeName) {
		VOLUME_NAME = volumeName;
	}
	public String getServer_group_id() {
		return SERVER_GROUP_ID;
	}
	public void setServer_group_id(String serverGroupId) {
		SERVER_GROUP_ID = serverGroupId;
	}
	public String getRoot_path() {
		return ROOT_PATH;
	}
	public void setRoot_path(String rootPath) {
		ROOT_PATH = rootPath;
	}
	public String getVolume_remark() {
		return VOLUME_REMARK;
	}
	public void setVolume_remark(String volumeRemark) {
		VOLUME_REMARK = volumeRemark;
	}
	public String getVolume_status() {
		return VOLUME_STATUS;
	}
	public void setVolume_status(String volumeStatus) {
		VOLUME_STATUS = volumeStatus;
	}
	public String getSave_path() {
		return SAVE_PATH;
	}
	public void setSave_path(String savePath) {
		SAVE_PATH = savePath;
	}
	public String getPath_rule() {
		return PATH_RULE;
	}
	public void setPath_rule(String pathRule) {
		PATH_RULE = pathRule;
	}
	public String getDir_number() {
		return DIR_NUMBER;
	}
	public void setDir_number(String dirNumber) {
		DIR_NUMBER = dirNumber;
	}
	public String getPath_number() {
		return PATH_NUMBER;
	}
	public void setPath_number(String pathNumber) {
		PATH_NUMBER = pathNumber;
	}
	public String getOS() {
		return OS;
	}
	public void setOS(String oS) {
		OS = oS;
	}
	@Override
	public String toString() {
		return "StoreObjectBean [OS=" + OS + ", DIR_NUMBER=" + DIR_NUMBER
				+ ", PATH_NUMBER=" + PATH_NUMBER + ", PATH_RULE=" + PATH_RULE
				+ ", ROOT_PATH=" + ROOT_PATH + ", SAVE_PATH=" + SAVE_PATH
				+ ", SERVER_GROUP_ID=" + SERVER_GROUP_ID + ", VOLUME_ID="
				+ VOLUME_ID + ", VOLUME_NAME=" + VOLUME_NAME
				+ ", VOLUME_REMARK=" + VOLUME_REMARK + ", VOLUME_STATUS="
				+ VOLUME_STATUS + "]";
	}
	
}
