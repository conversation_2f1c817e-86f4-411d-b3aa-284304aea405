package com.sunyard.console.safemanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONObject;

import com.sunyard.console.safemanage.bean.NewTokenInfoBean;
import com.sunyard.console.safemanage.bean.StateTokenBean;
import com.sunyard.console.safemanage.bean.TokenInfoBean;
import com.sunyard.console.safemanage.dao.TokenManageDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


/**
 * <p>Title: 令牌管理Action</p>
 * <p>Description: 处理令牌信息管理中的跳转</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class TokenManageAction extends BaseAction {
	@Autowired
	TokenManageDAO tmdao ;
	
	private String ip;			//申请令牌机器ip
	private String server_info;	//备注
	private int start;
	private int limit;
	private String ip_s;		//申请令牌机器ip集合
	private final static  Logger log = LoggerFactory.getLogger(TokenManageAction.class);
	private String value;
	
	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getIp_s() {
		return ip_s;
	}

	public void setIp_s(String ip_s) {
		this.ip_s = ip_s;
	}

	public TokenManageDAO getTmdao() {
		return tmdao;
	}

	public void setTmdao(TokenManageDAO tmdao) {
		this.tmdao = tmdao;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public String getServer_info() {
		return server_info;
	}

	public void setServer_info(String server_info) {
		this.server_info = server_info;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}
	
	/**
	 * 查询机器列表
	 * @return
	 */
	@RequestMapping(value = "/safeManage/getServerListAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String getServerList(int start, int limit) {
		log.info( "--getServerList(start)");
		String jsonStr  = null;
		try{
			List<TokenInfoBean> tokenInfoList = tmdao.getServerList(start+1 , limit);
			List<TokenInfoBean> tokenInfoAllList = tmdao.getAllServerList();
			
			jsonStr = new JSONUtil().createJsonDataByColl(tokenInfoList,tokenInfoAllList.size(),new TokenInfoBean());
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取机器信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "可申请动态令牌机器管理->查询机器列表失败->"+e.toString());
			log.error( "Exception:",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getServerList(over)");
		return null;
	}
	
	/**
	 * 增加机器
	 * @return
	 */
	@RequestMapping(value = "/safeManage/addServerAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String addServer(String ip, String server_info) {
		log.info( "--addServer(start)-->ip:"+ip+";server_info:"+server_info);
		TokenInfoBean tib		= new TokenInfoBean();
		String		 jsonStr	= null;
		JSONObject	 jsonResp	= new JSONObject();
		
		tib.setIp(ip);
		tib.setServer_info(server_info);
		
		try{
			if(tmdao.checkServerIp(ip) == 0){
				tmdao.addServer(tib);
				jsonResp.put("success", true);
				jsonResp.put("message", "增加机器成功!!");
			}else{
				jsonResp.put("success", true);
				jsonResp.put("message", "机器已经在列表中!!");
			}
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "增加机器失败!!");
			jsonStr = jsonResp.toString();
			log.error( "可申请动态令牌机器管理->新增机器列表失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--addServer(over)");
		return null;
	}
	
	/**
	 * 删除机器
	 * @return
	 */
	@RequestMapping(value = "/safeManage/deleteServerAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String deleteServer(String ip_s) {
		log.info( "--deleteServer(start)-->ip_s:"+ip_s);
		JSONObject	 jsonResp	= new JSONObject();
		String		 jsonStr	= null;
		try{
			tmdao.deleteServer(ip_s);
			jsonResp.put("success", true);
			jsonResp.put("message", "删除机器成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--deleteServer-->删除机器成功！");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "删除机器失败!!");
			jsonStr = jsonResp.toString();
			log.error( "可申请动态令牌机器管理->删除机器列表失败->"+e.toString());
			log.error( e.toString());
		}
		
		this.outJsonString(jsonStr);
		log.info( "--deleteServer(over)-->ip_s:"+ip_s);
		return null;
	}
	/**
	 * 查询机器列表
	 * @return
	 */
	@RequestMapping(value = "/safeManage/getTokenListAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String getTokenList(int start, int limit) {
		log.info( "--getTokenList(start)");
		String jsonStr  = null;
		try{
			List<StateTokenBean> tokenInfoList = tmdao.getTokenList(start+1 , limit);
			List<StateTokenBean> tokenInfoAllList = tmdao.getAllTokenList();
			jsonStr = new JSONUtil().createJsonDataByColl(tokenInfoList,tokenInfoAllList.size(),new StateTokenBean());
			log.debug( "--getTokenList-->查询机器列表成功！");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取静态令牌列表失败!!");
			jsonStr = jsonResp.toString();
			log.error( "静态令牌管理->查询静态列表列表失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--getTokenList(over)");
		return null;
	}
	/**
	 * 申请静态令牌
	 * @return
	 */
	@RequestMapping(value = "/safeManage/applicationTokenAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String applicationToken(String ip) {
		log.info( "--applicationToken(start)-->ip:"+ip);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try{
			tmdao.applicationToken(ip);
			jsonResp.put("success", true);
			jsonResp.put("message", "申请令牌成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--applicationToken-->申请令牌成功!!");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "申请令牌失败!!");
			jsonStr = jsonResp.toString();
			log.error( "令牌管理-->申请令牌失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--applicationToken(over)-->ip:"+ip);
		return null;
	}
	/**
	 * 删除静态令牌
	 * @return
	 */
	@RequestMapping(value = "/safeManage/delTokenAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String delToken(String ip) {
		log.info( "--delToken(start)-->ip:"+ip);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try{
			tmdao.delToken(ip);
			jsonResp.put("success", true);
			jsonResp.put("message", "删除令牌成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--delToken-->删除令牌成功!!");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "删除令牌失败!!");
			jsonStr = jsonResp.toString();
			log.error( "令牌管理-->删除令牌失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--delToken(over)-->ip:"+ip);
		return null;
	}
	/**
	 * ip唯一校验
	 * @return
	 */
	@RequestMapping(value = "/safeManage/checkBindingIpAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String checkBindIp(String value) {
		log.info( "--checkBindIp(start)-->value:"+value);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			if(value!=null){
				value=value.toLowerCase();
			}
			count = tmdao.checkBindIp(value);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "令牌管理->校验该IP是否存在失败!" + e.toString());
			log.error( e.toString());
		}
		if (count == 0) {
			jsonResp.put("valid", true);
			jsonResp.put("reason", true);
		} else if (count > 0) {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "IP已经被使用!!");
		} else {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "检验失败!!");
		}
		jsonResp.put("success", true);
		jsonStr = jsonResp.toString();
		this.outJsonString(jsonStr);
		log.info( "--checkBindIp(over)");
		return null;

	}
	
	/**
	 * 查询令牌列表
	 * @return
	 */
	@RequestMapping(value = "/safeManage/getTokenListVueAction.action")
	@ResponseBody
	public String getTokenListVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		log.info( "--getTokenListVue(start)");
		String jsonStr  = null;
		start = (page_int-1) * limit_int;
		try{
			List<NewTokenInfoBean> tokenInfoList = tmdao.getTokenListVue(start , limit_int);
			List<NewTokenInfoBean> tokenInfoAllList = tmdao.getAllTokenListVue();
			jsonStr = new JSONUtil().createJsonDataByColl(tokenInfoList,tokenInfoAllList.size(),new NewTokenInfoBean());
			log.debug( "--getTokenList-->查询令牌列表成功！");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取令牌列表失败!!");
			jsonStr = jsonResp.toString();
			log.error( "静态令牌管理->查询令牌列表失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--getTokenListVue(over)");
		return null;
	}
	
	
	/**
	 * 申请令牌
	 * @return
	 */
	@RequestMapping(value = "/safeManage/addTokenVueAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String addTokenVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String ip = modelJson.getString("ip");
		String isTrend = modelJson.getString("isTrend");
		String server_info = modelJson.getString("server_info");
		server_info = "undefined".equals(server_info) || server_info == null ? "" : server_info;
		log.info( "--addTokenVue(start)-->ip:"+ip+",isTrend:"+isTrend+",server_info:"+server_info);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try {
			server_info = URLDecoder.decode(server_info, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode tokenManage fields error, server_info=" + server_info,e1);
		}
		try{
			if(isTrend.equals("1")){//动态
				TokenInfoBean tib		= new TokenInfoBean();
				tib.setIp(ip);
				tib.setServer_info(server_info);
				if(tmdao.checkServerIp(ip) == 0){
					tmdao.addServer(tib);
					jsonResp.put("success", true);
					jsonResp.put("code", 20000);
					jsonResp.put("message", "增加机器成功!!");
				}else{
					jsonResp.put("success", true);
					jsonResp.put("code", 20000);
					jsonResp.put("message", "机器已经在列表中!!");
				}
			}else if(isTrend.equals("0")){//静态
				tmdao.applicationTokenVue(ip,server_info);
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("message", "申请静态令牌成功!!");
			}
			jsonStr = jsonResp.toString();
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "申请令牌失败!!");
			jsonStr = jsonResp.toString();
			log.error( "令牌管理-->申请令牌失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--addTokenVue(over)-->ip:"+ip+",isTrend:"+isTrend+",server_info:"+server_info);
		return null;
	}
	
	/**
	 * 删除机器
	 * @return
	 */
	@RequestMapping(value = "/safeManage/deleteServerVueAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String deleteServerVue(String ip_s) {
		log.info( "--deleteServer(start)-->ip_s:"+ip_s);
		JSONObject	 jsonResp	= new JSONObject();
		String		 jsonStr	= null;
		try{
			tmdao.deleteServer(ip_s);
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "删除机器成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--deleteServer-->删除机器成功！");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "删除机器失败!!");
			jsonStr = jsonResp.toString();
			log.error( "可申请动态令牌机器管理->删除机器列表失败->"+e.toString());
			log.error( e.toString());
		}
		
		this.outJsonString(jsonStr);
		log.info( "--deleteServer(over)-->ip_s:"+ip_s);
		return null;
	}
	
	/**
	 * 删除静态令牌
	 * @return
	 */
	@RequestMapping(value = "/safeManage/delTokenVueAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String delTokenVue(String ip) {
		log.info( "--delToken(start)-->ip:"+ip);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try{
			tmdao.delToken(ip);
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "删除令牌成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--delToken-->删除令牌成功!!");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "删除令牌失败!!");
			jsonStr = jsonResp.toString();
			log.error( "令牌管理-->删除令牌失败->"+e.toString());
			log.error( e.toString());
		}
		this.outJsonString(jsonStr);
		log.info( "--delToken(over)-->ip:"+ip);
		return null;
	}
}
