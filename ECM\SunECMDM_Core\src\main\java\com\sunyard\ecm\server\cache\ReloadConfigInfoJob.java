package com.sunyard.ecm.server.cache;

import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ReloadConfigInfoJob implements Job {

	/**
	 * 日志对象
	 */
	private final static  Logger log = LoggerFactory.getLogger(ReloadConfigInfoJob.class);

	public void execute(JobExecutionContext arg0) throws JobExecutionException {
		log.info("begin to ReloadConfigInfoJob");
		try {
			LazySingleton.getInstance().resetConfigInfo();
		} catch (Exception e) {
			log.error("dm begin to ReloadConfigInfoJob", e);
		}

	}

}
