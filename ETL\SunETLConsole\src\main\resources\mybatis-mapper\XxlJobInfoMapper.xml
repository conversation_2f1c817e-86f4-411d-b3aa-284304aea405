<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobInfoDao">
	
	<resultMap id="XxlJobInfo" type="com.xxl.job.admin.core.model.XxlJobInfo" >
		<result column="id" property="id" />
		<result column="job_group" property="jobGroup" />
	    <result column="job_cron" property="jobCron" />
	    <result column="job_desc" property="jobDesc" />
	    
	    <result column="add_time" property="addTime" />
	    <result column="update_time" property="updateTime" />
	    
	    <result column="author" property="author" />
	    <result column="alarm_email" property="alarmEmail" />

		<result column="executor_route_strategy" property="executorRouteStrategy" />
		<result column="executor_handler" property="executorHandler" />
	    <result column="executor_param" property="executorParam" />
		<result column="executor_block_strategy" property="executorBlockStrategy" />
		<result column="executor_fail_strategy" property="executorFailStrategy" />
	    <result column="glue_type" property="glueType" />
	    <result column="write_type" property="writeType" />
	    <result column="glue_source" property="glueSource" />
	    <result column="glue_remark" property="glueRemark" />
		<result column="glue_updatetime" property="glueUpdatetime" />
		<result column="JOB_START" property="jobStatus" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.job_group,
		t.job_cron,
		t.job_desc,
		t.add_time,
		t.update_time,
		t.author,
		t.alarm_email,
		t.executor_route_strategy,
		t.executor_handler,
		t.executor_param,
		t.executor_block_strategy,
		t.executor_fail_strategy,
		t.write_type,
		t.glue_type,
		t.glue_source,
		t.glue_remark,
		t.glue_updatetime,
		t.job_Start
	</sql>
	
	<select id="pageList" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_INFO t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobGroup gt 0">
				AND t.job_group = #{jobGroup}
			</if>
			<if test="executorHandler != null and executorHandler != ''">
				AND t.executor_handler like CONCAT(CONCAT('%', #{executorHandler}), '%')
			</if>
		</trim>
		ORDER BY id
	</select>

	<select id="ParentJobList" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_JOB_RELATION A,QRTZ_TRIGGER_INFO T
		WHERE A.JOB_ID = T.ID
		AND A.CHILDREN_JOB_ID = #{childrenJobId}
	</select>


	<select id="getAllJobInfos" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_INFO t
		ORDER BY id
	</select>



	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM QRTZ_TRIGGER_INFO t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobGroup gt 0">
				AND t.job_group = #{jobGroup}
			</if>
			<if test="executorHandler != null and executorHandler != ''">
				AND t.executor_handler like CONCAT(CONCAT('%', #{executorHandler}), '%')
			</if>
		</trim>
	</select>

	<insert id="save" parameterType="com.xxl.job.admin.core.model.XxlJobInfo" useGeneratedKeys="false" keyProperty="id" >
		INSERT INTO QRTZ_TRIGGER_INFO (
			id,
			job_group,
			job_cron,
			job_desc,
			add_time,
			update_time,
			author,
			alarm_email,
			write_type,
            executor_route_strategy,
			executor_handler,
			executor_param,
			executor_block_strategy,
			executor_fail_strategy,
			glue_type,
			glue_source,
			glue_remark,
			glue_updatetime
		) VALUES (
			#{id,jdbcType=INTEGER},
			#{jobGroup,jdbcType=INTEGER},
			#{jobCron,jdbcType=VARCHAR}, 
			#{jobDesc,jdbcType=VARCHAR},
			sysdate,
			sysdate,
			#{author,jdbcType=VARCHAR},
			#{alarmEmail,jdbcType=VARCHAR},
			#{writeType,jdbcType=VARCHAR},
			#{executorRouteStrategy,jdbcType=VARCHAR},
			#{executorHandler,jdbcType=VARCHAR},
			#{executorParam,jdbcType=VARCHAR},
			#{executorBlockStrategy,jdbcType=VARCHAR},
			#{executorFailStrategy,jdbcType=VARCHAR},
			#{glueType,jdbcType=VARCHAR},
			#{glueSource},
			#{glueRemark,jdbcType=VARCHAR},
			sysdate
		)
		<!--<selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="id">
			SELECT LAST_INSERT_ID()
			/*SELECT @@IDENTITY AS id*/
		</selectKey>-->
	</insert>

	<select id="loadById" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_INFO t
		WHERE t.id = #{id}
		order by id
	</select>


	<select id="getReactiveJobInfos" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_INFO t
		WHERE t.write_type = #{jobType}
		order by id
	</select>

	
	<update id="update" parameterType="com.xxl.job.admin.core.model.XxlJobInfo" >
		UPDATE QRTZ_TRIGGER_INFO
		SET
		<if test="jobCron != null and jobCron != ''">
		job_cron = #{jobCron,jdbcType=VARCHAR},
		</if>
			job_desc = #{jobDesc,jdbcType=VARCHAR},
			update_time = sysdate,
			author = #{author,jdbcType=VARCHAR},
			write_type = #{writeType,jdbcType=VARCHAR},
			alarm_email = #{alarmEmail,jdbcType=VARCHAR},
			executor_route_strategy = #{executorRouteStrategy,jdbcType=VARCHAR},
		<if test="executorHandler != null and executorHandler != ''">
			executor_handler = #{executorHandler,jdbcType=VARCHAR},
		</if>
			executor_param = #{executorParam,jdbcType=VARCHAR},
			executor_block_strategy = #{executorBlockStrategy,jdbcType=VARCHAR},
			executor_fail_strategy = #{executorFailStrategy,jdbcType=VARCHAR},
			glue_source = #{glueSource,jdbcType=VARCHAR},
			glue_remark = #{glueRemark,jdbcType=VARCHAR},
			glue_updatetime = #{glueUpdatetime}
		WHERE id = #{id,jdbcType=INTEGER}
	</update>
	<!--更新为正在执行,集群方式所以只在调度时候这么判断是否已经被其他机器执行-->
	<update id="updateJobSatrtING" parameterType="java.util.HashMap" keyProperty="id">
		UPDATE QRTZ_TRIGGER_INFO
		SET
		JOB_START = #{startCode,jdbcType=VARCHAR}
		WHERE id = #{id,jdbcType=INTEGER}
		AND JOB_START != 3
	</update>
	<update id="updateJobSatrt" parameterType="java.util.HashMap" keyProperty="id">
		UPDATE QRTZ_TRIGGER_INFO
		SET
		JOB_START = #{startCode,jdbcType=VARCHAR}
		WHERE id = #{id,jdbcType=INTEGER}
	</update>



	<update id="updateAllJobSatrt">
		UPDATE QRTZ_TRIGGER_INFO
		SET
		JOB_START = 1
		WHERE WRITE_TYPE = 'REACTIVE' AND EXISTS (SELECT 1 FROM QRTZ_TRIGGER_INFO WHERE EXECUTOR_HANDLER ='EndTask' AND JOB_START = '2'  AND WRITE_TYPE = 'REACTIVE')
	</update>
	<update id="updateAllErrorJobSatrt">
		UPDATE QRTZ_TRIGGER_INFO
		SET
		JOB_START = 1
		WHERE WRITE_TYPE = 'REACTIVE'
		AND JOB_START != 2
	</update>



	<delete id="delete" parameterType="java.util.HashMap">
		DELETE
		FROM QRTZ_TRIGGER_INFO
		WHERE id = #{id}
	</delete>

	<select id="getJobsByGroup" parameterType="java.util.HashMap" resultMap="XxlJobInfo">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_INFO t
		WHERE t.job_group = #{jobGroup}
		order by id
	</select>

	<select id="findAllCount" resultType="Integer">
		SELECT count(1)
		FROM QRTZ_TRIGGER_INFO
	</select>
	
	<select id="getMaxId" resultType="Integer">
		SELECT  nvl(MAX(id),0) 
		FROM QRTZ_TRIGGER_INFO
	</select>

</mapper>