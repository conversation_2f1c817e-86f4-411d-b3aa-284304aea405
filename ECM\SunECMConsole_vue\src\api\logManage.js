import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getLogRuleList(data) {
  const url = '/logManage/getLogRuleListAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}
export function checkSavePath(data) {
  const url = '/logManage/checkSavePathAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: {
      data: data
    }
  })
}

export function configLogRule(data) {
  const url = '/logManage/configLogRuleAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: {
      data: data
    }
  })
}

