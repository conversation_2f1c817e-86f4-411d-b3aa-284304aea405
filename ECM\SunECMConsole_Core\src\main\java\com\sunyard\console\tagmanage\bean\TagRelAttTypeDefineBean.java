package com.sunyard.console.tagmanage.bean;

/**
 * <p>
 * Title: ES属性类型定义信息bean
 * </p>
 * <p>
 * Description: ES属性类型定义信息
 * </p>
 * <p>
 * Copyright: Copyright (c) 2022
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class TagRelAttTypeDefineBean {
	private String type_id; // 类型ID
	private String type_name; // 类型名
	public String getType_id() {
		return type_id;
	}
	public void setType_id(String type_id) {
		this.type_id = type_id;
	}
	public String getType_name() {
		return type_name;
	}
	public void setType_name(String type_name) {
		this.type_name = type_name;
	}

}
