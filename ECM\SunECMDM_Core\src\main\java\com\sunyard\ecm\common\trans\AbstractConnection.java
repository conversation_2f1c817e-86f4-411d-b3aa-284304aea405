package com.sunyard.ecm.common.trans;

import com.sunyard.common.Configuration;
import com.sunyard.ecm.server.CheckLogin;
import com.sunyard.ecm.server.annotation.MessageOption;
import com.sunyard.ecm.server.bean.BatchBean;
import com.sunyard.ecm.server.bean.BatchFileBean;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.cache.LazySingleton;
import com.sunyard.ecm.server.context.Context;
import com.sunyard.ecm.server.context.ContextUtil;
import com.sunyard.ecm.server.event.EventManageCenter;
import com.sunyard.ecm.server.event.ExceptionEvent;
import com.sunyard.ecm.server.service.DmMessageServer;
import com.sunyard.ecm.server.service.IMessageServer;
import com.sunyard.ecm.server.util.GenerateMsg;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.msgqueue.MsgPut;
import com.sunyard.msgqueue.msgreport.SocketNumRecord;
import com.sunyard.msgqueue.msgreport.SocketStartRecord;
import com.sunyard.util.*;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 抽象的连接实现，完成了主要逻辑。
 * <p>
 * 此类需要{@link FileServer}和一个{@link Object}处理上传文件和处理一般请求。
 * <p>
 *
 * <AUTHOR>
 *
 */
public abstract class AbstractConnection implements Connection {
	final static Logger log = LoggerFactory.getLogger(AbstractConnection.class);

	private Map<String, Method> methodMap = new HashMap<String, Method>();
	private Map<String, String> argsMap = new HashMap<String, String>();
	private static TokenRelUserUtil tokenRelUserUtil;
	protected String clientIp;
	protected boolean alive = true;
	private FileServer fileServer;

	private IMessageServer service;
	private String conntentId = "";

	public AbstractConnection() throws IOException {
		fileServer = (FileServer) SpringUtil.getSpringBean("fileServer");
		service = (IMessageServer) SpringUtil.getSpringBean("server");
		tokenRelUserUtil = (TokenRelUserUtil) SpringUtil
				.getSpringBean("tokenRelUserUtil");
	}

	public final void process() {
		tokenRelUserUtil.removeThreadTokenBean();
		String processName = Thread.currentThread().getName();
		Context context = ContextUtil.getContext();
		context.setClientIp(clientIp);
		context.setConnectionType(getConnectionType());
		boolean excep = false;
		String writeReport = Configuration.get("writeReport", "false");
		if("true".equals(writeReport)){
			recordSocketTime(processName);
		}
		try {
			if (!enable()) {
				return;
			}
			conntentId = "";
			while (alive) {
				String receive = null;
				String result = null;
				try {
					receive = getMessages();
					if (!isLeagalMessage(receive)) {
						log.warn("收到无效的报文：[{}]。跳过执行，接收下一个报文。", receive);
						if (receive == null) {
							alive = false;
						}
						continue;
					}
					log.info("receiveMsg:" + receive);
					if (receive == null) {
						continue;
					}
					result = process(receive.trim());
					sendMessages(TransOptionKey.SERVER_OK
							+ TransOptionKey.SPLITSYM + result);
					needAlive();
				} catch (Exception e1) {

					context.setOptionStatus("0");

					excep = true;
					log.error("后台处理请求发生错误:", e1);
					String lastStepName = context.getLastTaskName();
					context.step("发生异常:{0}", e1.getMessage());
					context.stopTask();
					log.error("发生异常时上下文:{}", context.getDetail());
					alive = false;
					if (conntentId == null) {
						conntentId = "";
					}
					service.handleException(e1, context.getOption(),
							conntentId, null);

					EventManageCenter eventManageCenter = (EventManageCenter) SpringUtil
							.getSpringBean("eventManageCenter");
					ExceptionEvent event = new ExceptionEvent();
					event.setException(e1);
					event.setModeCode(context.getModelCode());
					event.setContentId(context.getContentId());
					event.setOption(context.getOption());
					eventManageCenter.fire(event);

					Throwable rootCause = ExceptionUtils.getRootCause(e1);
					String errorMessage = rootCause == null ? e1.toString()
							: rootCause.toString();
					// 如果可以去到当前执行步骤，则在返回的错误信息中加上此信息
					if (StringUtils.isNotBlank(lastStepName)) {
						errorMessage += TransOptionKey.SPLITSYM + "执行步骤:"
								+ lastStepName;
					}
					try {
						if (e1 instanceof SunECMException) {
							int code = ((SunECMException) e1).getCode();
							String c = String.valueOf(code);
							this.sendErrorMessages(code, c
									+ TransOptionKey.SPLITSYM + errorMessage);
						} else {
							String c = String
									.valueOf(SunECMExceptionStatus.SERVER_EXCEPTION);
							this.sendErrorMessages(700, c
									+ TransOptionKey.SPLITSYM + errorMessage);
						}
					} catch (Exception e4) {
						log.error("send error msg  to client get exception", e4);
					}
				}

			}
		} finally {
			Thread.currentThread().setName(processName);

			if (log.isDebugEnabled() && !excep) {
				log.debug("Context：{}", context.getDetail());
			}

			//是否开启统计信息
			try {
				if("true".equals(writeReport)){
					putMsg(context);
					String modelCode = context.getModelCode();
					if(StringUtils.isNotEmpty(modelCode)){
						String nodeId = LazySingleton.getInstance().load.getNodeInfoBean().getServer_id();
						SocketNumRecord.decrementSocketNum(nodeId,modelCode);
					}
				}
			} catch (Exception e){
				log.error("pug Msg error",e);
			}

			ContextUtil.remove();
			close();
			if("true".equals(writeReport)){
				delSocketTime(processName);
			}
			tokenRelUserUtil.removeThreadTokenBean();
		}
	}
	//http上传：process改造，多文件上传用同一个连接
	public final void httpUploadProcess() {
		tokenRelUserUtil.removeThreadTokenBean();
		String processName = Thread.currentThread().getName();
		Context context = ContextUtil.getContext();
		context.setClientIp(clientIp);
		context.setConnectionType(getConnectionType());
		boolean excep = false;
		String writeReport = Configuration.get("writeReport", "false");
		if("true".equals(writeReport)){
			recordSocketTime(processName);
		}
		try {
			if (!enable()) {
				return;
			}
			conntentId = "";
			while (alive) {
				String receive = null;
				try {
					receive = getHttpMessages();
					if (!isLeagalMessage(receive)) {
						log.warn("收到无效的报文：[{}]。跳过执行，接收下一个报文。", receive);
						if (receive == null) {
							alive = false;
						}
						continue;
					}
					log.info("receiveMsg:" + receive);
					if (receive == null) {
						continue;
					}
					String contentId = process(receive.trim());//DmMessageServer返回批次号，不返回客户端，继续处理。
					//模拟文件报文参数0003
					String[] content = receive.trim().split(TransOptionKey.SPLITSYM);
					Map<String, String> map=MapUtil.parseMap(content[content.length - 1], null);
					String  batchBeanXML=map.get("XML");
					BatchBean batchBean =tokenRelUserUtil.reqXmlInfo2BatchBean(batchBeanXML);
					batchBean.setCLIENT_IP(this.clientIp);
					//
					//
					List<BatchFileBean> batchFileBeans=batchBean.getDocument_Objects();
					for (BatchFileBean batchFileBean : batchFileBeans) {
						List<FileBean> fileBeans=batchFileBean.getFiles();
						for (FileBean fileBean : fileBeans) {
							Map<String, String> map2=new HashMap<String, String>();
							map2.put("FILEPATH", fileBean.getFileName());
							map2.put("FILESIZE", fileBean.getRealFilesize());
							map2.put("FORMAT", fileBean.getFileFormat());
							map2.put("CONTENTID", contentId);
							String result2 = httpReceiveFileprocess(contentId,map2);//接收文件
						}
					}
					//组装END报文
					String receive2="0002<<::>>OPTION=UPLOAD,START=END,CONTENTID=";
					String DMSNAME=",DMSNAME="+batchBean.getDmsName();
					String XML_END =receive2+contentId+DMSNAME;
//					System.out.println("end报文："+XML_END);
					String SendENDXML= process(XML_END.trim());//http改造后最后返回报文
					sendMessages(TransOptionKey.SERVER_OK
							+ TransOptionKey.SPLITSYM + SendENDXML);
					needAlive();
				} catch (Exception e1) {

					context.setOptionStatus("0");

					excep = true;
					log.error("后台处理请求发生错误:", e1);
					String lastStepName = context.getLastTaskName();
					context.step("发生异常:{0}", e1.getMessage());
					context.stopTask();
					log.error("发生异常时上下文:{}", context.getDetail());
					alive = false;
					if (conntentId == null) {
						conntentId = "";
					}
					service.handleException(e1, context.getOption(),
							conntentId, null);

					EventManageCenter eventManageCenter = (EventManageCenter) SpringUtil
							.getSpringBean("eventManageCenter");
					ExceptionEvent event = new ExceptionEvent();
					event.setException(e1);
					event.setModeCode(context.getModelCode());
					event.setContentId(context.getContentId());
					event.setOption(context.getOption());
					eventManageCenter.fire(event);

					Throwable rootCause = ExceptionUtils.getRootCause(e1);
					String errorMessage = rootCause == null ? e1.toString()
							: rootCause.toString();
					// 如果可以去到当前执行步骤，则在返回的错误信息中加上此信息
					if (StringUtils.isNotBlank(lastStepName)) {
						errorMessage += TransOptionKey.SPLITSYM + "执行步骤:"
								+ lastStepName;
					}
					try {
						if (e1 instanceof SunECMException) {
							int code = ((SunECMException) e1).getCode();
							String c = String.valueOf(code);
							this.sendErrorMessages(code, c
									+ TransOptionKey.SPLITSYM + errorMessage);
						} else {
							String c = String
									.valueOf(SunECMExceptionStatus.SERVER_EXCEPTION);
							this.sendErrorMessages(700, c
									+ TransOptionKey.SPLITSYM + errorMessage);
						}
					} catch (Exception e4) {
						log.error("send error msg  to client get exception", e4);
					}
				}

			}
		} finally {
			Thread.currentThread().setName(processName);

			if (log.isDebugEnabled() && !excep) {
				log.debug("Context：{}", context.getDetail());
			}

			//是否开启统计信息
			try {
				if("true".equals(writeReport)){
					putMsg(context);
					String modelCode = context.getModelCode();
					if(StringUtils.isNotEmpty(modelCode)){
						String nodeId = LazySingleton.getInstance().load.getNodeInfoBean().getServer_id();
						SocketNumRecord.decrementSocketNum(nodeId,modelCode);
					}
				}
			} catch (Exception e){
				log.error("pug Msg error",e);
			}

			ContextUtil.remove();
			close();
			if("true".equals(writeReport)){
				delSocketTime(processName);
			}
			tokenRelUserUtil.removeThreadTokenBean();
		}
	}

	public final void httpUpdateProcess() {
		tokenRelUserUtil.removeThreadTokenBean();
		String processName = Thread.currentThread().getName();
		Context context = ContextUtil.getContext();
		context.setClientIp(clientIp);
		context.setConnectionType(getConnectionType());
		boolean excep = false;
		String writeReport = Configuration.get("writeReport", "false");
		if("true".equals(writeReport)){
			recordSocketTime(processName);
		}
		try {
			if (!enable()) {
				return;
			}
			conntentId = "";
			while (alive) {
				String receive = null;
				String result = null;
				try {
					receive = getHttpMessages();
//					System.out.println("更新报文："+receive);
					if (!isLeagalMessage(receive)) {
						log.warn("收到无效的报文：[{}]。跳过执行，接收下一个报文。", receive);
						if (receive == null) {
							alive = false;
						}
						continue;
					}
					log.info("receiveMsg:" + receive);
					if (receive == null) {
						continue;
					}
					String contentId = process(receive.trim());//DmMessageServer返回批次号，不返回客户端，继续处理。
//					System.out.println("更新返回批次号 :"+contentId);
					//模拟文件报文参数
					String[] content = receive.trim().split(TransOptionKey.SPLITSYM);
					Map<String, String> map=MapUtil.parseMap(content[content.length - 1], null);
					String  batchBeanXML=map.get("XML");
					BatchBean batchBean =tokenRelUserUtil.reqXmlInfo2BatchBean(batchBeanXML);
					batchBean.setCLIENT_IP(this.clientIp);
					List<BatchFileBean> batchFileBeans=batchBean.getDocument_Objects();
					for (BatchFileBean batchFileBean : batchFileBeans) {
						List<FileBean> fileBeans=batchFileBean.getFiles();
						for (FileBean fileBean : fileBeans) {
							if (fileBean.getFileName()!=null&&fileBean.getFileName().length()!=0) {
								Map<String, String> map2=new HashMap<String, String>();
								map2.put("FILEPATH", fileBean.getFileName());
								map2.put("FILESIZE", fileBean.getRealFilesize());
								map2.put("FORMAT", fileBean.getFileFormat());
								map2.put("CONTENTID", contentId);
								String result2 = httpReceiveFileprocess(contentId,map2);//接收文件
							}
						}
					}
					//组装END报文
					String receive2="0002<<::>>OPTION=UPDATE,START=END,CONTENTID=";
					String DMSNAME=",DMSNAME="+batchBean.getDmsName();
					String XML_END =receive2+contentId+DMSNAME;
//					System.out.println("end报文："+XML_END);
					String SendENDXML= process(XML_END.trim());//http改造后最后返回报文
//					System.out.println(SendENDXML);
					sendMessages(TransOptionKey.SERVER_OK
							+ TransOptionKey.SPLITSYM + SendENDXML);
					needAlive();
				} catch (Exception e1) {

					context.setOptionStatus("0");

					excep = true;
					log.error("后台处理请求发生错误:", e1);
					String lastStepName = context.getLastTaskName();
					context.step("发生异常:{0}", e1.getMessage());
					context.stopTask();
					log.error("发生异常时上下文:{}", context.getDetail());
					alive = false;
					if (conntentId == null) {
						conntentId = "";
					}
					service.handleException(e1, context.getOption(),
							conntentId, null);

					EventManageCenter eventManageCenter = (EventManageCenter) SpringUtil
							.getSpringBean("eventManageCenter");
					ExceptionEvent event = new ExceptionEvent();
					event.setException(e1);
					event.setModeCode(context.getModelCode());
					event.setContentId(context.getContentId());
					event.setOption(context.getOption());
					eventManageCenter.fire(event);

					Throwable rootCause = ExceptionUtils.getRootCause(e1);
					String errorMessage = rootCause == null ? e1.toString()
							: rootCause.toString();
					// 如果可以去到当前执行步骤，则在返回的错误信息中加上此信息
					if (StringUtils.isNotBlank(lastStepName)) {
						errorMessage += TransOptionKey.SPLITSYM + "执行步骤:"
								+ lastStepName;
					}
					try {
						if (e1 instanceof SunECMException) {
							int code = ((SunECMException) e1).getCode();
							String c = String.valueOf(code);
							this.sendErrorMessages(code, c
									+ TransOptionKey.SPLITSYM + errorMessage);
						} else {
							String c = String
									.valueOf(SunECMExceptionStatus.SERVER_EXCEPTION);
							this.sendErrorMessages(700, c
									+ TransOptionKey.SPLITSYM + errorMessage);
						}
					} catch (Exception e4) {
						log.error("send error msg  to client get exception", e4);
					}
				}

			}
		} finally {
			Thread.currentThread().setName(processName);

			if (log.isDebugEnabled() && !excep) {
				log.debug("Context：{}", context.getDetail());
			}

			//是否开启统计信息
			try {
				if("true".equals(writeReport)){
					putMsg(context);
					String modelCode = context.getModelCode();
					if(StringUtils.isNotEmpty(modelCode)){
						String nodeId = LazySingleton.getInstance().load.getNodeInfoBean().getServer_id();
						SocketNumRecord.decrementSocketNum(nodeId,modelCode);
					}
				}
			} catch (Exception e){
				log.error("pug Msg error",e);
			}

			ContextUtil.remove();
			close();
			if("true".equals(writeReport)){
				delSocketTime(processName);
			}
			tokenRelUserUtil.removeThreadTokenBean();
		}
	}

	private void recordSocketTime(String processName){
		try {
			String nodeId = LazySingleton.getInstance().load.getNodeInfoBean().getServer_id();
			String time = DateUtil.get14bitDateStr();
			SocketStartRecord.addSocketStartRecord(nodeId, processName, time);
		} catch (Exception e){
			log.error("socket startTime add error", e);
		}
	}

	private void delSocketTime(String processName){
		try {
			String nodeId = LazySingleton.getInstance().load.getNodeInfoBean().getServer_id();
			SocketStartRecord.removeSocketStartRecord(nodeId, processName);
		} catch (Exception e){
			log.error("socket startTime remove error", e);
		}
	}

	private void putMsg(Context context) {
		String option = context.getOption();
		String modelCode = context.getModelCode();
		String optionStatus = context.getOptionStatus();

		if (StringUtils.isEmpty(modelCode)) {
			return;
		}

		long takeTime = context.getTotalTime();

		String date = new SimpleDateFormat("yyyyMMddHHmmssSSS").format(new Date());

		String nodeId = LazySingleton.getInstance().load.getNodeInfoBean()
				.getServer_id();
		if ("QUERY".equals(option)) {
			String msg = GenerateMsg.getOptMsg(modelCode, nodeId, date, option,
					optionStatus, takeTime);
			new MsgPut(msg).putMsg();
		} else if ("HEIGHT_QUERY".equals(option)) {
			String msg = GenerateMsg.getOptMsg(modelCode, nodeId, date,
					"HEIGQUERY", optionStatus, takeTime);
			new MsgPut(msg).putMsg();
		} else if ("UPLOAD".equals(option) || "UPDATE".equals(option)) {
			long sumFileSize = 0L;
			int sumFile = 0;
			if(service instanceof DmMessageServer){
				 sumFileSize = context.getSumFileSize();
				 sumFile = context.getSumFile();
			}
			String msg = GenerateMsg.getUploadUpdateMsg(modelCode, nodeId,
					date, option, optionStatus, sumFile, sumFileSize, takeTime);
			new MsgPut(msg).putMsg();
		} else if ("MIGRATE".equals(option)){
			if("1".equals(optionStatus)){
				String msg = GenerateMsg.getOptMsg(modelCode, nodeId, date, "MIGRATESERVER",
						optionStatus, takeTime);
				new MsgPut(msg).putMsg();
			}

		}
	}

	protected final String processMessage(Map<String, String> map)
			throws SunECMException {
		tokenRelUserUtil.tokenMessage2NormalMessage(map);
		CheckLogin.Login(map);
		Class<?> protocol = service.getClass();
		Method method = null;
		String params = null;
		String methodName = "";
		Object[] args = null;
		Method[] m = protocol.getMethods();
		//String option = map.get("OPTION");
		String option = map.get("OPTION").toUpperCase();  // 统一转为大写
		Context context = ContextUtil.getContext();
		if("LOGIN".equals(option)){
			context.setModelCode("OTHER");
		}
		context.setOption(option);
		method = methodMap.get(option);
		params = argsMap.get(option);
		if (method == null) {
			for (int i = 0; i < m.length; i++) {
				Method temp = m[i];
				methodName = temp.getName();
				if (methodName.equals("main")) {
					continue;
				}
				MessageOption messageOption = temp
						.getAnnotation(MessageOption.class);
				if (messageOption != null
						&& messageOption.optionName().equals(option)) {
					method = temp;
					params = messageOption.paraNames();
					methodMap.put(option, method);
					argsMap.put(option, params);
					break;
				}
			}
		}

		if (!StringUtils.isEmpty(params)) {
			String[] ps = params.split(",");
			args = new String[ps.length];
			for (int j = 0; j < ps.length; j++) {
				args[j] = map.get(ps[j]);
			}
		} else {
			args = null;
		}
		if (method == null) {
			throw new SunECMException(SunECMExceptionStatus.UNSUPPORT_OPRATION,
					"不支持此操作:" + option);
		}

		Object value = null;
		try {
			value = method.invoke(service, args);
		} catch (InvocationTargetException e) {
			Throwable target = e.getTargetException();
			if (target instanceof SunECMException) {
				throw (SunECMException) target;
			} else {
				SunECMException sune = new SunECMException(
						SunECMExceptionStatus.SERVER_EXCEPTION, e);
				sune.setStackTrace(target.getStackTrace());
				throw sune;
			}
		} catch (Exception e) {
			SunECMException sune = new SunECMException(
					SunECMExceptionStatus.SERVER_EXCEPTION, e);
			throw sune;
		}
		if ("TRUE".equals(map.get("ISCOMPRESS"))
				&& OptionKey.QUERY.equals(option)) {
			return compressResultString((String) value);
		}

		return (String) value;
	}

	protected final String process(String receive) throws SunECMException {
		String[] content = receive.split(TransOptionKey.SPLITSYM);
		Map<String, String> map = getInputParam(content);
		TokenUtil.addClientIp(map, this.getClientIp());
		String option = getOption(content);
		conntentId = map.get("CONTENTID");
		log.debug("option = " + option + ", contentid= " + conntentId);
		String result;
		if (option.equals(TransOptionKey.MESSAGE_PROCESS)) {
			return processMessage(map);
		}
		if (option.equals(TransOptionKey.FILE_RECIEVE)) { // 接收文件

			try {
				result = fileServer.prepareRecieveFile(map, this);
			} catch (Exception e) {
				Throwable rootCause = ExceptionUtils.getRootCause(e);
				String errorMessage = rootCause == null ? e.toString()
						: rootCause.toString();
				throw new SunECMException(
						SunECMExceptionStatus.RECEVIE_FILE_FAIL, errorMessage,
						e);
			}
		} else if (option.equals(TransOptionKey.MIGRATE_FILE_RECIEVE)) {
			try {
				this.sendMessages(TransOptionKey.SERVER_OK);
			} catch (IOException e) {
				throw new SunECMException(
						SunECMExceptionStatus.RECEVIE_FILE_FAIL,
						"failed to send message.", e);
			}
			result = fileServer.migratePrepareRecieveFile(map, this);
		} else if (option.equals(TransOptionKey.DISCONNECT_PROCESS)) {
			alive = false;
			result = SunECMExceptionStatus.SC_OK + "";
		} else {
			log.error("无效的消息报文类型:{}", option);
			throw new SunECMException("Illeagal trans option: " + option);
		}
		return result;
	}
    //http改造，多文件用同一个连接,这里其实只走接收文件逻辑
	protected final String httpReceiveFileprocess(String receive,Map<String, String> map) throws SunECMException {
		String[] content = receive.split(TransOptionKey.SPLITSYM);
		//Map<String, String> map = httpGetInputParam(content);不通过来原来的逻辑获取文件参数，方法上层以入参map获取相关参数
		TokenUtil.addClientIp(map, this.getClientIp());
		//String option = getOption(content);//原来的option仍未0002
		String option =TransOptionKey.FILE_RECIEVE ;//设option为0003，接收文件逻辑
		conntentId = map.get("CONTENTID");
		log.debug("option = " + option + ", contentid= " + conntentId);
		String result;
		if (option.equals(TransOptionKey.MESSAGE_PROCESS)) {
			return processMessage(map);
		}
		if (option.equals(TransOptionKey.FILE_RECIEVE)) { // 接收文件
			try {
				result = fileServer.httpPrepareRecieveFile(map, this);
			} catch (Exception e) {
				Throwable rootCause = ExceptionUtils.getRootCause(e);
				String errorMessage = rootCause == null ? e.toString()
						: rootCause.toString();
				throw new SunECMException(
						SunECMExceptionStatus.RECEVIE_FILE_FAIL, errorMessage,
						e);
			}
		} else if (option.equals(TransOptionKey.MIGRATE_FILE_RECIEVE)) {
			try {
				this.sendMessages(TransOptionKey.SERVER_OK);
			} catch (IOException e) {
				throw new SunECMException(
						SunECMExceptionStatus.RECEVIE_FILE_FAIL,
						"failed to send message.", e);
			}
			result = fileServer.migratePrepareRecieveFile(map, this);
		} else if (option.equals(TransOptionKey.DISCONNECT_PROCESS)) {
			alive = false;
			result = SunECMExceptionStatus.SC_OK + "";
		} else {
			log.error("无效的消息报文类型:{}", option);
			throw new SunECMException("Illeagal trans option: " + option);
		}
		return result;
	}
	/**
	 * 获得此次请求的类型。
	 *
	 * @param content
	 * @return
	 */
	protected abstract String getOption(String[] content);

	/**
	 * 获得此次请求的入参
	 *
	 * @param content
	 * @return
	 */
	protected abstract Map<String, String> getInputParam(String[] content);

	/**
	 * 判断接收到的信息是否合法，目前只判断了是否为空。
	 * <p>
	 * 子类可以根据需求进行覆盖。
	 *
	 * @param receive
	 * @return
	 */
	protected boolean isLeagalMessage(String receive) {
		if (receive == null || receive.length() == 0
				|| receive.trim().length() == 0) {
			return false;
		}
		return true;
	}

	/**
	 * 在处理完一次请求后（该次请求未产生异常）是否需要保持该链接存活
	 * <p>
	 * socket是可以继续，http无法继续
	 */
	public abstract void needAlive();

	public final String getClientIp() {
		return this.clientIp;
	}

	private String compressResultString(String result) throws SunECMException {
		try {
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			ZipOutputStream zipOutputStream = new ZipOutputStream(out);
			ZipEntry zipEntry = new ZipEntry(UUID.randomUUID().toString()
					.toUpperCase()
					+ ".xml");
			zipOutputStream.putNextEntry(zipEntry);
			ByteArrayInputStream in = new ByteArrayInputStream(
					result.getBytes());
			IOUtils.copy(in, zipOutputStream);
			zipOutputStream.close();
			in.close();
			out.close();
			result = new String(Base64.encodeBase64(out.toByteArray()), "UTF-8");
			return result.replace("\r", "").replace("\n", "");
		} catch (Exception e) {
			throw new SunECMException(SunECMExceptionStatus.URLENCODE_FAIL, e);
		}
	}

	/**
	 * 判断服务器是否启用
	 *
	 * @return
	 */
	private boolean enable() {
		String state = LazySingleton.getInstance().load.getNodeInfoBean()
				.getState();
		try {
			if (state == null) {
				log.error("服务未获取到配置信息，请检查是否配置统一接入(通过SunECMConsole配置)!");
				sendMessages(String
						.valueOf(SunECMExceptionStatus.CONSOLE_CONFIG_FAIL));
				return false;
			} else if (state.equals("0")) {
				log.warn("服务已暂停(通过SunECMConsole启用)!");
				sendMessages(String.valueOf(SunECMExceptionStatus.NODE_IS_STOP));
				return false;
			}
		} catch (IOException e) {
			log.error("服务未启用状态发送给客户端失败:", e);
		}
		return true;
	}
	public abstract  Map<String , MultipartFile> getHttpFiles() ;
}
