2025-07-11 09:13:34.136 [OrganNo_00023_UserNo_admin] [728b014afebd326f/daa7c26cc8fb1a39] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-11 09:13:34.461 [OrganNo_00023_UserNo_admin] [728b014afebd326f/10882ee23ec09e0c] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-11 09:13:34.462 [OrganNo_00023_UserNo_admin] [728b014afebd326f/10882ee23ec09e0c] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-11 09:13:34.470 [OrganNo_00023_UserNo_admin] [728b014afebd326f/10882ee23ec09e0c] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-11 09:13:34.472 [OrganNo_00023_UserNo_admin] [728b014afebd326f/10882ee23ec09e0c] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-11 09:13:34.472 [OrganNo_00023_UserNo_admin] [728b014afebd326f/10882ee23ec09e0c] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-11 09:13:34.481 [OrganNo_00023_UserNo_admin] [728b014afebd326f/10882ee23ec09e0c] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-11 09:13:34.504 [OrganNo_00023_UserNo_admin] [728b014afebd326f/daa7c26cc8fb1a39] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-11 09:13:34.585 [OrganNo_00023_UserNo_admin] [b7ed40454b35b43d/840debe3b3811f42] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-07-11 09:13:34.625 [OrganNo_00023_UserNo_admin] [b7ed40454b35b43d/82f898e66d3fde3c] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-07-11 09:13:34.626 [OrganNo_00023_UserNo_admin] [b7ed40454b35b43d/82f898e66d3fde3c] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String)
2025-07-11 09:13:34.638 [OrganNo_00023_UserNo_admin] [b7ed40454b35b43d/82f898e66d3fde3c] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-07-11 09:13:34.640 [OrganNo_00023_UserNo_admin] [b7ed40454b35b43d/82f898e66d3fde3c] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-07-11 09:13:34.642 [OrganNo_00023_UserNo_admin] [b7ed40454b35b43d/82f898e66d3fde3c] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 15(Integer)
2025-07-11 09:13:34.670 [OrganNo_00023_UserNo_admin] [b7ed40454b35b43d/82f898e66d3fde3c] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 11
2025-07-11 09:13:34.698 [OrganNo_00023_UserNo_admin] [b7ed40454b35b43d/840debe3b3811f42] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250703113315953115",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250703113315953115",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"7b53c149f1554632aa28e6b39de3c054",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250703",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"熊涛",
				"tellerNo":"7567209",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704154204216347",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704154204216347",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"1b58ac266ca547d39bb835c9fda6c9f8",
				"isDel":"0",
				"oganName":"15089-中国银行德阳罗江支行",
				"oganNo":"15089",
				"registerDate":"20250704",
				"siteName":"中国银行德阳罗江支行营业部",
				"siteNo":"15442",
				"tellerName":"唐甜",
				"tellerNo":"1713854",
				"userName":"唐甜",
				"userNo":"1713854",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704155047593474",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704155047593474",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"b6c986d1289d4f68b9494c90cc6bc057",
				"isDel":"0",
				"oganName":"14913-中国银行成都高新技术产业开发区支行",
				"oganNo":"14913",
				"registerDate":"20250704",
				"siteName":"中国银行成都复城广场支行",
				"siteNo":"35944",
				"tellerName":"张诗琪",
				"tellerNo":"8767041",
				"userName":"张诗琪",
				"userNo":"8767041",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704152937239660",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025070400023004",
				"batchId":"20250704152937239660",
				"businessDate":"20240910",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640910",
				"endBusiDate":"20240910",
				"id":"faecdbe2bb4e4627bbd995430f9d8b6e",
				"isDel":"0",
				"oganName":"14901-中国银行成都蜀都大道支行",
				"oganNo":"14901",
				"registerDate":"20250704",
				"siteName":"中国银行成都实业街支行",
				"siteNo":"14902",
				"tellerName":"童心",
				"tellerNo":"1488420",
				"userName":"童心",
				"userNo":"1488420",
				"warrantAmount":"3"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025061600023002",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025061600023001",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"46e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"15370-中国银行四川省分行支付清算部",
				"oganNo":"15370",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行支付清算部",
				"siteNo":"15370",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250616140816157403",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250616140816157403",
				"businessDate":"20250616",
				"codeName":"保存40年 (默认)",
				"codeNo":"PZ01",
				"destroyDate":"20650616",
				"endBusiDate":"20250626",
				"id":"4ea6430cec6f44749695b61ecf80e3cd",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250616",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"23"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250626150955945916",
				"applicationState":"FM_STATE_APP_13",
				"baleNo":"2025062600023001",
				"batchId":"20250626150955945916",
				"businessDate":"20250626",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650626",
				"endBusiDate":"20250626",
				"id":"3cbbc289402245118f5451c1e418610e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250626",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250627144548724684",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250627144548724684",
				"businessDate":"20250627",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650627",
				"endBusiDate":"20250627",
				"id":"ea2aa69f275744d8b54a5d9a9e203956",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250627",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250702171217509324",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250702171217509324",
				"businessDate":"20250702",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650702",
				"endBusiDate":"20250702",
				"id":"0ec8cc2c9c5242f9b11244fd6a46a95a",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250702",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"2"
			}
		],
		"totalCount":11,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-11 10:20:54.728 [OrganNo_00023_UserNo_admin] [7b78d39272bdcfe4/1579ec7a708fe9ed] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-11 10:20:54.769 [OrganNo_00023_UserNo_admin] [7b78d39272bdcfe4/81d3cbfd767147d7] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-11 10:20:54.770 [OrganNo_00023_UserNo_admin] [7b78d39272bdcfe4/81d3cbfd767147d7] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-11 10:20:54.773 [OrganNo_00023_UserNo_admin] [7b78d39272bdcfe4/81d3cbfd767147d7] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-11 10:20:54.774 [OrganNo_00023_UserNo_admin] [7b78d39272bdcfe4/81d3cbfd767147d7] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-11 10:20:54.774 [OrganNo_00023_UserNo_admin] [7b78d39272bdcfe4/81d3cbfd767147d7] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-11 10:20:54.776 [OrganNo_00023_UserNo_admin] [7b78d39272bdcfe4/81d3cbfd767147d7] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-11 10:20:54.793 [OrganNo_00023_UserNo_admin] [7b78d39272bdcfe4/1579ec7a708fe9ed] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-11 10:20:54.841 [OrganNo_00023_UserNo_admin] [d5aa949ea74a813c/ed08810132b3d973] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-07-11 10:20:54.855 [OrganNo_00023_UserNo_admin] [d5aa949ea74a813c/0bdd98dc79486380] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE IN ('23', '24')
2025-07-11 10:20:54.856 [OrganNo_00023_UserNo_admin] [d5aa949ea74a813c/0bdd98dc79486380] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String)
2025-07-11 10:20:54.861 [OrganNo_00023_UserNo_admin] [d5aa949ea74a813c/0bdd98dc79486380] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-07-11 10:20:54.862 [OrganNo_00023_UserNo_admin] [d5aa949ea74a813c/0bdd98dc79486380] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-07-11 10:20:54.862 [OrganNo_00023_UserNo_admin] [d5aa949ea74a813c/0bdd98dc79486380] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 15(Integer)
2025-07-11 10:20:54.872 [OrganNo_00023_UserNo_admin] [d5aa949ea74a813c/0bdd98dc79486380] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 11
2025-07-11 10:20:54.888 [OrganNo_00023_UserNo_admin] [d5aa949ea74a813c/ed08810132b3d973] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250703113315953115",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250703113315953115",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"7b53c149f1554632aa28e6b39de3c054",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250703",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"熊涛",
				"tellerNo":"7567209",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704154204216347",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704154204216347",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"1b58ac266ca547d39bb835c9fda6c9f8",
				"isDel":"0",
				"oganName":"15089-中国银行德阳罗江支行",
				"oganNo":"15089",
				"registerDate":"20250704",
				"siteName":"中国银行德阳罗江支行营业部",
				"siteNo":"15442",
				"tellerName":"唐甜",
				"tellerNo":"1713854",
				"userName":"唐甜",
				"userNo":"1713854",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704155047593474",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704155047593474",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"b6c986d1289d4f68b9494c90cc6bc057",
				"isDel":"0",
				"oganName":"14913-中国银行成都高新技术产业开发区支行",
				"oganNo":"14913",
				"registerDate":"20250704",
				"siteName":"中国银行成都复城广场支行",
				"siteNo":"35944",
				"tellerName":"张诗琪",
				"tellerNo":"8767041",
				"userName":"张诗琪",
				"userNo":"8767041",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704152937239660",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025070400023004",
				"batchId":"20250704152937239660",
				"businessDate":"20240910",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640910",
				"endBusiDate":"20240910",
				"id":"faecdbe2bb4e4627bbd995430f9d8b6e",
				"isDel":"0",
				"oganName":"14901-中国银行成都蜀都大道支行",
				"oganNo":"14901",
				"registerDate":"20250704",
				"siteName":"中国银行成都实业街支行",
				"siteNo":"14902",
				"tellerName":"童心",
				"tellerNo":"1488420",
				"userName":"童心",
				"userNo":"1488420",
				"warrantAmount":"3"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025061600023002",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025061600023001",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"46e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"15370-中国银行四川省分行支付清算部",
				"oganNo":"15370",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行支付清算部",
				"siteNo":"15370",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250408150555275274",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025042100023001",
				"batchId":"20250408150555275274",
				"businessDate":"20250408",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400408",
				"endBusiDate":"20250408",
				"id":"d6dc8701877d4fb0a185b0d13d9cb066",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250408",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin1",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250616140816157403",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250616140816157403",
				"businessDate":"20250616",
				"codeName":"保存40年 (默认)",
				"codeNo":"PZ01",
				"destroyDate":"20650616",
				"endBusiDate":"20250626",
				"id":"4ea6430cec6f44749695b61ecf80e3cd",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250616",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"23"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250626150955945916",
				"applicationState":"FM_STATE_APP_13",
				"baleNo":"2025062600023001",
				"batchId":"20250626150955945916",
				"businessDate":"20250626",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650626",
				"endBusiDate":"20250626",
				"id":"3cbbc289402245118f5451c1e418610e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250626",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250627144548724684",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250627144548724684",
				"businessDate":"20250627",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650627",
				"endBusiDate":"20250627",
				"id":"ea2aa69f275744d8b54a5d9a9e203956",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250627",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250702171217509324",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250702171217509324",
				"businessDate":"20250702",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650702",
				"endBusiDate":"20250702",
				"id":"0ec8cc2c9c5242f9b11244fd6a46a95a",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250702",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"2"
			}
		],
		"totalCount":11,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-11 10:21:44.928 [OrganNo_00023_UserNo_admin] [0c39bab8d2f9f392/dfe5d09638989219] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationNo":"20250702171217509324"
		}
	],
	"sysMap":{
		"oper_type":"printApplication"
	}
}
2025-07-11 10:21:44.936 [OrganNo_00023_UserNo_admin] [0c39bab8d2f9f392/d805f00a6706c1a3] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE APPLICATION_NO = ?
2025-07-11 10:21:44.937 [OrganNo_00023_UserNo_admin] [0c39bab8d2f9f392/d805f00a6706c1a3] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 20250702171217509324(String)
2025-07-11 10:21:44.941 [OrganNo_00023_UserNo_admin] [0c39bab8d2f9f392/d805f00a6706c1a3] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-07-11 10:21:45.078 [OrganNo_00023_UserNo_admin] [0c39bab8d2f9f392/dfe5d09638989219] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"fileName":"20250702171217509324.png",
		"imgPath":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/20250702171217509324/20250702171217509324.png",
		"returnList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250702171217509324",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250702171217509324",
				"businessDate":"20250702",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650702",
				"endBusiDate":"20250702",
				"id":"0ec8cc2c9c5242f9b11244fd6a46a95a",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250702",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"2"
			}
		]
	},
	"retMsg":"生成成功"
}
2025-07-11 10:22:10.103 [OrganNo_00023_UserNo_admin] [91b957e8ac62cc34/ca028a3d560b8b2d] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-11 10:22:10.113 [OrganNo_00023_UserNo_admin] [91b957e8ac62cc34/22beee51699f9801] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-11 10:22:10.113 [OrganNo_00023_UserNo_admin] [91b957e8ac62cc34/22beee51699f9801] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-11 10:22:10.116 [OrganNo_00023_UserNo_admin] [91b957e8ac62cc34/22beee51699f9801] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-11 10:22:10.117 [OrganNo_00023_UserNo_admin] [91b957e8ac62cc34/22beee51699f9801] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-11 10:22:10.117 [OrganNo_00023_UserNo_admin] [91b957e8ac62cc34/22beee51699f9801] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-11 10:22:10.118 [OrganNo_00023_UserNo_admin] [91b957e8ac62cc34/22beee51699f9801] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-11 10:22:10.142 [OrganNo_00023_UserNo_admin] [91b957e8ac62cc34/ca028a3d560b8b2d] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-11 10:22:10.188 [OrganNo_00023_UserNo_admin] [873608ef429f740c/ccc16cffbb3a807e] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-07-11 10:22:10.205 [OrganNo_00023_UserNo_admin] [873608ef429f740c/435e8897631a1e53] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-07-11 10:22:10.206 [OrganNo_00023_UserNo_admin] [873608ef429f740c/435e8897631a1e53] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String), 23(String)
2025-07-11 10:22:10.212 [OrganNo_00023_UserNo_admin] [873608ef429f740c/435e8897631a1e53] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-07-11 10:22:10.213 [OrganNo_00023_UserNo_admin] [873608ef429f740c/435e8897631a1e53] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE USER_NO = ? and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-07-11 10:22:10.213 [OrganNo_00023_UserNo_admin] [873608ef429f740c/435e8897631a1e53] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 23(String), 15(Integer)
2025-07-11 10:22:10.220 [OrganNo_00023_UserNo_admin] [873608ef429f740c/435e8897631a1e53] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 5
2025-07-11 10:22:10.234 [OrganNo_00023_UserNo_admin] [873608ef429f740c/ccc16cffbb3a807e] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250703113315953115",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250703113315953115",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"7b53c149f1554632aa28e6b39de3c054",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250703",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"熊涛",
				"tellerNo":"7567209",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250616140816157403",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250616140816157403",
				"businessDate":"20250616",
				"codeName":"保存40年 (默认)",
				"codeNo":"PZ01",
				"destroyDate":"20650616",
				"endBusiDate":"20250626",
				"id":"4ea6430cec6f44749695b61ecf80e3cd",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250616",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"23"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250626150955945916",
				"applicationState":"FM_STATE_APP_13",
				"baleNo":"2025062600023001",
				"batchId":"20250626150955945916",
				"businessDate":"20250626",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650626",
				"endBusiDate":"20250626",
				"id":"3cbbc289402245118f5451c1e418610e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250626",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250627144548724684",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250627144548724684",
				"businessDate":"20250627",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650627",
				"endBusiDate":"20250627",
				"id":"ea2aa69f275744d8b54a5d9a9e203956",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250627",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250702171217509324",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250702171217509324",
				"businessDate":"20250702",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650702",
				"endBusiDate":"20250702",
				"id":"0ec8cc2c9c5242f9b11244fd6a46a95a",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250702",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"2"
			}
		],
		"totalCount":5,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-11 10:22:12.518 [OrganNo_00023_UserNo_admin] [decdefcb3f655e6b/9cbf6b58601028e4] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationNo":"20250627144548724684"
		}
	],
	"sysMap":{
		"oper_type":"printApplication"
	}
}
2025-07-11 10:22:12.521 [OrganNo_00023_UserNo_admin] [decdefcb3f655e6b/bc284ad7611f5cbf] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE APPLICATION_NO = ?
2025-07-11 10:22:12.522 [OrganNo_00023_UserNo_admin] [decdefcb3f655e6b/bc284ad7611f5cbf] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 20250627144548724684(String)
2025-07-11 10:22:12.526 [OrganNo_00023_UserNo_admin] [decdefcb3f655e6b/bc284ad7611f5cbf] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-07-11 10:22:12.549 [OrganNo_00023_UserNo_admin] [decdefcb3f655e6b/9cbf6b58601028e4] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"fileName":"20250627144548724684.png",
		"imgPath":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/20250627144548724684/20250627144548724684.png",
		"returnList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250627144548724684",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250627144548724684",
				"businessDate":"20250627",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650627",
				"endBusiDate":"20250627",
				"id":"ea2aa69f275744d8b54a5d9a9e203956",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250627",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			}
		]
	},
	"retMsg":"生成成功"
}
2025-07-11 10:22:16.542 [OrganNo_00023_UserNo_admin] [5ef2de152724b62f/49ef3ff7eeaba738] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-11 10:22:16.548 [OrganNo_00023_UserNo_admin] [5ef2de152724b62f/471ad575306598da] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-11 10:22:16.548 [OrganNo_00023_UserNo_admin] [5ef2de152724b62f/471ad575306598da] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-11 10:22:16.551 [OrganNo_00023_UserNo_admin] [5ef2de152724b62f/471ad575306598da] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-11 10:22:16.551 [OrganNo_00023_UserNo_admin] [5ef2de152724b62f/471ad575306598da] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-11 10:22:16.551 [OrganNo_00023_UserNo_admin] [5ef2de152724b62f/471ad575306598da] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-11 10:22:16.553 [OrganNo_00023_UserNo_admin] [5ef2de152724b62f/471ad575306598da] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-11 10:22:16.577 [OrganNo_00023_UserNo_admin] [5ef2de152724b62f/49ef3ff7eeaba738] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-11 10:22:16.623 [OrganNo_00023_UserNo_admin] [5006801514878b7e/96883ec0add8e83d] [http-nio-9009-exec-14] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-07-11 10:22:16.638 [OrganNo_00023_UserNo_admin] [5006801514878b7e/8232e4640486a659] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE = ?
2025-07-11 10:22:16.639 [OrganNo_00023_UserNo_admin] [5006801514878b7e/8232e4640486a659] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String), 23(String)
2025-07-11 10:22:16.645 [OrganNo_00023_UserNo_admin] [5006801514878b7e/8232e4640486a659] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-07-11 10:22:16.646 [OrganNo_00023_UserNo_admin] [5006801514878b7e/8232e4640486a659] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-07-11 10:22:16.647 [OrganNo_00023_UserNo_admin] [5006801514878b7e/8232e4640486a659] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 23(String), 15(Integer)
2025-07-11 10:22:16.653 [OrganNo_00023_UserNo_admin] [5006801514878b7e/8232e4640486a659] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 7
2025-07-11 10:22:16.670 [OrganNo_00023_UserNo_admin] [5006801514878b7e/96883ec0add8e83d] [http-nio-9009-exec-14] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250703113315953115",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250703113315953115",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"7b53c149f1554632aa28e6b39de3c054",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250703",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"熊涛",
				"tellerNo":"7567209",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704154204216347",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704154204216347",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"1b58ac266ca547d39bb835c9fda6c9f8",
				"isDel":"0",
				"oganName":"15089-中国银行德阳罗江支行",
				"oganNo":"15089",
				"registerDate":"20250704",
				"siteName":"中国银行德阳罗江支行营业部",
				"siteNo":"15442",
				"tellerName":"唐甜",
				"tellerNo":"1713854",
				"userName":"唐甜",
				"userNo":"1713854",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704155047593474",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704155047593474",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"b6c986d1289d4f68b9494c90cc6bc057",
				"isDel":"0",
				"oganName":"14913-中国银行成都高新技术产业开发区支行",
				"oganNo":"14913",
				"registerDate":"20250704",
				"siteName":"中国银行成都复城广场支行",
				"siteNo":"35944",
				"tellerName":"张诗琪",
				"tellerNo":"8767041",
				"userName":"张诗琪",
				"userNo":"8767041",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250616140816157403",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250616140816157403",
				"businessDate":"20250616",
				"codeName":"保存40年 (默认)",
				"codeNo":"PZ01",
				"destroyDate":"20650616",
				"endBusiDate":"20250626",
				"id":"4ea6430cec6f44749695b61ecf80e3cd",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250616",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"23"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250626150955945916",
				"applicationState":"FM_STATE_APP_13",
				"baleNo":"2025062600023001",
				"batchId":"20250626150955945916",
				"businessDate":"20250626",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650626",
				"endBusiDate":"20250626",
				"id":"3cbbc289402245118f5451c1e418610e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250626",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250627144548724684",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250627144548724684",
				"businessDate":"20250627",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650627",
				"endBusiDate":"20250627",
				"id":"ea2aa69f275744d8b54a5d9a9e203956",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250627",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250702171217509324",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250702171217509324",
				"businessDate":"20250702",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650702",
				"endBusiDate":"20250702",
				"id":"0ec8cc2c9c5242f9b11244fd6a46a95a",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250702",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"2"
			}
		],
		"totalCount":7,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-11 10:22:16.719 [OrganNo_00023_UserNo_admin] [e5ac6df92dc8139a/7b4355ee091a2903] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"0"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-07-11 10:22:16.733 [OrganNo_00023_UserNo_admin] [e5ac6df92dc8139a/3a372d7a35db348a] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-07-11 10:22:16.734 [OrganNo_00023_UserNo_admin] [e5ac6df92dc8139a/3a372d7a35db348a] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 0(String), admin(String)
2025-07-11 10:22:16.743 [OrganNo_00023_UserNo_admin] [e5ac6df92dc8139a/3a372d7a35db348a] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-07-11 10:22:16.744 [OrganNo_00023_UserNo_admin] [e5ac6df92dc8139a/3a372d7a35db348a] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-07-11 10:22:16.744 [OrganNo_00023_UserNo_admin] [e5ac6df92dc8139a/3a372d7a35db348a] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 0(String), admin(String), 15(Integer)
2025-07-11 10:22:16.753 [OrganNo_00023_UserNo_admin] [e5ac6df92dc8139a/3a372d7a35db348a] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 3
2025-07-11 10:22:16.780 [OrganNo_00023_UserNo_admin] [e5ac6df92dc8139a/7b4355ee091a2903] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025061600023001",
				"baleState":"0",
				"id":"451e99a1f4554cd1baa97ffb855e968c",
				"sendDate":"20250616",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"15370"
			},
			{
				"baleNo":"2025061600023002",
				"baleState":"0",
				"id":"0e1b88c2cc7b45d0a1d6f23d9731ca14",
				"sendDate":"20250616",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025070400023004",
				"baleState":"0",
				"id":"a31612b4611441618c8d23680bbc2401",
				"siteNo":"14902"
			}
		],
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-07-11 18:23:25.565 [OrganNo_00023_UserNo_admin] [36b2f33253074e4c/629d6e57b2df74e1] [http-nio-9009-exec-17] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-11 18:23:27.253 [OrganNo_00023_UserNo_admin] [36b2f33253074e4c/ad52c63630b04e8a] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-11 18:23:27.254 [OrganNo_00023_UserNo_admin] [36b2f33253074e4c/ad52c63630b04e8a] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-11 18:23:27.261 [OrganNo_00023_UserNo_admin] [36b2f33253074e4c/ad52c63630b04e8a] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-11 18:23:27.261 [OrganNo_00023_UserNo_admin] [36b2f33253074e4c/ad52c63630b04e8a] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-11 18:23:27.262 [OrganNo_00023_UserNo_admin] [36b2f33253074e4c/ad52c63630b04e8a] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-11 18:23:27.265 [OrganNo_00023_UserNo_admin] [36b2f33253074e4c/ad52c63630b04e8a] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-11 18:23:27.297 [OrganNo_00023_UserNo_admin] [36b2f33253074e4c/629d6e57b2df74e1] [http-nio-9009-exec-17] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-11 18:23:27.396 [OrganNo_00023_UserNo_admin] [6dbd8da23437869d/8bdcc43f8ee16d20] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-07-11 18:23:27.442 [OrganNo_00023_UserNo_admin] [6dbd8da23437869d/09d3e83f6a0c4836] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-07-11 18:23:27.451 [OrganNo_00023_UserNo_admin] [6dbd8da23437869d/09d3e83f6a0c4836] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin(String)
2025-07-11 18:23:27.456 [OrganNo_00023_UserNo_admin] [6dbd8da23437869d/09d3e83f6a0c4836] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-07-11 18:23:27.457 [OrganNo_00023_UserNo_admin] [6dbd8da23437869d/09d3e83f6a0c4836] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-07-11 18:23:27.457 [OrganNo_00023_UserNo_admin] [6dbd8da23437869d/09d3e83f6a0c4836] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin(String), 15(Integer)
2025-07-11 18:23:27.461 [OrganNo_00023_UserNo_admin] [6dbd8da23437869d/09d3e83f6a0c4836] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 2
2025-07-11 18:23:27.496 [OrganNo_00023_UserNo_admin] [6dbd8da23437869d/8bdcc43f8ee16d20] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025062600023001",
				"baleState":"1",
				"id":"57a5acc00db249199fc3f3b7bbacc891",
				"sendDate":"20250626",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
