import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getContentServerGroup(data) {
  const url = '/contentServerManage/getContentServerGroupAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getRelContentServerTree(data) {
  const url = '/contentServerManage/getRelContentServerTreeAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}
export function logListByServerId(data) {
  const url = '/logManage/logListByServerIdAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: {
      data: data
    }
  })
}

