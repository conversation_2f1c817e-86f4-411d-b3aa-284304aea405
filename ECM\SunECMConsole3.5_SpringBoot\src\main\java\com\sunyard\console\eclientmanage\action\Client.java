package com.sunyard.console.eclientmanage.action;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Client {

	private final static  Logger log = LoggerFactory.getLogger(Client.class);
//	public static void main(String[] args){
//		Socket socket = null;
//		PrintWriter pw = null;
//		BufferedReader br = null;
//		try{
//			//创建socket对象，并指明服务器的IP地址和端口号
//			socket = new Socket("localhost",8585);
//			//得到socket发送数据的输出流
//			OutputStream out = socket.getOutputStream();
//			//将字节流包装成字符流
//			pw = new PrintWriter(out);
//			
//			//向服务器发送数据
//			pw.println("aaa");
//			//刷新流，确保数据能写到服务端
//			pw.flush();
//			
//			InputStream in = socket.getInputStream();
//			br = new BufferedReader(new InputStreamReader(in));
//			String info = br.readLine();
//			System.out.println(info);
//		}catch(Exception e){
//			e.printStackTrace();
//		}finally{
//			try{
//				pw.close();
//				socket.close();
//			}catch(IOException e){
//				e.printStackTrace();
//			}
//		}
//	}
	
//	public static void main(String[] args){
//		long t1 = System.currentTimeMillis();
//		for(int i=0;i<2000000;i++){
//			log.info("main" + i);
//		}
//		long t2 = System.currentTimeMillis();
//		System.out.println(t2-t1);
//	}
}
