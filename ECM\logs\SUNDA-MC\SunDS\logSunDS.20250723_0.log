2025-07-23 09:33:10.442 [OrganNo_00023_UserNo_admin] [155cff37e083d09f/a1225add608b0959] [http-nio-9058-exec-32] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessGrade":0,
			"businessNo":0,
			"parentBusiness":"200001"
		}
	],
	"sysMap":{
		"operType":"OP004"
	}
}
2025-07-23 09:33:10.554 [OrganNo_00023_UserNo_admin] [155cff37e083d09f/a1225add608b0959] [http-nio-9058-exec-32] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"list":[
			{
				"BUSINESS_ID":"WYLB00001",
				"BUSINESS_NAME":"结算账户管理",
				"BUSINESS_DESC":"结算账户管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":3
			},
			{
				"BUSINESS_ID":"WYLB00002",
				"BUSINESS_NAME":"内部账户及科目管理",
				"BUSINESS_DESC":"内部账户及科目管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":4
			},
			{
				"BUSINESS_ID":"WYLB00003",
				"BUSINESS_NAME":"支付结算业务管理",
				"BUSINESS_DESC":"支付结算业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":5
			},
			{
				"BUSINESS_ID":"WYLB00004",
				"BUSINESS_NAME":"信贷业务管理",
				"BUSINESS_DESC":"信贷业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":6
			},
			{
				"BUSINESS_ID":"WYLB00005",
				"BUSINESS_NAME":"现金及重空管理",
				"BUSINESS_DESC":"现金及重空管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":7
			},
			{
				"BUSINESS_ID":"WYLB00006",
				"BUSINESS_NAME":"资金异动管理",
				"BUSINESS_DESC":"资金异动管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":8
			},
			{
				"BUSINESS_ID":"WYLB00007",
				"BUSINESS_NAME":"个人业务管理",
				"BUSINESS_DESC":"个人业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":9
			},
			{
				"BUSINESS_ID":"WYLB00008",
				"BUSINESS_NAME":"其他",
				"BUSINESS_DESC":"其他",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":10
			},
			{
				"BUSINESS_ID":"WYLB00009",
				"BUSINESS_NAME":"机构及柜员管理",
				"BUSINESS_DESC":"机构及柜员管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":11
			}
		]
	},
	"retMsg":"树初始化成功"
}
2025-07-23 09:33:10.556 [] [155cff37e083d09f/a1225add608b0959] [http-nio-9058-exec-32] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作业务条线 操作方法: 对树进行加载!请求IP地址: ************** 操作开始时间: 2025-07-23 09:33:10 操作结束时间: 2025-07-23 09:33:10!总共花费时间: 144 毫秒！
2025-07-23 09:33:10.802 [OrganNo_00023_UserNo_admin] [70fcf050e0df5336/7a307fcc98c25644] [http-nio-9058-exec-33] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessLine":"",
			"modelType":"0",
			"riskLevel":""
		}
	],
	"sysMap":{
		"oper_type":"getUserModelInfos"
	}
}
2025-07-23 09:33:10.846 [OrganNo_00023_UserNo_admin] [70fcf050e0df5336/7a307fcc98c25644] [http-nio-9058-exec-33] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"userModelInfos":[
			
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 09:33:10.847 [] [70fcf050e0df5336/7a307fcc98c25644] [http-nio-9058-exec-33] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询模型 操作方法: 获取用户模型!请求IP地址: ************** 操作开始时间: 2025-07-23 09:33:10 操作结束时间: 2025-07-23 09:33:10!总共花费时间: 60 毫秒！
2025-07-23 10:11:27.412 [OrganNo_00023_UserNo_admin] [315fae4bc1340217/78545c87f354d562] [http-nio-9058-exec-47] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessGrade":0,
			"businessNo":0,
			"parentBusiness":"200001"
		}
	],
	"sysMap":{
		"operType":"OP004"
	}
}
2025-07-23 10:11:27.546 [OrganNo_00023_UserNo_admin] [315fae4bc1340217/78545c87f354d562] [http-nio-9058-exec-47] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"list":[
			{
				"BUSINESS_ID":"WYLB00001",
				"BUSINESS_NAME":"结算账户管理",
				"BUSINESS_DESC":"结算账户管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":3
			},
			{
				"BUSINESS_ID":"WYLB00002",
				"BUSINESS_NAME":"内部账户及科目管理",
				"BUSINESS_DESC":"内部账户及科目管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":4
			},
			{
				"BUSINESS_ID":"WYLB00003",
				"BUSINESS_NAME":"支付结算业务管理",
				"BUSINESS_DESC":"支付结算业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":5
			},
			{
				"BUSINESS_ID":"WYLB00004",
				"BUSINESS_NAME":"信贷业务管理",
				"BUSINESS_DESC":"信贷业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":6
			},
			{
				"BUSINESS_ID":"WYLB00005",
				"BUSINESS_NAME":"现金及重空管理",
				"BUSINESS_DESC":"现金及重空管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":7
			},
			{
				"BUSINESS_ID":"WYLB00006",
				"BUSINESS_NAME":"资金异动管理",
				"BUSINESS_DESC":"资金异动管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":8
			},
			{
				"BUSINESS_ID":"WYLB00007",
				"BUSINESS_NAME":"个人业务管理",
				"BUSINESS_DESC":"个人业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":9
			},
			{
				"BUSINESS_ID":"WYLB00008",
				"BUSINESS_NAME":"其他",
				"BUSINESS_DESC":"其他",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":10
			},
			{
				"BUSINESS_ID":"WYLB00009",
				"BUSINESS_NAME":"机构及柜员管理",
				"BUSINESS_DESC":"机构及柜员管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":11
			}
		]
	},
	"retMsg":"树初始化成功"
}
2025-07-23 10:11:27.547 [] [315fae4bc1340217/78545c87f354d562] [http-nio-9058-exec-47] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作业务条线 操作方法: 对树进行加载!请求IP地址: ************** 操作开始时间: 2025-07-23 10:11:27 操作结束时间: 2025-07-23 10:11:27!总共花费时间: 147 毫秒！
2025-07-23 10:11:27.837 [OrganNo_00023_UserNo_admin] [d53441a5d50a5c26/5c390eeae05956cb] [http-nio-9058-exec-46] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessLine":"",
			"modelType":"0",
			"riskLevel":""
		}
	],
	"sysMap":{
		"oper_type":"getUserModelInfos"
	}
}
2025-07-23 10:11:27.881 [OrganNo_00023_UserNo_admin] [d53441a5d50a5c26/5c390eeae05956cb] [http-nio-9058-exec-46] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"userModelInfos":[
			
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 10:11:27.881 [] [d53441a5d50a5c26/5c390eeae05956cb] [http-nio-9058-exec-46] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询模型 操作方法: 获取用户模型!请求IP地址: ************** 操作开始时间: 2025-07-23 10:11:27 操作结束时间: 2025-07-23 10:11:27!总共花费时间: 65 毫秒！
2025-07-23 10:15:13.350 [OrganNo_00023_UserNo_admin] [55a358676e276ac8/d6f9b2b149d489ea] [http-nio-9058-exec-49] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessGrade":0,
			"businessNo":0,
			"parentBusiness":"200001"
		}
	],
	"sysMap":{
		"operType":"OP004"
	}
}
2025-07-23 10:15:13.440 [OrganNo_00023_UserNo_admin] [55a358676e276ac8/d6f9b2b149d489ea] [http-nio-9058-exec-49] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"list":[
			{
				"BUSINESS_ID":"WYLB00001",
				"BUSINESS_NAME":"结算账户管理",
				"BUSINESS_DESC":"结算账户管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":3
			},
			{
				"BUSINESS_ID":"WYLB00002",
				"BUSINESS_NAME":"内部账户及科目管理",
				"BUSINESS_DESC":"内部账户及科目管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":4
			},
			{
				"BUSINESS_ID":"WYLB00003",
				"BUSINESS_NAME":"支付结算业务管理",
				"BUSINESS_DESC":"支付结算业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":5
			},
			{
				"BUSINESS_ID":"WYLB00004",
				"BUSINESS_NAME":"信贷业务管理",
				"BUSINESS_DESC":"信贷业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":6
			},
			{
				"BUSINESS_ID":"WYLB00005",
				"BUSINESS_NAME":"现金及重空管理",
				"BUSINESS_DESC":"现金及重空管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":7
			},
			{
				"BUSINESS_ID":"WYLB00006",
				"BUSINESS_NAME":"资金异动管理",
				"BUSINESS_DESC":"资金异动管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":8
			},
			{
				"BUSINESS_ID":"WYLB00007",
				"BUSINESS_NAME":"个人业务管理",
				"BUSINESS_DESC":"个人业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":9
			},
			{
				"BUSINESS_ID":"WYLB00008",
				"BUSINESS_NAME":"其他",
				"BUSINESS_DESC":"其他",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":10
			},
			{
				"BUSINESS_ID":"WYLB00009",
				"BUSINESS_NAME":"机构及柜员管理",
				"BUSINESS_DESC":"机构及柜员管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":11
			}
		]
	},
	"retMsg":"树初始化成功"
}
2025-07-23 10:15:13.441 [] [55a358676e276ac8/d6f9b2b149d489ea] [http-nio-9058-exec-49] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作业务条线 操作方法: 对树进行加载!请求IP地址: ************** 操作开始时间: 2025-07-23 10:15:13 操作结束时间: 2025-07-23 10:15:13!总共花费时间: 101 毫秒！
2025-07-23 10:15:13.729 [OrganNo_00023_UserNo_admin] [30ffbe9d47bb1144/a4a11b1a3d9208c0] [http-nio-9058-exec-51] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessLine":"",
			"modelType":"0",
			"riskLevel":""
		}
	],
	"sysMap":{
		"oper_type":"getUserModelInfos"
	}
}
2025-07-23 10:15:13.772 [OrganNo_00023_UserNo_admin] [30ffbe9d47bb1144/a4a11b1a3d9208c0] [http-nio-9058-exec-51] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"userModelInfos":[
			
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 10:15:13.774 [] [30ffbe9d47bb1144/a4a11b1a3d9208c0] [http-nio-9058-exec-51] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询模型 操作方法: 获取用户模型!请求IP地址: ************** 操作开始时间: 2025-07-23 10:15:13 操作结束时间: 2025-07-23 10:15:13!总共花费时间: 69 毫秒！
2025-07-23 19:36:23.348 [OrganNo_00023_UserNo_admin] [7c4181037d984b2f/62341797d686b08b] [http-nio-9058-exec-53] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessGrade":0,
			"businessNo":0,
			"parentBusiness":"200001"
		}
	],
	"sysMap":{
		"operType":"OP004"
	}
}
2025-07-23 19:36:23.489 [OrganNo_00023_UserNo_admin] [7c4181037d984b2f/62341797d686b08b] [http-nio-9058-exec-53] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"list":[
			{
				"BUSINESS_ID":"WYLB00001",
				"BUSINESS_NAME":"结算账户管理",
				"BUSINESS_DESC":"结算账户管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":3
			},
			{
				"BUSINESS_ID":"WYLB00002",
				"BUSINESS_NAME":"内部账户及科目管理",
				"BUSINESS_DESC":"内部账户及科目管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":4
			},
			{
				"BUSINESS_ID":"WYLB00003",
				"BUSINESS_NAME":"支付结算业务管理",
				"BUSINESS_DESC":"支付结算业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":5
			},
			{
				"BUSINESS_ID":"WYLB00004",
				"BUSINESS_NAME":"信贷业务管理",
				"BUSINESS_DESC":"信贷业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":6
			},
			{
				"BUSINESS_ID":"WYLB00005",
				"BUSINESS_NAME":"现金及重空管理",
				"BUSINESS_DESC":"现金及重空管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":7
			},
			{
				"BUSINESS_ID":"WYLB00006",
				"BUSINESS_NAME":"资金异动管理",
				"BUSINESS_DESC":"资金异动管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":8
			},
			{
				"BUSINESS_ID":"WYLB00007",
				"BUSINESS_NAME":"个人业务管理",
				"BUSINESS_DESC":"个人业务管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":9
			},
			{
				"BUSINESS_ID":"WYLB00008",
				"BUSINESS_NAME":"其他",
				"BUSINESS_DESC":"其他",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":10
			},
			{
				"BUSINESS_ID":"WYLB00009",
				"BUSINESS_NAME":"机构及柜员管理",
				"BUSINESS_DESC":"机构及柜员管理",
				"PARENT_BUSINESS":"200001",
				"PARENT":"false",
				"BUSINESS_GRADE":2,
				"BUSINESS_NO":11
			}
		]
	},
	"retMsg":"树初始化成功"
}
2025-07-23 19:36:23.490 [] [7c4181037d984b2f/62341797d686b08b] [http-nio-9058-exec-53] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作业务条线 操作方法: 对树进行加载!请求IP地址: ************** 操作开始时间: 2025-07-23 19:36:23 操作结束时间: 2025-07-23 19:36:23!总共花费时间: 159 毫秒！
2025-07-23 19:36:24.086 [OrganNo_00023_UserNo_admin] [5a8aa9c8c8bf0b0b/b9b913a2d8900ede] [http-nio-9058-exec-54] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"businessLine":"",
			"modelType":"0",
			"riskLevel":""
		}
	],
	"sysMap":{
		"oper_type":"getUserModelInfos"
	}
}
2025-07-23 19:36:24.130 [OrganNo_00023_UserNo_admin] [5a8aa9c8c8bf0b0b/b9b913a2d8900ede] [http-nio-9058-exec-54] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"userModelInfos":[
			
		]
	},
	"retMsg":"查询成功"
}
2025-07-23 19:36:24.131 [] [5a8aa9c8c8bf0b0b/b9b913a2d8900ede] [http-nio-9058-exec-54] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 查询模型 操作方法: 获取用户模型!请求IP地址: ************** 操作开始时间: 2025-07-23 19:36:24 操作结束时间: 2025-07-23 19:36:24!总共花费时间: 61 毫秒！
