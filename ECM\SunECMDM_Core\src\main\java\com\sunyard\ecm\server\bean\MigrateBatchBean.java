package com.sunyard.ecm.server.bean;

import com.sunyard.ws.utils.XMLUtil;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * 迁移组装对象
 * <AUTHOR>
 *
 */
@XStreamAlias("MigrateBatchBean")
public class MigrateBatchBean extends BatchBean {

	@XStreamAsAttribute
	private String EXE_SQL = "";//下级文档服务需要执行的sql
	
	@XStreamAsAttribute
	private String MODEL_TYPE = "";//模型类型
	
	@XStreamAsAttribute
	private String MIGRATE_FLAG = ""; //迁移标识 ： 0定时迁移任务，1立即迁移任务
	
	@XStreamAsAttribute
	private int MIGRATE_COUNT = 0; //迁移次数
	
	@XStreamAsAttribute
	private boolean IS_MIGRATE; // 是否设置迁移
	@XStreamAsAttribute
	private String USER_NAME; // 用户名
	private String businessStartDate;
	
	public String getBusinessStartDate() {
		return businessStartDate;
	}
	public void setBusinessStartDate(String businessStartDate) {
		this.businessStartDate = businessStartDate;
	}
	public String getExeSql() {
		return EXE_SQL;
	}
	public String getUserName() {
		return USER_NAME;
	}
	public void setUserName(String userName) {
		this.USER_NAME = userName;
	}
	public String getMigrateFlag() {
		return MIGRATE_FLAG;
	}
	public void setMigrateFlag(String migrateFlag) {
		this.MIGRATE_FLAG = migrateFlag;
	}
	public int getMigrateCount() {
		return MIGRATE_COUNT;
	}
	public void setMigrateCount(int migrateCount) {
		this.MIGRATE_COUNT = migrateCount;
	}
	public void setExeSql(String exeSql) {
		this.EXE_SQL = exeSql;
	}
	public String getModelType() {
		return MODEL_TYPE;
	}
	public void setModelType(String modelType) {
		this.MODEL_TYPE = modelType;
	}
	public boolean isMigrate() {
		return IS_MIGRATE;
	}
	public void setMigrate(boolean isMigrate) {
		this.IS_MIGRATE = isMigrate;
	}
	/**
	 * 将当前对象转换成xml
	 */
	public String toString(){
		String resultXml = XMLUtil.bean2XML(this);
		return resultXml;
	}
	
	/**
	 * 传入xml，转换成当前对象类型的Bean
	 * @param xmlString
	 * @return
	 */
	public static MigrateBatchBean toBean(String xmlString){
		return XMLUtil.xml2Bean(xmlString, MigrateBatchBean.class);
	}
//	public static void main(String[] args) {
//		MigrateBatchBean m = new MigrateBatchBean();
//		m.setMigrateFlag("0");
//		m.setMigrateCount(1);
//		m.setModelCode("index");
//		m.setModelType("0");
//		m.setMigrate(true);
//		System.out.println(m);
//	}

}