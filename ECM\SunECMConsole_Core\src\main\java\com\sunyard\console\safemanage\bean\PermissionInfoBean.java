package com.sunyard.console.safemanage.bean;

import java.io.Serializable;

/**
 * <p>Title: 权限bean</p>
 * <p>Description: 存放权限信息</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class PermissionInfoBean extends NodeBean implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private String permission_code;		//权限编号
	private String permission_name;		//权限名称
	private String permission_type;		//权限类型 : 1菜单 , 2按钮
	
	public String getPermission_code() {
		return permission_code;
	}
	public void setPermission_code(String permission_code) {
		this.permission_code = permission_code;
	}
	public String getPermission_name() {
		return permission_name;
	}
	public void setPermission_name(String permission_name) {
		this.permission_name = permission_name;
	}
	public String getPermission_type() {
		return permission_type;
	}
	public void setPermission_type(String permissionType) {
		permission_type = permissionType;
	}
	@Override
	public String getNodeID() {
		return permission_code;
	}
	@Override
	public String getNodeName() {
		return permission_name;
	}
	@Override
	public String getParentNodeID() {
		return permission_type+"";
	}
	@Override
	public String getHasCheckbox() {
		return "false";
	}
	
}
