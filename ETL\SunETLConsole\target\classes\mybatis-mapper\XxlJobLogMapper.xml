<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobLogDao">

	<resultMap id="XxlJobLog" type="com.xxl.job.admin.core.model.XxlJobLog" >
		<result column="id" property="id" />
		<result column="job_group" property="jobGroup" />
		<result column="job_id" property="jobId" />
		<result column="glue_type" property="glueType" />
		<result column="executor_address" property="executorAddress" />
		<result column="executor_handler" property="executorHandler" />
	    <result column="executor_param" property="executorParam" />
	    <result column="trigger_time" property="triggerTime" />
	    <result column="trigger_code" property="triggerCode" />
	    <result column="trigger_msg" property="triggerMsg" />
	    <result column="handle_time" property="handleTime" />
	    <result column="handle_code" property="handleCode" />
	    <result column="handle_msg" property="handleMsg" />
	    <result column="attemper_msg" property="attemperMsg" />
	    <result column="occur_date" property="occurDate" />
	    <result column="EXECUTOR_TIME" property="executortime" />
	</resultMap>








	<sql id="Base_Column_List">
		t.id,
		t.job_group,
		t.job_id,
		t.glue_type,
		t.executor_address,
		t.executor_handler,
		t.executor_param,
		t.EXECUTOR_TIME,
		t.trigger_time,
		t.trigger_code,
		t.trigger_msg,
		t.handle_time,
		t.handle_code,
		t.handle_msg,
		t.attemper_msg,
		t.occur_date
	</sql>

	<select id="pageList" resultMap="XxlJobLog">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_LOG t,QRTZ_TRIGGER_INFO info
		WHERE t.job_id = info.id AND info.write_type = #{jobType}
			<if test="jobGroup != null and jobGroup != ''">
				AND t.job_group = #{jobGroup}
			</if>
			<if test="jobId gt 0">
				AND t.job_id = #{jobId}
			</if>
			<if test="triggerTimeStart != null">
				AND t.trigger_time <![CDATA[ >= ]]> #{triggerTimeStart}
			</if>
			<if test="triggerTimeEnd != null">
				AND t.trigger_time <![CDATA[ <= ]]> #{triggerTimeEnd}
			</if>
			<if test="logStatus == 1" >
				AND t.handle_code = 200
			</if>
			<if test="logStatus == 2" >
				AND (
					(t.trigger_code <![CDATA[ > ]]> 0 AND t.trigger_code!=200) OR
					(t.handle_code <![CDATA[ > ]]> 0 AND t.handle_code!=200)
				)
			</if>
			<if test="logStatus == 3" >
				AND (t.trigger_code = 200 AND t.handle_code=0)
			</if>
		ORDER BY id DESC
	</select>

	<select id="pageListCount" resultType="int">
		SELECT count(1)
		FROM QRTZ_TRIGGER_LOG t,QRTZ_TRIGGER_INFO info
		WHERE t.job_id = info.id
		AND info.write_type = #{jobType}
		<if test="jobGroup != null and jobGroup != ''">
				AND t.job_group = #{jobGroup}
			</if>
			<if test="jobId gt 0">
				AND t.job_id = #{jobId}
			</if>
			<if test="triggerTimeStart != null">
				AND t.trigger_time <![CDATA[ >= ]]> #{triggerTimeStart}
			</if>
			<if test="triggerTimeEnd != null">
				AND t.trigger_time <![CDATA[ <= ]]> #{triggerTimeEnd}
			</if>
			<if test="logStatus == 1" >
				AND t.handle_code = 200
			</if>
			<if test="logStatus == 2" >
				AND (
				(t.trigger_code <![CDATA[ > ]]> 0 AND t.trigger_code!=200) OR
				(t.handle_code <![CDATA[ > ]]> 0 AND t.handle_code!=200)
				)
			</if>
			<if test="logStatus == 3" >
				AND (t.trigger_code = 200 AND t.handle_code=0)
			</if>
	</select>

	<select id="load" parameterType="java.lang.Integer" resultMap="XxlJobLog">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_LOG t
		WHERE t.id = #{id,jdbcType=INTEGER}
	</select>


	<insert id="save" parameterType="com.xxl.job.admin.core.model.XxlJobLog" useGeneratedKeys="true" keyProperty="id">
		INSERT INTO QRTZ_TRIGGER_LOG (
			job_group,
			job_id,
			occur_date
		) VALUES (
					 #{jobGroup},
					 #{jobId},
					 #{occurDate}
				 )
	</insert>

	<update id="updateTriggerInfo" >
		UPDATE QRTZ_TRIGGER_LOG
		SET
		    glue_type= #{glueType},
		    trigger_time= #{triggerTime,},
			trigger_code= #{triggerCode},
			trigger_msg= #{triggerMsg},
			executor_address= #{executorAddress,jdbcType=VARCHAR},
			executor_handler=#{executorHandler},
			executor_param= #{executorParam,jdbcType=VARCHAR}
		WHERE id= #{id,jdbcType=INTEGER}
	</update>

	<update id="updateHandleInfo">
		UPDATE QRTZ_TRIGGER_LOG
		SET
			handle_time= #{handleTime},
			handle_code= #{handleCode},
			handle_msg= #{handleMsg},
			occur_date= #{occurDate},
			EXECUTOR_TIME= #{executortime}
		WHERE id= #{id,jdbcType=INTEGER}
	</update>

	<update id="updateAttemperMsg">
		UPDATE QRTZ_TRIGGER_LOG
		SET
		attemper_msg = #{attemperMsg}
		WHERE id= #{id,jdbcType=INTEGER}
	</update>



	<delete id="delete" >
		delete from QRTZ_TRIGGER_LOG
		WHERE job_id = #{jobId}
	</delete>

	<select id="triggerCountByHandleCode" resultType="int" >
		SELECT count(1)
		FROM QRTZ_TRIGGER_LOG t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="handleCode gt 0">
				AND t.handle_code = #{handleCode}
			</if>
		</trim>
	</select>



	<select id="checkJobIsSuccess" resultType="int" >
		SELECT count(1)
		FROM QRTZ_TRIGGER_LOG t
		WHERE JOB_ID = #{jobId} AND OCCUR_DATE = #{occurDate} AND t.handle_code = #{handleCode}
	</select>


    <select id="triggerCountByDay" resultType="java.util.Map" >
        SELECT to_char(trigger_time,'yyyy/mm/dd') triggerDay, COUNT(id) triggerCount
        FROM QRTZ_TRIGGER_LOG
       	WHERE trigger_time BETWEEN #{from} and #{to}
		<if test="handleCode gt 0">
			AND handle_code = #{handleCode}
		</if>
        GROUP BY to_char(trigger_time,'yyyy/mm/dd')
		ORDER by to_char(trigger_time,'yyyy/mm/dd')
    </select>

	<select id="triggerCountByDayN" resultType="java.util.Map" >
		SELECT
		to_char(trigger_time,'yyyy/mm/dd') triggerDay,
		COUNT(id) triggerDayCount,
		SUM(CASE WHEN handle_code = 0 then 1 else 0 end) as triggerDayCountRunning,
		SUM(CASE WHEN handle_code = 200 then 1 else 0 end) as triggerDayCountSuc
		FROM XXL_JOB_QRTZ_TRIGGER_LOG
		WHERE trigger_time BETWEEN #{from} and #{to}
		GROUP BY triggerDay;
	</select>





	<delete id="clearLog" >
		delete from QRTZ_TRIGGER_LOG
		WHERE JOB_ID IN (SELECT id FROM QRTZ_TRIGGER_INFO WHERE WRITE_TYPE = #{jobType})
			<if test="jobGroup gt 0">
				AND job_group = #{jobGroup}
			</if>
			<if test="jobId gt 0">
				AND job_id = #{jobId}
			</if>

			<if test="clearBeforeTime != null">
				AND trigger_time <![CDATA[ <= ]]> #{clearBeforeTime}
			</if>
			<if test="clearBeforeNum gt 0">
				AND id NOT in(
					SELECT id FROM(
						SELECT id FROM QRTZ_TRIGGER_LOG t
						<trim prefix="WHERE" prefixOverrides="AND | OR" >
							<if test="jobGroup gt 0">
								AND t.job_group = #{jobGroup}
							</if>
							<if test="jobId gt 0">
								AND t.job_id = #{jobId}
							</if>
						</trim>
						ORDER BY t.trigger_time desc
						LIMIT 0, #{clearBeforeNum}
					) t1
				)
			</if>

		<if test="occur_dateStart != null" >
			AND occur_date = #{occur_dateStart}
		</if>

	</delete>


	<!-- 获取该任务最后执行时间 -->
	<select id="getMaxJobIdLogTime"  parameterType="java.util.Map" resultType="java.util.Map">
			SELECT
				JOB_ID,TRIGGER_TIME,HANDLE_TIME FROM(
			SELECT
				ROW_NUMBER()OVER(
				ORDER BY
					T.TRIGGER_TIME DESC) RN,
					T.JOB_ID,
					T.TRIGGER_TIME,
					T.HANDLE_TIME
			FROM
				QRTZ_TRIGGER_LOG T
			WHERE
				T.JOB_ID= #{jobId}
				AND OCCUR_DATE = #{occurDate}) TMP
			WHERE
				TMP.RN=1
	</select>

	<!-- 查询该任务当天是否有执行成功日志 -->
	<select id="getJobIdSuccessLogTime"  parameterType="java.util.Map" resultType="int">
		select COUNT(1) from QRTZ_TRIGGER_LOG LOG
		WHERE LOG.JOB_ID = #{jobId}
		AND LOG.OCCUR_DATE = #{occurDate} AND LOG.HANDLE_CODE = 200
	</select>


	<!--获取最后成功日期-->
	<select id="getMaxSuccessDate"  resultType="String">
		select max(LOG.OCCUR_DATE) from QRTZ_TRIGGER_LOG LOG,
		QRTZ_TRIGGER_INFO JOB
		WHERE LOG.JOB_ID = JOB.ID
		AND JOB.EXECUTOR_HANDLER = 'EndTask' AND HANDLE_CODE = 200
	</select>




</mapper>
