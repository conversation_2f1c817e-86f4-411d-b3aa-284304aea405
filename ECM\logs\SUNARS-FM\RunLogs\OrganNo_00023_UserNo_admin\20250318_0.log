2025-03-18 00:32:18.540 [OrganNo_00023_UserNo_admin] [b459da79e9226257/d4c8de805ff44066] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-18 00:32:20.563 [OrganNo_00023_UserNo_admin] [b459da79e9226257/d4c8de805ff44066] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-18 00:32:20.721 [OrganNo_00023_UserNo_admin] [0b73ff3c8ec0fdd1/f70d667ac4aa92fa] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-18 00:32:20.949 [OrganNo_00023_UserNo_admin] [0b73ff3c8ec0fdd1/f70d667ac4aa92fa] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			}
		],
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
