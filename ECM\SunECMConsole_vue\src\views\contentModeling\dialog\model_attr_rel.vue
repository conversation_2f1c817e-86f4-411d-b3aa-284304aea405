<template>
  <div>
    <el-dialog v-el-drag-dialog :close-on-click-modal="false"
      :title="modelAttrTitle"
      :visible.sync="modelAttrdialogFormVisible" width="1200px"
    >
    <div class="edit_dev">
      <el-transfer
        ref="transferCom"
        style="text-align: left; display: inline-block"
        v-model="choiceDataList"
        filterable
        :left-default-checked="[1]"
        :right-default-checked="[2]"
        :titles="['未关联属性', '已关联属性']"
        :button-texts="['删除', '添加']"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}',
        }"
        @change="handleChange"
        :data="notChoiceDataList"
      >
        <!-- <span slot-scope="{ option }"
          >{{ option.key }} - {{ option.label }}</span
        > -->
        <span slot-scope="{ option }"> {{ option.label }}</span>
      </el-transfer>
    </div>  
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="hide">取消</el-button>
        <el-button size="mini" type="primary" @click="getFather()">上一步</el-button>
        <el-button size="mini" type="primary" @click="handleCreateBusiStartDate()"
          >下一步</el-button
        >
      </div>
    </el-dialog>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :visible.sync="modelBusiStartDateDialogFormVisible">
      <el-form
        ref="dataForm"
        :model="objMsg"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="业务开始时间" prop="new_creation_date_column">
          <el-select
            v-model="loadAllRelAttrs.creation_date_column"
            placeholder="请选择类型"
            selected="loadAllRelAttrs.creation_date_column"
          >
            <el-option
              v-for="at in busiDateList"
              :key="at.key"
              :label="at.key"
              :value="at.key"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="业务结束时间" prop="new_finish_date_column">
          <el-select
            v-model="loadAllRelAttrs.finish_date_column"
            placeholder="请选择类型"
            selected="loadAllRelAttrs.finish_date_column"
          >
            <el-option
              v-for="at in busiDateList"
              :key="at.key"
              :label="at.key"
              :value="at.key"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div size="mini" slot="footer" class="dialog-footer">
        <el-button size="mini" @click="hide">取消</el-button>
        <el-button size="mini" type="primary" @click="getAllAttrsTreeByModelCode()"
          >上一步</el-button
        >

        <el-button size="mini" type="primary" @click="commitModelObjAttr()">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>


<style>
.transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
}
</style>

<script>
import {
  getAllAttrsTreeByModelCode,
  addContentObjectAttrsRel,
  updateContentObjectAttrsRel,
} from "@/api/contentObjectManage";
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "model-attr-rel",
  directives: { elDragDialog },
  props: {
    modelCode: {
      required: false,
      type: String,
    },
    modelType: {
      required: false,
      type: String,
    },
    model_attr_dialogStatus: {
      require: true,
      type: String,
    },
    objMsg: {
      require: true,
      type: Object,
    },
    modelUpdateState: {
      require: true,
      type: Boolean,
    },
  },
  data: function () {
    return {
      notChoiceDataList: [],
      value: [1],
      choiceDataList: [],
      modelAttrdialogFormVisible: false,
      modelBusiStartDateDialogFormVisible: false,
      busiDateList: [],
      modelAttrTitle: "配置模型属性",
      loadAllRelAttrs: {
        creation_date_column: "",
        finish_date_column: "",
      },
      listLoading: true,
      
    };
  },
  created() {},
  methods: {
    show() {
      this.modelBusiStartDateDialogFormVisible = false;
      this.modelAttrdialogFormVisible = true;
      this.clearSearch();
    },
    hide() {
      this.modelAttrdialogFormVisible = false;
      this.modelBusiStartDateDialogFormVisible = false;
    },
   clearSearch() { 
    const {transferCom} = this.$refs;
    if(transferCom){
      transferCom.$children['0']._data.query = '';
      transferCom.$children['3']._data.query = '';
    }
   },
    commitModelObjAttr() {
      this.objMsg.creation_date_column =
        this.loadAllRelAttrs.creation_date_column;
      this.objMsg.finish_date_column = this.loadAllRelAttrs.finish_date_column;

      if ("0" == this.modelType) {
        //索引对象必选配置业务开始时间
        let createDateColumn = this.objMsg.creation_date_column;
        if (createDateColumn == null || createDateColumn.length == 0) {
          alert("索引对象必选配置[业务开始时间]");
          return;
        }
      }
      var choiceAttrKeys = "";
      for (var i = 0; i < this.choiceDataList.length; i++) {
        if (choiceAttrKeys.length > 0) {
          choiceAttrKeys += ",";
        }
        choiceAttrKeys += this.choiceDataList[i];
      }
      this.$message.info("提交中...");
      if (this.modelUpdateState) {
        //表示更新
        updateContentObjectAttrsRel({
          attributeCode: choiceAttrKeys,
          objMsg: this.objMsg,
        }).then(() => {
          this.hide();
          this.$emit("getAllObj");
          this.$notify({
            title: "Success",
            message: "Created Successfully",
            type: "success",
            duration: 1000,
          });
          // this.$emit("resetModelForm");
        });
      } else {
        //新增
        addContentObjectAttrsRel({
          attributeCode: choiceAttrKeys,
          objMsg: this.objMsg,
        }).then(() => {
          this.hide();
          this.$emit("getAllObj");
          this.$notify({
            title: "Success",
            message: "Created Successfully",
            type: "success",
            duration: 1000,
          });
          // this.$emit("resetModelForm");
        });
      }
    },

    getAllAttrsTreeByModelCode(modelcode) {
      this.notChoiceDataList = [];
      this.choiceDataList = [];
      getAllAttrsTreeByModelCode(this.modelCode).then((response) => {
        let allattrsTree = response.msg;
        allattrsTree.map((item) => {
          this.notChoiceDataList.push({
            key: item.code,
            label: item.label,
          });
          if (item.isRel) {
            this.choiceDataList.push(item.code);
          }
        });
        this.show();
      });
    },
    handleCreateBusiStartDate() {
      this.busiDateList = [];
      let firstCreateDateColumn = this.objMsg.creation_date_column;
      let firstFinishDateColumn = this.objMsg.finish_date_column;
      let hasCreateDateColumn = false;
      let hasFinishDateColumn = false;
      this.loadAllRelAttrs.creation_date_column = "";
      this.loadAllRelAttrs.finish_date_column = "";

      for (var i = 0; i < this.choiceDataList.length; i++) {
        if (
          hasCreateDateColumn ||
          this.choiceDataList[i] == firstCreateDateColumn
        ) {
          hasCreateDateColumn = true;
        }
        if (
          hasFinishDateColumn ||
          this.choiceDataList[i] == firstFinishDateColumn
        ) {
          hasFinishDateColumn = true;
        }
        this.busiDateList.push({
          key: this.choiceDataList[i],
          // label:
        });
      }
      if (hasCreateDateColumn) {
        this.loadAllRelAttrs.creation_date_column = firstCreateDateColumn;
      }
      if (hasFinishDateColumn) {
        this.loadAllRelAttrs.finish_date_column = firstFinishDateColumn;
      }

      this.modelAttrdialogFormVisible = false;
      this.modelBusiStartDateDialogFormVisible = true;
    },
    getFather() {
      if (this.modelUpdateState == true) {
        this.$emit("test");
      } else {
        this.$emit("testt");
      }
    },
  },
};
</script>
<style scoped>
.edit_dev >>> .el-transfer-panel {
     width:350px;
   }
</style>
