<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-2.5.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
    <!-- 表主键自增配置 -->


    <bean id="roleInfo_roleID"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>

    <bean id="logRule_logID"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>

    <bean id="cachenodeInfo_nodeID"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>

    <bean id="contentServerGroup_groupID"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>

    <bean id="configInfoSynchroID"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>
    <bean id="volumeID"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>
    <bean id="unityAccessServerId"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>
    <bean id="unityAccessServerGroupId"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>
    <bean id="task_no_incrementer"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>
    <bean id="schedule_id_incrementer"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>
    <bean id="group_cmodel_rel_id_incrementer"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>
    <bean id="insNo_dms_rel_id_incrementer"
          class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"></property>
    </bean>
    <bean id="param_config_par_id_incrementer" class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"/>
    </bean>
    <bean id="param_show_par_show_id_incrementer" class="com.sunyard.console.common.database.ExtSqlServerDataFieldMaxValueIncrementer">
        <property name="jdbcTemplate" ref="jdbcTemplate"/>
    </bean>
</beans>