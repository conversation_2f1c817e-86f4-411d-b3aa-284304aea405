2025-03-12 17:33:36.441 [OrganNo_000001_UserNo_admin] [d9466e4e0bec2388/7b2e25ffca37da6c] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-12 17:33:36.717 [OrganNo_000001_UserNo_admin] [d9466e4e0bec2388/7b2e25ffca37da6c] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-12 17:33:36.815 [OrganNo_000001_UserNo_admin] [9e1033df3e3b4e08/353dd693f40db9e8] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-03-12 17:33:36.966 [OrganNo_000001_UserNo_admin] [9e1033df3e3b4e08/353dd693f40db9e8] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-03-12 17:37:42.649 [OrganNo_000001_UserNo_admin] [4302e4aeb1608c2a/3ff24333805064db] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-12 17:37:42.857 [OrganNo_000001_UserNo_admin] [4302e4aeb1608c2a/3ff24333805064db] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-12 17:37:42.938 [OrganNo_000001_UserNo_admin] [2cd3f159ba358b12/719e9cdca817d154] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-12 17:37:43.091 [OrganNo_000001_UserNo_admin] [2cd3f159ba358b12/719e9cdca817d154] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-12 17:37:54.018 [OrganNo_000001_UserNo_admin] [ca39b035b1f9ecb7/350376fb3273d807] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-03-12 17:37:54.138 [OrganNo_000001_UserNo_admin] [ca39b035b1f9ecb7/350376fb3273d807] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-03-12 17:37:57.179 [OrganNo_000001_UserNo_admin] [797ebe1158292df4/1a28863ff5dcea75] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-12 17:37:57.334 [OrganNo_000001_UserNo_admin] [797ebe1158292df4/1a28863ff5dcea75] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-12 17:37:57.395 [OrganNo_000001_UserNo_admin] [c2fa28b5043b94d8/1accd41c49c9ffee] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"appLocationQuery",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-03-12 17:37:57.534 [OrganNo_000001_UserNo_admin] [c2fa28b5043b94d8/1accd41c49c9ffee] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-12 17:37:58.927 [OrganNo_000001_UserNo_admin] [a02f9efe102e6b49/96bced7e133104e3] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-03-12 17:37:59.063 [OrganNo_000001_UserNo_admin] [a02f9efe102e6b49/96bced7e133104e3] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-03-12 17:38:01.107 [OrganNo_000001_UserNo_admin] [4a4feac266f8875d/16a9075909feefbe] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-12 17:38:01.248 [OrganNo_000001_UserNo_admin] [4a4feac266f8875d/16a9075909feefbe] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-12 17:38:01.325 [OrganNo_000001_UserNo_admin] [3922f9954fb47219/9eacc1a9e3554b62] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-03-12 17:38:01.450 [OrganNo_000001_UserNo_admin] [3922f9954fb47219/9eacc1a9e3554b62] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
