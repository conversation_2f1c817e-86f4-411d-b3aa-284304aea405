<template>
  <div class="app-container">
    <div class="filter-container">
       <el-select v-model="listQuery.group_id" placeholder="请选择存储服务器组" @change='getServers()'>
        <el-option
          v-for="item in storeGroupServers"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
       <el-select v-model="listQuery.server_id" placeholder="请选择存储服务器" @change="getSName">
              <el-option
                v-for="item in storeServers"
                :key="item.server_id"
                :label="item.server_name"
                  :value="item.server_id">

              </el-option>
       </el-select>
      <el-button
        v-if="this.hasPerm('logSearch')"
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain
        round
        @click="getLogs"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain
        round
        @click="handleclear"
      >
        清空
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >

     <el-table-column label="内容存储服务器" min-width="10%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.server_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="日志名称" min-width="10%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.log_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="日志路径" min-width="20%" align="center">
              <template slot-scope="{ row }">
                <span>{{ row.log_path }}</span>
              </template>
            </el-table-column>
      </el-table-column>

     <el-table-column
               label="操作"
               align="center"
               min-width="10%"
               class-name="small-padding fixed-width"
             >
               <template slot-scope="{ row, $index }">
                 <el-button v-if="hasPerm('logDown')"  type="primary"  icon="el-icon-edit" size="mini" @click="logDown(row)">
                   下载
                 </el-button>
               </template>
             </el-table-column>
           </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getContentServerGroup, getRelContentServerTree, logListByServerId} from '@/api/logDownLoad'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination

import global from '../../store/global.js'

export default {
  name: 'ComplexTable',
  components: { Pagination },
  directives: { waves,elDragDialog },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
       group_id: '',
       server_id: '',
       server_name: '',
        page: 1,
        limit: 20,
      },
      temp: {
        status: 'published',
        group_id:'',
      },
       storeGroupServers :  [],
       storeServers : [],
    }
  },

  created() {
    this.getServerGroup();
  },

  methods: {
    getServerGroup(){
        getContentServerGroup().then(response => {
        this.storeGroupServers = response.root;

      })
    },

    getServers(){
            getRelContentServerTree(this.listQuery).then(response => {
            this.storeServers = response.root;

          })
        },

    getSName(){
       let id  = this.listQuery.server_id;
       this.listQuery.server_name= "";
       let obj ={};
       obj = this.storeServers.find(item=> {
        return item.server_id===id
       });
       this.listQuery.server_name = obj.server_name;
    },


    handleclear() {
      this.listQuery.group_id= "";
      this.listQuery.server_id= ""
      this.listQuery.server_name= ""

    },

    getLogs() {
            logListByServerId(this.listQuery).then((response) => {
              this.list = response.root;
              this.total = Number(response.totalProperty)
            });
          },

           logDown(row) {
            window.open(row.url,'_blank');
           }
}
};
</script>
