import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'


/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard'
    }]
  }

]


export const asyncRoutes = [
  {
    path: '/contentModeling',
    component: Layout,
    redirect: 'noRedirect',
    name:'sdf',
    alwaysShow: true, // will always show the root menu
    meta: {title: '内容建模', icon: 'table',roles: ['1','2','3','22','23','26']
    },
    children: [
      {
        path: 'attributeManage',
        component: () => import('@/views/contentModeling/attributeManage'),
        name: 'attributeManage',
        meta: {
          title: '属性集管理',roles: ['1'], icon: 'component'
        }
      },
      {
        path: 'contentModelManagement',
        component: () => import('@/views/contentModeling/contentModelManagement'),
        name: 'contentModelManagement',
        meta: {
          title: '内容模型管理',roles: ['2'], icon: 'component'
        }
      },
      {
        path: 'historyTableManagement',
        component: () => import('@/views/contentModeling/historyTableManagement'),
        name: 'historyTableManagement',
        meta: {
          title: '历史表管理',roles: ['3'], icon: 'component'
        }
      },
      {
        path: 'configurationParameterManagement',
        component: () => import('@/views/contentModeling/configurationParameterManagement'),
        name: 'configurationParameterManagement',
        meta: {
          title: '配置参数管理',roles: ['23'], icon: 'component'
        }
      },
      {
        path: 'tagManagement',
        component: () => import('@/views/contentModeling/tagManagement'),
        name: 'tagManagement',
        meta: {
          title: '标签管理',roles: ['26'], icon: 'component'
        }
      }
    ]
  },

  {
    path: '/securityManagement',
    component: Layout,
    redirect: 'noRedirect',
    name: 'securityManagement',
    meta: {title: '安全管理', icon: 'table',roles: ['4','5','6']},
    children: [
      {
        path: 'userManagement',
        component: () => import('@/views/securityManagement/userManagement'),
        name: 'userManagement',
        meta: { title: '用户管理' ,roles: ['4'], icon: 'peoples'}
      },
      {
        path: 'roleManagement',
        component: () => import('@/views/securityManagement/roleManagement'),
        name: 'roleManagement',
        meta: { title: '角色管理',roles: ['5'], icon: 'user' }
      },
      {
        path: 'tokenManagement',
        component: () => import('@/views/securityManagement/tokenManagement'),
        name: 'tokenManagement',
        meta: { title: '令牌管理',roles: ['6'],icon: 'example'  }
      }
    ]
  },
  {
    path: '/contentStorageManagement',
    component: Layout,
    redirect: 'noRedirect',
    name: 'contentStorageManagement',
    meta: {
      title: '内容存储管理', icon:'table',roles: ['7','8']
    },
    children: [
      {
        path: '/storageServerManagement',
        component: () => import('@/views/contentStorageManagement/storageServerManagement'),
        name: 'storageServerManagement',
        meta: { title: '存储服务器管理',roles: ['7'],icon: 'international'  }
      },
      {
        path: '/storageServerGroupManagement',
        component: () => import('@/views/contentStorageManagement/storageServerGroupManagement'),
        name: 'storageServerGroupManagement',
        meta: { title: '存储服务器组管理',roles: ['8'],icon: 'international'  }
      }
    ]
  },
  {
    path: '/lifeCycleManagement',
    component: Layout,
    redirect: 'noRedirect',
    name: 'lifeCycleManagement',
    meta: {
      title: '生命周期管理',
      icon: 'table',
      roles: ['9','10']
    },
    children: [
      {
        path: '/cachePolicyManagement',
        component: () => import('@/views/lifeCycleManagement/cachePolicyManagement'),
        name: 'cachePolicyManagement',
        meta: { title: '缓存策略管理',roles: ['9'],icon: 'clipboard'  }
      },
      {
        path: '/taskManagement',
        component: () => import('@/views/lifeCycleManagement/taskManagement'),
        name: 'SelectExcel',
        meta: { title: '任务管理',roles: ['10'],icon: 'clipboard'  }
      }
    ]
  },
  {
    path: '/unityAccessManagement',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'unityAccessManagement',
    meta: { title: '统一接入服务管理', icon: 'table' ,roles: ['11']},
    children: [
      {
        path: '/unityAccess',
        component: () => import('@/views/unityAccessManagement/unityAccess'),
        name: 'unityAccess',
        meta: { title: '统一接入服务管理',icon: 'clipboard'  }
      }
    ]
  },

  {
    path: '/monitoringManagement',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'monitoringManagement',
    meta: { title: '监控管理', icon: 'table',roles: ['15','16','17','20','21','100','101','102','103','104','106','107','108'] },
    children: [
      {
        path: 'monitoringCenter',
        component: () => import('@/views/monitoringManagement/monitoringCenter'),
        name: 'monitoringCenter',
        meta: { title: '监控中心' ,roles: ['15'],icon: 'clipboard' }
      },
      // {
      //   path: 'dataMonitorManage',
      //   component: () => import('@/views/monitoringManagement/dataMonitorManage'),
      //   name: 'dataMonitorManage',
      //   meta: { title: '监控统计' ,roles: ['99'],icon: 'clipboard' }
      // },
      {
        path: 'dataStaticsManage',
        component: () => import('@/views/monitoringManagement/dataStaticsManage'),
        name: 'dataStaticsManage',
        meta: { title: '接口服务统计' ,roles: ['100'],icon: 'clipboard' }
      },
      {
        path: 'realDataScatterManage',
        component: () => import('@/views/monitoringManagement/realDataScatterManage'),
        name: 'realDataScatterManage',
        meta: { title: '接口耗时监控' ,roles: ['101'],icon: 'clipboard' }
      },
      {
        path: 'socketConnManage',
        component: () => import('@/views/monitoringManagement/socketConnManage'),
        name: 'socketConnManage',
        meta: { title: '模型socket连接数统计' ,roles: ['102'],icon: 'clipboard' }
      },
      {
        path: 'tcpStatusManage',
        component: () => import('@/views/monitoringManagement/tcpStatusManage'),
        name: 'tcpStatusManage',
        meta: { title: 'TCP状态监控' ,roles: ['103'],icon: 'clipboard' }
      },
      {
        path: 'schedulerTaskManage',
        component: () => import('@/views/monitoringManagement/schedulerTaskManage'),
        name: 'schedulerTaskManage',
        meta: { title: '后台定时任务数据统计' ,roles: ['104'],icon: 'clipboard' }
      },
      {
        path: 'socketConnNumManage',
        component: () => import('@/views/monitoringManagement/socketConnNumManage'),
        name: 'socketConnNumManage',
        meta: { title: 'socket线程监控' ,roles: ['106'],icon: 'clipboard' }
      },
      {
        path: 'migrateThreadPoolManage',
        component: () => import('@/views/monitoringManagement/migrateThreadPoolManage'),
        name: 'migrateThreadPoolManage',
        meta: { title: '迁移线程池监控' ,roles: ['107'],icon: 'clipboard' }
      },
      {
        path: 'migrateBlockQueueManage',
        component: () => import('@/views/monitoringManagement/migrateBlockQueueManage'),
        name: 'migrateBlockQueueManage',
        meta: { title: '迁移队列监控' ,roles: ['108'],icon: 'clipboard' }
      },
      {
        path: 'migrationFailureManagement',
        component: () => import('@/views/monitoringManagement/migrationFailureManagement'),
        name: 'migrationFailureManagement',
        meta: { title: '迁移失败管理' ,roles: ['16'],icon: 'clipboard' }
      },
      {
        path: 'synchronizationStatus',
        component: () => import('@/views/monitoringManagement/synchronizationStatus'),
        name: 'synchronizationStatus',
        meta: { title: '同步状态管理' ,roles: ['17'],icon: 'clipboard' }
      },
      // {
      //   path: 'migrationFile',
      //   component: () => import('@/views/monitoringManagement/migrationFile'),
      //   name: 'migrationFile',
      //   meta: { title: '迁移文件管理',roles: ['20'] }
      // },
       {
         path: 'recentFailure',
         component: () => import('@/views/monitoringManagement/recentFailure'),
         name: 'recentFailure',
         meta: { title: '近线失败管理',roles: ['21'] ,icon: 'clipboard' }
       }
    ]
  },
  {
    path: '/organizationalManagement',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'organizationalManagement',
    meta: { title: '机构管理', icon: 'table' ,roles: ['18']},
    children: [
      {
        path: 'organization',
        component: () => import('@/views/organizationalManagement/organization'),
        name: 'organization',
        meta: { title: '机构配置',icon: 'clipboard'  }
      }
    ]
  },
  {
    path: '/offlineManagement',
    component: Layout,
    redirect: 'noRedirect',
    alwaysShow: true,
    name: 'offlineManagement',
    meta: { title: 'offLine管理', icon: 'table',roles: ['201'] },
    children: [
      {
        path: 'index',
        component: () => import('@/views/offlineManagement/index'),
        name: 'index',
        meta: { title: 'offline管理' ,icon: 'clipboard' }
      }
    ]
  },
  {
    path: '/logManagement',
    component: Layout,
    redirect: 'noRedirect',
    name: 'logManagement',
    meta: {
      title: '日志管理',
      icon: 'table',
      roles: ['12','13']
    },
    children: [
      {
        path: '/logPolicyManagement',
        component: () => import('@/views/logManagement/logPolicyManagement'),
        name: 'logPolicyManagement',
        meta: { title: '日志策略管理',roles: ['12'],icon: 'clipboard'  }
      },
      {
        path: '/logFileDownload',
        component: () => import('@/views/logManagement/logFileDownload'),
        name: 'logFileDownload',
        meta: { title: '日志文件下载',roles: ['13'],icon: 'clipboard'  }
      }
    ]
  }
  ]




const createRouter = () => new Router({
  //mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  router.options.routes=[];
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
