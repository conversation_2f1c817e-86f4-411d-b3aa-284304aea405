2025-07-25 00:00:01.103 [] [/] [Timer-6318] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:00:01.106 [] [/] [Timer-6318] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:00:01.106 [] [/] [Timer-6318] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:00:01.107 [] [/] [Timer-6318] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:00:01.108 [] [/] [Timer-6318] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:00:32.125 [] [/] [Timer-6319] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:00:32.126 [] [/] [Timer-6319] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:00:32.127 [] [/] [Timer-6319] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:00:32.128 [] [/] [Timer-6319] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:00:32.128 [] [/] [Timer-6319] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:01:03.138 [] [/] [Timer-6320] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:01:03.139 [] [/] [Timer-6320] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:01:03.139 [] [/] [Timer-6320] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:01:03.140 [] [/] [Timer-6320] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:01:03.141 [] [/] [Timer-6320] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:01:34.155 [] [/] [Timer-6321] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:01:34.156 [] [/] [Timer-6321] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:01:34.156 [] [/] [Timer-6321] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:01:34.157 [] [/] [Timer-6321] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:01:34.158 [] [/] [Timer-6321] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:01:59.195 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:01:59.203 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:01:59.203 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:01:59.203 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:01:59.204 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:01:59.204 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:02:05.170 [] [/] [Timer-6322] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:02:05.170 [] [/] [Timer-6322] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:02:05.171 [] [/] [Timer-6322] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:02:05.171 [] [/] [Timer-6322] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:02:05.172 [] [/] [Timer-6322] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:02:36.186 [] [/] [Timer-6323] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:02:36.186 [] [/] [Timer-6323] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:02:36.187 [] [/] [Timer-6323] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:02:36.188 [] [/] [Timer-6323] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:02:36.188 [] [/] [Timer-6323] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:03:07.202 [] [/] [Timer-6324] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:03:07.202 [] [/] [Timer-6324] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:03:07.202 [] [/] [Timer-6324] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:03:07.203 [] [/] [Timer-6324] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:03:07.203 [] [/] [Timer-6324] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:03:38.214 [] [/] [Timer-6325] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:03:38.214 [] [/] [Timer-6325] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:03:38.214 [] [/] [Timer-6325] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:03:38.215 [] [/] [Timer-6325] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:03:38.215 [] [/] [Timer-6325] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:03:59.205 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:03:59.211 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:03:59.212 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:03:59.212 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:03:59.214 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:03:59.214 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:04:09.236 [] [/] [Timer-6326] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:04:09.236 [] [/] [Timer-6326] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:04:09.236 [] [/] [Timer-6326] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:04:09.237 [] [/] [Timer-6326] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:04:09.237 [] [/] [Timer-6326] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:04:38.359 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:04:40.265 [] [/] [Timer-6327] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:04:40.265 [] [/] [Timer-6327] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:04:40.266 [] [/] [Timer-6327] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:04:40.266 [] [/] [Timer-6327] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:04:40.267 [] [/] [Timer-6327] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:05:11.286 [] [/] [Timer-6328] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:05:11.286 [] [/] [Timer-6328] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:05:11.286 [] [/] [Timer-6328] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:05:11.287 [] [/] [Timer-6328] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:05:11.288 [] [/] [Timer-6328] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:05:42.312 [] [/] [Timer-6329] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:05:42.312 [] [/] [Timer-6329] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:05:42.312 [] [/] [Timer-6329] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:05:42.313 [] [/] [Timer-6329] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:05:42.313 [] [/] [Timer-6329] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:05:59.218 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:05:59.225 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:05:59.225 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:05:59.226 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:05:59.227 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:05:59.227 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:06:13.324 [] [/] [Timer-6330] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:06:13.324 [] [/] [Timer-6330] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:06:13.324 [] [/] [Timer-6330] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:06:13.325 [] [/] [Timer-6330] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:06:13.325 [] [/] [Timer-6330] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:06:44.346 [] [/] [Timer-6331] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:06:44.346 [] [/] [Timer-6331] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:06:44.346 [] [/] [Timer-6331] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:06:44.347 [] [/] [Timer-6331] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:06:44.347 [] [/] [Timer-6331] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:07:15.373 [] [/] [Timer-6332] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:07:15.373 [] [/] [Timer-6332] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:07:15.373 [] [/] [Timer-6332] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:07:15.375 [] [/] [Timer-6332] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:07:15.375 [] [/] [Timer-6332] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:07:46.393 [] [/] [Timer-6333] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:07:46.394 [] [/] [Timer-6333] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:07:46.394 [] [/] [Timer-6333] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:07:46.395 [] [/] [Timer-6333] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:07:46.395 [] [/] [Timer-6333] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:07:59.219 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:07:59.226 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:07:59.226 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:07:59.226 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:07:59.228 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:07:59.228 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:08:17.405 [] [/] [Timer-6334] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:08:17.405 [] [/] [Timer-6334] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:08:17.405 [] [/] [Timer-6334] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:08:17.406 [] [/] [Timer-6334] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:08:17.406 [] [/] [Timer-6334] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:08:48.416 [] [/] [Timer-6335] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:08:48.416 [] [/] [Timer-6335] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:08:48.416 [] [/] [Timer-6335] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:08:48.417 [] [/] [Timer-6335] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:08:48.417 [] [/] [Timer-6335] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:09:19.446 [] [/] [Timer-6336] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:09:19.446 [] [/] [Timer-6336] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:09:19.446 [] [/] [Timer-6336] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:09:19.447 [] [/] [Timer-6336] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:09:19.447 [] [/] [Timer-6336] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:09:38.362 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:09:50.474 [] [/] [Timer-6337] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:09:50.474 [] [/] [Timer-6337] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:09:50.474 [] [/] [Timer-6337] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:09:50.475 [] [/] [Timer-6337] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:09:50.476 [] [/] [Timer-6337] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:09:59.224 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:09:59.232 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:09:59.232 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:09:59.233 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:09:59.234 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:09:59.234 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:10:21.490 [] [/] [Timer-6338] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:10:21.490 [] [/] [Timer-6338] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:10:21.490 [] [/] [Timer-6338] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:10:21.491 [] [/] [Timer-6338] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:10:21.491 [] [/] [Timer-6338] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:10:52.514 [] [/] [Timer-6339] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:10:52.514 [] [/] [Timer-6339] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:10:52.515 [] [/] [Timer-6339] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:10:52.515 [] [/] [Timer-6339] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:10:52.516 [] [/] [Timer-6339] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:11:23.540 [] [/] [Timer-6340] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:11:23.540 [] [/] [Timer-6340] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:11:23.541 [] [/] [Timer-6340] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:11:23.541 [] [/] [Timer-6340] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:11:23.542 [] [/] [Timer-6340] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:11:54.554 [] [/] [Timer-6341] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:11:54.554 [] [/] [Timer-6341] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:11:54.554 [] [/] [Timer-6341] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:11:54.555 [] [/] [Timer-6341] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:11:54.556 [] [/] [Timer-6341] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:11:59.228 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:11:59.235 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:11:59.235 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:11:59.235 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:11:59.236 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:11:59.237 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:12:25.573 [] [/] [Timer-6342] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:12:25.573 [] [/] [Timer-6342] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:12:25.573 [] [/] [Timer-6342] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:12:25.574 [] [/] [Timer-6342] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:12:25.574 [] [/] [Timer-6342] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:12:56.589 [] [/] [Timer-6343] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:12:56.589 [] [/] [Timer-6343] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:12:56.589 [] [/] [Timer-6343] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:12:56.590 [] [/] [Timer-6343] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:12:56.590 [] [/] [Timer-6343] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:13:27.604 [] [/] [Timer-6344] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:13:27.604 [] [/] [Timer-6344] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:13:27.604 [] [/] [Timer-6344] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:13:27.605 [] [/] [Timer-6344] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:13:27.605 [] [/] [Timer-6344] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:13:58.620 [] [/] [Timer-6345] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:13:58.620 [] [/] [Timer-6345] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:13:58.620 [] [/] [Timer-6345] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:13:58.621 [] [/] [Timer-6345] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:13:58.621 [] [/] [Timer-6345] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:13:59.238 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:13:59.244 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:13:59.245 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:13:59.245 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:13:59.246 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:13:59.246 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:14:29.643 [] [/] [Timer-6346] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:14:29.643 [] [/] [Timer-6346] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:14:29.643 [] [/] [Timer-6346] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:14:29.644 [] [/] [Timer-6346] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:14:29.644 [] [/] [Timer-6346] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:14:38.375 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:15:00.662 [] [/] [Timer-6347] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:15:00.662 [] [/] [Timer-6347] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:15:00.663 [] [/] [Timer-6347] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:15:00.664 [] [/] [Timer-6347] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:15:00.664 [] [/] [Timer-6347] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:15:31.679 [] [/] [Timer-6348] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:15:31.679 [] [/] [Timer-6348] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:15:31.679 [] [/] [Timer-6348] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:15:31.680 [] [/] [Timer-6348] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:15:31.680 [] [/] [Timer-6348] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:15:59.239 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:15:59.247 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:15:59.248 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:15:59.248 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:15:59.249 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:15:59.249 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:16:02.700 [] [/] [Timer-6349] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:16:02.700 [] [/] [Timer-6349] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:16:02.700 [] [/] [Timer-6349] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:16:02.701 [] [/] [Timer-6349] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:16:02.701 [] [/] [Timer-6349] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:16:33.707 [] [/] [Timer-6350] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:16:33.708 [] [/] [Timer-6350] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:16:33.708 [] [/] [Timer-6350] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:16:33.709 [] [/] [Timer-6350] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:16:33.709 [] [/] [Timer-6350] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:17:04.727 [] [/] [Timer-6351] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:17:04.728 [] [/] [Timer-6351] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:17:04.728 [] [/] [Timer-6351] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:17:04.729 [] [/] [Timer-6351] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:17:04.729 [] [/] [Timer-6351] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:17:35.736 [] [/] [Timer-6352] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:17:35.736 [] [/] [Timer-6352] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:17:35.736 [] [/] [Timer-6352] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:17:35.737 [] [/] [Timer-6352] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:17:35.738 [] [/] [Timer-6352] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:17:59.251 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:17:59.258 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:17:59.259 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:17:59.259 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:17:59.260 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:17:59.260 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:18:06.766 [] [/] [Timer-6353] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:18:06.766 [] [/] [Timer-6353] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:18:06.767 [] [/] [Timer-6353] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:18:06.767 [] [/] [Timer-6353] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:18:06.768 [] [/] [Timer-6353] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:18:37.786 [] [/] [Timer-6354] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:18:37.786 [] [/] [Timer-6354] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:18:37.786 [] [/] [Timer-6354] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:18:37.787 [] [/] [Timer-6354] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:18:37.787 [] [/] [Timer-6354] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:19:08.797 [] [/] [Timer-6355] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:19:08.798 [] [/] [Timer-6355] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:19:08.798 [] [/] [Timer-6355] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:19:08.799 [] [/] [Timer-6355] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:19:08.800 [] [/] [Timer-6355] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:19:38.377 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:19:39.821 [] [/] [Timer-6356] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:19:39.821 [] [/] [Timer-6356] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:19:39.821 [] [/] [Timer-6356] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:19:39.822 [] [/] [Timer-6356] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:19:39.822 [] [/] [Timer-6356] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:19:59.259 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:19:59.266 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:19:59.267 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:19:59.267 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:19:59.268 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:19:59.268 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:20:10.828 [] [/] [Timer-6357] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:20:10.829 [] [/] [Timer-6357] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:20:10.829 [] [/] [Timer-6357] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:20:10.829 [] [/] [Timer-6357] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:20:10.831 [] [/] [Timer-6357] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:20:41.847 [] [/] [Timer-6358] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:20:41.848 [] [/] [Timer-6358] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:20:41.848 [] [/] [Timer-6358] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:20:41.849 [] [/] [Timer-6358] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:20:41.849 [] [/] [Timer-6358] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:21:12.862 [] [/] [Timer-6359] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:21:12.863 [] [/] [Timer-6359] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:21:12.863 [] [/] [Timer-6359] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:21:12.864 [] [/] [Timer-6359] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:21:12.864 [] [/] [Timer-6359] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:21:43.874 [] [/] [Timer-6360] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:21:43.874 [] [/] [Timer-6360] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:21:43.874 [] [/] [Timer-6360] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:21:43.875 [] [/] [Timer-6360] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:21:43.875 [] [/] [Timer-6360] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:21:59.273 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:21:59.282 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:21:59.283 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:21:59.283 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:21:59.284 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:21:59.284 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:22:14.879 [] [/] [Timer-6361] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:22:14.879 [] [/] [Timer-6361] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:22:14.879 [] [/] [Timer-6361] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:22:14.880 [] [/] [Timer-6361] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:22:14.880 [] [/] [Timer-6361] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:22:45.893 [] [/] [Timer-6362] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:22:45.893 [] [/] [Timer-6362] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:22:45.894 [] [/] [Timer-6362] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:22:45.895 [] [/] [Timer-6362] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:22:45.895 [] [/] [Timer-6362] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:23:16.898 [] [/] [Timer-6363] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:23:16.899 [] [/] [Timer-6363] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:23:16.899 [] [/] [Timer-6363] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:23:16.899 [] [/] [Timer-6363] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:23:16.900 [] [/] [Timer-6363] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:23:47.904 [] [/] [Timer-6364] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:23:47.904 [] [/] [Timer-6364] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:23:47.904 [] [/] [Timer-6364] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:23:47.905 [] [/] [Timer-6364] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:23:47.905 [] [/] [Timer-6364] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:23:59.280 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:23:59.287 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:23:59.287 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:23:59.287 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:23:59.288 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:23:59.288 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:24:18.923 [] [/] [Timer-6365] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:24:18.923 [] [/] [Timer-6365] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:24:18.924 [] [/] [Timer-6365] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:24:18.924 [] [/] [Timer-6365] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:24:18.925 [] [/] [Timer-6365] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:24:38.393 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:24:49.936 [] [/] [Timer-6366] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:24:49.936 [] [/] [Timer-6366] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:24:49.936 [] [/] [Timer-6366] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:24:49.936 [] [/] [Timer-6366] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:24:49.937 [] [/] [Timer-6366] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:25:20.961 [] [/] [Timer-6367] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:25:20.961 [] [/] [Timer-6367] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:25:20.961 [] [/] [Timer-6367] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:25:20.962 [] [/] [Timer-6367] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:25:20.962 [] [/] [Timer-6367] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:25:51.971 [] [/] [Timer-6368] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:25:51.972 [] [/] [Timer-6368] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:25:51.972 [] [/] [Timer-6368] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:25:51.973 [] [/] [Timer-6368] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:25:51.974 [] [/] [Timer-6368] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:25:59.280 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:25:59.287 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:25:59.287 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:25:59.288 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:25:59.288 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:25:59.289 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:26:22.995 [] [/] [Timer-6369] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:26:22.995 [] [/] [Timer-6369] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:26:22.995 [] [/] [Timer-6369] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:26:22.996 [] [/] [Timer-6369] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:26:22.996 [] [/] [Timer-6369] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:26:54.013 [] [/] [Timer-6370] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:26:54.013 [] [/] [Timer-6370] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:26:54.014 [] [/] [Timer-6370] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:26:54.014 [] [/] [Timer-6370] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:26:54.015 [] [/] [Timer-6370] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:27:25.024 [] [/] [Timer-6371] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:27:25.024 [] [/] [Timer-6371] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:27:25.024 [] [/] [Timer-6371] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:27:25.025 [] [/] [Timer-6371] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:27:25.025 [] [/] [Timer-6371] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:27:56.032 [] [/] [Timer-6372] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:27:56.032 [] [/] [Timer-6372] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:27:56.032 [] [/] [Timer-6372] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:27:56.033 [] [/] [Timer-6372] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:27:56.034 [] [/] [Timer-6372] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:27:59.286 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:27:59.292 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:27:59.293 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:27:59.293 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:27:59.294 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:27:59.295 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:28:27.048 [] [/] [Timer-6373] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:28:27.048 [] [/] [Timer-6373] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:28:27.048 [] [/] [Timer-6373] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:28:27.049 [] [/] [Timer-6373] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:28:27.050 [] [/] [Timer-6373] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:28:58.053 [] [/] [Timer-6374] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:28:58.053 [] [/] [Timer-6374] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:28:58.053 [] [/] [Timer-6374] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:28:58.054 [] [/] [Timer-6374] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:28:58.054 [] [/] [Timer-6374] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:29:29.070 [] [/] [Timer-6375] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:29:29.070 [] [/] [Timer-6375] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:29:29.071 [] [/] [Timer-6375] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:29:29.071 [] [/] [Timer-6375] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:29:29.072 [] [/] [Timer-6375] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:29:38.403 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:29:59.298 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:29:59.305 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:29:59.305 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:29:59.306 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:29:59.306 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:29:59.307 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:30:00.083 [] [/] [Timer-6376] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:30:00.083 [] [/] [Timer-6376] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:30:00.083 [] [/] [Timer-6376] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:30:00.084 [] [/] [Timer-6376] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:30:00.084 [] [/] [Timer-6376] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:30:31.103 [] [/] [Timer-6377] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:30:31.103 [] [/] [Timer-6377] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:30:31.104 [] [/] [Timer-6377] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:30:31.105 [] [/] [Timer-6377] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:30:31.107 [] [/] [Timer-6377] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:31:02.116 [] [/] [Timer-6378] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:31:02.117 [] [/] [Timer-6378] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:31:02.118 [] [/] [Timer-6378] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:31:02.119 [] [/] [Timer-6378] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:31:02.119 [] [/] [Timer-6378] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:31:33.133 [] [/] [Timer-6379] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:31:33.133 [] [/] [Timer-6379] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:31:33.134 [] [/] [Timer-6379] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:31:33.135 [] [/] [Timer-6379] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:31:33.135 [] [/] [Timer-6379] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:31:59.309 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:31:59.316 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:31:59.316 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:31:59.316 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:31:59.317 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:31:59.317 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:32:04.139 [] [/] [Timer-6380] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:32:04.139 [] [/] [Timer-6380] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:32:04.139 [] [/] [Timer-6380] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:32:04.141 [] [/] [Timer-6380] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:32:04.141 [] [/] [Timer-6380] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:32:35.153 [] [/] [Timer-6381] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:32:35.154 [] [/] [Timer-6381] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:32:35.154 [] [/] [Timer-6381] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:32:35.155 [] [/] [Timer-6381] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:32:35.155 [] [/] [Timer-6381] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:33:06.168 [] [/] [Timer-6382] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:33:06.168 [] [/] [Timer-6382] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:33:06.169 [] [/] [Timer-6382] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:33:06.169 [] [/] [Timer-6382] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:33:06.170 [] [/] [Timer-6382] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:33:37.181 [] [/] [Timer-6383] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:33:37.181 [] [/] [Timer-6383] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:33:37.181 [] [/] [Timer-6383] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:33:37.182 [] [/] [Timer-6383] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:33:37.183 [] [/] [Timer-6383] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:33:59.320 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:33:59.326 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:33:59.326 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:33:59.327 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:33:59.328 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:33:59.328 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:34:08.195 [] [/] [Timer-6384] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:34:08.196 [] [/] [Timer-6384] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:34:08.196 [] [/] [Timer-6384] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:34:08.196 [] [/] [Timer-6384] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:34:08.196 [] [/] [Timer-6384] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:34:38.416 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:34:39.202 [] [/] [Timer-6385] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:34:39.202 [] [/] [Timer-6385] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:34:39.203 [] [/] [Timer-6385] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:34:39.203 [] [/] [Timer-6385] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:34:39.204 [] [/] [Timer-6385] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:35:10.223 [] [/] [Timer-6386] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:35:10.223 [] [/] [Timer-6386] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:35:10.223 [] [/] [Timer-6386] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:35:10.224 [] [/] [Timer-6386] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:35:10.224 [] [/] [Timer-6386] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:35:41.233 [] [/] [Timer-6387] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:35:41.233 [] [/] [Timer-6387] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:35:41.233 [] [/] [Timer-6387] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:35:41.234 [] [/] [Timer-6387] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:35:41.234 [] [/] [Timer-6387] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:35:59.335 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:35:59.342 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:35:59.343 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:35:59.343 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:35:59.344 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:35:59.344 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:36:12.255 [] [/] [Timer-6388] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:36:12.255 [] [/] [Timer-6388] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:36:12.256 [] [/] [Timer-6388] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:36:12.257 [] [/] [Timer-6388] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:36:12.257 [] [/] [Timer-6388] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:36:43.268 [] [/] [Timer-6389] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:36:43.268 [] [/] [Timer-6389] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:36:43.268 [] [/] [Timer-6389] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:36:43.269 [] [/] [Timer-6389] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:36:43.270 [] [/] [Timer-6389] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:37:14.299 [] [/] [Timer-6390] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:37:14.299 [] [/] [Timer-6390] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:37:14.300 [] [/] [Timer-6390] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:37:14.301 [] [/] [Timer-6390] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:37:14.301 [] [/] [Timer-6390] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:37:45.315 [] [/] [Timer-6391] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:37:45.315 [] [/] [Timer-6391] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:37:45.315 [] [/] [Timer-6391] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:37:45.316 [] [/] [Timer-6391] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:37:45.316 [] [/] [Timer-6391] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:37:59.347 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:37:59.353 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:37:59.354 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:37:59.354 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:37:59.355 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:37:59.355 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:38:16.336 [] [/] [Timer-6392] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:38:16.336 [] [/] [Timer-6392] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:38:16.336 [] [/] [Timer-6392] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:38:16.337 [] [/] [Timer-6392] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:38:16.337 [] [/] [Timer-6392] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:38:47.348 [] [/] [Timer-6393] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:38:47.348 [] [/] [Timer-6393] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:38:47.349 [] [/] [Timer-6393] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:38:47.349 [] [/] [Timer-6393] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:38:47.350 [] [/] [Timer-6393] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:39:18.356 [] [/] [Timer-6394] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:39:18.356 [] [/] [Timer-6394] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:39:18.357 [] [/] [Timer-6394] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:39:18.358 [] [/] [Timer-6394] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:39:18.358 [] [/] [Timer-6394] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:39:38.422 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:39:49.369 [] [/] [Timer-6395] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:39:49.369 [] [/] [Timer-6395] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:39:49.369 [] [/] [Timer-6395] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:39:49.371 [] [/] [Timer-6395] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:39:49.371 [] [/] [Timer-6395] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:39:59.351 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:39:59.359 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:39:59.359 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:39:59.359 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:39:59.360 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:39:59.360 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:40:20.387 [] [/] [Timer-6396] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:40:20.387 [] [/] [Timer-6396] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:40:20.387 [] [/] [Timer-6396] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:40:20.388 [] [/] [Timer-6396] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:40:20.388 [] [/] [Timer-6396] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:40:51.396 [] [/] [Timer-6397] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:40:51.396 [] [/] [Timer-6397] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:40:51.396 [] [/] [Timer-6397] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:40:51.397 [] [/] [Timer-6397] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:40:51.398 [] [/] [Timer-6397] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:41:22.417 [] [/] [Timer-6398] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:41:22.418 [] [/] [Timer-6398] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:41:22.418 [] [/] [Timer-6398] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:41:22.420 [] [/] [Timer-6398] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:41:22.420 [] [/] [Timer-6398] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:41:53.435 [] [/] [Timer-6399] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:41:53.435 [] [/] [Timer-6399] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:41:53.435 [] [/] [Timer-6399] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:41:53.436 [] [/] [Timer-6399] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:41:53.436 [] [/] [Timer-6399] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:41:59.354 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:41:59.361 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:41:59.362 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:41:59.362 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:41:59.362 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:41:59.362 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:42:24.445 [] [/] [Timer-6400] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:42:24.445 [] [/] [Timer-6400] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:42:24.445 [] [/] [Timer-6400] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:42:24.447 [] [/] [Timer-6400] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:42:24.447 [] [/] [Timer-6400] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:42:55.462 [] [/] [Timer-6401] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:42:55.462 [] [/] [Timer-6401] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:42:55.463 [] [/] [Timer-6401] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:42:55.463 [] [/] [Timer-6401] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:42:55.464 [] [/] [Timer-6401] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:43:26.477 [] [/] [Timer-6402] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:43:26.477 [] [/] [Timer-6402] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:43:26.477 [] [/] [Timer-6402] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:43:26.479 [] [/] [Timer-6402] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:43:26.480 [] [/] [Timer-6402] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:43:57.491 [] [/] [Timer-6403] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:43:57.491 [] [/] [Timer-6403] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:43:57.491 [] [/] [Timer-6403] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:43:57.492 [] [/] [Timer-6403] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:43:57.492 [] [/] [Timer-6403] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:43:59.365 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:43:59.372 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:43:59.372 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:43:59.373 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:43:59.374 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:43:59.374 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:44:28.513 [] [/] [Timer-6404] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:44:28.513 [] [/] [Timer-6404] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:44:28.513 [] [/] [Timer-6404] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:44:28.514 [] [/] [Timer-6404] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:44:28.515 [] [/] [Timer-6404] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:44:38.432 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:44:59.534 [] [/] [Timer-6405] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:44:59.534 [] [/] [Timer-6405] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:44:59.535 [] [/] [Timer-6405] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:44:59.536 [] [/] [Timer-6405] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:44:59.537 [] [/] [Timer-6405] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:45:30.551 [] [/] [Timer-6406] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:45:30.552 [] [/] [Timer-6406] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:45:30.552 [] [/] [Timer-6406] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:45:30.553 [] [/] [Timer-6406] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:45:30.553 [] [/] [Timer-6406] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:45:59.366 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:45:59.372 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:45:59.372 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:45:59.373 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:45:59.374 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:45:59.374 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:46:01.559 [] [/] [Timer-6407] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:46:01.560 [] [/] [Timer-6407] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:46:01.560 [] [/] [Timer-6407] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:46:01.562 [] [/] [Timer-6407] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:46:01.562 [] [/] [Timer-6407] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:46:32.571 [] [/] [Timer-6408] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:46:32.571 [] [/] [Timer-6408] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:46:32.571 [] [/] [Timer-6408] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:46:32.572 [] [/] [Timer-6408] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:46:32.572 [] [/] [Timer-6408] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:47:03.590 [] [/] [Timer-6409] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:47:03.590 [] [/] [Timer-6409] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:47:03.591 [] [/] [Timer-6409] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:47:03.591 [] [/] [Timer-6409] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:47:03.592 [] [/] [Timer-6409] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:47:34.611 [] [/] [Timer-6410] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:47:34.611 [] [/] [Timer-6410] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:47:34.611 [] [/] [Timer-6410] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:47:34.612 [] [/] [Timer-6410] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:47:34.613 [] [/] [Timer-6410] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:47:59.373 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:47:59.383 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:47:59.383 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:47:59.384 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:47:59.384 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:47:59.384 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:48:05.629 [] [/] [Timer-6411] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:48:05.630 [] [/] [Timer-6411] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:48:05.630 [] [/] [Timer-6411] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:48:05.631 [] [/] [Timer-6411] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:48:05.632 [] [/] [Timer-6411] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:48:36.647 [] [/] [Timer-6412] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:48:36.647 [] [/] [Timer-6412] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:48:36.647 [] [/] [Timer-6412] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:48:36.648 [] [/] [Timer-6412] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:48:36.649 [] [/] [Timer-6412] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:49:07.664 [] [/] [Timer-6413] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:49:07.665 [] [/] [Timer-6413] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:49:07.665 [] [/] [Timer-6413] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:49:07.667 [] [/] [Timer-6413] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:49:07.668 [] [/] [Timer-6413] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:49:38.434 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:49:38.681 [] [/] [Timer-6414] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:49:38.681 [] [/] [Timer-6414] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:49:38.682 [] [/] [Timer-6414] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:49:38.682 [] [/] [Timer-6414] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:49:38.683 [] [/] [Timer-6414] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:49:59.375 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:49:59.384 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:49:59.385 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:49:59.385 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:49:59.385 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:49:59.385 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:50:09.694 [] [/] [Timer-6415] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:50:09.694 [] [/] [Timer-6415] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:50:09.694 [] [/] [Timer-6415] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:50:09.696 [] [/] [Timer-6415] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:50:09.696 [] [/] [Timer-6415] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:50:40.707 [] [/] [Timer-6416] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:50:40.707 [] [/] [Timer-6416] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:50:40.708 [] [/] [Timer-6416] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:50:40.709 [] [/] [Timer-6416] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:50:40.709 [] [/] [Timer-6416] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:51:11.717 [] [/] [Timer-6417] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:51:11.717 [] [/] [Timer-6417] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:51:11.717 [] [/] [Timer-6417] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:51:11.718 [] [/] [Timer-6417] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:51:11.720 [] [/] [Timer-6417] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:51:42.734 [] [/] [Timer-6418] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:51:42.734 [] [/] [Timer-6418] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:51:42.734 [] [/] [Timer-6418] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:51:42.736 [] [/] [Timer-6418] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:51:42.736 [] [/] [Timer-6418] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:51:59.377 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:51:59.386 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:51:59.387 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:51:59.387 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:51:59.388 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:51:59.388 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:52:13.755 [] [/] [Timer-6419] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:52:13.755 [] [/] [Timer-6419] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:52:13.755 [] [/] [Timer-6419] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:52:13.756 [] [/] [Timer-6419] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:52:13.757 [] [/] [Timer-6419] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:52:44.771 [] [/] [Timer-6420] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:52:44.772 [] [/] [Timer-6420] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:52:44.772 [] [/] [Timer-6420] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:52:44.773 [] [/] [Timer-6420] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:52:44.773 [] [/] [Timer-6420] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:53:15.792 [] [/] [Timer-6421] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:53:15.792 [] [/] [Timer-6421] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:53:15.793 [] [/] [Timer-6421] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:53:15.794 [] [/] [Timer-6421] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:53:15.794 [] [/] [Timer-6421] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:53:46.818 [] [/] [Timer-6422] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:53:46.818 [] [/] [Timer-6422] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:53:46.818 [] [/] [Timer-6422] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:53:46.820 [] [/] [Timer-6422] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:53:46.820 [] [/] [Timer-6422] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:53:59.378 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:53:59.388 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:53:59.388 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:53:59.388 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:53:59.390 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:53:59.390 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:54:17.837 [] [/] [Timer-6423] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:54:17.837 [] [/] [Timer-6423] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:54:17.837 [] [/] [Timer-6423] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:54:17.837 [] [/] [Timer-6423] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:54:17.837 [] [/] [Timer-6423] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:54:38.446 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:54:48.845 [] [/] [Timer-6424] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:54:48.846 [] [/] [Timer-6424] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:54:48.846 [] [/] [Timer-6424] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:54:48.847 [] [/] [Timer-6424] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:54:48.847 [] [/] [Timer-6424] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:55:19.857 [] [/] [Timer-6425] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:55:19.857 [] [/] [Timer-6425] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:55:19.857 [] [/] [Timer-6425] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:55:19.858 [] [/] [Timer-6425] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:55:19.859 [] [/] [Timer-6425] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:55:50.879 [] [/] [Timer-6426] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:55:50.879 [] [/] [Timer-6426] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:55:50.879 [] [/] [Timer-6426] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:55:50.880 [] [/] [Timer-6426] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:55:50.880 [] [/] [Timer-6426] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:55:59.387 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:55:59.395 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:55:59.395 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:55:59.395 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:55:59.396 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:55:59.397 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:56:21.903 [] [/] [Timer-6427] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:56:21.904 [] [/] [Timer-6427] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:56:21.904 [] [/] [Timer-6427] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:56:21.905 [] [/] [Timer-6427] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:56:21.905 [] [/] [Timer-6427] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:56:52.922 [] [/] [Timer-6428] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:56:52.922 [] [/] [Timer-6428] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:56:52.923 [] [/] [Timer-6428] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:56:52.924 [] [/] [Timer-6428] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:56:52.924 [] [/] [Timer-6428] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:57:23.939 [] [/] [Timer-6429] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:57:23.939 [] [/] [Timer-6429] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:57:23.939 [] [/] [Timer-6429] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:57:23.940 [] [/] [Timer-6429] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:57:23.940 [] [/] [Timer-6429] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:57:54.948 [] [/] [Timer-6430] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:57:54.948 [] [/] [Timer-6430] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:57:54.948 [] [/] [Timer-6430] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:57:54.949 [] [/] [Timer-6430] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:57:54.950 [] [/] [Timer-6430] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:57:59.396 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:57:59.403 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:57:59.404 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:57:59.404 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:57:59.405 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:57:59.405 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 00:58:25.957 [] [/] [Timer-6431] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:58:25.957 [] [/] [Timer-6431] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:58:25.958 [] [/] [Timer-6431] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:58:25.958 [] [/] [Timer-6431] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:58:25.959 [] [/] [Timer-6431] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:58:56.982 [] [/] [Timer-6432] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:58:56.982 [] [/] [Timer-6432] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:58:56.983 [] [/] [Timer-6432] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:58:56.983 [] [/] [Timer-6432] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:58:56.984 [] [/] [Timer-6432] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:59:27.997 [] [/] [Timer-6433] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:59:27.997 [] [/] [Timer-6433] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:59:27.998 [] [/] [Timer-6433] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:59:27.999 [] [/] [Timer-6433] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:59:27.999 [] [/] [Timer-6433] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:59:38.460 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 00:59:59.020 [] [/] [Timer-6434] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:59:59.020 [] [/] [Timer-6434] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 00:59:59.021 [] [/] [Timer-6434] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 00:59:59.021 [] [/] [Timer-6434] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 00:59:59.021 [] [/] [Timer-6434] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 00:59:59.403 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:59:59.410 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 00:59:59.410 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 00:59:59.410 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 00:59:59.412 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 00:59:59.412 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 01:00:30.033 [] [/] [Timer-6435] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:00:30.033 [] [/] [Timer-6435] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:00:30.034 [] [/] [Timer-6435] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:00:30.034 [] [/] [Timer-6435] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:00:30.035 [] [/] [Timer-6435] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:01:01.046 [] [/] [Timer-6436] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:01:01.046 [] [/] [Timer-6436] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:01:01.047 [] [/] [Timer-6436] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:01:01.048 [] [/] [Timer-6436] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:01:01.048 [] [/] [Timer-6436] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:01:32.061 [] [/] [Timer-6437] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:01:32.062 [] [/] [Timer-6437] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:01:32.062 [] [/] [Timer-6437] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:01:32.063 [] [/] [Timer-6437] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:01:32.064 [] [/] [Timer-6437] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:01:59.404 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:01:59.411 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:01:59.411 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 01:01:59.412 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 01:01:59.413 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 01:01:59.413 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 01:02:03.082 [] [/] [Timer-6438] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:02:03.082 [] [/] [Timer-6438] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:02:03.082 [] [/] [Timer-6438] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:02:03.082 [] [/] [Timer-6438] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:02:03.083 [] [/] [Timer-6438] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:02:34.093 [] [/] [Timer-6439] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:02:34.094 [] [/] [Timer-6439] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:02:34.094 [] [/] [Timer-6439] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:02:34.095 [] [/] [Timer-6439] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:02:34.095 [] [/] [Timer-6439] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:03:05.104 [] [/] [Timer-6440] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:03:05.104 [] [/] [Timer-6440] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:03:05.104 [] [/] [Timer-6440] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:03:05.105 [] [/] [Timer-6440] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:03:05.105 [] [/] [Timer-6440] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:03:36.118 [] [/] [Timer-6441] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:03:36.118 [] [/] [Timer-6441] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:03:36.119 [] [/] [Timer-6441] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:03:36.119 [] [/] [Timer-6441] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:03:36.120 [] [/] [Timer-6441] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:03:59.419 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:03:59.426 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:03:59.426 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 01:03:59.426 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 01:03:59.427 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 01:03:59.428 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 01:04:07.144 [] [/] [Timer-6442] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:04:07.144 [] [/] [Timer-6442] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:04:07.144 [] [/] [Timer-6442] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:04:07.145 [] [/] [Timer-6442] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:04:07.145 [] [/] [Timer-6442] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:04:38.164 [] [/] [Timer-6443] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:04:38.164 [] [/] [Timer-6443] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:04:38.165 [] [/] [Timer-6443] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:04:38.165 [] [/] [Timer-6443] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:04:38.166 [] [/] [Timer-6443] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:04:38.466 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 01:05:09.174 [] [/] [Timer-6444] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:05:09.174 [] [/] [Timer-6444] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:05:09.175 [] [/] [Timer-6444] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:05:09.175 [] [/] [Timer-6444] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:05:09.176 [] [/] [Timer-6444] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:05:40.198 [] [/] [Timer-6445] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:05:40.198 [] [/] [Timer-6445] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:05:40.198 [] [/] [Timer-6445] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:05:40.199 [] [/] [Timer-6445] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:05:40.199 [] [/] [Timer-6445] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:05:59.430 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:05:59.438 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:05:59.439 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 01:05:59.439 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 01:05:59.440 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 01:05:59.440 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 01:06:11.229 [] [/] [Timer-6446] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:06:11.229 [] [/] [Timer-6446] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:06:11.229 [] [/] [Timer-6446] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:06:11.230 [] [/] [Timer-6446] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:06:11.231 [] [/] [Timer-6446] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:06:42.258 [] [/] [Timer-6447] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:06:42.258 [] [/] [Timer-6447] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:06:42.258 [] [/] [Timer-6447] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:06:42.259 [] [/] [Timer-6447] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:06:42.260 [] [/] [Timer-6447] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:07:13.277 [] [/] [Timer-6448] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:07:13.277 [] [/] [Timer-6448] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:07:13.277 [] [/] [Timer-6448] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:07:13.278 [] [/] [Timer-6448] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:07:13.279 [] [/] [Timer-6448] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:07:44.301 [] [/] [Timer-6449] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:07:44.301 [] [/] [Timer-6449] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:07:44.301 [] [/] [Timer-6449] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:07:44.302 [] [/] [Timer-6449] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:07:44.302 [] [/] [Timer-6449] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:07:59.431 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:07:59.440 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:07:59.441 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 01:07:59.441 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 01:07:59.442 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 01:07:59.442 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 01:08:15.321 [] [/] [Timer-6450] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:08:15.321 [] [/] [Timer-6450] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:08:15.321 [] [/] [Timer-6450] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:08:15.322 [] [/] [Timer-6450] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:08:15.322 [] [/] [Timer-6450] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:08:46.338 [] [/] [Timer-6451] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:08:46.338 [] [/] [Timer-6451] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:08:46.339 [] [/] [Timer-6451] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:08:46.340 [] [/] [Timer-6451] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:08:46.340 [] [/] [Timer-6451] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:09:17.357 [] [/] [Timer-6452] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:09:17.357 [] [/] [Timer-6452] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:09:17.358 [] [/] [Timer-6452] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:09:17.358 [] [/] [Timer-6452] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:09:17.359 [] [/] [Timer-6452] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:09:38.467 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-25 01:09:48.381 [] [/] [Timer-6453] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:09:48.382 [] [/] [Timer-6453] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:09:48.382 [] [/] [Timer-6453] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:09:48.382 [] [/] [Timer-6453] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:09:48.383 [] [/] [Timer-6453] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:09:52.959 [] [aef05474f20b443b/73bcb43b4cccb811] [http-nio-9110-exec-6] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:09:52.968 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.CommonMapper.executeSelect - ==>  Preparing: select distinct t2.FLOW_ID,t2.ACCOUNT,t2.CUSTOMER_ID,t2.LOAN_NO,t2.CLIENT_NAME,t2.AMOUNT,t2.OPP_ACCT,t2.OPP_CLIENT_NAME,t2.CD_FLAG,t2.SITE_NO,t2.OPERATOR_NO,t2.GRANT_NO,t2.OCCUR_DATE,t2.OCCUR_TIME,t2.TX_CODE,t2.CURRENCY_TYPE,t2.CHECK_FLAG,t2.LSERIAL_NO,t2.SEQ_ID, t2.content_id,t2.nps_flag,t2.datasource_id,t2.index_name from FL_FLOW_TB t2 where 1=1 and t2.OCCUR_DATE= '********' and t2.SITE_NO= '14902' and t2.OPERATOR_NO= '1488420' and t2.FLOW_ID= '*********'
2025-07-25 01:09:52.968 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.CommonMapper.executeSelect - ==> Parameters: 
2025-07-25 01:09:52.970 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.CommonMapper.executeSelect - <==      Total: 2
2025-07-25 01:09:52.971 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.CommonMapper.executeSelect - ==>  Preparing: select distinct t3.BATCH_ID, t3.OCCUR_DATE, t3.SITE_NO, t3.OPERATOR_NO, t3.CONTENT_ID, t3.INPUT_DATE, t2.FLOW_ID, t4.INCCODEIN_BATCH, t4.SERIAL_NO, o.ORGAN_NAME, t.TELLER_NAME from FL_FLOW_TB t2, BP_TMPBATCH_TB t3, BP_TMPDATA_1_TB t4, SM_ORGAN_TB o, SM_TELLER_TB t where t2.OCCUR_DATE=t3.OCCUR_DATE and t2.SITE_NO=t3.SITE_NO and t2.OPERATOR_NO=t3.OPERATOR_NO and t3.BATCH_ID=t4.BATCH_ID and t2.LSERIAL_NO=t4.SERIAL_NO and t3.SITE_NO=o.ORGAN_NO and t3.OPERATOR_NO=t.TELLER_NO and t2.OCCUR_DATE='********' and t2.SITE_NO='14902' and t2.OPERATOR_NO='1488420' and t2.FLOW_ID='*********'
2025-07-25 01:09:52.971 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.CommonMapper.executeSelect - ==> Parameters: 
2025-07-25 01:09:52.978 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.CommonMapper.executeSelect - <==      Total: 1
2025-07-25 01:09:52.978 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.T.selByInccodein - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB where batch_id=? and (inccodein_Batch=? or primary_Inccodein=?) order by ps_Level desc, inccodein_Batch asc
2025-07-25 01:09:52.978 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.T.selByInccodein - ==> Parameters: ********150114621932(String), 3(String), 3(String)
2025-07-25 01:09:52.979 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.c.d.T.selByInccodein - <==      Total: 1
2025-07-25 01:09:52.980 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.f.d.f.F.getFlowInfoList - ==>  Preparing: select FLOW_ID,ACCOUNT,CUSTOMER_ID,LOAN_NO,CLIENT_NAME,AMOUNT,OPP_ACCT,OPP_CLIENT_NAME,CD_FLAG,SITE_NO,OPERATOR_NO,GRANT_NO,OCCUR_DATE,OCCUR_TIME,TX_CODE,CURRENCY_TYPE,CHECK_FLAG,LSERIAL_NO,SEQ_ID from FL_FLOW_TB WHERE LSERIAL_NO=? and OPERATOR_NO =? and site_no=? and occur_date=? order by flow_id
2025-07-25 01:09:52.980 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.f.d.f.F.getFlowInfoList - ==> Parameters: mkbANH3xsDT12bM71Y8(String), 1488420(String), 14902(String), ********(String)
2025-07-25 01:09:52.981 [] [aef05474f20b443b/d364e7f989847566] [http-nio-9110-exec-6] DEBUG c.s.a.f.d.f.F.getFlowInfoList - <==      Total: 2
2025-07-25 01:09:53.169 [] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250725010953168040，切换日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-25 01:09:53.176 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-25 01:09:53.188 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-25 01:09:53.189 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:09:53.190 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg
2025-07-25 01:09:53.191 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-25 01:09:53.191 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String)
2025-07-25 01:09:53.193 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-25 01:09:53.205 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-25 01:09:53.205 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-25 01:09:53.208 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-25 01:09:53.217 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-25 01:09:53.217 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-25 01:09:53.217 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-25 01:09:53.217 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-25 01:09:53.218 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-25 01:09:53.218 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-25 01:09:53.219 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-25 01:09:53.219 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-25 01:09:53.311 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-25 01:09:53.311 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-25 01:09:53.312 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-25 01:09:53.312 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-25 01:09:53.312 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-25 01:09:53.312 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-25 01:09:53.312 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-25 01:09:53.315 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-25 01:09:53.315 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-25 01:09:53.351 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jjIwfylNB4FfO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-25 01:09:53.351 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jjIwfylNB4FfO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-25 01:09:53.351 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-25 01:09:53.351 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-25 01:09:53.352 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-25 01:09:53.352 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-25 01:09:53.352 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jjIwfylNB4FfO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-25 01:09:53.354 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-25 01:09:53.369 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jjIwfylNB4FfO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-25 01:09:53.369 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250725010953168040，清空日志输出文件标识：OrganNo_00023_UserNo_admin
2025-07-25 01:09:53.370 [] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 看图方法 操作方法: 查询影像url(单张)!请求IP地址: ************** 操作开始时间: 2025-07-25 01:09:53 操作结束时间: 2025-07-25 01:09:53!总共花费时间: 202 毫秒！
2025-07-25 01:09:59.439 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:09:59.448 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:09:59.449 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==>  Preparing: SELECT count(0) FROM bp_tmpbatch_tb T JOIN SM_ORGAN_DATA_TB s ON T.SITE_NO = S.ORGAN_NO WHERE T.BATCH_COMMIT = '1' AND T.NEED_PROCESS = '1' AND T.IS_INVALID = '1' AND T.PROCINSTID = '0' AND T.PROGRESS_FLAG = '10' AND EXISTS (SELECT 1 FROM FL_MON_DATA_TB C WHERE C.OCCUR_DATE = T.OCCUR_DATE AND C.OP_FLAG = '1')
2025-07-25 01:09:59.449 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - ==> Parameters: 
2025-07-25 01:09:59.450 [] [/] [Timer-1] DEBUG c.s.a.c.d.T.getScanBatch_COUNT - <==      Total: 1
2025-07-25 01:09:59.450 [] [/] [Timer-1] INFO  c.sunyard.ars.file.init.FlowTaskInit - 本次轮询未加载到需要进入工作流的批次任务!120000毫秒后执行下一次。
2025-07-25 01:10:19.387 [] [/] [Timer-6454] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:10:19.387 [] [/] [Timer-6454] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:10:19.387 [] [/] [Timer-6454] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:10:19.388 [] [/] [Timer-6454] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:10:19.388 [] [/] [Timer-6454] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:10:50.413 [] [/] [Timer-6455] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:10:50.413 [] [/] [Timer-6455] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:10:50.413 [] [/] [Timer-6455] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:10:50.414 [] [/] [Timer-6455] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:10:50.414 [] [/] [Timer-6455] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:11:21.438 [] [/] [Timer-6456] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:11:21.438 [] [/] [Timer-6456] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:11:21.439 [] [/] [Timer-6456] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:11:21.440 [] [/] [Timer-6456] DEBUG c.s.a.c.dao.OcrTaskMapper.select - <==      Total: 0
2025-07-25 01:11:21.440 [] [/] [Timer-6456] INFO  c.sunyard.ars.common.comm.ARSOcrWork - 开始进行OCR识别勾兑,当前无勾对任务。30000毫秒后执行下一次。
2025-07-25 01:11:52.455 [] [/] [Timer-6457] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:11:52.455 [] [/] [Timer-6457] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==>  Preparing: select re.TASK_ID, re.MESSAGE,re.BATCH_ID from ( select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba where tba.TASK_STATE='1' order by randnum asc ) re where rownum = 1
2025-07-25 01:11:52.456 [] [/] [Timer-6457] DEBUG c.s.a.c.dao.OcrTaskMapper.select - ==> Parameters: 
2025-07-25 01:11:59.452 [] [/] [Timer-1] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:12:02.463 [] [/] [Timer-6457] ERROR druid.sql.Statement - {conn-210010, pstmt-220024} execute error. select re.TASK_ID, re.MESSAGE,re.BATCH_ID from (
		select random() randnum, tba.TASK_ID, tba.MESSAGE,tba.BATCH_ID from OCR_TASK_TB tba
		where tba.TASK_STATE='1' order by randnum asc ) re  where rownum = 1
org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:382)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:167)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:156)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at sun.reflect.GeneratedMethodAccessor69.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy241.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy273.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.GeneratedMethodAccessor80.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy187.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy188.select(Unknown Source)
	at com.sunyard.ars.common.comm.ARSOcrWork.dowork(ARSOcrWork.java:135)
	at com.sunyard.ars.common.comm.ARSOcrWork$OcrWoker.run(ARSOcrWork.java:122)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.postgresql.core.VisibleBufferedInputStream.readMore(VisibleBufferedInputStream.java:161)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:128)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:113)
	at org.postgresql.core.VisibleBufferedInputStream.read(VisibleBufferedInputStream.java:73)
	at org.postgresql.core.PGStream.receiveChar(PGStream.java:453)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2119)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	... 43 common frames omitted
2025-07-25 01:12:02.464 [] [/] [Timer-6457] ERROR c.alibaba.druid.pool.DruidDataSource - {conn-210010} discard
org.postgresql.util.PSQLException: An I/O error occurred while sending to the backend.
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:382)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:490)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:408)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:167)
	at org.postgresql.jdbc.PgPreparedStatement.execute(PgPreparedStatement.java:156)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3446)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.filter.FilterEventAdapter.preparedStatement_execute(FilterEventAdapter.java:434)
	at com.alibaba.druid.filter.FilterChainImpl.preparedStatement_execute(FilterChainImpl.java:3444)
	at com.alibaba.druid.proxy.jdbc.PreparedStatementProxyImpl.execute(PreparedStatementProxyImpl.java:152)
	at com.alibaba.druid.pool.DruidPooledPreparedStatement.execute(DruidPooledPreparedStatement.java:483)
	at sun.reflect.GeneratedMethodAccessor69.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:59)
	at com.sun.proxy.$Proxy241.execute(Unknown Source)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:64)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:79)
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:63)
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:325)
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:156)
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:109)
	at com.github.pagehelper.PageInterceptor.intercept(PageInterceptor.java:151)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:62)
	at com.sun.proxy.$Proxy273.query(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:151)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:145)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:140)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectOne(DefaultSqlSession.java:76)
	at sun.reflect.GeneratedMethodAccessor80.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:427)
	at com.sun.proxy.$Proxy187.selectOne(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.selectOne(SqlSessionTemplate.java:160)
	at org.apache.ibatis.binding.MapperMethod.execute(MapperMethod.java:87)
	at org.apache.ibatis.binding.MapperProxy$PlainMethodInvoker.invoke(MapperProxy.java:145)
	at org.apache.ibatis.binding.MapperProxy.invoke(MapperProxy.java:86)
	at com.sun.proxy.$Proxy188.select(Unknown Source)
	at com.sunyard.ars.common.comm.ARSOcrWork.dowork(ARSOcrWork.java:135)
	at com.sunyard.ars.common.comm.ARSOcrWork$OcrWoker.run(ARSOcrWork.java:122)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at org.postgresql.core.VisibleBufferedInputStream.readMore(VisibleBufferedInputStream.java:161)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:128)
	at org.postgresql.core.VisibleBufferedInputStream.ensureBytes(VisibleBufferedInputStream.java:113)
	at org.postgresql.core.VisibleBufferedInputStream.read(VisibleBufferedInputStream.java:73)
	at org.postgresql.core.PGStream.receiveChar(PGStream.java:453)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2119)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:355)
	... 43 common frames omitted
2025-07-25 01:12:02.465 [] [/] [Timer-6457] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:12:09.466 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 01:12:19.472 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 01:12:19.472 [] [/] [Druid-ConnectionPool-Create-**********] INFO  c.a.d.pool.DruidAbstractDataSource - {dataSource-3} failContinuous is true
2025-07-25 01:12:29.988 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 01:12:40.496 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 01:12:51.008 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 01:12:51.516 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 01:12:52.032 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:39.550 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:40.092 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:40.600 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:41.112 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:41.623 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:42.141 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:42.664 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:43.213 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:43.903 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.NoRouteToHostException: No route to host: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:01:44.537 [] [/] [Timer-1] WARN  c.alibaba.druid.pool.DruidDataSource - get connection timeout retry : 1
2025-07-25 08:01:47.547 [] [/] [Timer-6457] WARN  c.alibaba.druid.pool.DruidDataSource - get connection timeout retry : 1
2025-07-25 08:01:54.605 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
2025-07-25 08:02:05.133 [] [/] [Druid-ConnectionPool-Create-**********] ERROR c.alibaba.druid.pool.DruidDataSource - create connection SQLException, url: jdbc:postgresql://**************:5432/sunaos, errorCode 0, state 08001
org.postgresql.util.PSQLException: 尝试连线已失败。
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:331)
	at org.postgresql.core.ConnectionFactory.openConnection(ConnectionFactory.java:49)
	at org.postgresql.jdbc.PgConnection.<init>(PgConnection.java:223)
	at org.postgresql.Driver.makeConnection(Driver.java:402)
	at org.postgresql.Driver.connect(Driver.java:261)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterEventAdapter.connection_connect(FilterEventAdapter.java:33)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1703)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1786)
	at com.alibaba.druid.pool.DruidDataSource$CreateConnectionThread.run(DruidDataSource.java:2910)
Caused by: java.net.SocketTimeoutException: connect timed out
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at org.postgresql.core.PGStream.createSocket(PGStream.java:241)
	at org.postgresql.core.PGStream.<init>(PGStream.java:98)
	at org.postgresql.core.v3.ConnectionFactoryImpl.tryConnect(ConnectionFactoryImpl.java:109)
	at org.postgresql.core.v3.ConnectionFactoryImpl.openConnectionImpl(ConnectionFactoryImpl.java:235)
	... 13 common frames omitted
