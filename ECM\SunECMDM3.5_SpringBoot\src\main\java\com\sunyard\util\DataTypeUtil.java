package com.sunyard.util;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.sunyard.ecm.server.bean.SQLBean;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;

/**
 * 数据库的数据类型
 * <AUTHOR>
 *
 */
public class DataTypeUtil {

	/**
	 * 按照数据库格式转换数据类型
	 * 1	字节
	 * 12	字符
	 * 3	浮点形
	 * 4	整形
	 * 5	短整形
	 * 8	双精度
	 * 91	日期
	 * 92	时间
	 * 93	时间戳记
	 * 2004	BLOB
	 * 2005	CLOB
	 * @throws SunECMException 
	 */
	public static Object convertData(String dataTypeCode, String value) throws SunECMException {
		
		if("1".equals(dataTypeCode) || "12".equals(dataTypeCode)){
			if(value == null || "null".equalsIgnoreCase(value)||"".equalsIgnoreCase(value))  {
				return "NULL";
			}
			return value;
		}
		if(value == null) {
			return SQLBean.NULL;
		}
		if("3".equals(dataTypeCode)){
			if("".equals(value) || "null".equalsIgnoreCase(value) || value == null){
				value = "0";
			}
			BigDecimal decimal = new BigDecimal(value);
			return decimal;
		}
		if("4".equals(dataTypeCode)){
			if("".equalsIgnoreCase(value) || "null".equalsIgnoreCase(value) || value == null){
				return Integer.valueOf("0");
			}
			return Integer.valueOf(value);
		}
		if("5".equals(dataTypeCode)){
			if("".equals(value)){
				return Short.valueOf("0");
			}
			return Short.valueOf(value);
		}
		if("8".equals(dataTypeCode)){
			if("".equals(value)){
				return Double.valueOf("0");
			}
			return Double.valueOf(value);
		}
		if("91".equals(dataTypeCode)){ // 日期格式必须为标准，暂定yyyyMMdd
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
			Date date;
			try {
				date = sdf.parse(value);
				return date;
			} catch (ParseException e) {
				throw new SunECMException(SunECMExceptionStatus.PARSING_ERROR, e.toString());
			}
		}
		if("92".equals(dataTypeCode)){ // 时间格式必须为标准，暂定HHmmss
			SimpleDateFormat sdf = new SimpleDateFormat("HHmmss");
			Date date;
			try {
				date = sdf.parse(value);
				return date;
			} catch (ParseException e) {
				throw new SunECMException(SunECMExceptionStatus.PARSING_ERROR, e.toString());
			}
		}
		if("93".equals(dataTypeCode)){ // 时间戳记格式必须为标准，暂定yyyyMMddHHmmss
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
			Date date;
			try {
				date = sdf.parse(value);
				return date;
			} catch (ParseException e) {
				throw new SunECMException(SunECMExceptionStatus.PARSING_ERROR, e.toString());
			}
		}
		if("2004".equals(dataTypeCode)){
			return value;
		}
		if("2005".equals(dataTypeCode)){
			return value;
		}
		if("7".equals(dataTypeCode)) {
			if(value == null || "null".equalsIgnoreCase(value)||"".equalsIgnoreCase(value))  {
				return "NULL";
			}
			return value;
		}
		throw new SunECMException(SunECMExceptionStatus.DATA_TYPE_ERROR,"不支持的数据类型代码" + dataTypeCode);
	}
//	public static void main(String[] args) {
//		String value ="";
//		Integer.valueOf(value);
//	}
}