<?xml version="1.0" encoding="UTF-8" ?>
<ROOT>
	<!--删除过期的检出记录 -->
	<SQL name="check_out_delete"><![CDATA[ 
	DELETE FROM CHECK_LOCK_INFO WHERE CONTENTID=? AND
		CHECKTIME {0} ?
]]>
	</SQL>

	<!-- 生成检出记录 -->
	<SQL name="check_out_insert"><![CDATA[ 
		INSERT INTO CHECK_LOCK_INFO (CONTENTID, CHECKFLAG,
		CHECKTIME) VALUES (?,?,?)
]]>
	</SQL>


	<!-- 生成检出记录 -->
	<SQL name="is_check_out"><![CDATA[ 
		SELECT l.CONTENTID FROM CHECK_LOCK_INFO l WHERE l.CONTENTID=? AND l.CHECKFLAG=?
]]>
	</SQL>







</ROOT>