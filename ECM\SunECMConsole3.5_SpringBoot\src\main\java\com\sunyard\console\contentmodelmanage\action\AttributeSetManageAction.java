package com.sunyard.console.contentmodelmanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.contentmodelmanage.bean.AttributeInfoBean;
import com.sunyard.console.contentmodelmanage.bean.AttributeTypeDefineInfoBean;
import com.sunyard.console.contentmodelmanage.bean.DesensitiveTypeInfoBean;
import com.sunyard.console.contentmodelmanage.dao.AttributeSetManageDao;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.sunyard.console.safemanage.bean.UserInfoBean;
import com.sunyard.console.safemanage.filter.SessionManage;
import com.sunyard.console.threadpoool.IssueUtils;

/**
 * <p>
 * Title: 属性集管理action
 * </p>
 * <p>
 * Description: 用于管理属性信息的获取,以及增删改等操作的action访问类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class AttributeSetManageAction extends BaseAction {
	/**
	 * 属性集管理的数据库操作对象
	 */
	@Autowired
	AttributeSetManageDao attributeSetManageDao;
	/**
	 * 日志对象
	 */
	private final static Logger log = LoggerFactory.getLogger(AttributeSetManageAction.class);

	public void setAttributeSetManageDao(AttributeSetManageDao attributeSetManageDao) {
		this.attributeSetManageDao = attributeSetManageDao;
	}

	public AttributeSetManageDao getAttributeSetManageDao() {
		return attributeSetManageDao;
	}

	/**
	 * 属性集查询action 将结果集组合成jsonStr，返回给页面
	 *
	 * @return null
	 */
	@RequestMapping("/contentModelManage/attributeInfoAction.action")
	@ResponseBody
	public String attributeInfoSearch(String data) {

		JSONObject modelJson = JSONObject.fromObject(data);
		int page = modelJson.getInt("page");
		int limit = modelJson.getInt("limit");
		int showSystemAttr = modelJson.getInt("showSystemAttr");
		String attributeCode = (String) modelJson.getOrDefault("attributeCode", "");
		String attributeName = (String) modelJson.getOrDefault("attributeName", "");

		log.info("--attributeInfoSearch(start)-->attributeCode:" + attributeCode);
		String jsonStr = null;
		int start = (page - 1) * limit;
		try {
			if (attributeCode != null) {
				attributeCode = attributeCode.toUpperCase();
			}
			List<AttributeInfoBean> attributeInfoList = attributeSetManageDao.attributeInfoList(attributeCode,
					attributeName, start + 1, limit, showSystemAttr);
			List<AttributeInfoBean> AllAttrInfoList = attributeSetManageDao.attributeInfoList(attributeCode,
					attributeName, showSystemAttr);
			int size = 0;
			if (AllAttrInfoList != null && AllAttrInfoList.size() > 0) {
				size = AllAttrInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(attributeInfoList, size, new AttributeInfoBean());
			log.debug("--attributeInfoSearch-->查询成功;attributeCode:" + attributeCode);
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取属性信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("属性集管理->获取属性列表失败" + e.toString());
			log.error("Exception:", e);
		}
		this.outJsonString(jsonStr);
		log.info("--attributeInfoSearch(over)");
		return null;
	}

	/**
	 * 新增属性
	 *
	 * @return
	 */
	@RequestMapping(value = "/contentModelManage/addAttributeInfoAction.action", method = RequestMethod.POST)
	@ResponseBody
	public String addAttributeInfo(String data) {
		if (StringUtil.stringIsNull(data)) {
			log.error("data is null");
			return null;
		}
		SessionManage smg = new SessionManage();

		UserInfoBean userBean = smg.getUserSession(getRequest().getHeader("X-Token"), getRequest());

		if (userBean == null ) {
			log.error("user is null");
			return null;
		}
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		int attributeIsNULL = 0;

		JSONObject modelJson = JSONObject.fromObject(data);
		AttributeInfoBean aib = new AttributeInfoBean();
		String attributeCode = modelJson.getString("attribute_code").toUpperCase();
		aib.setAttribute_code(attributeCode);
		String attributeIsNull = (String) modelJson.getOrDefault("attribute_isNull", "0");
		if (attributeIsNull != null) {
			attributeIsNULL = Integer.parseInt(attributeIsNull);
		}
		try {
//
			aib.setAttribute_name(URLDecoder.decode(modelJson.getString("attributeName"), "utf-8"));
			aib.setAttribute_default(URLDecoder.decode(modelJson.getString("attributeDefault"), "utf-8"));
		} catch (UnsupportedEncodingException e1) {
			log.error("decode attribute fields error", e1);
		}
		String attribute_type = modelJson.getString("attribute_type");
		if (!"4".equals(attribute_type) && !"3".equals(attribute_type)) {
			aib.setAttribute_length((modelJson.getInt("attribute_length") + ""));
		}else if("3".equals(attribute_type)) {
			aib.setAttribute_length(modelJson.getInt("precision") + ","+modelJson.getInt("decimals"));
		}
		aib.setAttribute_type(attribute_type);
		aib.setAttribute_isNull(attributeIsNULL);
		aib.setAttribute_upUser(userBean.getLogin_id());
		aib.setAttribute_is_multivalue(0);
		aib.setAttribute_value_mapping(0);
//		aib.setAttribute_otherName(modelJson.getString("attributeOtherName"));
//		aib.setAttribute_desenrule(modelJson.getString("desensitive_rule"));

		SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM-dd");
		sm.format(new Date());
		aib.setAttribute_upTime(sm.format(new Date()));
		log.debug("--addAttributeInfo-->aib:" + aib);
		String optionFlag = modelJson.getString("optionFlag");
//		String exisAttributeCode = modelJson.getString("exisAttributeCode");
		try {
			if (optionFlag.equals("create1")) {
				if (attributeSetManageDao.hasAttributeRecord(attributeCode) > 0) {
					jsonResp.put("success", false);
					jsonResp.put("message", "属性代码" + attributeCode + "]已被使用");
					jsonStr = jsonResp.toString();
					log.debug("属性代码" + attributeCode + "]已被使用");
					this.outJsonString(jsonStr);
					return null;
				}
				attributeSetManageDao.addAttributeInfo(aib);
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
				jsonResp.put("success", true);
				jsonResp.put("message", "新增属性成功!!");
				jsonResp.put("code", 20000);// TODO mock

			} else {
				boolean hasRef = attributeSetManageDao.hasRef(attributeCode);
				boolean onlyDesensitive = modelJson.getBoolean("onlyDesensitive");//是否只修改了脱敏字段

				if (hasRef && !onlyDesensitive) {
					jsonResp.put("success", true);
					jsonResp.put("message", "修改属性失败!!该属性正被使用!!");
				} else {
//					if (exisAttributeCode != null) {
//						exisAttributeCode = exisAttributeCode.toUpperCase();
//					}
//					aib.setAttribute_code(exisAttributeCode);
					attributeSetManageDao.updateAttributeInfo(aib);
					IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
					IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
					jsonResp.put("success", true);
					jsonResp.put("message", "配置修改成功!!");
					jsonResp.put("code", 20000);// TODO mock
				}
			}
			jsonStr = jsonResp.toString();
			log.debug("--addAttributeInfo-->新增属性成功;attributeCode:" + attributeCode);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置修改失败!!");
			jsonStr = jsonResp.toString();
			String msg = optionFlag.equals("create") ? "新增" : "修改";
			// 记录日志
			log.error("属性集管理->" + msg + "属性失败！" + e.toString());
			log.error("Exception:", e);
		}
		this.outJsonString(jsonStr);
		log.info("--addAttributeInfo(over)");
		return null;
	}

	/**
	 * 删除属性action
	 *
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/contentModelManage/delAttributeInfoAction.action")
	public String delAttributeInfo(String attributeCode) {
		log.info("--delAttributeInfo(start)-->attributeCode:" + attributeCode);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean hasRef = attributeSetManageDao.hasRef(attributeCode);
			if (hasRef) {
				log.debug("--delAttributeInfo-->删除属性失败!!该属性正被使用!!-->attributeCode:" + attributeCode);
				jsonResp.put("success", false);
				jsonResp.put("message", "删除属性失败!!该属性正被使用!!");
				jsonStr = jsonResp.toString();
			} else {
				boolean result = attributeSetManageDao.delAttributeInfo(attributeCode);
				jsonResp.put("success", result);
				jsonResp.put("code", 20000);// TODO mock
				if (result) {
					jsonResp.put("message", "删除属性成功!!");
					log.debug("--delAttributeInfo-->删除属性成功!!-->attributeCode:" + attributeCode);
				} else {
					log.debug("--delAttributeInfo-->删除属性失败!!-->attributeCode:" + attributeCode);
					jsonResp.put("message", "删除属性失败!!");
				}
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "删除属性失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("属性集管理->删除属性失败！" + e.toString());
			log.error("Exception:", e);
		}
		this.outJsonString(jsonStr);
		log.info("--delAttributeInfo(over)");
		return null;
	}

	/**
	 * 取属性定义类型
	 *
	 * @return
	 */
	@RequestMapping("/contentModelManage/getAttributeTypeAction.action")
	@ResponseBody
	public String getAttributeType() {
		log.info("--getAttributeType(start)");
		String jsonStr = null;

		try {
			List<AttributeTypeDefineInfoBean> attibuteTypeList = attributeSetManageDao.getAttributeTypeList();
			if (attibuteTypeList == null) {
				attibuteTypeList = new ArrayList<AttributeTypeDefineInfoBean>();
			} else {
				log.debug("--getAttributeType-->attibuteTypeList:" + attibuteTypeList);
			}
			int size = 0;
			if (attibuteTypeList.size() > 0) {
				size = attibuteTypeList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(attibuteTypeList, size, new AttributeTypeDefineInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取属性类型失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("属性集管理->获取属性类型失败！" + e.toString());
			log.error("Exception:", e);
		}
		this.outJsonString(jsonStr);
		log.info("--getAttributeType(over)");
		return null;
	}

	/**
	 * 检测该属性代码是否存在
	 *
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/contentModelManage/hasAttributeRecordAction.action")
	public String hasAttributeRecord(@RequestParam("value") String value) {
		log.info("--hasAttributeRecord(start)-->value:" + value);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			if (value != null) {
				count = attributeSetManageDao.hasAttributeRecord(value.toUpperCase());
			}
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error("属性集管理->获取属性类型失败!" + e.toString());
			log.error("Exception:", e);
		}
		log.debug("--hasAttributeRecord-->count:" + count);
		if (count == 0) {
			jsonResp.put("success", true);
			jsonResp.put("valid", true);
			jsonResp.put("reason", true);
			jsonStr = jsonResp.toString();
			log.debug("--hasAttributeRecord(success)-->value:" + value);
		} else if (count > 0) {
			jsonResp.put("success", true);
			jsonResp.put("valid", false);
			jsonResp.put("reason", "属性代码已经被使用!");
			jsonStr = jsonResp.toString();
			log.debug("--hasAttributeRecord(属性代码已经被使用!)-->value:" + value);
		} else {
			jsonResp.put("success", true);
			jsonResp.put("valid", false);
			jsonResp.put("reason", "检验失败!");
			jsonStr = jsonResp.toString();
			log.debug("--hasAttributeRecord(检验失败!)-->value:" + value);
		}
		this.outJsonString(jsonStr);
		log.info("--hasAttributeRecord(over)");
		return null;
	}

	/**
	 * 取脱敏规则
	 *
	 * @return
	 */
	@RequestMapping("/contentModelManage/getDesensitiveRuleAction.action")
	@ResponseBody
	public String getDesensitiveRule() {
		log.info("--getDesensitiveRule(start)");
		String jsonStr = null;

		try {
			List<DesensitiveTypeInfoBean> desensitiveRuleList = attributeSetManageDao.getDesensitiveRuleList();
			if (desensitiveRuleList == null) {
				desensitiveRuleList = new ArrayList<DesensitiveTypeInfoBean>();
			} else {
				log.debug("--getDesensitiveRule-->desensitiveRuleList:" + desensitiveRuleList);
			}
			int size = 0;
			if (desensitiveRuleList.size() > 0) {
				size = desensitiveRuleList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(desensitiveRuleList, size, new DesensitiveTypeInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取脱敏规则失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("属性集管理->获取脱敏规则失败！" + e.toString());
			log.error("Exception:", e);
		}
		this.outJsonString(jsonStr);
		log.info("--getDesensitiveRule(over)");
		return null;
	}

}
