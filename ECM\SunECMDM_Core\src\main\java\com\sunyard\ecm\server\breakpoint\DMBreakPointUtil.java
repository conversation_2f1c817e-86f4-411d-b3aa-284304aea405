package com.sunyard.ecm.server.breakpoint;

import com.sunyard.ecm.server.bean.BatchBean;
import com.sunyard.ecm.server.bean.BatchFileBean;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.daoinf.IBreakPointDao;
import com.sunyard.ecm.server.dm.BatchInfoCache;
import com.sunyard.ecm.server.dm.BatchInfoCacheFactory;
import com.sunyard.ecm.server.util.EcmUtil;
import com.sunyard.ecm.server.util.ModelUtil;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.util.FileUtil;
import com.sunyard.ws.utils.XMLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * DM保存断点文件。
 * <p>
 * 将断点文件放在模型的根目录下面，BREAK_POINTER文件夹内。因为contentID有可能是客户端生成的，
 * 无法根据contentID获取批次的相对路径。
 * 
 * <AUTHOR>
 *
 */
public class DMBreakPointUtil implements IBreakPointDao {
	private final static Logger log = LoggerFactory
			.getLogger(DMBreakPointUtil.class);
	private BatchInfoCache batchInfoCache = BatchInfoCacheFactory.getInstance();

	/**
	 * 如果服务端提供断点，那么在出错时需要保存该批次文件断点信息时调用。
	 * <p>
	 * 因为在外面没有做断点判断，所以需要在此方法中做判断。
	 * <p>
	 * 
	 * @param contentID
	 */
	public void saveBreakpointInfo(BatchBean batch) {
		String contentID = batch.getIndex_Object().getContentID();
		try {
			if (!batch.isBreakPoint()) {
				log.debug("无需断点：" + contentID);
				return;
			}
			List<BatchFileBean> batchFileBeans = batch.getDocument_Objects();
			List<FileBean> files = new ArrayList<FileBean>();
			for (BatchFileBean batchFileBean : batchFileBeans) {
				List<FileBean> fileBeans = batchFileBean.getFiles();
				for (FileBean filebean : fileBeans) {
					if (filebean.getSaveName() != null) {// 表示文件已经上传到服务器
						files.add(filebean);
					}
				}
			}
			if (files.size() == 0) {
				log.debug("批次没有对应的上传文件信息");
				return;
			}
			String filePath = getAbsPath(batch.getModelCode());
			FileUtil.writeXML(XMLUtil.list2Xml(files), filePath, contentID
					+ "_BREAKPOINT");
			batchInfoCache.removeBatch(contentID);
			log.info("生成断点信息，保存在：" + filePath + contentID + "_BREAKPOINT.xml");
		} catch (SunECMException e) {
			log.error("保存 " + contentID + " 断点信息时出错", e);
		}

	}

	private String getAbsPath(String modelCode) throws SunECMException {
		String filePath = ModelUtil.getModelRootPath(modelCode)
				+ "/BREAK_POINTER/";
		return filePath;
	}

	public void removeBreakPointInfo(String modeCode, String contentID) {
		try {
			String path = getAbsPath(modeCode);
			String breakPointName = path + contentID + "_BREAKPOINT.xml";
			File file = new File(breakPointName);
			if (!file.exists()) {
				return;
			}
			if (file.delete()) {
				log.debug("删除断点文件成功：" + contentID);
			}
		} catch (SunECMException e) {
			log.error("删除断点文件失败", e);
		}

	}

	private String getBreakPointInfoAsXML(String modeCode, String contentID)
			throws SunECMException {
		String path = getAbsPath(modeCode);
		String breakPointMsg = null;
		try {
			File file = new File(path + contentID + "_BREAKPOINT.xml");
			if (file.exists()) {
				log.debug("获得 " + contentID + " : " + modeCode + " 的断点信息");
				breakPointMsg = FileUtil.readXml(path + contentID
						+ "_BREAKPOINT.xml");
			}
		} catch (IOException e) {
			log.error("--getBreakPointMsg-->IOException"
					+ SunECMExceptionStatus.FILE_NOT_FOUND + e.toString());
			throw new SunECMException(SunECMExceptionStatus.FILE_NOT_FOUND,
					e.toString());
		}
		return breakPointMsg;
	}

	public List<FileBean> getBreakPoint(String modeCode, String contentId)
			throws SunECMException {
		String fileStr = getBreakPointInfoAsXML(modeCode, contentId);
		if (EcmUtil.isBlank(fileStr)) {
			return null;
		}
		return XMLUtil.xml2list(fileStr, FileBean.class);
	}
}
