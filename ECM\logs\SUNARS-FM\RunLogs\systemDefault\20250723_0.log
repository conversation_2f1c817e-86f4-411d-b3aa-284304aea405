2025-07-23 00:01:29.905 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:06:29.923 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:11:29.937 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:16:29.949 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:21:29.955 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:26:29.964 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:31:29.974 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:36:29.985 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:41:29.989 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:46:30.001 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:51:30.009 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 00:56:30.022 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 01:01:30.025 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 08:56:50.237 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:01:50.247 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:06:50.253 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:11:50.258 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:16:50.266 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:21:50.277 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:26:50.291 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:31:50.302 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:36:50.311 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:41:50.318 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:46:50.324 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:51:50.331 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 09:56:50.342 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:01:50.350 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:06:52.765 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:11:52.779 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:15:19.493 [] [299fa39633da409a/5e6cebca5463be2d] [http-nio-9009-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:15:19 操作结束时间: 2025-07-23 10:15:19!总共花费时间: 131 毫秒！
2025-07-23 10:15:19.585 [] [5d4ecfb9c3522de3/164c2dc1ae9a501d] [http-nio-9009-exec-68] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:15:19 操作结束时间: 2025-07-23 10:15:19!总共花费时间: 63 毫秒！
2025-07-23 10:16:52.789 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:21:52.800 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:26:52.811 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:31:11.476 [] [f630fc017ae99866/258d20942031feab] [http-nio-9009-exec-70] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:31:11 操作结束时间: 2025-07-23 10:31:11!总共花费时间: 92 毫秒！
2025-07-23 10:31:14.886 [] [683c5893637dd7aa/65e51959cbe108d7] [http-nio-9009-exec-72] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-23 10:31:14 操作结束时间: 2025-07-23 10:31:14!总共花费时间: 31 毫秒！
2025-07-23 10:31:15.086 [] [d4a2d5aad97bde49/cbcfeff74f2d1ee1] [http-nio-9009-exec-73] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:31:14 操作结束时间: 2025-07-23 10:31:15!总共花费时间: 177 毫秒！
2025-07-23 10:31:21.077 [] [1d56063230655e4c/856e761c946ee569] [http-nio-9009-exec-75] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 打印档案!请求IP地址: ************** 操作开始时间: 2025-07-23 10:31:21 操作结束时间: 2025-07-23 10:31:21!总共花费时间: 42 毫秒！
2025-07-23 10:31:52.814 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:36:52.819 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:41:52.829 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:46:52.832 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:48:18.889 [] [9efcf462d4fd2026/ad4ff9c73f66d108] [http-nio-9009-exec-76] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-23 10:48:18 操作结束时间: 2025-07-23 10:48:18!总共花费时间: 66 毫秒！
2025-07-23 10:48:18.978 [] [cd0131718e7720f0/05f395f097ad445b] [http-nio-9009-exec-78] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:48:18 操作结束时间: 2025-07-23 10:48:18!总共花费时间: 66 毫秒！
2025-07-23 10:48:24.118 [] [9674824d2eeea748/9b61c77179207ea1] [http-nio-9009-exec-80] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 打印档案!请求IP地址: ************** 操作开始时间: 2025-07-23 10:48:24 操作结束时间: 2025-07-23 10:48:24!总共花费时间: 51 毫秒！
2025-07-23 10:49:04.970 [] [6915a22895635c07/d8060b4baa2f10aa] [http-nio-9009-exec-81] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:49:04 操作结束时间: 2025-07-23 10:49:04!总共花费时间: 77 毫秒！
2025-07-23 10:49:05.063 [] [bff193c8e8768ad3/ee968b742e5c7331] [http-nio-9009-exec-83] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:49:05 操作结束时间: 2025-07-23 10:49:05!总共花费时间: 60 毫秒！
2025-07-23 10:49:07.964 [] [8f5b3986e3e4ca28/8c91e94ed9bdd901] [http-nio-9009-exec-84] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-23 10:49:07 操作结束时间: 2025-07-23 10:49:07!总共花费时间: 100 毫秒！
2025-07-23 10:51:52.837 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:56:52.842 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 10:57:28.593 [] [e87e860c5ba51e3d/9b908f9c8734ec12] [http-nio-9009-exec-86] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:57:28 操作结束时间: 2025-07-23 10:57:28!总共花费时间: 75 毫秒！
2025-07-23 10:57:28.699 [] [549be75e9119e399/5b6e94884b1660c5] [http-nio-9009-exec-87] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 10:57:28 操作结束时间: 2025-07-23 10:57:28!总共花费时间: 68 毫秒！
2025-07-23 10:57:31.169 [] [64e1cddf55fa77b5/58b31021f9d05d6b] [http-nio-9009-exec-88] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-23 10:57:31 操作结束时间: 2025-07-23 10:57:31!总共花费时间: 91 毫秒！
2025-07-23 11:01:52.845 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:06:52.854 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:11:52.866 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:16:52.868 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:21:52.877 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:26:52.885 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:31:52.888 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:36:52.891 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:41:52.901 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:46:52.905 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:51:52.909 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 11:56:52.922 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:01:52.936 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:06:52.946 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:11:52.960 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:16:52.969 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:21:52.984 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:26:52.989 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:31:52.993 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:36:53.004 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:41:53.005 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:46:53.017 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:51:53.026 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 12:56:53.034 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 13:01:53.039 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 13:06:53.045 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 13:11:53.059 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 13:16:53.070 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 13:21:53.084 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 13:26:53.085 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 13:31:53.090 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 13:35:36.443 [] [f880abfce9b7fe92/ee3a06b95d2fe920] [http-nio-9009-exec-90] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 13:35:36 操作结束时间: 2025-07-23 13:35:36!总共花费时间: 93 毫秒！
2025-07-23 13:35:36.535 [] [fcf54a9134b6340b/1e7fd3f2d793deea] [http-nio-9009-exec-91] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 13:35:36 操作结束时间: 2025-07-23 13:35:36!总共花费时间: 68 毫秒！
2025-07-23 13:35:40.896 [] [d46f195174d08f1a/47ac0f4d2c9b05d3] [http-nio-9009-exec-93] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-23 13:35:40 操作结束时间: 2025-07-23 13:35:40!总共花费时间: 75 毫秒！
2025-07-23 13:36:41.506 [] [/] [SpringApplicationShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1753249001506, current=DOWN, previous=UP]
2025-07-23 13:36:41.508 [] [/] [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNARS-FM/localhost:9009: registering service...
2025-07-23 13:36:41.528 [] [/] [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNARS-FM/localhost:9009 - registration status: 204
2025-07-23 13:36:41.651 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 13:36:43.691 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 13:36:43.692 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 13:36:43.693 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 13:36:43.972 [] [/] [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-07-23 13:36:43.982 [] [/] [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closing ...
2025-07-23 13:36:43.984 [] [/] [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4} closed
2025-07-23 13:36:43.984 [] [/] [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-07-23 13:36:43.990 [] [/] [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-07-23 13:36:43.991 [] [/] [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2025-07-23 13:36:44.006 [] [/] [SpringApplicationShutdownHook] ERROR c.s.aos.common.init.InitCurrServer - 服务「SUNARS-FM」 |执行销毁
2025-07-23 13:36:44.055 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-23 13:36:44.055 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.simpl.SimpleThreadPool - Job execution threads will use class loader of thread: SpringApplicationShutdownHook
2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'DefaultQuartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutting down.
2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED paused.
2025-07-23 13:36:44.056 [] [/] [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler DefaultQuartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-07-23 13:36:44.057 [] [/] [SpringApplicationShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Shutting down DiscoveryClient ...
2025-07-23 13:36:47.063 [] [/] [SpringApplicationShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Unregistering ...
2025-07-23 13:36:47.077 [] [/] [SpringApplicationShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNARS-FM/localhost:9009 - deregister  status: 200
2025-07-23 13:36:47.087 [] [/] [SpringApplicationShutdownHook] INFO  c.netflix.discovery.DiscoveryClient - Completed shut down of DiscoveryClient
2025-07-23 14:42:29.310 [] [/] [main] INFO  com.sunyard.aos.FmApplication - The following 1 profile is active: "dev"
2025-07-23 14:42:34.043 [] [/] [main] INFO  c.s.a.c.d.c.DynamicDataSourceLoading - 【加密类型】：AES
2025-07-23 14:42:34.063 [] [/] [main] INFO  c.s.a.c.d.c.DynamicDataSourceLoading - 【加密类型】：AES
2025-07-23 14:42:34.360 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2025-07-23 14:42:34.418 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,db1} inited
2025-07-23 14:42:34.419 [] [/] [main] INFO  c.b.d.d.p.AbstractJdbcDataSourceProvider - 成功加载数据库驱动程序
2025-07-23 14:42:34.428 [] [/] [main] INFO  c.b.d.d.p.AbstractJdbcDataSourceProvider - 成功获取数据库连接
2025-07-23 14:42:34.481 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-3,master} inited
2025-07-23 14:42:34.537 [] [/] [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-4,db1} inited
2025-07-23 14:42:34.539 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [db1] success
2025-07-23 14:42:34.541 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-07-23 14:42:34.541 [] [/] [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-23 14:42:38.372 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-07-23 14:42:38.381 [] [/] [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-07-23 14:42:38.381 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-07-23 14:42:38.382 [] [/] [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-07-23 14:42:38.382 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-07-23 14:42:38.383 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-07-23 14:42:38.383 [] [/] [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-07-23 14:42:38.383 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@53813f01
2025-07-23 14:42:38.602 [] [/] [main] INFO  c.s.a.c.filter.CSRFValidationFilter - 未配置CSRF验证请求源url，不进行Referer请求来源地址验证。
2025-07-23 14:42:38.624 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 开始初始化风险公共内容。
2025-07-23 14:42:38.625 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 数据库驱动名称：org.postgresql.Driver
2025-07-23 14:42:38.626 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 初始化数据库类型VariableArs.dbType = DbTypeEnum{driverClassName='org.postgresql.Driver,', upperCase=false}
2025-07-23 14:42:38.626 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 为DataConnectionUtil设置dataSource
2025-07-23 14:42:38.627 [] [/] [main] INFO  c.sunyard.ars.common.ArsCommonConfig - 初始化风险公共内容完成。
2025-07-23 14:42:39.589 [] [/] [main] INFO  c.s.a.c.d.config.DruidAuthConfig - 是否开启Druid授权访问：false
2025-07-23 14:42:40.510 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - 初始化flow-config加载
2025-07-23 14:42:40.532 [] [/] [main] DEBUG c.s.a.f.dao.NodeMapper.selectAllFlow - ==>  Preparing: select a.value value, a.defaultNode defaultNode, a.id id, b.id bId,b.flowId flowId,b.value bvalue,b.text btext,b.purview purview,b.canBeCancel canBeCancel,b.cancelNode cancelNode,b.canBeClose canBeClose, c.value cvalue,c.text ctext,c.flowNodeId flowNodeId FROM fm_flow a LEFT JOIN fm_flow_node b ON b.flowId = a.id LEFT JOIN fm_next_flow_node c ON c.flowNodeId = b.id
2025-07-23 14:42:40.548 [] [/] [main] DEBUG c.s.a.f.dao.NodeMapper.selectAllFlow - ==> Parameters: 
2025-07-23 14:42:40.566 [] [/] [main] DEBUG c.s.a.f.dao.NodeMapper.selectAllFlow - <==      Total: 7
2025-07-23 14:42:40.568 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - 初始化flow-config完成
2025-07-23 14:42:40.568 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - 初始化log模板
2025-07-23 14:42:40.568 [] [/] [main] DEBUG c.s.a.f.d.L.sysInitLogTemplate - ==>  Preparing: select TYPE "TYPE", INFO "INFO",DETAIL "DETAIL" FROM FM_LOG_TEMPLATE
2025-07-23 14:42:40.568 [] [/] [main] DEBUG c.s.a.f.d.L.sysInitLogTemplate - ==> Parameters: 
2025-07-23 14:42:40.574 [] [/] [main] DEBUG c.s.a.f.d.L.sysInitLogTemplate - <==      Total: 30
2025-07-23 14:42:40.574 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - log模板size为30
2025-07-23 14:42:40.583 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 注入配置  ---------------
2025-07-23 14:42:40.583 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 获取配置文件参数开始  ---------------
2025-07-23 14:42:40.584 [] [/] [main] INFO  c.s.aos.common.config.AosConfigs - ------------- 获取配置文件参数完成！ ---------------
2025-07-23 14:42:40.811 [] [/] [main] INFO  org.redisson.Version - Redisson 3.26.1
2025-07-23 14:42:41.080 [] [/] [redisson-netty-4-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 14:42:41.092 [] [/] [redisson-netty-4-13] INFO  o.r.connection.ConnectionsHolder - 5 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-07-23 14:42:43.808 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Initializing Eureka in region us-east-1
2025-07-23 14:42:43.853 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON encoding codec LegacyJacksonJson
2025-07-23 14:42:43.854 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using JSON decoding codec LegacyJacksonJson
2025-07-23 14:42:43.952 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML encoding codec XStreamXml
2025-07-23 14:42:43.952 [] [/] [main] INFO  c.n.d.p.DiscoveryJerseyProvider - Using XML decoding codec XStreamXml
2025-07-23 14:42:44.250 [] [/] [main] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 14:42:44.268 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Disable delta property : false
2025-07-23 14:42:44.268 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Single vip registry refresh property : null
2025-07-23 14:42:44.268 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Force full registry fetch : false
2025-07-23 14:42:44.269 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Application is null : false
2025-07-23 14:42:44.269 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Registered Applications size is zero : true
2025-07-23 14:42:44.269 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Application version is -1: true
2025-07-23 14:42:44.269 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Getting all instance registry info from the eureka server
2025-07-23 14:42:44.314 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - The response status is 200
2025-07-23 14:42:44.316 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Starting heartbeat executor: renew interval is: 30
2025-07-23 14:42:44.318 [] [/] [main] INFO  c.n.discovery.InstanceInfoReplicator - InstanceInfoReplicator onDemand update allowed rate per min is 4
2025-07-23 14:42:44.322 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Discovery Client initialized at timestamp 1753252964321 with initial instances count: 9
2025-07-23 14:42:44.529 [] [/] [main] INFO  com.sunyard.ars.fm.init.FmSystemInit - 是否开启实物档案流程控制影像采集开关为false
2025-07-23 14:42:44.818 [] [/] [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-07-23 14:42:46.589 [] [/] [main] INFO  c.netflix.discovery.DiscoveryClient - Saw local status change event StatusChangeEvent [timestamp=1753252966589, current=UP, previous=STARTING]
2025-07-23 14:42:46.591 [] [/] [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNARS-FM/localhost:9009: registering service...
2025-07-23 14:42:46.623 [] [/] [DiscoveryClient-InstanceInfoReplicator-0] INFO  c.netflix.discovery.DiscoveryClient - DiscoveryClient_SUNARS-FM/localhost:9009 - registration status: 204
2025-07-23 14:42:48.838 [] [/] [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-07-23 14:42:48.845 [] [/] [main] INFO  c.s.a.c.util.easyExcel.ExportUtil - 获取配置的excel下载文件夹路径/home/<USER>/template/downloadPath/
2025-07-23 14:42:48.846 [] [/] [main] INFO  c.s.a.c.util.easyExcel.ExportUtil - 填充模板文件目录/home/<USER>/template/fillTemplate/
2025-07-23 14:42:48.848 [] [/] [main] INFO  c.s.a.common.util.easyExcel.FillUtil - 填充模板文件目录/home/<USER>/template/fillTemplate/
2025-07-23 14:42:48.848 [] [/] [main] INFO  c.s.a.common.util.easyExcel.FillUtil - 导出文件目录/home/<USER>/template/downloadPath/
2025-07-23 14:42:48.871 [] [/] [main] INFO  com.sunyard.aos.FmApplication - Started FmApplication in 27.081 seconds (JVM running for 29.349)
2025-07-23 14:42:48.879 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 初始化服务「SUNARS-FM」 |获取应用服务信息配置 | 开始执行
2025-07-23 14:42:49.706 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20190129000000000001 | 「地址」127.0.0.1:9007 | 「访问目录」/ | 「服务类型」0 | 「启用状态」1
2025-07-23 14:42:49.706 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20191217154129494011 | 「地址」**************:8089 | 「访问目录」/SunAOS | 「服务类型」2 | 「启用状态」1
2025-07-23 14:42:49.710 [] [/] [main] ERROR c.s.aos.common.init.InitCurrServer - 初始化服务「SUNARS-FM」 |执行失败 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
com.sunyard.aos.common.exception.SunAOSException: 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
2025-07-23 14:42:49.711 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 初始化服务「SUNARS-FM」 |获取应用服务信息配置 | 开始执行
2025-07-23 14:42:50.562 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20190129000000000001 | 「地址」127.0.0.1:9007 | 「访问目录」/ | 「服务类型」0 | 「启用状态」1
2025-07-23 14:42:50.563 [] [/] [main] INFO  c.s.aos.common.init.InitCurrServer - 「应用服务id」20191217154129494011 | 「地址」**************:8089 | 「访问目录」/SunAOS | 「服务类型」2 | 「启用状态」1
2025-07-23 14:42:50.563 [] [/] [main] ERROR c.s.aos.common.init.InitCurrServer - 初始化服务「SUNARS-FM」 |执行失败 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
com.sunyard.aos.common.exception.SunAOSException: 初始化当前应用服务信息异常，未获取到匹配的应用服务配置信息，请检查「应用服务配置」中是否包含当前服务！
2025-07-23 14:42:52.619 [] [/] [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=SUNARS-FM, managementUrl=http://************:9009/actuator, healthUrl=http://************:9009/actuator/health, serviceUrl=http://************:9009/) at spring-boot-admin ([http://127.0.0.1:8878/admin/instances]): I/O error on POST request for "http://127.0.0.1:8878/admin/instances": Connect to 127.0.0.1:8878 [/127.0.0.1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8878 [/127.0.0.1] failed: Connection refused: connect. Further attempts are logged on DEBUG level
2025-07-23 14:43:59.641 [] [701c983934463809/7d527972de7ab56f] [http-nio-9009-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 14:43:59 操作结束时间: 2025-07-23 14:43:59!总共花费时间: 438 毫秒！
2025-07-23 14:43:59.776 [] [671afe2f40edb301/e07e83c42508d840] [http-nio-9009-exec-2] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-23 14:43:59 操作结束时间: 2025-07-23 14:43:59!总共花费时间: 88 毫秒！
2025-07-23 14:44:01.844 [] [b2f51dc8ac2d0d3a/d397eb2e30a8e96c] [http-nio-9009-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-23 14:44:01 操作结束时间: 2025-07-23 14:44:01!总共花费时间: 238 毫秒！
2025-07-23 14:47:44.283 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 14:52:44.286 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 14:57:44.301 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:02:44.310 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:07:44.316 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:12:44.331 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:17:44.333 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:22:44.342 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:27:44.355 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:32:44.357 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:37:44.361 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:42:44.366 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:47:44.376 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:52:44.387 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 15:57:44.388 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:02:44.396 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:07:44.405 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:12:44.412 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:17:44.425 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:22:44.431 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:27:44.437 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:32:44.438 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:37:44.453 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:42:44.456 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:47:44.458 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:52:44.468 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 16:57:44.474 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:02:44.488 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:07:44.493 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:12:44.496 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:17:44.502 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:22:44.514 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:27:44.530 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:32:44.540 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:37:44.546 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:42:44.550 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:47:44.559 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:52:44.564 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 17:57:44.570 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:02:44.571 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:07:44.577 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:12:44.592 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:17:44.594 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:22:44.606 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:27:44.611 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:32:44.625 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:37:44.635 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:42:44.647 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:47:44.649 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:52:44.652 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 18:57:44.661 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:02:44.675 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:07:44.803 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:12:44.911 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:17:44.918 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:22:44.930 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:27:44.941 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:32:44.947 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:37:44.956 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:42:44.962 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:47:44.970 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:52:44.978 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 19:57:44.981 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:02:44.982 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:07:44.989 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:12:44.998 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:17:45.014 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:22:45.017 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:27:45.020 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:32:45.034 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:37:45.048 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:42:45.059 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:47:45.070 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:52:45.079 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 20:57:45.092 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:02:45.101 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:07:45.110 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:12:45.111 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:17:45.119 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:22:45.127 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:27:45.130 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:32:45.131 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:37:45.144 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:42:45.146 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:47:45.160 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:52:45.173 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 21:57:45.179 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:02:45.191 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:07:45.194 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:12:45.209 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:17:45.214 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:22:45.217 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:27:45.228 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:32:45.234 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:37:45.243 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:42:45.253 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:47:45.257 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:52:45.261 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 22:57:45.270 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:02:45.272 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:07:45.286 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:12:45.296 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:17:45.308 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:22:45.314 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:27:45.316 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:32:45.333 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:37:45.344 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:42:45.357 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:47:45.373 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:52:45.380 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-23 23:57:45.385 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
