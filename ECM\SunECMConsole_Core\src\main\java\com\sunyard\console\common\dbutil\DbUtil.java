package com.sunyard.console.common.dbutil;

import com.sunyard.console.common.util.SpringUtil;
import org.apache.commons.dbutils.DbUtils;
import org.apache.commons.dbutils.QueryRunner;
import org.apache.commons.dbutils.ResultSetHandler;
import org.apache.commons.dbutils.handlers.BeanHandler;
import org.apache.commons.dbutils.handlers.BeanListHandler;
import org.apache.commons.dbutils.handlers.MapListHandler;
import org.apache.commons.dbutils.handlers.ScalarHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * 数据库操作类
 * 
 * <AUTHOR> @version 3.1 2012 
 */
public class DbUtil {

	private static final Logger log = LoggerFactory.getLogger(DbUtil.class);

//	public static boolean DEBUG_SQL = Boolean.getBoolean("dbutil.sql.debug");
//	public static boolean DEBUG_SELECT = Boolean
//			.getBoolean("dbutil.select.debug");
//	public static boolean DEBUG_INSERT = Boolean
//			.getBoolean("dbutil.insert.debug");

	public static final String DEFAULT_NAME = "SQL";


	public static DbUtil stringDbUtil(String name, boolean debug) {	
		DbUtil util = new DbUtil(new QueryRunner((DataSource) SpringUtil.ctx
				.getBean("dataSource")));
		util.setName(name);
		util.setLog_debug(debug);
		return util;
	}

	private String name = DEFAULT_NAME;
	private boolean log_debug = true;

	private QueryRunner queryParam;

	public DbUtil(QueryRunner runner) {
		this.queryParam = runner;

	}

	// /////////////////////////下面为查询方法/////////////////////////////

	public Object query(String sql, ResultSetHandler handler)
			throws SQLException {
		Object ret = queryParam.query(sql, handler);

		if (log_debug) {
			if (ret != null) {
				String info = ret.getClass().toString();
				if (ret instanceof Collection) {
					Collection cret = (Collection) ret;
					int size = cret.size();
					info += "(size=" + size + ")";
//					if (DEBUG_SELECT) {
//						info += " -> " + ret;
//					}
				} else if (ret instanceof Map) {
					Map map = (Map) ret;
					info += "(size=" + map.size() + ")";
//					if (DEBUG_SELECT) {
//						info += " -> " + ret;
//					}
				} else {
					info = ret.toString();
				}
				log.debug("[" + name + "]\t: " + sql + " -> " + info);

			} else {
				log.debug("[" + name + "]\t: " + sql + " -> (null)");

			}
		}

		return ret;
	}
	public Object query(String sql, ResultSetHandler handler, Object[] params) throws SQLException{
		Object ret = queryParam.query(sql,  handler,params);
		if (log_debug) {
			if (ret != null) {
				String info = ret.getClass().toString();
				if (ret instanceof Collection) {
					Collection cret = (Collection) ret;
					int size = cret.size();
					info += "(size=" + size + ")";
//					if (DEBUG_SELECT) {
//						info += " -> " + ret;
//					}
				} else if (ret instanceof Map) {
					Map map = (Map) ret;
					info += "(size=" + map.size() + ")";
//					if (DEBUG_SELECT) {
//						info += " -> " + ret;
//					}
				} else {
					info = ret.toString();
				}
				log.debug("[" + name + "]\t: " + sql + " -> " + info);
				
			} else {
				log.debug("[" + name + "]\t: " + sql + " -> (null)");
				
			}
		}
		
		return ret;
		
		
	} 
	public boolean queryExist(String sql) throws SQLException {
		return ((Boolean) query(sql, new ResultSetHandler() {

			public Object handle(ResultSet result) throws SQLException {
				return Boolean.valueOf(result.next());
			}

		})).booleanValue();
	}


	public int queryInt(String sql) throws SQLException {
		return ((Integer) query(sql, new ResultSetHandler() {
			
			public Object handle(ResultSet result) throws SQLException {
				int ret = result.next() ? result.getInt(1) : 0;
				return new Integer(ret);
			}
			
		})).intValue();
	}
	public int queryInt(String sql,Object[] params) throws SQLException {
		return ((Integer) query(sql, new ResultSetHandler() {

			public Object handle(ResultSet result) throws SQLException {
				int ret = result.next() ? result.getInt(1) : 0;
				return new Integer(ret);
			}

		},params)).intValue();
	}


	public String queryString(String sql) throws SQLException {
		return (String) query(sql, new ScalarHandler());
	}
	public String queryString(String sql,Object[] params) throws SQLException {
		return (String) query(sql, new ScalarHandler(),params);
	}

	public List queryMapList(String sql) throws SQLException {
		return (List) query(sql, new MapListHandler());
	}
	
	public Object queryBean(String sql, Class clazz) throws SQLException {
		return query(sql, new BeanHandler(clazz));
	}

	public Object queryBean(String sql, Class clazz,Object[] params) throws SQLException {
		return query(sql, new BeanHandler(clazz),params);
	}

	public List queryBeanList(String sql, Class clazz) throws SQLException {
		return (List) query(sql, new BeanListHandler(clazz));
	}
	public List queryBeanList(String sql, Class clazz,Object[] params) throws SQLException {
		return (List) query(sql, new BeanListHandler(clazz),params);
	}


	// ///////////////////////更新语句//////////////////////////////

	public int update(String sql) throws SQLException {
		int ret = queryParam.update(sql);
		if (log_debug) {
			log.debug("[" + name + "]\t: " + sql + " -> " + ret);
		}
		return ret;
	}
	public int update(String sql,Object[] params) throws SQLException {
		int ret = queryParam.update(sql,params);
		if (log_debug) {
			log.debug("[" + name + "]\t: " + sql + " -> " + ret);
		}
		return ret;
	}

	// ///////////////////////插入语句///////////////////////////////

	private static boolean CANNOT_AUTO_COMMIT = false;

	public void exceTrans(Collection sqls) throws SQLException {
		Connection conn = queryParam.getDataSource().getConnection();
		conn.setAutoCommit(false);
		try {

			Iterator iter = sqls.iterator();

			String sql;
			while (iter.hasNext()) {
				sql = (String) iter.next();
				int ret =queryParam.update(conn, sql);
				log.debug("[" + name + "]\t: " + sql + " -> "
						+   ret);
				
			}
			DbUtils.commitAndClose(conn);
			
		} catch (SQLException e) {
			log.info(e.toString());
			DbUtils.rollbackAndCloseQuietly(conn);
			throw new SQLException(e.getMessage());
		}

	}
	
	
	/**
	 * 手动事务控制
	 * @param sqls			SQL语句块
	 * @param conn			连接
	 * @return
	 * @throws SQLException
	 */
	public boolean exceTrans(Collection sqls,Connection conn) throws SQLException{
		boolean result = true;
		
		try{
			Iterator iter = sqls.iterator();

			String sql;
			while (iter.hasNext()) {
				sql = (String) iter.next();
				int ret =queryParam.update(conn, sql);
				log.debug("[" + name + "]\t: " + sql + " -> "
						+   ret);
				
			}
		}catch (SQLException e) {
			result = false;
			log.info(e.toString());
			DbUtils.rollbackAndCloseQuietly(conn);
			throw new SQLException(e.getMessage());
		}
		
		return result;
	}
	
	/**
	 * 取得连接
	 * @return
	 * @throws SQLException
	 */
	public Connection getConnection() throws SQLException{
		return queryParam.getDataSource().getConnection();
	}
	
	/**
	 * 建表
	 * @param sql
	 * @throws SQLException
	 */
	public boolean execute(String sql) throws SQLException{
		boolean result = true;
		Connection conn = null;
		Statement stmt = null;
		try{
			conn = this.getConnection();
			stmt = conn.createStatement();
			stmt.execute(sql);
			
		}catch(SQLException e){
			result = false;
			throw new SQLException(e.getMessage());
		}finally{
			if(stmt != null){
				DbUtils.close(stmt);
			}
			if(conn != null){
				DbUtils.close(conn);
			}
		}
		return result;
		
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public boolean isLog_debug() {
		return log_debug;
	}

	public void setLog_debug(boolean log_debug) {
		this.log_debug = log_debug;
	}

}
