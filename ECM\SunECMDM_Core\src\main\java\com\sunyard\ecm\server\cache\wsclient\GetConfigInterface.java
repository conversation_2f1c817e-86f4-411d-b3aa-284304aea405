package com.sunyard.ecm.server.cache.wsclient;

import com.sunyard.ecm.server.bean.*;

import java.util.List;

/**
 * <p>
 * Title: 获得webService接口
 * </p>
 * <p>
 * Description: 获得webService接口，发送获取配置信息请求
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface GetConfigInterface {
	/** 获取所有服务器信息*/
	public List<NodeInfo> getContentServer();

	/** 
	 * 
	 * 主动获取内容模型模板信息
	 * @param groupID 服务器组ID
	 * @param objectNames 内容模型模版名称列表
	 * 
	 *  */
	public List<ModelTemplateBean> getModelTemplate(String[] docNames,
													String groupId);

	/** 
	 * 
	 * 获取生命周期策略
	 * 
	 * @param groupID 服务器组ID
	 *  */
	public List<LifeCycleStrategyBean> getLifeCycleStrategy(String groupID);

	/** 
	 * 获取存储对象
	 * 
	 * @param  groupID 服务器组ID
	 *  */
	public List<StoreObjectBean> getStoreObject(String groupID);

	/**
	 *  获取内容模型列表信息
	 *  
	 *  */
	public List<AllModelMsgBean> getAllModelMsg();

	/**
	 *  获取内容模型的表
	 *  */
	public List<ModelTableBean> getModelTable();
	/**
	 * 获取内容服务器组和内容对象对应卷
	 * 
	 * @param groupID  服务器组ID
	 * */
	public List<SGroupModleSetBean> getSgroupmodleSet(String groupID);
	/**主动获取日志*/
	public List<DMLogRuleBean> getLogRule();
	/** 
	 * 获得参数配置列表信息
	 * 
	 * */
	public List<AllParamMsgBean> getAllParConfigMsg();
	/**主动标签信息*/
	public List<TagInfoBean> getAllTagInfoBean();
	/**主动获取属性脱敏规则信息*/
	public List<AttrDesenRuleBean> getAllAttrDesenBean();
}