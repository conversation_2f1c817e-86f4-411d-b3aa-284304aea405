package com.sunyard.console.eclientmanage.action;//package com.sunyard.console.eclientmanage.action;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
//import javax.servlet.http.HttpSession;
//
//import net.sf.json.JSONObject;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//
//import com.sunyard.client.SunEcmClientApi;
//import com.sunyard.client.bean.ClientBatchBean;
//import com.sunyard.client.bean.ClientHeightQuery;
//import com.sunyard.client.impl.SunEcmClientWebserviceApiImpl;
//import com.sunyard.console.common.ext.data.record.Creater;
//import com.sunyard.console.common.struts.BaseAction;
//import com.sunyard.console.common.util.Constant;
//import com.sunyard.console.common.util.JSONUtil;
//import com.sunyard.console.configmanager.bean.BatchIndexBean;
//import com.sunyard.console.configmanager.bean.HeightQuery;
//import com.sunyard.console.configmanager.bean.ModelTemplateBean;
//import com.sunyard.console.configmanager.dao.GetDBConfigDao;
//import com.sunyard.console.configmanager.dao.GetDBConfigDaoImpl;
//import com.sunyard.console.configmanager.wsserviceutil.DMAdressConstuct;
//import com.sunyard.console.contentmodelmanage.bean.ContentObjectInfoBean;
//import com.sunyard.console.contentmodelmanage.bean.TreeBean;
//import com.sunyard.console.contentmodelmanage.dao.ContentObjectManageDao;
//import com.sunyard.console.eclientmanage.dao.EclientManageDAO;
//import com.sunyard.console.eclientmanage.util.EclientManageUtil;
//import com.sunyard.console.eclientmanage.util.NearLineBatchListStatic;
//import com.sunyard.console.eclientmanage.util.NearLineStringUtil;
//import com.sunyard.console.safemanage.bean.UserInfoBean;
//import com.sunyard.console.unityaccessservermanage.dao.UnityAccessServerManageDAO;
//import com.sunyard.ws.client.WSNearLineClient;
//import com.sunyard.ws.internalapi.NearLineWsInterface;
//import com.sunyard.ws.utils.XMLUtil;
//
///**
// * <p>
// * Title: 客户端工具Action
// * </p>
// * <p>
// * Description: 处理动态生成表、单查询批次信息
// * </p>
// * <p>
// * Copyright: Copyright (c) 2012
// * </p>
// * <p>
// * Company: sunyard
// * </p>
// * 
// * <AUTHOR>
// * @version 1.0
// */
//public class EclientManageAction extends BaseAction {
//	private EclientManageDAO emdao = null;
//	private ContentObjectManageDao comdao = null;
//	private UnityAccessServerManageDAO uadao = null;
//	private EclientManageUtil formPanelCreater = null;
//	private final static  Logger log = LoggerFactory.getLogger(EclientManageAction.class);
//
//	public EclientManageDAO getEmdao() {
//		return emdao;
//	}
//
//	public void setEmdao(EclientManageDAO emdao) {
//		this.emdao = emdao;
//	}
//
//	public EclientManageUtil getFormPanelCreater() {
//		return formPanelCreater;
//	}
//
//	public void setFormPanelCreater(EclientManageUtil formPanelCreater) {
//		this.formPanelCreater = formPanelCreater;
//	}
//
//	public ContentObjectManageDao getComdao() {
//		return comdao;
//	}
//
//	public void setComdao(ContentObjectManageDao comdao) {
//		this.comdao = comdao;
//	}
//
//	public UnityAccessServerManageDAO getUadao() {
//		return uadao;
//	}
//
//	public void setUadao(UnityAccessServerManageDAO uadao) {
//		this.uadao = uadao;
//	}
//
//	private String server_id; // 服务器ID
//	private String model_code; // 内容模型代码
//	private String model_name; // 内容模型名称
//	private String content_id; // 内容编号
//	private String userName; // 用户ID
//	private String userPassWord; // 用户密码
//	private String contentID; // 批次号
//	private String max_version; // 批次最大版本号
//	private String data;
//	private String batch_id; //批次编号
//	private String start;	//分页参数
//	private String limit;	 //分页参数
//	private String flag;   // 0 在线  1 近线
//	private String checkId; //复核用户ID
//	public String getData() {
//		return data;
//	}
//	public void setData(String data) {
//		this.data = data;
//	}
//	public String getContentID() {
//		return contentID;
//	}
//	public void setContentID(String contentID) {
//		this.contentID = contentID;
//	}
//	public String getMax_version() {
//		return max_version;
//	}
//	public void setMax_version(String max_version) {
//		this.max_version = max_version;
//	}
//	public String getServer_id() {
//		return server_id;
//	}
//	public void setServer_id(String server_id) {
//		this.server_id = server_id;
//	}
//	public String getContent_id() {
//		return content_id;
//	}
//	public void setContent_id(String content_id) {
//		this.content_id = content_id;
//	}
//	public String getModel_code() {
//		return model_code;
//	}
//	public void setModel_code(String model_code) {
//		this.model_code = model_code;
//	}
//	public String getModel_name() {
//		return model_name;
//	}
//	public void setModel_name(String model_name) {
//		this.model_name = model_name;
//	}
//	public String getUserName() {
//		return userName;
//	}
//	public void setUserName(String userName) {
//		this.userName = userName;
//	}
//	public String getUserPassWord() {
//		return userPassWord;
//	}
//	public void setUserPassWord(String userPassWord) {
//		this.userPassWord = userPassWord;
//	}
//	public String getBatch_id() {
//		return batch_id;
//	}
//	public void setBatch_id(String batchId) {
//		batch_id = batchId;
//	}
//	public String getStart() {
//		return start;
//	}
//	public void setStart(String start) {
//		this.start = start;
//	}
//	public String getLimit() {
//		return limit;
//	}
//	public void setLimit(String limit) {
//		this.limit = limit;
//	}
//	public String getFlag() {
//		return flag;
//	}
//	public void setFlag(String flag) {
//		this.flag = flag;
//	}
//
//	public String getCheckId() {
//		return checkId;
//	}
//
//	public void setCheckId(String checkId) {
//		this.checkId = checkId;
//	}
//
//	/**
//	 * 表单模板创建
//	 * 
//	 * @return
//	 */
//	public String formModelCreate() {
//		log.info( "--formModelCreate(start)");
//		try {
//			Map<String, Object> m = formPanelCreater.createFormPanel(
//					"\'./eclientManage/queryBatchInfoAction.action\'",
//					server_id, model_code, model_name).getFormPanel();
//			this.outString("var formPanel" + model_code
//					+ " = new Ext.FormPanel(" + new JSONUtil().createExtJS(m)
//					+ ")");
//		} catch (Exception e) {
//			log.error( "客户端工具管理->获取表单模板创建失败->" + e.toString(), e);
//		}
//		log.info( "--formModelCreate(over)");
//		return null;
//	}
//
//	/**
//	 * grid列模板创建
//	 * 
//	 * @return
//	 */
//	public String createGridColumnModel() {
//		log.info( "--createGridColumnModel(start)-->model_code:"+model_code);
//		try {
//			List<Map<String, Object>> columnModels = formPanelCreater
//					.createColumnModel(model_code);
//			this.outJsonString(new JSONUtil().createJsonDataByMapList(
//					columnModels, columnModels.size()));
//		} catch (Exception e) {
//			log.error( "客户端工具管理->grid列模板失败->" + e.toString(), e);
//		}
//		log.info( "--createGridColumnModel(over)");
//		return null;
//	}
//	/**
//	 * 列表data模板创建
//	 * 
//	 * @return
//	 */
//	public String createGridDateCreater() {
//		log.info( "--createGridDateCreater(start)");
//		String jsonStr = null;
//		boolean serverConnectFlag = true; // 默认可连接上
//		HttpSession session = getSession();
//		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//		String dmName = DMAdressConstuct.getDMName();
//		log.debug( "--createGridDateCreater-->dmName:"+dmName);
//		Map<String, String> parameterMap = formPanelCreater
//				.getParameterMap(this.getRequest());
//		log.debug( "--createGridDateCreater-->parameterMap:"+parameterMap);
//		String flag = parameterMap.get("flag");
//		log.debug( "--createGridDateCreater-->flag:"+flag);
//		if (flag.equals("0")) { // 在线
//			Map<String, String> nodeInfoMap = formPanelCreater
//					.getNodeInfoMap(server_id);
//			log.debug( "--createGridDateCreater-->nodeInfoMap:"+nodeInfoMap);
//			SunEcmClientApi clientApi = new SunEcmClientWebserviceApiImpl(
//					nodeInfoMap.get("ip"), Integer.parseInt(nodeInfoMap
//							.get("http")), dmName);
//			try {
//				// 传输过程对密码进行加密传输
//				log.debug( "--createGridDateCreater-->userName:"+user.getLogin_id()+";userPassWord:" + user.getUser_password());
//				clientApi.login(user.getLogin_id(),user.getUser_password());
//				log.debug( "--createGridDateCreater-->连接服务器成功!");
//			} catch (Exception e) {
//				log.error( "客户端工具管理->连接服务器失败->" + e.toString(), e);
//				JSONObject jsonResp = new JSONObject();
//				jsonResp.put("success", false);
//				jsonResp.put("message", "连接服务器失败，请稍后重试");
//				jsonStr = jsonResp.toString();
//				serverConnectFlag = false; // 登录出现异常时连接设置为连接不上
//			}
//			log.debug( "--createGridDateCreater-->serverConnectFlag:"+serverConnectFlag);
//			if (serverConnectFlag) {
//				try {
//					session.setAttribute(Constant.SUN_NODE_INFO_MAP,
//							nodeInfoMap);
//					session.setAttribute(Constant.SUN_REQUEST_PARAMETERS,
//							parameterMap);
//
//					Creater cModel_greadDataCreater = formPanelCreater
//							.createCreaterModel(model_code);
//					log.debug( "--createGridDateCreater-->cModel_greadDataCreater:"+cModel_greadDataCreater);
//					jsonStr = new JSONUtil().createJsonDataByMapList(
//							cModel_greadDataCreater.getCreater(),
//							cModel_greadDataCreater.getCreater().size());
//				log.debug( "--createGridDateCreater-->客户端工具管理->列表data模板成功！");
//				} catch (Exception e) {
//					log.error( "客户端工具管理->列表data模板失败->" + e.toString(), e);
//					JSONObject jsonResp = new JSONObject();
//					jsonResp.put("success", false);
//					jsonResp.put("message", "列表data模板失败，请稍后重试");
//					jsonStr = jsonResp.toString();
//				}
//			}
//		} else { // 近线
//			Map<String, String> nodeInfoMap = formPanelCreater.getNodeInfoMap();
//			log.debug( "--createGridDateCreater-->nodeInfoMap:"+nodeInfoMap);
//			NearLineWsInterface Nlinterface = new WSNearLineClient()
//					.getNearLineClient("http://" + nodeInfoMap.get("ip") + ":"
//							+ nodeInfoMap.get("http") + "/"
//							+ nodeInfoMap.get("name")
//							+ "/webservices/WsInterface", 300000);
//			try {
//				String result = Nlinterface.getStrategy();
//				log.debug( "###############查询结果[" + result+"]");
//			} catch (Exception e) {
//				log.error( "客户端工具管理->连接服务器失败->" + e.toString(), e);
//				JSONObject jsonResp = new JSONObject();
//				jsonResp.put("success", false);
//				jsonResp.put("message", "连接服务器失败，请稍后重试");
//				jsonStr = jsonResp.toString();
//				serverConnectFlag = false; // 登录出现异常时连接设置为连接不上
//			}
//			log.debug( "--createGridDateCreater-->serverConnectFlag:"+serverConnectFlag);
//			if (serverConnectFlag) {
//				try {
//					log.debug( "--createGridDateCreater-->nodeInfoMap:"+nodeInfoMap);
//					session.setAttribute(Constant.SUN_NODE_INFO_MAP,
//							nodeInfoMap);
//					log.debug( "--createGridDateCreater-->parameterMap:"+parameterMap);
//					session.setAttribute(Constant.SUN_REQUEST_PARAMETERS,
//							parameterMap);
//
//					Creater cModel_greadDataCreater = formPanelCreater
//							.createCreaterModel(model_code);
//					log.debug( "--createGridDateCreater-->cModel_greadDataCreater:"+cModel_greadDataCreater);
//					jsonStr = new JSONUtil().createJsonDataByMapList(
//							cModel_greadDataCreater.getCreater(),
//							cModel_greadDataCreater.getCreater().size());
//					log.debug( "--createGridDateCreater-->客户端工具管理->列表data模板成功!");
//				} catch (Exception e) {
//					log.error( "客户端工具管理->列表data模板失败->" + e.toString(), e);
//					JSONObject jsonResp = new JSONObject();
//					jsonResp.put("success", false);
//					jsonResp.put("message", "列表data模板失败，请稍后重试");
//					jsonStr = jsonResp.toString();
//				}
//			}
//		}
//		this.outJsonString(jsonStr);
//		log.info( "--createGridDateCreater(over)");
//		return null;
//	}
//
//	/**
//	 * 查询批次信息
//	 * 
//	 * @return
//	 */
//	public String queryBatchInfo() {
//		log.info( "--queryBatchInfo(start)");
//		String resultMsg = null;
//		String jsonStr = null;
//		HttpSession session = getSession();
//		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//		List<Map<String, Object>> batch = new ArrayList<Map<String, Object>>();
//		List<Map<String, Object>> totalBatch = new ArrayList<Map<String, Object>>();
//		Map<String, String> parameterMap = formPanelCreater
//				.getParameterMap(this.getRequest());
//		log.debug( "--queryBatchInfo-->parameterMap:"+parameterMap);
//		String flag = parameterMap.get("flag");
//		parameterMap.remove("flag");
//		Map<String, String> customAtt = formPanelCreater
//				.getCustomAtt(parameterMap);
//		log.debug( "--queryBatchInfo-->customAtt:"+customAtt);
////		List<String> sysc = NearLineStringUtil.getSystemColmon(model_code);
////		for (String s : sysc) {
////			System.out.println(s);
////		}
//		log.debug( "--queryBatchInfo-->falg:"+flag);
//		if (flag.equals("0")) {// 在线
//			Map<String, String> nodeInfoMap = formPanelCreater
//					.getNodeInfoMap(server_id);
//			log.debug( "--queryBatchInfo-->nodeInfoMap:"+nodeInfoMap);
//			ClientHeightQuery heightQuery = formPanelCreater
//					.setClientHeightQuery(user.getLogin_id(), user.getUser_password(),customAtt, model_code);
//			String dmName = DMAdressConstuct.getDMName();
//			log.debug( "--queryBatchInfo-->dmName:"+dmName);
//			SunEcmClientApi clientApi = new SunEcmClientWebserviceApiImpl(
//					nodeInfoMap.get("ip"), Integer.parseInt(nodeInfoMap
//							.get("http")), dmName);
//			if (parameterMap.get("batch_id") != null
//					&& !parameterMap.get("batch_id").equals(""))
//				heightQuery.addfilters("content_id= '"
//						+ parameterMap.get("batch_id") + "'");
//			try {
//				resultMsg = null;
//				log.debug( "--queryBatchInfo-->userName:"+user.getLogin_id()+";userPassWord"+user.getUser_password());
//				clientApi.login(user.getLogin_id(), user.getUser_password());
//				resultMsg = clientApi.heightQuery(heightQuery, "");
//				clientApi.logout(user.getLogin_id());
//				log.debug( "#######调用高级搜索返回的信息[" + resultMsg + "]#######");
//			} catch (Exception e) {
//				// 服务器连接不上，
//				log.error( "查询批次->连接服务器失败"+e.toString(),e);
//				JSONObject jsonResp = new JSONObject();
//				jsonResp.put("success", false);
//				jsonResp.put("message", "连接服务器失败，请稍后重试");
//				jsonStr = jsonResp.toString();
//			}
//			log.debug( "--queryBatchInfo-->resultMsg:"+resultMsg);
//			if (resultMsg!=null&&resultMsg.startsWith("0001<<::>>")) {
//				HeightQuery hq = XMLUtil.xml2Bean(XMLUtil
//						.removeHeadRoot(resultMsg
//								.replaceFirst("0001<<::>>", "")),
//						HeightQuery.class);
//				List<BatchIndexBean> batchList = hq.getIndexBeans();
//				if (batchList != null && batchList.size() > 0) {
//					log.debug( "--queryBatchInfo-->batchList:"+batchList);
//					ContentObjectInfoBean objBean = comdao.getContentObjOffDate(model_code);
//					String  start_date="";
//					if(objBean!=null){
//						//获取业务开始时间字段
//						 start_date = objBean.getCreation_date_column();
//					}
//
//					for (int i = 0; i < batchList.size(); i++) {
//						BatchIndexBean batchIndex = new BatchIndexBean();
//						batchIndex = batchList.get(i);
//						log.debug( "--queryBatchInfo-->batchIndex:"+batchIndex);
//						Map<String, Object> m = new HashMap<String, Object>();
//						Map<String,String> map=batchIndex.getCustomMap();
//						 if(map.containsKey(start_date)){
//							 //存在业务开始时间，则将该信息加入到map中
//							 String start_date_value=map.get(start_date);
//							 m.put("start_date", start_date);
//							 m.put("start_date_value", start_date_value);
//						 }
//						m.putAll(batchIndex.getCustomMap());
//						m.put("contentID", batchIndex.getContentID());
//						m.put("max_version", batchIndex.getMaxVersion());
//						log.debug( "--queryBatchInfo-->m:"+m);
//						batch.add(m);
//					}
//				}
//				log.debug( "--queryBatchInfo-->parameterMap:"+parameterMap);
//				session.setAttribute(Constant.SUN_REQUEST_PARAMETERS,
//						parameterMap);
//				log.debug( "--queryBatchInfo-->nodeInfoMap:"+nodeInfoMap);
//				session.setAttribute(Constant.SUN_NODE_INFO_MAP, nodeInfoMap);
//				// String mapstr = "";
//				// for (String key:nodeInfoMap.keySet()) {
//				// mapstr +=key+"="+nodeInfoMap.get(key)+",";
//				// }
//				// log.debug("#############session.setAttribute("+Constant.SUN_NODE_INFO_MAP+",{"+mapstr+"}");
//				jsonStr = new JSONUtil().createJsonDataByMapList(batch, batch
//						.size());
//			}
//		} else {// 近线
//			Map<String, String> nodeInfoMap = formPanelCreater.getNodeInfoMap();
//			log.debug( "--queryBatchInfo-->nodeInfoMap:"+nodeInfoMap);
//			log.debug( "--queryBatchInfo-->start:"+start);
//			if(start.equals("0")){
//				NearLineWsInterface Nlinterface = new WSNearLineClient()
//						.getNearLineClient("http://" + nodeInfoMap.get("ip") + ":"
//								+ nodeInfoMap.get("http") + "/"
//								+ nodeInfoMap.get("name")
//								+ "/webservices/WsInterface", 300000);
//				String xml = getNLsearchXML();
//				log.debug( "--queryBatchInfo-->xml:"+xml);
//				resultMsg = Nlinterface.search(xml);
//				log.debug( "######近线客户端查询结果[" + resultMsg + "]######");
//				List<BatchIndexBean> batchList = NearLineStringUtil.getBatchList(resultMsg,model_code);
//				if (batchList != null && batchList.size() > 0) {
//					log.debug( "--queryBatchInfo-->batchList:"+batchList);
//					for (int i = 0; i < batchList.size(); i++) {
//						BatchIndexBean batchIndex = new BatchIndexBean();
//						batchIndex = batchList.get(i);
//						log.debug( "--queryBatchInfo-->batchIndex:"+batchIndex);
//						Map<String, Object> m = new HashMap<String, Object>();
//						m.putAll(batchIndex.getCustomMap());
//						m.put("contentID", batchIndex.getContentID());
//						m.put("max_version", batchIndex.getMaxVersion()); // VERSION
//						log.debug( "--queryBatchInfo-->m:"+m);
//						totalBatch.add(m);
//					}
//					NearLineBatchListStatic.getInstance().addBatchList(totalBatch);
//				}
//			}else{
//				totalBatch = NearLineBatchListStatic.getInstance().getBatchList();
//			}
//			batch = getSubList(totalBatch, start, limit);
//			log.debug( "--queryBatchInfo-->parameterMap:"+parameterMap);
//			log.debug( "--queryBatchInfo-->nodeInfoMap:"+nodeInfoMap);
//			session.setAttribute(Constant.SUN_REQUEST_PARAMETERS, parameterMap);
//			session.setAttribute(Constant.SUN_NODE_INFO_MAP, nodeInfoMap);
//			jsonStr = new JSONUtil().createJsonDataByMapList(batch, totalBatch
//					.size());
//		}
//		this.outJsonString(jsonStr);
//		log.info( "--queryBatchInfo(over)");
//		return null;
//	}
//
//
//	/**
//	 * 近线批次分页
//	 * @param totalBatch 总批次列表信息
//	 * @param start2 分页参数
//	 * @param limit2 分页参数
//	 * @return
//	 */
//	private List<Map<String, Object>> getSubList(
//			List<Map<String, Object>> totalBatch, String start2, String limit2) {
//		log.info( "--getSubList-->totalBatch:"+totalBatch);
//		int iStart = Integer.valueOf(start2);
//		int iLimit = Integer.valueOf(limit2)+Integer.valueOf(start2);
//		log.debug( "--getSubList-->iStart:"+iStart+";iLimit:"+iLimit);
//		log.debug( "--getSubList-->totalBatch.size():"+totalBatch.size());
//		if(iStart > totalBatch.size()){
//			iStart = totalBatch.size();
//		}
//		if(iLimit > totalBatch.size() || iLimit < iStart){
//			iLimit = totalBatch.size();
//		}
//		log.debug( "--getSubList-->iStart:"+iStart+";iLimit:"+iLimit);
//		log.info( "--getSubList(over)");
//		return totalBatch.subList(iStart, iLimit);
//	}
//
//	/**
//	 * 组装近线查询 XML
//	 * 
//	 * @return
//	 */
//	private String getNLsearchXML() {
//		log.info( "--getNLsearchXML(start)");
//		StringBuffer buffer = new StringBuffer();
//		String content_time = getContentTime();
//
//		Map<String, String> parameterMap = formPanelCreater
//				.getParameterMap(this.getRequest());
//		log.debug( "--getNLsearchXML-->parameterMap:"+parameterMap);
//		parameterMap.remove("flag");
//		buffer.append("<?xml version=\"1.0\" encoding=\"UTF-8\" ?>");
//		buffer.append("<ROOT>");
//		buffer.append("<CONTENT_MODEL CODE = '").append(model_code)
//				.append("'>");
//		log.debug( "--getNLsearchXML-->content_time:"+content_time);
//		if(parameterMap.get(content_time)!=null && !parameterMap.get(content_time).equals("")){
//			
//			buffer.append("<CONTENT_TIME>").append(parameterMap.get(content_time))
//			.append("</CONTENT_TIME>");
//		}
//		log.debug( "--getNLsearchXML-->batch_id:"+batch_id);
//		if(batch_id !=null && !batch_id.equals("")){
//			buffer.append("<CONTENT_ID>").append(batch_id).append("</CONTENT_ID>");
//		}
//		// 自定义属性添加属性添加
//		parameterMap.remove(content_time);
//		log.debug( "--getNLsearchXML-->parameterMap:"+parameterMap);
//		Map<String, String> customAtt = formPanelCreater
//				.getCustomAtt(parameterMap);
//		log.debug( "--getNLsearchXML-->customAtt:"+customAtt);
//		Set<String> key = customAtt.keySet();
//		for (String it : key) {
//			log.debug( "--getNLsearchXML-->it:"+it);
//			buffer.append("<").append(it).append(">");
//			buffer.append(parameterMap.get(it));
//			buffer.append("</").append(it).append(">");
//
//		}
//		buffer.append("</CONTENT_MODEL>");
//		buffer.append("</ROOT>");
//		log.debug( "########近线查询xml[" + buffer.toString() + "]####");
//		log.info( "--getNLsearchXML(over)");
//		return buffer.toString();
//	}
//
//	/**
//	 * 获取开始时间参数
//	 * 
//	 * @return
//	 */
//	private String getContentTime() {
//		log.info( "--getContentTime(start)");
//		GetDBConfigDao configDao = new GetDBConfigDaoImpl();
//		// List<UnityAccessServerInfoBean> serverList =
//		// uadao.getUnityAccessServerList(0, true);
//		String content_time = null;
//		String[] strAry = { model_code };
//		List<ModelTemplateBean> list;
//		try {
//			list = configDao.getModelTemplate(null, strAry);
//			for (ModelTemplateBean modelTemplateBean : list) {
//				if (modelTemplateBean.getCreation_date_column() != null) {
//					content_time = modelTemplateBean.getCreation_date_column();
//				}
//			}
//			log.debug( "--getContentTime-->content_time:"+content_time);
//		} catch (Exception e) {
//			log.error( "客户端管理->获取内容模型开始时间字段失败"+e.toString(), e);
//		}
//		log.info( "--getContentTime(over)");
//		return content_time;
//	}
//
//	/**
//	 * 根据内容模型代码获取与其对应服务器组列表
//	 * 
//	 * @param model_code
//	 * @return
//	 */
//	public String getContentServerGroupListByModelCode() {
//		log.info( "--getContentServerGroupListByModelCode(start)-->model_code:"+model_code);
//		List result = emdao.getAllServerGroupByModelCode(model_code);
//		log.debug( "--getContentServerGroupListByModelCode(over)");
//		this.outJsonString(new JSONUtil().createJsonDataByMapList(result,
//				result.size()));
//		log.info( "--getContentServerGroupListByModelCode(over)-->model_code:"+model_code);
//		return null;
//	}
//
//	public String getBatchInfoByContentID() {
//		log.info( "--getBatchInfoByContentID(start)-->contentID:"+contentID);
//		HttpSession session = this.getSession();
//		Map<String, String> parameterMap = (Map<String, String>) session
//		.getAttribute(Constant.SUN_REQUEST_PARAMETERS);
//		log.debug( "--getBatchInfoByContentID-->parameterMap:"+parameterMap);
//		Map<String, String> customAtt = formPanelCreater
//		.getCustomAtt(parameterMap);
//		log.debug( "--getBatchInfoByContentID-->customAtt:"+customAtt);
//		Map<String, String> nodeInfoMap = (Map<String, String>) session
//		.getAttribute(Constant.SUN_NODE_INFO_MAP);
//		log.debug( "--getBatchInfoByContentID-->nodeInfoMap:"+nodeInfoMap);
//		String dmName = DMAdressConstuct.getDMName();
//		log.debug( "--getBatchInfoByContentID-->dmName:"+dmName);
//		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//		log.debug( "--getBatchInfoByContentID-->Login_id:"+user.getLogin_id()+"getUser_password"+user.getUser_password());
//		EclientManageUtil util=new EclientManageUtil();
//		ClientBatchBean clientBatchBean = new ClientBatchBean();
//		clientBatchBean.setModelCode(model_code);
//		clientBatchBean.setUser(user.getLogin_id());
//		clientBatchBean.getIndex_Object().setVersion(max_version);
//		clientBatchBean.getIndex_Object().setContentID(contentID);
//		clientBatchBean.getIndex_Object().setCustomMap(customAtt);
//		clientBatchBean.setPassWord(user.getUser_password());
//		//获取安全令牌
//		Map<String,String> map=util.getToken(model_code, user.getLogin_id(), nodeInfoMap.get("ip"));
//		if(map!=null&&map.get("NEED_TOKEN").equals("true")){
//			//需要令牌校验
//			clientBatchBean.setToken_check_value(map.get("TOKEN_CHECK_VALUE"));
//			clientBatchBean.setToken_code(map.get("TOKEN_CODE"));
//		}
//		log.debug( "--getBatchInfoByContentID-->clientBatchBean:"+clientBatchBean);
//		String resultMsg = null;
//		log.debug( "--getBatchInfoByContentID-->flag:"+flag);
//		if(flag == null || flag.equals("0")){//在线
//			log.debug( "--getBatchInfoByContentID-->flag is null or flag == 0");
//			// 若内容模型配置有安全校验
//			SunEcmClientApi clientApi = new SunEcmClientWebserviceApiImpl(
//					nodeInfoMap.get("ip"), Integer
//							.parseInt(nodeInfoMap.get("http")), dmName);
//			try {
//				log.debug( "--getBatchInfoByContentID-->-->登入");
//				clientApi.login(user.getLogin_id(), user.getUser_password());
//				resultMsg = clientApi.queryBatch(clientBatchBean, dmName);
//				log.debug( "#######查询在线批次返回的信息[" + resultMsg + "]#######");
//				clientApi.logout(user.getLogin_id());
//				log.debug( "--getBatchInfoByContentID-->-->登出");
//				session.setAttribute("data", resultMsg);
//			} catch (Exception e) {
//				log.error( "查询在线批次返回的信息失败！"+e.toString(), e);
//			}
//		}else{//近线
//			resultMsg = NearLineBatchListStatic.getInstance().getBatchMap().get(contentID);
//			session.setAttribute("data", resultMsg);
//			log.debug( "#######查询近线批次返回的信息[" + resultMsg + "]#######");
//		}
//		log.info( "--getBatchInfoByContentID(over)");
//		return null;
//	}
//
//	public String delBatch() {
//		log.info( "--delBatch(start)");
//		String jsonStr = null;
//		JSONObject jsonResp = new JSONObject();
//		HttpSession session = this.getSession();
//		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//		Map<String, String> parameterMap = (Map<String, String>) session
//				.getAttribute(Constant.SUN_REQUEST_PARAMETERS);
//		log.debug( "--delBatch-->parameterMap:"+parameterMap);
//		Map<String, String> customAtt = formPanelCreater
//				.getCustomAtt(parameterMap);
//		log.debug( "--delBatch-->customAtt:"+customAtt);
//		Map<String, String> nodeInfoMap = (Map<String, String>) session
//				.getAttribute(Constant.SUN_NODE_INFO_MAP);
//		log.debug( "--delBatch-->nodeInfoMap:"+nodeInfoMap);
//		ClientBatchBean clientBatchBean = new ClientBatchBean();
//		clientBatchBean.setModelCode(model_code);
//		clientBatchBean.setUser(user.getLogin_id());
//		clientBatchBean.getIndex_Object().setContentID(contentID);
//		clientBatchBean.getIndex_Object().setCustomMap(customAtt);
//		clientBatchBean.setPassWord(user.getUser_password());
//		
//		//获取安全令牌
//		EclientManageUtil util=new EclientManageUtil();
//		Map<String,String> map=util.getToken(model_code, user.getLogin_id(), nodeInfoMap.get("ip"));
//		if(map!=null&&map.get("NEED_TOKEN").equals("true")){
//			//需要令牌校验
//			clientBatchBean.setToken_check_value(map.get("TOKEN_CHECK_VALUE"));
//			clientBatchBean.setToken_code(map.get("TOKEN_CODE"));
//		}
//		
//		log.debug( "--delBatch-->clientBatchBean:"+clientBatchBean);
//		String dmName = DMAdressConstuct.getDMName();
//		log.debug( "--delBatch-->dmName:"+dmName);
//		SunEcmClientApi clientApi = new SunEcmClientWebserviceApiImpl(
//				nodeInfoMap.get("ip"), Integer
//						.parseInt(nodeInfoMap.get("http")), dmName);
//		try {
//			log.debug( "----delBatch-->Login_id:"+user.getLogin_id()+",password"+user.getPassword()+",checkUserId:"+checkId);
//			clientApi.login(user.getLogin_id(), user.getUser_password());
//			String resultMsg = clientApi.delete(clientBatchBean, dmName);
//			clientApi.logout(user.getLogin_id());
//			log.debug( "#######删除批次返回的信息[" + resultMsg + "]#######");
//			if (resultMsg.equals("0001<<::>>SUCCESS")) {
//				jsonResp.put("success", true);
//				jsonResp.put("message", "批次删除成功!!");
//				jsonStr = jsonResp.toString();
//			} else {
//				jsonResp.put("success", false);
//				jsonResp.put("message", "批次删除失败!!");
//				jsonStr = jsonResp.toString();
//			}
//		} catch (Exception e) {
//			log.error( "批次删除失败!!", e);
//		}
//		this.outJsonString(jsonStr);
//		log.info( "--delBatch(over)");
//		return null;
//	}
//	/**
//	 * 校验模型是否需要安全验证，如果需要则进行验证并返回token值（直接从console中gettoken）
//	 * @return 
//	 * 
//	 */
//	public String getToken() {
//		log.info( "--getToken(start)");
//		String jsonStr = null;
//		JSONObject jsonResp = new JSONObject();
//		HttpSession session = this.getSession();
//		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//		Map<String, String> nodeInfoMap = (Map<String, String>) session
//		.getAttribute(Constant.SUN_NODE_INFO_MAP);
//		String client_ip=nodeInfoMap.get("ip");
//		
//		EclientManageUtil eUtile=new EclientManageUtil();
//		Map<String,String> map=eUtile.getToken(model_code, user.getLogin_id(), client_ip);
//		
//		if(map!=null){
//			String needToken=map.get("NEED_TOKEN");
//			if(needToken!=null&&needToken.equals("true")){
//				//需要令牌校验
//				jsonResp.put("NEED_TOKEN", true);
//				jsonResp.put("TOKEN_CHECK_VALUE",map.get("TOKEN_CHECK_VALUE") );
//				jsonResp.put("TOKEN_CODE", map.get("TOKEN_CODE"));
//				
//			}else {
//				//不需要校验,返回
//				jsonResp.put("NEED_TOKEN", false);
//			}
//		}
//		jsonStr = jsonResp.toString();
//		this.outJsonString(jsonStr);
//		log.info( "--getToken(over)");
//		return null;
//	}
//
//	/**
//	 * 构建内容模型节点
//	 * 
//	 * @return
//	 */
//	public String getDocPartList() {
//		log.info( "--getDocPartList(start)-->model_code:"+model_code);
//		String jsonStr = "";
//		try {
//			List<TreeBean> list = comdao.getDocPartList(model_code);
//			ContentObjectInfoBean bean = comdao
//					.getContentObjectByCode(model_code);
//			if (list != null && list.size() > 0) {
//				TreeBean tree = new TreeBean();
//				jsonStr = tree.createJsonStr(list);
//				jsonStr = "[{'id':'" + bean.getModel_code() + "','text':'"
//						+ bean.getModel_name()
//						+ "','leaf':false,'expanded':true,'children':"
//						+ jsonStr + "}]";
//			} else
//				jsonStr = "[{'id':'" + bean.getModel_code() + "','text':'"
//						+ bean.getModel_name()
//						+ "','leaf':true,'expanded':true}]";
//			log.debug( "--getDocPartList-->获取内容对象列表成功!!");
//		} catch (Exception e) {
//			JSONObject jsonResp = new JSONObject();
//			jsonResp.put("success", false);
//			jsonResp.put("message", "获取内容对象列表失败!!");
//			jsonStr = jsonResp.toString();
//			log.error( "客户端工具管理->构建内容模型节点失败->" + e.toString(), e);
//		}
//		this.outJsonString(jsonStr);
//		log.info( "--getDocPartList(over)-->model_code:"+model_code);
//		return null;
//	}
//
//	/**
//	 * 创建批次属性信息Form
//	 * 
//	 * @return
//	 */
//	public String batchAttribute() {
//		log.info( "--batchAttribute");
//		try {
//			Map<String, Object> m = formPanelCreater.createAccordingPanel(
//					model_code).getPanel();
//			this.outString("var panel" + model_code + " = new Ext.Panel("
//					+ new JSONUtil().createExtJS(m) + ")");
//		} catch (Exception e) {
//			log.error( "客户端工具管理->创建批次属性信息Form失败->" + e.toString(), e);
//		}
//		log.info( "--batchAttribute(over)");
//		return null;
//	}
//	/**
//	 * 校验该内容对象代码是否存在
//	 * 
//	 * @return
//	 */
//	public String checkUserLogin() {
//		String user_name = "";
//		boolean success = true;
//		try {
//			HttpSession session = getSession();
//			UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//			
//			String user_password = user.getUser_password();
//			user_name = user.getUser_name();
//			if (user_password == null || user_password.equals("")) {
//				// 用户未登录，需要用户登录
//				success = false;
//			}
//		} catch (Exception e) {
//			success = false;
//			// 记录日志
//			log.error(e.toString());
//		}
//		JSONObject jsonResp = new JSONObject();
//		String jsonStr = "";
//
//		jsonResp.put("success", success);
//		jsonResp.put("username", user_name);
//		jsonStr = jsonResp.toString();
//		this.outJsonString(jsonStr);
//		log.info("--checkUserLogin(over)");
//		return null;
//	}
//
////	public static void main(String[] args) {
////		String ip = "************";
////		int httpPort = 8080;
////		String a;
////		try {
////			ClientHeightQuery heightQuery = new ClientHeightQuery();
////			heightQuery.setLimit(5);
////
////			SunEcmClientApi clientApi = new SunEcmClientWebserviceApiImpl(ip,
////					httpPort, "SunECMDM");
////			a = clientApi.getContentServerInfo_Client();
////			System.out.println(a);
////		} catch (Exception e) {
////			// TODO Auto-generated catch block
////			e.printStackTrace();
////		}
////	}
//	
//}
