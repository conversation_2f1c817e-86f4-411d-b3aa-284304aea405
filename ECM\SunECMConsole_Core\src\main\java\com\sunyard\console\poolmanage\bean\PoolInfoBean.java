package com.sunyard.console.poolmanage.bean;

public class PoolInfoBean {
	private int state;// 状态
	private String poolname;// 连接池名称
	private int sdb_connection_id;// 连接池id
	private String urls;// 连接池地址
	private String so_maxidenum;// 保留连接数量
	private String co_connecttimeout;// 失败超时时间
	private String so_maxconnectionnum;// 最大连接数
	private String co_maxautoconnectretrytime;// 失败重试次数
	private int validateConnection;// 连接出池时，是否检测连接的可用性，默认不检测。
	private String so_deltainccount;// 增加连接数
	private String so_abandontime;// 连接存活时间
	private String so_recheckcycleperiod;// 清除周期
	private String so_syncCoordInterval;// 同步周期
	private String username;//用户名
	private String password;//登陆密码

	@Override
	public String toString() {
		return "PoolInfoBean [state=" + state + ", poolname=" + poolname
				+ ", sdb_connection_id=" + sdb_connection_id + ", urls=" + urls
				+ ", so_maxidenum=" + so_maxidenum + ", co_connecttimeout="
				+ co_connecttimeout + ", so_maxconnectionnum="
				+ so_maxconnectionnum + ", co_maxautoconnectretrytime="
				+ co_maxautoconnectretrytime + ", ValidateConnection="
				+ validateConnection + ", so_deltainccount=" + so_deltainccount
				+ ", so_abandontime=" + so_abandontime
				+ ", so_recheckcycleperiod=" + so_recheckcycleperiod
				+ ", so_syncCoordInterval=" + so_syncCoordInterval
				+ ", username=" + username + ", password=" + password + "]";
	}

	public String getUsername() {
		return username;
	}

	public int getValidateConnection() {
		return validateConnection;
	}

	public void setValidateConnection(int validateConnection) {
		this.validateConnection = validateConnection;
	}

	public String getSo_syncCoordInterval() {
		return so_syncCoordInterval;
	}

	public void setSo_syncCoordInterval(String so_syncCoordInterval) {
		this.so_syncCoordInterval = so_syncCoordInterval;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getPoolname() {
		return poolname;
	}

	public void setPoolname(String poolname) {
		this.poolname = poolname;
	}

	public int getSdb_connection_id() {
		return sdb_connection_id;
	}

	public void setSdb_connection_id(int sdb_connection_id) {
		this.sdb_connection_id = sdb_connection_id;
	}

	public String getUrls() {
		return urls;
	}

	public void setUrls(String urls) {
		this.urls = urls;
	}

	public String getSo_maxidenum() {
		return so_maxidenum;
	}

	public void setSo_maxidenum(String so_maxidenum) {
		this.so_maxidenum = so_maxidenum;
	}

	public String getCo_connecttimeout() {
		return co_connecttimeout;
	}

	public void setCo_connecttimeout(String co_connecttimeout) {
		this.co_connecttimeout = co_connecttimeout;
	}

	public String getSo_maxconnectionnum() {
		return so_maxconnectionnum;
	}

	public void setSo_maxconnectionnum(String so_maxconnectionnum) {
		this.so_maxconnectionnum = so_maxconnectionnum;
	}

	public String getCo_maxautoconnectretrytime() {
		return co_maxautoconnectretrytime;
	}

	public void setCo_maxautoconnectretrytime(String co_maxautoconnectretrytime) {
		this.co_maxautoconnectretrytime = co_maxautoconnectretrytime;
	}

	

	public String getSo_deltainccount() {
		return so_deltainccount;
	}

	public void setSo_deltainccount(String so_deltainccount) {
		this.so_deltainccount = so_deltainccount;
	}



	public String getSo_abandontime() {
		return so_abandontime;
	}

	public void setSo_abandontime(String so_abandontime) {
		this.so_abandontime = so_abandontime;
	}

	public String getSo_recheckcycleperiod() {
		return so_recheckcycleperiod;
	}

	public void setSo_recheckcycleperiod(String so_recheckcycleperiod) {
		this.so_recheckcycleperiod = so_recheckcycleperiod;
	}

	

}
