package com.sunyard.console.contentservermanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.contentmodelmanage.bean.TreeBean;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.sunyard.console.common.accesscontrol.LicenseFileConstant;
import com.sunyard.console.contentservermanage.bean.ContentServerGroupInfoBean;
import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.contentservermanage.bean.GroupCmodelRelInfoBean;
import com.sunyard.console.contentservermanage.dao.ContentServerGroupManageDAO;
import com.sunyard.console.threadpoool.IssueUtils;

/**
 * <p>
 * Title: 内容存储服务器组管理Action
 * </p>
 * <p>
 * Description: 内容存储服务器组管理
 * </p>
 * <p>
 * Copyright: Copyright (c) 2012
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0 
 */
@Controller
public class ContentServerGroupManageAction extends BaseAction {
	/**
	 * 服务器组数据库操作类对象
	 */
	@Autowired
	ContentServerGroupManageDAO contentServerGroupManageDao;
	private int group_id;// 服务器id
	private String group_name;// 服务器名称
	private String group_ip;// 采用第三方其群服务时 ， 集群服务器组对外的IP
	private int http_port;// http端口
	private int https_port;// https端口
	private int socket_port;// socket端口
	private int state;// 0:停用 1:启用
	private int os;// 1:windows 2:mac 3:linux 4:aix
	private String remark;// 备注
	private int start;
	private int limit;
	private int deploy_mode;// 0:ECM负载均衡 1:其他集群方式
	private String value;// 唯一性校验
	private int is_ecm_db;//是不是使用ecm数据库，默认1，是ecm为1，其他是0
	private String trans_protocol;//

	public String getTrans_protocol() {
		return trans_protocol;
	}

	public void setTrans_protocol(String trans_protocol) {
		this.trans_protocol = trans_protocol;
	}

	public int getHttps_port() {
		return https_port;
	}

	public void setHttps_port(int https_port) {
		this.https_port = https_port;
	}

	/**
	 * 日志对象
	 */
	private  final static Logger log = LoggerFactory.getLogger(ContentServerGroupManageAction.class);
	/**
	 * 增加和修改内容存储服务器标识
	 */
	private String optionFlag;// 
	/**
	 * 服务器id组成的字符串，之间用“，”隔开
	 */
	private String server_ids;
	/**
	 * 服务器组id组成的字符串
	 */
	private String group_ids;
	private String modelCode_volumeId;
	/**
	 * 由serverId和权重组成的字符串，格式为“serverId=权重”，多个之间用“,”号隔开
	 */
	private String serverId_weight;
	/**
	 * 由内容模型代码组成的字符串，用”，“隔开
	 */
	private String modelCodes;

	public int getIs_ecm_db() {
		return is_ecm_db;
	}

	public void setIs_ecm_db(int isEcmDb) {
		is_ecm_db = isEcmDb;
	}

	public ContentServerGroupManageDAO getContentServerGroupManageDao() {
		return contentServerGroupManageDao;
	}

	public void setContentServerGroupManageDao(
			ContentServerGroupManageDAO contentServerGroupManageDao) {
		this.contentServerGroupManageDao = contentServerGroupManageDao;
	}

	public int getGroup_id() {
		return group_id;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public void setGroup_id(int groupId) {
		group_id = groupId;
	}

	public String getGroup_name() {
		return group_name;
	}

	public String getModelCodes() {
		return modelCodes;
	}

	public void setModelCodes(String modelCodes) {
		this.modelCodes = modelCodes;
	}

	public void setGroup_name(String groupName) {
		group_name = groupName;
	}

	public String getServerId_weight() {
		return serverId_weight;
	}

	public void setServerId_weight(String serverIdWeight) {
		serverId_weight = serverIdWeight;
	}

	public String getGroup_ip() {
		return group_ip;
	}

	public void setGroup_ip(String groupIp) {
		group_ip = groupIp;
	}

	public int getHttp_port() {
		return http_port;
	}

	public void setHttp_port(int httpPort) {
		http_port = httpPort;
	}

	public int getSocket_port() {
		return socket_port;
	}

	public void setSocket_port(int socketPort) {
		socket_port = socketPort;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public int getOs() {
		return os;
	}

	public void setOs(int os) {
		this.os = os;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	public int getDeploy_mode() {
		return deploy_mode;
	}

	public void setDeploy_mode(int deployMode) {
		deploy_mode = deployMode;
	}

	public String getServer_ids() {
		return server_ids;
	}

	public void setServer_ids(String serverIds) {
		server_ids = serverIds;
	}

	public String getGroup_ids() {
		return group_ids;
	}

	public void setGroup_ids(String groupIds) {
		group_ids = groupIds;
	}

	public String getModelCode_volumeId() {
		return modelCode_volumeId;
	}

	public void setModelCode_volumeId(String modelCodeVolumeId) {
		modelCode_volumeId = modelCodeVolumeId;
	}

	/**
	 * 分页查询内容存储服务器组list，将结果集组合成jsonStr，返回给页面
	 * 
	 * @return ContentServerInfoBean
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getContentServerGroupListAction.action", method = RequestMethod.POST)
	public String getContentServerGroupList(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int start = modelJson.getInt("start");
		int limit = modelJson.getInt("limit");
		start = (start-1) * limit;
        String group_name = (String) modelJson.getOrDefault("group_name","");
        String group_id = (String) modelJson.getOrDefault("group_id","");
		log.info("--getContentServerGroupList(start)-->group_id:" + group_id + ";group_name:" + group_name);
		String jsonStr = null;
		group_id = "".equals(group_id) || group_id == null ? "0" : group_id;
		try {
			group_name = URLDecoder.decode(group_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode ContentServerGroupServer fields error, group_name=" + group_name);
		}
		try {
			List<ContentServerGroupInfoBean> contentServerGroupInfoList = contentServerGroupManageDao
					.getContentServerGroupList(Integer.valueOf(group_id), group_name, start + 1,
							limit);
			List<ContentServerGroupInfoBean> AllInfoList = contentServerGroupManageDao
					.getContentServerGroupList(Integer.valueOf(group_id), group_name);
			int size = 0;
			if (AllInfoList != null && AllInfoList.size() > 0) {
				size = AllInfoList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(
					contentServerGroupInfoList, size,
					new ContentServerInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器组信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器管理->获取服务器组列表失败" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getContentServerGroupList(over)-->group_id:" + group_id);
		return null;
	}

	/**
	 * 分页查询内容存储服务器组list，将结果集组合成jsonStr，返回给页面 将所有新增或者删除的关联关系的服务器 下发到
	 * 所有启用的==服务器+统一接入服务器+该服务器本身
	 * 
	 * @return ContentServerInfoBean
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/addContentServerGroupAction.action", method = RequestMethod.POST)
	public String addContentServerGroup(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int group_id = modelJson.getInt("group_id");
		String group_name = (String) modelJson.getOrDefault("group_name", "");
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");
		int deploy_mode = modelJson.getInt("deploy_mode");
		String group_ip = (String) modelJson.getOrDefault("group_ip", "");
		int http_port = modelJson.getInt("http_port");
		int https_port = modelJson.getInt("https_port");
		int socket_port = modelJson.getInt("socket_port");
		String trans_protocol = (String) modelJson.getOrDefault("trans_protocol", "");
		int os = modelJson.getInt("os");
		String remark = (String) modelJson.getOrDefault("remark", "");
		int state = modelJson.getInt("state");
		int is_ecm_db = modelJson.getInt("is_ecm_db");
		String server_ids = (String) modelJson.getOrDefault("server_ids", "");
		String serverId_weight = (String) modelJson.getOrDefault("serverId_weight", "");
		try {
			group_name = URLDecoder.decode(group_name, "utf-8");
			remark = URLDecoder.decode(remark, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode UnityAccessServer fields error, group_name=" + group_name
					+ ", remark=" + remark, e1);
		}
		log.info( "--addContentServerGroup(start)-->group_id:" + group_id + ";group_name:" + group_name);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		ContentServerGroupInfoBean bean = new ContentServerGroupInfoBean();
		if(optionFlag !=null && optionFlag.equals("create1"))
		if(checkMaxUser()){ //添加服务器组时判断是否超过最大数限制
			log.debug( "当前服务器组数量未达上限，允许添加存储服务器");
		}else{
			log.warn( "当前服务器组数量已达上限，无法添加存储服务器");
			jsonResp.put("success", false);
			jsonResp.put("message", "当前服务器组数量已达上限，无法添加存储服务器!");
			jsonStr = jsonResp.toString();
			this.outJsonString(jsonStr);
			return null;
		}
		if (deploy_mode == 0) {// Ecm负载均衡,不需要设置ip和端口
			group_ip = "";
			http_port = 0;
			socket_port = 0;
			https_port=0;
			trans_protocol="";
		}
		bean.setGroup_id(group_id);
		bean.setGroup_ip(group_ip);
		bean.setGroup_name(group_name);
		bean.setHttp_port(http_port);
		bean.setOs(os);
		bean.setRemark(remark);
		bean.setSocket_port(socket_port);
		bean.setState(state);
		bean.setDeploy_mode(deploy_mode);
		bean.setIs_ecm_db(is_ecm_db);
		bean.setHttps_port(https_port);
		bean.setTrans_protocol(trans_protocol);
		try {
			boolean result = false;
			// 获取服务器组新增和删除的服务器关联关系列表组成的map
			Map<String, List<String>> map = contentServerGroupManageDao
					.getDiffContentServer(server_ids, group_id);

			// 根据部署方式（0为ECM负载均衡，需要传递权重，否则为其他负载方式，不需要传权重），来确定前台传过来的服务器id信息，
			String ids = deploy_mode == 0 ? serverId_weight : server_ids;

			if (optionFlag != null && optionFlag.equals("create1")) {
				// 新增内容存储服务器组
				result = contentServerGroupManageDao.addContentServerGroup(
						bean, ids);
			} else if (optionFlag != null && optionFlag.equals("update1")) {
				
				// 修改内容存储服务器组
				result = contentServerGroupManageDao.updateContentServerGroup(
						bean, ids);
			}
			log.debug( "--addContentServerGroup-->add or update result:" + result);
			if (result) {
//				List<String> addServerList = map.get("addServerList");
				List<String> removeServerList = map.get("removeServerList");
				List<String> allServerList = map.get("allServerList");
				Set<Integer> set=new HashSet<Integer>();
				if (removeServerList != null) {
					for (int i = 0; i < removeServerList.size(); i++) {
						// 调用下发删除服务器和服务器组关联关系线程
					/*	ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentServerThread(Integer
								.parseInt(removeServerList.get(i)), 2)));*/
						set.add(Integer.parseInt(removeServerList.get(i)));
					}
					//log.debug( "--addContentServerGroup-->remove SendContentServerThread start over");
				}
				if (allServerList != null) {
					// 调用下发服务器和服务器组关联关系线程
					for (int i = 0; i < allServerList.size(); i++) {
						/*new Thread(new SendContentServerThread(Integer
								.parseInt(allServerList.get(i)), 1))
								.start();*/
						set.add(Integer.parseInt(allServerList.get(i)));
					}
					//log.debug( "--addContentServerGroup-->all SendContentServerThread start over");
				}
				IssueUtils.IssueContentServerGroupInfo(set);
				jsonResp.put("success", true);
				jsonResp.put("message", "配置内容存储服务器组成功!!");
				jsonResp.put("code", 20000);//TODO mock
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "配置内容存储服务器组失败!!");
			}
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置内容存储服务器组失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->新增服务器组失败:" + e.toString(), e);
		}
		log.info( "--addContentServerGroup(start)");
		this.outJsonString(jsonStr);
		return null;
	}

	/**
	 * 查询被内容存储服务器组关联的内容存储服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getRelContentServerTreeAction.action", method = RequestMethod.POST)
	public String getRelContentServerTree(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int group_id = modelJson.getInt("group_id");
		log.info( "--getRelContentServerTree(start)-->group_id:" + group_id);
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		try {
			List<TreeBean> attrList = contentServerGroupManageDao
					.getRelContentServerTree(Integer.toString(group_id));
			if (attrList != null && attrList.size() > 0) {
				for (int i = 0; i < attrList.size(); i++) {
					JSONObject jsonObj = new JSONObject();
					jsonObj.put("server_id", attrList.get(i).getId());
					jsonObj.put("server_name", attrList.get(i).getText_text());
					jsonObj.put("leaf", true);
					jsonArray.add(jsonObj);
				}
			}
			jsonResp.put("root", jsonArray);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonStr=jsonResp.toString();
		} catch (Exception e) {
			jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "展示已选择服务器树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->查询关联的服务器失败:" + e.toString(), e);

		}
		
		this.outJsonString(jsonStr);
		log.info( "--getRelContentServerTree(over)-->group_id:" + group_id);
		return null;
	}

	/**
	 * 查询未被内容存储服务器组关联的内容存储服务器树
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getUnRelContentServerTreeAction.action")
	public String getUnRelContentServerTree() {
		log.info( "--getUnRelContentServerTree(start)-->group_id:" + group_id);
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		try {
			List<TreeBean> attrList = contentServerGroupManageDao
					.getUnRelContentServerTree(group_id);
			if (attrList != null && attrList.size() > 0) {
				for (int i = 0; i < attrList.size(); i++) {
					JSONObject jsonObj = new JSONObject();
					jsonObj.put("server_id", attrList.get(i).getId());
					jsonObj.put("server_name", attrList.get(i).getText_text());
					jsonObj.put("leaf", true);
					jsonArray.add(jsonObj);
				}	
			}
			jsonResp.put("root", jsonArray);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonStr=jsonResp.toString();
		} catch (Exception e) {
			jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "展示未选择服务器树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->查询未关联的服务器失败:" + e.toString(), e);

		}
		this.outJsonString(jsonStr);
		log.info( "--getUnRelContentServerTree(over)-->group_id:" + group_id);
		return null;
	}

	@ResponseBody
	@RequestMapping(value = "/contentServerManage/startContentServerGroupAction.action", method = RequestMethod.POST)
	public String startContentServerGroup(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String group_ids = (String) modelJson.getOrDefault("group_id", "");
		log.info( "--startContentServerGroup(start)-->group_ids:" + group_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean result = contentServerGroupManageDao
					.startContentServerGroup(group_ids);
			if (result) {
				// 查询服务器组下的服务器信息，并下发
				List<ContentServerInfoBean> serverBeanList = contentServerGroupManageDao
						.getContentServerByGroupId(group_ids);
				if (serverBeanList != null && serverBeanList.size() > 0) {
					// 下发服务器组下所有的服务器信息，并下发
					/*for (ContentServerInfoBean bean : serverBeanList)
						ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentServerThread(bean
								.getServer_id(), 4)));*/
					IssueUtils.IssueInfoToDM(serverBeanList);
					log.debug( "--startContentServerGroup-->SendContentServerThread start over");
				}
				jsonResp.put("success", true);
				jsonResp.put("message", "启用服务器组成功!!");
				jsonResp.put("code", 20000);//TODO mock
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "启用服务器组失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "启用服务器组失败!!");
			jsonStr = jsonResp.toString();
			log.error( "内容存储服务器组管理->启用服务器组失败:" + e.toString(), e);
		}

		this.outJsonString(jsonStr);
		log.info( "--startContentServerGroup(over)-->group_ids:" + group_ids);
		return null;
	}

	@ResponseBody
	@RequestMapping(value = "/contentServerManage/stopContentServerGroupAction.action", method = RequestMethod.POST)
	public String stopContentServerGroup(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String group_ids = (String) modelJson.getOrDefault("group_id", "");
		log.info( "--stopContentServerGroup(start)-->group_ids:" + group_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean result = contentServerGroupManageDao
					.stopContentServerGroup(group_ids);
			if (result) {
				// 查询服务器组下的服务器信息，并下发
				List<ContentServerInfoBean> serverBeanList = contentServerGroupManageDao
						.getContentServerByGroupId(group_ids);
				if (serverBeanList != null && serverBeanList.size() > 0) {
					// 下发服务器组下所有的服务器信息，并下发
				/*	for (ContentServerInfoBean bean : serverBeanList)
						ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentServerThread(bean
								.getServer_id(), 5)));*/
					IssueUtils.IssueInfoToDM(serverBeanList);
					log.debug( "--stopContentServerGroup-->SendContentServerThread start over");
				}
				jsonResp.put("success", true);
				jsonResp.put("message", "禁用服务器组成功!!");
				jsonResp.put("code", 20000);//TODO mock
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "禁用服务器组失败!!");
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "禁用服务器组失败!!");
			jsonStr = jsonResp.toString();
			log.error( "内容存储服务器组管理->禁用服务器组失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--stopContentServerGroup(over)");
		return null;

	}

	/**
	 *校验IP地址和端口唯一性
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/checkGroupIPandPortAction.action", method = RequestMethod.POST)
	public String checkGroupIPandPort(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String group_id = (String) modelJson.getOrDefault("group_id", "");
		String group_ip = (String) modelJson.getOrDefault("group_ip", "");
		int http_port = modelJson.getInt("http_port");
		int socket_port = modelJson.getInt("socket_port");
		log.info( "--checkGroupIPandPort-->group_id:" + group_id + ";group_ip:" + group_ip);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			count = contentServerGroupManageDao.checkGroupIPandPort(Integer.valueOf(group_id),
					group_ip, http_port, socket_port);
			log.debug( "--checkGroupIPandPort-->count:" + count);
		} catch (Exception e) {
			log.error( "内容存储服务器组管理->校验IP地址和端口唯一性失败:" + e.toString(), e);
			count = -1;
		}
		if (count == 0) {
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();
		} else if (count > 0) {
			jsonResp.put("success", false);
			jsonResp.put("message","IP和端口已经存在!!");
			jsonResp.put("reason", "IP和端口已经存在!!");
			jsonStr = jsonResp.toString();
		} else {
			jsonResp.put("success", false);
			jsonResp.put("message","IP和端口检验失败!!");
			jsonResp.put("reason", "检验失败!!");
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--checkGroupIPandPort(over)");
		return null;
	}

	/**
	 * 获取内容存储服务器组已关联的内容对象树，包含卷id
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getRelContentObjectTreeAction.action", method = RequestMethod.POST)
	public String getRelContentObjectTree(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int group_id = modelJson.getInt("group_id");
		log.info( "--getRelContentObjectTree(start)-->group_id:" + group_id );
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		try {
			List<TreeBean> attrList = contentServerGroupManageDao
					.getRelContentObjectTree(group_id);
			if (attrList != null && attrList.size() > 0) {
				for (int i = 0; i < attrList.size(); i++) {
					JSONObject jsonObj = new JSONObject();
					jsonObj.put("id", attrList.get(i).getId());
					jsonObj.put("text", attrList.get(i).getText_text());
					jsonObj.put("volumnId", attrList.get(i).getVolumnId());
					jsonObj.put("leaf", true);
					jsonArray.add(jsonObj);
				}
			}
			jsonResp.put("root", jsonArray);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("result", "success");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
		    jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "展示已选择内容对象树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->查询关联的内容对象失败:" + e.toString(), e);

		}
		this.outJsonString(jsonStr);
		log.info( "--getRelContentObjectTree(over)-->group_id:" + group_id );
		return null;
	}

	/*
	 * 获取内容存储服务器组未关联的内容对象树
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getUnRelContentObjectTreeAction.action", method = RequestMethod.POST)
	public String getUnRelContentObjectTree(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int group_id = modelJson.getInt("group_id");
		log.info( "--getUnRelContentObjectTree(start)-->group_id:" + group_id );
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		try {
			List<TreeBean> list = contentServerGroupManageDao
					.getUnRelContentObjectTree(group_id);
			if (list != null && list.size() > 0) {
				TreeBean bean = null;
				for (Iterator<TreeBean> it = list.iterator(); it.hasNext();) {
					bean = it.next();
					log.debug( "--getUnRelContentObjectTree-->bean:" + bean.toString() );
					if (bean.getColumeDate() == null
							|| bean.getColumeDate().equals("")
							|| bean.getColumeDate().equals("null")
							|| bean.getColumeDate().equals("NULL")) {
						// 将内容模型中开始时间字段为null的过滤掉
						it.remove();
					}
				}
				for (int i = 0; i < list.size(); i++) {
					JSONObject jsonObj = new JSONObject();
					jsonObj.put("id", list.get(i).getId());
					jsonObj.put("text", list.get(i).getText_text());
					jsonObj.put("leaf", true);
					jsonArray.add(jsonObj);
				}
			}
			jsonResp.put("root", jsonArray);
			jsonResp.put("root", jsonArray);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("result", "success");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "展示未选择内容对象树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->查询未关联的内容对象失败:" + e.toString(), e);

		}
		this.outJsonString(jsonStr);
		log.info( "--getUnRelContentObjectTree(over)-->group_id:" + group_id );
		return null;
	}

	/**
	 * 获取服务器组关联的卷树结构
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getRelVolumnAction.action", method = RequestMethod.POST)
	public String getRelVolumnTree(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int group_id = modelJson.getInt("group_id");
		log.info( "--getRelVolumnTree(start)-->group_id:" + group_id );
		String jsonStr = "";
		JSONObject jsonResp=new JSONObject();
		JSONArray jsonArray=new JSONArray();
		Object obj;
		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getRelVolumnTree(group_id);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				size = objectList.size();
				Iterator iter = objectList.iterator();
				JSONObject jsonObj = null;
				Map map = null;
				Set keySet = null;
				Iterator keyIter = null;
				Object key = null;
				while(iter.hasNext()){
					obj = (Object) iter.next();
                    map = BeanUtils.describe(obj);
					if(log.isDebugEnabled()){
						log.debug( "==>>jsonStr："+map.toString());
					}
					keySet = map.keySet();
					keyIter = keySet.iterator();
					jsonObj = new JSONObject();
					while(keyIter.hasNext()){
						key = keyIter.next();
						jsonObj.put(key, map.get(key));
					}
					jsonArray.add(jsonObj);
				}
			}
			jsonResp.put("root",jsonArray);
			jsonResp.put("totalProperty",size+"");//总行数
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
		    jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "展示已关联的卷树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->查询关联的卷失败:" + e.toString(), e);

		}
		this.outJsonString(jsonStr);
		log.info( "--getRelVolumnTree(over)-->group_id:" + group_id );
		return null;
	}

	/**
	 * 增加内容存储服务器组和内容对象关联关系
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/addRelGroupAndContentObjectAction.action", method = RequestMethod.POST)
	public String addRelGroupAndContentObject(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int group_id = modelJson.getInt("group_id");
		String modelCode_volumeId = (String) modelJson.getOrDefault("modelCode_volumeId", "");
		log.info( "--addRelGroupAndContentObject(start)-->group_id:" + group_id );
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try {
			// 获取服务器组新增和删除的内容模型关联关系列表组成的map
			// m为查询出的所有内容模型和组ID为条件查询出的关联卷信息:包括已删除的和需要添加的
			Map<String, List<GroupCmodelRelInfoBean>> m = contentServerGroupManageDao
					.getDiffContentObjAndVolume(modelCode_volumeId, group_id);
			List<GroupCmodelRelInfoBean> addContentObjList = m
					.get("addContentObjList");
			List<GroupCmodelRelInfoBean> removeContentObjList = m
					.get("removeContentObjList");

			// 删除内容模型和服务器组关联关系
			removeContentObjList = contentServerGroupManageDao
					.removeRelGroupAndContentObject(removeContentObjList);
			if (removeContentObjList != null) {
				log.debug("--addRelGroupAndContentObject-->removeContentObjList:" + removeContentObjList);
			}

			// 增加内容模型和服务器组关联关系
			addContentObjList = contentServerGroupManageDao
					.addRelGroupAndContentObject(addContentObjList);
			if (addContentObjList != null) {
				log.debug("--addRelGroupAndContentObject-->addContentObjList:" + addContentObjList);
			}

			if (removeContentObjList != null || addContentObjList != null) {
				
				Set<String> groupIdSet=new HashSet<String>();
				if (removeContentObjList != null
						&& removeContentObjList.size() > 0) {
					// 启用下发删除内容模型和服务器组关联关系线程
					for (int i = 0; i < removeContentObjList.size(); i++) {
						// 若该内容模型有替换操作则不用下发删除操作
						boolean flag = true; // 是否执行删除的下发标识
						if (addContentObjList != null && addContentObjList.size() > 0) {
							// 在addContentObjList中匹配模型英文名,若存在则不下发删除
							for (GroupCmodelRelInfoBean addBean : addContentObjList) {
								log.debug( "--addRelGroupAndContentObject-->getMcode:" + addBean.getModel_code() + ";list getMCode" + removeContentObjList.get(i).getModel_code());
								if(addBean.getModel_code().equals(removeContentObjList.get(i).getModel_code())){
									flag = false;
								}
							}
						}
						log.debug( "--addRelGroupAndContentObject-->flag:" + flag);
						if(flag){
							groupIdSet.add(removeContentObjList.get(i).getGroup_id()+"");
							//contentServerList.addAll(contentServerGroupManageDao.getRelContentServerList(removeContentObjList.get(i).getGroup_id()+"",true));
							/*ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentServerGroupThread(
									removeContentObjList.get(i), 2)));*/
							log.debug( "--addRelGroupAndContentObject-->SendContentServerGroupThread start");
						}
					}
				}
				List<String> addedModeCodesList = new ArrayList<String>(); // 记录增加的模型名
				if (addContentObjList != null && addContentObjList.size() > 0) {
					// 启用下发增加内容模型和服务器组关联关系线程
					for (int i = 0; i < addContentObjList.size(); i++) {
						GroupCmodelRelInfoBean bean = addContentObjList.get(i);
						addedModeCodesList.add(bean.getModel_code());
						//ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentServerGroupThread(bean, 1)));
						//contentServerList.addAll(contentServerGroupManageDao.getRelContentServerList(bean.getGroup_id()+"",true));
						groupIdSet.add(bean.getGroup_id()+"");
					}
					log.debug( "--addRelGroupAndContentObject-->SendContentServerGroupThread start");
				}
				// 启用下发分表线程（只在服务器组新增了模型时才需要下发信息）
//				if (modelCodes != null && !modelCodes.equals("")) { 此处的modelCodes从页面获取，始终都有值，失去判断作用 [弃用]
				List<ContentServerInfoBean> contentServerList=new ArrayList<ContentServerInfoBean>();
				if (addedModeCodesList.size() > 0) {
//					List<String> list = contentServerGroupManageDao  该部分代码无法正确分离出新增加的模型名，[弃用]，改为直接从addContentObjList中获取
//							.getDiffContentObj(modelCodes, group_id);
//					if (list != null && list.size() > 0) {
					Set<String> modelSet=new HashSet<String>();
					modelSet.addAll(addedModeCodesList);
					for (String id : modelSet) {
					/*	// 启用下发分表线程
						ConsoleThreadPool.getThreadPool().submit(new Thread(new SendModelTableThread(id, "3")));
						// 启用下发内容模型模板线程
					    ConsoleThreadPool.getThreadPool().submit(new Thread(new SendContentModelThread(id, "3")));*/
						contentServerList.addAll(IssueUtils.getAliveByModelCodeDM(id));
					
					}
					log.debug( "--addRelGroupAndContentObject-->SendModelTableThread and SendContentModelThread start");
//					}
				}
				for(String groupid:groupIdSet){
					contentServerList.addAll(contentServerGroupManageDao.getRelContentServerList(groupid,true));
				}
				IssueUtils.IssueInfoToDM(contentServerList);
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
				jsonResp.put("success", true);
				jsonResp.put("message", "配置内容存储服务器组和内容对象关联关系成功!!");
			} else {
				jsonResp.put("success", true);
				jsonResp.put("message", "配置内容存储服务器组和内容对象关联关系成功!!");
			}
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置内容存储服务器组和内容对象关联关系失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->增加服务器组和内容对象关系失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--addRelGroupAndContentObject(over)-->group_id:" + group_id );
		return null;
	}

	/**
	 * 获取内容存储服务器组id和名称
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getContentServerGroupAction.action")
	public String getContentServerGroup() {
		log.info( "--getContentServerGroup(start)");
		String jsonStr = null;

		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getContentServerGroup();
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getContentServerGroup-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器组名称列表失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->获取内容存储服务器组名称列表失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--getContentServerGroup(over)");
		return null;
	}
	/**
	 * 获取内容存储服务器组id和名称
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getSuperContentServerGroupAction.action", method = RequestMethod.POST)
	public String getSuperContentServerGroup(int group_id) {
		log.info( "--getSuperContentServerGroup(start)-->group_id:" + group_id);
		String jsonStr = null;

		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getContentServerGroup(group_id);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getSuperContentServerGroup-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器组名称列表失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->获取内容存储服务器组名称列表失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--getSuperContentServerGroup(over)");
		return null;
	}

	/**
	 * 获取内容存储服务器组关联的内容对象id和名称
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getRelContentObjectAction.action", method = RequestMethod.POST)
	public String getRelContentObject(int group_id) {
		log.info( "--getRelContentObject(start)-->group_id:" + group_id);
		String jsonStr = null;

		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getRelContentObjectTree(group_id);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getRelContentObject-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器关联内容对象失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->获取内容存储服务器关联内容对象失败:" + e.toString(), e);

		}
		
		this.outJsonString(jsonStr);
		log.debug( "--getRelContentObject(over)");
		return null;
	}
	
	/**
	 * 获取内容存储服务器组关联的内容对象id和名称
	 * 没有关联内容存储服务器组时显示全部内容对象
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getUnRelOrRelContentObjectAction.action")
	public String getUnRelOrRelContentObject(String group_id) {
		log.info( "--getUnRelOrRelContentObject(start)-->group_id:" + group_id);
		String jsonStr = null;
		try {
			List<TreeBean> objectList = null;
			group_id=("").equals(group_id)||null==group_id?"0":group_id;
			if(group_id.equals("0")){
				objectList = contentServerGroupManageDao
						.getAllContentObjectTree();
			}else{
				objectList = contentServerGroupManageDao
					.getRelContentObjectTree(Integer.valueOf(group_id));
			}
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getUnRelOrRelContentObject-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器内容对象失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->获取内容存储服务器内容对象失败:" + e.toString(), e);

		}
		
		this.outJsonString(jsonStr);
		log.debug( "--getUnRelOrRelContentObject(over)");
		return null;
	}
	
	/**
	 * 查询被内容存储服务器组关联的内容存储服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getRelContentServerAction.action", method = RequestMethod.POST)
	public String getRelContentServer(String group_id) {
		log.info( "--getRelContentServer(start)-->group_id:" + group_id);
		String jsonStr = "";

		try {
			List<TreeBean> serverList = contentServerGroupManageDao
					.getRelContentServerTree(group_id);
			int size = 0;
			if (serverList != null && serverList.size() > 0) {
				log.debug( "--getRelContentObject-->objectList:" + serverList );
				size = serverList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(serverList, size,
				new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "展示已选择服务器树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->查询关联的服务器失败:" + e.toString(), e);

		}
		
		this.outJsonString(jsonStr);
		log.info( "--getRelContentServer(over)-->group_id:" + group_id);
		return null;
	}
	
	/**
	 * 检测该服务器组名称是否存在
	 * @return 
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/checkGroupNameAction.action")
	public String checkGroupName(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int group_id = modelJson.getInt("group_id");
		String group_name = (String) modelJson.getOrDefault("group_name", "");
		try {
			group_name = URLDecoder.decode(group_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode UnityAccessServer fields error, group_name=" + group_name, e1);
		}
		log.info( "--checkGroupName-->group_id:" + group_id + ";group_name:" + group_name);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
		 	count = contentServerGroupManageDao.checkGroupName(group_id,
					group_name);
		 	log.debug( "--checkGroupName-->count:"+count);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "内容存储服务器组管理->校验服务器组名称唯一性失败!" + e.toString());
		}
		if (count == 0) {
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();
		} else if (count > 0) {
			jsonResp.put("success", false);
			jsonResp.put("message","服务器组名称已经存在!!");
			jsonResp.put("reason", "服务器组名称已经存在!!");
			jsonStr = jsonResp.toString();
		} else {
			jsonResp.put("success", true);
			jsonResp.put("message","服务器组名称检验失败!!");
			jsonResp.put("reason", "检验失败!!");
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--checkGroupName(over)");
		return null;
	}
	
	/**
	 * 最大用户数量校验
	 * 判断是否允许添加内容存储服务器组 
	 * @return true 允许添加 false 拒绝添加
	 */
	private boolean checkMaxUser(){
		log.info( "--checkMaxUser(start)");
		boolean flag = false;
		String maxuser = LicenseFileConstant.getMaxUsers();
		int contentServerGroupNum = contentServerGroupManageDao
		.getContentServerGroupList(0,"").size(); //查询系统服务器组个数
		log.debug( "--checkMaxUser-->contentServerGroupNum:" + contentServerGroupNum + ";maxuser:" + maxuser);
		if(maxuser.equals("0")){
			flag = true;
		}
		if(contentServerGroupNum < Integer.parseInt(maxuser)){
			flag = true;
		}
		log.info( "--checkMaxUser(over)-->falg:"+flag);
		return flag;
	} 
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getGroupNameAction.action")
	public String getGroupNameList() {
		log.info( "--getObjectName(start)-->:");
		String jsonStr = null;

		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getGroupNameList();
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容对象名称列表");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容模型管理->获取内容对象名称列表失败!" + e.toString());
		}

		this.outJsonString(jsonStr);
		return null;
	}
	
	/**
	 * 获取内容存储服务器组关联的内容对象id和名称
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getNearLineVolumeAction.action", method = RequestMethod.POST)
	public String getNearLineVolume(int group_id, String modelCodes) {		log.info( "group_id:" + group_id+",modelCode:"+modelCodes+"]");
		String jsonStr = null;

		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getNearLineVolumeTree(group_id,modelCodes);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getRelContentObject-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器关联卷");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("group_id:" + group_id+",modelCode:"+modelCodes+"]", e);
		}
		
		this.outJsonString(jsonStr);
		log.debug( "--getNearLineVolume(over)");
		return null;
	}
	
	/**
	 * 获取内容存储服务器组id和名称
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getContentServerGroupVueAction.action")
	public String getContentServerGroupVue() {
		log.info( "--getContentServerGroup(start)");
		String jsonStr = null;

		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getContentServerGroup();
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getContentServerGroup-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器组名称列表失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->获取内容存储服务器组名称列表失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--getContentServerGroup(over)");
		return null;
	}
	
	/**
	 * 获取内容存储服务器组关联的内容对象id和名称
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getRelContentObjectVueAction.action", method = RequestMethod.POST)
	public String getRelContentObjectVue(int group_id) {
		log.info( "--getRelContentObject(start)-->group_id:" + group_id);
		String jsonStr = null;

		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getRelContentObjectTree(group_id);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getRelContentObject-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器关联内容对象失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->获取内容存储服务器关联内容对象失败:" + e.toString(), e);

		}
		
		this.outJsonString(jsonStr);
		log.debug( "--getRelContentObject(over)");
		return null;
	}
	
	/**
	 * 获取内容存储服务器组id和名称
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getSuperContentServerGroupVueAction.action", method = RequestMethod.POST)
	public String getSuperContentServerGroupVue(int group_id) {
		log.info( "--getSuperContentServerGroup(start)-->group_id:" + group_id);
		String jsonStr = null;
		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getContentServerGroup(group_id);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getSuperContentServerGroup-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器组名称列表失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "内容存储服务器组管理->获取内容存储服务器组名称列表失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getSuperContentServerGroup(over)");
		return null;
	}
	
	@ResponseBody
	@RequestMapping(value = "/contentServerManage/getNearLineVolumeVueAction.action", method = RequestMethod.POST)
	public String getNearLineVolumeVue(int group_id, String modelCodes) {		log.info( "group_id:" + group_id+",modelCode:"+modelCodes+"]");
		String jsonStr = null;
		try {
			List<TreeBean> objectList = contentServerGroupManageDao
					.getNearLineVolumeTree(group_id,modelCodes);
			int size = 0;
			if (objectList != null && objectList.size() > 0) {
				log.debug( "--getRelContentObject-->objectList:" + objectList );
				size = objectList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(objectList, size,
					new TreeBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取内容存储服务器关联卷");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("group_id:" + group_id+",modelCode:"+modelCodes+"]", e);
		}
		this.outJsonString(jsonStr);
		log.debug( "--getNearLineVolume(over)");
		return null;
	}
}
