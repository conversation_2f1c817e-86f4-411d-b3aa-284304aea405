2025-07-24 00:04:48.476 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 00:09:48.479 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:19:57.578 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:24:57.588 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:29:57.602 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:34:57.611 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:39:57.621 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:44:57.635 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:49:57.645 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:54:57.654 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 08:59:57.668 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:04:57.682 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:09:57.687 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:14:57.699 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:19:57.713 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:24:57.728 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:29:57.735 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:34:57.737 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:39:57.748 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:44:57.758 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:49:57.763 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:54:57.764 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 09:59:57.777 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:04:57.789 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:09:57.791 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:14:57.793 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:19:57.800 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:24:57.813 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:29:57.822 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:34:57.827 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:39:57.835 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:44:57.845 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:49:57.858 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:54:57.860 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 10:59:57.861 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:04:57.863 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:09:57.864 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:14:57.865 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:19:57.866 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:24:57.868 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:29:57.869 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:34:57.870 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:39:57.873 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:44:57.875 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:49:57.882 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:54:57.886 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 11:59:57.901 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:04:57.909 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:09:57.920 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:14:57.935 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:19:57.940 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:24:57.951 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:29:57.954 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:34:57.959 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:39:57.972 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:44:57.979 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:49:57.993 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:54:58.001 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 12:59:58.009 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:04:58.023 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:09:58.034 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:14:58.039 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:19:58.039 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:24:58.055 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:29:58.057 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:34:58.060 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:39:58.070 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:44:58.071 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:49:58.083 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:54:58.088 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 13:59:58.095 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:04:58.103 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:09:58.111 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:14:58.121 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:19:58.122 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:24:58.124 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:29:58.136 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:34:58.151 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:39:58.159 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:44:58.164 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:49:58.167 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:54:58.167 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 14:59:58.181 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:04:58.197 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:09:58.198 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:14:58.206 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:19:58.245 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:24:58.251 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:29:58.263 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:34:58.271 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:39:58.272 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:43:29.489 [] [3a55e5e9c89f1159/366546fb3a0ab8e1] [http-nio-9902-exec-68] DEBUG c.s.a.s.d.b.T.selectBySelective - ==>  Preparing: select TELLER_NO, TELLER_NAME, TELLER_LEVEL, TELLER_TYPE, PARENT_ORGAN, TELLER_STATE from SM_TELLER_TB WHERE TELLER_NO = ?
2025-07-24 15:43:29.491 [] [3a55e5e9c89f1159/366546fb3a0ab8e1] [http-nio-9902-exec-68] DEBUG c.s.a.s.d.b.T.selectBySelective - ==> Parameters: admin(String)
2025-07-24 15:43:29.498 [] [3a55e5e9c89f1159/366546fb3a0ab8e1] [http-nio-9902-exec-68] DEBUG c.s.a.s.d.b.T.selectBySelective - <==      Total: 1
2025-07-24 15:43:39.917 [] [3ad0d769763522cd/9b481d4d2615d62c] [http-nio-9902-exec-66] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 查询指定指定级别机构!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:39 操作结束时间: 2025-07-24 15:43:39!总共花费时间: 127 毫秒！
2025-07-24 15:43:40.289 [] [097834f31b9ef843/61bcf6511b16958b] [http-nio-9902-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 用户权限机构!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:40 操作结束时间: 2025-07-24 15:43:40!总共花费时间: 223 毫秒！
2025-07-24 15:43:40.468 [] [1adf0a9b081060b0/b79352d4e411d8c8] [http-nio-9902-exec-70] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 用户权限机构!请求IP地址: ************** 操作开始时间: 2025-07-24 15:43:40 操作结束时间: 2025-07-24 15:43:40!总共花费时间: 144 毫秒！
2025-07-24 15:44:58.286 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:49:58.294 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:54:58.301 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 15:59:58.303 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:04:58.307 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:09:58.315 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:12:32.793 [] [379d20702817872b/f59fc2e09ef2fdaa] [http-nio-9902-exec-72] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 16:12:32.794 [] [379d20702817872b/f59fc2e09ef2fdaa] [http-nio-9902-exec-72] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 16:12:32.797 [] [379d20702817872b/f59fc2e09ef2fdaa] [http-nio-9902-exec-72] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 16:13:05.754 [] [ee0fb50ed0f175b0/9e00f548ddf951bd] [http-nio-9902-exec-74] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 16:13:05.756 [] [ee0fb50ed0f175b0/9e00f548ddf951bd] [http-nio-9902-exec-74] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 16:13:05.760 [] [ee0fb50ed0f175b0/9e00f548ddf951bd] [http-nio-9902-exec-74] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 16:13:06.328 [] [66ed12056f559406/1fd00f421bd1056f] [http-nio-9902-exec-79] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:13:06.329 [] [66ed12056f559406/1fd00f421bd1056f] [http-nio-9902-exec-79] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:13:06.333 [] [66ed12056f559406/1fd00f421bd1056f] [http-nio-9902-exec-79] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:13:06.348 [] [66ed12056f559406/70a8bc2cd76ba441] [http-nio-9902-exec-76] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:13:06.350 [] [66ed12056f559406/70a8bc2cd76ba441] [http-nio-9902-exec-76] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:13:06.356 [] [66ed12056f559406/70a8bc2cd76ba441] [http-nio-9902-exec-76] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.113 [] [731cf6b6674f1e71/dc1fb34ab94ce4e9] [http-nio-9902-exec-78] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:07.114 [] [731cf6b6674f1e71/dc1fb34ab94ce4e9] [http-nio-9902-exec-78] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:07.117 [] [731cf6b6674f1e71/dc1fb34ab94ce4e9] [http-nio-9902-exec-78] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.124 [] [731cf6b6674f1e71/6f6abd5dd3f88c39] [http-nio-9902-exec-82] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:07.125 [] [731cf6b6674f1e71/6f6abd5dd3f88c39] [http-nio-9902-exec-82] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:07.127 [] [731cf6b6674f1e71/6f6abd5dd3f88c39] [http-nio-9902-exec-82] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.918 [] [63e36fe03f45f4f7/d305ee19443e15bc] [http-nio-9902-exec-80] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:07.920 [] [63e36fe03f45f4f7/d305ee19443e15bc] [http-nio-9902-exec-80] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:07.922 [] [63e36fe03f45f4f7/d305ee19443e15bc] [http-nio-9902-exec-80] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:07.929 [] [63e36fe03f45f4f7/476844ccbae1a6c6] [http-nio-9902-exec-84] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:07.929 [] [63e36fe03f45f4f7/476844ccbae1a6c6] [http-nio-9902-exec-84] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:07.932 [] [63e36fe03f45f4f7/476844ccbae1a6c6] [http-nio-9902-exec-84] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:08.559 [] [87acf394bf78ba88/1bf14cfced9d9d12] [http-nio-9902-exec-75] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:08.560 [] [87acf394bf78ba88/1bf14cfced9d9d12] [http-nio-9902-exec-75] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:08.564 [] [87acf394bf78ba88/1bf14cfced9d9d12] [http-nio-9902-exec-75] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:08.571 [] [87acf394bf78ba88/ac988d1c7b58baba] [http-nio-9902-exec-83] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:08.571 [] [87acf394bf78ba88/ac988d1c7b58baba] [http-nio-9902-exec-83] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:08.574 [] [87acf394bf78ba88/ac988d1c7b58baba] [http-nio-9902-exec-83] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:09.190 [] [19c71d8414b9aa3f/6b3d5ff40164fc64] [http-nio-9902-exec-81] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:09.190 [] [19c71d8414b9aa3f/6b3d5ff40164fc64] [http-nio-9902-exec-81] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:09.193 [] [19c71d8414b9aa3f/6b3d5ff40164fc64] [http-nio-9902-exec-81] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:09.199 [] [19c71d8414b9aa3f/7d3f033517f76843] [http-nio-9902-exec-85] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:09.200 [] [19c71d8414b9aa3f/7d3f033517f76843] [http-nio-9902-exec-85] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:09.203 [] [19c71d8414b9aa3f/7d3f033517f76843] [http-nio-9902-exec-85] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:09.762 [] [63847ee46908df67/df8976425cc13283] [http-nio-9902-exec-86] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:09.763 [] [63847ee46908df67/df8976425cc13283] [http-nio-9902-exec-86] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:09.765 [] [63847ee46908df67/df8976425cc13283] [http-nio-9902-exec-86] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:09.770 [] [63847ee46908df67/50af257a140e75a5] [http-nio-9902-exec-87] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:09.770 [] [63847ee46908df67/50af257a140e75a5] [http-nio-9902-exec-87] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:09.772 [] [63847ee46908df67/50af257a140e75a5] [http-nio-9902-exec-87] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:10.380 [] [7c2c53fcae689ecd/5cf53d38ed4ed10c] [http-nio-9902-exec-88] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:10.380 [] [7c2c53fcae689ecd/5cf53d38ed4ed10c] [http-nio-9902-exec-88] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:10.384 [] [7c2c53fcae689ecd/5cf53d38ed4ed10c] [http-nio-9902-exec-88] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:10.389 [] [7c2c53fcae689ecd/820bf75f9375a9c8] [http-nio-9902-exec-89] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:10.390 [] [7c2c53fcae689ecd/820bf75f9375a9c8] [http-nio-9902-exec-89] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:10.392 [] [7c2c53fcae689ecd/820bf75f9375a9c8] [http-nio-9902-exec-89] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:11.027 [] [8e86f1afffd6cd37/ecdcde9f57663e23] [http-nio-9902-exec-90] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:11.027 [] [8e86f1afffd6cd37/ecdcde9f57663e23] [http-nio-9902-exec-90] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:11.030 [] [8e86f1afffd6cd37/ecdcde9f57663e23] [http-nio-9902-exec-90] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:11.036 [] [8e86f1afffd6cd37/4475dc0e52467568] [http-nio-9902-exec-91] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:11.036 [] [8e86f1afffd6cd37/4475dc0e52467568] [http-nio-9902-exec-91] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:11.038 [] [8e86f1afffd6cd37/4475dc0e52467568] [http-nio-9902-exec-91] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:11.703 [] [da57141b334dfb33/8635b4ca341a2509] [http-nio-9902-exec-92] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:11.704 [] [da57141b334dfb33/8635b4ca341a2509] [http-nio-9902-exec-92] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:11.706 [] [da57141b334dfb33/8635b4ca341a2509] [http-nio-9902-exec-92] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:11.711 [] [da57141b334dfb33/a485b75013e73bce] [http-nio-9902-exec-93] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:11.712 [] [da57141b334dfb33/a485b75013e73bce] [http-nio-9902-exec-93] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:11.714 [] [da57141b334dfb33/a485b75013e73bce] [http-nio-9902-exec-93] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:12.548 [] [6f54b58ab40db412/e60984974453aae3] [http-nio-9902-exec-94] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:12.549 [] [6f54b58ab40db412/e60984974453aae3] [http-nio-9902-exec-94] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:12.551 [] [6f54b58ab40db412/e60984974453aae3] [http-nio-9902-exec-94] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:12.559 [] [6f54b58ab40db412/d25e384f4f38782e] [http-nio-9902-exec-95] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:12.560 [] [6f54b58ab40db412/d25e384f4f38782e] [http-nio-9902-exec-95] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:12.562 [] [6f54b58ab40db412/d25e384f4f38782e] [http-nio-9902-exec-95] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:13.453 [] [8733e613d0fdd3d3/d0b20726b2d86412] [http-nio-9902-exec-96] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:13.454 [] [8733e613d0fdd3d3/d0b20726b2d86412] [http-nio-9902-exec-96] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:13.457 [] [8733e613d0fdd3d3/d0b20726b2d86412] [http-nio-9902-exec-96] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:13.461 [] [8733e613d0fdd3d3/523929c0044e44bb] [http-nio-9902-exec-97] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:13.461 [] [8733e613d0fdd3d3/523929c0044e44bb] [http-nio-9902-exec-97] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:13.464 [] [8733e613d0fdd3d3/523929c0044e44bb] [http-nio-9902-exec-97] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:14.227 [] [b66c5f2da5f14e3b/b28260558448ea0a] [http-nio-9902-exec-98] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:14.228 [] [b66c5f2da5f14e3b/b28260558448ea0a] [http-nio-9902-exec-98] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:14.231 [] [b66c5f2da5f14e3b/b28260558448ea0a] [http-nio-9902-exec-98] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:14.235 [] [b66c5f2da5f14e3b/b32be84e49fbd79f] [http-nio-9902-exec-99] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:14.236 [] [b66c5f2da5f14e3b/b32be84e49fbd79f] [http-nio-9902-exec-99] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:14.238 [] [b66c5f2da5f14e3b/b32be84e49fbd79f] [http-nio-9902-exec-99] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:14.923 [] [9a5d6aeae2a84607/f2ec346cf99e9b4f] [http-nio-9902-exec-100] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:14.924 [] [9a5d6aeae2a84607/f2ec346cf99e9b4f] [http-nio-9902-exec-100] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:14.926 [] [9a5d6aeae2a84607/f2ec346cf99e9b4f] [http-nio-9902-exec-100] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:14.931 [] [9a5d6aeae2a84607/78d1c84bb95fddda] [http-nio-9902-exec-2] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:14.931 [] [9a5d6aeae2a84607/78d1c84bb95fddda] [http-nio-9902-exec-2] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:14.935 [] [9a5d6aeae2a84607/78d1c84bb95fddda] [http-nio-9902-exec-2] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:15.875 [] [5876175cc56f57a3/38816f904a01914f] [http-nio-9902-exec-1] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:15.875 [] [5876175cc56f57a3/38816f904a01914f] [http-nio-9902-exec-1] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:15.877 [] [5876175cc56f57a3/38816f904a01914f] [http-nio-9902-exec-1] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:15.881 [] [5876175cc56f57a3/4dbfea3a67d4be8b] [http-nio-9902-exec-3] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:15.881 [] [5876175cc56f57a3/4dbfea3a67d4be8b] [http-nio-9902-exec-3] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:15.884 [] [5876175cc56f57a3/4dbfea3a67d4be8b] [http-nio-9902-exec-3] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:17.089 [] [4a46b258ca270cda/fa49625a069bb81c] [http-nio-9902-exec-4] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:14:17.089 [] [4a46b258ca270cda/fa49625a069bb81c] [http-nio-9902-exec-4] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:14:17.092 [] [4a46b258ca270cda/fa49625a069bb81c] [http-nio-9902-exec-4] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:17.102 [] [4a46b258ca270cda/d4ea8e0dc39e63ae] [http-nio-9902-exec-5] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:14:17.102 [] [4a46b258ca270cda/d4ea8e0dc39e63ae] [http-nio-9902-exec-5] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:14:17.104 [] [4a46b258ca270cda/d4ea8e0dc39e63ae] [http-nio-9902-exec-5] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:14:58.328 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:19:58.335 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:24:58.349 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:29:58.362 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:32:06.560 [] [d922716cefe4a492/1920befcf3015c88] [http-nio-9902-exec-9] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 16:32:06.561 [] [d922716cefe4a492/1920befcf3015c88] [http-nio-9902-exec-9] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 16:32:06.564 [] [d922716cefe4a492/1920befcf3015c88] [http-nio-9902-exec-9] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 16:32:06.906 [] [710f353ebfc9a454/72c97fbac7e730ff] [http-nio-9902-exec-10] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:32:06.907 [] [710f353ebfc9a454/72c97fbac7e730ff] [http-nio-9902-exec-10] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:32:06.911 [] [710f353ebfc9a454/72c97fbac7e730ff] [http-nio-9902-exec-10] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:32:06.918 [] [710f353ebfc9a454/6bb8a1bca9947ad2] [http-nio-9902-exec-11] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:32:06.918 [] [710f353ebfc9a454/6bb8a1bca9947ad2] [http-nio-9902-exec-11] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:32:06.920 [] [710f353ebfc9a454/6bb8a1bca9947ad2] [http-nio-9902-exec-11] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:34:58.368 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:39:58.372 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:40:34.277 [] [43489b793f4ac8d2/0cd17bf8abbadfd4] [http-nio-9902-exec-15] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 16:40:34.277 [] [43489b793f4ac8d2/0cd17bf8abbadfd4] [http-nio-9902-exec-15] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 16:40:34.280 [] [43489b793f4ac8d2/0cd17bf8abbadfd4] [http-nio-9902-exec-15] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 16:40:34.680 [] [36da0f8fefdc62ff/3b38672861bbb3e8] [http-nio-9902-exec-14] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:40:34.681 [] [36da0f8fefdc62ff/3b38672861bbb3e8] [http-nio-9902-exec-14] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:40:34.684 [] [36da0f8fefdc62ff/3b38672861bbb3e8] [http-nio-9902-exec-14] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:40:34.689 [] [36da0f8fefdc62ff/1fad35f8a6b2b179] [http-nio-9902-exec-13] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:40:34.689 [] [36da0f8fefdc62ff/1fad35f8a6b2b179] [http-nio-9902-exec-13] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:40:34.692 [] [36da0f8fefdc62ff/1fad35f8a6b2b179] [http-nio-9902-exec-13] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:44:58.375 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:49:58.375 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:54:58.389 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 16:55:16.465 [] [3d7705e847730900/6d1fa242e301ea88] [http-nio-9902-exec-17] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 16:55:16.466 [] [3d7705e847730900/6d1fa242e301ea88] [http-nio-9902-exec-17] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 16:55:16.470 [] [3d7705e847730900/6d1fa242e301ea88] [http-nio-9902-exec-17] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 16:55:16.783 [] [c85d501c35d423b4/22e937b2f9e985f5] [http-nio-9902-exec-18] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 16:55:16.784 [] [c85d501c35d423b4/22e937b2f9e985f5] [http-nio-9902-exec-18] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 16:55:16.787 [] [c85d501c35d423b4/22e937b2f9e985f5] [http-nio-9902-exec-18] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:55:16.792 [] [c85d501c35d423b4/49f70f911eb79c0f] [http-nio-9902-exec-19] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 16:55:16.793 [] [c85d501c35d423b4/49f70f911eb79c0f] [http-nio-9902-exec-19] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 16:55:16.796 [] [c85d501c35d423b4/49f70f911eb79c0f] [http-nio-9902-exec-19] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 16:59:58.400 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:04:58.414 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:09:58.425 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:14:58.429 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:19:58.430 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:24:58.432 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:29:58.446 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:34:58.452 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:39:58.467 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:44:58.473 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:49:58.480 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:54:58.492 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 17:59:58.506 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:04:58.521 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:09:58.522 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:14:12.206 [] [cba1d956341a49b7/4502a25839fe7dbd] [http-nio-9902-exec-25] DEBUG c.s.a.s.d.b.T.selectBySelective - ==>  Preparing: select TELLER_NO, TELLER_NAME, TELLER_LEVEL, TELLER_TYPE, PARENT_ORGAN, TELLER_STATE from SM_TELLER_TB WHERE TELLER_NO = ?
2025-07-24 18:14:12.207 [] [cba1d956341a49b7/4502a25839fe7dbd] [http-nio-9902-exec-25] DEBUG c.s.a.s.d.b.T.selectBySelective - ==> Parameters: admin(String)
2025-07-24 18:14:12.214 [] [cba1d956341a49b7/4502a25839fe7dbd] [http-nio-9902-exec-25] DEBUG c.s.a.s.d.b.T.selectBySelective - <==      Total: 1
2025-07-24 18:14:16.594 [] [4d7397b40df9ccc9/2b639864bf4f5997] [http-nio-9902-exec-27] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 查询指定指定级别机构!请求IP地址: ************** 操作开始时间: 2025-07-24 18:14:16 操作结束时间: 2025-07-24 18:14:16!总共花费时间: 125 毫秒！
2025-07-24 18:14:16.905 [] [dddcadb6eeed90fa/ea00e79bc12b1186] [http-nio-9902-exec-26] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 用户权限机构!请求IP地址: ************** 操作开始时间: 2025-07-24 18:14:16 操作结束时间: 2025-07-24 18:14:16!总共花费时间: 208 毫秒！
2025-07-24 18:14:17.108 [] [4f42a8d095f7d75a/a8c209e1c847e1d2] [http-nio-9902-exec-24] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 用户权限机构!请求IP地址: ************** 操作开始时间: 2025-07-24 18:14:16 操作结束时间: 2025-07-24 18:14:17!总共花费时间: 172 毫秒！
2025-07-24 18:14:58.535 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:19:58.544 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:25:00.653 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:30:00.663 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:35:00.679 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:40:00.686 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:45:00.693 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:50:00.702 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 18:55:00.713 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:00:00.714 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:05:00.726 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:10:00.738 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:15:00.741 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:20:00.746 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:25:00.758 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:30:00.767 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:35:00.771 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:40:00.783 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:45:00.796 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:50:00.802 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 19:55:00.810 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:00:00.823 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:05:00.826 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:10:00.838 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:13:59.843 [] [94e412399e428fb4/2962cc9ddfe75f6e] [http-nio-9902-exec-22] DEBUG c.s.a.s.d.b.T.selectBySelective - ==>  Preparing: select TELLER_NO, TELLER_NAME, TELLER_LEVEL, TELLER_TYPE, PARENT_ORGAN, TELLER_STATE from SM_TELLER_TB WHERE TELLER_NO = ?
2025-07-24 20:13:59.845 [] [94e412399e428fb4/2962cc9ddfe75f6e] [http-nio-9902-exec-22] DEBUG c.s.a.s.d.b.T.selectBySelective - ==> Parameters: admin(String)
2025-07-24 20:13:59.851 [] [94e412399e428fb4/2962cc9ddfe75f6e] [http-nio-9902-exec-22] DEBUG c.s.a.s.d.b.T.selectBySelective - <==      Total: 1
2025-07-24 20:14:04.449 [] [be53fe3c9fa86a67/fecccf8585505a15] [http-nio-9902-exec-29] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 查询指定指定级别机构!请求IP地址: ************** 操作开始时间: 2025-07-24 20:14:04 操作结束时间: 2025-07-24 20:14:04!总共花费时间: 129 毫秒！
2025-07-24 20:14:04.803 [] [19dbe67dacb7de3f/c46182615a116fcc] [http-nio-9902-exec-30] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 用户权限机构!请求IP地址: ************** 操作开始时间: 2025-07-24 20:14:04 操作结束时间: 2025-07-24 20:14:04!总共花费时间: 204 毫秒！
2025-07-24 20:14:04.966 [] [4f4141f357b7d321/c7654448a6a2f610] [http-nio-9902-exec-31] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 操作机构信息 操作方法: 用户权限机构!请求IP地址: ************** 操作开始时间: 2025-07-24 20:14:04 操作结束时间: 2025-07-24 20:14:04!总共花费时间: 134 毫秒！
2025-07-24 20:14:19.715 [] [45feb7e0456ef93c/db11fcc6c9466f47] [http-nio-9902-exec-33] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 20:14:19.715 [] [45feb7e0456ef93c/db11fcc6c9466f47] [http-nio-9902-exec-33] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 20:14:19.719 [] [45feb7e0456ef93c/db11fcc6c9466f47] [http-nio-9902-exec-33] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 20:14:19.944 [] [4955f9c42a67db07/f192596ed86ee8b8] [http-nio-9902-exec-35] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 20:14:19.945 [] [4955f9c42a67db07/f192596ed86ee8b8] [http-nio-9902-exec-35] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 20:14:19.949 [] [4955f9c42a67db07/f192596ed86ee8b8] [http-nio-9902-exec-35] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:14:19.955 [] [4955f9c42a67db07/8126ddaf14e58022] [http-nio-9902-exec-34] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 20:14:19.956 [] [4955f9c42a67db07/8126ddaf14e58022] [http-nio-9902-exec-34] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 20:14:19.959 [] [4955f9c42a67db07/8126ddaf14e58022] [http-nio-9902-exec-34] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:14:37.223 [] [bd0202f7fdc97fc2/015d191a5ada72c2] [http-nio-9902-exec-37] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 20:14:37.224 [] [bd0202f7fdc97fc2/015d191a5ada72c2] [http-nio-9902-exec-37] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 20:14:37.227 [] [bd0202f7fdc97fc2/015d191a5ada72c2] [http-nio-9902-exec-37] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 20:14:37.446 [] [0a16db8889917aa9/cf1fde6e5877b997] [http-nio-9902-exec-38] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 20:14:37.446 [] [0a16db8889917aa9/cf1fde6e5877b997] [http-nio-9902-exec-38] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 20:14:37.451 [] [0a16db8889917aa9/cf1fde6e5877b997] [http-nio-9902-exec-38] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:14:37.459 [] [0a16db8889917aa9/7dcfb7f713b5ee8b] [http-nio-9902-exec-39] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 20:14:37.460 [] [0a16db8889917aa9/7dcfb7f713b5ee8b] [http-nio-9902-exec-39] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 20:14:37.465 [] [0a16db8889917aa9/7dcfb7f713b5ee8b] [http-nio-9902-exec-39] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:15:00.843 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:17:32.679 [] [3243a60e3c3c7e94/e5825d83a8051708] [http-nio-9902-exec-41] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==>  Preparing: select T1.FIELD_NAME "FIELD_NAME",T2.ELSE_NAME "ELSE_NAME",T2.FIELD_FLOAT "FIELD_FLOAT" from sm_table_field_tb t1,sm_field_def_tb t2 where t1.field_name=t2.field_name and t1.table_id = ? and t1.location!=0 order by t1.location
2025-07-24 20:17:32.679 [] [3243a60e3c3c7e94/e5825d83a8051708] [http-nio-9902-exec-41] DEBUG c.s.a.c.d.S.getFieldsByTableId - ==> Parameters: 3(Integer)
2025-07-24 20:17:32.681 [] [3243a60e3c3c7e94/e5825d83a8051708] [http-nio-9902-exec-41] DEBUG c.s.a.c.d.S.getFieldsByTableId - <==      Total: 19
2025-07-24 20:17:32.974 [] [a5e879cdb14f1ff5/3dab0dfd9b545d14] [http-nio-9902-exec-42] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select DATA_SOURCE_ID, GROUP_NAME, MODE_CODE, INDEX_NAME, FILE_PART_NAME, UA_IP, UA_PORT, DATA_DESC, CUST_ID, SERVICE_ID,DATA_SOURCE_TYPE from SM_DATASOURCE_SET_TB where DATA_SOURCE_ID = ?
2025-07-24 20:17:32.974 [] [a5e879cdb14f1ff5/3dab0dfd9b545d14] [http-nio-9902-exec-42] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 0(String)
2025-07-24 20:17:32.977 [] [a5e879cdb14f1ff5/3dab0dfd9b545d14] [http-nio-9902-exec-42] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:17:32.984 [] [a5e879cdb14f1ff5/a09d0a2ec8a31049] [http-nio-9902-exec-43] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==>  Preparing: select SERVICE_ID, SERVICE_NAME, SERVICE_IP, SERVICE_PORT, SERVICE_TYPE, LOGIN_NAME, LOGIN_PASS, IS_MONITOR, LISTENER_POST, GROUP_ID, WORK_TYPE from SM_SERVICE_REG_TB where SERVICE_ID = ?
2025-07-24 20:17:32.985 [] [a5e879cdb14f1ff5/a09d0a2ec8a31049] [http-nio-9902-exec-43] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - ==> Parameters: 1(BigDecimal)
2025-07-24 20:17:32.989 [] [a5e879cdb14f1ff5/a09d0a2ec8a31049] [http-nio-9902-exec-43] DEBUG c.s.a.s.d.s.S.selectByPrimaryKey - <==      Total: 1
2025-07-24 20:20:00.846 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:25:00.854 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:30:00.857 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:35:00.868 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:40:00.872 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:45:00.881 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:50:00.886 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 20:55:00.896 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:00:00.911 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:05:00.912 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:10:00.915 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:15:00.928 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:20:00.931 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:25:00.942 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:30:00.944 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:35:00.947 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:40:00.956 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:45:00.959 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:50:00.966 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 21:55:00.977 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:00:00.990 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:05:01.004 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:10:01.015 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:15:01.018 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:20:01.025 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:25:01.037 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:30:01.043 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:35:01.045 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:40:01.056 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:45:01.065 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:50:01.068 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 22:55:01.083 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:00:01.097 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:05:01.111 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:10:01.120 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:15:01.131 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:20:01.138 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:25:01.148 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:30:01.164 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:35:01.167 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:40:01.174 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:45:01.178 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:50:01.189 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-24 23:55:01.194 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
