package com.sunyard.console.safemanage.bean;
/**
 * <p>Title: 申请令牌bean</p>
 * <p>Description: 存放动态/动态令牌信息</p>
 * <p>Copyright: Copyright (c) 2021</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class NewTokenInfoBean {
	private String ip;			//机器IP
	private String server_info;	//机器信息
	private String isTrend;//是否是动态 1为动态 0为静态
	private String token_code;	//令牌
	public String getIp() {
		return ip;
	}
	public void setIp(String ip) {
		this.ip = ip;
	}
	public String getServer_info() {
		return server_info;
	}
	public void setServer_info(String server_info) {
		this.server_info = server_info;
	} 
	public String getToken_code() {
		return token_code;
	}
	public void setToken_code(String token_code) {
		this.token_code = token_code;
	}
	public String getIsTrend() {
		return isTrend;
	}
	public void setIsTrend(String isTrend) {
		this.isTrend = isTrend;
	}
	public String toString(){
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append("ip:").append(ip);
		sBuilder.append(";server_info:").append(server_info);
		sBuilder.append(";token_code:").append(token_code);
		sBuilder.append(";isTrend:").append(isTrend);
		return sBuilder.toString();
	}
    
    public int compareTo(NewTokenInfoBean o) {
        return this.ip.compareTo(o.getIp());
    }
	
}
