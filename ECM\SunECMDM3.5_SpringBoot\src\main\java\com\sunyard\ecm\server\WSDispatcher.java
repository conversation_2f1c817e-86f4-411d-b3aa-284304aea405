package com.sunyard.ecm.server;

import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;
import javax.xml.ws.handler.MessageContext;

import com.sunyard.ecm.common.trans.Connection;
import com.sunyard.ecm.server.cache.LazySingleton;
import org.apache.commons.lang.mutable.MutableObject;
import org.apache.cxf.jaxws.context.WebServiceContextImpl;
import org.apache.cxf.transport.http.AbstractHTTPDestination;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.ecm.common.trans.ws.WsConnection;
import com.sunyard.exception.SunECMExceptionStatus;

/**
 * WEBSERVICE接口请求的分派
 * <AUTHOR>
 *
 */
public class WSDispatcher {
	private final static  Logger log = LoggerFactory.getLogger(WSDispatcher.class);
	
	public static String handleRequest(Map<String, String> map){
		// 判断服务器的是否启用
		String state = LazySingleton.getInstance().load.getNodeInfoBean().getState();
		log.debug("当前内容存储服务器状态["+state+"],[0] 代表停用,[1]代表启用");
		if(state == null){
			return String.valueOf(SunECMExceptionStatus.CONSOLE_CONFIG_FAIL);
		} else if(state.equals("0")){
			return String.valueOf(SunECMExceptionStatus.NODE_IS_STOP);
		}
		MutableObject result=new MutableObject("");
		try {
			Connection con=new WsConnection(map,result);
			con.process();
		} catch (IOException e) {
			//unreachable
		}
		return (String)result.getValue();
		/*//handler = (Handler) SpringUtil.getSpringBean("handler");
		// 派分请求
		String optionKey = map.get("OPTION");
		String contentID = map.get("CONTENTID");
		String result= "";
		//操作前判断是否需要登录操作
		try {
			CheckLogin.Login(map);
		} catch (SunECMException e1) {
		}
		TokenUtil.addClientIp(map,getClientIp());
		try {
			switch(OptionKey.OptionKeys.valueOf(optionKey)){
			case INQUIREDM:
				result = handler.inquiredm(map);
				break;
			case LOGIN : 
				result = handler.login(map);
				break;
			case LOGOUT : 
				result = handler.logout(map);
				break;
			case PERMISSION :
				result = handler.getPermission(map);
				break;
			case UPLOAD : 
				result = handler.upload(map);
				break;
			case UPDATE : 
				result = handler.update(map);
				break;
			case DEL :
				result = handler.delete(map);
				break;
			case QUERY :
				result = handler.query(map);
				break;	
			case HEIGHT_QUERY :
				result = handler.heightQuery(map);
				break;
			case ALLMODELMSG :
				result = handler.getAllModelMsg_Client(map);
				break;
			case METATEMPLATE :
				result = handler.getMetaDataTemplate(map);
				break;
			case CHECKIN :
				result = handler.checkIn(map);
				break;
			case CHECKOUT :
				result = handler.checkOut(map);
				break;
			case ANNOTATION : 
				result = handler.queryAnnotation(map);
				break;
			case A_OR_U_ANNOTATION :
				result = handler.updateAnnotation(map);
				break;
			case GET_NODE :
				result = handler.getAllContentServer(map);
				break;
			case BREAK_POINT :
				result = handler.breakPoint(map);
				break;
			case IMMEDIATEMIGRATE :
				//调用迁移接口
				result = handler.immediateMigrate(map);
				break;
			}
			// 回传信息加上成功与否的头
			String resultMsg = TransOptionKey.SERVER_OK + TransOptionKey.SPLITSYM + result;
			return resultMsg;
		} catch (Exception e) {
			try {
//				StaticFileParams.getInstance().handleBatch(contentID);
				fileParamsStorage.handleBatch(contentID);
			} catch (SunECMException fileError) {
				log.error( "handling the transport file occurs error:" + fileError.toString());
			}
			log.error( e);
			return e.getMessage();
		}*/
	}
	/** 
	 * 获得当前连接的客户端 
	 * */
	public static String getClientIp() {
		log.info( "--getClientIp(start)");
		WebServiceContext context = new WebServiceContextImpl();
		try {
			MessageContext ctx = context.getMessageContext();
			HttpServletRequest request = (HttpServletRequest) ctx
					.get(AbstractHTTPDestination.HTTP_REQUEST);
			String ip = request.getRemoteAddr();
			log.info( "--getClientIp(over)-->ip:" + ip);
			return ip;
		} catch (Exception e) {
			log.error( e.toString());
			return null;
		}
	}
}