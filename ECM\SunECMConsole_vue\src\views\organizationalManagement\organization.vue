<template>
  <div class="app-container">
    <div>
      <label-wrap>项类型:</label-wrap>
      <el-input
        v-model="listQuery.model_code"
        placeholder="项类型"
        style="width: 200px"
      />
      <label-wrap>机构编号:</label-wrap>
      <el-input
        v-model="listQuery.ins_No"
        placeholder="机构编号"
        style="width: 200px"
      />

    </div>
    <div align="center" style="margin-top: 30px;margin-bottom: 30px">
      <el-button  v-if="this.hasPerm('queryInSno')" type="primary"
        icon="el-icon-search"
        size="mini"
        plain
        round  @click="handleFilter">查询</el-button>
      <el-button type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain
        round  @click="reset">重置</el-button>
    </div>


    <div style="margin-top: 20px">
      <el-button
        v-if="this.hasPerm('addInSno')"
        type="primary"
        icon="el-icon-plus"
        size="mini"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>

    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[optionFlag]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="机构编号" prop="insNo">
          <el-input
            v-model="temp.insNo"
            placeholder="机构编号"
            style="width: 200px"
            @input="handleInput"
          />
        </el-form-item>
        <el-form-item label="项类型" prop="model_code">
          <el-select v-model="temp.model_code" placeholder="请选择" @change="handleInput">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="服务器组" prop="group_id">
          <el-select v-model="temp.group_id" placeholder="请选择" @change="handleInput">
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary" size="mini"
          @click="optionFlag === 'create1' ? createData() : updateData()"
        >
          提交
        </el-button>
      </div>
    </el-dialog>


    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="项类型名称" width="300px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.model_name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="服务器组名" width="300px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.group_name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="机构编码" width="300px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.insNo}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="230"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row, $index }">
          <el-button v-if="hasPerm('updateInSno')" type="primary" size="mini" @click="handleUpdate(row)" >
            编辑
          </el-button>

          <el-button
            v-if="row.status != 'deleted'|| hasPerm('delInSno')"
            size="mini"
            type="danger"
            @click="handleDelete(row, $index)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>



    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination'
  import {getObjectName} from "@/api/historyTable";
  import {getInsnoDmsList,addInsNoDms,delInsnoDms} from "@/api/organizational"
  import {getGroupNameList} from "@/api/contentServer"
  import elDragDialog from "@/directive/el-drag-dialog";

  export default {
    components: { Pagination},
    directives: { elDragDialog },
    data() {
      return {
        tableKey: 0,
        list: null,
        total: 0,
        options:"",
        options2:"",
        optionFlag:'',
        listLoading: true,
        listQuery: {
          start:0,
          page: 1,
          limit:20,
          model_code: '',
          ins_No: ''
        },
        temp: {
          nowPID:'',
          insNo: '',
          model_code: '',
          group_name: '',
          optionFlag: '',
          group_id:''

        },
        dialogFormVisible: false,
        textMap: {
          create: '新增',
          update: '修改'
        },
        dialogPvVisible : false,
        isRewrite : false,
        rules: {
          ins_No: [{ required: true, message: '机构编号必输', trigger: 'blur' }],
          model_code: [{ required: true, message: '项类型必输', trigger: 'blur' }],
          group_name: [{ required: true, message: '服务器组必输', trigger: 'blur' }],
        }
      }
    },
    created() {
      this.getList(),
        this.pickerChange(),
        this.pickerChange2()
    },
    methods: {
      getList() {
        this.listLoading = true
        getInsnoDmsList(this.listQuery).then(response => {
          this.list = response.root
          this.total = Number(response.totalProperty)
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
      resetTemp() {
        this.temp = {
          nowPID:'',
          insNo: '',
          model_code: '',
          group_name: '',
          optionFlag: '',
          group_id:''
        }
        this.isRewrite = false;
      },
      handleInput() {
        this.isRewrite = true;
      },
      handleCreate() {
        this.resetTemp()
        this.optionFlag = 'create1'
        this.temp.optionFlag = 'create1'
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      createData() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            addInsNoDms(this.temp).then(() => {
              this.dialogFormVisible = false
              this.getList();
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            })
          }
        })
      },
      handleFilter() {
        this.listQuery.page = 1
        this.getList()
      },
      handleUpdate(row) {
        this.temp = Object.assign({}, row)
        this.optionFlag = 'update1'
        this.temp.optionFlag = 'update1'
        this.isRewrite = false
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      updateData() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid && this.isRewrite) {
            addInsNoDms(this.temp).then(() => {
              this.dialogFormVisible = false
              this.getList();
              this.$notify({
                title: 'Success',
                message: 'Update Successfully',
                type: 'success',
                duration: 2000
              })
            })
          }else if(valid){
            this.$notify({
                title: 'fail',
                message: '数据没有修改，请先进行修改再提交',
                type: 'fail',
                duration: 2000
              })
          }
        })
      },
      handleDelete(row, index) {
        this.openDelConfirm().then(() => {
          delInsnoDms(row).then(response => {
            this.getList()
            this.$notify({
              title: "Success",
              message: "Delete Successfully",
              type: "success",
              duration: 2000,
            });
          })
        })
      },
      openDelConfirm(){
        return this.$confirm(`是否确定删除？`,'提示',{
          confirmButtonText:'确定',
          cancelButtonText:'取消',
          type:'warning'
        })
      },
      reset() {
        this.listQuery.model_code=""
        this.listQuery.ins_No=""
      },
      pickerChange(){
        getObjectName().then(response => {
          this.options = response.root
          for (var i = 0; i < this.options.length; i++) {
            this.options[i].label = this.options[i].text_text;
            this.options[i].value = this.options[i].id;
          }
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
      pickerChange2(){
        getGroupNameList().then(response => {
          this.options2 = response.root
          for (var i = 0; i < this.options2.length; i++) {
            this.options2[i].label = this.options2[i].text_text;
            this.options2[i].value = this.options2[i].id;
          }
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
    }
  }
</script>
