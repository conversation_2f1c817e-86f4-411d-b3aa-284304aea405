package com.sunyard.console.unityaccessservermanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.contentmodelmanage.bean.TreeBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.threadpoool.IssueUtils;
import com.sunyard.console.unityaccessservermanage.bean.UnityAccessServerGroupInfoBean;
import com.sunyard.console.unityaccessservermanage.dao.UnityAccessServerGroupManageDAO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 统一接入服务器管理action
 * 
 * <AUTHOR>
 * 
 */
@Controller
public class UnityAccessServerGroupAction extends BaseAction {
	@Autowired
	UnityAccessServerGroupManageDAO unityAccessServerGroupManageDao;
	/**
	 * 服务器组id
	 */
	private int group_id;
	/**
	 * 服务器组名称
	 */
	private String group_name;
	/**
	 * 服务器组ip
	 */
	private String ip;
	/**
	 * http端口
	 */
	private int http_port;
	/**
	 * socket端口
	 */
	private int socket_port;
	/**
	 * 备注
	 */
	private String remark;

	private int start;
	private int limit;
	/**
	 * 新增或修改的标识符
	 */
	private String optionFlag;
	/**
	 * 多个服务器id组成的字符串
	 */
	private String server_ids;
	private String value;//唯一性校验
	/**
	 * 日志对象
	 */
	private final static  Logger log = LoggerFactory.getLogger(UnityAccessServerGroupAction.class);

	public UnityAccessServerGroupManageDAO getUnityAccessServerGroupManageDao() {
		return unityAccessServerGroupManageDao;
	}

	public void setUnityAccessServerGroupManageDao(
			UnityAccessServerGroupManageDAO unityAccessServerGroupManageDao) {
		this.unityAccessServerGroupManageDao = unityAccessServerGroupManageDao;
	}

	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int groupId) {
		group_id = groupId;
	}

	public String getGroup_name() {
		return group_name;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public void setGroup_name(String groupName) {
		group_name = groupName;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public int getHttp_port() {
		return http_port;
	}

	public void setHttp_port(int httpPort) {
		http_port = httpPort;
	}

	public int getSocket_port() {
		return socket_port;
	}

	public void setSocket_port(int socketPort) {
		socket_port = socketPort;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	public String getServer_ids() {
		return server_ids;
	}

	public void setServer_ids(String serverIds) {
		server_ids = serverIds;
	}

	/**
	 * 分页获取统一接入服务器组列表
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/getUnityAccessServerGroupListAction.action", method = RequestMethod.POST)
	public String getUnityAccessServerGroupList(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int start = modelJson.getInt("start");
		int limit = modelJson.getInt("limit");
		String group_id = (String) modelJson.getOrDefault("group_id", "");
		String group_name = (String) modelJson.getOrDefault("group_name", "");
		log.info("--getUnityAccessServerGroupList(start)-->group_id:" + group_id + ",group_name：" + group_name);
		String jsonStr = null;
		group_id = "".equals(group_id) || group_id == null ? "0" : group_id;
		start = (start-1) * limit;
		try {
			List<UnityAccessServerGroupInfoBean> ServerInfoList = unityAccessServerGroupManageDao
					.getUnityAccessServerGroupList(Integer.valueOf(group_id), group_name,
							start + 1, limit);
			log.debug("--getUnityAccessServerGroupList-->ServerInfoList:" + ServerInfoList);
			List<UnityAccessServerGroupInfoBean> AllInfoList = unityAccessServerGroupManageDao
					.getUnityAccessServerGroupList(Integer.valueOf(group_id), group_name);
			log.debug("--getUnityAccessServerGroupList-->AllInfoList:" + AllInfoList);
			jsonStr = new JSONUtil().createJsonDataByColl(ServerInfoList,
					AllInfoList.size(), new UnityAccessServerGroupInfoBean());
			log.debug("--getUnityAccessServerGroupList-->获取统一接入服务器组列表成功!");
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取统一接入服务器组信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "统一接入服务器组管理->获取统一接入服务器组列表失败!" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getUnityAccessServerGroupList(over)-->group_id:"+group_id);
		return null;

	}

	/**
	 *校验服务器组IP地址和端口唯一性
	 * 
	 * @return 
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/checkServerGroupIPandPortAction.action", method = RequestMethod.POST)
	public String checkServerGroupIPandPort(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String group_id = (String) modelJson.getOrDefault("group_id", "");
		String ip = (String) modelJson.getOrDefault("ip", "");
		int http_port = modelJson.getInt("http_port");
		int socket_port = modelJson.getInt("socket_port");
		log.info("--checkServerGroupIPandPort(start)-->group_id:" + group_id + ",ip:" + ip + ",http_port:" + http_port + ",socket_port:" + socket_port);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		group_id = "".equals(group_id) || group_id == null ? "0" : group_id;
		int count = 0;
		try {
			count = unityAccessServerGroupManageDao.checkServerGroupIPandPort(
					Integer.valueOf(group_id), ip, http_port, socket_port);
			log.debug( "--checkServerGroupIPandPort(over)");
		} catch (Exception e) {
			count = -1;
			log.error( "统一接入服务器组管理->检验服务器组IP地址和端口唯一性失败!" + e.toString(), e);
		}
		if (count == 0) {
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();
		} else if (count > 0) {
			jsonResp.put("success", false);
			jsonResp.put("message","IP和端口已经存在!!");
			jsonResp.put("reason", "IP和端口已经存在!!");
			jsonStr = jsonResp.toString();
		} else {
			jsonResp.put("success", true);
			jsonResp.put("message","IP和端口检验失败!!");
			jsonResp.put("reason", "检验失败!!");
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--checkServerGroupIPandPort(over)-->group_id:"+group_id);
		return null;
	}

	/**
	 * 新增或修改统一接入服务器组
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/addUnityAccessServerGroupAction.action", method = RequestMethod.POST)
	public String addUnityAccessServerGroup(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_ids = (String) modelJson.getOrDefault("server_ids", "");
		String group_id = (String) modelJson.getOrDefault("group_id", "");
		String group_name = (String) modelJson.getOrDefault("group_name", "");
		String ip = (String) modelJson.getOrDefault("ip", "");
		int http_port = modelJson.getInt("http_port");
		int socket_port = modelJson.getInt("socket_port");
		String remark = (String) modelJson.getOrDefault("remark", "");
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");
		try {
			group_name = URLDecoder.decode(group_name, "utf-8");
			remark = URLDecoder.decode(remark, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode UnityAccessServer fields error, group_name=" + group_name
					+ ", remark=" + remark, e1);
		}
		log.info("--addUnityAccessServerGroup(start)-->server_ids" + server_ids);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		UnityAccessServerGroupInfoBean bean = new UnityAccessServerGroupInfoBean();
		group_id = "".equals(group_id) || group_id == null ? "0" : group_id;
		bean.setGroup_id(Integer.valueOf(group_id));
		bean.setGroup_name(group_name);
		bean.setIp(ip);
		bean.setHttp_port(http_port);
		bean.setSocket_port(socket_port);
		bean.setRemark(remark);
		log.debug( "--addUnityAccessServerGroup-->bean:"+bean);
		try {
			boolean result = false;
			if (optionFlag != null && optionFlag.equals("create1")) {
				result = unityAccessServerGroupManageDao
						.addUnityAccessServerGroup(bean, server_ids);
			} else if (optionFlag != null && optionFlag.equals("update1")) {
				result = unityAccessServerGroupManageDao
						.updateUnityAccessServerGroup(bean, server_ids);
			}
			log.debug( "--addUnityAccessServerGroup-->result:"+result);
			if (result) {
				log.debug( "--addUnityAccessServerGroup-->配置统一接入服务器组成功!");
				jsonResp.put("success", true);
				jsonResp.put("message", "配置统一接入服务器组成功!!");
				jsonResp.put("code", 20000);//TODO mock
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			} else {
				log.debug( "--addUnityAccessServerGroup-->配置统一接入服务器组失败!");
				jsonResp.put("success", false);
				jsonResp.put("message", "配置统一接入服务器组失败!!");
			}
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置统一接入服务器组失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "统一接入服务器组管理->配置统一接入服务器组列表失败!" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--addUnityAccessServerGroup(over)-->server_ids"+server_ids);
		return null;
	}

	/**
	 * 查询被统一接入服务器组关联的统一接入服务器
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/getRelUnityAccessServerTreeAction.action", method = RequestMethod.POST)
	public String getRelUnityAccessServerTree(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int group_id = modelJson.getInt("group_id");
		log.info("--getRelUnityAccessServerTree(start)-->group_id:" + group_id);
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		try {
			List<TreeBean> attrList = unityAccessServerGroupManageDao
					.getRelUnityAccessServerTree(group_id);
			if (attrList != null && attrList.size() > 0) {
				for (int i = 0; i < attrList.size(); i++) {
					JSONObject jsonObj = new JSONObject();
					jsonObj.put("server_id", attrList.get(i).getId());
					jsonObj.put("server_name", attrList.get(i).getText_text());
					jsonObj.put("leaf", true);
					jsonArray.add(jsonObj);
				}
			}
			jsonResp.put("root", jsonArray);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonStr = jsonResp.toString();
			log.debug( "--getRelUnityAccessServerTree-->查询关联的统一接入服务器成功！");
		} catch (Exception e) {
			jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "展示已选择服务器树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "统一接入服务器组管理->查询关联的统一接入服务器失败!" + e.toString(), e);
		}

		this.outJsonString(jsonStr);
		log.info( "--getRelUnityAccessServerTree(over)-->group_id:"+group_id);
		return null;
	}

	/**
	 * 查询未被统一接入服务器组关联的统一接入服务器树
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/getUnRelUnityAccessServerTreeAction.action")
	public String getUnRelUnityAccessServerTree() {
		log.info( "--getUnRelUnityAccessServerTree(start)");
		String jsonStr = "";
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		try {
			List<TreeBean> attrList = unityAccessServerGroupManageDao
					.getUnRelUnityAccessServerTree();
			if (attrList != null && attrList.size() > 0) {
				for (int i = 0; i < attrList.size(); i++) {
					JSONObject jsonObj = new JSONObject();
					jsonObj.put("server_id", attrList.get(i).getId());
					jsonObj.put("server_name", attrList.get(i).getText_text());
					jsonObj.put("leaf", true);
					jsonArray.add(jsonObj);
				}
			}
			jsonResp.put("root", jsonArray);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonStr = jsonResp.toString();
			log.debug( "--getUnRelUnityAccessServerTree-->展示未选择服务器树成功!!");
		} catch (DBRuntimeException e) {
		    jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "展示未选择服务器树失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "统一接入服务器组管理->查询未关联的统一接入服务器失败!" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getUnRelUnityAccessServerTree(over)");
		return null;
	}
	/**
	 * 检测该服务器组名称是否存在
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/untiyAccessServerManage/checkGroupNameAction.action", method = RequestMethod.POST)
	public String checkGroupName(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String group_id = (String) modelJson.getOrDefault("group_id", "");
		String group_name = (String) modelJson.getOrDefault("group_name", "");
		try {
			group_name = URLDecoder.decode(group_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode UnityAccessServer fields error, group_name=" + group_name, e1);
		}
		log.info("--checkGroupName(start)-->group_id:" + group_id + ";group_name:" + group_name);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		group_id = "".equals(group_id) || group_id == null ? "0" : group_id;
		int count = 0;
		try {
			count = unityAccessServerGroupManageDao.checkGroupName(Integer.valueOf(group_id), group_name);
			log.debug("--checkGroupName(over)-->count:" + count);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "统一接入服务器组管理->校验服务器组名称唯一性失败!" + e.toString(), e);
		}
		if (count == 0) {
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonStr = jsonResp.toString();
		} else if (count > 0) {
			jsonResp.put("success", false);
			jsonResp.put("message","服务器组名称已经存在!!");
			jsonResp.put("reason", "服务器组名称已经存在!!");
			jsonStr = jsonResp.toString();
		} else {
			jsonResp.put("success", true);
			jsonResp.put("message","服务器组名称检验失败!!");
			jsonResp.put("reason", "检验失败!!");
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--checkGroupName(over)-->group_id:"+group_id);
		return null;
	}
}
