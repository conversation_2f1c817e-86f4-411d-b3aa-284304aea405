﻿@echo off
#set dt=2021-05-19
set dt=%date:~0,10%
rem date format is "YYYY-MM-DD"
rem set /P dt="Input Date: "
set dy=%dt:~0,4%
set dm=%dt:~5,2%
set dd=%dt:~8,2%


if %dm%%dd%==0101 goto L01
if %dm%%dd%==0201 goto L02
if %dm%%dd%==0301 goto L07
if %dm%%dd%==0401 goto L02
if %dm%%dd%==0501 goto L04
if %dm%%dd%==0601 goto L02
if %dm%%dd%==0701 goto L04
if %dm%%dd%==0801 goto L02
if %dm%%dd%==0901 goto L02
if %dm%%dd%==1001 goto L05
if %dm%%dd%==1101 goto L03
if %dm%%dd%==1201 goto L06
if %dd%==02 goto L10
if %dd%==03 goto L10
if %dd%==04 goto L10
if %dd%==05 goto L10
if %dd%==06 goto L10
if %dd%==07 goto L10
if %dd%==08 goto L10
if %dd%==09 goto L10
if %dd%==10 goto L11
set /A dd=dd-1
set dt=%dy%%dm%%dd%
goto END
:L10
set /A dd=%dd:~1,1%-1
set dt=%dy%%dm%0%dd%
goto END
:L11
set dt=%dy%%dm%09
goto END
:L02
set /A dm=%dm:~1,1%-1
set dt=%dy%0%dm%31
goto END
:L04
set /A dm=dm-1
set dt=%dy%0%dm%30
goto END
:L05
set dt=%dy%0930
goto END
:L03
set dt=%dy%1031
goto END
:L06
set dt=%dy%1130
goto END
:L01
set /A dy=dy-1
set dt=%dy%1231
goto END
:L07
set /A "dd=dy%%4"
if not %dd%==0 goto L08
set /A "dd=dy%%100"
if not %dd%==0 goto L09
set /A "dd=dy%%400"
if %dd%==0 goto L09
:L08
set dt=%dy%0228
goto END
:L09
set dt=%dy%0229
goto END
:END
echo %dt%
set dtd=%dt%
rem 判断业务日期文件夹是否存在
set windir=D:\data\%dt%
:: ADD 2019-03-01 关闭非输出显示
@echo off
:: END 2019-03-01 
if exist %windir% (
	 
		if exist %windir%\COSD0090_%dt%.txt ( 
			echo COSD0090 yes 
		) else (
			del /Q .\files-moxing.ftp
			call makefile-moxing > files-moxing.ftp
			ftp.exe -n -s:"files-moxing.ftp"
                        echo biaobao is not exist
			if exist %windir%\COSD0090_%dtd%.txt ( 
				echo COSD0090 yes 
			) else ( 
				if exist %windir%\CORD0050_%dtd%.txt ( 
					cd.>%windir%\COSD0090_%dt%.txt
				)
			)
		)
		if exist %windir%\CORD0030_%dt%.txt ( 
			echo CORD0030 yes 
		) else (
			del /Q .\files-moxing.ftp
			call makefile-moxing > files-moxing.ftp
			ftp.exe -n -s:"files-moxing.ftp" 
                        echo biaobao is not exist
			if exist %windir%\CORD0030_%dtd%.txt ( 
				echo CORD0030 yes 
			) else ( 
				if exist %windir%\CORD0050_%dtd%.txt ( 
					cd.>%windir%\CORD0030_%dt%.txt
				)
			)
		)
		if exist %windir%\DEPD0020_%dt%.txt ( 
			echo DEPD0020 yes 
		) else (
			del /Q .\files-moxing.ftp
			call makefile-moxing > files-moxing.ftp
			ftp.exe -n -s:"files-moxing.ftp"
                        echo biaobao is not exist
			if exist %windir%\DEPD0020_%dtd%.txt ( 
				echo DEPD0020 yes 
			) else ( 
				if exist %windir%\CORD0050_%dtd%.txt ( 
					cd.>%windir%\DEPD0020_%dt%.txt
				)
			)
		)
		if exist %windir%\PIVS0030_%dt%.txt ( 
			echo PIVS0030 yes 
		) else (
			del /Q .\files-moxing.ftp
			call makefile-moxing > files-moxing.ftp
			ftp.exe -n -s:"files-moxing.ftp" 
                        echo biaobao is not exist
			if exist %windir%\PIVS0030_%dtd%.txt ( 
				echo PIVS0030 yes 
			) else ( 
				if exist %windir%\CORD0050_%dtd%.txt ( 
					cd.>%windir%\PIVS0030_%dt%.txt
				)
			)
		)
		if exist %windir%\PIVS0060_%dt%.txt ( 
			echo PIVS0060 yes 
		) else (
			del /Q .\files-moxing.ftp
			call makefile-moxing > files-moxing.ftp
			ftp.exe -n -s:"files-moxing.ftp"
                        echo biaobao is not exist
			if exist %windir%\PIVS0060_%dtd%.txt ( 
				echo PIVS0060 yes 
			) else ( 
				if exist %windir%\CORD0050_%dtd%.txt ( 
					cd.>%windir%\PIVS0060_%dt%.txt
				)
			)
		) 
	
) else (
rem 第一次下载文件 
	md %windir%
	del /Q .\files-moxing.ftp
	call makefile-moxing > files-moxing.ftp
	ftp.exe -n -s:"files-moxing.ftp"
)
echo %windir%

if exist %windir%\END.TXT (
echo END
) 
  else(

set flag=0
for %%a in ( 
	%windir%\0100006D.d01.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\0100006D.k02.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\0100006D.k07.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\0100006D.k11.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\0100006D.k15.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\0186310D.at1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.at2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.at3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.at4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.d11.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.d12.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.d13.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.d14.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.d21.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.d22.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
  %windir%\0186310D.d23.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.d24.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.dd1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.dm1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.ew1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.i21.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.i22.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.i23.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.i24.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.i31.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.i32.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.i33.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.i34.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.id1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.id2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.id3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.id4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.k10.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l11.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l12.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l13.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l14.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l21.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l22.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l23.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l24.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l51.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l52.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l53.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.l54.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.m01.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.m02.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.m03.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.pc1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.pd1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.pj1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.u00.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.u20.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.u40.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.ub0.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.uc0.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v01.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v02.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v03.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v04.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v25.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v26.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v27.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v28.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v35.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v36.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v37.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v38.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v45.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v46.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v47.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v48.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v55.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v56.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v57.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v58.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v65.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v66.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v67.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v68.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vh1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vh2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vh3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vh4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vo1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vo2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vo3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vo4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vu1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vu2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vu3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vu4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vw1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vw2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vw3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vw4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x01.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x02.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x03.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x04.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x31.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x32.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x33.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x34.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x91.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x92.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x93.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.x94.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xg1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xg2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xg3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xg4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xh1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xh2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xh3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xh4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xm1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xm2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xm3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xm4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xt1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xt2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xt3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xt4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xx1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xx2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xx3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xx4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
        %windir%\0186310D.va1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.va2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.va3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.va4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vq1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vq2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vq3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vq4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
        %windir%\PI86310D.P30.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\6386310D.CAD.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
        %windir%\GL00006D.CRA.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\6386310D.FIX.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\6386310D.OBM.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\6386310D.OBP.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\6386310D.SAV.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\6386310D.TXN.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\BS86310D.101.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\GL00006D.CRA.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\US86310D.TLG.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
        %windir%\SO86310D.QAT.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
        %windir%\SO86310D.TRN.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1, 
	%windir%\0100006D.pg1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\0100006D.k22.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\REVERSE%dt:~0,4%%dt:~4,2%%dt:~6,2%*,
	%windir%\DEPD0020_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\CORD0030_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\PIVS0030_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\PIVS0060_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\trace%dt:~0,4%%dt:~4,2%%dt:~6,2%.AB6
) do if not exist %%a ( 	
echo %%a is not exist 
set flag=1)
echo %flag%
if %flag% == 1 ( 
	del /Q .\files-moxing.ftp
	call makefile-moxing > files-moxing.ftp
	ftp.exe -n -s:"files-moxing.ftp"
 ) 
 )
if %flag% == 0 ( cd.>%windir%\END.TXT )
)
