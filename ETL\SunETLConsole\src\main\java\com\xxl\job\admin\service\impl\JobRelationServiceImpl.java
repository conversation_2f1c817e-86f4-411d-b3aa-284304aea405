package com.xxl.job.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.xxl.job.admin.core.model.JobRelation;
import com.xxl.job.admin.core.model.TreeNode;
import com.xxl.job.admin.core.model.TreeNodesCheck;
import com.xxl.job.admin.dao.JobRelationDao;
import com.xxl.job.admin.service.JobRelationService;
import com.xxl.job.core.biz.model.ReturnT;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class JobRelationServiceImpl implements JobRelationService {
    @Resource
    private JobRelationDao jobRelationDao;

    @Override
    public Map<String, Object> jobRelationPageList(int start, int length, int jobId, String executorHandler, String filterTime) {
        // page list
    	PageHelper.startPage(start/length+1, length);
        List<JobRelation> list = jobRelationDao.pageListWithDesc(start, length, jobId, executorHandler);
        int list_count = jobRelationDao.pageListCount(start, length, jobId, executorHandler);


        // package result
        Map<String, Object> maps = new HashMap<String, Object>();
        maps.put("recordsTotal", list_count);		// 总记录数
        maps.put("recordsFiltered", list_count);	// 过滤后的总记录数
        maps.put("data", list);  					// 分页列表
        return maps;
    }

    public ReturnT<String> add(List<JobRelation> jobRelations) {
        int vnt = 0;
        for (int x = 0 ;x < jobRelations.size() ;x++){
            vnt = jobRelationDao.insertRelation(jobRelations.get(x));
        }
       if(vnt > 0){
            return  ReturnT.SUCCESS;
       }else {
        return ReturnT.FAIL;
       }
    }

    @Override
    public ReturnT<String> reschedule(JobRelation jobRelation) {
        int
            vnt = jobRelationDao.update(jobRelation);

        if(vnt > 0){
            return  ReturnT.SUCCESS;
        }else {
            return ReturnT.FAIL;
        }
    }

    @Override
    public ReturnT<String> remove(int id) {
        int vnt = 0;
        vnt = jobRelationDao.delete(id);
        if(vnt > 0){
            return  ReturnT.SUCCESS;
        }else {
            return ReturnT.FAIL;
        }
    }

    /*
    *
    */
    public  String jobQueryList(int jobId) {
       List<JobRelation>  jobRelations =  jobRelationDao.jobQueryList(jobId);

        List<TreeNode> treeNodes = new ArrayList<TreeNode>();


        for (int i = 0; i < jobRelations.size(); i++) {
            treeNodes.add(new TreeNode(jobRelations.get(i).getChildrenJob()+"", jobRelations.get(i).getJobId()+""));

        }

        StringBuffer data = new StringBuffer("");

        List<String>[] retListArr = new TreeNodesCheck().getList(String.valueOf(jobId).trim(), treeNodes);

        List<String> errMsg = retListArr[1];
        if(errMsg != null && errMsg.size()>0) {
            for(String err : errMsg) {
                data.append(","+err);
            }
        }else {
            List<String> jobList = retListArr[0];
            for(String l : jobList) {
                data.append(","+l);
            }
        }

        System.out.println("JobRelationServiceImpl.jobQueryList"+data.substring(1));
        return data.substring(1);
    }

    @Override
    public JobRelation loadJobRelation(int id) {
        return jobRelationDao.loadById(id);
    }


}
