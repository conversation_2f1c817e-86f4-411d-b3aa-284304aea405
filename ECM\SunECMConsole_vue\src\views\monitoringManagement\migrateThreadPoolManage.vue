<template>
  <div class="app-container">
    <div class="filter-container">
       <el-select v-model="listQuery.group_id" placeholder="请选择存储服务器组" @change='getServers()'>
        <el-option
          v-for="item in storeGroups"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
     <el-select v-model="listQuery.server_id" placeholder="请选择存储服务器">
        <el-option
          v-for="item in storeServers"
          :key="item.id"
          :label="item.text"
          :value="item.id">
        </el-option>
      </el-select>
      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain
        round
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain
        round
        @click="handleclear"
      >
        清空
      </el-button>
    </div>

    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column label="服务器ID" align="center" min-width="20%">
        <template slot-scope="{ row }">
          <span>{{ row.server_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="线程名称" align="center" min-width="20%">
        <template slot-scope="{ row }">
          <span>{{ row.thread_Name }}</span>
        </template>
      </el-table-column>

      <el-table-column label="线程进入时间" align="center" min-width="20%">
        <template slot-scope="{ row }">
          <span :style="{'color':timecolor}">{{ row.thread_Time }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {getContentServerGroup,getRelServers,getMigrateThreadPool} from '@/api/monitorManage'
import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination

import global from "../../store/global.js";

export default {
  name: "ComplexTable",
  components: { Pagination },
  directives: { waves,elDragDialog },
  filters: {
    statusFilter(status) {
      const statusMap = {
        published: "success",
        draft: "info",
        deleted: "danger",
      };
      return statusMap[status];
    },
  },

  data() {
    return {
      timecolor : "green",
      storeGroups : [],
      storeServers : [],
      tableKey: 0,
      list: null,
      total: 0,
      listLoading: true,
      listQuery: {
        page: 1,
        limit: 20,
        importance: undefined,
        group_id: "",
        server_id: "",
        title: undefined,
        type: undefined,
        sort: "+server_id",
      },

      importanceOptions: [1, 2, 3],
      sortOptions: [
        { label: "ID Ascending", key: "+server_id" },
        { label: "ID Descending", key: "-server_id" },
      ],
      showReviewer: false,
      downloadLoading: false,
    };
  },

  created() {
    this.getList();
    this.getGroups();
  },

  methods: {
    getList() {
      this.listLoading = true;
      getMigrateThreadPool(this.listQuery).then((response) => {
        this.list = response.root;
        this.total = Number(response.totalProperty);
        if(this.total!='0'){
          this.changeTimeColor();
        }
        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },

    changeTimeColor(){
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hour = date.getHours();
      let minute = date.getMinutes();
      let s = date.getSeconds();
      if (month < 10) {
        month = '0' + month;
      }
      if (day < 10) {
        day = '0' + day;
      }
      if (hour < 10) {
        hour = '0' + hour;
      }
      if (minute < 10) {
        minute = '0' + minute;
      }
      if (s < 10) {
        s = '0' + s;
      }
      for(let item of this.list){
        let svalue = item.thread_Time;
        let time = year + '' + month + day + hour + minute + s;
        if ((parseInt(time) - parseInt(svalue)) >= 100) {
          this.timecolor = "red";
        } else if((parseInt(time) - parseInt(svalue)) >= 50){
          this.timecolor = "black";
        }else{
          this.timecolor = "green";
        }
        item.thread_Time = svalue.substr(0,4)+'-'+svalue.substr(4,2)+'-'+svalue.substr(6,2)+' '+svalue.substr(8,2)+':'+svalue.substr(10,2)+':'+svalue.substr(12,2);
      }
    },

    getGroups(){
        getContentServerGroup().then(response => {
        this.storeGroups = response.root;
      })
    },

    getServers(){
      getRelServers(this.listQuery).then(response => {
        this.storeServers = response.root;
      })
    },

    handleclear() {
      this.listQuery.group_id = "";
      this.listQuery.server_id = "";
    },

    handleFilter() {
      this.listQuery.page = 1;
      this.getList();
    },
    sortChange(data) {
      const { prop, order } = data;
      alert(prop);
      if (prop === "server_id") {
        this.sortByID(order);
      }
    },
    sortByID(order) {
      if (order === "ascending") {
        this.listQuery.sort = "+server_id";
      } else {
        this.listQuery.sort = "-server_id";
      }
      this.handleFilter();
    },

    formatJson(filterVal) {
      return this.list.map((v) =>
        filterVal.map((j) => {
          if (j === "timestamp") {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    },
    getSortClass: function (key) {
      const sort = this.listQuery.sort;
      return sort === `+${key}` ? "ascending" : "descending";
    },
  },
};
</script>
