package com.sunyard.ecm.server.cache.wsclient;

import com.sunyard.ecm.consoleclient.WSConsoleUtil;
import com.sunyard.ecm.server.bean.*;
import com.sunyard.initialization.LoadConfigFile;
import com.sunyard.ws.internalapi.SunEcmConsole;
import com.sunyard.ws.utils.XMLUtil;

import java.util.List;


/**
 * <p>
 * Title: 鑾峰緱webService鎺ュ彛
 * </p>
 * <p>
 * Description: 鑾峰緱webService鎺ュ彛锛屽彂閫佽幏鍙栭厤缃俊鎭姹�
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class GetConfigInterfaceImpl implements GetConfigInterface {



    private SunEcmConsole wsInterface;


    // 获得webService接口
    public SunEcmConsole getWsInterface() {
        if (wsInterface == null) {
            wsInterface = WSConsoleUtil.getConsoleClient();
        }
        return wsInterface;
    }

    public void setWsInterface(SunEcmConsole wsInterface) {
        this.wsInterface = wsInterface;
    }


    /**
     * 获取内容模型列表信息
     */
    public List<AllModelMsgBean> getAllModelMsg() {
        String xml;
        List<AllModelMsgBean> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getAllModelMsg();
        list = XMLUtil.xml2list(XMLUtil
                .removeHeadRoot(xml), AllModelMsgBean.class);
        return list;
    }

    /**
     * 获得参数配置列表信息
     */
    public List<AllParamMsgBean> getAllParConfigMsg() {
        String xml;
        List<AllParamMsgBean> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getAllParConfigMsg();
        list = XMLUtil.xml2list(XMLUtil
                .removeHeadRoot(xml), AllParamMsgBean.class);
        return list;
    }

    /**
     * 获取生命周期策略
     *
     * @param groupID 服务器组ID
     */
    public List<LifeCycleStrategyBean> getLifeCycleStrategy(String groupID) {
        String xml;
        List<LifeCycleStrategyBean> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getLifeCycleStrategy(groupID);
        list = XMLUtil.xml2list(XMLUtil
                .removeHeadRoot(xml), LifeCycleStrategyBean.class);
        return list;
    }

    /**
     * 获取内容模型的表
     */
    public List<ModelTableBean> getModelTable() {
        String xml;
        List<ModelTableBean> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getModelTable();
        list = XMLUtil.xml2list(XMLUtil
                .removeHeadRoot(xml), ModelTableBean.class);
        return list;
    }

    /**
     * 获取内容模型模版信息
     *
     * @param docNames 内容模型模版code列表
     * @param groupID  服务器组Id
     */
    public List<ModelTemplateBean> getModelTemplate(String[] docNames,
                                                    String groupID) {
        String xml;
        List<ModelTemplateBean> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getModelTemplate(groupID, docNames);
        list = XMLUtil.xml2list(XMLUtil
                .removeHeadRoot(xml), ModelTemplateBean.class);
        return list;
    }

    /**
     * 获取所有服务器信息
     */
    public List<NodeInfo> getContentServer() {
        String xml;
        List<NodeInfo> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getContentServer();
        list = XMLUtil.xml2list(XMLUtil.removeHeadRoot(xml), NodeInfo.class);
        return list;
    }

    /**
     * 获取存储对象
     *
     * @param groupID 所属服务器组ID
     */
    public List<StoreObjectBean> getStoreObject(String groupID) {
        String xml;
        List<StoreObjectBean> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getStoreObject(groupID);
        list = XMLUtil.xml2list(XMLUtil
                .removeHeadRoot(xml), StoreObjectBean.class);
        return list;
    }

    /**
     * 获取内容服务器组和内容对象对应卷
     *
     * @param groupID 服务器组ID
     */
    public List<SGroupModleSetBean> getSgroupmodleSet(
            String groupID) {
        String xml;
        List<SGroupModleSetBean> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getSgroupmodleSet(groupID);
        list = XMLUtil.xml2list(XMLUtil
                .removeHeadRoot(xml), SGroupModleSetBean.class);
        return list;
    }

    /**
     * 主动获取日志策略
     */
    public List<DMLogRuleBean> getLogRule() {
        String xml;
        List<DMLogRuleBean> list;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getLogRule();
        list = XMLUtil.xml2list(XMLUtil
                .removeHeadRoot(xml), DMLogRuleBean.class);
        return list;
    }

    /**
     * 主动获取所有EsInfoBean信息
     *
     * */
    public List<TagInfoBean> getAllTagInfoBean(){
        String xml;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getAll_TagInfoBeans();
        List<TagInfoBean> list = XMLUtil.xml2list(XMLUtil.removeHeadRoot(xml), TagInfoBean.class);
        return list;
    }

    /**
     * 主动获取所有AttrDesenRuleBean信息
     *
     * */
    public List<AttrDesenRuleBean> getAllAttrDesenBean() {
        String xml;
        SunEcmConsole wsInterface = getWsInterface();
        xml = wsInterface.getAllAttrDesenRuleBeans();
        List<AttrDesenRuleBean> list = XMLUtil.xml2list(XMLUtil.removeHeadRoot(xml), AttrDesenRuleBean.class);
        return list;
    }

}

