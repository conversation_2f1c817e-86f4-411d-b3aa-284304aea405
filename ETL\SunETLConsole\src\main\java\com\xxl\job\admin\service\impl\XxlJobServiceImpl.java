package com.xxl.job.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.xxl.job.admin.core.enums.ExecutorFailStrategyEnum;
import com.xxl.job.admin.core.enums.WriteTypeEnum;
import com.xxl.job.admin.core.model.XxlJobGroup;
import com.xxl.job.admin.core.model.XxlJobInfo;
import com.xxl.job.admin.core.route.ExecutorRouteStrategyEnum;
import com.xxl.job.admin.core.schedule.XxlJobDynamicScheduler;
import com.xxl.job.admin.core.trigger.XxlJobTrigger;
import com.xxl.job.admin.core.util.LocalCacheUtil;
import com.xxl.job.admin.dao.JobParamDao;
import com.xxl.job.admin.dao.XxlJobGroupDao;
import com.xxl.job.admin.dao.XxlJobInfoDao;
import com.xxl.job.admin.dao.XxlJobLogDao;
import com.xxl.job.admin.service.XxlJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.enums.ExecutorBlockStrategyEnum;
import com.xxl.job.core.glue.GlueTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang.time.FastDateFormat;
import org.quartz.CronExpression;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * core job action for xxl-job
 * <AUTHOR> 2016-5-28 15:30:33
 */
@Service
public class XxlJobServiceImpl implements XxlJobService {
	private static Logger logger = LoggerFactory.getLogger(XxlJobServiceImpl.class);

	@Resource
	private XxlJobGroupDao xxlJobGroupDao;
	@Resource
	private XxlJobInfoDao xxlJobInfoDao;
	@Resource
	public XxlJobLogDao xxlJobLogDao;
	@Resource
	public JobParamDao jobParamDao;



	@Override
	public Map<String, Object> pageList(int start, int length, int jobGroup, String executorHandler, String filterTime) {

		// page list
		PageHelper.startPage(start/length+1, length);
		List<XxlJobInfo> list = xxlJobInfoDao.pageList(start, length, jobGroup, executorHandler);
		int list_count = xxlJobInfoDao.pageListCount(start, length, jobGroup, executorHandler);

		// fill job info
		if (list!=null && list.size()>0) {
			for (XxlJobInfo jobInfo : list) {
				if (WriteTypeEnum.match(jobInfo.getWriteType()) == WriteTypeEnum.AUTO) {
					XxlJobDynamicScheduler.fillJobInfo(jobInfo);
				}
			}
		}

		// package result
		Map<String, Object> maps = new HashMap<String, Object>();
	    maps.put("recordsTotal", list_count);		// 总记录数
	    maps.put("recordsFiltered", list_count);	// 过滤后的总记录数
	    maps.put("data", list);  					// 分页列表
		return maps;
	}





	@Override
	public ReturnT<String> add(XxlJobInfo jobInfo) {

		// valid
		XxlJobGroup group = xxlJobGroupDao.load(jobInfo.getJobGroup());
		if (group == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "请选择“执行器”");
		}
		if (!CronExpression.isValidExpression(jobInfo.getJobCron())) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入格式正确的“Cron”");
		}
		if (StringUtils.isBlank(jobInfo.getJobDesc())) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入“任务描述”");
		}
//		if (StringUtils.isBlank(jobInfo.getAuthor())) {
//			return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入“负责人”");
//		}
		if (ExecutorRouteStrategyEnum.match(jobInfo.getExecutorRouteStrategy(), null) == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "路由策略非法");
		}
		if (ExecutorBlockStrategyEnum.match(jobInfo.getExecutorBlockStrategy(), null) == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "阻塞处理策略非法");
		}
		if (ExecutorFailStrategyEnum.match(jobInfo.getExecutorFailStrategy(), null) == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "失败处理策略非法");
		}
		if (GlueTypeEnum.match(jobInfo.getGlueType()) == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "运行模式非法非法");
		}
		if (GlueTypeEnum.BEAN==GlueTypeEnum.match(jobInfo.getGlueType()) && StringUtils.isBlank(jobInfo.getExecutorHandler())) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入“JobHandler”");
		}

		// fix "\r" in shell
		//		if (GlueTypeEnum.GLUE_SHELL==GlueTypeEnum.match(jobInfo.getGlueType()) && jobInfo.getGlueSource()!=null) {
		//			jobInfo.setGlueSource(jobInfo.getGlueSource().replaceAll("\r", ""));
		//		}

		// childJobKey valid
		//		if (StringUtils.isNotBlank(jobInfo.getChildJobKey())) {
		//			String[] childJobKeys = jobInfo.getChildJobKey().split(",");
		//			for (String childJobKeyItem: childJobKeys) {
		//				String[] childJobKeyArr = childJobKeyItem.split("_");
		//				if (childJobKeyArr.length!=2) {
		//					return new ReturnT<String>(ReturnT.FAIL_CODE, MessageFormat.format("子任务Key({0})格式错误", childJobKeyItem));
		//				}
		//				XxlJobInfo childJobInfo = xxlJobInfoDao.loadById(Integer.valueOf(childJobKeyArr[1]));
		//				if (childJobInfo==null) {
		//					return new ReturnT<String>(ReturnT.FAIL_CODE, MessageFormat.format("子任务Key({0})无效", childJobKeyItem));
		//				}
		//			}
		//		}

		Integer maxId = xxlJobInfoDao.getMaxId();
		jobInfo.setId(maxId+1);

		//jobInfo.setExecutorParam(jobInfo.getId()+","+jobInfo.getExecutorParam());


		// add in db
		if (xxlJobInfoDao.save(jobInfo) < 1) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "新增任务失败");
		}

		// add in quartz
		//TODO 手动管理不自动放入
		if(WriteTypeEnum.match(jobInfo.getWriteType()) == WriteTypeEnum.AUTO){
			String qz_group = String.valueOf(jobInfo.getJobGroup());
			String qz_name = String.valueOf(jobInfo.getId());
			try {
				XxlJobDynamicScheduler.addJob(qz_name, qz_group, jobInfo.getJobCron());
				return ReturnT.SUCCESS;
			} catch (SchedulerException e) {
				logger.error("", e);
				try {
					xxlJobInfoDao.delete(jobInfo.getId());
					XxlJobDynamicScheduler.removeJob(qz_name, qz_group);
				} catch (SchedulerException e1) {
					logger.error("", e1);
				}
				return new ReturnT<String>(ReturnT.FAIL_CODE, "新增任务失败:" + e.getMessage());
			}
		}else{
			return ReturnT.SUCCESS;
		}

	}

	@Override
	public ReturnT<String> reschedule(XxlJobInfo jobInfo) {

		// valid
		if (!CronExpression.isValidExpression(jobInfo.getJobCron())) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入格式正确的“Cron”");
		}
		if (StringUtils.isBlank(jobInfo.getJobDesc())) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入“任务描述”");
		}
		if (ExecutorRouteStrategyEnum.match(jobInfo.getExecutorRouteStrategy(), null) == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "路由策略非法");
		}
		if (ExecutorBlockStrategyEnum.match(jobInfo.getExecutorBlockStrategy(), null) == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "阻塞处理策略非法");
		}
		if (ExecutorFailStrategyEnum.match(jobInfo.getExecutorFailStrategy(), null) == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "失败处理策略非法");
		}

		// childJobKey valid
		//		if (StringUtils.isNotBlank(jobInfo.getChildJobKey())) {
		//			String[] childJobKeys = jobInfo.getChildJobKey().split(",");
		//			for (String childJobKeyItem: childJobKeys) {
		//				String[] childJobKeyArr = childJobKeyItem.split("_");
		//				if (childJobKeyArr.length!=2) {
		//					return new ReturnT<String>(ReturnT.FAIL_CODE, MessageFormat.format("子任务Key({0})格式错误", childJobKeyItem));
		//				}
		//                XxlJobInfo childJobInfo = xxlJobInfoDao.loadById(Integer.valueOf(childJobKeyArr[1]));
		//				if (childJobInfo==null) {
		//					return new ReturnT<String>(ReturnT.FAIL_CODE, MessageFormat.format("子任务Key({0})无效", childJobKeyItem));
		//				}
		//			}
		//		}

		// stage job info
		XxlJobInfo exists_jobInfo = xxlJobInfoDao.loadById(jobInfo.getId());
		if (exists_jobInfo == null) {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "参数异常");
		}
		//String old_cron = exists_jobInfo.getJobCron();

		String writeType_old = exists_jobInfo.getWriteType();

		exists_jobInfo.setJobCron(jobInfo.getJobCron());
		exists_jobInfo.setJobDesc(jobInfo.getJobDesc());
		exists_jobInfo.setAuthor(jobInfo.getAuthor());
		exists_jobInfo.setAlarmEmail(jobInfo.getAlarmEmail());
		exists_jobInfo.setExecutorRouteStrategy(jobInfo.getExecutorRouteStrategy());
		exists_jobInfo.setExecutorHandler(jobInfo.getExecutorHandler());
		exists_jobInfo.setExecutorParam(jobInfo.getExecutorParam());
		exists_jobInfo.setExecutorBlockStrategy(jobInfo.getExecutorBlockStrategy());
		exists_jobInfo.setExecutorFailStrategy(jobInfo.getExecutorFailStrategy());
//		exists_jobInfo.setChildJobKey(jobInfo.getChildJobKey());
		exists_jobInfo.setWriteType(jobInfo.getWriteType());
        xxlJobInfoDao.update(exists_jobInfo);

		// fresh quartz

		//TODO 手动管理不自动放入
		String qz_group = String.valueOf(exists_jobInfo.getJobGroup());
		String qz_name = String.valueOf(exists_jobInfo.getId());

		if(WriteTypeEnum.match(writeType_old) == WriteTypeEnum.AUTO){ //开始是定时任务,那么如果改成日终任务，那么从队列中移除，如果继续那修改队列任务
			if(WriteTypeEnum.match(jobInfo.getWriteType()) == WriteTypeEnum.AUTO) {
				try {
					boolean ret = XxlJobDynamicScheduler.rescheduleJob(qz_group, qz_name, exists_jobInfo.getJobCron());
					return ret ? ReturnT.SUCCESS : new ReturnT<String>(ReturnT.FAIL_CODE, "放入定时任务队列中出现异常");
				} catch (SchedulerException e) {
					logger.error(e.getMessage(), e);
				}
			}else {
				try {
					boolean ret = XxlJobDynamicScheduler.removeJob(qz_name, qz_group);
					return ret ? ReturnT.SUCCESS : new ReturnT<String>(ReturnT.FAIL_CODE, "移出定时任务队列中出现异常");
				} catch (SchedulerException e) {
					e.printStackTrace();
				}
			}
		}else {//开始是日终任务,那么如果改成定时任务，那么需要新加入队列
			if(WriteTypeEnum.match(jobInfo.getWriteType()) == WriteTypeEnum.AUTO) {
				try {

					boolean ret = XxlJobDynamicScheduler.addJob(qz_name, qz_group, exists_jobInfo.getJobCron());
					return ret ? ReturnT.SUCCESS : new ReturnT<String>(ReturnT.FAIL_CODE, "放入定时任务队列中出现异常");
				} catch (SchedulerException e) {
					logger.error(e.getMessage(), e);
				}
			}
		}

		return ReturnT.SUCCESS;
	}

	@Override
	public ReturnT<String> remove(int id) {
		XxlJobInfo xxlJobInfo = xxlJobInfoDao.loadById(id);
        String group = String.valueOf(xxlJobInfo.getJobGroup());
        String name = String.valueOf(xxlJobInfo.getId());

		try {
			XxlJobDynamicScheduler.removeJob(name, group);
			xxlJobInfoDao.delete(id);
			xxlJobLogDao.delete(id);
//			xxlJobLogGlueDao.deleteByJobId(id);
			jobParamDao.deleteParam(id);
			return ReturnT.SUCCESS;
		} catch (SchedulerException e) {
			logger.error(e.getMessage(), e);
		}
		return ReturnT.FAIL;
	}

	@Override
	public ReturnT<String> pause(int id) {
        XxlJobInfo xxlJobInfo = xxlJobInfoDao.loadById(id);
        String group = String.valueOf(xxlJobInfo.getJobGroup());
        String name = String.valueOf(xxlJobInfo.getId());

		try {
            boolean ret = XxlJobDynamicScheduler.pauseJob(name, group);	// jobStatus do not store
            return ret?ReturnT.SUCCESS:ReturnT.FAIL;
		} catch (SchedulerException e) {
			logger.error(e.getMessage(), e);
			return ReturnT.FAIL;
		}
	}

	@Override
	public ReturnT<String> resume(int id) {
        XxlJobInfo xxlJobInfo = xxlJobInfoDao.loadById(id);
        String group = String.valueOf(xxlJobInfo.getJobGroup());
        String name = String.valueOf(xxlJobInfo.getId());

		try {
			boolean ret = XxlJobDynamicScheduler.resumeJob(name, group);
			return ret?ReturnT.SUCCESS:ReturnT.FAIL;
		} catch (SchedulerException e) {
			logger.error(e.getMessage(), e);
			return ReturnT.FAIL;
		}
	}

	@Override
	public ReturnT<String> triggerJob(int id) {
        XxlJobInfo xxlJobInfo = xxlJobInfoDao.loadById(id);
        if (xxlJobInfo == null) {
        	return new ReturnT<String>(ReturnT.FAIL_CODE, "任务ID非法");
		}

		if(WriteTypeEnum.match(xxlJobInfo.getWriteType()) == WriteTypeEnum.REACTIVE){
			return XxlJobTrigger.trigger(id,null);
		}


		String group = String.valueOf(xxlJobInfo.getJobGroup());
        String name = String.valueOf(xxlJobInfo.getId());

		try {

			if(!XxlJobDynamicScheduler.checkExists(name, group)){
				 boolean ret = XxlJobDynamicScheduler.addJob(name, group, xxlJobInfo.getJobCron());
				 if(!ret){
						return new ReturnT<String>(ReturnT.FAIL_CODE, "放入定时队列失败,执行队列可能已经存在该任务编号");
				 }
			}

			if(XxlJobDynamicScheduler.triggerJob(name, group))
			return ReturnT.SUCCESS;
			else
			return new ReturnT<String>(ReturnT.FAIL_CODE, "放入执行队列失败,执行队列可能已经存在该任务编号");
		} catch (SchedulerException e) {
			logger.error(e.getMessage(), e);
			return new ReturnT<String>(ReturnT.FAIL_CODE, e.getMessage());
		}
	}

	@Override
	public Map<String, Object> dashboardInfo() {

		int jobInfoCount = xxlJobInfoDao.findAllCount();
		int jobLogCount = xxlJobLogDao.triggerCountByHandleCode(-1);
		int jobLogSuccessCount = xxlJobLogDao.triggerCountByHandleCode(ReturnT.SUCCESS_CODE);

		// executor count
		Set<String> executerAddressSet = new HashSet<String>();
		List<XxlJobGroup> groupList = xxlJobGroupDao.findAll();

		if (CollectionUtils.isNotEmpty(groupList)) {
			for (XxlJobGroup group: groupList) {
				if (CollectionUtils.isNotEmpty(group.getRegistryList())) {
					executerAddressSet.addAll(group.getRegistryList());
				}
			}
		}

		int executorCount = executerAddressSet.size();

		Map<String, Object> dashboardMap = new HashMap<String, Object>();
		dashboardMap.put("jobInfoCount", jobInfoCount);
		dashboardMap.put("jobLogCount", jobLogCount);
		dashboardMap.put("jobLogSuccessCount", jobLogSuccessCount);
		dashboardMap.put("executorCount", executorCount);
		return dashboardMap;
	}

	@Override
	public ReturnT<Map<String, Object>> triggerChartDate() {
		Date from = DateUtils.addDays(new Date(), -30);
		Date to = new Date();

		List<String> triggerDayList = new ArrayList<String>();
		List<Integer> triggerDayCountSucList = new ArrayList<Integer>();
		List<Integer> triggerDayCountFailList = new ArrayList<Integer>();
		int triggerCountSucTotal = 0;
		int triggerCountFailTotal = 0;

		List<Map<String, Object>> triggerCountMapAll = xxlJobLogDao.triggerCountByDay(from, to, -1);
		List<Map<String, Object>> triggerCountMapSuc = xxlJobLogDao.triggerCountByDay(from, to, ReturnT.SUCCESS_CODE);
		if (CollectionUtils.isNotEmpty(triggerCountMapAll)) {
			for (Map<String, Object> item: triggerCountMapAll) {
				String day = String.valueOf(item.get("triggerday"));
				Object triggerCountObj = item.get("triggercount");
				int dayAllCount = (triggerCountObj != null) ? Integer.valueOf(String.valueOf(triggerCountObj)) : 0;
				int daySucCount = 0;
				int dayFailCount = dayAllCount - daySucCount;

				if (CollectionUtils.isNotEmpty(triggerCountMapSuc)) {
					for (Map<String, Object> sucItem: triggerCountMapSuc) {
						String daySuc = String.valueOf(sucItem.get("triggerday"));
						if (day.equals(daySuc)) {
							Object sucTriggerCountObj = sucItem.get("triggercount");
							daySucCount = (sucTriggerCountObj != null) ? Integer.valueOf(String.valueOf(sucTriggerCountObj)) : 0;
							dayFailCount = dayAllCount - daySucCount;
						}
					}
				}

				triggerDayList.add(day);
				triggerDayCountSucList.add(daySucCount);
				triggerDayCountFailList.add(dayFailCount);
				triggerCountSucTotal += daySucCount;
				triggerCountFailTotal += dayFailCount;
			}
		} else {
			for (int i = 4; i > -1; i--) {
				triggerDayList.add(FastDateFormat.getInstance("yyyy-MM-dd").format(DateUtils.addDays(new Date(), -i)));
				triggerDayCountSucList.add(0);
				triggerDayCountFailList.add(0);
			}
		}

		Map<String, Object> result = new HashMap<String, Object>();
		result.put("triggerDayList", triggerDayList);
		result.put("triggerDayCountSucList", triggerDayCountSucList);
		result.put("triggerDayCountFailList", triggerDayCountFailList);
		result.put("triggerCountSucTotal", triggerCountSucTotal);
		result.put("triggerCountFailTotal", triggerCountFailTotal);
		return new ReturnT<Map<String, Object>>(result);
	}


	private static final String TRIGGER_CHART_DATA_CACHE = "trigger_chart_data_cache";
	public ReturnT<Map<String, Object>> chartInfo(Date startDate, Date endDate) {
		// get cache
		String cacheKey = TRIGGER_CHART_DATA_CACHE + "_" + startDate.getTime() + "_" + endDate.getTime();
		Map<String, Object> chartInfo = (Map<String, Object>) LocalCacheUtil.get(cacheKey);
		if (chartInfo != null) {
			return new ReturnT<Map<String, Object>>(chartInfo);
		}

		// process
		List<String> triggerDayList = new ArrayList<String>();
		List<Integer> triggerDayCountRunningList = new ArrayList<Integer>();
		List<Integer> triggerDayCountSucList = new ArrayList<Integer>();
		List<Integer> triggerDayCountFailList = new ArrayList<Integer>();
		int triggerCountRunningTotal = 0;
		int triggerCountSucTotal = 0;
		int triggerCountFailTotal = 0;

		List<Map<String, Object>> triggerCountMapAll = xxlJobLogDao.triggerCountByDayN(startDate, endDate);
		if (CollectionUtils.isNotEmpty(triggerCountMapAll)) {
			for (Map<String, Object> item: triggerCountMapAll) {
				String day = String.valueOf(item.get("triggerDay"));
				int triggerDayCount = Integer.valueOf(String.valueOf(item.get("triggerDayCount")));
				int triggerDayCountRunning = Integer.valueOf(String.valueOf(item.get("triggerDayCountRunning")));
				int triggerDayCountSuc = Integer.valueOf(String.valueOf(item.get("triggerDayCountSuc")));
				int triggerDayCountFail = triggerDayCount - triggerDayCountRunning - triggerDayCountSuc;

				triggerDayList.add(day);
				triggerDayCountRunningList.add(triggerDayCountRunning);
				triggerDayCountSucList.add(triggerDayCountSuc);
				triggerDayCountFailList.add(triggerDayCountFail);

				triggerCountRunningTotal += triggerDayCountRunning;
				triggerCountSucTotal += triggerDayCountSuc;
				triggerCountFailTotal += triggerDayCountFail;
			}
		} else {
			for (int i = 4; i > -1; i--) {
				triggerDayList.add(org.apache.commons.lang3.time.FastDateFormat.getInstance("yyyy-MM-dd").format(org.apache.commons.lang3.time.DateUtils.addDays(new Date(), -i)));
				triggerDayCountSucList.add(0);
				triggerDayCountFailList.add(0);
			}
		}

		Map<String, Object> result = new HashMap<String, Object>();
		result.put("triggerDayList", triggerDayList);
		result.put("triggerDayCountRunningList", triggerDayCountRunningList);
		result.put("triggerDayCountSucList", triggerDayCountSucList);
		result.put("triggerDayCountFailList", triggerDayCountFailList);

		result.put("triggerCountRunningTotal", triggerCountRunningTotal);
		result.put("triggerCountSucTotal", triggerCountSucTotal);
		result.put("triggerCountFailTotal", triggerCountFailTotal);

		// set cache
		LocalCacheUtil.set(cacheKey, result, 60*1000);     // cache 60s

		return new ReturnT<Map<String, Object>>(result);
	}


	@Override
	public List<XxlJobInfo> getAllJobInfos() {
		return xxlJobInfoDao.getAllJobInfos();
	}

	@Override
	public XxlJobInfo loadById(int id) {
		// TODO Auto-generated method stub
		return xxlJobInfoDao.loadById(id);
	}

    /**
     * 移除定时队列不删除
     * @param id
     * @return
     */
	public ReturnT<String> remjob(int id) {

		XxlJobInfo xxlJobInfo = xxlJobInfoDao.loadById(id);
		String group = String.valueOf(xxlJobInfo.getJobGroup());
		String name = String.valueOf(xxlJobInfo.getId());
        try {
            boolean ret = XxlJobDynamicScheduler.removeJob(name, group);
            return ret?ReturnT.SUCCESS:ReturnT.FAIL;
        } catch (SchedulerException e) {
            logger.error(e.getMessage(), e);
            return ReturnT.FAIL;
        }
	}

    /**
     * 将任务加入定时任务执行队列中
     * @param id
     * @return
     */
    public ReturnT<String> addjob(int id) {
        XxlJobInfo xxlJobInfo = xxlJobInfoDao.loadById(id);
        String group = String.valueOf(xxlJobInfo.getJobGroup());
        String name = String.valueOf(xxlJobInfo.getId());
        try {
            boolean ret = XxlJobDynamicScheduler.addJob(name, group,xxlJobInfo.getJobCron());
            return ret?ReturnT.SUCCESS:ReturnT.FAIL;
        } catch (SchedulerException e) {
            logger.error(e.getMessage(), e);
            return ReturnT.FAIL;
        }
    }

}
