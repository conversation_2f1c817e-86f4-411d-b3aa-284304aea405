package com.sunyard;

import com.sunyard.console.common.util.ServiceLocator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
//import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ImportResource;




@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ImportResource(value = {"classpath:conf/applicationContext.xml"})
@ServletComponentScan
public class SunEcmConsoleApplication {

    public SunEcmConsoleApplication(ServiceLocator serviceLocator){}

    public static void main(String[] args) {
        SpringApplication.run(SunEcmConsoleApplication.class, args);
    }

}
