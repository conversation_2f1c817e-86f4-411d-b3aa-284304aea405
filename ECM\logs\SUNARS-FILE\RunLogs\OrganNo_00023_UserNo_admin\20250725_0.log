2025-07-25 01:09:53.176 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] DEBUG c.s.cop.IF.controller.BaseController - 前台发送数据(脱敏前-生产INFO级别不输出):  "{\"parameterList\":[{}],\"sysMap\":{\"batchId\":\"********150114621932\",\"inputDate\":\"********\",\"fileName\":\"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg\",\"siteNo\":\"\",\"contentId\":\"\",\"module\":\"ars-img\"}}"
2025-07-25 01:09:53.188 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"batchId":"********150114621932",
		"inputDate":"********",
		"fileName":"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg",
		"siteNo":"",
		"contentId":"",
		"module":"ars-img"
	}
}
2025-07-25 01:09:53.189 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] DEBUG c.b.d.d.DynamicRoutingDataSource - dynamic-datasource switch to the primary datasource
2025-07-25 01:09:53.190 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - [showImg]：batchId:********150114621932,contentId:,fileName:00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg
2025-07-25 01:09:53.191 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectDataNps - ==>  Preparing: SELECT SERIAL_NO, BATCH_ID, INCCODEIN_BATCH, COPY_INCCODEIN, COPY_REC, IS_FRONT_PAGE, PS_LEVEL, PRIMARY_INCCODEIN, FORM_NAME, FORM_GROUP, VOUH_TYPE, CHECK_FLAG, ERROR_FLAG, IS_AUDIT, SELF_DELETE, MEMO, PROCESS_STATE, REC_DATE, REC_FAIL_CAUSE, FLOW_ID, IMAGE_SIZE, BACK_IMAGE_SIZE, PATCH_FLAG, FILE_NAME, BACK_FILE_NAME, OCR_WORKER, OCR_DATE, OCR_TIME, COPY_SERIALNO, IS_SIGN, SUPERVISE_DEAL, CONTENT_ID, DATA_SOURCE_ID, VOUCHER_NUMBER, FORM_ID,INSERT_DATE FROM BP_TMPDATA_1_TB WHERE BATCH_ID = ? AND (FILE_NAME = ? or BACK_FILE_NAME = ?) ORDER BY INCCODEIN_BATCH
2025-07-25 01:09:53.191 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectDataNps - ==> Parameters: ********150114621932(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String), 00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg(String)
2025-07-25 01:09:53.193 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectDataNps - <==      Total: 1
2025-07-25 01:09:53.205 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==>  Preparing: select BATCH_ID, BATCH_LOCK, BATCH_TOTAL_PAGE, BUSINESS_ID, BATCH_COMMIT, INPUT_DATE, TEMP_DATA_TABLE, INPUT_WORKER, MACHINE_ID, NEED_PROCESS, OCCUR_DATE, OPERATOR_NO, PROGRESS_FLAG, SITE_NO, INPUT_TIME, IMAGE_STATUS, IS_INVALID, PRIORITY_LEVEL, WORKER, WORK_TIME,PROCINSTID,OCR_FACTOR_FLAG,AREA_CODE,CONTENT_ID, REVIEW_REMARK, REVIEW_BY, REVIEW_TIME from BP_TMPBATCH_TB where BATCH_ID = ?
2025-07-25 01:09:53.205 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectByPrimaryKey - ==> Parameters: ********150114621932(String)
2025-07-25 01:09:53.208 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.a.c.d.T.selectByPrimaryKey - <==      Total: 1
2025-07-25 01:09:53.217 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询
2025-07-25 01:09:53.217 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-25 01:09:53.218 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-25 01:09:53.219 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时发送的消息：0002<<::>>OPTION=HEIGHT_QUERY,XML=<HeightQuery IS_UNITED_ACCESS="false" USER_NAME="admin" COUNT="0" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt></HeightQuery>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-25 01:09:53.311 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>
2025-07-25 01:09:53.312 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-25 01:09:53.312 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --heightQuery-->高级查询(over)
2025-07-25 01:09:53.312 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.ars.common.util.ECMUtil - #######调用高级搜索返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><HeightQuery USER_NAME="admin" COUNT="1" LIMIT="10" PAGE="1" MODEL_CODE="XCMS" PASSWORD="MzMz">  <customAtt>    <CREATEDATE><string>********</string></CREATEDATE>    <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>  </customAtt>  <groupId>1</groupId>  <indexBeans>    <BatchIndexBean CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" SERVER_IP="************" MIGRATION_STATUS="1" SOCKET_PORT="8023" HTTP_PORT="8083" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">      <customMap>        <NEAR_PATH><string>202507/791/</string></NEAR_PATH>        <CREATEDATE><string>********</string></CREATEDATE>        <OFFLINE_PATH><string></string></OFFLINE_PATH>        <TAG_INFO><string>null</string></TAG_INFO>        <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>        <TAG_DBTIME><string></string></TAG_DBTIME>      </customMap>    </BatchIndexBean>  </indexBeans></HeightQuery></root>]#######
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --connectToHost-->建立socket连接-->host: ************port: 8023
2025-07-25 01:09:53.314 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --newHost-->建立连接-->ip: ************ socketPort:8023
2025-07-25 01:09:53.315 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时发送的消息：0002<<::>>OPTION=QUERY,XML=<BatchBean IS_UNITED_ACCESS="false" MODEL_CODE="XCMS" USER="admin" IS_BREAK_POINT="false" IS_OWN_MD5="false" IS_DOWNLOAD="false" PASSWORD="MzMz" PASSWD="MzMz">  <index_Object CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" VERSION="0">    <customMap>      <CREATEDATE><string>********</string></CREATEDATE>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <FILTERS>        <entry>          <string>TRUENAME</string>          <string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string>        </entry>      </FILTERS>    </BatchFileBean>  </document_Objects></BatchBean>,DMSNAME=null,USERNAME=admin,PASSWORD=MzMz
2025-07-25 01:09:53.351 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询时返回的消息：0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jjIwfylNB4FfO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>
2025-07-25 01:09:53.351 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.client.conn.SocketConn - 关闭SOCKET连接
2025-07-25 01:09:53.352 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] DEBUG c.s.c.impl.SunEcmClientSocketApiImpl - --queryBatch-->查询(over)
2025-07-25 01:09:53.352 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  com.sunyard.ars.common.util.ECMUtil - #######查询批次返回的信息[0001<<::>><?xml version="1.0" encoding="UTF-8"?><root><BatchBean MODEL_CODE="XCMS" IS_UNITED_ACCESS="false" IS_SUNECM_COSOLE="false" IS_BREAK_POINT="false" IS_DOWNLOAD="false">  <index_Object CONTENT_STATUS="1" SERVER_ID="1" UPLOAD_USER="admin" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" MIGRATION_STATUS="1" VERSION="1" MAX_VERSION="1" GROUP_ID="1" GROUP_NAME="XCGROUP" IS_LAST_VERSION="1">    <customMap>      <NEAR_PATH><string>202507/791/</string></NEAR_PATH>      <CREATEDATE><string>********</string></CREATEDATE>      <OFFLINE_PATH><string></string></OFFLINE_PATH>      <TAG_INFO><string>null</string></TAG_INFO>      <BUSI_SERIAL_NO><string>********150114621932</string></BUSI_SERIAL_NO>      <TAG_DBTIME><string></string></TAG_DBTIME>    </customMap>  </index_Object>  <document_Objects>    <BatchFileBean FILE_PART_NAME="XCMS_PART">      <files>        <FileBean VERSION="1" UPLOAD_TIME="********150307" FILE_FORMAT="jpg" FILE_NO="2FD867D3-63BE-9745-72CB-8A15A47B425A" SAVE_NAME="2FD867D3-63BE-9745-72CB-8A15A47B425A" CONTENT_ID="202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1" FILE_STATUS="1" FILE_NAME="/home/<USER>/202507/791/2FD867D3-63BE-9745-72CB-8A15A47B425A" OPTION_TYPE="1" FILE_SIZE="865019" SERVER_ID="1" GROUP_ID="1" URL="http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jjIwfylNB4FfO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60=" FILE_PATH="202507/791/" VOLUME_ID="1" ENCODESIZE="">          <otherAtt>            <NEAR_PATH><string></string></NEAR_PATH>            <RESEVER><string></string></RESEVER>            <FILEFORM><string>KJ_IMAGE</string></FILEFORM>            <TRUENAME><string>00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg</string></TRUENAME>            <FILEMD5><string>F8DC6724FA55CC0552AC76D3E07F1DBF                                </string></FILEMD5>            <CREATEDATE><string></string></CREATEDATE>            <SHOWNAME><string>00004_A</string></SHOWNAME>            <FILEATTR><string>1   </string></FILEATTR>            <OFFLINE_PATH><string></string></OFFLINE_PATH>            <FILEFACE><string>1</string></FILEFACE>            <TAG_INFO><string></string></TAG_INFO>            <TAG_DBTIME><string></string></TAG_DBTIME>          </otherAtt>        </FileBean>      </files>    </BatchFileBean>  </document_Objects></BatchBean></root>]#######
2025-07-25 01:09:53.354 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/795de0882a94b7da] [http-nio-9110-exec-7] INFO  c.s.a.s.s.i.i.ARSImageServiceImpl - 成功获取到url
2025-07-25 01:09:53.369 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"url":"http://************:8083/SunECMDM/servlet/getFile?s/lV3bcibQhA2EXkqVcyNJjMHAqbfUEI81Gz2PYq1t69YXuGpY1K4nAYpKCt6vO4shKMfVUeZ1JX3p/npEnQrb912qgvxQQCrM3p7PBTKqkpHfCJ147HL+K8ND6QQgaTxY2Ca94wx719aHxidh1VFPa1eBCcyr6jjIwfylNB4FfO0QF/92KsCXVTQlnkDtPzIOYARy9+Iofnz+bi4T7WKoIUY40i5bbC6nRD1UTomugL56zd5FVuf2JGwC3OYVf9C+vC8bwZJLWrRjnsLPiEIo2YMLgH7V0x7yXAROtRWSQAQSWwq/J55Z2NAbACYC+AuMvBGOy1Z60="
	},
	"retMsg":"查询影像信息成功"
}
2025-07-25 01:09:53.369 [OrganNo_00023_UserNo_admin] [6b66beb9aa3cc2c3/6a561c2515d1b21f] [http-nio-9110-exec-7] DEBUG c.s.cop.IF.controller.BaseController - 请求编号：20250725010953168040，清空日志输出文件标识：OrganNo_00023_UserNo_admin
