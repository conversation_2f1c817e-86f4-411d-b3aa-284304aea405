package com.sunyard.console.safemanage.bean;
/**
 * <p>Title: 用户信息bean</p>
 * <p>Description: 存放用户信息</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class UserInfoBean implements java.io.Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public final static int UPDATE_PASSWORD_SUCCESS = 0;
	public final static int OLD_PASSWORD_FAIL = 1; //旧密码错误
	public final static int UPDATE_PASSWORD_FAIL = 2;
	public final static int WEAK_PASSWORD_FAIL = 3; //弱密码
	public final static int UPDATE_USER_FAIL = 4; //用户名错误，或用户被禁用
	private String login_id;			//登陆名
	private String user_name;			//用户姓名
	private String user_password;       //用户密码
	private String user_post;			//职位
	private String user_department;		//所属部门
	private String user_state;			//状态
	private String ldap_code;			//ldap代码
	private String user_city;			//所在城市
	private String password;
	private String psw_mdf_date;    //本次密码修改时间 
	private String token;
	
	
	
	public String getToken() {
		return token;
	}
	public void setToken(String token) {
		this.token = token;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getLogin_id() {
		return login_id;
	}
	public void setLogin_id(String login_id) {
		this.login_id = login_id;
	}
	public String getUser_name() {
		return user_name;
	}
	public String getPsw_mdf_date() {
		return psw_mdf_date;
	}
	public void setPsw_mdf_date(String pswMdfDate) {
		psw_mdf_date = pswMdfDate;
	}
	public void setUser_name(String user_name) {
		this.user_name = user_name;
	}
	public String getUser_post() {
		return user_post;
	}
	public void setUser_post(String user_post) {
		this.user_post = user_post;
	}
	public String getUser_department() {
		return user_department;
	}
	public void setUser_department(String user_department) {
		this.user_department = user_department;
	}
	public String getUser_state() {
		return user_state;
	}
	public void setUser_state(String user_state) {
		this.user_state = user_state;
	}
	public String getLdap_code() {
		return ldap_code;
	}
	public void setLdap_code(String ldap_code) {
		this.ldap_code = ldap_code;
	}
	public String getUser_city() {
		return user_city;
	}
	public void setUser_city(String user_city) {
		this.user_city = user_city;
	}
	public String getUser_password() {
		return user_password;
	}
	public void setUser_password(String user_password) {
		this.user_password = user_password;
	}
	public String toString(){
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append("login_id:").append(login_id);
		sBuilder.append(";user_name:").append(user_name);
		sBuilder.append(";user_password:").append(user_password);
		sBuilder.append(";user_post:").append(user_post);
		sBuilder.append(";user_department:").append(user_department);
		sBuilder.append(";user_state:").append(user_state);
		sBuilder.append(";ldap_code:").append(ldap_code);
		sBuilder.append(";user_city:").append(user_city);
		sBuilder.append(";password:").append(password);
		return sBuilder.toString();
	}
}

