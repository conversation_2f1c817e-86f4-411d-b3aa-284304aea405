package com.sunyard.client.impl;

import java.io.File;
import java.io.IOException;
import java.net.Socket;
import java.security.SecureRandom;
import java.util.*;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocket;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;

import com.sunyard.ecm.server.bean.MigrateBatchBean;

import com.sunyard.util.*;
import com.sunyard.ws.client.WSConsoleClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.SunEcmClientApi;
import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientBatchFileBean;
import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.client.bean.ClientHeightQuery;
import com.sunyard.client.bean.ClientTagBean;
import com.sunyard.client.common.ClientConfiguration;
import com.sunyard.client.conn.SocketConn;
import com.sunyard.ecm.util.net.DummyTrustManager;
import com.sunyard.es.util.EsBean;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.ws.internalapi.SunEcmConsole;
import com.sunyard.ws.utils.XMLUtil;

/**
 * 客户端实现类
 * 
 * <AUTHOR>
 * 
 */
public class SunEcmClientSocketApiImpl extends AbstractSunECMClientApi implements SunEcmClientApi {
	private final static  Logger log = LoggerFactory.getLogger(SunEcmClientSocketApiImpl.class);
	protected SocketConn conn;
	protected String splitSym = TransOptionKey.SPLITSYM;
	protected String ipAddress;
	protected int socketPort;
	private boolean ssl = false;

	static {
		// 初始化客户端配置信息
		ClientConfiguration.init();
	}

	public SunEcmClientSocketApiImpl(String ip, int socketPort) {
		this.ipAddress = ip;
		this.socketPort = socketPort;
	}

	public SunEcmClientSocketApiImpl(String ip, int socketPort, boolean ssl) {
		this.ipAddress = ip;
		this.socketPort = socketPort;
		this.ssl = ssl;
	}

	/**
	 * 建立socket
	 * 
	 * @param host
	 *            主机IP
	 * @param port
	 *            主机端口
	 * @return 成功/失败
	 */
	protected boolean connectToHost(String host, int port) throws SunECMException {
		log.debug("--connectToHost-->建立socket连接-->host: " + host + "port: " + port);
		int connectretrytimes = ClientConfiguration.getInt("client.socket.retrytimes", 5);
		// 重复连接CONNECTRETRYTIMES
		for (int i = 0; i < connectretrytimes; i++) {
			if (newHost(host, port)) {
				return true;
			}
			// 等待200毫秒再下一次连接
			try {
				Thread.sleep(200);
			} catch (InterruptedException e) {
				log.debug("--connectToHost-->建立socket连接尝试连接次数：" + (i + 1));
			}
			if (i == connectretrytimes - 1) {
				log.warn("连接主机：" + host + " 端口:" + port + "失败");
				throw new SunECMException(SunECMExceptionStatus.CLIENT_CONNECT_TO_SERVER_ERROR, "连接主机：" + host + " 端口:"
						+ port + "失败");
			}
		}
		return false;
	}

	/**
	 * 建立连接
	 * 
	 * @param ip
	 * @param socketPort
	 * @return
	 */
	private boolean newHost(String ip, int socketPort) {
		log.debug("--newHost-->建立连接-->ip: " + ip + " socketPort:" + socketPort);
		Socket socket = null;
		try {
			if (ssl) {
				SSLContext context = SSLContext.getInstance("SSL");
				// 初始化
				context.init(null, new TrustManager[] { new DummyTrustManager() }, new SecureRandom());
				SSLSocketFactory factory = context.getSocketFactory();
				socket = (SSLSocket) factory.createSocket(ip, socketPort);
			} else {
				socket = new Socket(ip, socketPort);
			}

			socket.setSendBufferSize(ClientConfiguration.getInt("client.socket.sendbuffersize", 8192));
			this.conn = new SocketConn(socket);
		}  catch (Exception e) {
			log.error("error",e);
			return false;
		}
		return true;
	}

	public String checkIn(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		log.debug("--checkIn-->检入");
		connectToHost(ipAddress, socketPort);
		String result = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.CHECKIN + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug("--checkIn-->检入时发送的信息：" + sendMsg);
			String msg = conn.receiveMsg();
			if (msg == null) {
				log.error("返回为 空");
				return "";
			}
			log.debug("--checkIn-->检入时返回的信息：" + msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = TransOptionKey.SUCCESS + splitSym;
				} else {
					result = TransOptionKey.FAIL;
				}
			} else {
				log.warn("--checkIn-->检入时发生异常...异常代码：" + msg);
				throw new SunECMException("--checkIn-->检入时发生异常...异常代码：" + msg);
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--checkIn-->检入-->result:" + result);
		return result;
	}

	public String checkOut(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		log.debug("---->checkOut-->检出");
		connectToHost(ipAddress, socketPort);
		String result = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg =TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION="+ OptionKey.CHECKOUT + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName ;
			sendMsg = createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug("--checkOut-->检出时发送的信息：" + sendMsg);
			String msg = conn.receiveMsg();
			if (msg == null) {
				log.error("返回为 空");
				return "";
			}
			log.debug("--checkOut-->检出时返回的信息：" + msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = TransOptionKey.SUCCESS + splitSym + strArray[2];
				} else {
					result = TransOptionKey.FAIL + splitSym + strArray[2];
				}
			} else {
				log.warn("--checkOut-->检出时发生异常...异常代码：" + msg);
				throw new SunECMException("--checkOut-->检出时发生异常...异常代码：" + msg);
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("---->checkOut-->检出(over)-->result:" + result);
		return result;
	}

	/**
	 * 申请批次号
	 * 
	 * @throwsSunECMException
	 * @throwsIOException
	 */
	public String createContentID(ClientBatchBean clientBatchBean, String groupname)
			throws SunECMException, IOException {
		log.debug("--createContentID-->申请批次号");
		connectToHost(ipAddress, socketPort);
		String result = "";
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.CREATEID + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",GROUPNAME=" + groupname;
			sendMsg = createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			String msg = conn.receiveMsg();
			if (msg == null) {
				log.error("返回为 空");
				return "";
			}
			log.debug("--createContentID-->申请批次号时返回的信息：" + msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = msg;
				} else {
					result = TransOptionKey.SERVER_OK + splitSym + TransOptionKey.FAIL + strArray[2];
				}
			} else {
				log.warn("--createContentID-->申请批次号时发生异常...异常代码：" + msg);
				throw new SunECMException("--createContentID-->申请批次号时发生异常...异常代码：" + msg);
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--createContentID-->申请批次号-->result:" + result);
		return result;
	}
	
	public String delete(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		log.debug("--delete-->删除");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.DEL + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName ;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug("--delete-->删除时发送的信息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--delete-->删除时返回的信息：" + resultStr);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--delete-->删除(over)");
		return resultStr;
	}

	public String login(String userName, String password) throws SunECMException, IOException {
		// 增加 对传入的参数进行加密 2013年9月18日
		password = CodeUtil.encode(password);
		log.debug("--login-->登录 userName:" + userName + "password:" + password);
		connectToHost(ipAddress, socketPort);
		String resultMsg = TransOptionKey.FAIL;
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append(TransOptionKey.MESSAGE_PROCESS).append(splitSym + "OPTION=" + OptionKey.LOGIN + ",USERNAME=")
					.append(userName).append(",PASSWORD=").append(password);
			conn.sendMsg(sbuf.toString());
			log.debug("--login-->登录时发送的消息：" + sbuf.toString());
			String msg = conn.receiveMsg();
			log.debug("--login-->登录时返回的信息：" + msg);

			// 服务端异常有可能返回NULL判断为空时提示客户端登陆失败
			if (msg == null || "null".equals(msg)) {
				throw new SunECMException("--login-->登陆失败");
			}

			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					resultMsg = TransOptionKey.SUCCESS;
				} else {
					log.warn("--login-->登陆失败");
					// throw new SunECMException("--login-->登陆失败");
				}
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--login-->登录(over)-->resultMsg:" + resultMsg);
		return resultMsg;
	}

	public String logout(String userName) throws SunECMException, IOException {
		log.debug("--logout-->登出  userName:" + userName);
		connectToHost(ipAddress, socketPort);
		String resultMsg = TransOptionKey.FAIL;
		try {
			StringBuffer sbuf = new StringBuffer();
			sbuf.append(TransOptionKey.MESSAGE_PROCESS).append(splitSym + "OPTION=" + OptionKey.LOGOUT + ",USERNAME=")
					.append(userName);
			conn.sendMsg(sbuf.toString());
			log.debug("--logout-->登出时发送的消息：" + sbuf.toString());
			String msg = conn.receiveMsg();
			if (msg == null) {
				log.error("返回为 空");
				return "";
			}
			log.debug("--logout-->登出时返回的消息：" + msg);
			log.debug(msg);
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					resultMsg = TransOptionKey.SUCCESS;
				} else {
					log.warn("--logout-->登出异常");
					throw new SunECMException("--logout-->登出异常");
				}
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--logout-->登出 (over)-->resultMsg:" + resultMsg);
		return resultMsg;
	}

	public String operAnnotation(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		log.debug("--operAnnotation-->批注操作");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		try {
			conn.sendMsg(TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.A_OR_U_ANNOTATION + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName);
			resultStr = conn.receiveMsg();
			log.debug("--operAnnotation-->批注操作时返回的消息：" + resultStr);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--operAnnotation-->批注操作(over)");
		return resultStr;
	}

	public String queryAnnotation(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		log.debug("--queryAnnotation-->批注查询");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.ANNOTATION + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			conn.sendMsg(sendMsg);

			resultStr = conn.receiveMsg();
			log.debug("--queryAnnotation-->批注查询时返回的消息：" + resultStr);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--queryAnnotation-->批注查询(over)");
		return resultStr;
	}

	public String queryBatch(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		log.debug("--queryBatch-->查询");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		CodeUtil.encodeInBean(clientBatchBean);
		clientBatchBean.setPASSWD(clientBatchBean.getPassWord());
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.QUERY + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName ;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug("--queryBatch-->查询时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--queryBatch-->查询时返回的消息：" + resultStr);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--queryBatch-->查询(over)");
		return resultStr;
	}

	public String update(ClientBatchBean clientBatchBean, String dmsName, boolean isAutoCheck) throws SunECMException,
			IOException {
		log.debug("--update-->更新");
		String resultStr = TransOptionKey.FAIL;
		if (!checkFileExist(clientBatchBean)) {
			return TransOptionKey.FAIL + TransOptionKey.SPLITSYM + SunECMExceptionStatus.FILE_NOT_FOUND; // 文件不存在
		}
		// 添加MD5码
		MD5Util.addBatchMD5Code(clientBatchBean);
		if (isAutoCheck) {
			// 保存之前的password
			String OldPassWord = clientBatchBean.getPassWord();

			String checkOutMsg = checkOut(clientBatchBean, dmsName);
			log.debug("--update-->检出时返回的消息: " + checkOutMsg);
			if (checkOutMsg.split(splitSym)[0].equals(TransOptionKey.SUCCESS)) {
				clientBatchBean.setCheckToken(checkOutMsg.split(splitSym)[1]);
				log.debug("--update-->批次[" + clientBatchBean.getIndex_Object().getContentID() + "]自动检出成功");
			} else {
				throw new SunECMException("--update-->批次[" + clientBatchBean.getIndex_Object().getContentID()
						+ "]自动检出失败:" + checkOutMsg);
			}
			try {
				// 重置之前的password
				clientBatchBean.setPassWord(OldPassWord);
				resultStr = update(clientBatchBean, dmsName);
				log.debug("--update-->自动检入检出更新返回消息：" + resultStr);
			} finally {
				// 重置之前的password
				clientBatchBean.setPassWord(OldPassWord);
				String checkInMsg = checkIn(clientBatchBean, dmsName);
				if (!checkInMsg.split(splitSym)[0].equals(TransOptionKey.SUCCESS)) {
					throw new SunECMException("--update-->批次[" + clientBatchBean.getIndex_Object().getContentID()
							+ "]自动检入失败:" + checkInMsg);
				}
			}
			log.debug("--update-->批次[" + clientBatchBean.getIndex_Object().getContentID() + "]自动检入成功");
		} else {
			resultStr = update(clientBatchBean, dmsName);
			log.debug("--update-->手动检入检出更新返回消息：" + resultStr);
		}
		log.debug("--update-->更新(over)-->resultStr:" + resultStr);
		return resultStr;
	}

	private String update(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		if(!checkES(clientBatchBean,false)) {
			return TransOptionKey.FAIL+splitSym+"please check TAG_CODE or TAG_MAP or TAG_OPTYPE is null";
		}
		connectToHost(ipAddress, socketPort);
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPDATE
					+ ",START=START,XML=" + XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug("--update-->更新时发送的消息：" + sendMsg);
			String msg = conn.receiveMsg();
			if (msg == null) {
				log.error("返回为 空");
				return "";
			}
			log.debug("--update-->更新前建立连接时服务端返回的消息：" + msg);
			String[] msgArray = msg.split(splitSym);
			if (msgArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (clientBatchBean.isBreakPoint()) {
					sendBreakClientBatchFileBean(clientBatchBean.getDocument_Objects(), clientBatchBean
							.getIndex_Object().getContentID(), clientBatchBean.getModelCode());
				} else {
					sendClientBatchFileBean(clientBatchBean.getDocument_Objects(), clientBatchBean.getIndex_Object()
							.getContentID());
				}
				String sendMsg_1 = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPDATE
						+ ",START=END,CONTENTID=" + clientBatchBean.getIndex_Object().getContentID() + ",DMSNAME="
						+ dmsName;
				conn.sendMsg(sendMsg_1);
				log.debug("--update-->更新文件上传完成后发送的消息：" + sendMsg_1);
				msg = conn.receiveMsg();
				if (msg == null) {
					log.error("返回为 空");
					return "";
				}
				log.debug("--update-->更新文件上传完成后服务端返回：" + msg);
				String[] strArray = msg.split(splitSym);
				if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
					if (strArray[1].equals(TransOptionKey.SUCCESS)) {
						resultStr = TransOptionKey.SUCCESS;
					}
				} else {
					resultStr += TransOptionKey.SPLITSYM + msg;
				}
			} else {
				resultStr = msg;
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--update-->return:" + resultStr);
		return resultStr;
	}

	public String upload(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		log.debug("--upload-->上传");
		if(!checkES(clientBatchBean,true)) {
			return TransOptionKey.FAIL+splitSym+"please check TAG_CODE or TAG_MAP or TAG_OPTYPE is null";
		}
		long start = System.currentTimeMillis();
		connectToHost(ipAddress, socketPort);
		if (!checkFileExist(clientBatchBean)) {
			return "false";
		}
		CodeUtil.encodeInBean(clientBatchBean);
		// 添加MD5码
		MD5Util.addBatchMD5Code(clientBatchBean);
		String resultStr = "";
		String contentID = TransOptionKey.FAIL;
		try {
			long beginstart = System.currentTimeMillis();

			long beginend = System.currentTimeMillis();
			log.debug("--upload-->socket连接和校验耗时[" + (beginend - beginstart) + "]毫秒");
			long end1 = System.currentTimeMillis();
			String str = XMLUtil.bean2XML(clientBatchBean);
			long end3 = System.currentTimeMillis();

			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPLOAD
					+ ",START=START,XML=" + str + ",DMSNAME=" + dmsName ;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			log.debug("--upload-->组装BEAN的XML耗时[" + (end3 - end1) + "]毫秒" + sendMsg);
			conn.sendMsg(sendMsg);
			String msg = conn.receiveMsg();
			if (msg == null) {
				log.error("返回为空");
				return "";
			}
			long end2 = System.currentTimeMillis();
			log.debug("--upload-->发送耗时[" + (end2 - end3) + "]毫秒");
			log.debug("--upload-->上传文件前返回:" + msg); // 第1次发送报文
			String[] params = msg.split(splitSym); // --到这里是返回第1次的报文
			if (params[0].equals(TransOptionKey.SERVER_OK)) {
				long _end3 = System.currentTimeMillis();
				contentID = params[1];
				resultStr = TransOptionKey.FAIL + TransOptionKey.SPLITSYM + contentID;
				clientBatchBean.getIndex_Object().setContentID(contentID);
				if (clientBatchBean.isBreakPoint()) {
					sendBreakClientBatchFileBean(clientBatchBean.getDocument_Objects(), contentID,
							clientBatchBean.getModelCode());
				} else {
					sendClientBatchFileBean(clientBatchBean.getDocument_Objects(), contentID);
				}
				long _end4 = System.currentTimeMillis();
				log.debug("--upload-->上传文件耗时[" + (_end4 - _end3) + "]毫秒");

				String sendMsg_1 = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPLOAD
						+ ",START=END,CONTENTID=" + clientBatchBean.getIndex_Object().getContentID() + ",DMSNAME="
						+ dmsName;
				log.debug("--upload-->上传文件完成" + sendMsg_1);
				conn.sendMsg(sendMsg_1);
				msg = conn.receiveMsg();
				if (msg == null) {
					log.error("返回为空");
					return "";
				}
				long _end5 = System.currentTimeMillis();
				log.debug("--upload-->上传文件后发送报文耗时[" + (_end5 - _end4) + "]毫秒, 服务端返回信息" + msg);
				String[] strArray = msg.split(splitSym);
				if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
					if (strArray[1].equals(TransOptionKey.SUCCESS)) {
						resultStr = TransOptionKey.SUCCESS + TransOptionKey.SPLITSYM + strArray[2];
					}
				} else {
					resultStr += TransOptionKey.SPLITSYM + msg;
				}
			} else {
				resultStr = TransOptionKey.FAIL + TransOptionKey.SPLITSYM
						+ msg;
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		long end4 = System.currentTimeMillis();
		log.debug("--upload-->上传操作耗时[" + (end4 - start) + "]毫秒");
		log.debug("--upload(over)-->resultStr=" + resultStr);
		return resultStr;
	}

	private void sendClientBatchFileBean(List<ClientBatchFileBean> ClientBatchFileBeans, String contentID)
			throws SunECMException, IOException {
		log.debug("--sendClientBatchFileBean-->发送批次文件 contentID:" + contentID);
		for (ClientBatchFileBean batchFileBean : ClientBatchFileBeans) {
			List<ClientFileBean> fileBeans = batchFileBean.getFiles();
			for (ClientFileBean fileBean : fileBeans) {
				if (fileBean.getFileName() != null) {
					conn.sendFileData(fileBean.getFileName(), contentID, TransOptionKey.FILE_RECIEVE
							+ TransOptionKey.SPLITSYM);
				}
			}
		}
	}

	private void sendBreakClientBatchFileBean(List<ClientBatchFileBean> ClientBatchFileBeans, String contentID,
			String modeCode) throws IOException, SunECMException {
		log.debug("--sendBreakClientBatchFileBean-->发送批次文件");
		conn.sendMsg(TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.BREAK_POINT + ",CONTENTID="
				+ contentID + ",MODECODE=" + modeCode);
		String aa=conn.receiveMsg();
		if(aa==null){
			log.error("返回为空");
			return ;
		}
		String filesStr =aa.split(splitSym)[1];
		log.debug("--sendBreakClientBatchFileBean-->Break Point Msg is: " + filesStr);
		List<ClientFileBean> completeFiles = XMLUtil.xml2list(filesStr, ClientFileBean.class);

		// 统计所有的待上传文件
		log.debug("--sendBreakClientBatchFileBean-->统计所有的待上传文件");
		List<ClientFileBean> totalFileBean = new ArrayList<ClientFileBean>();
		for (ClientBatchFileBean batchFileBean : ClientBatchFileBeans) {
			List<ClientFileBean> fileBeans = batchFileBean.getFiles();
			for (ClientFileBean fileBean : fileBeans) {
				if (fileBean.getFileName() != null) {
					totalFileBean.add(fileBean);
				}
			}
		}
		log.debug("--sendBreakClientBatchFileBean-->totalFileBean:" + totalFileBean.toString());
		// 将不属于已上传的文件上传
		log.debug("--sendBreakClientBatchFileBean-->将不属于已上传的文件上传");
		if (completeFiles.size() != 0) {
			List<ClientFileBean> breakFileBean=new ArrayList<ClientFileBean>();
			for (ClientFileBean fileBean : totalFileBean) {
				boolean flag=true;
				for (ClientFileBean complteFile : completeFiles) {
					if (fileBean.getFileName().equals(complteFile.getOrigName())) {
						flag=false;
						Long l=new File(fileBean.getFileName()).length();
						if(complteFile.getReceived()!=null&&!l.equals(complteFile.getReceived())){
							breakFileBean.add(fileBean);
						}
					}
				}
				if(flag){
					breakFileBean.add(fileBean);
				}
			}
			for (ClientFileBean fileBean : breakFileBean) {
				conn.sendFileData(fileBean.getFileName(), contentID,
						TransOptionKey.FILE_RECIEVE + TransOptionKey.SPLITSYM);
			}
		} else {
			sendClientBatchFileBean(ClientBatchFileBeans, contentID);
		}
	}

	/**
	 * 校验文件是否存在,并统计文件数
	 * 
	 * @param clientBatchBean
	 * @return
	 */
	private boolean checkFileExist(ClientBatchBean clientBatchBean) {
		log.debug("--checkFileExist-->校验文件是否存在");
		boolean flag = false; // 是否自动统计批次下的文件数
		if (clientBatchBean.getIndex_Object().getAmount() == null
				|| clientBatchBean.getIndex_Object().getAmount().equals("")) {
			flag = true;
		}
		int totalFile = 0;
		// 校验文件是否存在
		List<ClientBatchFileBean> batchFileBeans = clientBatchBean.getDocument_Objects();
		for (ClientBatchFileBean clientBatchFileBean : batchFileBeans) {
			List<ClientFileBean> files = clientBatchFileBean.getFiles();
			for (ClientFileBean clientFileBean : files) {
				if (clientBatchFileBean.getFilePartName().equals(clientBatchBean.getModelCode())) {
					clientBatchBean.getIndex_Object().setCustomMap(clientFileBean.getOtherAtt());
				}
				if (clientFileBean.getFileName() != null) {
					File file = new File(clientFileBean.getFileName());
					if (!file.exists() || file.isDirectory()) {
						log.debug("--checkFileExist-->" + file.getPath() + "文件不存在...");
						return false;
					} else {
						if (flag) {
							totalFile++;
						}
					}
				}
				if(clientFileBean.getProtocol()!=null&&flag){
						totalFile++;
				}
			}
		}
		if (flag) {
			clientBatchBean.getIndex_Object().setAmount(String.valueOf(totalFile));
		}
		return true;
	}
	
	private boolean checkES(ClientBatchBean clientBatchBean,boolean isupload) {
		log.debug("--checkES-->校验标签参数");
		boolean flag = true;
		ArrayList<String> sysAttrList = new ArrayList<>(Arrays.asList("ECM_DOC_ID","MODEL_CODE","ES_QUERY_TIME","CONTENT_ID","GROUP_ID","ES_STATE","FILE_NO","PART_CODE","indexName","data"));
		List<ClientTagBean> indexTagBeanList = new ArrayList<ClientTagBean>();
		indexTagBeanList = clientBatchBean.getIndex_Object().getTagBeanList();
		if (indexTagBeanList!=null) {
			for (int i = 0; i < indexTagBeanList.size(); i++) {
				if (StringUtil.stringIsNull(indexTagBeanList.get(i).getTAG_CODE())) {
				    return false;	
				}
				if (isupload) {
					indexTagBeanList.get(i).setOPTYPE(EsOptionKey.ES_ADD);
				}
				if (StringUtil.stringIsNull(indexTagBeanList.get(i).getOPTYPE())) {
				    return false;
				}
				if (indexTagBeanList.get(i).getOPTYPE().equals(EsOptionKey.ES_DEL)) {
					indexTagBeanList.get(i).setTAG_MAP(new HashMap<String, Object>());
				}
				if (indexTagBeanList.get(i).getTAG_MAP()==null||indexTagBeanList.get(i).getTAG_MAP().size()==0) {
					if (!indexTagBeanList.get(i).getOPTYPE().equals(EsOptionKey.ES_DEL)) {
						return false;
					}
				}
				if(indexTagBeanList.get(i).getTAG_MAP()!=null||indexTagBeanList.get(i).getTAG_MAP().size()!=0) {
					HashMap<String , Object> map = new HashMap<String, Object>();
					map.putAll(indexTagBeanList.get(i).getTAG_MAP());//用户自定义标签集合
					Set<String> attrkeys = map.keySet();
					for (String attrkey : attrkeys) {
						if (sysAttrList.contains(attrkey)) {
							log.warn("索引标签属性名称不能使用以下系统字段:[ECM_DOC_ID,MODEL_CODE,ES_QUERY_TIME,CONTENT_ID,GROUP_ID,ES_STATE,FILE_NO,PART_CODE,indexName,data]");
							return false;
						}
					}
				}
			}
		}
		if (clientBatchBean.getDocument_Objects()!=null) {
			for (int i = 0; i <clientBatchBean.getDocument_Objects().size() ; i++) {
			     ClientBatchFileBean clientBatchFileBean = new ClientBatchFileBean();
			     clientBatchFileBean =clientBatchBean.getDocument_Objects().get(i);
			     if ( clientBatchFileBean.getFiles()!=null) {
			    	 for (int j = 0; j < clientBatchFileBean.getFiles().size(); j++) {
						    List<ClientTagBean> docTagBeanList = new ArrayList<ClientTagBean>();
						    docTagBeanList = clientBatchFileBean.getFiles().get(j).getTagBeanLis();
						    if (docTagBeanList!=null) {
						      for (int k = 0; k < docTagBeanList.size(); k++) {
						    	  if (StringUtil.stringIsNull(docTagBeanList.get(k).getTAG_CODE())) {
									    return false;	
								  }
						    	  if (isupload) {
						    		  docTagBeanList.get(k).setOPTYPE(EsOptionKey.ES_ADD);
								  }
					    		  if (StringUtil.stringIsNull(docTagBeanList.get(k).getOPTYPE())) {
								        return false;
								  }
					    		  if (docTagBeanList.get(k).getOPTYPE().equals(EsOptionKey.ES_DEL)) {
					    			  docTagBeanList.get(k).setTAG_MAP(new HashMap<String, Object>());
								  }
								  if (docTagBeanList.get(k).getTAG_MAP()==null||docTagBeanList.get(k).getTAG_MAP().size()==0) {
									  if (!docTagBeanList.get(k).getOPTYPE().equals(EsOptionKey.ES_DEL)) {
									    return false;
									  }  
								  }
								  if(docTagBeanList.get(k).getTAG_MAP()!=null||docTagBeanList.get(k).getTAG_MAP().size()!=0) {
									  HashMap<String , Object> map = new HashMap<String, Object>();
									  map.putAll(docTagBeanList.get(k).getTAG_MAP());//用户自定义标签集合
									  Set<String> attrkeys = map.keySet();
									  for (String attrkey : attrkeys) {
										  if (sysAttrList.contains(attrkey)) {
											  log.warn("文档标签属性名称不能使用以下系统字段:[ECM_DOC_ID,MODEL_CODE,ES_QUERY_TIME,CONTENT_ID,GROUP_ID,ES_STATE,FILE_NO,PART_CODE,indexName,data]");
											  return false;
										  }
									  }
								  }
						      }	
					        }
						}
				 }
		   }
		}
		return flag;
	}

	public String heightQuery(ClientHeightQuery heightQuery, String dmsName) throws SunECMException, IOException {
		log.debug("--heightQuery-->高级查询");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		CodeUtil.encodeInBean(heightQuery);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.HEIGHT_QUERY + ",XML="
					+ XMLUtil.bean2XML(heightQuery) + ",DMSNAME=" + dmsName ;
			sendMsg=createHeightQuerySendMsgbyToken(heightQuery, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug("--heightQuery-->查询时发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--heightQuery-->查询时返回的消息：" + resultStr);

		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--heightQuery-->高级查询(over)");
		return resultStr;
	}

	public String getAllModelMsg_Client() throws SunECMException, IOException {
		log.debug("--getAllModelMsg_Client-->客户端获得获取内容模型列表信息");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.ALLMODELMSG;
			conn.sendMsg(sendMsg);
			log.debug("--getAllModelMsg_Client-->获取内容模型列表信息发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--getAllModelMsg_Client-->获得获取内容模型列表信息返回的消息：" + resultStr);

		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--getAllModelMsg_Client-->客户端获得获取内容模型列表信息(over)");
		return resultStr;
	}

	public String getContentServerInfo_Client() throws SunECMException, IOException {
		log.debug("--getContentServerInfo_Client-->客户端获取所有服务器信息");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.GET_NODE;
			conn.sendMsg(sendMsg);
			log.debug("--getContentServerInfo_Client-->获取所有服务器信息发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--getContentServerInfo_Client-->获取所有服务器信息返回的消息：" + resultStr);

		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--getContentServerInfo_Client-->客户端获取所有服务器信息(over)");
		return resultStr;
	}

	public String getModelTemplate_Client(String[] modeCodes) throws SunECMException, IOException {
		log.debug("--getModelTemplate_Client-->客户端获取内容模型模版");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		try {
			StringBuffer modeNamesStr = new StringBuffer();
			for (String modeCode : modeCodes) {
				modeNamesStr.append(modeCode).append(TransOptionKey.MODESPLITSYM);
			}
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.METATEMPLATE
					+ ",MODENAMES=" + modeNamesStr.toString();
			conn.sendMsg(sendMsg);
			log.debug("--getModelTemplate_Client-->获取内容模型模版发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--getModelTemplate_Client-->获取内容模型模版返回的消息：" + resultStr);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--getModelTemplate_Client-->客户端获取内容模型模版");
		return resultStr;
	}

	public String getPermissions_Client(String userName, String passWord) throws SunECMException, IOException {
		log.debug("--getPermissions_Client-->客户端获取内容模型权限 userName:" + userName);
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.PERMISSION
					+ ",USERNAME=" + userName + ",PASSWORD=" + CodeUtil.encode(passWord);
			conn.sendMsg(sendMsg);
			log.debug("--getPermissions_Client-->获取内容模型权限发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--getModelTemplate_Client-->获取内容模型权限返回的消息：" + resultStr);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--getPermissions_Client-->客户端获取内容模型权限(over)");
		return resultStr;
	}

	public String getToken(String ipAddress, String tokenCheckValue, String userName, String operationCode)
			throws SunECMException {
		log.debug("--getToken-->客户端获取动态令牌");
		String token = "";
		WSConsoleClient consoleClient = new WSConsoleClient();
		SunEcmConsole console = consoleClient.getEcmConsoleClient(ipAddress + "/webservices/WsInterface", 300000);
		log.debug("--getToken-->获取动态令牌发送时的参数[tokenCheckValue,userName,operationCode]:" + tokenCheckValue + ","
				+ userName + "," + operationCode);
		token = console.getToken(tokenCheckValue, userName, operationCode);
		log.debug("--getToken-->获取动态令牌发返回的消息:" + token);
		return token;
	}

	public String inquireDMByGroup(String userName, String groupName) throws Exception {
		log.debug("--inquireDMByGroup-->向统一接入问询内容存储服务器的地址" + "userName:" + userName + "groupName" + groupName);
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.INQUIREDM + ",USERNAME="
					+ userName + ",DMSNAME=" + groupName;
			conn.sendMsg(sendMsg);
			log.debug("--inquireDMByGroup-->向统一接入问询内容存储服务器的地址发送的消息:" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--inquireDMByGroup-->向统一接入问询内容存储服务器的地址返回的消息:" + sendMsg);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--inquireDMByGroup-->向统一接入问询内容存储服务器的地址(over)-->resultStr:" + resultStr);
		return resultStr;
	}

	public String immedMigrate(MigrateBatchBean migrateBatchBean, String dmsName) throws Exception {
		log.debug("--immedMigrate-->立即迁移");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		CodeUtil.encodeInBean(migrateBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.IMMEDIATEMIGRATE
					+ ",XML=" + XMLUtil.bean2XML(migrateBatchBean) + ",DMSNAME=" + dmsName ;
			sendMsg=createImmigrateBatchSendMsgbyToken(migrateBatchBean, sendMsg);
			conn.sendMsg(sendMsg);
			log.debug("--immedMigrate-->立即迁移发送的消息:" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--immedMigrate-->立即迁移发送的消息:" + resultStr);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--immedMigrate-->立即迁移(over)");
		return resultStr;
	}

	public String queryNodeInfoByGroupIdAndInsNo(String itemType, String insNo) throws Exception {
		log.debug("--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION="
					+ OptionKey.QUERY_NODEINFO_BY_GROUPID_AND_INSNO + ",MODELCODE=" + itemType + ",INSNO=" + insNo;
			conn.sendMsg(sendMsg);
			log.debug("--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息时候发送的信息:" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息时候返回的消息:" + sendMsg);
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		log.debug("--queryNodeInfoByGroupIdAndInsNo-->向统一接入问询机构配置信息的服务器信息(over)-->resultStr:" + resultStr);
		return resultStr;
	}

	public boolean isSsl() {
		return ssl;
	}

	public void setSsl(boolean ssl) {
		this.ssl = ssl;
	}

	public String createUserToken(String username, String password) throws Exception {
		log.info("开始获取用户令牌");
		String result = TransOptionKey.FAIL;
		try {
			connectToHost(ipAddress, socketPort);
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.CREATEUSERTOKEN
					+ ",USERNAME=" + username + ",PASSWORD=" + CodeUtil.encode(password);
			conn.sendMsg(sendMsg);
			log.debug("--createUserToken-->获取用户令牌时发送的消息：" + sendMsg);
			String res = conn.receiveMsg();
			log.debug("--createUserToken-->获取用户令牌时返回的消息：" + res);

			// 服务端异常有可能返回NULL判断为空时提示客户端登陆失败
			if (res == null || "null".equals(res)) {
				log.warn("--login-->获取用户令牌失败-->msg=" + res);
				throw new SunECMException("--createUserToken-->获取用户令牌失败");
			}

			String[] strArray = res.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					result = TransOptionKey.SUCCESS + splitSym + strArray[2];
				} else {
					result = TransOptionKey.FAIL + splitSym + strArray[2];
				}
			} else {
				throw new SunECMException("获取用户令牌时发生异常...异常代码：" + res);
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		return result;
	}

	public String copyBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		log.debug("--copyBatch-->批次复制(begin)");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.COPY_BATCH + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName + ",USERNAME="
					+ clientBatchBean.getUser() + ",PASSWORD=" + clientBatchBean.getPassWord();
			conn.sendMsg(sendMsg);
			log.debug("--copyBatch-->批次复制发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--copyBatch-->批次复制返回的消息：" + resultStr);
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.debug("--copyBatch-->批次复制(over)");
		return resultStr;
	
	}

	public String immedCopyBatch(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		log.debug("--copyBatch-->批次复制(begin)");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.IMMEDCOPY_BATCH + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName + ",USERNAME="
					+ clientBatchBean.getUser() + ",PASSWORD=" + clientBatchBean.getPassWord();
			conn.sendMsg(sendMsg);
			log.debug("--copyBatch-->批次复制发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--copyBatch-->批次复制返回的消息：" + resultStr);
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.debug("--copyBatch-->批次复制(over)");
		return resultStr;
		
	}

	public String otherRuquest(ClientBatchBean clientBatchBean, String requestName) throws Exception {
		log.debug("--第三方请求-->");
		connectToHost(ipAddress, socketPort);
		String resultStr = "";
		CodeUtil.encodeInBean(clientBatchBean);
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + "OTHERREQUEST" + ",XML="
					+ XMLUtil.bean2XML(clientBatchBean) + ",REQUESTNAME=" + requestName;
			conn.sendMsg(sendMsg);
			log.debug("--otherRuquest-->第三方请求发送的消息：" + sendMsg);
			resultStr = conn.receiveMsg();
			log.debug("--otherRuquest-->第三方请求返回的消息：" + resultStr);
		} finally {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.destroy();
		}
		log.debug("--第三方请求--->(over)");
		return resultStr;
	}
	
	public String uploadByStream(ClientBatchBean clientBatchBean, String dmsName) throws SunECMException, IOException {
		log.debug("--upload-->上传");
		long start = System.currentTimeMillis();
		connectToHost(ipAddress, socketPort);
		String checkResult= checkFileExistByStream(clientBatchBean);
		if (!checkResult.equals("CHERCK_SUCEESS")) {
			return checkResult;
		}
	    checkFileExistByStream(clientBatchBean);//文件流上传uuid生成FIleName,并检验是否添加了文件流
		CodeUtil.encodeInBean(clientBatchBean);
		// 添加MD5码   目前逻辑是需要客户端传MD5，没传就false
//		if (!MD5Util.addBatchMD5CodeByStream(clientBatchBean)) {
//			return "#######上传批次缺少MD5[FAIL<<::>>711]#######";
//		};//可以服务端参数校验是否缺少MD5
		String resultStr = "";
		String contentID = TransOptionKey.FAIL;
		try {
			long beginstart = System.currentTimeMillis();

			long beginend = System.currentTimeMillis();
			log.debug("--upload-->socket连接和校验耗时[" + (beginend - beginstart) + "]毫秒");
			long end1 = System.currentTimeMillis();
			String str = XMLUtil.bean2XML(clientBatchBean);
			long end3 = System.currentTimeMillis();

			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPLOAD
					+ ",START=START,XML=" + str + ",DMSNAME=" + dmsName ;
			sendMsg=createBatchSendMsgbyToken(clientBatchBean, sendMsg);
			log.debug("--upload-->组装BEAN的XML耗时[" + (end3 - end1) + "]毫秒" + sendMsg);
			conn.sendMsg(sendMsg);
			String msg = conn.receiveMsg();
			if (msg == null) {
				log.error("返回为空");
				return "";
			}
			long end2 = System.currentTimeMillis();
			log.debug("--upload-->发送耗时[" + (end2 - end3) + "]毫秒");
			log.debug("--upload-->上传文件前返回:" + msg); // 第1次发送报文
			String[] params = msg.split(splitSym); // --到这里是返回第1次的报文
			if (params[0].equals(TransOptionKey.SERVER_OK)) {
				long _end3 = System.currentTimeMillis();
				contentID = params[1];
				resultStr = TransOptionKey.FAIL + TransOptionKey.SPLITSYM + contentID;
				clientBatchBean.getIndex_Object().setContentID(contentID);
				if (clientBatchBean.isBreakPoint()) {
					sendBreakClientBatchFileBean(clientBatchBean.getDocument_Objects(), contentID,
							clientBatchBean.getModelCode());
				} else {
					sendClientBatchFileBeanbyStream(clientBatchBean.getDocument_Objects(), contentID);
				}
				long _end4 = System.currentTimeMillis();
				log.debug("--upload-->上传文件耗时[" + (_end4 - _end3) + "]毫秒");

				String sendMsg_1 = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPLOAD
						+ ",START=END,CONTENTID=" + clientBatchBean.getIndex_Object().getContentID() + ",DMSNAME="
						+ dmsName;
				log.debug("--upload-->上传文件完成" + sendMsg_1);
				conn.sendMsg(sendMsg_1);
				msg = conn.receiveMsg();
				if (msg == null) {
					log.error("返回为空");
					return "";
				}
				long _end5 = System.currentTimeMillis();
				log.debug("--upload-->上传文件后发送报文耗时[" + (_end5 - _end4) + "]毫秒, 服务端返回信息" + msg);
				String[] strArray = msg.split(splitSym);
				if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
					if (strArray[1].equals(TransOptionKey.SUCCESS)) {
						resultStr = TransOptionKey.SUCCESS + TransOptionKey.SPLITSYM + strArray[2];
					}
				} else {
					resultStr += TransOptionKey.SPLITSYM + msg;
				}
			} else {
				resultStr = TransOptionKey.FAIL + TransOptionKey.SPLITSYM
						+ msg;
			}
		} finally {
			try{
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				String endsString=conn.receiveMsg();
			}catch (Exception e) {
					log.warn(e.toString());
				}finally{
				conn.destroy();
				}
		}
		long end4 = System.currentTimeMillis();
		log.debug("--upload-->上传操作耗时[" + (end4 - start) + "]毫秒");
		log.debug("--upload(over)-->resultStr=" + resultStr);
		return resultStr;
	}
	
    //byStream
	private void sendClientBatchFileBeanbyStream(List<ClientBatchFileBean> ClientBatchFileBeans, String contentID)
			throws SunECMException, IOException {
		log.debug("--sendClientBatchFileBean-->发送批次文件 contentID:" + contentID);
		for (ClientBatchFileBean batchFileBean : ClientBatchFileBeans) {
			List<ClientFileBean> fileBeans = batchFileBean.getFiles();
			for (ClientFileBean fileBean : fileBeans) {
				if (fileBean.getInputStream() != null) {
					conn.sendFileDatabyStream(fileBean , contentID, TransOptionKey.FILE_RECIEVE
							+ TransOptionKey.SPLITSYM);
				}
			}
		}
	}
		
	/**
	 * 校验流是否为空,并统计文件数,FileName由UUID生成
	 * 
	 * @param clientBatchBean
	 * @return
	 * @throws SunECMException 
	 */
	private String checkFileExistByStream(ClientBatchBean clientBatchBean) throws SunECMException {
		log.debug("--checkFileExist-->校验文件是否存在");
		boolean flag = false; // 是否自动统计批次下的文件数
		if (clientBatchBean.getIndex_Object().getAmount() == null
				|| clientBatchBean.getIndex_Object().getAmount().equals("")) {
			flag = true;
		}
		int totalFile = 0;
		// 校验文件是否存在
		List<ClientBatchFileBean> batchFileBeans = clientBatchBean.getDocument_Objects();
		for (ClientBatchFileBean clientBatchFileBean : batchFileBeans) {
			List<ClientFileBean> files = clientBatchFileBean.getFiles();
			for (ClientFileBean clientFileBean : files) {
				if (clientBatchFileBean.getFilePartName().equals(clientBatchBean.getModelCode())) {
					clientBatchBean.getIndex_Object().setCustomMap(clientFileBean.getOtherAtt());
				}
				clientFileBean.setFileName( UUID.randomUUID().toString().replace("-", ""));
				if (flag) {
					totalFile++;
				}
				if (clientFileBean.getInputStream() == null) {
					return "FAIL:文件["+ clientFileBean.getFileName() + "]的流为NUll";

				}
				if(clientFileBean.getProtocol()!=null&&flag){
						totalFile++;
				}
			}
		}
		if (flag) {
			clientBatchBean.getIndex_Object().setAmount(String.valueOf(totalFile));
		}
		return "CHERCK_SUCEESS";
	}	
	
	public String queryESByECM_DOC_ID(String indexName, String ecm_doc_id) throws Exception {
		log.info("ecm_doc_id:"+ecm_doc_id+",indexName:"+indexName);
		try {
			connectToHost(ipAddress, socketPort);
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.QUERY_ES_BY_ECM_DOC_ID
					+ ",INDEXNAME=" + indexName + ",ECM_DOC_ID=" + ecm_doc_id;
			conn.sendMsg(sendMsg);
			log.info("queryESByECM_DOC_ID发送的信息:" + sendMsg);
			String resultStr  = conn.receiveMsg();
			log.info("queryESByECM_DOC_ID返回的消息:" + resultStr);
		return resultStr;
		} finally {
			try {
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			} catch (Exception e) {
				log.warn(e.toString());
			} finally {
				conn.destroy();
			}
		}

	}
	
	public String queryESByBool(EsBean esBean) throws Exception {
		log.info("queryESByBool");
		connectToHost(ipAddress, socketPort);
		try {
		String xml=	XMLUtil.bean2XML(esBean);
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.QUERY_ES_BY_BOOL
					+ ",XML=" + xml;
			
			conn.sendMsg(sendMsg);
			log.info("queryESByESbean发送的信息:" + sendMsg);
			String resultStr = conn.receiveMsg();
			log.info("queryESByESbean返回的消息:" + resultStr);
			return resultStr;
		} finally {
			try {
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			} catch (Exception e) {
				log.warn(e.toString());
			} finally {
				conn.destroy();
			}
		}

	}

	public String uploadESTag(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
		log.info("uploadESTAG");
		if(!checkES(clientBatchBean,true)) {
			return TransOptionKey.FAIL+splitSym+"please check TAG_CODE or TAG_MAP or TAG_OPTYPE is null";
		}
		connectToHost(ipAddress, socketPort);
		String resultStr = TransOptionKey.FAIL;
		CodeUtil.encodeInBean(clientBatchBean);
		clientBatchBean.setPASSWD(clientBatchBean.getPassWord());
		try {
			String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPLOAD_ES_TAG
					+ ",START=START,XML=" + XMLUtil.bean2XML(clientBatchBean)+",CONTENTID="+ clientBatchBean
					.getIndex_Object().getContentID()+ ",DMSNAME=" + dmsName;
			conn.sendMsg(sendMsg);
			log.info("--uploadESTAG-->sendMSG：" + sendMsg);
			String msg = conn.receiveMsg();
			log.info("--uploadESTAG-->recMSG：" + msg);
			if (msg == null) {
				log.error("返回为空");
				return "";
			}
			String[] strArray = msg.split(splitSym);
			if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
				if (strArray[1].equals(TransOptionKey.SUCCESS)) {
					resultStr = TransOptionKey.SUCCESS ;
				}else {
					resultStr = strArray[1]+splitSym+strArray[2];
				}
			}else {
				resultStr = msg;
			}
			log.info("--uploadESTAG-->resultStr：" + resultStr);
		} finally {
			try {
				conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
				conn.receiveMsg();
			} catch (Exception e) {
				log.warn(e.toString());
			} finally {
				conn.destroy();
			}
		}
		log.debug("--uploadESTAG-->(over)");
		return resultStr;

	}	

public String updateESTag(ClientBatchBean clientBatchBean, String dmsName) throws Exception {
	log.debug("updateESTAG");
	if(!checkES(clientBatchBean,false)) {
		return TransOptionKey.FAIL+splitSym+"please check TAG_CODE or TAG_MAP or TAG_OPTYPE is null";
	}
	connectToHost(ipAddress, socketPort);
	String resultStr = "";
	CodeUtil.encodeInBean(clientBatchBean);
	clientBatchBean.setPASSWD(clientBatchBean.getPassWord());
	try {
		String sendMsg = TransOptionKey.MESSAGE_PROCESS + splitSym + "OPTION=" + OptionKey.UPDATE_ES_TAG + ",XML="
				+ XMLUtil.bean2XML(clientBatchBean) + ",DMSNAME=" + dmsName;
		conn.sendMsg(sendMsg);
		log.info("--updateESTAG-->sendMSG：" + sendMsg);
		String msg = conn.receiveMsg();
		String[] strArray = msg.split(splitSym);
		if (strArray[0].equals(TransOptionKey.SERVER_OK)) {
			if (strArray[1].equals(TransOptionKey.SUCCESS)) {
				resultStr = TransOptionKey.SUCCESS ;
			}else {
				resultStr = strArray[1]+splitSym+strArray[2];
			}
		}else {
			resultStr = msg;
		}
		log.info("--updateESTAG-->resultStr：" + resultStr);
	} finally {
		try {
			conn.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			conn.receiveMsg();
		} catch (Exception e) {
			log.warn(e.toString());
		} finally {
			conn.destroy();
		}
	}
	log.debug("--updateESTAG-->(over)");
	return resultStr;

}
}