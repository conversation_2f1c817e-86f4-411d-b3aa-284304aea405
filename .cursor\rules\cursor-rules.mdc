---
description: 
globs: 
alwaysApply: true
---
{
  "name": "�й����пƼ���˾��Ӫƽ̨�ع�����",
  "description": "����ָ����ϵͳ��������Ӫƽ̨�ع��Ĺ���",
  "rules": [
    {
      "name": "�����Ŀ�ṹת��",
      "description": "����ϵͳ�ĺ����Ŀ�ṹת��Ϊ�µ�΢����ܹ�",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-$1/src/main/java/com/sunyard/ars/$1/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/src/com/sunyard/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-$1/src/main/java/com/sunyard/ars/$1/{{filename}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/java/com/sunyard/ars/fm/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/java/com/sunyard/ars/risk/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/src/com/sunyard/action/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/java/com/sunyard/ars/risk/controller/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/src/com/sunyard/service/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/java/com/sunyard/ars/risk/service/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/src/com/sunyard/dao/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/java/com/sunyard/ars/risk/dao/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/src/com/sunyard/common/model/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/java/com/sunyard/ars/risk/bean/{{filename}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/src/com/sunyard/common/util/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/java/com/sunyard/ars/risk/comm/util/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/WebRoot/WEB-INF/classes/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-$1/src/main/resources/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/WebRoot/WEB-INF/classes/**/*.properties",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-$1/src/main/resources/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/ET/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-�������-et/src/main/java/com/sunyard/ars/et/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/TRT/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵʱԤ��-trt/src/main/java/com/sunyard/ars/trt/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/MC/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-������������-mc/src/main/java/com/sunyard/ars/mc/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/SUPERVISE/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-�ص�ල-supervise/src/main/java/com/sunyard/ars/supervise/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/SYSTEM/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ϵͳ����-SunARS-system/src/main/java/com/sunyard/ars/system/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FILE/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-���ӵ���-SunARS-file/src/main/java/com/sunyard/ars/file/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/ModelRun/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ģ������-ModelRun/src/main/java/com/sunyard/ars/modelrun/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/AI/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ƶ����-SunARS-ai/src/main/java/com/sunyard/ars/ai/{{path}}"
        }
      ]
    },
    {
      "name": "ǰ����Դת��",
      "description": "����ϵͳ��ǰ����Դת��Ϊ�µ�ǰ����Ŀ�ṹ",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/static/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ʵ�ﵵ��-fm/static/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/static/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-����Ԥ��-risk/static/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/WebRoot/static/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-$1/static/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/WebRoot/js/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-$1/static/js/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/WebRoot/css/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-$1/static/css/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/WebRoot/images/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-$1/static/images/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/WebRoot/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-$1/static/html/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ʵ�ﵵ��-fm/static/html/fm/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-����Ԥ��-risk/static/html/risk/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/ET/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-�������-et/static/html/et/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/TRT/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ʵʱԤ��-trt/static/html/trt/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/MC/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-������������-mc/static/html/mc/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/SUPERVISE/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-�ص�ල-supervise/static/html/supervise/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/SYSTEM/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ϵͳ����-sunarsSystem/static/html/system/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FILE/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-���ӵ���-file/static/html/file/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/ModelRun/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ģ������-ModelRun/static/html/file/{{path}}.html"
        }
      ]
    },
    {
      "name": "����ת������",
      "description": "����ϵͳ�Ĵ���ģʽת��Ϊ��ϵͳ�Ĵ���ģʽ",
      "patterns": [
        {
          "from": "import org.springframework.jdbc.core.JdbcTemplate;",
          "to": "import org.springframework.jdbc.core.JdbcTemplate;\nimport org.springframework.stereotype.Service;"
        },
        {
          "from": "public class ([A-Za-z0-9]+)Dao {",
          "to": "@Service\npublic class $1Dao {"
        },
        {
          "from": "public class ([A-Za-z0-9]+)Service {",
          "to": "@Service\npublic class $1Service {"
        },
        {
          "from": "public class ([A-Za-z0-9]+)Controller extends BaseController {",
          "to": "@RestController\n@RequestMapping(\"/$1\")\npublic class $1Controller {"
        },
        {
          "from": "request.getParameter\\(\"([^\"]*)\"\\)",
          "to": "@RequestParam(\"$1\") String $1"
        },
        {
          "from": "@Resource",
          "to": "@Autowired"
        },
        {
          "from": "import javax.annotation.Resource;",
          "to": "import org.springframework.beans.factory.annotation.Autowired;"
        }
      ]
    },
    {
      "name": "�����ļ�ת��",
      "description": "����ϵͳ�������ļ�ת��Ϊ��ϵͳ�������ļ���ʽ",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-$1/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/src/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-$1/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/WebRoot/WEB-INF/web.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-$1/src/main/resources/META-INF/web.xml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/ET/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-�������-et/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/TRT/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵʱԤ��-trt/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/MC/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-������������-mc/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/SUPERVISE/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-�ص�ල-supervise/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/SYSTEM/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ϵͳ����-SunARS-system/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FILE/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-���ӵ���-SunARS-file/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/ModelRun/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ģ������-ModelRun/src/main/resources/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/AI/config/**/*.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ƶ����-SunARS-ai/src/main/resources/config/{{path}}"
        }
      ]
    },
    {
      "name": "��������ת��",
      "description": "Ϊÿ���ع���ģ�鴴��Maven POM�ļ�",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/*/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-$1/pom.xml",
          "transform": "createMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/pom.xml",
          "transform": "createFmMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/pom.xml",
          "transform": "createRiskMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/ET/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-�������-et/pom.xml",
          "transform": "createEtMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/TRT/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵʱԤ��-trt/pom.xml",
          "transform": "createTrtMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/MC/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-������������-mc/pom.xml",
          "transform": "createMcMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/SUPERVISE/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-�ص�ල-supervise/pom.xml",
          "transform": "createSuperviseMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/SYSTEM/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ϵͳ����-SunARS-system/pom.xml",
          "transform": "createSystemMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FILE/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-���ӵ���-SunARS-file/pom.xml",
          "transform": "createFileMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/ModelRun/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ģ������-ModelRun/pom.xml",
          "transform": "createModelRunMavenPom"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/AI/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ƶ����-SunARS-ai/pom.xml",
          "transform": "createAiMavenPom"
        }
      ]
    },
    {
      "name": "ǰ��JSPתHTML",
      "description": "��JSPҳ��ת��ΪHTMLҳ��",
      "patterns": [
        {
          "from": "<%@ page.*%>",
          "to": "<!-- ת����JSPҳ�� -->"
        },
        {
          "from": "<%@ include file=\"([^\"]*)\" %>",
          "to": "<!-- �����ļ�: $1 -->"
        },
        {
          "from": "<jsp:include page=\"([^\"]*)\" />",
          "to": "<!-- ����ҳ��: $1 -->"
        },
        {
          "from": "<%=([^%]*)%>",
          "to": "{{$1}}"
        },
        {
          "from": "<c:forEach items=\"\\$\\{([^}]*)\\}\" var=\"([^\"]*)\">(.*?)</c:forEach>",
          "to": "<!-- ѭ����ʼ: $1 as $2 -->\n$3\n<!-- ѭ������ -->"
        },
        {
          "from": "<c:if test=\"\\$\\{([^}]*)\\}\">(.*?)</c:if>",
          "to": "<!-- ������ʼ: $1 -->\n$2\n<!-- �������� -->"
        },
        {
          "from": "<c:choose>(.*?)</c:choose>",
          "to": "<!-- ѡ��ʼ -->\n$1\n<!-- ѡ����� -->"
        },
        {
          "from": "<c:when test=\"\\$\\{([^}]*)\\}\">(.*?)</c:when>",
          "to": "<!-- ������Ϊ: $1 -->\n$2\n<!-- �������� -->"
        },
        {
          "from": "<c:otherwise>(.*?)</c:otherwise>",
          "to": "<!-- ������� -->\n$1\n<!-- ����������� -->"
        }
      ]
    },
    {
      "name": "��˴����ִ���",
      "description": "����ϵͳ��Java�����ִ���",
      "patterns": [
        {
          "from": "new ArrayList\\(\\)",
          "to": "new ArrayList<>()"
        },
        {
          "from": "new HashMap\\(\\)",
          "to": "new HashMap<>()"
        },
        {
          "from": "new LinkedList\\(\\)",
          "to": "new LinkedList<>()"
        },
        {
          "from": "new HashSet\\(\\)",
          "to": "new HashSet<>()"
        },
        {
          "from": "for \\(int i = 0; i < ([^;]*)\\.size\\(\\); i\\+\\+\\) \\{\n\\s*([^\\s]+) ([^\\s]+) = \\([^\\)]+\\) \\1\\.get\\(i\\);",
          "to": "for ($2 $3 : $1) {"
        },
        {
          "from": "StringBuffer",
          "to": "StringBuilder"
        },
        {
          "from": "([A-Za-z0-9_]+)\\.substring\\(([0-9]+)\\)",
          "to": "Optional.ofNullable($1).map(s -> s.substring($2)).orElse(\"\")"
        },
        {
          "from": "if\\s*\\(([^=!><]*) == null\\)",
          "to": "if (Objects.isNull($1))"
        },
        {
          "from": "if\\s*\\(([^=!><]*) != null\\)",
          "to": "if (Objects.nonNull($1))"
        }
      ]
    },
    {
      "name": "���ݿ�����ִ���",
      "description": "��JDBC����ת��ΪMyBatis",
      "patterns": [
        {
          "from": "String sql = \"([^\"]*)\";\\s*jdbcTemplate\\.([^\\(]*)\\(",
          "to": "// MyBatisע�ⷽʽ\n@$2(\"$1\")"
        },
        {
          "from": "jdbcTemplate\\.update\\(([^,]*), new Object\\[\\] \\{([^\\}]*)\\}\\)",
          "to": "// ʹ��MyBatis\n@Update($1)\nvoid updateMethod($2)"
        },
        {
          "from": "jdbcTemplate\\.queryForList\\(([^,]*), new Object\\[\\] \\{([^\\}]*)\\}\\)",
          "to": "// ʹ��MyBatis\n@Select($1)\nList<Map<String, Object>> selectList($2)"
        },
        {
          "from": "jdbcTemplate\\.queryForObject\\(([^,]*), new Object\\[\\] \\{([^\\}]*)\\}, ([A-Za-z0-9_<>]+)\\.class\\)",
          "to": "// ʹ��MyBatis\n@Select($1)\n$3 selectObject($2)"
        },
        {
          "from": "jdbcTemplate\\.query\\(([^,]*), new Object\\[\\] \\{([^\\}]*)\\}, new ([A-Za-z0-9_]+)\\(\\)\\)",
          "to": "// ʹ��MyBatis\n@Select($1)\n@Results({\n  // ����ӳ���ϵ\n})\nList<Object> query($2)"
        }
      ]
    },
    {
      "name": "Springע���ִ���",
      "description": "����Springע��Ϊ���°汾",
      "patterns": [
        {
          "from": "@Controller",
          "to": "@RestController"
        },
        {
          "from": "@ResponseBody",
          "to": ""
        },
        {
          "from": "import org.springframework.stereotype.Component;",
          "to": "import org.springframework.stereotype.Component;\nimport org.springframework.stereotype.Service;"
        },
        {
          "from": "import org.springframework.web.bind.annotation.RequestMapping;",
          "to": "import org.springframework.web.bind.annotation.RequestMapping;\nimport org.springframework.web.bind.annotation.RestController;\nimport org.springframework.web.bind.annotation.RequestParam;\nimport org.springframework.web.bind.annotation.PathVariable;"
        }
      ]
    },
    {
      "name": "������Ӫƽ̨ģ��ת��",
      "description": "���������Ӫƽ̨����ģ��Ĺ���",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FLOW/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/������Ӫƽ̨-���̹���-flow/src/main/java/com/sunyard/ars/flow/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/UNIFY/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/������Ӫƽ̨-ͳһ�Ż�-unify/src/main/java/com/sunyard/ars/unify/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/COMMON/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-SunARS-common/src/main/java/com/sunyard/ars/common/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FLOW/WebRoot/static/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/������Ӫƽ̨-���̹���-flow/static/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FLOW/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/������Ӫƽ̨-���̹���-flow/static/html/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/UNIFY/WebRoot/static/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/������Ӫƽ̨-ͳһ�Ż�-unify/static/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/UNIFY/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/������Ӫƽ̨-ͳһ�Ż�-unify/static/html/{{path}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/AUTO_PAGE/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/������Ӫƽ̨-�Զ���ҳ��-autoPage/src/main/java/com/sunyard/ars/autopage/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/AUTO_PAGE/WebRoot/static/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/������Ӫƽ̨-�Զ���ҳ��-autoPage/static/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/AUTO_PAGE/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/������Ӫƽ̨-�Զ���ҳ��-autoPage/static/html/{{path}}.html"
        }
      ]
    },
    {
      "name": "΢���������ʩת��",
      "description": "����ϵͳת��Ϊ����΢����ܹ�����ϵͳ",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/CONFIG/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/������Ӫƽ̨-��������-config/src/main/java/com/sunyard/ars/config/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/EUREKA/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/������Ӫƽ̨-ע������-eureka/src/main/java/com/sunyard/ars/eureka/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FEIGN/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/������Ӫƽ̨-�����ӿ�-feign-client/src/main/java/com/sunyard/ars/feign/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/EXTEND/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/������Ӫƽ̨-ͨ����չ-extend/src/main/java/com/sunyard/ars/extend/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/START/src/com/sunyard/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ����ƽ̨-�ǹ���-start/src/main/java/com/sunyard/ars/start/{{path}}"
        }
      ]
    },
    {
      "name": "RCS�ض��ṹת��",
      "description": "ΪRCS��Ŀ����ṹ���Ƶ�ת������",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/WEB-INF/applicationContext.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/resources/applicationContext.xml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/WEB-INF/springmvc-servlet.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/resources/springmvc-servlet.xml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/WEB-INF/classes/jdbc.properties",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/resources/application.yml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/config/**/*",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/resources/config/{{path}}"
        }
      ]
    },
    {
      "name": "RCSǰ����Դ�ض�ת��",
      "description": "ΪRCS��Ŀǰ����Դ���Ƶ�ת������",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/js/**/*.js",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-����Ԥ��-risk/static/js/risk/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/css/**/*.css",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-����Ԥ��-risk/static/css/risk/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/images/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-����Ԥ��-risk/static/images/risk/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-����Ԥ��-risk/static/html/{{filename}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-����Ԥ��-risk/static/html/risk/{{path}}.html"
        }
      ]
    },
    {
      "name": "RCS JSPתVue���",
      "description": "��RCS��ĿJSPҳ��ת��ΪVue���",
      "patterns": [
        {
          "from": "<%@ page.*%>\\s*<html[^>]*>\\s*<head[^>]*>(.*?)<\\/head>\\s*<body[^>]*>(.*?)<\\/body>\\s*<\\/html>",
          "to": "<template>\n<div class=\"risk-container\">\n$2\n</div>\n</template>\n\n<script>\nexport default {\n  name: 'RiskComponent',\n  data() {\n    return {\n      // �������\n    }\n  },\n  methods: {\n    // �������\n  }\n}\n</script>\n\n<style scoped>\n/* �����ʽ */\n</style>"
        },
        {
          "from": "<form [^>]*action=\"([^\"]*)\"[^>]*>(.*?)<\\/form>",
          "to": "<el-form :model=\"formData\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\" class=\"risk-form\">\n$2\n</el-form>"
        },
        {
          "from": "<input\\s+type=\"text\"\\s+name=\"([^\"]*)\"\\s+value=\"([^\"]*)\"[^>]*>",
          "to": "<el-input v-model=\"formData.$1\" placeholder=\"������$1\"></el-input>"
        },
        {
          "from": "<input\\s+type=\"button\"\\s+value=\"([^\"]*)\"\\s+onclick=\"([^\"]*)\"[^>]*>",
          "to": "<el-button type=\"primary\" @click=\"$2\">$1</el-button>"
        },
        {
          "from": "<select\\s+name=\"([^\"]*)\"[^>]*>(.*?)<\\/select>",
          "to": "<el-select v-model=\"formData.$1\" placeholder=\"��ѡ��$1\">\n$2\n</el-select>"
        },
        {
          "from": "<option\\s+value=\"([^\"]*)\"[^>]*>(.*?)<\\/option>",
          "to": "<el-option label=\"$2\" value=\"$1\"></el-option>"
        },
        {
          "from": "<table[^>]*>(.*?)<\\/table>",
          "to": "<el-table :data=\"tableData\" border style=\"width: 100%\">\n$1\n</el-table>"
        },
        {
          "from": "<tr[^>]*>\\s*<th[^>]*>(.*?)<\\/th>\\s*<\\/tr>",
          "to": "<!-- ��ͷ: $1 -->"
        },
        {
          "from": "<th[^>]*>(.*?)<\\/th>",
          "to": "<el-table-column prop=\"$1Field\" label=\"$1\" width=\"180\"></el-table-column>"
        }
      ]
    },
    {
      "name": "RCS�ض�JDBCתMyBatis",
      "description": "��RCS��Ŀ�е�JDBC����ת��ΪMyBatis��ܴ���",
      "patterns": [
        {
          "from": "jdbcTemplate\\.update\\(\"INSERT INTO ([A-Z0-9_]+)\\s*\\(([^\\)]*)\\)\\s*VALUES\\s*\\(([^\\)]*)\\)\"([^;]*);",
          "to": "// MyBatisע�ⷽʽ\n@Insert(\"INSERT INTO $1 ($2) VALUES ($3)\")\nvoid insert$1($4);"
        },
        {
          "from": "jdbcTemplate\\.update\\(\"UPDATE ([A-Z0-9_]+)\\s*SET\\s*([^\\s]*)\\s*WHERE\\s*([^\\s]*)\"([^;]*);",
          "to": "// MyBatisע�ⷽʽ\n@Update(\"UPDATE $1 SET $2 WHERE $3\")\nvoid update$1($4);"
        },
        {
          "from": "jdbcTemplate\\.update\\(\"DELETE FROM ([A-Z0-9_]+)\\s*WHERE\\s*([^\\s]*)\"([^;]*);",
          "to": "// MyBatisע�ⷽʽ\n@Delete(\"DELETE FROM $1 WHERE $2\")\nvoid deleteFrom$1($3);"
        },
        {
          "from": "jdbcTemplate\\.queryForList\\(\"SELECT ([^\\s]*) FROM ([A-Z0-9_]+)\\s*WHERE\\s*([^\\s]*)\"([^;]*);",
          "to": "// MyBatisע�ⷽʽ\n@Select(\"SELECT $1 FROM $2 WHERE $3\")\nList<Map<String, Object>> selectListFrom$2($4);"
        },
        {
          "from": "jdbcTemplate\\.queryForObject\\(\"SELECT ([^\\s]*) FROM ([A-Z0-9_]+)\\s*WHERE\\s*([^\\s]*)\"([^;]*), ([A-Za-z0-9_<>]+)\\.class\\);",
          "to": "// MyBatisע�ⷽʽ\n@Select(\"SELECT $1 FROM $2 WHERE $3\")\n$5 selectObjectFrom$2($4);"
        },
        {
          "from": "String sql = \"([^\"]*)\";\\s*jdbcTemplate\\.([^\\(]*)\\(sql",
          "to": "// MyBatis�ӿڷ�ʽ\n@$2(\"$1\")\nList<Map<String, Object>> execute$2()"
        }
      ]
    },
    {
      "name": "RCS�ض�Spring Boot����",
      "description": "ΪRCS��Ŀ����Spring Boot�ض�����",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/WEB-INF/classes/jdbc.properties",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/resources/application.yml",
          "transform": "jdbcToYaml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/WEB-INF/web.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/java/com/sunyard/RiskApplication.java",
          "transform": "webXmlToSpringBoot"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/WebRoot/WEB-INF/applicationContext.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/src/main/java/com/sunyard/ars/RiskConfig.java",
          "transform": "appContextToConfig"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/RCS/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-����Ԥ��-risk/pom.xml",
          "transform": "createRiskMavenPomDetailed"
        }
      ]
    },
    {
      "name": "FM�ض��ṹת��",
      "description": "ΪFM��Ŀ����ṹ���Ƶ�ת������",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/src/com/sunyard/fm/action/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/java/com/sunyard/ars/fm/controller/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/src/com/sunyard/fm/service/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/java/com/sunyard/ars/fm/service/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/src/com/sunyard/fm/dao/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/java/com/sunyard/ars/fm/dao/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/src/com/sunyard/fm/util/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/java/com/sunyard/ars/fm/comm/util/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/src/com/sunyard/fm/api/**/*.java",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/java/com/sunyard/ars/fm/api/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/WEB-INF/applicationContext.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/resources/applicationContext.xml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/WEB-INF/springmvc-servlet.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/resources/springmvc-servlet.xml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/WEB-INF/classes/jdbc.properties",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/resources/application.yml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/config/**/*",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/resources/config/{{path}}"
        }
      ]
    },
    {
      "name": "FMǰ����Դ�ض�ת��",
      "description": "ΪFM��Ŀǰ����Դ���Ƶ�ת������",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/js/**/*.js",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ʵ�ﵵ��-fm/static/js/fm/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/css/**/*.css",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ʵ�ﵵ��-fm/static/css/fm/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/images/**/*",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ʵ�ﵵ��-fm/static/images/fm/{{path}}"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ʵ�ﵵ��-fm/static/html/{{filename}}.html"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/pages/**/*.jsp",
          "to": "ǰ��/������Ӫƽ̨-�ǹ��̻�ǰ��/��Ӫ���ռ��-ʵ�ﵵ��-fm/static/html/fm/{{path}}.html"
        }
      ]
    },
    {
      "name": "FM JSPתVue���",
      "description": "��FM��ĿJSPҳ��ת��ΪVue���",
      "patterns": [
        {
          "from": "<%@ page.*%>\\s*<html[^>]*>\\s*<head[^>]*>(.*?)<\\/head>\\s*<body[^>]*>(.*?)<\\/body>\\s*<\\/html>",
          "to": "<template>\n<div class=\"fm-container\">\n$2\n</div>\n</template>\n\n<script>\nexport default {\n  name: 'FmComponent',\n  data() {\n    return {\n      // �������\n    }\n  },\n  methods: {\n    // �������\n  }\n}\n</script>\n\n<style scoped>\n/* �����ʽ */\n</style>"
        },
        {
          "from": "<form [^>]*action=\"([^\"]*)\"[^>]*>(.*?)<\\/form>",
          "to": "<el-form :model=\"formData\" :rules=\"rules\" ref=\"ruleForm\" label-width=\"100px\" class=\"fm-form\">\n$2\n</el-form>"
        },
        {
          "from": "<input\\s+type=\"text\"\\s+name=\"([^\"]*)\"\\s+value=\"([^\"]*)\"[^>]*>",
          "to": "<el-input v-model=\"formData.$1\" placeholder=\"������$1\"></el-input>"
        },
        {
          "from": "<input\\s+type=\"button\"\\s+value=\"([^\"]*)\"\\s+onclick=\"([^\"]*)\"[^>]*>",
          "to": "<el-button type=\"primary\" @click=\"$2\">$1</el-button>"
        },
        {
          "from": "<select\\s+name=\"([^\"]*)\"[^>]*>(.*?)<\\/select>",
          "to": "<el-select v-model=\"formData.$1\" placeholder=\"��ѡ��$1\">\n$2\n</el-select>"
        },
        {
          "from": "<option\\s+value=\"([^\"]*)\"[^>]*>(.*?)<\\/option>",
          "to": "<el-option label=\"$2\" value=\"$1\"></el-option>"
        },
        {
          "from": "<table[^>]*>(.*?)<\\/table>",
          "to": "<el-table :data=\"tableData\" border style=\"width: 100%\">\n$1\n</el-table>"
        },
        {
          "from": "<tr[^>]*>\\s*<th[^>]*>(.*?)<\\/th>\\s*<\\/tr>",
          "to": "<!-- ��ͷ: $1 -->"
        },
        {
          "from": "<th[^>]*>(.*?)<\\/th>",
          "to": "<el-table-column prop=\"$1Field\" label=\"$1\" width=\"180\"></el-table-column>"
        }
      ]
    },
    {
      "name": "FM�ض�JDBCתMyBatis",
      "description": "��FM��Ŀ�е�JDBC����ת��ΪMyBatis��ܴ���",
      "patterns": [
        {
          "from": "jdbcTemplate\\.update\\(\"INSERT INTO ([A-Z0-9_]+)\\s*\\(([^\\)]*)\\)\\s*VALUES\\s*\\(([^\\)]*)\\)\"([^;]*);",
          "to": "// MyBatisע�ⷽʽ\n@Insert(\"INSERT INTO $1 ($2) VALUES ($3)\")\nvoid insert$1($4);"
        },
        {
          "from": "jdbcTemplate\\.update\\(\"UPDATE ([A-Z0-9_]+)\\s*SET\\s*([^\\s]*)\\s*WHERE\\s*([^\\s]*)\"([^;]*);",
          "to": "// MyBatisע�ⷽʽ\n@Update(\"UPDATE $1 SET $2 WHERE $3\")\nvoid update$1($4);"
        },
        {
          "from": "jdbcTemplate\\.update\\(\"DELETE FROM ([A-Z0-9_]+)\\s*WHERE\\s*([^\\s]*)\"([^;]*);",
          "to": "// MyBatisע�ⷽʽ\n@Delete(\"DELETE FROM $1 WHERE $2\")\nvoid deleteFrom$1($3);"
        },
        {
          "from": "jdbcTemplate\\.queryForList\\(\"SELECT ([^\\s]*) FROM ([A-Z0-9_]+)\\s*WHERE\\s*([^\\s]*)\"([^;]*);",
          "to": "// MyBatisע�ⷽʽ\n@Select(\"SELECT $1 FROM $2 WHERE $3\")\nList<Map<String, Object>> selectListFrom$2($4);"
        },
        {
          "from": "jdbcTemplate\\.queryForObject\\(\"SELECT ([^\\s]*) FROM ([A-Z0-9_]+)\\s*WHERE\\s*([^\\s]*)\"([^;]*), ([A-Za-z0-9_<>]+)\\.class\\);",
          "to": "// MyBatisע�ⷽʽ\n@Select(\"SELECT $1 FROM $2 WHERE $3\")\n$5 selectObjectFrom$2($4);"
        },
        {
          "from": "String sql = \"([^\"]*)\";\\s*jdbcTemplate\\.([^\\(]*)\\(sql",
          "to": "// MyBatis�ӿڷ�ʽ\n@$2(\"$1\")\nList<Map<String, Object>> execute$2()"
        }
      ]
    },
    {
      "name": "FM�ض�Spring Boot����",
      "description": "ΪFM��Ŀ����Spring Boot�ض�����",
      "patterns": [
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/WEB-INF/classes/jdbc.properties",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/resources/application.yml",
          "transform": "jdbcToYaml"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/WEB-INF/web.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/java/com/sunyard/FmApplication.java",
          "transform": "webXmlToSpringBoot"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/WebRoot/WEB-INF/applicationContext.xml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/src/main/java/com/sunyard/ars/FmConfig.java",
          "transform": "appContextToConfig"
        },
        {
          "from": "��ϵͳ��Ҫ�ع�����Ŀ/FM/*.iml",
          "to": "���/������Ӫƽ̨-������-dop/��Ӫ���ռ��-ʵ�ﵵ��-fm/pom.xml",
          "transform": "createFmMavenPomDetailed"
        }
      ]
    }
  ],
  "transformers": {
    "createMavenPom": "function(content, filename) {\n  const moduleName = filename.split('/').slice(-2)[0];\n  return `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<project xmlns=\"http://maven.apache.org/POM/4.0.0\"\n         xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\n  <modelVersion>4.0.0</modelVersion>\n  <parent>\n    <groupId>com.sunyard.aos</groupId>\n    <artifactId>dop</artifactId>\n    <version>4.1.0</version>\n  </parent>\n\n  <artifactId>SunARS-${moduleName.toLowerCase()}</artifactId>\n  <version>\${sunars-version}</version>\n  <name>��Ӫ���ռ��-${moduleName}</name>\n  <description>��Ӫ���ռ��-${moduleName}ģ��</description>\n\n  <properties>\n    <sunars-version>6.1.2</sunars-version>\n  </properties>\n\n  <dependencies>\n    <dependency>\n      <groupId>com.sunyard.aos</groupId>\n      <artifactId>SunARS-common</artifactId>\n      <version>\${sunars-version}</version>\n    </dependency>\n  </dependencies>\n\n  <build>\n    <plugins>\n      <plugin>\n        <groupId>org.springframework.boot</groupId>\n        <artifactId>spring-boot-maven-plugin</artifactId>\n        <configuration>\n          <fork>true</fork>\n        </configuration>\n        <executions>\n          <execution>\n            <goals>\n              <goal>repackage</goal>\n            </goals>\n          </execution>\n        </executions>\n      </plugin>\n    </plugins>\n    <resources>\n      <resource>\n        <directory>src/main/java</directory>\n        <includes>\n          <include>**/*.xml</include>\n        </includes>\n      </resource>\n      <resource>\n        <directory>src/main/resources</directory>\n        <includes>\n          <include>**/*.yml</include>\n          <include>**/*.txt</include>\n          <include>**/*.xml</include>\n          <include>**/*.properties</include>\n        </includes>\n        <filtering>true</filtering>\n      </resource>\n    </resources>\n  </build>\n</project>`;\n}",
    "createRiskMavenPomDetailed": "function(content, filename) {\n  return `<project xmlns=\"http://maven.apache.org/POM/4.0.0\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n\txsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\n\t<modelVersion>4.0.0</modelVersion>\n\t<parent>\n\t\t<groupId>com.sunyard.aos</groupId>\n\t\t<artifactId>dop</artifactId>\n\t\t<version>4.1.0</version>\n\t</parent>\n\n\t<artifactId>SunARS-risk</artifactId>\n\t<version>\${sunars-version}</version>\n\t<name>��Ӫ���ռ��-����Ԥ��-risk</name>\n\t<description>��Ӫ���ռ��-����Ԥ��-risk</description>\n\n\t<properties>\n\t\t<sunars-version>6.1.2</sunars-version>\n\t\t<mybatis.version>3.5.9</mybatis.version>\n\t\t<mybatis-spring.version>2.0.7</mybatis-spring.version>\n\t\t<druid.version>1.2.8</druid.version>\n\t</properties>\n\n\t<dependencies>\n\t\t<dependency>\n\t\t\t<groupId>com.sunyard.aos</groupId>\n\t\t\t<artifactId>SunARS-common</artifactId>\n\t\t\t<version>\${sunars-version}</version>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-web</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-jdbc</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.mybatis.spring.boot</groupId>\n\t\t\t<artifactId>mybatis-spring-boot-starter</artifactId>\n\t\t\t<version>2.2.2</version>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>com.alibaba</groupId>\n\t\t\t<artifactId>druid-spring-boot-starter</artifactId>\n\t\t\t<version>\${druid.version}</version>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-aop</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-validation</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>com.oracle.database.jdbc</groupId>\n\t\t\t<artifactId>ojdbc8</artifactId>\n\t\t</dependency>\n\t</dependencies>\n\n\t<build>\n\t\t<plugins>\n\t\t\t<plugin>\n\t\t\t\t<artifactId>maven-resources-plugin</artifactId>\n\t\t\t\t<executions>\n\t\t\t\t\t<execution>\n\t\t\t\t\t\t<id>copy-resource</id>\n\t\t\t\t\t\t<phase>compile</phase>\n\t\t\t\t\t\t<goals>\n\t\t\t\t\t\t\t<goal>copy-resources</goal>\n\t\t\t\t\t\t</goals>\n\t\t\t\t\t\t<configuration>\n\t\t\t\t\t\t\t<outputDirectory>\${project.build.outputDirectory}</outputDirectory>\n\t\t\t\t\t\t\t<resources>\n\t\t\t\t\t\t\t\t<resource>\n\t\t\t\t\t\t\t\t\t<!-- �ļ���ַ -->\n\t\t\t\t\t\t\t\t\t<directory>\${basedir}/../������Ӫƽ̨-ͨ����չ-extend/src/main/resources/common/</directory>\n\t\t\t\t\t\t\t\t\t<includes>\n\t\t\t\t\t\t\t\t\t\t<include>*</include>\n\t\t\t\t\t\t\t\t\t\t<include>*/*</include>\n\t\t\t\t\t\t\t\t\t</includes>\n\t\t\t\t\t\t\t\t</resource>\n\t\t\t\t\t\t\t</resources>\n\t\t\t\t\t\t</configuration>\n\t\t\t\t\t</execution>\n\t\t\t\t</executions>\n\t\t\t</plugin>\n\t\t\t<plugin>\n\t\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t\t<artifactId>spring-boot-maven-plugin</artifactId>\n\t\t\t\t<configuration>\n\t\t\t\t\t<fork>true</fork>\n\t\t\t\t</configuration>\n\t\t\t\t<executions>\n\t\t\t\t\t<execution>\n\t\t\t\t\t\t<goals>\n\t\t\t\t\t\t\t<goal>repackage</goal>\n\t\t\t\t\t\t</goals>\n\t\t\t\t\t</execution>\n\t\t\t\t</executions>\n\t\t\t</plugin>\n\t\t</plugins>\n\t\t<resources>\n\t\t\t<resource>\n\t\t\t\t<directory>src/main/java</directory>\n\t\t\t\t<includes>\n\t\t\t\t\t<include>**/*.xml</include>\n\t\t\t\t</includes>\n\t\t\t</resource>\n\t\t\t<resource>\n\t\t\t\t<directory>src/main/resources</directory>\n\t\t\t\t<includes>\n\t\t\t\t\t<include>**/*.yml</include>\n\t\t\t\t\t<include>**/*.txt</include>\n\t\t\t\t\t<include>**/*.xml</include>\n\t\t\t\t\t<include>**/*.dvc</include>\n\t\t\t\t\t<include>**/*.properties</include>\n\t\t\t\t</includes>\n\t\t\t\t<filtering>true</filtering>\n\t\t\t</resource>\n\t\t</resources>\n\t</build>\n</project>`;\n}",
    "createFmMavenPomDetailed": "function(content, filename) {\n  return `<project xmlns=\"http://maven.apache.org/POM/4.0.0\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n\txsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\n\t<modelVersion>4.0.0</modelVersion>\n\t<parent>\n\t\t<groupId>com.sunyard.aos</groupId>\n\t\t<artifactId>dop</artifactId>\n\t\t<version>4.1.0</version>\n\t</parent>\n\n\t<artifactId>SunARS-fm</artifactId>\n\t<version>\${sunars-version}</version>\n\t<name>��Ӫ���ռ��-ʵ�ﵵ��-fm</name>\n\t<description>��Ӫ���ռ��-ʵ�ﵵ��-fm</description>\n\n\t<properties>\n\t\t<sunars-version>6.1.2</sunars-version>\n\t\t<mybatis.version>3.5.9</mybatis.version>\n\t\t<mybatis-spring.version>2.0.7</mybatis-spring.version>\n\t\t<druid.version>1.2.8</druid.version>\n\t</properties>\n\n\t<dependencies>\n\t\t<dependency>\n\t\t\t<groupId>com.sunyard.aos</groupId>\n\t\t\t<artifactId>SunARS-common</artifactId>\n\t\t\t<version>\${sunars-version}</version>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-web</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-jdbc</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.mybatis.spring.boot</groupId>\n\t\t\t<artifactId>mybatis-spring-boot-starter</artifactId>\n\t\t\t<version>2.2.2</version>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>com.alibaba</groupId>\n\t\t\t<artifactId>druid-spring-boot-starter</artifactId>\n\t\t\t<version>\${druid.version}</version>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-aop</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t<artifactId>spring-boot-starter-validation</artifactId>\n\t\t</dependency>\n\t\t<dependency>\n\t\t\t<groupId>com.oracle.database.jdbc</groupId>\n\t\t\t<artifactId>ojdbc8</artifactId>\n\t\t</dependency>\n\t</dependencies>\n\n\t<build>\n\t\t<plugins>\n\t\t\t<plugin>\n\t\t\t\t<artifactId>maven-resources-plugin</artifactId>\n\t\t\t\t<executions>\n\t\t\t\t\t<execution>\n\t\t\t\t\t\t<id>copy-resource</id>\n\t\t\t\t\t\t<phase>compile</phase>\n\t\t\t\t\t\t<goals>\n\t\t\t\t\t\t\t<goal>copy-resources</goal>\n\t\t\t\t\t\t</goals>\n\t\t\t\t\t\t<configuration>\n\t\t\t\t\t\t\t<outputDirectory>\${project.build.outputDirectory}</outputDirectory>\n\t\t\t\t\t\t\t<resources>\n\t\t\t\t\t\t\t\t<resource>\n\t\t\t\t\t\t\t\t\t<!-- �ļ���ַ -->\n\t\t\t\t\t\t\t\t\t<directory>\${basedir}/../������Ӫƽ̨-ͨ����չ-extend/src/main/resources/common/</directory>\n\t\t\t\t\t\t\t\t\t<includes>\n\t\t\t\t\t\t\t\t\t\t<include>*</include>\n\t\t\t\t\t\t\t\t\t\t<include>*/*</include>\n\t\t\t\t\t\t\t\t\t</includes>\n\t\t\t\t\t\t\t\t</resource>\n\t\t\t\t\t\t\t</resources>\n\t\t\t\t\t\t</configuration>\n\t\t\t\t\t</execution>\n\t\t\t\t</executions>\n\t\t\t</plugin>\n\t\t\t<plugin>\n\t\t\t\t<groupId>org.springframework.boot</groupId>\n\t\t\t\t<artifactId>spring-boot-maven-plugin</artifactId>\n\t\t\t\t<configuration>\n\t\t\t\t\t<fork>true</fork>\n\t\t\t\t</configuration>\n\t\t\t\t<executions>\n\t\t\t\t\t<execution>\n\t\t\t\t\t\t<goals>\n\t\t\t\t\t\t\t<goal>repackage</goal>\n\t\t\t\t\t\t</goals>\n\t\t\t\t\t</execution>\n\t\t\t\t</executions>\n\t\t\t</plugin>\n\t\t</plugins>\n\t\t<resources>\n\t\t\t<resource>\n\t\t\t\t<directory>src/main/java</directory>\n\t\t\t\t<includes>\n\t\t\t\t\t<include>**/*.xml</include>\n\t\t\t\t</includes>\n\t\t\t</resource>\n\t\t\t<resource>\n\t\t\t\t<directory>src/main/resources</directory>\n\t\t\t\t<includes>\n\t\t\t\t\t<include>**/*.yml</include>\n\t\t\t\t\t<include>**/*.txt</include>\n\t\t\t\t\t<include>**/*.xml</include>\n\t\t\t\t\t<include>**/*.dvc</include>\n\t\t\t\t\t<include>**/*.properties</include>\n\t\t\t\t</includes>\n\t\t\t\t<filtering>true</filtering>\n\t\t\t</resource>\n\t\t</resources>\n\t</build>\n</project>`;\n}",
    "jdbcToYaml": "function(content, filename) {\n  const moduleName = filename.includes('FM') ? 'fm' : \n                     filename.includes('RCS') ? 'risk' : \n                     filename.includes('ET') ? 'et' : \n                     filename.includes('TRT') ? 'trt' : \n                     filename.includes('MC') ? 'mc' : \n                     filename.includes('SUPERVISE') ? 'supervise' : \n                     filename.includes('SYSTEM') ? 'system' : \n                     filename.includes('FILE') ? 'file' : \n                     filename.includes('ModelRun') ? 'modelrun' : \n                     filename.includes('AI') ? 'ai' : 'app';\n  \n  // ��ģ��JDBC���Ե�YAML��ת��\n  // ʵ��Ӧ����Ӧ�ý���content���ݽ���ת��\n  return `server:\n  port: 8080\n  servlet:\n    context-path: /${moduleName}\n\nspring:\n  application:\n    name: ${moduleName}-service\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: oracle.jdbc.OracleDriver\n    url: ***************************************    username: ${moduleName}\n    password: ${moduleName}123\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      time-between-eviction-runs-millis: 60000\n      min-evictable-idle-time-millis: 300000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      pool-prepared-statements: true\n      max-pool-prepared-statement-per-connection-size: 20\n\nmybatis:\n  mapper-locations: classpath:com/sunyard/ars/${moduleName}/dao/mapper/*.xml\n  type-aliases-package: com.sunyard.aos.${moduleName}.bean\n  configuration:\n    map-underscore-to-camel-case: true\n    cache-enabled: false\n\nlogging:\n  level:\n    com.sunyard.aos.${moduleName}: debug\n  file:\n    name: logs/${moduleName}-service.log\n`;\n}",
    "webXmlToSpringBoot": "function(content, filename) {\n  const moduleName = filename.includes('FM') ? 'Fm' : \n                     filename.includes('RCS') ? 'Risk' : \n                     filename.includes('ET') ? 'Et' : \n                     filename.includes('TRT') ? 'Trt' : \n                     filename.includes('MC') ? 'Mc' : \n                     filename.includes('SUPERVISE') ? 'Supervise' : \n                     filename.includes('SYSTEM') ? 'System' : \n                     filename.includes('FILE') ? 'File' : \n                     filename.includes('ModelRun') ? 'ModelRun' : \n                     filename.includes('AI') ? 'Ai' : 'App';\n  \n  const packageName = moduleName.toLowerCase();\n  \n  return `package com.sunyard;\n\nimport org.springframework.boot.SpringApplication;\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\nimport org.springframework.cloud.client.discovery.EnableDiscoveryClient;\nimport org.springframework.cloud.openfeign.EnableFeignClients;\nimport org.springframework.context.annotation.ComponentScan;\n\n/**\n * ${moduleName}����������\n * �ع�����ϵͳ��Ŀ\n */\n@SpringBootApplication\n@EnableDiscoveryClient\n@EnableFeignClients(basePackages = \"com.sunyard.aos\")\n@ComponentScan(basePackages = {\"com.sunyard.aos\"})\npublic class ${moduleName}Application {\n\n    public static void main(String[] args) {\n        SpringApplication.run(${moduleName}Application.class, args);\n    }\n\n}\n`;\n}",
    "appContextToConfig": "function(content, filename) {\n  const moduleName = filename.includes('FM') ? 'Fm' : \n                     filename.includes('RCS') ? 'Risk' : \n                     filename.includes('ET') ? 'Et' : \n                     filename.includes('TRT') ? 'Trt' : \n                     filename.includes('MC') ? 'Mc' : \n                     filename.includes('SUPERVISE') ? 'Supervise' : \n                     filename.includes('SYSTEM') ? 'System' : \n                     filename.includes('FILE') ? 'File' : \n                     filename.includes('ModelRun') ? 'ModelRun' : \n                     filename.includes('AI') ? 'Ai' : 'App';\n  \n  return `package com.sunyard.ars;\n\nimport org.springframework.context.annotation.Configuration;\nimport org.springframework.context.annotation.ImportResource;\n\n/**\n * ${moduleName}������\n * ����������XML�����ļ�\n */\n@Configuration\npublic class ${moduleName}Config {\n    \n    // �����Ҫ����������XML���ã�����ʹ������ע��\n    // @ImportResource(\"classpath:applicationContext.xml\")\n    \n}\n`;\n}",
    "jdbcToYaml": "function(content, filename) {\n  const moduleName = filename.includes('FM') ? 'fm' : \n                     filename.includes('RCS') ? 'risk' : \n                     filename.includes('ET') ? 'et' : \n                     filename.includes('TRT') ? 'trt' : \n                     filename.includes('MC') ? 'mc' : \n                     filename.includes('SUPERVISE') ? 'supervise' : \n                     filename.includes('SYSTEM') ? 'system' : \n                     filename.includes('FILE') ? 'file' : \n                     filename.includes('ModelRun') ? 'modelrun' : \n                     filename.includes('AI') ? 'ai' : 'app';\n  \n  // ��ģ��JDBC���Ե�YAML��ת��\n  // ʵ��Ӧ����Ӧ�ý���content���ݽ���ת��\n  return `server:\n  port: 8080\n  servlet:\n    context-path: /${moduleName}\n\nspring:\n  application:\n    name: ${moduleName}-service\n  datasource:\n    type: com.alibaba.druid.pool.DruidDataSource\n    driver-class-name: oracle.jdbc.OracleDriver\n    url: ***************************************    username: ${moduleName}\n    password: ${moduleName}123\n    druid:\n      initial-size: 5\n      min-idle: 5\n      max-active: 20\n      max-wait: 60000\n      time-between-eviction-runs-millis: 60000\n      min-evictable-idle-time-millis: 300000\n      validation-query: SELECT 1 FROM DUAL\n      test-while-idle: true\n      test-on-borrow: false\n      test-on-return: false\n      pool-prepared-statements: true\n      max-pool-prepared-statement-per-connection-size: 20\n\nmybatis:\n  mapper-locations: classpath:com/sunyard/ars/${moduleName}/dao/mapper/*.xml\n  type-aliases-package: com.sunyard.aos.${moduleName}.bean\n  configuration:\n    map-underscore-to-camel-case: true\n    cache-enabled: false\n\nlogging:\n  level:\n    com.sunyard.aos.${moduleName}: debug\n  file:\n    name: logs/${moduleName}-service.log\n`;\n}"
  }
} 
















