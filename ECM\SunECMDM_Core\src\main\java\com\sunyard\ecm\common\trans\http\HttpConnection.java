package com.sunyard.ecm.common.trans.http;

import com.sunyard.ecm.common.trans.AbstractConnection;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.context.Context;
import com.sunyard.ecm.server.context.ContextUtil;
import com.sunyard.exception.SunECMException;
import com.sunyard.util.MapUtil;
import com.sunyard.util.TransOptionKey;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class HttpConnection extends AbstractConnection {
	private HttpServletRequest request;
	private HttpServletResponse response;
	private String option;
	private FileItemFactory factory = new DiskFileItemFactory();
	private ServletFileUpload upload = new ServletFileUpload(factory);
	private FileItem itemTmp;
	private MultipartHttpServletRequest multiRequest;
	private static final Logger log = LoggerFactory
			.getLogger(HttpServletRequest.class);
	public HttpConnection(HttpServletRequest request,
                          HttpServletResponse response, String option) throws IOException {
		super();
		this.request = request;
		this.response = response;
		this.clientIp = request.getRemoteAddr();
		this.option = option;
	}

	public String getMessages() throws IOException {
		String msg = request.getParameter("HTTPMSG");
		Context context = ContextUtil.getContext();
		context.recvRecord(msg);
		return msg == null ? "" : msg;
	}
	//http改造，用于http上传用同一个连接上传文件
	public String getHttpMessages() throws IOException {
		this.multiRequest=  (MultipartHttpServletRequest) request;
        String msg = multiRequest.getParameter("HTTPMSG");
		Context context = ContextUtil.getContext();
		context.recvRecord(msg);
		return msg == null ? "" : msg;
		
	}

	public void reciveFile(long filesize, OutputStream out, FileBean fileBean)
			throws IOException {//改造前http
		if (itemTmp instanceof DiskFileItem) {
			DiskFileItem diskFileItem = (DiskFileItem) itemTmp;
			if (diskFileItem.isInMemory()) {
				if (diskFileItem.get().length != filesize) {
					throw new IOException("文件大小不符，设置的值为：" + filesize
							+ " , 实际的值为：" + diskFileItem.get().length);
				}
				byte[] b=diskFileItem.get();
				out.write(b);
				fileBean.setReceived(new Long(b.length));
			} else {
				if (diskFileItem.getStoreLocation().length() != filesize) {
					throw new IOException("文件大小不符，设置的值为：" + filesize
							+ " , 实际的值为："
							+ diskFileItem.getStoreLocation().length());
				}
				InputStream is = null;
				try {
					is = new BufferedInputStream(new FileInputStream(
							diskFileItem.getStoreLocation()));
					int buffer = 65536;
					int read;
					byte[] bytes = new byte[buffer];
					while (-1 != (read = is.read(bytes, 0, buffer))) {
						out.write(bytes, 0, read);
						fileBean.setReceived(fileBean.getReceived()+read);
					}
				} finally {
					if (is != null) {
						IOUtils.closeQuietly(is);
					}
				}
			}
		}
	}
	//http传输多个文件用同一连接
	public void httpReciveFile(long filesize, OutputStream out, FileBean fileBean)
			throws IOException {
         @SuppressWarnings("unchecked")
	     Iterator<String> it=  multiRequest.getFileNames();
	     while (it.hasNext()) {
	      String filename = it.next();
	      if (filename.equals(fileBean.getOrigName())) {
	    	  MultipartFile multipartFile2=multiRequest.getFile(filename);
	    	  CommonsMultipartFile commonsMultipartFile = (CommonsMultipartFile) multipartFile2;
	    	  FileItem fileItem2 = commonsMultipartFile.getFileItem();
	    	  if (itemTmp!=null) {
	    		 itemTmp.delete();//若不为空则先删除上一个的，否则可能导致FileItem实例写入错误的文件
			  }
	    	  itemTmp=fileItem2;
	    	  break;
		  }
	    }
		if (itemTmp instanceof DiskFileItem) {
			DiskFileItem diskFileItem = (DiskFileItem) itemTmp;
			if (diskFileItem.isInMemory()) {
				if (diskFileItem.get().length != filesize) {
					throw new IOException("文件大小不符，设置的值为：" + filesize
							+ " , 实际的值为：" + diskFileItem.get().length);
				}
				byte[] b=diskFileItem.get();
				out.write(b);
				fileBean.setReceived(new Long(b.length));
			} else {
				if (diskFileItem.getStoreLocation().length() != filesize) {
					throw new IOException("文件大小不符，设置的值为：" + filesize
							+ " , 实际的值为："
							+ diskFileItem.getStoreLocation().length());
				}
				InputStream is = null;
				try {
					is = new BufferedInputStream(new FileInputStream(
							diskFileItem.getStoreLocation()));
					int buffer = 65536;
					int read;
					byte[] bytes = new byte[buffer];
					while (-1 != (read = is.read(bytes, 0, buffer))) {
						out.write(bytes, 0, read);
						fileBean.setReceived(fileBean.getReceived()+read);
					}
				} finally {
					if (is != null) {
						IOUtils.closeQuietly(is);
					}
				}
			}
		}
	}

	public boolean sendMessages(String msg) throws IOException {
		response.getOutputStream().write(msg.getBytes("UTF-8"));
		//response.getOutputStream().println();
		Context context = ContextUtil.getContext();
		context.sendRecord(msg);
		return true;
	}

	public void needAlive() {
		this.alive = false;
	}

	public void close() {
	}

	@Override
	protected Map<String, String> getInputParam(String[] content) {
		Context context = ContextUtil.getContext();
		context.setOption(option);
		if (option.equals(TransOptionKey.MESSAGE_PROCESS)) {
			return MapUtil.parseMap(content[content.length - 1], null);
		}
		Map<String, String> param = new HashMap<String, String>();
		List<FileItem> fileItems = null;
		try {
			fileItems = upload.parseRequest(request);
		} catch (FileUploadException e1) {
			log.error("出错",e1);
		}
		if (option.equals(TransOptionKey.FILE_RECIEVE)) {
			if(fileItems!=null){
			for (int i = 0; i < fileItems.size(); i++) {
				FileItem item = (FileItem) fileItems.get(i);
				if (item.isFormField()) {
					String name = item.getFieldName().trim();
					String value = "";
					try {
						value = item.getString("UTF-8").trim();
					} catch (UnsupportedEncodingException e) {
						log.error("出错",e);
					}
					param.put(name, value);
				} else {
					itemTmp = item;
				}
			}
			return param;
			}
		}
		if (option.equals(TransOptionKey.MIGRATE_FILE_RECIEVE)) {
			if(fileItems!=null){
			for (int i = 0; i < fileItems.size(); i++) {
				FileItem item = (FileItem) fileItems.get(i);
				if (item.isFormField()) {
					String name = item.getFieldName().trim();
					String value = "";
					try {
						value = item.getString("UTF-8").trim();
					} catch (UnsupportedEncodingException e) {
						log.error("出错",e);
					}
					if ("CONTENTID".equals(name)) {
						param.put(name, value);
					}
					if ("FILEPATH".equals(name)) {
						param.put(name, value);
					}
					if ("MSG".equals(name)) {
						String[] values = value.split(TransOptionKey.SPLITSYM);
						param.putAll(MapUtil.parseMap(
								values[values.length - 1],
								request.getRemoteAddr()));
					}
				} else {
					itemTmp = item;
				}
			}
			return param;}
		}
		return null;
	}

	@Override
	protected String getOption(String[] content) {
		return option;
	}

	@Override
	protected boolean isLeagalMessage(String receive) {
		if (option.equals(TransOptionKey.MESSAGE_PROCESS)) {
			return super.isLeagalMessage(receive);
		}
		if (option.equals(TransOptionKey.FILE_RECIEVE)) {
			return true;
		}
		if (option.equals(TransOptionKey.MIGRATE_FILE_RECIEVE)) {
			return true;
		}
		throw new RuntimeException("Unreachable");
	}

	public boolean sendErrorMessages(int code ,String msg) throws IOException {
		response.getOutputStream().write(msg.getBytes("UTF-8"));
		//response.setStatus(code);
		Context context = ContextUtil.getContext();
		context.sendRecord(msg);
		return true;
	}

	public String getConnectionType() {
		return "HTTP";
	}

	public void reciveFile(long filesize, OutputStream out, FileBean fileBean, String encodeLength)
			throws IOException, SunECMException {
		if (StringUtils.isEmpty(encodeLength) || encodeLength.equals("0") || "null".equals(encodeLength)) {
			reciveFile(filesize, out, fileBean);
			return;
		} else {
			log.error("HTTP不支持加密模型上传，encodeLength：" + encodeLength);
			throw new SunECMException("HTTP不支持加密模型上传，encodeLength：" + encodeLength);
		}
	}
	
	//http传输文件用同一个连接
	public void httpReciveFile(long filesize, OutputStream out, FileBean fileBean, String encodeLength)
			throws IOException, SunECMException {
		if (StringUtils.isEmpty(encodeLength) || encodeLength.equals("0") || "null".equals(encodeLength)) {
			httpReciveFile(filesize, out, fileBean);
			return;
		} else {
			log.error("HTTP不支持加密模型上传，encodeLength：" + encodeLength);
			throw new SunECMException("HTTP不支持加密模型上传，encodeLength：" + encodeLength);
		}	
	
//		if (itemTmp instanceof DiskFileItem) {
//			DiskFileItem diskFileItem = (DiskFileItem) itemTmp;
//		int encodeLengthInt=Integer.parseInt(encodeLength);
//		Long received=fileBean.getReceived();
//		String tempFileSize=null ;
//		boolean flag = true;
//		long remain = filesize-received;
//		int read;
//		int fileBufferSize = 65536;
//		int buffer = (int) Math.min(fileBufferSize, remain);
//		byte[] bytes = new byte[buffer];
//		byte[] cipherText = null;
//		InputStream is = null;
//		is = new BufferedInputStream(new FileInputStream(
//				diskFileItem.getStoreLocation()));
//		try {
//			bytes = encrypterService.getbytes(buffer, encodeLengthInt);
//			tempFileSize = encrypterService.gettempFileSize(buffer, encodeLengthInt);//部分加密返回加密前字节_加密后字节，全量加密返回-1
//			
//			//设置加密的文件大小,加密前字节+"_"+加密后字节
//			fileBean.setENCODESIZE(tempFileSize);
//			
//			//进行第一次读写（部分加密）
//			while(flag && -1 != (read = is.read(bytes, 0, (int)bytes.length))){
//				cipherText = encrypterService.encryptData_ECB(bytes);//cipherText为加密后字节
//				log.debug("加密后的字节长度为" + cipherText.length);
//				out.write(cipherText);
//				received+=read;
//				fileBean.setReceived(received);
//				remain = remain - read;
//				flag = false;
//				buffer = (int) Math.min(fileBufferSize, remain);
//				bytes = new byte[buffer];
//			}
//			//剩余字节读写
//			while (remain > 0
//					&& -1 != (read = is.read(bytes, 0, buffer))) {
//				out.write(bytes, 0, read);
//				received+=read;
//				fileBean.setReceived(received);
//				remain = remain - read;
//				buffer = (int) Math.min(fileBufferSize, remain);
//				bytes = new byte[buffer];
//			}
//			if (remain != 0) {
//				log.error("HTTP连接异常结束，文件有剩余未接收");
//				throw new IOException("在接受所有文件数据前读到了文件流结尾，剩余：" + remain);
//			}
//		} catch (IOException e) {
//			throw new SunECMException(SunECMExceptionStatus.RECEVIE_FILE_FAIL, e.toString());
//		} finally{
//			if (is != null) {
//				IOUtils.closeQuietly(is);
//			}
//			if(out!=null){
//				out.flush();
//				out.close();
//			}
//		}
//	}
	}
	public  Map<String , MultipartFile> getHttpFiles() {
		@SuppressWarnings("unchecked")
		Map<String , MultipartFile> imgs = multiRequest.getFileMap();
		return imgs;
	}

}