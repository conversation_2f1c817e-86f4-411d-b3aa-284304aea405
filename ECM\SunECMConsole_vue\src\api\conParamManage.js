import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getParamShow(data) {
  const obj = {
    'page': data.page, 
    'limit': data.limit, 
    'par_key': data.par_key
  }
  return request({
    url: '/conParamManage/getParamShowAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function addParamShow(data) {
  const url = '/conParamManage/addParamShowAction'+EndUrl.EndUrl;
  const obj = {
    'optionFlag': data.optionFlag,
    'par_show_id': data.par_show_id,
    'par_key': data.par_key,
    'par_show_name' : encodeURI(data.par_show_name), 
    'par_remark': encodeURI(data.par_remark),
    'par_old_key': data.par_old_key,
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function delParamShow(data) {
  const url = '/conParamManage/delParamShowAction'+EndUrl.EndUrl;
  const obj = {
    'par_show_ids': data.par_show_id,
    'par_keys': data.par_key
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function getConParam(data) {
  const obj = {
    'page': data.page, 
    'limit': data.limit, 
    'par_key': data.par_key,
    'par_group': data.group_id,
    'par_model': data.model_code
  }
  return request({
    url: '/conParamManage/getConParamAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function getParKey() {
  return request({
    url: '/conParamManage/getParKeyAction'+EndUrl.EndUrl,
    method: 'get'
  })
}

export function addConParam(data) {
  const url = '/conParamManage/addConParamAction'+EndUrl.EndUrl;
  const obj = {
    'optionFlag': data.optionFlag,
    'par_id': data.par_id,
    'par_key': data.par_key,
    'par_val': data.par_val,
    'par_all': data.par_all,
    'par_group': data.par_group,
    'par_group' : data.par_group, 
    'par_server': data.par_server,
    'par_model': data.par_model,
    'par_state': data.par_state,

  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function startConParam(data) {
  const url = '/conParamManage/startConParamAction'+EndUrl.EndUrl;
  const obj = {
    'par_ids': data.par_id
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function stopConParam(data) {
  const url = '/conParamManage/stopConParamAction'+EndUrl.EndUrl;
  const obj = {
    'par_ids': data.par_id
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}