<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">

	<bean id="service" class="com.syd.common.serviceinit.ServiceLoader"
		scope="singleton" init-method="loading">
		<property name="interruptLoad" value="false" />
		<property name="serviceList">
			<list>
				<ref bean="sunECMServerIni" />
				<ref bean="socketInitial" />
				<ref bean="schedulerServerInit" />
				<ref bean="contentMigrateInit" />
				<ref bean="download"/>
				<ref bean="batchCopy"/>
				<ref bean="contentBusiOfflineInit"/>
			</list>
		</property>
	</bean>
	<bean id="batchCopy"
		class="com.sunyard.ecm.server.dm.batchcopy.initial.BatchCopyThreadIni" 
		destroy-method="stop">
	</bean>
	<bean id="sunECMServerIni" class="com.sunyard.initialization.SunECMServerIni"
		destroy-method="stop">
	</bean>
	<bean id="schedulerServerInit" class="com.sunyard.scheduler.SchedulerServerInit"
		destroy-method="stop">
	</bean>
	<bean id="download"
		class="com.sunyard.ecm.server.dm.batchdown.initial.DownloadThreadIni"
		destroy-method="stop">
	</bean>
	<bean id="contentMigrateInit"
		class="com.sunyard.ecm.server.dm.batchmigrate.ContentMigrateInit"
		destroy-method="stop">
	</bean>
		<bean id="contentBusiOfflineInit"
		class="com.sunyard.ecm.server.dm.batchofflinebusi.ContentOfflineBusiInit"
		destroy-method="stop">
	</bean>
	

	<bean id="baseServer" class="com.sunyard.ecm.server.service.BaseMessageServer"
		abstract="true">
		<property name="checkDao" ref="checkDao" />
		<property name="batchDao" ref="batchDao" />
		<property name="dMDBDao" ref="dMDBDao" />
		<property name="loginDao" ref="loginDao" />
		<property name="offlineService" ref="offlineService" />
		<property name="extStoreService" ref="extStoreService" />
		<property name="sunECMDao" ref="sunECMDao" />
		<property name="tokenRelUserUtil" ref="tokenRelUserUtil" />
		<property name="fileSystem" ref="fileSystem" />
		<property name="eventManageCenter" ref="eventManageCenter" />

	</bean>
	<bean id="transferFileService" class="com.sunyard.ecm.server.fileserver.TransferFileService">
		<property name="fileSystem" ref="fileSystem"></property>
		<property name="protocolMap">
			<map>
				<entry key="COPY">
					<bean id="COPY"
						class="com.sunyard.ecm.server.fileserver.impl.CopyFileFromlocal" />
				</entry>
				<entry key="CUT">
					<bean id="CUT"
						class="com.sunyard.ecm.server.fileserver.impl.CutFileFromlocal" />
				</entry>
				<entry key="HTTP">
					<bean id="HTTP"
						class="com.sunyard.ecm.server.fileserver.impl.HttpTransferFile" />
				</entry>
			</map>
		</property>
	</bean>
	<bean id = "batchCopyAssist" class="com.sunyard.ecm.server.dm.batchcopy.service.BatchCopyAssist">
		<property name="fileSystem" ref="fileSystem"/>
		<property name="checkDao" ref="checkDao" />
		<property name="batchDao" ref="batchDao" />
	</bean>


	<bean id="server" class="com.sunyard.ecm.server.service.DmMessageServer"
		parent="baseServer">
		<property name="clientMigrateDao" ref="immedMigrateDao"></property>
		<property name="migrateServerDao" ref="migrateServerDao"></property>
		<property name="breakPointHandler" ref="breakPointHandler"></property>
		<property name="fileEncryptAndDecryptDao" ref="fileEncryptAndDecryptDao"></property>
		<property name="transferFileService" ref="transferFileService"></property>
		<property name="conParamService" ref="conParamService"></property>
	</bean>

	<bean id="offlineService" class="com.sunyard.ecm.server.service.OfflineService">
		<property name="sunECMDao" ref="sunECMDao" />
		<property name="eventManageCenter" ref="eventManageCenter"></property>
	</bean>
	<bean id="extStoreService" class="com.sunyard.ecm.server.service.ExtStoreService">
	</bean>
	<bean id="conParamService" class="com.sunyard.ecm.server.service.ConParamService">
	</bean>

	<bean id="fileServer" class="com.sunyard.ecm.server.fileserver.FileServerImp">
		<property name="fileSystem" ref="fileSystem" />
		<property name="eventManageCenter" ref="eventManageCenter"></property>
		<property name="conParamService" ref="conParamService"></property>
	</bean>
	
	<bean id="encrypterService" class="com.sunyard.ecm.server.service.impl.SM4Encrypter">
	</bean>
	
	<!-- DESEncrypter SM4Encrypter -->
	
</beans>