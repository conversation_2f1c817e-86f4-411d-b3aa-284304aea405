package com.sunyard.client.conn;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.mime.MultipartEntity;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.apache.james.mime4j.util.CharsetUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;

/**
 * 客户端HTTP连接
 * 
 * <AUTHOR>
 * 
 */
public class HttpsConn implements TransConn {
	private  final static  Logger log = LoggerFactory.getLogger(HttpsConn.class);
	private HttpClient httpClient = null;
	private HttpPost httppost = null;
	private HttpResponse response = null;

	private String url = "";

	/**
	 * 建立HTTP连接
	 * 
	 * @param url
	 *            连接的URL
	 * @param connTimeOut
	 *            连接超时时间
	 * @param reqTimeOut
	 *            请求超时时间
	 */
	public HttpsConn(String url, int connTimeOut, int reqTimeOut) {
		log.info("建立HTTP连接,URL:" + url + ",连接超时时间:" + connTimeOut + ",请求超时时间:" + reqTimeOut);
		this.url = url;
		httpClient = url.startsWith("https://") ? new SSLClient() : new DefaultHttpClient();
		httppost = new HttpPost(url);
		// 设置连接超时时间(毫秒为单位)
		httpClient.getParams().setIntParameter(HttpConnectionParams.CONNECTION_TIMEOUT, connTimeOut * 1000);
		// 设置请求超时时间(毫秒为单位)
		httpClient.getParams().setIntParameter(HttpConnectionParams.SO_TIMEOUT, reqTimeOut * 1000);
	}

	public void destroy() {
		log.info("关闭HTTP连接");
		if (httppost != null) {
			httppost.abort();
		}
		if (httpClient != null) {
			httpClient.getConnectionManager().shutdown();
		}
	}

	public String receiveMsg() throws IOException {
		log.info("HTTP接受消息");
		String xmlString = "";
		try {
			HttpEntity entity = response.getEntity();
			if (entity != null) {
				xmlString = EntityUtils.toString(entity, "utf-8");
			}
		} catch (IOException e) {
			log.error("--HttpConn-->receiveMsg-->IOException-->" + e.getMessage());
			throw new IOException(e.toString());
		}
		return xmlString;
	}

	public boolean sendFileData(String filePath, String contentID, String transType)
			throws UnsupportedEncodingException, ClientProtocolException, IOException, SunECMException {
		log.info("发送文件,文件路径：" + filePath + ",contentID:" + contentID);
		int i = filePath.lastIndexOf('.');
		String format = "";
		// 获取文件后缀
		if ((i > -1) && (i < (filePath.length()))) {
			format = filePath.substring(i + 1, filePath.length());
		}
		// 判断文件是否存在
		File file = new File(filePath);
		if (!file.exists()) {
			log.debug("文件不存在,文件路径：" + file.getPath());
			return false;
		}
		HttpPost httppost = new HttpPost(url);// 建立HTTP连接
		MultipartEntity reqEntity = new MultipartEntity(); // HTTP传输对象
		StringBody contentIDBody; // 每个文件传输需要带上批次号
		StringBody filePathBody; // 文件全路径+文件名
		StringBody formatBody; // 文件后缀
		StringBody fileSizeBody; // 文件后缀
		StringBody msg; // 其余消息

		try {
			// 设置传输的格式UTF-8
			contentIDBody = new StringBody(contentID, CharsetUtil.getCharset("UTF-8"));
			filePathBody = new StringBody(filePath, CharsetUtil.getCharset("UTF-8"));
			formatBody = new StringBody(format, CharsetUtil.getCharset("UTF-8"));
			fileSizeBody = new StringBody(String.valueOf(file.length()), CharsetUtil.getCharset("UTF-8"));
			log.debug("--sendFileData-->transType:" + transType);
			if (transType != null) {
				msg = new StringBody(transType, CharsetUtil.getCharset("UTF-8"));
				reqEntity.addPart("MSG", msg);
			}
			reqEntity.addPart("CONTENTID", contentIDBody);
			reqEntity.addPart("FILEPATH", filePathBody);
			reqEntity.addPart("FORMAT", formatBody);
			reqEntity.addPart("FILESIZE", fileSizeBody);

		} catch (UnsupportedEncodingException e) {
			log.error("--HttpConn-->sendFileData-->UnsupportedEncodingException-->" + e.toString());
			throw new UnsupportedEncodingException(e.toString());
		}
		// 设置传输的文件
		FileBody fileBody = new FileBody(file, "application/octet-stream; charset=UTF-8");
		reqEntity.addPart("fileBody", fileBody);
		httppost.setEntity(reqEntity);
		try {
			// 执行HTTP传输
			response = httpClient.execute(httppost);
		} catch (ClientProtocolException e) {
			log.error("--HttpConn-->sendFileData-->ClientProtocolException-->" + e.toString());
			throw new ClientProtocolException(e.toString());
		} catch (IOException e) {
			log.error("--HttpConn-->sendFileData-->IOException-->" + e.toString());
			throw new IOException(e.toString());
		}
		int status = response.getStatusLine().getStatusCode();
		if (status != 200) {
			String res = EntityUtils.toString(response.getEntity(), "UTF-8");
			log.error("--HttpConn-->sendFileData-->Exception-->" + res);
			throw new SunECMException(SunECMExceptionStatus.HTTP_RESPONSE_ERROR, res);
		} else {
			return true;
		}
	}

	public void sendMsg(String msg) throws UnsupportedEncodingException, IOException, SunECMException {
		log.info("HTTP发送消息" + "msg:" + msg);
		List<NameValuePair> nvps = new ArrayList<NameValuePair>();
		String name = "HTTPMSG";
		String value = msg;
		nvps.add(new BasicNameValuePair(name, value));
		try {
			httppost.setEntity(new UrlEncodedFormEntity(nvps, HTTP.UTF_8));
			response = httpClient.execute(httppost);
		} catch (UnsupportedEncodingException e) {
			log.error("--HttpConn-->sendMsg-->UnsupportedEncodingException-->" + e.toString());
			throw new UnsupportedEncodingException(e.toString());
		} catch (IOException e) {
			log.error("--HttpConn-->sendMsg-->IOException-->:", e);
			throw e;
		}
		int status = response.getStatusLine().getStatusCode();
		if (status != 200) {
			log.error("--HttpConn-->sendMsg-->Exception-->" + status);
			throw new SunECMException(status, EntityUtils.toString(response.getEntity(), "utf-8"));
		}
	}

}