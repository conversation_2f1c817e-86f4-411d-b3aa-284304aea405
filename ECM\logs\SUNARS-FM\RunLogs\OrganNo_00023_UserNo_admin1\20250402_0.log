2025-04-02 11:33:52.952 [OrganNo_00023_UserNo_admin1] [6abb118464879982/be7d9c9f2915425a] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 11:33:53.313 [OrganNo_00023_UserNo_admin1] [6abb118464879982/20f9b5d0483ede8d] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 11:33:53.315 [OrganNo_00023_UserNo_admin1] [6abb118464879982/20f9b5d0483ede8d] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 11:33:53.349 [OrganNo_00023_UserNo_admin1] [6abb118464879982/20f9b5d0483ede8d] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 11:33:53.433 [OrganNo_00023_UserNo_admin1] [6abb118464879982/be7d9c9f2915425a] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 11:40:36.079 [OrganNo_00023_UserNo_admin1] [cb41e94c6af31747/4f55ea37f1e602c6] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 11:40:36.155 [OrganNo_00023_UserNo_admin1] [cb41e94c6af31747/a30e82af1bde0f53] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 11:40:36.156 [OrganNo_00023_UserNo_admin1] [cb41e94c6af31747/a30e82af1bde0f53] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 11:40:36.186 [OrganNo_00023_UserNo_admin1] [cb41e94c6af31747/a30e82af1bde0f53] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 11:40:36.274 [OrganNo_00023_UserNo_admin1] [cb41e94c6af31747/4f55ea37f1e602c6] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 11:41:07.395 [OrganNo_00023_UserNo_admin1] [95fc1a33686e6b33/8dedd3aabe6939a5] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 11:41:07.407 [OrganNo_00023_UserNo_admin1] [95fc1a33686e6b33/ebb6db9d3398e6bb] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 11:41:07.407 [OrganNo_00023_UserNo_admin1] [95fc1a33686e6b33/ebb6db9d3398e6bb] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 11:41:07.437 [OrganNo_00023_UserNo_admin1] [95fc1a33686e6b33/ebb6db9d3398e6bb] [http-nio-9009-exec-5] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 11:41:07.513 [OrganNo_00023_UserNo_admin1] [95fc1a33686e6b33/8dedd3aabe6939a5] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 11:45:07.615 [OrganNo_00023_UserNo_admin1] [9a87e03f180ce269/f357c7692faaf2de] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 11:45:07.694 [OrganNo_00023_UserNo_admin1] [9a87e03f180ce269/130afd728c471d03] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 11:45:07.695 [OrganNo_00023_UserNo_admin1] [9a87e03f180ce269/130afd728c471d03] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 11:45:07.725 [OrganNo_00023_UserNo_admin1] [9a87e03f180ce269/130afd728c471d03] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 11:45:07.796 [OrganNo_00023_UserNo_admin1] [9a87e03f180ce269/f357c7692faaf2de] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 11:48:17.510 [OrganNo_00023_UserNo_admin1] [6c66faabfe0e11a9/d145d5f6de533cc3] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 11:48:17.585 [OrganNo_00023_UserNo_admin1] [6c66faabfe0e11a9/865e94aa55e3c4f8] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 11:48:17.586 [OrganNo_00023_UserNo_admin1] [6c66faabfe0e11a9/865e94aa55e3c4f8] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 11:48:17.615 [OrganNo_00023_UserNo_admin1] [6c66faabfe0e11a9/865e94aa55e3c4f8] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 11:48:17.685 [OrganNo_00023_UserNo_admin1] [6c66faabfe0e11a9/d145d5f6de533cc3] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 11:51:11.710 [OrganNo_00023_UserNo_admin1] [b9998274c570a76a/704a7377318b4683] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 11:51:11.783 [OrganNo_00023_UserNo_admin1] [b9998274c570a76a/ff00ecf154655c68] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 11:51:11.784 [OrganNo_00023_UserNo_admin1] [b9998274c570a76a/ff00ecf154655c68] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 11:51:11.815 [OrganNo_00023_UserNo_admin1] [b9998274c570a76a/ff00ecf154655c68] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 11:51:11.890 [OrganNo_00023_UserNo_admin1] [b9998274c570a76a/704a7377318b4683] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 11:52:02.640 [OrganNo_00023_UserNo_admin1] [d52cf26bd677ae16/1c254695f0551cbd] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 11:52:02.653 [OrganNo_00023_UserNo_admin1] [d52cf26bd677ae16/1e42632ed996a201] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 11:52:02.654 [OrganNo_00023_UserNo_admin1] [d52cf26bd677ae16/1e42632ed996a201] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 11:52:02.685 [OrganNo_00023_UserNo_admin1] [d52cf26bd677ae16/1e42632ed996a201] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 11:52:02.758 [OrganNo_00023_UserNo_admin1] [d52cf26bd677ae16/1c254695f0551cbd] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 11:52:31.779 [OrganNo_00023_UserNo_admin1] [02cf8fe7999c4e29/b397b28a21eced00] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 11:52:31.792 [OrganNo_00023_UserNo_admin1] [02cf8fe7999c4e29/8b64a1ea45b97006] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 11:52:31.794 [OrganNo_00023_UserNo_admin1] [02cf8fe7999c4e29/8b64a1ea45b97006] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 11:52:31.823 [OrganNo_00023_UserNo_admin1] [02cf8fe7999c4e29/8b64a1ea45b97006] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 11:52:31.902 [OrganNo_00023_UserNo_admin1] [02cf8fe7999c4e29/b397b28a21eced00] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 16:47:17.457 [OrganNo_00023_UserNo_admin1] [eb68a84bbb125523/cc4310426dfab028] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-02 16:47:17.840 [OrganNo_00023_UserNo_admin1] [eb68a84bbb125523/8fe756f0629cce08] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-02 16:47:17.841 [OrganNo_00023_UserNo_admin1] [eb68a84bbb125523/8fe756f0629cce08] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-02 16:47:17.877 [OrganNo_00023_UserNo_admin1] [eb68a84bbb125523/8fe756f0629cce08] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-02 16:47:17.878 [OrganNo_00023_UserNo_admin1] [eb68a84bbb125523/8fe756f0629cce08] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-02 16:47:17.879 [OrganNo_00023_UserNo_admin1] [eb68a84bbb125523/8fe756f0629cce08] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-02 16:47:17.911 [OrganNo_00023_UserNo_admin1] [eb68a84bbb125523/8fe756f0629cce08] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-02 16:47:18.009 [OrganNo_00023_UserNo_admin1] [eb68a84bbb125523/cc4310426dfab028] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:47:18.121 [OrganNo_00023_UserNo_admin1] [df9a8f92bdaf8bbf/b543abc814adfe09] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-04-02 16:47:18.178 [OrganNo_00023_UserNo_admin1] [df9a8f92bdaf8bbf/b543abc814adfe09] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							},
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							},
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-04-02 16:47:18.257 [OrganNo_00023_UserNo_admin1] [affea163c3257794/256bdac6ff855cbd] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"request"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-04-02 16:47:18.306 [OrganNo_00023_UserNo_admin1] [affea163c3257794/7ef21e3a18d4d1bd] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BORROW_REQUEST WHERE USER_NO = ? AND ((REQUEST_STATE = '003' AND FLOW = '7') OR (REQUEST_STATE = '002' AND FLOW = '7') OR (REQUEST_STATE = '001' AND FLOW = '7') OR (REQUEST_STATE = '701' AND FLOW = '7'))
2025-04-02 16:47:18.308 [OrganNo_00023_UserNo_admin1] [affea163c3257794/7ef21e3a18d4d1bd] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - ==> Parameters: admin1(String)
2025-04-02 16:47:18.346 [OrganNo_00023_UserNo_admin1] [affea163c3257794/7ef21e3a18d4d1bd] [http-nio-9009-exec-3] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - <==      Total: 1
2025-04-02 16:47:18.429 [OrganNo_00023_UserNo_admin1] [affea163c3257794/256bdac6ff855cbd] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:47:21.011 [OrganNo_00023_UserNo_admin1] [9a4ad7d53f4d412a/60a60832d3fe753c] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"getServerTime"
	}
}
2025-04-02 16:47:21.067 [OrganNo_00023_UserNo_admin1] [9a4ad7d53f4d412a/60a60832d3fe753c] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"myServerTime":"20250402",
		"serverTime":1743583641017
	},
	"retMsg":""
}
2025-04-02 16:47:38.221 [OrganNo_00023_UserNo_admin1] [109f8d50c405ffee/fa1b67d9f45e84a5] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-02 16:47:38.228 [OrganNo_00023_UserNo_admin1] [109f8d50c405ffee/0f23f173a8226eab] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-02 16:47:38.230 [OrganNo_00023_UserNo_admin1] [109f8d50c405ffee/0f23f173a8226eab] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-02 16:47:38.263 [OrganNo_00023_UserNo_admin1] [109f8d50c405ffee/0f23f173a8226eab] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-02 16:47:38.264 [OrganNo_00023_UserNo_admin1] [109f8d50c405ffee/0f23f173a8226eab] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-02 16:47:38.264 [OrganNo_00023_UserNo_admin1] [109f8d50c405ffee/0f23f173a8226eab] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-02 16:47:38.298 [OrganNo_00023_UserNo_admin1] [109f8d50c405ffee/0f23f173a8226eab] [http-nio-9009-exec-6] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-02 16:47:38.376 [OrganNo_00023_UserNo_admin1] [109f8d50c405ffee/fa1b67d9f45e84a5] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:47:38.440 [OrganNo_00023_UserNo_admin1] [fc61e84923805eee/af5dc9039a7a6e06] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-04-02 16:47:38.487 [OrganNo_00023_UserNo_admin1] [fc61e84923805eee/af5dc9039a7a6e06] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							},
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							},
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-04-02 16:47:38.563 [OrganNo_00023_UserNo_admin1] [dd756db0009a0cac/c2fb8e0fa6410273] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"audit"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-04-02 16:47:38.582 [OrganNo_00023_UserNo_admin1] [dd756db0009a0cac/253aea692e273b01] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BORROW_REQUEST WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND ((REQUEST_STATE = '701' AND FLOW = '7') OR (REQUEST_STATE = '701' AND FLOW = '7') OR (REQUEST_STATE = '701' AND FLOW = '7') OR (REQUEST_STATE = '701' AND FLOW = '7') OR (REQUEST_STATE = '701' AND FLOW = '7'))
2025-04-02 16:47:38.583 [OrganNo_00023_UserNo_admin1] [dd756db0009a0cac/253aea692e273b01] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - ==> Parameters: admin1(String)
2025-04-02 16:47:38.621 [OrganNo_00023_UserNo_admin1] [dd756db0009a0cac/253aea692e273b01] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - <==      Total: 1
2025-04-02 16:47:38.701 [OrganNo_00023_UserNo_admin1] [dd756db0009a0cac/c2fb8e0fa6410273] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:47:55.349 [OrganNo_00023_UserNo_admin1] [ae7ccb1adc4b1809/679e746455976d5c] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-02 16:47:55.356 [OrganNo_00023_UserNo_admin1] [ae7ccb1adc4b1809/66040e8ace9da8fc] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-02 16:47:55.356 [OrganNo_00023_UserNo_admin1] [ae7ccb1adc4b1809/66040e8ace9da8fc] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-02 16:47:55.388 [OrganNo_00023_UserNo_admin1] [ae7ccb1adc4b1809/66040e8ace9da8fc] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-02 16:47:55.389 [OrganNo_00023_UserNo_admin1] [ae7ccb1adc4b1809/66040e8ace9da8fc] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-02 16:47:55.389 [OrganNo_00023_UserNo_admin1] [ae7ccb1adc4b1809/66040e8ace9da8fc] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-02 16:47:55.422 [OrganNo_00023_UserNo_admin1] [ae7ccb1adc4b1809/66040e8ace9da8fc] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-02 16:47:55.493 [OrganNo_00023_UserNo_admin1] [ae7ccb1adc4b1809/679e746455976d5c] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:47:55.573 [OrganNo_00023_UserNo_admin1] [f99dc48071797ede/087ac919c07b023f] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"appLocationQuery",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-04-02 16:47:55.615 [OrganNo_00023_UserNo_admin1] [f99dc48071797ede/e6c969d2389f3d18] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery_COUNT - ==>  Preparing: SELECT count(0) FROM (SELECT t1.application_no, t1.business_date, t1.site_no, t1.site_name, t1.teller_no, t1.teller_name, t1.code_no, t1.code_name, t1.warrant_amount, t1.user_no, t1.user_name, t1.register_date, t1.application_state, t1.remark, t1.monitor, t2.package_no, t2.package_state, '' tmp1, '' tmp2, '' tmp3, '' pru_no, '' pru_name, '' prd, t3.trunk_no, t3.trunk_state, t3.app_count, t3.trunk_user_no, t3.trunk_user_name, t3.trunk_date, t3.frame_user_no tru_no, t3.frame_user_name tru_name, t3.frame_date trd, '' area_no, t3.ware_house_no, t3.batch_no, t1.ogan_no, t1.ogan_name, t1.APP_INEXNO, t1.END_BUSI_DATE, t1.FRAMESEQ, t1.app_type, t4.BALE_NO, t4.SEND_USER_NO, t4.SEND_USER_NAME, t4.SEND_DATE, t4.RECEIVE_DATE, t4.RECEIVE_USER_NO, t4.RECEIVE_USER_NAME FROM fm_application t1 LEFT JOIN fm_package t2 ON t1.package_no = t2.package_no LEFT JOIN fm_trunk t3 ON t1.trunk_no = t3.trunk_no LEFT JOIN FM_BALE_TB t4 ON t1.BALE_NO = t4.BALE_NO WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = t1.site_no)) row_
2025-04-02 16:47:55.617 [OrganNo_00023_UserNo_admin1] [f99dc48071797ede/e6c969d2389f3d18] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery_COUNT - ==> Parameters: admin1(String)
2025-04-02 16:47:55.655 [OrganNo_00023_UserNo_admin1] [f99dc48071797ede/e6c969d2389f3d18] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery_COUNT - <==      Total: 1
2025-04-02 16:47:55.658 [OrganNo_00023_UserNo_admin1] [f99dc48071797ede/e6c969d2389f3d18] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery - ==>  Preparing: select * from ( select t1.application_no,t1.business_date,t1.site_no,t1.site_name, t1.teller_no,t1.teller_name,t1.code_no,t1.code_name,t1.warrant_amount, t1.user_no,t1.user_name,t1.register_date,t1.application_state,t1.remark,t1.monitor, t2.package_no,t2.package_state,'' tmp1,'' tmp2,'' tmp3, '' pru_no,'' pru_name,'' prd, t3.trunk_no,t3.trunk_state,t3.app_count,t3.trunk_user_no,t3.trunk_user_name, t3.trunk_date,t3.frame_user_no tru_no,t3.frame_user_name tru_name, t3.frame_date trd,'' area_no,t3.ware_house_no,t3.batch_no,t1.ogan_no,t1.ogan_name,t1.APP_INEXNO,t1.END_BUSI_DATE,t1.FRAMESEQ,t1.app_type ,t4.BALE_NO,t4.SEND_USER_NO,t4.SEND_USER_NAME,t4.SEND_DATE,t4.RECEIVE_DATE,t4.RECEIVE_USER_NO,t4.RECEIVE_USER_NAME from fm_application t1 left join fm_package t2 on t1.package_no = t2.package_no left join fm_trunk t3 on t1.trunk_no = t3.trunk_no LEFT JOIN FM_BALE_TB t4 ON t1.BALE_NO = t4.BALE_NO WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = t1.site_no ) ) row_ order by row_.business_date DESC LIMIT ?
2025-04-02 16:47:55.661 [OrganNo_00023_UserNo_admin1] [f99dc48071797ede/e6c969d2389f3d18] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery - ==> Parameters: admin1(String), 15(Integer)
2025-04-02 16:47:55.702 [OrganNo_00023_UserNo_admin1] [f99dc48071797ede/e6c969d2389f3d18] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery - <==      Total: 4
2025-04-02 16:47:55.784 [OrganNo_00023_UserNo_admin1] [f99dc48071797ede/087ac919c07b023f] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"app_type":"1",
				"application_state":"23",
				"teller_no":"2966437",
				"user_no":"admin",
				"site_no":"00023",
				"app_inexno":"1",
				"ogan_no":"00023",
				"ogan_name":"00023-中国银行四川省分行",
				"warrant_amount":"6",
				"site_name":"中国银行四川省分行",
				"user_name":"系统超级管理员",
				"teller_name":"李静",
				"business_date":"20250320",
				"code_name":"差错单",
				"application_no":"20250320111254186478",
				"end_busi_date":"20250320",
				"register_date":"20250320",
				"code_no":"1"
			},
			{
				"app_type":"1",
				"application_state":"14",
				"teller_no":"2966437",
				"user_no":"admin",
				"site_no":"00023",
				"app_inexno":"1",
				"send_date":"20250314",
				"ogan_no":"00023",
				"ogan_name":"00023-中国银行四川省分行",
				"bale_no":"2025031400023002",
				"warrant_amount":"19",
				"site_name":"中国银行四川省分行",
				"receive_user_no":"admin",
				"receive_user_name":"系统超级管理员",
				"user_name":"系统超级管理员",
				"teller_name":"李静",
				"business_date":"20250314",
				"code_name":"凭证",
				"application_no":"20250314183802129311",
				"end_busi_date":"20250314",
				"send_user_no":"admin",
				"register_date":"20250314",
				"code_no":"0",
				"send_user_name":"系统超级管理员",
				"receive_date":"20250314"
			},
			{
				"app_type":"1",
				"application_state":"13",
				"teller_no":"6045330",
				"user_no":"admin",
				"package_state":"1",
				"site_no":"00023",
				"app_inexno":"1",
				"send_date":"20250314",
				"ogan_no":"00023",
				"ogan_name":"00023-中国银行四川省分行",
				"bale_no":"2025031400023001",
				"warrant_amount":"10",
				"site_name":"中国银行四川省分行",
				"receive_user_no":"admin",
				"receive_user_name":"系统超级管理员",
				"user_name":"系统超级管理员",
				"teller_name":"李虹",
				"business_date":"20250314",
				"code_name":"凭证",
				"application_no":"20250314115427438151",
				"end_busi_date":"20250314",
				"send_user_no":"admin",
				"package_no":"0202500023D1500001",
				"register_date":"20250314",
				"code_no":"0",
				"send_user_name":"系统超级管理员",
				"receive_date":"20250314"
			},
			{
				"app_type":"1",
				"application_state":"23",
				"teller_no":"2966437",
				"user_no":"admin",
				"site_no":"00023",
				"app_inexno":"1",
				"ogan_no":"00023",
				"ogan_name":"00023-中国银行四川省分行",
				"warrant_amount":"18",
				"site_name":"中国银行四川省分行",
				"user_name":"系统超级管理员",
				"teller_name":"李静",
				"business_date":"20250313",
				"code_name":"凭证",
				"application_no":"20250314153732058149",
				"end_busi_date":"20250313",
				"register_date":"20250314",
				"code_no":"0"
			}
		],
		"totalCount":4,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 16:48:18.334 [OrganNo_00023_UserNo_admin1] [45f2b6674976c352/45eb91a05d8a61c6] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 16:48:18.359 [OrganNo_00023_UserNo_admin1] [45f2b6674976c352/085d619784ed9a1e] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HANDOVER WHERE handover_site_no IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?)
2025-04-02 16:48:18.360 [OrganNo_00023_UserNo_admin1] [45f2b6674976c352/085d619784ed9a1e] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - ==> Parameters: admin1(String)
2025-04-02 16:48:18.393 [OrganNo_00023_UserNo_admin1] [45f2b6674976c352/085d619784ed9a1e] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.H.selectHandoverByConfig_COUNT - <==      Total: 1
2025-04-02 16:48:18.473 [OrganNo_00023_UserNo_admin1] [45f2b6674976c352/45eb91a05d8a61c6] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 16:48:45.763 [OrganNo_00023_UserNo_admin1] [d6f55caf1bd5ec06/513718e4b61d8cf1] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-02 16:48:45.770 [OrganNo_00023_UserNo_admin1] [d6f55caf1bd5ec06/066f9e80d3b56ac9] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-02 16:48:45.771 [OrganNo_00023_UserNo_admin1] [d6f55caf1bd5ec06/066f9e80d3b56ac9] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-02 16:48:45.808 [OrganNo_00023_UserNo_admin1] [d6f55caf1bd5ec06/066f9e80d3b56ac9] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-02 16:48:45.809 [OrganNo_00023_UserNo_admin1] [d6f55caf1bd5ec06/066f9e80d3b56ac9] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-02 16:48:45.809 [OrganNo_00023_UserNo_admin1] [d6f55caf1bd5ec06/066f9e80d3b56ac9] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-02 16:48:45.843 [OrganNo_00023_UserNo_admin1] [d6f55caf1bd5ec06/066f9e80d3b56ac9] [http-nio-9009-exec-15] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-02 16:48:45.923 [OrganNo_00023_UserNo_admin1] [d6f55caf1bd5ec06/513718e4b61d8cf1] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:48:45.995 [OrganNo_00023_UserNo_admin1] [e6dc96411fb6c7ff/53511f5cb7cb62f5] [http-nio-9009-exec-16] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-04-02 16:48:46.028 [OrganNo_00023_UserNo_admin1] [e6dc96411fb6c7ff/f6c3b5006f29217b] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE IN ('23', '24')
2025-04-02 16:48:46.029 [OrganNo_00023_UserNo_admin1] [e6dc96411fb6c7ff/f6c3b5006f29217b] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-04-02 16:48:46.066 [OrganNo_00023_UserNo_admin1] [e6dc96411fb6c7ff/f6c3b5006f29217b] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-04-02 16:48:46.068 [OrganNo_00023_UserNo_admin1] [e6dc96411fb6c7ff/f6c3b5006f29217b] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-04-02 16:48:46.068 [OrganNo_00023_UserNo_admin1] [e6dc96411fb6c7ff/f6c3b5006f29217b] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-04-02 16:48:46.106 [OrganNo_00023_UserNo_admin1] [e6dc96411fb6c7ff/f6c3b5006f29217b] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-04-02 16:48:46.197 [OrganNo_00023_UserNo_admin1] [e6dc96411fb6c7ff/53511f5cb7cb62f5] [http-nio-9009-exec-16] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 16:49:05.126 [OrganNo_00023_UserNo_admin1] [09d2b6d9131a768f/13fc046ea39a867d] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-02 16:49:05.132 [OrganNo_00023_UserNo_admin1] [09d2b6d9131a768f/a956c098c75ad7a7] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-02 16:49:05.133 [OrganNo_00023_UserNo_admin1] [09d2b6d9131a768f/a956c098c75ad7a7] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-02 16:49:05.167 [OrganNo_00023_UserNo_admin1] [09d2b6d9131a768f/a956c098c75ad7a7] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-02 16:49:05.168 [OrganNo_00023_UserNo_admin1] [09d2b6d9131a768f/a956c098c75ad7a7] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-02 16:49:05.168 [OrganNo_00023_UserNo_admin1] [09d2b6d9131a768f/a956c098c75ad7a7] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-02 16:49:05.201 [OrganNo_00023_UserNo_admin1] [09d2b6d9131a768f/a956c098c75ad7a7] [http-nio-9009-exec-18] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-02 16:49:05.280 [OrganNo_00023_UserNo_admin1] [09d2b6d9131a768f/13fc046ea39a867d] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:49:05.355 [OrganNo_00023_UserNo_admin1] [47a5e285795ae436/c0b7324577ead489] [http-nio-9009-exec-19] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-04-02 16:49:05.377 [OrganNo_00023_UserNo_admin1] [47a5e285795ae436/5a6119f7f2e4163a] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-04-02 16:49:05.378 [OrganNo_00023_UserNo_admin1] [47a5e285795ae436/5a6119f7f2e4163a] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-04-02 16:49:05.411 [OrganNo_00023_UserNo_admin1] [47a5e285795ae436/5a6119f7f2e4163a] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-04-02 16:49:05.491 [OrganNo_00023_UserNo_admin1] [47a5e285795ae436/c0b7324577ead489] [http-nio-9009-exec-19] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 16:49:11.566 [OrganNo_00023_UserNo_admin1] [890014b2d4335ad1/de4372e2ce75d6a3] [http-nio-9009-exec-21] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-02 16:49:11.574 [OrganNo_00023_UserNo_admin1] [890014b2d4335ad1/675b50aced345b99] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-02 16:49:11.575 [OrganNo_00023_UserNo_admin1] [890014b2d4335ad1/675b50aced345b99] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-02 16:49:11.609 [OrganNo_00023_UserNo_admin1] [890014b2d4335ad1/675b50aced345b99] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-02 16:49:11.609 [OrganNo_00023_UserNo_admin1] [890014b2d4335ad1/675b50aced345b99] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-02 16:49:11.609 [OrganNo_00023_UserNo_admin1] [890014b2d4335ad1/675b50aced345b99] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-02 16:49:11.641 [OrganNo_00023_UserNo_admin1] [890014b2d4335ad1/675b50aced345b99] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-02 16:49:11.715 [OrganNo_00023_UserNo_admin1] [890014b2d4335ad1/de4372e2ce75d6a3] [http-nio-9009-exec-21] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:49:11.779 [OrganNo_00023_UserNo_admin1] [9e19558b41556a9b/1fee218ab3215b0c] [http-nio-9009-exec-22] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-04-02 16:49:11.800 [OrganNo_00023_UserNo_admin1] [9e19558b41556a9b/7e661b47dae36535] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE = ?
2025-04-02 16:49:11.802 [OrganNo_00023_UserNo_admin1] [9e19558b41556a9b/7e661b47dae36535] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-04-02 16:49:11.836 [OrganNo_00023_UserNo_admin1] [9e19558b41556a9b/7e661b47dae36535] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-04-02 16:49:11.837 [OrganNo_00023_UserNo_admin1] [9e19558b41556a9b/7e661b47dae36535] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-04-02 16:49:11.838 [OrganNo_00023_UserNo_admin1] [9e19558b41556a9b/7e661b47dae36535] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 23(String), 15(Integer)
2025-04-02 16:49:11.874 [OrganNo_00023_UserNo_admin1] [9e19558b41556a9b/7e661b47dae36535] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-04-02 16:49:11.948 [OrganNo_00023_UserNo_admin1] [9e19558b41556a9b/1fee218ab3215b0c] [http-nio-9009-exec-22] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 16:49:12.031 [OrganNo_00023_UserNo_admin1] [9e2ac1820b1695d8/13807bd9a171a008] [http-nio-9009-exec-23] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-04-02 16:49:12.055 [OrganNo_00023_UserNo_admin1] [9e2ac1820b1695d8/91cbfc30c9a13de8] [http-nio-9009-exec-23] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND EXISTS (SELECT 1 FROM sm_organ_parent_tb sopt WHERE sopt.parent_organ = ? AND sopt.organ_no = SITE_NO)
2025-04-02 16:49:12.056 [OrganNo_00023_UserNo_admin1] [9e2ac1820b1695d8/91cbfc30c9a13de8] [http-nio-9009-exec-23] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), 00023(String)
2025-04-02 16:49:12.091 [OrganNo_00023_UserNo_admin1] [9e2ac1820b1695d8/91cbfc30c9a13de8] [http-nio-9009-exec-23] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-04-02 16:49:12.181 [OrganNo_00023_UserNo_admin1] [9e2ac1820b1695d8/13807bd9a171a008] [http-nio-9009-exec-23] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:49:40.791 [OrganNo_00023_UserNo_admin1] [8b25e33c465a702a/9e5750cb7b45ea74] [http-nio-9009-exec-25] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-04-02 16:49:40.797 [OrganNo_00023_UserNo_admin1] [8b25e33c465a702a/c66d4f9cc5fec031] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-04-02 16:49:40.798 [OrganNo_00023_UserNo_admin1] [8b25e33c465a702a/c66d4f9cc5fec031] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-04-02 16:49:40.829 [OrganNo_00023_UserNo_admin1] [8b25e33c465a702a/c66d4f9cc5fec031] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-04-02 16:49:40.831 [OrganNo_00023_UserNo_admin1] [8b25e33c465a702a/c66d4f9cc5fec031] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-04-02 16:49:40.831 [OrganNo_00023_UserNo_admin1] [8b25e33c465a702a/c66d4f9cc5fec031] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-04-02 16:49:40.864 [OrganNo_00023_UserNo_admin1] [8b25e33c465a702a/c66d4f9cc5fec031] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-04-02 16:49:40.940 [OrganNo_00023_UserNo_admin1] [8b25e33c465a702a/9e5750cb7b45ea74] [http-nio-9009-exec-25] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:49:41.001 [OrganNo_00023_UserNo_admin1] [f7060df06c1d2eb2/a0eea6da5961f1db] [http-nio-9009-exec-26] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-04-02 16:49:41.048 [OrganNo_00023_UserNo_admin1] [f7060df06c1d2eb2/a0eea6da5961f1db] [http-nio-9009-exec-26] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							},
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							},
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-04-02 16:49:41.124 [OrganNo_00023_UserNo_admin1] [ebba7241ae72aa13/11deb20a7143c55d] [http-nio-9009-exec-33] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"request"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-04-02 16:49:41.139 [OrganNo_00023_UserNo_admin1] [ebba7241ae72aa13/fc4b2ee88f0d4e66] [http-nio-9009-exec-33] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BORROW_REQUEST WHERE USER_NO = ? AND ((REQUEST_STATE = '003' AND FLOW = '7') OR (REQUEST_STATE = '002' AND FLOW = '7') OR (REQUEST_STATE = '001' AND FLOW = '7') OR (REQUEST_STATE = '701' AND FLOW = '7'))
2025-04-02 16:49:41.141 [OrganNo_00023_UserNo_admin1] [ebba7241ae72aa13/fc4b2ee88f0d4e66] [http-nio-9009-exec-33] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - ==> Parameters: admin1(String)
2025-04-02 16:49:41.174 [OrganNo_00023_UserNo_admin1] [ebba7241ae72aa13/fc4b2ee88f0d4e66] [http-nio-9009-exec-33] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - <==      Total: 1
2025-04-02 16:49:41.245 [OrganNo_00023_UserNo_admin1] [ebba7241ae72aa13/11deb20a7143c55d] [http-nio-9009-exec-33] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-04-02 16:49:46.405 [OrganNo_00023_UserNo_admin1] [6e5c50d747529b48/dd406ffc4d163248] [http-nio-9009-exec-27] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"getServerTime"
	}
}
2025-04-02 16:49:46.458 [OrganNo_00023_UserNo_admin1] [6e5c50d747529b48/dd406ffc4d163248] [http-nio-9009-exec-27] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"myServerTime":"20250402",
		"serverTime":1743583786407
	},
	"retMsg":""
}
2025-04-02 16:50:01.256 [OrganNo_00023_UserNo_admin1] [fe65b20d2b60e32c/f408f734f6d72a1b] [http-nio-9009-exec-30] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-04-02 16:50:01.287 [OrganNo_00023_UserNo_admin1] [fe65b20d2b60e32c/4982f1041cd32bb8] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HOUSEKEYEXCHANGE_TB WHERE (SENDUSER IS NULL OR SENDUSER IN (SELECT USER_NO FROM SM_USERS_TB WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SM_USERS_TB.ORGAN_NO))) AND (RECIEVEUSER IS NULL OR RECIEVEUSER IN (SELECT USER_NO FROM SM_USERS_TB WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SM_USERS_TB.ORGAN_NO))) AND (GUARDER IS NULL OR GUARDER IN (SELECT USER_NO FROM SM_USERS_TB WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SM_USERS_TB.ORGAN_NO)))
2025-04-02 16:50:01.288 [OrganNo_00023_UserNo_admin1] [fe65b20d2b60e32c/4982f1041cd32bb8] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - ==> Parameters: admin1(String), admin1(String), admin1(String)
2025-04-02 16:50:01.326 [OrganNo_00023_UserNo_admin1] [fe65b20d2b60e32c/4982f1041cd32bb8] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - <==      Total: 1
2025-04-02 16:50:01.396 [OrganNo_00023_UserNo_admin1] [fe65b20d2b60e32c/f408f734f6d72a1b] [http-nio-9009-exec-30] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-04-02 16:50:11.748 [OrganNo_00023_UserNo_admin1] [e8fa380fd7c37bc3/7bcb874c6af69691] [http-nio-9009-exec-38] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-04-02 16:50:11.759 [OrganNo_00023_UserNo_admin1] [e8fa380fd7c37bc3/258414df66f5d8d8] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.S.selectMoreSelective_COUNT - ==>  Preparing: SELECT count(0) FROM FM_SPECIALBUSINESS_TB
2025-04-02 16:50:11.762 [OrganNo_00023_UserNo_admin1] [e8fa380fd7c37bc3/258414df66f5d8d8] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.S.selectMoreSelective_COUNT - ==> Parameters: 
2025-04-02 16:50:11.795 [OrganNo_00023_UserNo_admin1] [e8fa380fd7c37bc3/258414df66f5d8d8] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.S.selectMoreSelective_COUNT - <==      Total: 1
2025-04-02 16:50:11.877 [OrganNo_00023_UserNo_admin1] [e8fa380fd7c37bc3/7bcb874c6af69691] [http-nio-9009-exec-38] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-04-02 16:50:26.699 [OrganNo_00023_UserNo_admin1] [aa975142849d0cd9/6ce29cf087d4f764] [http-nio-9009-exec-35] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"checkUser",
		"checkUserNo":"12",
		"checkUserOrgan":"5102"
	}
}
2025-04-02 16:50:26.898 [OrganNo_00023_UserNo_admin1] [aa975142849d0cd9/6ce29cf087d4f764] [http-nio-9009-exec-35] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"500",
	"retMap":{
		
	},
	"retMsg":"该用户不存在"
}
2025-04-02 16:50:36.187 [OrganNo_00023_UserNo_admin1] [b407e6f9018f601a/f66c0fe735f563c5] [http-nio-9009-exec-36] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"checkUser",
		"checkUserNo":"陈港",
		"checkUserOrgan":"00023"
	}
}
2025-04-02 16:50:36.395 [OrganNo_00023_UserNo_admin1] [b407e6f9018f601a/f66c0fe735f563c5] [http-nio-9009-exec-36] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"500",
	"retMap":{
		
	},
	"retMsg":"该用户不存在"
}
2025-04-02 16:50:43.141 [OrganNo_00023_UserNo_admin1] [8af0a221b4a8ee78/17b8bc154a2279ff] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"checkUser",
		"checkUserNo":"admin",
		"checkUserOrgan":"00023"
	}
}
2025-04-02 16:50:43.306 [OrganNo_00023_UserNo_admin1] [8af0a221b4a8ee78/17b8bc154a2279ff] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"该用户在当前用户的权限机构下"
}
2025-04-02 16:56:50.467 [OrganNo_00023_UserNo_admin1] [14f82b9dbd8ddc38/b1c27e47491152cf] [http-nio-9009-exec-41] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"checkUser",
		"checkUserNo":"admin1",
		"checkUserOrgan":"00023"
	}
}
2025-04-02 16:56:50.673 [OrganNo_00023_UserNo_admin1] [14f82b9dbd8ddc38/b1c27e47491152cf] [http-nio-9009-exec-41] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"该用户在当前用户的权限机构下"
}
