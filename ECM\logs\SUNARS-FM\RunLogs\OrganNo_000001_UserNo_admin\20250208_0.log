2025-02-08 11:50:48.175 [OrganNo_000001_UserNo_admin] [799720a9f68c919d/30d855482a0175fa] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-02-08 11:50:48.254 [OrganNo_000001_UserNo_admin] [799720a9f68c919d/30d855482a0175fa] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:50:48.280 [OrganNo_000001_UserNo_admin] [b111130b9ff18926/5d7346557ac36c56] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:50:48.294 [OrganNo_000001_UserNo_admin] [b111130b9ff18926/5d7346557ac36c56] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:50:48.335 [OrganNo_000001_UserNo_admin] [8d18836b8e098ed7/d159fc1887fb9ff3] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"queryType":"1",
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 11:50:48.355 [OrganNo_000001_UserNo_admin] [8d18836b8e098ed7/d159fc1887fb9ff3] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:50:48.382 [OrganNo_000001_UserNo_admin] [f395922c25f41515/c6f828abdbae3eaf] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"queryType":"1",
		"pageSize":15,
		"currentPage":1,
		"trunkStates":[
			"4",
			"5"
		]
	}
}
2025-02-08 11:50:48.420 [OrganNo_000001_UserNo_admin] [f395922c25f41515/c6f828abdbae3eaf] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:50:49.416 [OrganNo_000001_UserNo_admin] [d4db856570a7fb2f/46cd370e67399782] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:50:49.429 [OrganNo_000001_UserNo_admin] [d4db856570a7fb2f/46cd370e67399782] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:50:49.445 [OrganNo_000001_UserNo_admin] [7b9b8307ebc4b5d4/8008da46105def2c] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-02-08 11:50:49.464 [OrganNo_000001_UserNo_admin] [7b9b8307ebc4b5d4/8008da46105def2c] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:50:49.491 [OrganNo_000001_UserNo_admin] [cce97ee4df0fa3db/5b5e2bfcfd6405b4] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"ALREADY_WAREHOUSE"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 11:50:49.515 [OrganNo_000001_UserNo_admin] [cce97ee4df0fa3db/5b5e2bfcfd6405b4] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:50:49.541 [OrganNo_000001_UserNo_admin] [c65c6fdb0e24bdf5/7f2a965cc52de130] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"trunkStates":[
			"4",
			"5"
		],
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 11:50:49.564 [OrganNo_000001_UserNo_admin] [c65c6fdb0e24bdf5/7f2a965cc52de130] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:06.645 [OrganNo_000001_UserNo_admin] [75d848c925758c1e/86753244e0536d2f] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 11:52:06.671 [OrganNo_000001_UserNo_admin] [75d848c925758c1e/86753244e0536d2f] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:52:07.658 [OrganNo_000001_UserNo_admin] [efc3684bd04b57c3/eef620ba67631ed7] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 11:52:07.675 [OrganNo_000001_UserNo_admin] [efc3684bd04b57c3/eef620ba67631ed7] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:08.615 [OrganNo_000001_UserNo_admin] [579bd966d1cbe435/8165dc4b71e9967e] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 11:52:08.636 [OrganNo_000001_UserNo_admin] [579bd966d1cbe435/8165dc4b71e9967e] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:52:09.366 [OrganNo_000001_UserNo_admin] [bcfef69ab372bd41/a22b7b1e350f44d7] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:09.391 [OrganNo_000001_UserNo_admin] [bcfef69ab372bd41/a22b7b1e350f44d7] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:52:10.134 [OrganNo_000001_UserNo_admin] [f20632e49ff69ea1/e438151fb719ea76] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":0
	}
}
2025-02-08 11:52:10.153 [OrganNo_000001_UserNo_admin] [f20632e49ff69ea1/e438151fb719ea76] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:52:10.173 [OrganNo_000001_UserNo_admin] [d6e9c4bb66f37c98/773e6ff3a19b8821] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":1
	}
}
2025-02-08 11:52:10.187 [OrganNo_000001_UserNo_admin] [d6e9c4bb66f37c98/773e6ff3a19b8821] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:10.773 [OrganNo_000001_UserNo_admin] [f860ddf9a89ccaa1/a806a7de2aaa886d] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:10.787 [OrganNo_000001_UserNo_admin] [f860ddf9a89ccaa1/a806a7de2aaa886d] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:11.548 [OrganNo_000001_UserNo_admin] [4384802d93837af4/78c9f164680e3e35] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:11.564 [OrganNo_000001_UserNo_admin] [4384802d93837af4/78c9f164680e3e35] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:11.589 [OrganNo_000001_UserNo_admin] [e4a05bc74d46b502/4085e3d3bbe5c646] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:11.618 [OrganNo_000001_UserNo_admin] [e4a05bc74d46b502/4085e3d3bbe5c646] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:13.062 [OrganNo_000001_UserNo_admin] [8efc19488565fa47/fdfb8cf311655b2c] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:13.076 [OrganNo_000001_UserNo_admin] [8efc19488565fa47/fdfb8cf311655b2c] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:13.094 [OrganNo_000001_UserNo_admin] [52bfbabdf7a950a7/8080f853a56f390e] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"appLocationQuery",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:13.129 [OrganNo_000001_UserNo_admin] [52bfbabdf7a950a7/8080f853a56f390e] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:14.359 [OrganNo_000001_UserNo_admin] [1bde2e928f08bd7c/7744292816c55fc7] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 11:52:14.378 [OrganNo_000001_UserNo_admin] [1bde2e928f08bd7c/7744292816c55fc7] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:52:15.070 [OrganNo_000001_UserNo_admin] [bb3a24dbd6b3c019/bbb69b48e9eab496] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":15
	}
}
2025-02-08 11:52:15.089 [OrganNo_000001_UserNo_admin] [bb3a24dbd6b3c019/bbb69b48e9eab496] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:52:15.103 [OrganNo_000001_UserNo_admin] [4988770d51557cf7/78b046b660b34fb9] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 11:52:15.116 [OrganNo_000001_UserNo_admin] [4988770d51557cf7/78b046b660b34fb9] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:15.759 [OrganNo_000001_UserNo_admin] [a26470e61307f375/c1638c63bfe826d9] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 11:52:15.777 [OrganNo_000001_UserNo_admin] [a26470e61307f375/c1638c63bfe826d9] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:17.395 [OrganNo_000001_UserNo_admin] [2b174740a382b1fa/95cbbfeb8096dc72] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:17.409 [OrganNo_000001_UserNo_admin] [2b174740a382b1fa/95cbbfeb8096dc72] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:17.434 [OrganNo_000001_UserNo_admin] [f22cc9dff7a84409/74bb28adb169df15] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-02-08 11:52:17.453 [OrganNo_000001_UserNo_admin] [f22cc9dff7a84409/74bb28adb169df15] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:18.028 [OrganNo_000001_UserNo_admin] [76ffedbe87d9f1e2/b0bf4b5b87a889c3] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:18.040 [OrganNo_000001_UserNo_admin] [76ffedbe87d9f1e2/b0bf4b5b87a889c3] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:18.054 [OrganNo_000001_UserNo_admin] [2fc0f50e7f7ddcab/0371e08f90b1d76a] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_4"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1",
		"isCenter":"1"
	}
}
2025-02-08 11:52:18.077 [OrganNo_000001_UserNo_admin] [2fc0f50e7f7ddcab/0371e08f90b1d76a] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:18.095 [OrganNo_000001_UserNo_admin] [4afba73a9359751f/25a553b556d1de08] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"packageState":"FM_STATE_PACK_1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:18.116 [OrganNo_000001_UserNo_admin] [4afba73a9359751f/25a553b556d1de08] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:18.818 [OrganNo_000001_UserNo_admin] [3b44d84a951c4562/5a929d4b9d13cafa] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:18.829 [OrganNo_000001_UserNo_admin] [3b44d84a951c4562/5a929d4b9d13cafa] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:18.844 [OrganNo_000001_UserNo_admin] [286fb8b1cbbfaebe/e402a7a789b8f195] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"packageState":"FM_STATE_PACK_1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:18.863 [OrganNo_000001_UserNo_admin] [286fb8b1cbbfaebe/e402a7a789b8f195] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:18.879 [OrganNo_000001_UserNo_admin] [77cc1ac5862835a9/50db9be0280ef8ac] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_WAREHOUSE"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:18.902 [OrganNo_000001_UserNo_admin] [77cc1ac5862835a9/50db9be0280ef8ac] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:19.538 [OrganNo_000001_UserNo_admin] [08d9b13304e43195/b7c95989c530e79a] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:19.549 [OrganNo_000001_UserNo_admin] [08d9b13304e43195/b7c95989c530e79a] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:19.563 [OrganNo_000001_UserNo_admin] [e258ac5c7c6258dc/492864db48fd0a9f] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-02-08 11:52:19.581 [OrganNo_000001_UserNo_admin] [e258ac5c7c6258dc/492864db48fd0a9f] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 11:52:19.602 [OrganNo_000001_UserNo_admin] [5116cadcab6dce45/2d5429857d025df9] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 11:52:19.615 [OrganNo_000001_UserNo_admin] [5116cadcab6dce45/2d5429857d025df9] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:19.640 [OrganNo_000001_UserNo_admin] [320c0a1ef8912e06/4e3195fecd91bb0b] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_WAREHOUSE"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 11:52:19.661 [OrganNo_000001_UserNo_admin] [320c0a1ef8912e06/4e3195fecd91bb0b] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:20.884 [OrganNo_000001_UserNo_admin] [215a7ea46e1ac203/5fba7c0b2065103e] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:20.897 [OrganNo_000001_UserNo_admin] [215a7ea46e1ac203/5fba7c0b2065103e] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:20.916 [OrganNo_000001_UserNo_admin] [ddb27358b1314af4/b44f808568289c6d] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"trunkDestroyQuery",
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 11:52:20.938 [OrganNo_000001_UserNo_admin] [ddb27358b1314af4/b44f808568289c6d] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:20.953 [OrganNo_000001_UserNo_admin] [363ed8141e609127/d0e27e0e646aa0ab] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_DESTORY"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:20.974 [OrganNo_000001_UserNo_admin] [363ed8141e609127/d0e27e0e646aa0ab] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:21.632 [OrganNo_000001_UserNo_admin] [ba4a515d2fdf5f3c/99dde86368aabc3a] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_DESTORY"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:21.654 [OrganNo_000001_UserNo_admin] [ba4a515d2fdf5f3c/99dde86368aabc3a] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:21.669 [OrganNo_000001_UserNo_admin] [6794649b31d509bb/134aba97926bdd6b] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"IS_DESTORY"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 11:52:21.689 [OrganNo_000001_UserNo_admin] [6794649b31d509bb/134aba97926bdd6b] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 11:52:22.916 [OrganNo_000001_UserNo_admin] [d1ea21063102dfe1/2ea74d57d9a58473] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:22.929 [OrganNo_000001_UserNo_admin] [d1ea21063102dfe1/2ea74d57d9a58473] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:22.954 [OrganNo_000001_UserNo_admin] [7277915ff61ed008/56b4a773640e8287] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-02-08 11:52:22.967 [OrganNo_000001_UserNo_admin] [7277915ff61ed008/56b4a773640e8287] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							},
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							},
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-02-08 11:52:22.994 [OrganNo_000001_UserNo_admin] [28cc605b9b4e015c/a49de48b2b366741] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"request"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 11:52:23.016 [OrganNo_000001_UserNo_admin] [28cc605b9b4e015c/a49de48b2b366741] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:23.537 [OrganNo_000001_UserNo_admin] [006c5bc32cb46a27/58ad457ea130cc31] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:23.547 [OrganNo_000001_UserNo_admin] [006c5bc32cb46a27/58ad457ea130cc31] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:23.562 [OrganNo_000001_UserNo_admin] [8f17eb5cb6f533a7/07dd0194a754a964] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-02-08 11:52:23.569 [OrganNo_000001_UserNo_admin] [8f17eb5cb6f533a7/07dd0194a754a964] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							},
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							},
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-02-08 11:52:23.591 [OrganNo_000001_UserNo_admin] [ea489d9c62edd075/0952d5fc398134ec] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"audit"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 11:52:23.609 [OrganNo_000001_UserNo_admin] [ea489d9c62edd075/0952d5fc398134ec] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:24.164 [OrganNo_000001_UserNo_admin] [c8d1e0f49fbbfe0c/a704837f935e7a08] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:24.179 [OrganNo_000001_UserNo_admin] [c8d1e0f49fbbfe0c/a704837f935e7a08] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:24.194 [OrganNo_000001_UserNo_admin] [1fcf24e52cd50b90/42454ad0c7d76250] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-02-08 11:52:24.201 [OrganNo_000001_UserNo_admin] [1fcf24e52cd50b90/42454ad0c7d76250] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							},
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							},
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-02-08 11:52:24.226 [OrganNo_000001_UserNo_admin] [8bf19c4691904eb8/b697f77092ac2560] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"response"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 11:52:24.246 [OrganNo_000001_UserNo_admin] [8bf19c4691904eb8/b697f77092ac2560] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:24.871 [OrganNo_000001_UserNo_admin] [adcd14416a78fb43/7614449f6ec3a53d] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 11:52:24.883 [OrganNo_000001_UserNo_admin] [adcd14416a78fb43/7614449f6ec3a53d] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 11:52:24.912 [OrganNo_000001_UserNo_admin] [1bda4eeb71312885/de21c01952bcd9f8] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"recordStates":[
			"0",
			"1",
			"2"
		]
	}
}
2025-02-08 11:52:24.937 [OrganNo_000001_UserNo_admin] [1bda4eeb71312885/de21c01952bcd9f8] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:45.756 [OrganNo_000001_UserNo_admin] [7a7a958261734ddb/24517eddffe67fdc] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:45.804 [OrganNo_000001_UserNo_admin] [7a7a958261734ddb/24517eddffe67fdc] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:45.834 [OrganNo_000001_UserNo_admin] [ec553f4721a9391c/3331834e29837528] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-02-08 12:14:45.855 [OrganNo_000001_UserNo_admin] [ec553f4721a9391c/3331834e29837528] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:46.196 [OrganNo_000001_UserNo_admin] [41a364bec17e828a/54dacecc9ff30344] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:46.206 [OrganNo_000001_UserNo_admin] [41a364bec17e828a/54dacecc9ff30344] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:46.229 [OrganNo_000001_UserNo_admin] [0bb7ebf0d4911dda/f4178e693bbc4a3d] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-02-08 12:14:46.247 [OrganNo_000001_UserNo_admin] [0bb7ebf0d4911dda/f4178e693bbc4a3d] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:46.686 [OrganNo_000001_UserNo_admin] [cc408e77b1964708/d51034b5301f1e3b] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:46.695 [OrganNo_000001_UserNo_admin] [cc408e77b1964708/d51034b5301f1e3b] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:46.718 [OrganNo_000001_UserNo_admin] [cea6e1a11b7c9dc7/8733072427dcacf4] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-02-08 12:14:46.738 [OrganNo_000001_UserNo_admin] [cea6e1a11b7c9dc7/8733072427dcacf4] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:46.760 [OrganNo_000001_UserNo_admin] [27920ead1b506c30/77aa74c8eac33018] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-02-08 12:14:46.772 [OrganNo_000001_UserNo_admin] [27920ead1b506c30/77aa74c8eac33018] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:47.542 [OrganNo_000001_UserNo_admin] [3cd01c1730058735/435b906f19212f74] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-02-08 12:14:47.559 [OrganNo_000001_UserNo_admin] [3cd01c1730058735/435b906f19212f74] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:14:47.573 [OrganNo_000001_UserNo_admin] [785b580d298d54b2/72ee0fd5f743fe56] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:47.582 [OrganNo_000001_UserNo_admin] [785b580d298d54b2/72ee0fd5f743fe56] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:47.610 [OrganNo_000001_UserNo_admin] [019f1f5467066b00/b10d1faf95d59707] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"queryType":"1",
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 12:14:47.622 [OrganNo_000001_UserNo_admin] [019f1f5467066b00/b10d1faf95d59707] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:47.635 [OrganNo_000001_UserNo_admin] [542f876451854000/ffd16eead090bee7] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"queryType":"1",
		"pageSize":15,
		"currentPage":1,
		"trunkStates":[
			"4",
			"5"
		]
	}
}
2025-02-08 12:14:47.657 [OrganNo_000001_UserNo_admin] [542f876451854000/ffd16eead090bee7] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:48.006 [OrganNo_000001_UserNo_admin] [f6a9f7b12e612ec2/db5c04642c92f172] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:48.014 [OrganNo_000001_UserNo_admin] [f6a9f7b12e612ec2/db5c04642c92f172] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:48.027 [OrganNo_000001_UserNo_admin] [1323f21ae0108d04/87b55b58df1130af] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-02-08 12:14:48.043 [OrganNo_000001_UserNo_admin] [1323f21ae0108d04/87b55b58df1130af] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:14:48.064 [OrganNo_000001_UserNo_admin] [f486d89f86787b11/d66d15953e529ccd] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"ALREADY_WAREHOUSE"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 12:14:48.082 [OrganNo_000001_UserNo_admin] [f486d89f86787b11/d66d15953e529ccd] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:48.103 [OrganNo_000001_UserNo_admin] [e615f2409fe6cce8/73d3c3ea94ee3672] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"trunkStates":[
			"4",
			"5"
		],
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 12:14:48.122 [OrganNo_000001_UserNo_admin] [e615f2409fe6cce8/73d3c3ea94ee3672] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:48.873 [OrganNo_000001_UserNo_admin] [0a99ebd2b9d51ba1/7330cc4217ca7094] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:48.884 [OrganNo_000001_UserNo_admin] [0a99ebd2b9d51ba1/7330cc4217ca7094] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:48.899 [OrganNo_000001_UserNo_admin] [71402f8e8b486a6f/4a505640f98ff754] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-02-08 12:14:48.905 [OrganNo_000001_UserNo_admin] [71402f8e8b486a6f/4a505640f98ff754] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							},
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							},
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-02-08 12:14:48.929 [OrganNo_000001_UserNo_admin] [0de222c4c0c3bf57/dd375c9e12d3ea40] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"request"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 12:14:48.943 [OrganNo_000001_UserNo_admin] [0de222c4c0c3bf57/dd375c9e12d3ea40] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:49.298 [OrganNo_000001_UserNo_admin] [536660169c09573a/8ccd235ca74b5792] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:49.307 [OrganNo_000001_UserNo_admin] [536660169c09573a/8ccd235ca74b5792] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:49.321 [OrganNo_000001_UserNo_admin] [fe3d3030c61bb102/c2401455708c5bcb] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-02-08 12:14:49.325 [OrganNo_000001_UserNo_admin] [fe3d3030c61bb102/c2401455708c5bcb] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							},
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							},
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-02-08 12:14:49.346 [OrganNo_000001_UserNo_admin] [384b16495bd4f85e/406be7962be8ee46] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"audit"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 12:14:49.362 [OrganNo_000001_UserNo_admin] [384b16495bd4f85e/406be7962be8ee46] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:49.724 [OrganNo_000001_UserNo_admin] [81e4ccaeb8c3543c/8c17e51c814104e5] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:49.733 [OrganNo_000001_UserNo_admin] [81e4ccaeb8c3543c/8c17e51c814104e5] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:49.746 [OrganNo_000001_UserNo_admin] [37908a3e35f615f8/01e01f5cdadc88c5] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-02-08 12:14:49.750 [OrganNo_000001_UserNo_admin] [37908a3e35f615f8/01e01f5cdadc88c5] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							},
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							},
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-02-08 12:14:49.772 [OrganNo_000001_UserNo_admin] [592650dc0dd05f0a/35961c9e426baa2d] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"response"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 12:14:49.787 [OrganNo_000001_UserNo_admin] [592650dc0dd05f0a/35961c9e426baa2d] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:50.093 [OrganNo_000001_UserNo_admin] [2bf77eb956fe389f/77e9097ccf534c8d] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:50.105 [OrganNo_000001_UserNo_admin] [2bf77eb956fe389f/77e9097ccf534c8d] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:50.129 [OrganNo_000001_UserNo_admin] [a5b9ee9d172d856a/b0cbd6a8fa0d23bd] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"recordStates":[
			"0",
			"1",
			"2"
		]
	}
}
2025-02-08 12:14:50.148 [OrganNo_000001_UserNo_admin] [a5b9ee9d172d856a/b0cbd6a8fa0d23bd] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:50.956 [OrganNo_000001_UserNo_admin] [79280b806a699e0b/8c01e048ed88c2c0] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:50.967 [OrganNo_000001_UserNo_admin] [79280b806a699e0b/8c01e048ed88c2c0] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:50.982 [OrganNo_000001_UserNo_admin] [9b5b8f75c4cdc785/d3d9e8acde9fa80f] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"trunkDestroyQuery",
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 12:14:51.002 [OrganNo_000001_UserNo_admin] [9b5b8f75c4cdc785/d3d9e8acde9fa80f] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:51.016 [OrganNo_000001_UserNo_admin] [04b8bc2830151bc3/c40a3502cd01556d] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_DESTORY"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:14:51.036 [OrganNo_000001_UserNo_admin] [04b8bc2830151bc3/c40a3502cd01556d] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:51.373 [OrganNo_000001_UserNo_admin] [3a01799503458a39/1aa82373a6950c3b] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_DESTORY"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:14:51.394 [OrganNo_000001_UserNo_admin] [3a01799503458a39/1aa82373a6950c3b] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:51.410 [OrganNo_000001_UserNo_admin] [cb08fcf03cf3c081/913421ee6690eea8] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"IS_DESTORY"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:14:51.429 [OrganNo_000001_UserNo_admin] [cb08fcf03cf3c081/913421ee6690eea8] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:52.332 [OrganNo_000001_UserNo_admin] [6336b55b9c2a1f63/716ad51437326e71] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:52.342 [OrganNo_000001_UserNo_admin] [6336b55b9c2a1f63/716ad51437326e71] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:52.364 [OrganNo_000001_UserNo_admin] [c8b6c08cc91eae35/cc083b5ea7bfd0f1] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-02-08 12:14:52.378 [OrganNo_000001_UserNo_admin] [c8b6c08cc91eae35/cc083b5ea7bfd0f1] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:52.722 [OrganNo_000001_UserNo_admin] [1be3bfe1d34d88bf/df67c8c44c767e43] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:52.732 [OrganNo_000001_UserNo_admin] [1be3bfe1d34d88bf/df67c8c44c767e43] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:52.745 [OrganNo_000001_UserNo_admin] [6a3f1cdad05b580a/5b43ff4b93543bf4] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_4"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1",
		"isCenter":"1"
	}
}
2025-02-08 12:14:52.765 [OrganNo_000001_UserNo_admin] [6a3f1cdad05b580a/5b43ff4b93543bf4] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:52.779 [OrganNo_000001_UserNo_admin] [89aacd9e93dc1962/8092053a52731e46] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"packageState":"FM_STATE_PACK_1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:14:52.801 [OrganNo_000001_UserNo_admin] [89aacd9e93dc1962/8092053a52731e46] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:53.133 [OrganNo_000001_UserNo_admin] [b564c532d73f499a/01798223dcd5a02a] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:53.143 [OrganNo_000001_UserNo_admin] [b564c532d73f499a/01798223dcd5a02a] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:53.156 [OrganNo_000001_UserNo_admin] [cc093ad34426d08b/43d9104af12e4008] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"packageState":"FM_STATE_PACK_1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:14:53.174 [OrganNo_000001_UserNo_admin] [cc093ad34426d08b/43d9104af12e4008] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:53.187 [OrganNo_000001_UserNo_admin] [4f16669f41b7f438/54d78337699cfa58] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_WAREHOUSE"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:14:53.205 [OrganNo_000001_UserNo_admin] [4f16669f41b7f438/54d78337699cfa58] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:53.478 [OrganNo_000001_UserNo_admin] [0c05bffc667ee931/4326b98df3136614] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:53.488 [OrganNo_000001_UserNo_admin] [0c05bffc667ee931/4326b98df3136614] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:53.502 [OrganNo_000001_UserNo_admin] [495092ba24d16909/b6e2361510e38e97] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-02-08 12:14:53.518 [OrganNo_000001_UserNo_admin] [495092ba24d16909/b6e2361510e38e97] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:14:53.538 [OrganNo_000001_UserNo_admin] [b1a7b238e606e534/fcc04207491d81f5] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 12:14:53.550 [OrganNo_000001_UserNo_admin] [b1a7b238e606e534/fcc04207491d81f5] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:53.572 [OrganNo_000001_UserNo_admin] [8be103e063496bde/28dc3f9f44d650e2] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_WAREHOUSE"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1
	}
}
2025-02-08 12:14:53.594 [OrganNo_000001_UserNo_admin] [8be103e063496bde/28dc3f9f44d650e2] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:55.285 [OrganNo_000001_UserNo_admin] [c27f234bfe299e78/bec594c611ecdc15] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 12:14:55.304 [OrganNo_000001_UserNo_admin] [c27f234bfe299e78/bec594c611ecdc15] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:14:55.608 [OrganNo_000001_UserNo_admin] [a3db298c6d4b0bbb/69f88c4bb0b5b676] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":15
	}
}
2025-02-08 12:14:55.623 [OrganNo_000001_UserNo_admin] [a3db298c6d4b0bbb/69f88c4bb0b5b676] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:14:55.637 [OrganNo_000001_UserNo_admin] [2b00c14c43605aaf/0e488a4274a0a60e] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 12:14:55.649 [OrganNo_000001_UserNo_admin] [2b00c14c43605aaf/0e488a4274a0a60e] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:14:56.880 [OrganNo_000001_UserNo_admin] [dec492e5f4ab4de2/a72ba12343b31470] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 12:14:56.889 [OrganNo_000001_UserNo_admin] [dec492e5f4ab4de2/a72ba12343b31470] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:58.240 [OrganNo_000001_UserNo_admin] [ee14d0f8654e6a4b/7204f7de7f7e6661] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:14:58.249 [OrganNo_000001_UserNo_admin] [ee14d0f8654e6a4b/7204f7de7f7e6661] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:14:58.261 [OrganNo_000001_UserNo_admin] [5c17343ef8dd9602/0f137bd1ed5acad1] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"appLocationQuery",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:14:58.293 [OrganNo_000001_UserNo_admin] [5c17343ef8dd9602/0f137bd1ed5acad1] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:15:02.083 [OrganNo_000001_UserNo_admin] [b18d63845768ca31/96108be2807e04df] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 12:15:02.099 [OrganNo_000001_UserNo_admin] [b18d63845768ca31/96108be2807e04df] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:15:02.457 [OrganNo_000001_UserNo_admin] [c314e3c54c31f10d/d338b2d2937e3936] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 12:15:02.467 [OrganNo_000001_UserNo_admin] [c314e3c54c31f10d/d338b2d2937e3936] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 12:15:02.844 [OrganNo_000001_UserNo_admin] [2aa038ceb6f094f9/09c537b9dbbdc534] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 12:15:02.856 [OrganNo_000001_UserNo_admin] [2aa038ceb6f094f9/09c537b9dbbdc534] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:15:03.226 [OrganNo_000001_UserNo_admin] [a05391a297536549/043d34b94d01f125] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":0
	}
}
2025-02-08 12:15:03.243 [OrganNo_000001_UserNo_admin] [a05391a297536549/043d34b94d01f125] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:15:03.255 [OrganNo_000001_UserNo_admin] [2dd73617a22e5bef/a1ee46bb9965347f] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":1
	}
}
2025-02-08 12:15:03.263 [OrganNo_000001_UserNo_admin] [2dd73617a22e5bef/a1ee46bb9965347f] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:15:03.583 [OrganNo_000001_UserNo_admin] [13f20331ffabbf1a/b67f5f20b3eb6160] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:15:03.591 [OrganNo_000001_UserNo_admin] [13f20331ffabbf1a/b67f5f20b3eb6160] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-02-08 12:15:03.950 [OrganNo_000001_UserNo_admin] [ba3a378b7871056e/21fff261370dff87] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:15:03.959 [OrganNo_000001_UserNo_admin] [ba3a378b7871056e/21fff261370dff87] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:15:04.307 [OrganNo_000001_UserNo_admin] [0aab4afd9f932334/2fb998a29deac23e] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 12:15:04.315 [OrganNo_000001_UserNo_admin] [0aab4afd9f932334/2fb998a29deac23e] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 12:15:04.328 [OrganNo_000001_UserNo_admin] [55d6711b68cd925e/009307ebe1a3876b] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-02-08 12:15:04.345 [OrganNo_000001_UserNo_admin] [55d6711b68cd925e/009307ebe1a3876b] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 15:02:40.878 [OrganNo_000001_UserNo_admin] [b709859e03a21843/cc18edc644fdb57b] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-02-08 15:02:40.935 [OrganNo_000001_UserNo_admin] [b709859e03a21843/cc18edc644fdb57b] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-02-08 15:02:40.954 [OrganNo_000001_UserNo_admin] [ed413bb0f6b5d26e/0d448c4304bcd362] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 15:02:40.969 [OrganNo_000001_UserNo_admin] [ed413bb0f6b5d26e/0d448c4304bcd362] [http-nio-9009-exec-5] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 15:02:41.000 [OrganNo_000001_UserNo_admin] [c91e1a81ab86d222/4d536a9aca799a96] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"queryType":"1",
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-02-08 15:02:41.014 [OrganNo_000001_UserNo_admin] [c91e1a81ab86d222/4d536a9aca799a96] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 15:02:41.028 [OrganNo_000001_UserNo_admin] [5b3b66578e063c68/65e598f41b664220] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"queryType":"1",
		"pageSize":15,
		"currentPage":1,
		"trunkStates":[
			"4",
			"5"
		]
	}
}
2025-02-08 15:02:41.052 [OrganNo_000001_UserNo_admin] [5b3b66578e063c68/65e598f41b664220] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-02-08 15:02:42.819 [OrganNo_000001_UserNo_admin] [0293c5313d9b5a58/2ac2f8216701a69d] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-02-08 15:02:42.832 [OrganNo_000001_UserNo_admin] [0293c5313d9b5a58/2ac2f8216701a69d] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-02-08 15:02:42.856 [OrganNo_000001_UserNo_admin] [92697bd832a3f48a/5aafaebdde2d1a16] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-02-08 15:02:42.883 [OrganNo_000001_UserNo_admin] [92697bd832a3f48a/5aafaebdde2d1a16] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
