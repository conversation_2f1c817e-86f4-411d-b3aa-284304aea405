package com.sunyard.client.bean.converter;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.collections.map.LinkedMap;

import com.sunyard.ws.utils.XMLUtil;
import com.thoughtworks.xstream.converters.Converter;
import com.thoughtworks.xstream.converters.MarshallingContext;
import com.thoughtworks.xstream.converters.UnmarshallingContext;
import com.thoughtworks.xstream.io.HierarchicalStreamReader;
import com.thoughtworks.xstream.io.HierarchicalStreamWriter;
/**
 * <p>
 * Title:xstream 转换器
 * </p>
 * <p>
 * Description: 讲对象中的字典属性转化成标准xml格式
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ClientStringCustomConverter implements Converter {

	/**
	 * Marshal方法是用来将对象转换为XML的，他有三个参数： 
     *       1，我们准备转换的对象 value
     *       2，我们准备输出对象的writer 
     *       3，当前的marshaling context
	 */
	public void marshal(Object value, HierarchicalStreamWriter writer,
			MarshallingContext context) {
		map2xml(value, writer, context);
	}

	protected void map2xml(Object value, HierarchicalStreamWriter writer,
            MarshallingContext context) {
		Map map = (Map) value;
        for (Iterator<Entry<String, Object>> iterator = map.entrySet()
                .iterator(); iterator.hasNext();) {
            Entry<String, Object> entry = (Entry<String, Object>) iterator
                    .next();
            String key = (String) entry.getKey();
            Object subvalue = entry.getValue();
            writer.startNode(key);
            if (canConvert(subvalue.getClass())) {
                map2xml(subvalue, writer, context);
            } else {
            	writer.setValue(XMLUtil.bean2XML(subvalue));
            }
            writer.endNode();
        }
	}
	
	/**
	 * Unmarshal方法是用来将XML转换为对象，他有两个参数： 
     *       1，我们准备读取对象的reader 
     *       2，当前的marshaling context
	 */
	public Object unmarshal(HierarchicalStreamReader reader,
			UnmarshallingContext context) {
		Map<String, String> map = (Map<String, String>) populateMap(reader,
                context);
		return map;
	}
	
	protected Object populateMap(HierarchicalStreamReader reader,
            UnmarshallingContext context){
		Map<String, String> map = new HashMap<String, String>();
		String answer = "";
		String value = null;
		while (reader.hasMoreChildren()){
			reader.moveDown();
			answer = reader.getNodeName();
			value = convertAttributeBean(reader, context);
			reader.moveUp();
			map.put(answer, value);
		}
		return map;
	}
	
	protected String convertAttributeBean(HierarchicalStreamReader reader, UnmarshallingContext context){
		String str = new String();
		reader.moveDown();
		str = (String) context.convertAnother(str, String.class);
		reader.moveUp();
		return str;
	}

	/**
	 * 转换类匹配
	 */
	public boolean canConvert(Class clazz) {
		return clazz.equals(Map.class)||clazz.equals(HashMap.class)||clazz.equals(LinkedMap.class);
	}

}