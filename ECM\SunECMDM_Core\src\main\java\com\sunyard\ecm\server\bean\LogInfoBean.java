package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

@XStreamAlias("LogInfoBean")
public class LogInfoBean {
	
	@XStreamAsAttribute
	private String SERVER_ID;
	@XStreamAsAttribute
	private String SERVER_NAME;
	@XStreamAsAttribute
	private String LOG_NAME;
	@XStreamAsAttribute
	private String LOG_PATH;
	@XStreamAsAttribute
	private String URL;
	public String getServer_id() {
		return SERVER_ID;
	}
	public void setServer_id(String server_id) {
		this.SERVER_ID = server_id;
	}
	public String getServer_name() {
		return SERVER_NAME;
	}
	public void setServer_name(String server_name) {
		this.SERVER_NAME = server_name;
	}
	public String getLog_name() {
		return LOG_NAME;
	}
	public void setLog_name(String log_name) {
		this.LOG_NAME = log_name;
	}
	public String getLog_path() {
		return LOG_PATH;
	}
	public void setLog_path(String log_path) {
		this.LOG_PATH = log_path;
	}
	public String getUrl() {
		return URL;
	}
	public void setUrl(String url) {
		this.URL = url;
	}
	
}