<?xml version="1.0" encoding="UTF-8" ?>
<ROOT>
	<!-- 在DM数据库中查询最大版本，包括所有使用DM数据库的组 -->
	<SQL name="getMaxVersionInDMDB"><![CDATA[ 
        SELECT MAX(VERSION) AS MAXVERSION
		FROM {0} T
		WHERE
		T.CONTENT_ID= #{ContentID}
		AND T.CONTENT_STATUS=1
		AND T.MIGRATION_STATUS
		!= #{ALREADYOFFLINECLEAR}
]]>

	</SQL>

	<!-- 在DM数据库中查询批次是否存在，包括所有使用DM数据库的组 -->
	<SQL name="isBatchExistInDMDB"><![CDATA[ 
        select content_id from {0} t where
		content_id=#{contentId} and
		CONTENT_STATUS = '1'
]]>
	</SQL>

	<!-- 查询非DM数据库存储信息的服务器 -->
	<SQL name="getExtStoreGroup"><![CDATA[ 
    SELECT  scr.MODEL_CODE as MODEL_CODE,csg.GROUP_NAME  as GROUP_NAME
    FROM content_server_group csg INNER JOIN sgroup_cmodel_rel scr 
    ON csg.GROUP_ID=scr.GROUP_ID  AND csg.IS_ECM_DB=0
]]>

	</SQL>

	<!--在DM数据库中查询最大版本和所在服务器，包括所有使用DM数据库的组 -->
	<SQL name="POSTGRESQL_getMaxVersionAndGroupInDMDB"><![CDATA[
   SELECT T.VERSION, G.GROUP_NAME ,T.GROUP_ID ,T.MIGRATION_STATUS FROM CONTENT_SERVER_GROUP G,{0} T ,
    (SELECT MAX(VERSION) AS MAXVERSION FROM {0} WHERE CONTENT_ID = #{contentId} AND CONTENT_STATUS = '1'  AND IS_LAST_VERSION='1') MSQL
   WHERE T.CONTENT_ID = #{contentId}
   AND  T.VERSION=MSQL.MAXVERSION
   AND G.GROUP_ID=cast(T.GROUP_ID as NUMERIC)
   AND T.CONTENT_STATUS = '1'
]]>
	</SQL>

	<!--在DM数据库中查询最大版本和所在服务器，包括所有使用DM数据库的组 -->
	<SQL name="MYSQL_getMaxVersionAndGroupInDMDB"><![CDATA[ 
   SELECT T.VERSION, G.GROUP_NAME ,T.GROUP_ID ,T.MIGRATION_STATUS FROM CONTENT_SERVER_GROUP G,{0} T , 
    (SELECT MAX(VERSION) AS MAXVERSION FROM {0} WHERE CONTENT_ID = #{contentId} AND CONTENT_STATUS = '1'  AND IS_LAST_VERSION='1') MSQL 
   WHERE T.CONTENT_ID = #{contentId}
   AND  T.VERSION=MSQL.MAXVERSION
   AND G.GROUP_ID=cast(T.GROUP_ID as char)
   AND T.CONTENT_STATUS = '1' 
]]>
</SQL>
		<!--在DM数据库中查询最大版本和所在服务器，包括所有使用DM数据库的组 -->
		<SQL name="DB2_getMaxVersionAndGroupInDMDB"><![CDATA[ 
   SELECT T.VERSION, G.GROUP_NAME ,T.GROUP_ID,T.MIGRATION_STATUS FROM CONTENT_SERVER_GROUP G,{0} T , 
    (SELECT MAX(VERSION) AS MAXVERSION FROM {0} WHERE CONTENT_ID = #{contentId} AND CONTENT_STATUS = '1'  AND IS_LAST_VERSION='1') MSQL 
   WHERE T.CONTENT_ID = #{contentId}
   AND  T.VERSION=MSQL.MAXVERSION
   AND G.GROUP_ID=int(T.GROUP_ID)
   AND T.CONTENT_STATUS = 1 
]]>

		</SQL>

		<!--在DM数据库中查询最大版本和所在服务器，包括所有使用DM数据库的组 -->
		<SQL name="getMaxVersionAndGroupInDMDB"><![CDATA[ 
   SELECT T.VERSION, G.GROUP_NAME ,T.GROUP_ID,T.MIGRATION_STATUS FROM CONTENT_SERVER_GROUP G,{0} T , 
    (SELECT MAX(VERSION) AS MAXVERSION FROM {0} WHERE CONTENT_ID = #{contentId} AND CONTENT_STATUS = '1' AND IS_LAST_VERSION='1') MSQL 
   WHERE T.CONTENT_ID = #{contentId}
   AND  T.VERSION=MSQL.MAXVERSION
   AND G.GROUP_ID=T.GROUP_ID
   AND T.CONTENT_STATUS = '1' 
]]>

		</SQL>


		<!--在DM数据库中查询是否离线，包括所有使用DM数据库的组 -->
		<SQL name="isOfflineInDMDB"><![CDATA[ 
   select content_id from {0} t where content_id=?  and migration_status in (?,?)
]]>
		</SQL>

		<!--在DM数据库中删除批次，包括所有使用DM数据库的组 -->
		<SQL name="deleteInDmdb"><![CDATA[ 
      UPDATE  {0}  SET CONTENT_STATUS=0  WHERE CONTENT_ID=?  AND CONTENT_STATUS !=0
]]>

		</SQL>


</ROOT>