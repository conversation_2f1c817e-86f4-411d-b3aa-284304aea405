2025-07-18 00:00:27.744 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:05:27.755 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:07:01.306 [OrganNo_00023_UserNo_admin] [adf2dd3d36e5ae14/980d2cc672c959f5] [http-nio-9009-exec-34] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-18 00:07:01.336 [OrganNo_00023_UserNo_admin] [adf2dd3d36e5ae14/6b5d4379a395d8b5] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-18 00:07:01.337 [OrganNo_00023_UserNo_admin] [adf2dd3d36e5ae14/6b5d4379a395d8b5] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-18 00:07:01.340 [OrganNo_00023_UserNo_admin] [adf2dd3d36e5ae14/6b5d4379a395d8b5] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-18 00:07:01.341 [OrganNo_00023_UserNo_admin] [adf2dd3d36e5ae14/6b5d4379a395d8b5] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-18 00:07:01.341 [OrganNo_00023_UserNo_admin] [adf2dd3d36e5ae14/6b5d4379a395d8b5] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-18 00:07:01.342 [OrganNo_00023_UserNo_admin] [adf2dd3d36e5ae14/6b5d4379a395d8b5] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-18 00:07:01.360 [OrganNo_00023_UserNo_admin] [adf2dd3d36e5ae14/980d2cc672c959f5] [http-nio-9009-exec-34] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-18 00:07:01.361 [] [adf2dd3d36e5ae14/980d2cc672c959f5] [http-nio-9009-exec-34] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:07:01 操作结束时间: 2025-07-18 00:07:01!总共花费时间: 64 毫秒！
2025-07-18 00:07:01.452 [] [36f8b3246d631c44/6aa9a9aaa7254db6] [http-nio-9009-exec-35] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:07:01 操作结束时间: 2025-07-18 00:07:01!总共花费时间: 66 毫秒！
2025-07-18 00:07:01.513 [] [414d70143e4c1710/33d8121a9c66fa2a] [http-nio-9009-exec-36] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:07:01 操作结束时间: 2025-07-18 00:07:01!总共花费时间: 41 毫秒！
2025-07-18 00:10:27.760 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:15:27.771 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:20:27.781 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:25:27.791 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:30:27.805 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:35:27.821 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:40:27.829 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:40:47.610 [OrganNo_00023_UserNo_admin] [2cc0acc99b2f67b4/870a73eae05d95dd] [http-nio-9009-exec-42] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-18 00:40:47.633 [OrganNo_00023_UserNo_admin] [2cc0acc99b2f67b4/9f2d6e92073c1cb8] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-18 00:40:47.635 [OrganNo_00023_UserNo_admin] [2cc0acc99b2f67b4/9f2d6e92073c1cb8] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-18 00:40:47.638 [OrganNo_00023_UserNo_admin] [2cc0acc99b2f67b4/9f2d6e92073c1cb8] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-18 00:40:47.639 [OrganNo_00023_UserNo_admin] [2cc0acc99b2f67b4/9f2d6e92073c1cb8] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-18 00:40:47.639 [OrganNo_00023_UserNo_admin] [2cc0acc99b2f67b4/9f2d6e92073c1cb8] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-18 00:40:47.640 [OrganNo_00023_UserNo_admin] [2cc0acc99b2f67b4/9f2d6e92073c1cb8] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-18 00:40:47.657 [OrganNo_00023_UserNo_admin] [2cc0acc99b2f67b4/870a73eae05d95dd] [http-nio-9009-exec-42] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-18 00:40:47.658 [] [2cc0acc99b2f67b4/870a73eae05d95dd] [http-nio-9009-exec-42] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:40:47 操作结束时间: 2025-07-18 00:40:47!总共花费时间: 58 毫秒！
2025-07-18 00:40:47.752 [] [3d0084fd9e832b76/4545077a26e4c0b5] [http-nio-9009-exec-43] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:40:47 操作结束时间: 2025-07-18 00:40:47!总共花费时间: 69 毫秒！
2025-07-18 00:40:47.828 [] [42314db68720798e/80cf97706ef4ff7d] [http-nio-9009-exec-44] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:40:47 操作结束时间: 2025-07-18 00:40:47!总共花费时间: 56 毫秒！
2025-07-18 00:40:49.768 [] [5e9c601e3e7df669/f12493bc519ba4f1] [http-nio-9009-exec-45] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 00:40:49 操作结束时间: 2025-07-18 00:40:49!总共花费时间: 50 毫秒！
2025-07-18 00:43:45.207 [] [041b837fe729831e/c9b1b01c5c5fb18c] [http-nio-9009-exec-47] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:43:45 操作结束时间: 2025-07-18 00:43:45!总共花费时间: 32 毫秒！
2025-07-18 00:43:45.285 [] [d69b1dfacb0fbfa2/1048eb5cc0bc3301] [http-nio-9009-exec-48] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:43:45 操作结束时间: 2025-07-18 00:43:45!总共花费时间: 58 毫秒！
2025-07-18 00:43:45.362 [] [ed4e6f6ab5f90be6/bbf5b2ddc41be7d5] [http-nio-9009-exec-49] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:43:45 操作结束时间: 2025-07-18 00:43:45!总共花费时间: 55 毫秒！
2025-07-18 00:43:46.951 [] [cf71f5e13b09c744/519e9890e13885cd] [http-nio-9009-exec-50] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 00:43:46 操作结束时间: 2025-07-18 00:43:46!总共花费时间: 48 毫秒！
2025-07-18 00:43:55.179 [] [96c9caf73abbc3a7/2da925be99a9a031] [http-nio-9009-exec-52] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:43:55 操作结束时间: 2025-07-18 00:43:55!总共花费时间: 35 毫秒！
2025-07-18 00:43:55.256 [] [7c3ba2c399aff655/702dd99a29fd922b] [http-nio-9009-exec-53] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:43:55 操作结束时间: 2025-07-18 00:43:55!总共花费时间: 58 毫秒！
2025-07-18 00:44:03.374 [] [ba51514f75b84371/7b9131220da2602c] [http-nio-9009-exec-55] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 柜员凭证登记 打印凭证封面!请求IP地址: ************** 操作开始时间: 2025-07-18 00:44:03 操作结束时间: 2025-07-18 00:44:03!总共花费时间: 29 毫秒！
2025-07-18 00:45:27.845 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:50:27.851 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:55:27.859 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 00:58:34.813 [] [0d9cbf788644a7cd/7222d0972c67b200] [http-nio-9009-exec-57] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:58:34 操作结束时间: 2025-07-18 00:58:34!总共花费时间: 71 毫秒！
2025-07-18 00:58:34.875 [] [482d012e09186db4/2242d20462975a7b] [http-nio-9009-exec-58] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:58:34 操作结束时间: 2025-07-18 00:58:34!总共花费时间: 44 毫秒！
2025-07-18 00:58:35.035 [] [3e522c10ee9888cf/91892d5ad0026767] [http-nio-9009-exec-59] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:58:34 操作结束时间: 2025-07-18 00:58:35!总共花费时间: 142 毫秒！
2025-07-18 00:58:37.280 [] [431587ab44484bd8/fa1ca77e0d38d2e5] [http-nio-9009-exec-60] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 校验交接袋是否可移交!请求IP地址: ************** 操作开始时间: 2025-07-18 00:58:37 操作结束时间: 2025-07-18 00:58:37!总共花费时间: 41 毫秒！
2025-07-18 00:58:42.613 [] [4815ed3bcd8e0ad7/df79d5f73cd7680b] [http-nio-9009-exec-62] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 交接袋业务 操作方法: 获取交接人列表!请求IP地址: ************** 操作开始时间: 2025-07-18 00:58:42 操作结束时间: 2025-07-18 00:58:42!总共花费时间: 54 毫秒！
2025-07-18 00:58:45.803 [] [3505e23264cd8f64/b3e01aae19c50592] [http-nio-9009-exec-64] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 00:58:45 操作结束时间: 2025-07-18 00:58:45!总共花费时间: 48 毫秒！
2025-07-18 00:58:48.561 [] [f95d1673a33cec32/a8d980388bf25076] [http-nio-9009-exec-65] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 00:58:48 操作结束时间: 2025-07-18 00:58:48!总共花费时间: 43 毫秒！
2025-07-18 00:59:09.624 [] [498a26c440efa8ed/a1b0d7bac3570f13] [http-nio-9009-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 00:59:09 操作结束时间: 2025-07-18 00:59:09!总共花费时间: 42 毫秒！
2025-07-18 01:00:27.871 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:05:25.629 [] [26ae7650ed62d917/15cc1fa2fc2e67d2] [http-nio-9009-exec-69] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:05:25 操作结束时间: 2025-07-18 01:05:25!总共花费时间: 50 毫秒！
2025-07-18 01:05:25.705 [] [859d14cabf816c9a/e26d304b80f5bd95] [http-nio-9009-exec-70] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:05:25 操作结束时间: 2025-07-18 01:05:25!总共花费时间: 58 毫秒！
2025-07-18 01:05:25.781 [] [c75671f75bb78133/7fc61461cd85ddc0] [http-nio-9009-exec-71] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:05:25 操作结束时间: 2025-07-18 01:05:25!总共花费时间: 51 毫秒！
2025-07-18 01:05:27.456 [] [408cd86b6bff1ba2/14cb1adc1ae5330c] [http-nio-9009-exec-72] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:05:27 操作结束时间: 2025-07-18 01:05:27!总共花费时间: 46 毫秒！
2025-07-18 01:05:27.883 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:06:02.452 [] [f1f88ce67a004138/d711bf1c48cb4c4e] [http-nio-9009-exec-74] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:06:02 操作结束时间: 2025-07-18 01:06:02!总共花费时间: 43 毫秒！
2025-07-18 01:10:27.891 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:13:00.639 [] [90ec2cc65f0d5687/edaf84d3f946a23f] [http-nio-9009-exec-76] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:13:00 操作结束时间: 2025-07-18 01:13:00!总共花费时间: 53 毫秒！
2025-07-18 01:13:00.683 [OrganNo_00023_UserNo_admin] [121311cddc99ed79/49d8b09f4c5e1d4c] [http-nio-9009-exec-77] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-07-18 01:13:00.706 [OrganNo_00023_UserNo_admin] [121311cddc99ed79/c919e2423ffdeb68] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE = ?
2025-07-18 01:13:00.707 [OrganNo_00023_UserNo_admin] [121311cddc99ed79/c919e2423ffdeb68] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String), 23(String)
2025-07-18 01:13:00.712 [OrganNo_00023_UserNo_admin] [121311cddc99ed79/c919e2423ffdeb68] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-07-18 01:13:00.712 [OrganNo_00023_UserNo_admin] [121311cddc99ed79/c919e2423ffdeb68] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-07-18 01:13:00.712 [OrganNo_00023_UserNo_admin] [121311cddc99ed79/c919e2423ffdeb68] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 23(String), 15(Integer)
2025-07-18 01:13:00.719 [OrganNo_00023_UserNo_admin] [121311cddc99ed79/c919e2423ffdeb68] [http-nio-9009-exec-77] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 6
2025-07-18 01:13:00.742 [OrganNo_00023_UserNo_admin] [121311cddc99ed79/49d8b09f4c5e1d4c] [http-nio-9009-exec-77] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250703113315953115",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250703113315953115",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"7b53c149f1554632aa28e6b39de3c054",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250703",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"熊涛",
				"tellerNo":"7567209",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704154204216347",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704154204216347",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"1b58ac266ca547d39bb835c9fda6c9f8",
				"isDel":"0",
				"oganName":"15089-中国银行德阳罗江支行",
				"oganNo":"15089",
				"registerDate":"20250704",
				"siteName":"中国银行德阳罗江支行营业部",
				"siteNo":"15442",
				"tellerName":"唐甜",
				"tellerNo":"1713854",
				"userName":"唐甜",
				"userNo":"1713854",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704155047593474",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704155047593474",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"b6c986d1289d4f68b9494c90cc6bc057",
				"isDel":"0",
				"oganName":"14913-中国银行成都高新技术产业开发区支行",
				"oganNo":"14913",
				"registerDate":"20250704",
				"siteName":"中国银行成都复城广场支行",
				"siteNo":"35944",
				"tellerName":"张诗琪",
				"tellerNo":"8767041",
				"userName":"张诗琪",
				"userNo":"8767041",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704152937239660",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704152937239660",
				"businessDate":"20240910",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640910",
				"endBusiDate":"20240910",
				"id":"faecdbe2bb4e4627bbd995430f9d8b6e",
				"isDel":"0",
				"oganName":"14901-中国银行成都蜀都大道支行",
				"oganNo":"14901",
				"registerDate":"20250704",
				"siteName":"中国银行成都实业街支行",
				"siteNo":"14902",
				"tellerName":"童心",
				"tellerNo":"1488420",
				"userName":"童心",
				"userNo":"1488420",
				"warrantAmount":"3"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250616140816157403",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250616140816157403",
				"businessDate":"20250616",
				"codeName":"保存40年 (默认)",
				"codeNo":"PZ01",
				"destroyDate":"20650616",
				"endBusiDate":"20250626",
				"id":"4ea6430cec6f44749695b61ecf80e3cd",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250616",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"23"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250626150955945916",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250626150955945916",
				"businessDate":"20250626",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20650626",
				"endBusiDate":"20250626",
				"id":"3cbbc289402245118f5451c1e418610e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250626",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"何文敏",
				"tellerNo":"3205888",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			}
		],
		"totalCount":6,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-18 01:13:00.743 [] [121311cddc99ed79/49d8b09f4c5e1d4c] [http-nio-9009-exec-77] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:13:00 操作结束时间: 2025-07-18 01:13:00!总共花费时间: 81 毫秒！
2025-07-18 01:13:00.836 [] [8cc2b25f6564bf93/86e935a44a418c43] [http-nio-9009-exec-78] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:13:00 操作结束时间: 2025-07-18 01:13:00!总共花费时间: 67 毫秒！
2025-07-18 01:13:02.408 [] [9cd5511887794e64/42d84f6313f5fecc] [http-nio-9009-exec-79] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:13:02 操作结束时间: 2025-07-18 01:13:02!总共花费时间: 63 毫秒！
2025-07-18 01:13:08.417 [OrganNo_00023_UserNo_admin] [70476f7f02c79db9/b2a4a8fa95d904b3] [http-nio-9009-exec-81] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleNo":"2025061600023001"
		}
	],
	"sysMap":{
		"oper_type":"printCode"
	}
}
2025-07-18 01:13:08.421 [OrganNo_00023_UserNo_admin] [70476f7f02c79db9/f50fea158ebe9444] [http-nio-9009-exec-81] DEBUG c.s.a.f.d.A.selectApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE BALE_NO = ?
2025-07-18 01:13:08.421 [OrganNo_00023_UserNo_admin] [70476f7f02c79db9/f50fea158ebe9444] [http-nio-9009-exec-81] DEBUG c.s.a.f.d.A.selectApplications - ==> Parameters: 2025061600023001(String)
2025-07-18 01:13:08.425 [OrganNo_00023_UserNo_admin] [70476f7f02c79db9/f50fea158ebe9444] [http-nio-9009-exec-81] DEBUG c.s.a.f.d.A.selectApplications - <==      Total: 1
2025-07-18 01:13:08.445 [OrganNo_00023_UserNo_admin] [70476f7f02c79db9/b2a4a8fa95d904b3] [http-nio-9009-exec-81] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"fileName":"2025061600023001.png",
		"imgPath":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/2025061600023001/2025061600023001.png",
		"returnList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_14",
				"baleNo":"2025061600023001",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"46e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"15370-中国银行四川省分行支付清算部",
				"oganNo":"15370",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行支付清算部",
				"siteNo":"15370",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		]
	},
	"retMsg":"生成成功"
}
2025-07-18 01:13:08.445 [] [70476f7f02c79db9/b2a4a8fa95d904b3] [http-nio-9009-exec-81] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:13:08 操作结束时间: 2025-07-18 01:13:08!总共花费时间: 43 毫秒！
2025-07-18 01:15:27.901 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:17:32.373 [] [455f9dafa50581ee/c3d41c18889eb9ba] [http-nio-9009-exec-83] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:17:32 操作结束时间: 2025-07-18 01:17:32!总共花费时间: 55 毫秒！
2025-07-18 01:17:32.460 [] [5d7edec754df46bf/24a0fe849cbee0f2] [http-nio-9009-exec-84] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:17:32 操作结束时间: 2025-07-18 01:17:32!总共花费时间: 62 毫秒！
2025-07-18 01:20:27.902 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:25:27.908 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:25:58.917 [] [dd63627cea576503/8b5e8c86b4a7a226] [http-nio-9009-exec-86] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:25:58 操作结束时间: 2025-07-18 01:25:58!总共花费时间: 144 毫秒！
2025-07-18 01:25:59.010 [] [4eb0941aa2fa1cc2/5561e67dfb3ef174] [http-nio-9009-exec-87] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:25:58 操作结束时间: 2025-07-18 01:25:59!总共花费时间: 71 毫秒！
2025-07-18 01:25:59.084 [] [fb965b563515df10/9be0b01d9d743175] [http-nio-9009-exec-88] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:25:59 操作结束时间: 2025-07-18 01:25:59!总共花费时间: 52 毫秒！
2025-07-18 01:26:01.276 [] [8c26cf0596e80075/7c623fbe8da2d6d9] [http-nio-9009-exec-89] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:01 操作结束时间: 2025-07-18 01:26:01!总共花费时间: 50 毫秒！
2025-07-18 01:26:11.506 [] [43f8b18579e08a89/14de6906bd625d05] [http-nio-9009-exec-91] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:11 操作结束时间: 2025-07-18 01:26:11!总共花费时间: 46 毫秒！
2025-07-18 01:26:23.147 [] [7d3efb42e19aeb8a/0d75cfeb5005452f] [http-nio-9009-exec-93] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 凭证移交中,添加时的查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:23 操作结束时间: 2025-07-18 01:26:23!总共花费时间: 33 毫秒！
2025-07-18 01:26:33.990 [] [9b60b76b0d2baba4/cc9a746e05cce131] [http-nio-9009-exec-95] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 凭证移交中,添加时的查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:33 操作结束时间: 2025-07-18 01:26:33!总共花费时间: 30 毫秒！
2025-07-18 01:26:46.162 [] [ce730f52555d5e97/5ee954168033c2ef] [http-nio-9009-exec-97] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 凭证移交中,添加时的查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:46 操作结束时间: 2025-07-18 01:26:46!总共花费时间: 33 毫秒！
2025-07-18 01:26:49.636 [] [a06a83b23c6ba868/4ca90c2f3571d636] [http-nio-9009-exec-99] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 修改!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:49 操作结束时间: 2025-07-18 01:26:49!总共花费时间: 40 毫秒！
2025-07-18 01:26:53.697 [] [7809ec0448e853a7/5cc6033d99b8a52e] [http-nio-9009-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:53 操作结束时间: 2025-07-18 01:26:53!总共花费时间: 36 毫秒！
2025-07-18 01:26:53.758 [] [0a0d7ec8d26c3e97/b8c29bf74f61d6d3] [http-nio-9009-exec-2] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:53 操作结束时间: 2025-07-18 01:26:53!总共花费时间: 46 毫秒！
2025-07-18 01:26:53.820 [] [c88d61b4bb96ee79/ddabb1520787239e] [http-nio-9009-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:53 操作结束时间: 2025-07-18 01:26:53!总共花费时间: 46 毫秒！
2025-07-18 01:26:56.746 [] [998c3bfa2f580c60/fd3bf5f5baa35829] [http-nio-9009-exec-4] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:26:56 操作结束时间: 2025-07-18 01:26:56!总共花费时间: 48 毫秒！
2025-07-18 01:29:34.484 [] [e08312110125bf4c/b98cb9aacce951ea] [http-nio-9009-exec-6] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:29:34 操作结束时间: 2025-07-18 01:29:34!总共花费时间: 45 毫秒！
2025-07-18 01:29:34.577 [] [b6cf34baaf5de111/427e253a5e2fbe59] [http-nio-9009-exec-7] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:29:34 操作结束时间: 2025-07-18 01:29:34!总共花费时间: 70 毫秒！
2025-07-18 01:29:34.655 [] [b183f0e166c93574/d71ecceb323c6cec] [http-nio-9009-exec-8] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:29:34 操作结束时间: 2025-07-18 01:29:34!总共花费时间: 51 毫秒！
2025-07-18 01:29:36.179 [] [536fe7f93cb9b756/233fff4f22582043] [http-nio-9009-exec-9] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:29:36 操作结束时间: 2025-07-18 01:29:36!总共花费时间: 51 毫秒！
2025-07-18 01:29:50.107 [] [95a3b77935b85d8e/78b910250f2678b2] [http-nio-9009-exec-11] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:29:50 操作结束时间: 2025-07-18 01:29:50!总共花费时间: 34 毫秒！
2025-07-18 01:29:57.131 [] [708c5430bd8b57bb/fe507726c1119b92] [http-nio-9009-exec-13] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:29:57 操作结束时间: 2025-07-18 01:29:57!总共花费时间: 35 毫秒！
2025-07-18 01:30:27.916 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:35:27.920 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:40:27.925 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:41:02.507 [] [2729937a392cc200/d948112c66428a27] [http-nio-9009-exec-15] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:02 操作结束时间: 2025-07-18 01:41:02!总共花费时间: 89 毫秒！
2025-07-18 01:41:04.296 [] [3424d6c4430ed488/4f3faf24f0ad6741] [http-nio-9009-exec-16] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:04 操作结束时间: 2025-07-18 01:41:04!总共花费时间: 191 毫秒！
2025-07-18 01:41:04.387 [] [0cc7ce1f60b2e7c3/9dd0d8dda5158008] [http-nio-9009-exec-17] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:04 操作结束时间: 2025-07-18 01:41:04!总共花费时间: 65 毫秒！
2025-07-18 01:41:11.730 [] [92d99ac184ca16cb/e69963706c992fbc] [http-nio-9009-exec-19] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 库房定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:11 操作结束时间: 2025-07-18 01:41:11!总共花费时间: 55 毫秒！
2025-07-18 01:41:11.776 [] [1a7957c34bc43fcb/7d8103aaefb56dcf] [http-nio-9009-exec-20] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询区域数据!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:11 操作结束时间: 2025-07-18 01:41:11!总共花费时间: 34 毫秒！
2025-07-18 01:41:14.091 [] [228079cdf2c0bec1/77a6454218de25e0] [http-nio-9009-exec-21] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 获取打印信息!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:14 操作结束时间: 2025-07-18 01:41:14!总共花费时间: 55 毫秒！
2025-07-18 01:41:34.020 [] [bc389964c2735c59/21b7b6e438fb1013] [http-nio-9009-exec-23] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 根据箱号查询档案!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:33 操作结束时间: 2025-07-18 01:41:34!总共花费时间: 28 毫秒！
2025-07-18 01:41:42.921 [] [9e63542600a3996b/6fd18eb0957390a5] [http-nio-9009-exec-25] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:42 操作结束时间: 2025-07-18 01:41:42!总共花费时间: 177 毫秒！
2025-07-18 01:41:47.118 [] [e426569b538e39f0/ed39aaae989d61dc] [http-nio-9009-exec-27] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:47 操作结束时间: 2025-07-18 01:41:47!总共花费时间: 41 毫秒！
2025-07-18 01:41:49.258 [] [417157279334ce74/6049f9cdeab8c46a] [http-nio-9009-exec-28] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:49 操作结束时间: 2025-07-18 01:41:49!总共花费时间: 47 毫秒！
2025-07-18 01:41:49.322 [] [27324f27e2f3e041/088216d7a502ac4f] [http-nio-9009-exec-29] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:49 操作结束时间: 2025-07-18 01:41:49!总共花费时间: 46 毫秒！
2025-07-18 01:41:49.382 [] [647af00d5875a5c0/ef13187047175876] [http-nio-9009-exec-30] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:49 操作结束时间: 2025-07-18 01:41:49!总共花费时间: 42 毫秒！
2025-07-18 01:41:51.044 [] [102d424b3db21120/7c90ffde27f5797c] [http-nio-9009-exec-31] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:51 操作结束时间: 2025-07-18 01:41:51!总共花费时间: 35 毫秒！
2025-07-18 01:41:51.123 [] [ba380d47c7ff89d2/7c0e38ecdfc8fda0] [http-nio-9009-exec-32] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:41:51 操作结束时间: 2025-07-18 01:41:51!总共花费时间: 55 毫秒！
2025-07-18 01:43:00.523 [] [bd82066deb5dbdaf/73268f06eaa9073b] [http-nio-9009-exec-34] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:43:00 操作结束时间: 2025-07-18 01:43:00!总共花费时间: 46 毫秒！
2025-07-18 01:43:00.612 [] [fd05926a84852372/e13562fd9718ff93] [http-nio-9009-exec-35] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:43:00 操作结束时间: 2025-07-18 01:43:00!总共花费时间: 69 毫秒！
2025-07-18 01:43:00.672 [] [41be966a54a374e7/865aba355b4af2f0] [http-nio-9009-exec-36] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:43:00 操作结束时间: 2025-07-18 01:43:00!总共花费时间: 42 毫秒！
2025-07-18 01:43:01.311 [] [ff9d47d05e3ba88f/2adb395c2a94d7c4] [http-nio-9009-exec-37] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-18 01:43:01 操作结束时间: 2025-07-18 01:43:01!总共花费时间: 31 毫秒！
2025-07-18 01:43:01.372 [] [8685c8c4372e8c0e/32d95651f0ba0ba5] [http-nio-9009-exec-38] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:43:01 操作结束时间: 2025-07-18 01:43:01!总共花费时间: 43 毫秒！
2025-07-18 01:45:27.934 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:48:44.857 [OrganNo_00023_UserNo_admin] [e10912367b01a03d/4d28df7a1e2b48a1] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-18 01:48:44.867 [OrganNo_00023_UserNo_admin] [e10912367b01a03d/25a5034ee1bef531] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-18 01:48:44.868 [OrganNo_00023_UserNo_admin] [e10912367b01a03d/25a5034ee1bef531] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-18 01:48:44.870 [OrganNo_00023_UserNo_admin] [e10912367b01a03d/25a5034ee1bef531] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-18 01:48:44.871 [OrganNo_00023_UserNo_admin] [e10912367b01a03d/25a5034ee1bef531] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-18 01:48:44.871 [OrganNo_00023_UserNo_admin] [e10912367b01a03d/25a5034ee1bef531] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-18 01:48:44.874 [OrganNo_00023_UserNo_admin] [e10912367b01a03d/25a5034ee1bef531] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-18 01:48:44.884 [OrganNo_00023_UserNo_admin] [e10912367b01a03d/4d28df7a1e2b48a1] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-18 01:48:44.885 [] [e10912367b01a03d/4d28df7a1e2b48a1] [http-nio-9009-exec-40] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:48:44 操作结束时间: 2025-07-18 01:48:44!总共花费时间: 38 毫秒！
2025-07-18 01:48:44.963 [] [65f672f5c4b128b8/24474827970a1079] [http-nio-9009-exec-41] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:48:44 操作结束时间: 2025-07-18 01:48:44!总共花费时间: 60 毫秒！
2025-07-18 01:48:45.024 [] [522f22365713767a/5c6c2ecc7eaaf3ac] [http-nio-9009-exec-42] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:48:44 操作结束时间: 2025-07-18 01:48:45!总共花费时间: 39 毫秒！
2025-07-18 01:48:47.146 [] [dcdf1e34a45a1808/fc52970b935c72f5] [http-nio-9009-exec-43] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:48:47 操作结束时间: 2025-07-18 01:48:47!总共花费时间: 51 毫秒！
2025-07-18 01:48:55.455 [] [ca8d89256b40eb29/752963a9bdc3233f] [http-nio-9009-exec-45] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:48:55 操作结束时间: 2025-07-18 01:48:55!总共花费时间: 39 毫秒！
2025-07-18 01:49:53.690 [] [decdf5d4cd9a8446/2106ab6eaba1b89e] [http-nio-9009-exec-47] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:49:53 操作结束时间: 2025-07-18 01:49:53!总共花费时间: 49 毫秒！
2025-07-18 01:50:05.853 [] [dbab619fcc94dfff/19ccbb2912b875ea] [http-nio-9009-exec-49] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:50:05 操作结束时间: 2025-07-18 01:50:05!总共花费时间: 38 毫秒！
2025-07-18 01:50:05.931 [] [fd2463dd9c85a679/5995cafe1934ec25] [http-nio-9009-exec-50] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:50:05 操作结束时间: 2025-07-18 01:50:05!总共花费时间: 57 毫秒！
2025-07-18 01:50:08.661 [] [b297fe73bd2f9ce5/f34cbbc352700f20] [http-nio-9009-exec-51] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:50:08 操作结束时间: 2025-07-18 01:50:08!总共花费时间: 60 毫秒！
2025-07-18 01:50:27.945 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:55:26.476 [] [b23ad3465a559315/458fd9099ff1f5d6] [http-nio-9009-exec-53] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:55:26 操作结束时间: 2025-07-18 01:55:26!总共花费时间: 45 毫秒！
2025-07-18 01:55:26.568 [] [07533dcc4a160b60/0a72e142299521c4] [http-nio-9009-exec-54] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:55:26 操作结束时间: 2025-07-18 01:55:26!总共花费时间: 70 毫秒！
2025-07-18 01:55:26.629 [] [bd5021a7db2c7400/202dd60e1cc6f1eb] [http-nio-9009-exec-55] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:55:26 操作结束时间: 2025-07-18 01:55:26!总共花费时间: 39 毫秒！
2025-07-18 01:55:27.947 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 01:55:28.249 [] [dcda217cdb61c086/2b5b13678a624824] [http-nio-9009-exec-56] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:55:28 操作结束时间: 2025-07-18 01:55:28!总共花费时间: 46 毫秒！
2025-07-18 01:55:37.978 [] [948a5061692d913b/069f0f5b0fc22bcf] [http-nio-9009-exec-58] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:55:37 操作结束时间: 2025-07-18 01:55:37!总共花费时间: 43 毫秒！
2025-07-18 01:55:52.905 [] [e33b34dffb3a79c3/167389d452c203b6] [http-nio-9009-exec-60] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:55:52 操作结束时间: 2025-07-18 01:55:52!总共花费时间: 30 毫秒！
2025-07-18 01:56:06.160 [] [2707fd706a93130b/687b4d6bef19af54] [http-nio-9009-exec-62] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:56:06 操作结束时间: 2025-07-18 01:56:06!总共花费时间: 42 毫秒！
2025-07-18 01:58:15.117 [] [09e99df1a2de696c/35f7bbed69047948] [http-nio-9009-exec-64] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:58:15 操作结束时间: 2025-07-18 01:58:15!总共花费时间: 46 毫秒！
2025-07-18 01:58:15.195 [] [1ef5fe64a92acb81/f3285005f49502e2] [http-nio-9009-exec-65] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:58:15 操作结束时间: 2025-07-18 01:58:15!总共花费时间: 59 毫秒！
2025-07-18 01:58:15.289 [] [58f4bfe91d7dc162/135fa7ca78e9b4c0] [http-nio-9009-exec-66] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 01:58:15 操作结束时间: 2025-07-18 01:58:15!总共花费时间: 71 毫秒！
2025-07-18 01:58:16.890 [] [b4da9743d6e9a1f8/b1a234e3aa4fa4a1] [http-nio-9009-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 01:58:16 操作结束时间: 2025-07-18 01:58:16!总共花费时间: 51 毫秒！
2025-07-18 02:00:27.948 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:01:02.280 [] [7ee1acf3a8d6865b/c75a89addfcb0ca6] [http-nio-9009-exec-69] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:01:02 操作结束时间: 2025-07-18 02:01:02!总共花费时间: 41 毫秒！
2025-07-18 02:01:02.340 [] [3ae8cea4a4d7b231/95db0e5ed9d934c7] [http-nio-9009-exec-70] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:01:02 操作结束时间: 2025-07-18 02:01:02!总共花费时间: 43 毫秒！
2025-07-18 02:01:02.404 [] [19237fa68cf966be/18aad667fb01c4e2] [http-nio-9009-exec-71] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:01:02 操作结束时间: 2025-07-18 02:01:02!总共花费时间: 46 毫秒！
2025-07-18 02:01:04.208 [] [5c904939219582f3/4ec3d27b8640f683] [http-nio-9009-exec-72] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:01:04 操作结束时间: 2025-07-18 02:01:04!总共花费时间: 39 毫秒！
2025-07-18 02:01:15.682 [] [d5ed8e0faf17e270/c57e69531f629790] [http-nio-9009-exec-74] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:01:15 操作结束时间: 2025-07-18 02:01:15!总共花费时间: 30 毫秒！
2025-07-18 02:01:15.761 [] [ba641c281db9244d/454035c224687d0c] [http-nio-9009-exec-75] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:01:15 操作结束时间: 2025-07-18 02:01:15!总共花费时间: 57 毫秒！
2025-07-18 02:01:17.763 [] [cd6e4b2640b8df3a/0117f80940c94cde] [http-nio-9009-exec-76] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 柜员凭证登记 打印凭证封面!请求IP地址: ************** 操作开始时间: 2025-07-18 02:01:17 操作结束时间: 2025-07-18 02:01:17!总共花费时间: 51 毫秒！
2025-07-18 02:05:27.961 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:06:35.985 [] [ed3eca1fda802727/64dbf079709c9e15] [http-nio-9009-exec-78] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:06:35 操作结束时间: 2025-07-18 02:06:35!总共花费时间: 149 毫秒！
2025-07-18 02:06:36.075 [] [e125d7763c7d2ad0/a0c0cfa80e71623d] [http-nio-9009-exec-79] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:06:36 操作结束时间: 2025-07-18 02:06:36!总共花费时间: 69 毫秒！
2025-07-18 02:06:36.167 [] [a142771c4decb1a0/3d332147ebcf71ce] [http-nio-9009-exec-80] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:06:36 操作结束时间: 2025-07-18 02:06:36!总共花费时间: 71 毫秒！
2025-07-18 02:06:38.302 [] [58742e7351bafaa5/003b7407ef0abf33] [http-nio-9009-exec-81] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:06:38 操作结束时间: 2025-07-18 02:06:38!总共花费时间: 48 毫秒！
2025-07-18 02:07:42.951 [] [8b817a1302b461f4/f77cb0357d8855c3] [http-nio-9009-exec-83] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:07:42 操作结束时间: 2025-07-18 02:07:42!总共花费时间: 46 毫秒！
2025-07-18 02:07:44.876 [] [a026c04eb5b95e1b/137c2c1232b59c25] [http-nio-9009-exec-84] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:07:44 操作结束时间: 2025-07-18 02:07:44!总共花费时间: 57 毫秒！
2025-07-18 02:07:44.969 [] [70bc167fbfb7ae4f/2919570926250f50] [http-nio-9009-exec-85] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:07:44 操作结束时间: 2025-07-18 02:07:44!总共花费时间: 69 毫秒！
2025-07-18 02:07:45.030 [] [8a883e83c674d3d6/36479eb3219362cf] [http-nio-9009-exec-86] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:07:44 操作结束时间: 2025-07-18 02:07:45!总共花费时间: 44 毫秒！
2025-07-18 02:07:48.045 [] [69c581b7e30224cb/77c998daadc4755e] [http-nio-9009-exec-87] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:07:47 操作结束时间: 2025-07-18 02:07:48!总共花费时间: 52 毫秒！
2025-07-18 02:09:55.245 [] [cce8154ca666c090/1661b7819a453ab5] [http-nio-9009-exec-89] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 新增!请求IP地址: ************** 操作开始时间: 2025-07-18 02:09:55 操作结束时间: 2025-07-18 02:09:55!总共花费时间: 51 毫秒！
2025-07-18 02:09:55.300 [] [554b8bb07f582436/49ec5a69569f24a5] [http-nio-9009-exec-90] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:09:55 操作结束时间: 2025-07-18 02:09:55!总共花费时间: 40 毫秒！
2025-07-18 02:09:55.362 [] [d761f65d3607c705/57425caba86664e4] [http-nio-9009-exec-91] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:09:55 操作结束时间: 2025-07-18 02:09:55!总共花费时间: 51 毫秒！
2025-07-18 02:10:13.718 [] [b94e491764f45359/dd6c604ff8966862] [http-nio-9009-exec-95] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:10:13 操作结束时间: 2025-07-18 02:10:13!总共花费时间: 32 毫秒！
2025-07-18 02:10:27.966 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:15:27.968 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:16:22.505 [] [874e9e519430095f/5149983df5c105a2] [http-nio-9009-exec-97] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 凭证移交中,添加时的查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:16:22 操作结束时间: 2025-07-18 02:16:22!总共花费时间: 40 毫秒！
2025-07-18 02:16:27.536 [] [3aea35d704fa8bdf/a88207fd56b6340f] [http-nio-9009-exec-99] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-18 02:16:27 操作结束时间: 2025-07-18 02:16:27!总共花费时间: 32 毫秒！
2025-07-18 02:16:27.598 [] [dffc2035811c6bd9/9e8f6afc8b7ef826] [http-nio-9009-exec-100] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:16:27 操作结束时间: 2025-07-18 02:16:27!总共花费时间: 43 毫秒！
2025-07-18 02:16:29.632 [] [05a7cf23335379ca/799f8047ccfdaf8b] [http-nio-9009-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-18 02:16:29 操作结束时间: 2025-07-18 02:16:29!总共花费时间: 50 毫秒！
2025-07-18 02:16:29.691 [] [fa94a75e3198c313/a382866718f20934] [http-nio-9009-exec-2] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取用户机构信息!请求IP地址: ************** 操作开始时间: 2025-07-18 02:16:29 操作结束时间: 2025-07-18 02:16:29!总共花费时间: 46 毫秒！
2025-07-18 02:16:31.672 [] [60c0c4a8c0bcd521/757a4c689673ed27] [http-nio-9009-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:16:31 操作结束时间: 2025-07-18 02:16:31!总共花费时间: 42 毫秒！
2025-07-18 02:16:34.615 [] [49b2ca7ea0981b40/422bb97a66d748b6] [http-nio-9009-exec-4] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:16:34 操作结束时间: 2025-07-18 02:16:34!总共花费时间: 64 毫秒！
2025-07-18 02:16:34.693 [] [8f912d5efbd18e0b/db5082f67bc37936] [http-nio-9009-exec-5] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:16:34 操作结束时间: 2025-07-18 02:16:34!总共花费时间: 56 毫秒！
2025-07-18 02:18:55.923 [] [913f9edc6f52cca3/2d0095c373c0c8f3] [http-nio-9009-exec-7] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:18:55 操作结束时间: 2025-07-18 02:18:55!总共花费时间: 40 毫秒！
2025-07-18 02:18:55.952 [OrganNo_00023_UserNo_admin] [3e3f089eafff407a/ae8046d95f339072] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-07-18 02:18:55.969 [OrganNo_00023_UserNo_admin] [3e3f089eafff407a/52fc76ec309c978b] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE SITE_NO IN (SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ?) AND APPLICATION_STATE = ?
2025-07-18 02:18:55.970 [OrganNo_00023_UserNo_admin] [3e3f089eafff407a/52fc76ec309c978b] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin(String), 23(String)
2025-07-18 02:18:55.972 [OrganNo_00023_UserNo_admin] [3e3f089eafff407a/52fc76ec309c978b] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-07-18 02:18:55.973 [OrganNo_00023_UserNo_admin] [3e3f089eafff407a/52fc76ec309c978b] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE SITE_NO in ( SELECT ORGAN_NO FROM SM_USER_ORGAN_TB WHERE USER_NO = ? ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-07-18 02:18:55.974 [OrganNo_00023_UserNo_admin] [3e3f089eafff407a/52fc76ec309c978b] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin(String), 23(String), 15(Integer)
2025-07-18 02:18:55.975 [OrganNo_00023_UserNo_admin] [3e3f089eafff407a/52fc76ec309c978b] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 5
2025-07-18 02:18:55.999 [OrganNo_00023_UserNo_admin] [3e3f089eafff407a/ae8046d95f339072] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250703113315953115",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250703113315953115",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"7b53c149f1554632aa28e6b39de3c054",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250703",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"熊涛",
				"tellerNo":"7567209",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"4"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704154204216347",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704154204216347",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"1b58ac266ca547d39bb835c9fda6c9f8",
				"isDel":"0",
				"oganName":"15089-中国银行德阳罗江支行",
				"oganNo":"15089",
				"registerDate":"20250704",
				"siteName":"中国银行德阳罗江支行营业部",
				"siteNo":"15442",
				"tellerName":"唐甜",
				"tellerNo":"1713854",
				"userName":"唐甜",
				"userNo":"1713854",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704155047593474",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704155047593474",
				"businessDate":"20240819",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640819",
				"endBusiDate":"20240819",
				"id":"b6c986d1289d4f68b9494c90cc6bc057",
				"isDel":"0",
				"oganName":"14913-中国银行成都高新技术产业开发区支行",
				"oganNo":"14913",
				"registerDate":"20250704",
				"siteName":"中国银行成都复城广场支行",
				"siteNo":"35944",
				"tellerName":"张诗琪",
				"tellerNo":"8767041",
				"userName":"张诗琪",
				"userNo":"8767041",
				"warrantAmount":"2"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250704152937239660",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250704152937239660",
				"businessDate":"20240910",
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"destroyDate":"20640910",
				"endBusiDate":"20240910",
				"id":"faecdbe2bb4e4627bbd995430f9d8b6e",
				"isDel":"0",
				"oganName":"14901-中国银行成都蜀都大道支行",
				"oganNo":"14901",
				"registerDate":"20250704",
				"siteName":"中国银行成都实业街支行",
				"siteNo":"14902",
				"tellerName":"童心",
				"tellerNo":"1488420",
				"userName":"童心",
				"userNo":"1488420",
				"warrantAmount":"3"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250616140816157403",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250616140816157403",
				"businessDate":"20250616",
				"codeName":"保存40年 (默认)",
				"codeNo":"PZ01",
				"destroyDate":"20650616",
				"endBusiDate":"20250626",
				"id":"4ea6430cec6f44749695b61ecf80e3cd",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250616",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李虹",
				"tellerNo":"6045330",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"23"
			}
		],
		"totalCount":5,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-18 02:18:56.000 [] [3e3f089eafff407a/ae8046d95f339072] [http-nio-9009-exec-8] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:18:55 操作结束时间: 2025-07-18 02:18:56!总共花费时间: 58 毫秒！
2025-07-18 02:18:56.074 [] [632032a5c075c2cd/b45a50e5f3c67880] [http-nio-9009-exec-9] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:18:56 操作结束时间: 2025-07-18 02:18:56!总共花费时间: 52 毫秒！
2025-07-18 02:19:11.057 [] [df8492b885efb90d/5e2ddbc9f2b45e72] [http-nio-9009-exec-11] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:19:11 操作结束时间: 2025-07-18 02:19:11!总共花费时间: 33 毫秒！
2025-07-18 02:20:27.975 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:25:27.981 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:25:35.474 [OrganNo_00023_UserNo_admin] [f242137c8f73b8ee/a2a92be32b4895ca] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-18 02:25:35.482 [OrganNo_00023_UserNo_admin] [f242137c8f73b8ee/60db87938917181f] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-07-18 02:25:35.483 [OrganNo_00023_UserNo_admin] [f242137c8f73b8ee/60db87938917181f] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-07-18 02:25:35.484 [OrganNo_00023_UserNo_admin] [f242137c8f73b8ee/60db87938917181f] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-07-18 02:25:35.484 [OrganNo_00023_UserNo_admin] [f242137c8f73b8ee/60db87938917181f] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-07-18 02:25:35.484 [OrganNo_00023_UserNo_admin] [f242137c8f73b8ee/60db87938917181f] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-07-18 02:25:35.485 [OrganNo_00023_UserNo_admin] [f242137c8f73b8ee/60db87938917181f] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 1
2025-07-18 02:25:35.494 [OrganNo_00023_UserNo_admin] [f242137c8f73b8ee/a2a92be32b4895ca] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-18 02:25:35.495 [] [f242137c8f73b8ee/a2a92be32b4895ca] [http-nio-9009-exec-13] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:25:35 操作结束时间: 2025-07-18 02:25:35!总共花费时间: 32 毫秒！
2025-07-18 02:25:35.556 [] [3b1b56b46310e821/46f0cde2b6db12ac] [http-nio-9009-exec-14] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:25:35 操作结束时间: 2025-07-18 02:25:35!总共花费时间: 45 毫秒！
2025-07-18 02:25:35.634 [] [18f4a003d4a706ac/bff09f2060ef0b8e] [http-nio-9009-exec-15] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:25:35 操作结束时间: 2025-07-18 02:25:35!总共花费时间: 51 毫秒！
2025-07-18 02:25:37.529 [] [57725b914eb54480/69d45bba5112e151] [http-nio-9009-exec-16] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:25:37 操作结束时间: 2025-07-18 02:25:37!总共花费时间: 40 毫秒！
2025-07-18 02:25:53.911 [] [7ebaaa81a0f2eea9/eaa9e0fe2a4a7476] [http-nio-9009-exec-18] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:25:53 操作结束时间: 2025-07-18 02:25:53!总共花费时间: 42 毫秒！
2025-07-18 02:27:44.900 [] [24e5650af7242032/f5c6d6803dc94428] [http-nio-9009-exec-20] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:27:44 操作结束时间: 2025-07-18 02:27:44!总共花费时间: 41 毫秒！
2025-07-18 02:27:45.004 [] [245c2bdef4cbadcd/0e4908945aeadf52] [http-nio-9009-exec-21] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:27:44 操作结束时间: 2025-07-18 02:27:45!总共花费时间: 84 毫秒！
2025-07-18 02:27:46.869 [] [c452e1e95f1de280/20113f6adcff65c3] [http-nio-9009-exec-22] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 柜员凭证登记 打印凭证封面!请求IP地址: ************** 操作开始时间: 2025-07-18 02:27:46 操作结束时间: 2025-07-18 02:27:46!总共花费时间: 48 毫秒！
2025-07-18 02:27:53.401 [] [9cacf504059ecaf1/8c1514442bcfb008] [http-nio-9009-exec-24] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:27:53 操作结束时间: 2025-07-18 02:27:53!总共花费时间: 34 毫秒！
2025-07-18 02:27:53.523 [] [0b3c6ff1cc9b3cac/dcdc1d86002124f4] [http-nio-9009-exec-25] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:27:53 操作结束时间: 2025-07-18 02:27:53!总共花费时间: 102 毫秒！
2025-07-18 02:27:53.586 [] [2eace49731cbe9a9/bccbf49840d55359] [http-nio-9009-exec-26] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:27:53 操作结束时间: 2025-07-18 02:27:53!总共花费时间: 44 毫秒！
2025-07-18 02:27:55.161 [] [4333db03788e33d9/5d818a9ef32dd503] [http-nio-9009-exec-27] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:27:55 操作结束时间: 2025-07-18 02:27:55!总共花费时间: 47 毫秒！
2025-07-18 02:29:25.840 [] [a931fc0164f49891/4ad39c8c5b21b61a] [http-nio-9009-exec-29] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:25 操作结束时间: 2025-07-18 02:29:25!总共花费时间: 39 毫秒！
2025-07-18 02:29:25.917 [] [e3ede7f1eaa3fe98/657dccd70a32b901] [http-nio-9009-exec-30] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:25 操作结束时间: 2025-07-18 02:29:25!总共花费时间: 59 毫秒！
2025-07-18 02:29:25.978 [] [bc5645f9da0bec1f/50a8dd023f0a0cac] [http-nio-9009-exec-31] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:25 操作结束时间: 2025-07-18 02:29:25!总共花费时间: 41 毫秒！
2025-07-18 02:29:27.457 [] [565697d22d32e57e/54756900b5587666] [http-nio-9009-exec-32] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:27 操作结束时间: 2025-07-18 02:29:27!总共花费时间: 50 毫秒！
2025-07-18 02:29:33.473 [] [81459ab7c6995361/03eb9af6dd5ea0b2] [http-nio-9009-exec-34] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:33 操作结束时间: 2025-07-18 02:29:33!总共花费时间: 37 毫秒！
2025-07-18 02:29:37.606 [] [3ab190387ea7e3f0/3aeafe3c9c7d5357] [http-nio-9009-exec-36] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:37 操作结束时间: 2025-07-18 02:29:37!总共花费时间: 37 毫秒！
2025-07-18 02:29:37.699 [] [96a88b4627fe1a3c/c8716747b26da275] [http-nio-9009-exec-37] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:37 操作结束时间: 2025-07-18 02:29:37!总共花费时间: 69 毫秒！
2025-07-18 02:29:37.774 [] [4cf78b355f9b2ab7/c83e87239aa9b677] [http-nio-9009-exec-38] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:37 操作结束时间: 2025-07-18 02:29:37!总共花费时间: 54 毫秒！
2025-07-18 02:29:39.086 [] [1f275f567aed774c/25ba070bb2e62a39] [http-nio-9009-exec-39] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:29:39 操作结束时间: 2025-07-18 02:29:39!总共花费时间: 39 毫秒！
2025-07-18 02:30:02.827 [] [3b70ea2a2bac3ad1/3efc1353dd7a0844] [http-nio-9009-exec-41] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:30:02 操作结束时间: 2025-07-18 02:30:02!总共花费时间: 43 毫秒！
2025-07-18 02:30:02.901 [] [4cac482379b0e532/66dc565887117e89] [http-nio-9009-exec-42] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:30:02 操作结束时间: 2025-07-18 02:30:02!总共花费时间: 56 毫秒！
2025-07-18 02:30:02.964 [] [9c3bf0acf590a457/aedd3a4d221e3c3f] [http-nio-9009-exec-43] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:30:02 操作结束时间: 2025-07-18 02:30:02!总共花费时间: 42 毫秒！
2025-07-18 02:30:04.422 [] [fd1dbc475a2fa2f7/dc8731ed9ae3c982] [http-nio-9009-exec-44] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:30:04 操作结束时间: 2025-07-18 02:30:04!总共花费时间: 44 毫秒！
2025-07-18 02:30:07.475 [] [5b0369591c5a066f/351e52f5417a4214] [http-nio-9009-exec-45] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:30:07 操作结束时间: 2025-07-18 02:30:07!总共花费时间: 40 毫秒！
2025-07-18 02:30:27.984 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:30:38.500 [] [8e0def6d2c950074/e20e098267841978] [http-nio-9009-exec-47] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:30:38 操作结束时间: 2025-07-18 02:30:38!总共花费时间: 36 毫秒！
2025-07-18 02:31:52.293 [] [303b3dbb9f90e5d7/672cbd9451abccc4] [http-nio-9009-exec-49] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:31:52 操作结束时间: 2025-07-18 02:31:52!总共花费时间: 68 毫秒！
2025-07-18 02:31:52.381 [] [f62020a071df6f65/87b089aa744cb076] [http-nio-9009-exec-50] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:31:52 操作结束时间: 2025-07-18 02:31:52!总共花费时间: 62 毫秒！
2025-07-18 02:31:52.458 [] [e60c7011ea90fe28/f6955960af7697b5] [http-nio-9009-exec-51] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:31:52 操作结束时间: 2025-07-18 02:31:52!总共花费时间: 55 毫秒！
2025-07-18 02:31:54.062 [] [1ccbc11fe960ef19/68b53d15fb14ca06] [http-nio-9009-exec-52] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:31:54 操作结束时间: 2025-07-18 02:31:54!总共花费时间: 49 毫秒！
2025-07-18 02:33:36.117 [] [0cc5f9fe757ee17a/5fd761fb5d61b236] [http-nio-9009-exec-54] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:33:36 操作结束时间: 2025-07-18 02:33:36!总共花费时间: 66 毫秒！
2025-07-18 02:33:36.203 [] [41962f03dcbe4d60/ac8241001785786a] [http-nio-9009-exec-55] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:33:36 操作结束时间: 2025-07-18 02:33:36!总共花费时间: 65 毫秒！
2025-07-18 02:33:36.265 [] [b7958f39e6ef12b8/1db0f815fbe5ef4b] [http-nio-9009-exec-56] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:33:36 操作结束时间: 2025-07-18 02:33:36!总共花费时间: 40 毫秒！
2025-07-18 02:33:37.883 [] [86e012d9ffdc398d/fe6495ddcc2ee55c] [http-nio-9009-exec-57] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:33:37 操作结束时间: 2025-07-18 02:33:37!总共花费时间: 50 毫秒！
2025-07-18 02:35:27.994 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:35:53.749 [] [fc97d733e9391348/8fb38e59abd16c4f] [http-nio-9009-exec-59] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:35:53 操作结束时间: 2025-07-18 02:35:53!总共花费时间: 38 毫秒！
2025-07-18 02:35:53.837 [] [a968e8d0ecc10951/2f00422e5e12720c] [http-nio-9009-exec-60] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:35:53 操作结束时间: 2025-07-18 02:35:53!总共花费时间: 65 毫秒！
2025-07-18 02:35:53.900 [] [563e3d059f07437f/fb4bed002ce80601] [http-nio-9009-exec-61] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:35:53 操作结束时间: 2025-07-18 02:35:53!总共花费时间: 41 毫秒！
2025-07-18 02:35:55.754 [] [96fdf49fea39c094/7302e103c2d394f4] [http-nio-9009-exec-62] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:35:55 操作结束时间: 2025-07-18 02:35:55!总共花费时间: 48 毫秒！
2025-07-18 02:36:32.321 [] [f01de606d8c18836/c8f57393c94b6a4a] [http-nio-9009-exec-64] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:36:32 操作结束时间: 2025-07-18 02:36:32!总共花费时间: 37 毫秒！
2025-07-18 02:39:08.235 [] [c8e42cbf039b30bf/1fca0eb52eddcdd7] [http-nio-9009-exec-66] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:39:08 操作结束时间: 2025-07-18 02:39:08!总共花费时间: 37 毫秒！
2025-07-18 02:39:08.296 [] [063fdfaaabd49519/7478f04588d93b7b] [http-nio-9009-exec-67] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:39:08 操作结束时间: 2025-07-18 02:39:08!总共花费时间: 43 毫秒！
2025-07-18 02:39:08.358 [] [bdbed45c0869c3c1/b95bf448a1d00f99] [http-nio-9009-exec-68] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:39:08 操作结束时间: 2025-07-18 02:39:08!总共花费时间: 44 毫秒！
2025-07-18 02:39:09.979 [] [b4b50862971655e2/1311fc052129e27f] [http-nio-9009-exec-69] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:39:09 操作结束时间: 2025-07-18 02:39:09!总共花费时间: 53 毫秒！
2025-07-18 02:40:28.002 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:41:08.401 [] [67cabf72661c3f82/4f94e9e94e9b91c2] [http-nio-9009-exec-71] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:41:08 操作结束时间: 2025-07-18 02:41:08!总共花费时间: 40 毫秒！
2025-07-18 02:41:08.477 [] [26410dd2b5558690/b923d02d63eebe2b] [http-nio-9009-exec-72] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:41:08 操作结束时间: 2025-07-18 02:41:08!总共花费时间: 59 毫秒！
2025-07-18 02:41:08.536 [] [9de5fd4dadcb7b58/d42302140a35e24a] [http-nio-9009-exec-73] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:41:08 操作结束时间: 2025-07-18 02:41:08!总共花费时间: 39 毫秒！
2025-07-18 02:41:10.633 [] [0a92653006940051/0a6f5bfa9d5835d1] [http-nio-9009-exec-74] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:41:10 操作结束时间: 2025-07-18 02:41:10!总共花费时间: 50 毫秒！
2025-07-18 02:41:23.093 [] [4eb33192f701e00d/afd184f692223004] [http-nio-9009-exec-76] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:41:23 操作结束时间: 2025-07-18 02:41:23!总共花费时间: 39 毫秒！
2025-07-18 02:41:51.145 [] [7c2e747a24678291/129ec25f95ecc2a2] [http-nio-9009-exec-78] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:41:51 操作结束时间: 2025-07-18 02:41:51!总共花费时间: 34 毫秒！
2025-07-18 02:42:06.538 [] [e4415899bbd39043/0e80475b90b5d229] [http-nio-9009-exec-80] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:42:06 操作结束时间: 2025-07-18 02:42:06!总共花费时间: 31 毫秒！
2025-07-18 02:42:36.042 [] [526be08d0597bf83/d4dcb244d39f13c4] [http-nio-9009-exec-82] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:42:36 操作结束时间: 2025-07-18 02:42:36!总共花费时间: 29 毫秒！
2025-07-18 02:42:46.338 [] [657fa39d7fbbb97c/f6fb0c9e43c7dcc7] [http-nio-9009-exec-84] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:42:46 操作结束时间: 2025-07-18 02:42:46!总共花费时间: 26 毫秒！
2025-07-18 02:42:46.399 [] [a5efede5b8674967/236525fb6bc52cfc] [http-nio-9009-exec-85] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:42:46 操作结束时间: 2025-07-18 02:42:46!总共花费时间: 45 毫秒！
2025-07-18 02:45:28.006 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:49:19.818 [] [2dcca584c131aece/57327ae1aa9a824b] [http-nio-9009-exec-87] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:19 操作结束时间: 2025-07-18 02:49:19!总共花费时间: 46 毫秒！
2025-07-18 02:49:19.849 [OrganNo_00023_UserNo_admin] [717b374fd936750e/666059ef68526423] [http-nio-9009-exec-88] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-07-18 02:49:19.858 [OrganNo_00023_UserNo_admin] [717b374fd936750e/ac82328955129390] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND SITE_NO IN (SELECT org.ORGAN_NO FROM SM_USER_ORGAN_TB org WHERE org.USER_NO = ?)
2025-07-18 02:49:19.859 [OrganNo_00023_UserNo_admin] [717b374fd936750e/ac82328955129390] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), admin(String)
2025-07-18 02:49:19.861 [OrganNo_00023_UserNo_admin] [717b374fd936750e/ac82328955129390] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-07-18 02:49:19.861 [OrganNo_00023_UserNo_admin] [717b374fd936750e/ac82328955129390] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==>  Preparing: select ID "ID",BALE_NO "BALE_NO",BALE_STATE "BALE_STATE",SEND_USER_NO "SEND_USER_NO",SEND_USER_NAME "SEND_USER_NAME", SEND_DATE "SEND_DATE",RECEIVE_USER_NO "RECEIVE_USER_NO",RECEIVE_USER_NAME "RECEIVE_USER_NAME", RECEIVE_DATE "RECEIVE_DATE",SITE_NO "SITE_NO",APP_TYPE "APP_TYPE",LOCK_CODE "LOCK_CODE" from FM_BALE_TB WHERE BALE_STATE = ? and SITE_NO in ( select org.ORGAN_NO from SM_USER_ORGAN_TB org where org.USER_NO = ? ) LIMIT ?
2025-07-18 02:49:19.861 [OrganNo_00023_UserNo_admin] [717b374fd936750e/ac82328955129390] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - ==> Parameters: 1(String), admin(String), 15(Integer)
2025-07-18 02:49:19.862 [OrganNo_00023_UserNo_admin] [717b374fd936750e/ac82328955129390] [http-nio-9009-exec-88] DEBUG c.s.a.f.d.B.selectBaleOfOneUse - <==      Total: 4
2025-07-18 02:49:19.880 [OrganNo_00023_UserNo_admin] [717b374fd936750e/666059ef68526423] [http-nio-9009-exec-88] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"baleNo":"2025042100023001",
				"baleState":"1",
				"id":"d5d1e7b00c0d4bde9dbbc539bd1d3443",
				"sendDate":"20250421",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin1",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025062600023001",
				"baleState":"1",
				"id":"57a5acc00db249199fc3f3b7bbacc891",
				"sendDate":"20250626",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025071500023001",
				"baleState":"1",
				"id":"da1af8df46fa4766bb7e372e36fc9ffc",
				"lockCode":"111111",
				"sendDate":"20250717",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			},
			{
				"baleNo":"2025061600023002",
				"baleState":"1",
				"id":"0e1b88c2cc7b45d0a1d6f23d9731ca14",
				"sendDate":"20250717",
				"sendUserName":"系统超级管理员",
				"sendUserNo":"admin",
				"siteNo":"00023"
			}
		],
		"totalCount":4,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-07-18 02:49:19.881 [] [717b374fd936750e/666059ef68526423] [http-nio-9009-exec-88] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:19 操作结束时间: 2025-07-18 02:49:19!总共花费时间: 47 毫秒！
2025-07-18 02:49:22.789 [] [6c298b5e8c47b783/3d02b341ebc12ea5] [http-nio-9009-exec-89] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:22 操作结束时间: 2025-07-18 02:49:22!总共花费时间: 50 毫秒！
2025-07-18 02:49:31.284 [] [d3bff5e054777523/352e3bef87f623d7] [http-nio-9009-exec-91] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:31 操作结束时间: 2025-07-18 02:49:31!总共花费时间: 28 毫秒！
2025-07-18 02:49:31.362 [] [6a58213e59ac5dae/24c044332236e549] [http-nio-9009-exec-92] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:31 操作结束时间: 2025-07-18 02:49:31!总共花费时间: 56 毫秒！
2025-07-18 02:49:31.424 [] [af7a4b640ef16837/c59c3577212bca1c] [http-nio-9009-exec-93] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:31 操作结束时间: 2025-07-18 02:49:31!总共花费时间: 44 毫秒！
2025-07-18 02:49:33.689 [] [b90b4633918137b9/4e8b75ed710e1648] [http-nio-9009-exec-94] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:33 操作结束时间: 2025-07-18 02:49:33!总共花费时间: 31 毫秒！
2025-07-18 02:49:40.503 [] [9d1fc30f8972ccf9/85112a71dbd3018a] [http-nio-9009-exec-96] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 校验交接袋是否可移交!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:40 操作结束时间: 2025-07-18 02:49:40!总共花费时间: 39 毫秒！
2025-07-18 02:49:42.727 [] [c78c5eb480fffc18/37ae37eaf8e3421f] [http-nio-9009-exec-97] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 交接袋业务 操作方法: 校验身份证号!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:42 操作结束时间: 2025-07-18 02:49:42!总共花费时间: 29 毫秒！
2025-07-18 02:49:42.770 [] [dee10e8311a5fbac/dd7ddccea4dffd5e] [http-nio-9009-exec-98] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 应急移交交接袋!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:42 操作结束时间: 2025-07-18 02:49:42!总共花费时间: 34 毫秒！
2025-07-18 02:49:42.834 [] [41f414b75f47711c/f8f8e90e7e8bbcb7] [http-nio-9009-exec-99] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:42 操作结束时间: 2025-07-18 02:49:42!总共花费时间: 48 毫秒！
2025-07-18 02:49:45.625 [] [34089078acf2d3a9/44760a1fd7c97791] [http-nio-9009-exec-100] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:45 操作结束时间: 2025-07-18 02:49:45!总共花费时间: 53 毫秒！
2025-07-18 02:49:45.686 [] [de59fb9c64f5cc1b/a0327317ef7dd8d2] [http-nio-9009-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:45 操作结束时间: 2025-07-18 02:49:45!总共花费时间: 45 毫秒！
2025-07-18 02:49:49.008 [] [fb570e66c46f2d88/b97c2644167044e1] [http-nio-9009-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 打印移交清单!请求IP地址: ************** 操作开始时间: 2025-07-18 02:49:48 操作结束时间: 2025-07-18 02:49:49!总共花费时间: 30 毫秒！
2025-07-18 02:50:07.790 [] [8666a3da7c7ba3d7/1a11c920d9c5a82f] [http-nio-9009-exec-5] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:07 操作结束时间: 2025-07-18 02:50:07!总共花费时间: 23 毫秒！
2025-07-18 02:50:07.852 [] [f41867c9eaeb6309/2e38584d49908f51] [http-nio-9009-exec-6] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:07 操作结束时间: 2025-07-18 02:50:07!总共花费时间: 53 毫秒！
2025-07-18 02:50:07.931 [] [cb5e10fd60d5e156/889ecf894b50fb84] [http-nio-9009-exec-7] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证封包操作 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:07 操作结束时间: 2025-07-18 02:50:07!总共花费时间: 68 毫秒！
2025-07-18 02:50:08.787 [] [55eee6bbcd9655c0/c48e637666652ddb] [http-nio-9009-exec-8] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:08 操作结束时间: 2025-07-18 02:50:08!总共花费时间: 22 毫秒！
2025-07-18 02:50:08.849 [] [37554f308f2532a0/88bf385f89a972b7] [http-nio-9009-exec-9] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证封包操作 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:08 操作结束时间: 2025-07-18 02:50:08!总共花费时间: 52 毫秒！
2025-07-18 02:50:08.943 [] [07ccb11771aa5b76/5f8ade91276a24cd] [http-nio-9009-exec-10] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 装箱操作 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:08 操作结束时间: 2025-07-18 02:50:08!总共花费时间: 84 毫秒！
2025-07-18 02:50:15.775 [] [ee5d2c5d5d513148/9f6d2afb0d81d476] [http-nio-9009-exec-12] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:15 操作结束时间: 2025-07-18 02:50:15!总共花费时间: 35 毫秒！
2025-07-18 02:50:17.004 [] [2f13b4557e54f3b5/867296f3ed8405bf] [http-nio-9009-exec-13] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:16 操作结束时间: 2025-07-18 02:50:17!总共花费时间: 73 毫秒！
2025-07-18 02:50:17.065 [] [aef4ac88e28da1fd/f6852617932b30b2] [http-nio-9009-exec-14] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:17 操作结束时间: 2025-07-18 02:50:17!总共花费时间: 43 毫秒！
2025-07-18 02:50:18.407 [] [9f4a7e0a866d7004/156686f8ba60cb75] [http-nio-9009-exec-15] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:18 操作结束时间: 2025-07-18 02:50:18!总共花费时间: 41 毫秒！
2025-07-18 02:50:20.743 [] [3078ba2a63251f7e/50d297240bde903e] [http-nio-9009-exec-16] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:20 操作结束时间: 2025-07-18 02:50:20!总共花费时间: 55 毫秒！
2025-07-18 02:50:20.805 [] [fa1e29df5253c604/17134fd660b221bf] [http-nio-9009-exec-17] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:20 操作结束时间: 2025-07-18 02:50:20!总共花费时间: 42 毫秒！
2025-07-18 02:50:22.115 [] [b152597b47dc247e/edc089559b412229] [http-nio-9009-exec-18] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:22 操作结束时间: 2025-07-18 02:50:22!总共花费时间: 67 毫秒！
2025-07-18 02:50:22.178 [] [fde0f5c8afcf88a1/9a7dfab237636e0e] [http-nio-9009-exec-19] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:22 操作结束时间: 2025-07-18 02:50:22!总共花费时间: 44 毫秒！
2025-07-18 02:50:23.021 [] [6ffffbad4795806d/706a30af0f3ccf5f] [http-nio-9009-exec-20] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:22 操作结束时间: 2025-07-18 02:50:23!总共花费时间: 53 毫秒！
2025-07-18 02:50:28.017 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 02:50:29.489 [] [2780353c44240662/829120952373ce4b] [http-nio-9009-exec-22] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:29 操作结束时间: 2025-07-18 02:50:29!总共花费时间: 33 毫秒！
2025-07-18 02:50:29.549 [] [fa9a2f1595c55dab/92b21a511fa98d85] [http-nio-9009-exec-23] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:29 操作结束时间: 2025-07-18 02:50:29!总共花费时间: 43 毫秒！
2025-07-18 02:50:33.661 [] [26c6272b7db8b7b6/cdbfa4426e39e7e3] [http-nio-9009-exec-25] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:33 操作结束时间: 2025-07-18 02:50:33!总共花费时间: 38 毫秒！
2025-07-18 02:50:33.722 [] [6a0b83e1374fd968/bc01adc124f4394e] [http-nio-9009-exec-26] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:33 操作结束时间: 2025-07-18 02:50:33!总共花费时间: 45 毫秒！
2025-07-18 02:50:36.298 [] [e740ba610d32e7ec/c98c3fab98b28e93] [http-nio-9009-exec-27] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:36 操作结束时间: 2025-07-18 02:50:36!总共花费时间: 48 毫秒！
2025-07-18 02:50:36.373 [] [2c08b65e17aa849d/95be4d9dca618f56] [http-nio-9009-exec-28] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案登记 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:36 操作结束时间: 2025-07-18 02:50:36!总共花费时间: 46 毫秒！
2025-07-18 02:50:36.433 [] [24c20d4a12a4d610/41f15c23f6a1edc5] [http-nio-9009-exec-29] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 档案移交 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-18 02:50:36 操作结束时间: 2025-07-18 02:50:36!总共花费时间: 44 毫秒！
2025-07-18 02:55:28.029 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:00:28.033 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:05:28.041 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:10:28.054 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:15:28.055 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:20:28.061 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:25:28.071 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:30:28.084 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:35:28.093 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:40:28.095 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:45:28.105 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:50:28.117 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 03:55:28.130 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 08:50:04.067 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 08:55:04.068 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 09:00:04.080 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 09:05:04.093 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 09:10:04.100 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 09:15:04.110 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 09:20:04.114 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
2025-07-18 09:25:04.122 [] [/] [AsyncResolver-bootstrap-executor-0] INFO  c.n.d.s.r.aws.ConfigClusterResolver - Resolving eureka endpoints via configuration
