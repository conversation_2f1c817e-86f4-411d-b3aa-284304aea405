package com.sunyard.util;
/**
 * 批注表的系统默认字段
 * <AUTHOR>
 *
 */
public class AnnoSystemColumn {
	public final static String CONTENT_ID = "CONTENT_ID";
	public final static String FILE_NO = "FILE_NO";
	public final static String VERSION = "VERSION";
	public final static String ANNOTATION_ID = "ANNOTATION_ID";
	public final static String ANNOTATION_TIME = "ANNOTATION_TIME";
	public final static String ANNOTATION_CONTENT = "ANNOTATION_CONTENT";
	public final static String ANNOTATION_COLOR = "ANNOTATION_COLOR";
	public final static String ANNOTATION_USER = "ANNOTATION_USER";
	public final static String ANNOTATION_STATE = "ANNOTATION_STATE";
	public final static String ANNOTATION_X1POS = "ANNOTATION_X1POS";
	public final static String ANNOTATION_Y1POS = "ANNOTATION_Y1POS";
	public final static String ANNOTATION_X2POS = "ANNOTATION_X2POS";
	public final static String ANNOTATION_Y2POS = "ANNOTATION_Y2POS";
	
	/**
	 * 得到字段
	 * @return 字段数组
	 */
	public static String[] getColumns() {
		String[] strs = { CONTENT_ID, FILE_NO, VERSION, ANNOTATION_ID,
				ANNOTATION_TIME, ANNOTATION_CONTENT, ANNOTATION_COLOR,
				ANNOTATION_USER, ANNOTATION_STATE, ANNOTATION_X1POS,
				ANNOTATION_Y1POS, ANNOTATION_X2POS, ANNOTATION_Y2POS };
		return strs;
	}
	
	/**
	 * 得到字段String
	 * @return 字段String
	 */
	public static String getColumnsString() {
		return  "CONTENT_ID, FILE_NO, VERSION, ANNOTATION_ID," +
				"ANNOTATION_TIME, ANNOTATION_CONTENT, ANNOTATION_COLOR," +
				"ANNOTATION_USER, ANNOTATION_STATE, ANNOTATION_X1POS," +
				"ANNOTATION_Y1POS, ANNOTATION_X2POS, ANNOTATION_Y2POS";
	};
}