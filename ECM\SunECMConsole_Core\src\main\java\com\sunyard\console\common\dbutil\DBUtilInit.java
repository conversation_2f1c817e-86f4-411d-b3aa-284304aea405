package com.sunyard.console.common.dbutil;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

/**
 * <p>
 * Title: 数据库操作类DBUtil初始化
 * </p>
 * <p>
 * Description: 初始化数据库操作类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@WebListener
public class DBUtilInit implements ServletContextListener {
	final static Logger log = LoggerFactory.getLogger(DBUtilInit.class);

	public void contextInitialized(ServletContextEvent event) {

		log.debug("======开始初始化DBUtil=====");
		SpringUtil.ctx = WebApplicationContextUtils
				.getWebApplicationContext(event.getServletContext());
		DataBaseUtil.SUNECM = DbUtil.stringDbUtil("SunECMConsole", true);
		log.debug("======DBUtil初始化完成=====");
	}

	public void contextDestroyed(ServletContextEvent event) {
		DataBaseUtil.SUNECM = null;
	}
}
