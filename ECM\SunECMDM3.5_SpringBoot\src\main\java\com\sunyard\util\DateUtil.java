package com.sunyard.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class DateUtil {
	private final static Logger log = LoggerFactory.getLogger(DateUtil.class);

	/**
	 * 获取系统当前时间
	 * @return yyyy-MM-dd HH:mm:ss字符串
	 */
	public static String getNowTime(){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdf.format(new Date());
	}
	
	/**
	 * 获取8位天日期格式
	 * @return
	 */
	public static String get8bitDateStr(){
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取8位天日期格式
	 * @return
	 */
	public static String get8bitDateStr(Date date){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取4位年日期格式
	 * @return
	 */
	public static String get4bitDateStr(){
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取4位年日期格式
	 * @return
	 */
	public static String get4bitDateStr(Date date){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取6位月日期格式
	 * @return
	 */
	public static String get6bitDateStr(){
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取6位月日期格式
	 * @return
	 */
	public static String get6bitDateStr(Date date){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取12位分钟日期格式
	 * @return
	 */
	public static String get12bitDateStr(){
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取12位分钟日期格式
	 * @return
	 */
	public static String get12bitDateStr(Date date){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取10位小时日期格式 
	 * @return
	 */
	public static String get10bitDateStr() {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");
		String s_date = sdf.format(date);
		long l_date = Long.parseLong(s_date);
		String ddate = String.valueOf(l_date);
		return ddate;
	}
	
	/**
	 * 获取14位精确到秒日期格式
	 * 
	 * @return
	 */
	public static String get14bitDateStr(){
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取14位精确到秒日期格式
	 * 
	 * @param date
	 *            传入的时间
	 * @return
	 */
	public static String get14bitDateStr(Date date){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 获取某一格式的日期
	 * @param format
	 * @return
	 */
	public static  String getMDrqzhsti(String format){
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		String s_date = sdf.format(date);
		return s_date;
	}
	
	/**
	 * 将time加上或减去minDate分钟
	 * 
	 * @param time
	 *            日期
	 * @param minDate
	 *            加上或减去的分钟
	 * @param option
	 *            add(加上)min(减去)
	 * @return
	 */
	public static String getMinusDate(String time, String minDate, String option) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
		try {
			Date date = sdf.parse(time);
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(date);
			if ("ADD".equalsIgnoreCase(option)) {
				calendar.add(Calendar.MINUTE, +Integer.parseInt(minDate));
			} else {
				calendar.add(Calendar.MINUTE, -Integer.parseInt(minDate));
			}
			String s_date = sdf.format(calendar.getTime());
			long l_date = Long.parseLong(s_date);
			String ddate = String.valueOf(l_date);
			return ddate;
		} catch (ParseException e) {
			log.error("线程出错["+time+"]");
		}
		return "";
	}
//	public static void main(String[] args) {
//		System.out.println(getMinusDate(DateUtil
//				.getMDrqzhsti("yyyyMMddHHmm"), String.valueOf(Integer
//						.parseInt("2") * 24 * 60), "MIN"));
//	}
	/**
	 * 获取10位小时日期格式 
	 * @return
	 */
	public static String get10bitDateStr(Date date) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");
		String s_date = sdf.format(date);
		long l_date = Long.parseLong(s_date);
		String ddate = String.valueOf(l_date);
		return ddate;
	}
	
	/**
	 * 获取1970年到当前的分钟数
	 * @return
	 */
	public static long getMinutesDate(){
		Date now = new Date();
		return now.getTime()/(30*60*1000);
	}

	
	/**
	 * 获取x分钟前的日期时间，12位String
	 * @param x 分钟数
	 * @return
	 */
	public static String getTimeBeforeX(int x){
		Date date = new Date();
		long time = date.getTime() - (x*60*1000);
		Date newDate = new Date(time);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmm");
		String minbefore = sdf.format(newDate);
		return minbefore;
	}
	
	/**
	 * 获取时分秒.毫秒的时间格式
	 * @return
	 */
	public static String getHms(){
		return getHms(new Date());
	}
	
	/**
	 * 获取12位分钟日期格式
	 * @return
	 */
	public static String getHms(Date date){
		SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss.SSS");
		String s_date = sdf.format(date);
		return s_date;
	}
	public static List<String> getBetweenDayList(String start, String end, boolean includeStart) throws ParseException {
		List<String> r = new ArrayList<String>();
		if (includeStart) {
			r.add(start);
		}
		String next = null;
		try {
			next = getNextDay(start);
		} catch (ParseException e) {
			log.error("解析日期字符出错：" + start, e);
			throw e;
		}
		while (next.compareTo(end) <= 0) {
			r.add(next);
			try {
				next = getNextDay(next);
			} catch (ParseException e) {
				log.error("解析日期字符出错：" + next, e);
				throw e;
			}
		}
		return r;
	}	
	public static String getNextDay(String day) throws ParseException {
		SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
		Date beginDate = dft.parse(day);
		Calendar date = Calendar.getInstance();
		date.setTime(beginDate);
		date.add(Calendar.DATE, 1);
		String dateString = dft.format(date.getTime());
		return dateString;
	}
}