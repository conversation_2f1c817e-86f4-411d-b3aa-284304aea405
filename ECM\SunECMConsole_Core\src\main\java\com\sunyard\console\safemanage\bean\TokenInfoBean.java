package com.sunyard.console.safemanage.bean;
/**
 * <p>Title: 可申请动态令牌机器bean</p>
 * <p>Description: 存放可申请动态令牌机器信息</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class TokenInfoBean {
	private String ip;			//机器IP
	private String server_info;	//机器信息
	public String getIp() {
		return ip;
	}
	public void setIp(String ip) {
		this.ip = ip;
	}
	public String getServer_info() {
		return server_info;
	}
	public void setServer_info(String server_info) {
		this.server_info = server_info;
	} 
	public String toString(){
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append("ip:").append(ip);
		sBuilder.append(";server_info:").append(server_info);
		return sBuilder.toString();
	}
}
