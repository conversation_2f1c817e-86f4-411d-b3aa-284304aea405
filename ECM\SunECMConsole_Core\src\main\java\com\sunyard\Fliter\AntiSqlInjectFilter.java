package com.sunyard.Fliter;

import com.sunyard.util.LoadProperties;
import com.sunyard.util.StringUtil;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.List;

public class AntiSqlInjectFilter implements Filter {
	final static  private Logger log = LoggerFactory.getLogger(AntiSqlInjectFilter.class);

	public void destroy() {
		// TODO Auto-generated method stub
	}

	public void init(FilterConfig arg0) throws ServletException {
		// TODO Auto-generated method stub
	}

	public void doFilter(ServletRequest args0, ServletResponse args1, FilterChain chain) throws IOException, ServletException {
		HttpServletRequest req = (HttpServletRequest) args0;
		HttpServletResponse res = (HttpServletResponse) args1;

		// 获得所有请求参数名
		Enumeration<?> params = req.getParameterNames();
		while (params.hasMoreElements()) {
			// 得到参数名
			String name = params.nextElement().toString();
			String pn = req.getParameter(name);
			if (StringUtil.stringIsNull(pn)) {
				continue;
			}
			String msg=sqlValidate(name, pn);
			if (msg!=null) {
				JSONObject jsonResp = new JSONObject();
				jsonResp.put("success", false);
				jsonResp.put("valid", false);
				jsonResp.put("message", "数据含有非法字符["+msg+"]");
				res.setContentType("application/json");
				res.setCharacterEncoding("utf-8");
				res.getWriter().write(jsonResp.toString());
				res.getWriter().flush();
				return;
			}
		}
		chain.doFilter(args0, args1);
	}

	// 效验
	protected static String sqlValidate(String name, String value) {
		value = value.toLowerCase();// 统一转为小写
		List<String> keywords = LoadProperties.getKeyWords();
		if(keywords==null||keywords.size()==0){
			return null;
		}
		for (String kw : keywords) {
			if (value.equalsIgnoreCase(kw)) {
				String msg=name+"="+value;
				log.error("非法字符["+msg + "]");
				return msg;
			}
		}
		return null;
	}
}
