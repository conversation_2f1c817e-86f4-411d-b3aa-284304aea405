2025-07-25 01:09:37.970 [OrganNo_null_UserNo_null] [94419d0b8645632f/fd05f2afd09a7dd2] [http-nio-9007-exec-68] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-25 01:09:38.038 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-25 01:09:38.039 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-25 01:09:38.039 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-25 01:09:38.041 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-25 01:09:38.042 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-25 01:09:38.042 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-25 01:09:38.044 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-25 01:09:38.045 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-25 01:09:38.045 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-25 01:09:38.047 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-25 01:09:38.047 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-25 01:09:38.047 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-25 01:09:38.048 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-25 01:09:38.058 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-25 01:09:38.058 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-25 01:09:38.059 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-25 01:09:38.059 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-25 01:09:38.062 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-25 01:09:38.063 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-25 01:09:38.063 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-25 01:09:38.065 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-25 01:09:38.065 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-25 01:09:38.065 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-25 01:09:38.067 [OrganNo_null_UserNo_null] [94419d0b8645632f/0709dc921f15806d] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-25 01:09:38.077 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-25 01:09:38.078 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-25 01:09:38.090 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-25 01:09:38.098 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-25 01:09:38.098 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-25 01:09:38.104 [OrganNo_null_UserNo_null] [94419d0b8645632f/1d8f2ece2280b26e] [http-nio-9007-exec-68] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-25 01:09:38.115 [OrganNo_null_UserNo_null] [94419d0b8645632f/fd05f2afd09a7dd2] [http-nio-9007-exec-68] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
