<component name="libraryTable">
  <library name="lib1">
    <CLASSES>
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/target/classes/lib/sunecmoffline.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/target/classes/lib/sun_comm_service_init.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/target/classes/lib/DmJdbcDriver18.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/target/classes/lib/SunECMClientV3.5.0.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/target/classes/lib/redisclien.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/target/classes/lib/ESAPI.jar!/" />
      <root url="jar://$PROJECT_DIR$/SunECMDM_Core/target/classes/lib/postgresql-42.2.19.jar!/" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>