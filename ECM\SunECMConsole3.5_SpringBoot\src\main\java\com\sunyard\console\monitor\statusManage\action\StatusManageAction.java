package com.sunyard.console.monitor.statusManage.action;

/**
 * <p>
 * Title: 同步信息管理action
 * </p>
 * <p>
 * Description: 用于管理同步信息的获取,以及删改等操作的action访问类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2012
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR> 
 * @version 1.0
 */
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.console.monitor.statusManage.bean.ConfigInfoSynchroBean;
import com.sunyard.console.monitor.statusManage.dao.StatusManageDao;
import com.sunyard.console.threadpoool.IssueUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class StatusManageAction extends BaseAction {
	private static final long serialVersionUID = -8063924300482847108L;
	private  final static  Logger log = LoggerFactory.getLogger(StatusManageAction.class);
	@Autowired
	private StatusManageDao statusManageDao;    //状态管理 数据库操作类
	private int start;    //开始范围
	private int limit;    //结束范围
	private String config_table;    //配置表
	private String config_code;    //配置编号
	private String node_id;   //服务器ID
	private String config_ids;    //配置ID
	
	public StatusManageDao getStatusManageDao() {
		return statusManageDao;
	}
	public void setStatusManageDao(StatusManageDao statusManageDao) {
		this.statusManageDao = statusManageDao;
	}

	public int getStart() {
		return start;
	}
	public void setStart(int start) {
		this.start = start;
	}
	public int getLimit() {
		return limit;
	}
	public void setLimit(int limit) {
		this.limit = limit;
	}
	public String getConfig_table() {
		return config_table;
	}
	public void setConfig_table(String configTable) {
		config_table = configTable;
	}
	public String getConfig_code() {
		return config_code;
	}
	public void setConfig_code(String configCode) {
		config_code = configCode;
	}
	public String getNode_id() {
		return node_id;
	}
	public void setNode_id(String nodeId) {
		node_id = nodeId;
	}
	public String getConfig_ids() {
		return config_ids;
	}
	public void setConfig_ids(String configIds) {
		config_ids = configIds;
	}
	/**
	 * 查询所有节点的状态信息
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/statusManage/getNodeStatusAction.action", method = RequestMethod.POST)
	public String getNodeStatusInfo(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		log.info( "--getNodeStatusInfo(start)");
		start = (page_int-1) * limit_int;
		String jsonStr = null;
		try {
			List<ConfigInfoSynchroBean> nodeList = statusManageDao.getNodeStatusList(start+1 , limit_int);
			List<ConfigInfoSynchroBean> allNodeList = statusManageDao.getAllNodeStatusList();
			if (nodeList == null || allNodeList == null) {
				log.debug( "--getNodeStatusInfo-->nodeList is null or allNodeList is null");
				JSONObject jsonResp=new JSONObject();
				jsonResp.put("success", false);
				jsonResp.put("message", "无节点状态信息!!!");
				jsonStr = jsonResp.toString();
				this.outJsonString(jsonStr);
				return null;
			}
			jsonStr = new JSONUtil().createJsonDataByColl(nodeList,allNodeList.size(),new ConfigInfoSynchroBean());
			log.debug( "--getNodeStatusInfo-->nodeList:" + nodeList);
		} catch (Exception e) {
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取节点状态信息失败!!!");
			jsonStr = jsonResp.toString();
			log.error( "状态管理->查询所有节点的状态信息失败->"+e.toString());
		}
		log.info( "--getNodeStatusInfo(over)");
		this.outJsonString(jsonStr);
		return null;
	}
	
	/**
	 * 删除选择的节点信息
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/statusManage/delSynStatusInfoAction.action", method = RequestMethod.POST)
	public String delSynStatusInfo(String config_ids) {
		log.info( "--delSynStatusInfo(start)-->config_ids:" + config_ids);
		String jsonStr = null;
		try {
			
			statusManageDao.delSyncStatusInfo(config_ids);
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "删除节点信息成功!");
			jsonStr = jsonResp.toString();
			log.debug( "--delSynStatusInfo-->删除节点成功！");
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", true);
			jsonResp.put("message", "删除节点信息失败!");
			jsonStr = jsonResp.toString();
			log.error( "状态管理->删除节点信息失败->"+e.toString());
		}
		log.info( "--delSynStatusInfo(over)-->config_ids:" + config_ids);
		this.outJsonString(jsonStr);
		return null;
	}
	
	/**
	 * 手动同步节点
	 * @return
	 */

	@ResponseBody
	@RequestMapping(value = "/statusManage/send2LowerLevelsAction.action", method = RequestMethod.POST)
	public String send2LowerLevels(String config_ids) {
		log.info( "--send2LowerLevels(start)-->config_ids:" + config_ids);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try {
			if ("".equals(config_ids) || config_ids == null) {
				log.debug( "--send2LowerLevels-->config_ids is null");
				jsonResp.put("success", false);
				jsonResp.put("message", "没有可以进行同步服务器!!!");
				jsonStr = jsonResp.toString();
				this.outJsonString(jsonStr);
				return null;
			}

			String[] ids = config_ids.split(",");
            Set<Integer> dmIdSet=new HashSet<Integer>();
            Set<Integer> uaIdSet=new HashSet<Integer>();
			for (int i = 0; i < ids.length; i++) {
				String config_id = ids[i];
				ConfigInfoSynchroBean configInfoSynchro = statusManageDao
						.getSingleServerStatus(config_id);
				if (configInfoSynchro == null) {
					jsonResp.put("success", false);
					jsonResp.put("message", "没有可以进行同步服务器!!!");
					jsonStr = jsonResp.toString();
					this.outJsonString(jsonStr);
					return null;
				}
				if(configInfoSynchro.getTarget_server_type()==1){
					dmIdSet.add(configInfoSynchro.getNode_id());
				}else{
					uaIdSet.add(configInfoSynchro.getNode_id());					
				}
			}
            IssueUtils.IssueInfoToDM(IssueUtils.getDMServerInfoByDmServerIds(dmIdSet));	
            IssueUtils.IssueInfoToUA(IssueUtils.getUaServerInfoByUaServerIds(uaIdSet));	
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "加入同步列表成功!");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "手动同步失败!");
			jsonStr = jsonResp.toString();
			log.error( "状态管理->手动同步失败->"+e.toString());
		}
		log.info( "--send2LowerLevels(over)");
		this.outJsonString(jsonStr);
		return null;	
	}
}
