package com.sunyard.ecm.server.cache.wsserver;

import com.sunyard.ecm.server.bean.LifeCycleStrategyBean;
import com.sunyard.ecm.server.bean.NodeInfo;
import com.sunyard.ecm.server.cache.LazySingleton;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.ws.utils.XMLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Title:监听注册服务类
 * </p>
 * <p>
 * Description: 控制台下发配置信息时，保存信息，调用相关服务
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ServiceInfoOption {
	private final static  Logger log = LoggerFactory.getLogger(ServiceInfoOption.class);
	private static configRegist regist = configRegist.getInstance();

	/**
	 * 注册内容模型模版
	 * 
	 * @param xml
	 *            接受到的xml
	 */
	public static void changeModelDoc(String xml) {
		try {
			log.debug( "内容模型模版改变-->接收xml:" + xml);
			/* 修改下发内容为主动获取配置信息 */
			// List<ModelTemplateBean> list = XMLUtil.xml2list(XMLUtil
			// .removeHeadRoot(xml), ModelTemplateBean.class);
			//
			// LazySingleton.updateModel(list);

			LazySingleton.getConfigInfo();

			regist.bind(configRegist.modelDoc, xml);
		} catch (Exception e) {
			log.error( "同步内容模型模版失败-->" + e.getMessage());
			throw new RuntimeException("同步内容模型模版失败-->" + e.getMessage());
		}

	}

	/**
	 * 注册服务器节点
	 * 
	 * @param xml
	 *            接受到的xml
	 */
	public static void changeNodeInfo(String xml) {
		try {
			log.debug( "服务器节点改变-->接收xml:" + xml);
			/* 修改下发内容为主动获取配置信息 */
			// List<NodeInfo> list =
			// XMLUtil.xml2list(XMLUtil.removeHeadRoot(xml), NodeInfo.class);
			//
			// LazySingleton.updateNodeInfo(list);
			LazySingleton.getConfigInfo();

			regist.bind(configRegist.contentServer, xml);
		} catch (Exception e) {
			log.error( "同步服务器节点失败-->" + e.getMessage());
			throw new RuntimeException("同步服务器节点信息失败-->" + e.getMessage());
		}

	}

	/**
	 * 内容模型的表改变 方法名是changeModelTable
	 * 
	 * @param xml
	 *            接受到的xml
	 */
	public static void changeMetaTable(String xml) {
		try {
			log.debug( "内容模型的表改变-->接收xml:" + xml);
			/* 修改下发内容为主动获取配置信息 */

			// List<ModelTableBean> list = XMLUtil.xml2list(XMLUtil
			// .removeHeadRoot(xml), ModelTableBean.class);
			//
			// LazySingleton.updateModelTable(list);
			LazySingleton.getConfigInfo();

			regist.bind(configRegist.metaTable, xml);
		} catch (Exception e) {
			log.error( "同步内容模型的表失败-->" + e.getMessage());
			throw new RuntimeException("同步内容模型的表信息失败-->" + e.getMessage());
		}
	}

	/**
	 * 存储对象信息改变 未提供保存到内存中的方法
	 * 
	 * @param xml
	 *            接受到的xml
	 */
	public static void changeStoreObject(String xml) {
		try {
			log.debug( "存储对象信息改变-->接收xml:" + xml);
			/* 修改下发内容为主动获取配置信息 */
			// List<StoreObjectBean> list = XMLUtil.xml2list(XMLUtil
			// .removeHeadRoot(xml), StoreObjectBean.class);
			LazySingleton.getConfigInfo();

			regist.bind(configRegist.storeObject, xml);
		} catch (Exception e) {
			log.error( "同步存储对象信息失败-->" + e.getMessage());
			throw new RuntimeException("同步存储对象信息信息失败-->" + e.getMessage());
		}

	}

	/**
	 * 内容模型列表信息改变
	 * 
	 * @param xml
	 *            接受到的xml
	 * */
	public static void changeAllModelMsg(String xml) {
		try {
			log.debug( "内容模型列表信息改变-->接收xml:" + xml);
			/* 修改下发内容为主动获取配置信息 */
			// List<AllModelMsgBean> list = XMLUtil.xml2list(XMLUtil
			// .removeHeadRoot(xml), AllModelMsgBean.class);
			//
			// LazySingleton.updateAllModelMsg(list);
			LazySingleton.getConfigInfo();
			regist.bind(configRegist.allModelMsg, xml);
		} catch (Exception e) {
			log.error( "同步内容模型列表信息失败-->" + e.getMessage());
			throw new RuntimeException("同步内容模型列表信息失败-->" + e.getMessage());
		}
	}

	/**
	 * 生命周期策略改变 部分实现
	 * 
	 * @param xml
	 *            接受到的xml
	 * */
	public static void changeScheduler(String xml) {
		try {
			log.debug( "生命周期策略改变-->接收xml:" + xml);
			List<LifeCycleStrategyBean> list = XMLUtil.xml2list(XMLUtil
					.removeHeadRoot(xml), LifeCycleStrategyBean.class);
			LazySingleton.updateLifeCycleStrategy(list);
			regist.bind(configRegist.scheduler, xml);
		} catch (Exception e) {
			log.error( e.toString());
			log.error( "同步生命周期策略失败-->" + e.getMessage());
			throw new RuntimeException("同步生命周期策略信息失败-->" + e.getMessage());
		}
	}

	public static void changeSchedulerOnOrOff(String xml) {
		log.debug( "生命周期策略禁用,启用改变-->接收xml:" + xml);
	}

	/**
	 * 内容服务器组和内容对象对应卷
	 * 
	 * @param xml
	 *            接受到的xml
	 * */
	public static void changeSgroupmodleSet(String xml) {
		try {
			log.debug( "服务器组和内容对象对应卷改变-->接收xml:" + xml);
			// List<SGroupModleSetBean> list = XMLUtil.xml2list(XMLUtil
			// .removeHeadRoot(xml), SGroupModleSetBean.class);
			// LazySingleton.updateSGroupModle(list);
			LazySingleton.getConfigInfo();
			regist.bind(configRegist.sgroupmodleSet, xml);
		} catch (Exception e) {
			log.error( "同步服务器组和内容对象对应卷失败-->" + e.getMessage());
			throw new RuntimeException("同步服务器组和内容对象对应卷失败-->" + e.getMessage());
		}

	}

	/**
	 * 日志策略变化
	 * 
	 * @param xml
	 *            接受到的xml
	 * */
	public static void changeLogRule(String xml) {
//		try {
//			log.debug( "日志策略改变-->接收xml:" + xml);
//			List<DMLogRuleBean> list = XMLUtil.xml2list(XMLUtil
//					.removeHeadRoot(xml), DMLogRuleBean.class);
//			LazySingleton.updateLogRule(list);
//			// 修改log4j.properties 增加相对应的类的日志输出级别
//			Log4jConfigOperUtil log4jConfigOperUtil = new Log4jConfigOperUtil();
//			String path = Log4jConfigOperUtil.class.getClassLoader()
//					.getResource("").getPath()
//					+ "log4j.properties";
//			log.debug("修改日志log4j.properties文件-->路径：" + path);
//			Properties properties = log4jConfigOperUtil.readProperties(path);
//			for (DMLogRuleBean dmLogRuleBean : list) {
//				log4jConfigOperUtil.writeDMLogRule(properties, path,
//						dmLogRuleBean);
//			}
//			regist.bind(configRegist.logRule, xml);
//		} catch (Exception e) {
//			log.error( "同步日志策略失败-->" + e.getMessage());
//			throw new RuntimeException("同日志策略失败-->" + e.getMessage());
//		}
	}

	/**
	 * 服务器组启用和禁用
	 * 
	 * @param msg
	 *            接受到的服务器状态
	 * */
	public static void setDMStat(String msg) {
		log.debug( "当前服务器状态改变-->接收xml:" + msg);
		try {
			String[] strs = msg.split(":");
			Map<String, NodeInfo> allNodeInfoTable = LazySingleton
					.getInstance().allNodeInfoTable.getAllNodeInfoTable();
			if (allNodeInfoTable == null) {
				LazySingleton.resetConfigInfo();
				return;
			}
			NodeInfo info = allNodeInfoTable.get(strs[0]);
			info.setState(strs[1]);
			NodeInfo selfInfo = LazySingleton.getInstance().load
					.getNodeInfoBean();
			if (selfInfo.getServer_id() == null) {
				throw new SunECMException(
						SunECMExceptionStatus.CONSOLE_CONFIG_FAIL,
						"内容储存服务器获取不到console的配置信息,服务器ID为null");
			}
			if (selfInfo.getServer_id().equals(strs[0])) {
				selfInfo.setState(strs[1]);
				// 若是启用则重新获取一次console配置的信息
				LazySingleton.resetConfigInfo();
			}
			regist.bind(configRegist.conentServerStat, msg);
		} catch (Exception e) {
			log.error( "当前服务器状态改变失败-->" + e.getMessage());
			throw new RuntimeException("当前服务器状态改变失败，" + e.getMessage());
		}
	}

	public static configRegist getRegist() {
		return regist;
	}

	public static void setRegist(configRegist regist) {
		ServiceInfoOption.regist = regist;
	}

}