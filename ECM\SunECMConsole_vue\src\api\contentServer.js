import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getContentServerList(data) {
  const obj =  { 
    'page': data.page, 
    limit: data.limit, 
    'server_id': data.server_id, 
    'server_name': encodeURI(data.server_name),
    }
  return request({
    url: '/contentServerManage/getContentServerListAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function addContentServer(data) {
  let url = '/contentServerManage/addContentServerAction'+EndUrl.EndUrl;
  // data.remark = encodeURI(data.remark);
  // data.server_name = encodeURI(data.server_name);
  // data.optionFlag = 'create1';
    const obj = {
    'optionFlag': 'create1',
    'server_id' : data.server_id,
    'server_name': encodeURI(data.server_name),
    'http_port': data.http_port,
    'isdb_conn': parseInt(data.isdb_conn),
    'remark': encodeURI(data.remark),
    'socket_port': data.socket_port,
    'state': parseInt(data.state),
    'server_ip': data.server_ip,
    'https_port': data.https_port,
    'trans_protocol': data.trans_protocol,
    'weight': data.weight,
    'group_id': data.group_id
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function updateContentServer(data) {
  let url = '/contentServerManage/addContentServerAction'+EndUrl.EndUrl;
  // data.optionFlag = 'update1';
 const obj =  {
      'optionFlag': 'update1',
      'server_id' : data.server_id,
      'server_name': encodeURI(data.server_name),
      'http_port': data.http_port,
      'isdb_conn': data.isdb_conn,
      'remark': encodeURI(data.remark),
      'socket_port': data.socket_port,
      'state': data.state,
      'server_ip': data.server_ip,
      'https_port': data.https_port,
      'trans_protocol': data.trans_protocol,
      'weight': data.weight,
      'group_id': data.group_id
    }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function stopContentServer(data) {
  return request({
    url: '/contentServerManage/stopContentServerAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'server_ids': data.server_id
    }
  })
}

export function startContentServer(data) {
  return request({
    url: '/contentServerManage/startContentServerAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'server_ids': data.server_id
    }
  })
}

export function getGroupNameList() {
  return request({
    url: '/contentServerManage/getGroupNameAction'+EndUrl.EndUrl,
    method: 'post'
  })
}

export function singletonInfoSearch(data){
  let url = '/singletonManage/getSingletonInfoAction'+EndUrl.EndUrl;
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function resetSingletonInfoSearch(data){
  let url = '/singletonManage/resetSingletonInfoAction'+EndUrl.EndUrl;
  const obj = {
    'server_ip' : data.server_ip,
    'http_port': data.http_port.toString()
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}