package com.sunyard.console.common.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.SecureRandom;

/**
 * 字符串加密、解密类
 * <AUTHOR>
 * @version 1.0
 *
 */
public class CryptUtil {   
	private static final CryptUtil instance = new CryptUtil();
	private final static String encoding = "UTF-8";  
	private static final String Algorithm = "DESede"; // 定义 加密算法,可用 DESede  , AES
	private static final int Keysize = 168; //AES  128 ,  DESede  168
	
	public static CryptUtil getInstance() {   
	       return instance;   
	} 
	  /**  
	   * AES加密  
	   *   
	   * @param content  
	   * @param ss  
	   * @return  
	   */  
	  public static String encryptAES(String content, String ss) throws Exception{   
	      byte[] encryptResult = encrypt(content, ss);   
	      String encryptResultStr = parseByte2HexStr(encryptResult);   
	      // BASE64位加密   
	      encryptResultStr = ebotongEncrypto(encryptResultStr);   
	      return encryptResultStr;   
	  }   

	  /**  
	   * AES解密  
	   *   
	   * @param encryptResultStr  
	   * @param ss  
	   * @return  
	   */  
	  public static String decryptAES(String encryptResultStr, String ss) throws Exception {   
		  try{
			  // BASE64位解密   
		      String decrpt = ebotongDecrypto(encryptResultStr);   
		      byte[] decryptFrom = parseHexStr2Byte(decrpt);   
		      byte[] decryptResult = decrypt(decryptFrom, ss);   
		      return new String(decryptResult ,encoding);   
		  }catch (Exception e) {
			  throw e; 
		}
	  }   

	   /**  
	   * 加密字符串  
	   */  
	  public static String ebotongEncrypto(String str) throws Exception{   
       Base64 base64encoder = new Base64();   
	      String result = str;   
	      if (str != null && str.length() > 0) {   
	          try {   
	              byte[] encodeByte = str.getBytes(encoding);   
	              result = new String(base64encoder.encode(encodeByte));   
	          } catch (Exception e) {   
	              throw e;   
	          }   
	      }   
	      //base64加密超过一定长度会自动换行 需要去除换行符   
	      return result.replaceAll("\r\n", "").replaceAll("\r", "").replaceAll("\n", "");   
	  }   

	  /**  
	   * 解密字符串  
	   */  
	  public static String ebotongDecrypto(String str) throws IOException{   
		  Base64 base64decoder = new Base64();   
	      byte[] encodeByte = base64decoder.decode(str.getBytes());   
		  return new String(encodeByte);   
	  }   
	  /**    
	   * 加密    
	   *     
	   * @param content 需要加密的内容    
	   * @param ss  加密密码    
	   * @return    
	   */     
	  private static byte[] encrypt(String content, String ss) throws Exception{      
	          try {                 
	                  KeyGenerator kgen = KeyGenerator.getInstance(Algorithm);    
	                  //防止linux下 随机生成key   
	                  SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG" );      
	                  secureRandom.setSeed(ss.getBytes());      
	                  kgen.init(Keysize, secureRandom);   
	                  SecretKey secretKey = kgen.generateKey();      
	                  byte[] enCodeFormat = secretKey.getEncoded();      
	                   SecretKeySpec key = new SecretKeySpec(enCodeFormat, Algorithm);      
	                   Cipher cipher = Cipher.getInstance(Algorithm);// 创建密码器      
	                   byte[] byteContent = content.getBytes(encoding);      
	                   cipher.init(Cipher.ENCRYPT_MODE, key);// 初始化      
	                   byte[] result = cipher.doFinal(byteContent);      
	                   return result; // 加密      
	           } catch (Exception e) {      
	                   throw e;      
	           }      
	   }     
	 
	 
	   /**解密    
	    * @param content  待解密内容    
	    * @param ss 解密密钥    
	    * @return    
	    */     
	   private static byte[] decrypt(byte[] content, String ss) throws Exception{      
	           try {      
	                    KeyGenerator kgen = KeyGenerator.getInstance(Algorithm);    
	                  //防止linux下 随机生成key   
	                    SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG" );      
	                    secureRandom.setSeed(ss.getBytes());      
	                    kgen.init(Keysize, secureRandom);   
	                    SecretKey secretKey = kgen.generateKey();      
	                    byte[] enCodeFormat = secretKey.getEncoded();      
	                    SecretKeySpec key = new SecretKeySpec(enCodeFormat, Algorithm);                  
	                    Cipher cipher = Cipher.getInstance(Algorithm);// 创建密码器      
	                   cipher.init(Cipher.DECRYPT_MODE, key);// 初始化      
	                   byte[] result = cipher.doFinal(content);      
	                   return result; // 加密      
	           } catch (Exception e) {      
	                   throw e;     
	           }      
	   }     
	 
	   /**将二进制转换成16进制    
	    * @param buf    
	    * @return    
	    */     
	   public static String parseByte2HexStr(byte buf[]) {      
	           StringBuffer sb = new StringBuffer();      
	           for (int i = 0; i < buf.length; i++) {      
	                   String hex = Integer.toHexString(buf[i] & 0xFF);      
	                   if (hex.length() == 1) {      
	                           hex = '0' + hex;      
	                   }      
	                   sb.append(hex.toUpperCase());      
	           }      
	           return sb.toString();      
	   }     
	 
	 
	   /**将16进制转换为二进制    
	    * @param hexStr    
	    * @return    
	    */     
	   public static byte[] parseHexStr2Byte(String hexStr) {      
	           if (hexStr.length() < 1)      
	                   return null;      
	           byte[] result = new byte[hexStr.length()/2];      
	           for (int i = 0;i< hexStr.length()/2; i++) {      
	                   int high = Integer.parseInt(hexStr.substring(i*2, i*2+1), 16);      
	                   int low = Integer.parseInt(hexStr.substring(i*2+1, i*2+2), 16);      
	                   result[i] = (byte) (high * 16 + low);      
	           }      
	           return result;      
	   }     
 
  
//    public static void main(String[] args)  {   
//    	for(int i=0; i<1; i++){
//    		Thread thread = new Thread(
//    				new Runnable(){
//    					public void run(){
//    						for(int j = 0; j<1; j++){
//    							try{
//	    							CryptUtil crypt = CryptUtil.getInstance();         
//	    					        String content = "111";   
//	//    					        System.out.println(crypt.encryptAES(content, "aaa22"));   
//	    					        String dcontent = crypt.encryptAES(content, "key");   
//	//    					        System.out.println(crypt.decryptAES(dcontent, "aaa22"));
//	    					        String result = crypt.decryptAES(dcontent, "key");
//	    					        if(content.equals(result))
//	    					        	System.out.println(dcontent + "==" + content + "=====" + result  + content.equals(result));
//	    					        else
//	    					        	System.err.println(dcontent + "==" + content + "=====" + result  + content.equals(result));
//    							}catch (Exception e) {
//									// TODO: handle exception
//								}
//    						}
//    					}
//    				}
//    		);
//    		thread.start();
//    	}
//        
//  
//    }   
}  

