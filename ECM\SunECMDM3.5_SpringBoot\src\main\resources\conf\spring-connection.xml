<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jaxws="http://cxf.apache.org/jaxws"
	xsi:schemaLocation="http://www.springframework.org/schema/beans 
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
          http://cxf.apache.org/jaxws
		http://cxf.apache.org/schemas/jaxws.xsd
		http://cxf.apache.org/configuration/beans
		http://cxf.apache.org/schemas/configuration/cxf-beans.xsd">


	<bean id="socketInitial" class="com.sunyard.ecm.server.SocketInitial"
		destroy-method="cleanup">
		<constructor-arg type="com.sunyard.ecm.common.trans.GetSocketPort" ref="socketPort" />
	</bean>
		<!-- socketPort 获取 -->
		
	<bean id="socketPort" class="com.sunyard.ecm.common.trans.socket.DMGetSocketPort">
	</bean>
	


<!--	<import resource="classpath:META-INF/cxf/cxf.xml" />-->
	
	<bean id="sunEcmAccess" class="com.sunyard.ecm.server.SunEcmAccessImpl">
		<property name="server" ref="server"></property>
	</bean>
	<jaxws:endpoint id="WsInterface" address="/WsInterface"
		implementor="#sunEcmAccess" />
</beans>