package com.sunyard.es.util;

import java.util.Map;

/*  
 *  must 一定存在的条件
 *  mustNot 一定不存在的条件
 *  should 可能存在的条件
 *  range,里面结构{key:map{"min":数字,日期,字符串类型的变量,"max":数字,日期,字符串类型的变量}}
 *  key对应排序的字段,min对应字段的最小值,max对应字段的最大值
 * 
 */
public class EsBean {
	
	private Map<String, String> must;
	
	private Map<String, String> mustNot;
	
	private Map<String, String> should;
	
	private Map<String,Object> range;
	
	private String indexName;
	
	private int pageNo;
	
	private int pageSize;
	
	private Map<String,Object> map;

	public Map<String, String> getMust() {
		return must;
	}

	public void setMust(Map<String, String> must) {
		this.must = must;
	}

	public Map<String, String> getMustNot() {
		return mustNot;
	}

	public void setMustNot(Map<String, String> mustNot) {
		this.mustNot = mustNot;
	}

	public Map<String, String> getShould() {
		return should;
	}

	public void setShould(Map<String, String> should) {
		this.should = should;
	}

	public Map<String, Object> getRange() {
		return range;
	}

	public void setRange(Map<String, Object> range) {
		this.range = range;
	}

	public String getIndexName() {
		return indexName;
	}

	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}

	public int getPageNo() {
		return pageNo;
	}

	public void setPageNo(int pageNo) {
		this.pageNo = pageNo;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public Map<String, Object> getMap() {
		return map;
	}

	public void setMap(Map<String, Object> map) {
		this.map = map;
	}

	@Override
	public String toString() {
		return "EsBean [must=" + must + ", mustNot=" + mustNot + ", should=" + should + ", range=" + range
				+ ", indexName=" + indexName + ", pageNo=" + pageNo + ", pageSize=" + pageSize + ", map=" + map + "]";
	}
}