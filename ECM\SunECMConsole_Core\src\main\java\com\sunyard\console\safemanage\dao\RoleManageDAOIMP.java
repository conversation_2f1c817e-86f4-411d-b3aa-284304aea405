package com.sunyard.console.safemanage.dao;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.safemanage.bean.NodeBean;
import com.sunyard.console.safemanage.bean.PermissionInfoBean;
import com.sunyard.console.safemanage.bean.RoleCmodelRelBean;
import com.sunyard.console.safemanage.bean.RoleInfoBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Title: 角色管理数据库操作实现类
 * </p>
 * <p>
 * Description: 实现角色管理中数据库操作的方法
 * </p>
 * <p>
 * Copyright: Copyright (c) 2012
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Repository("rmdao")
public class RoleManageDAOIMP implements RoleManageDAO {
	@Autowired
	private PageTool pageTool;
	@Resource(name = "roleInfo_roleID")
	private DataFieldMaxValueIncrementer incrementer;
	private final static  Logger log = LoggerFactory.getLogger(RoleManageDAOIMP.class);

	public DataFieldMaxValueIncrementer getIncrementer() {
		return incrementer;
	}

	public void setIncrementer(DataFieldMaxValueIncrementer incrementer) {
		this.incrementer = incrementer;
	}

	public PageTool getPageTool() {
		return pageTool;
	}

	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}

	/**
	 * 获取全部角色对象列表
	 * 
	 * @return
	 */
	public List<Map<String, String>> getAllRoleList() {
		log.info("--getAllRoleList(start)");
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT ROLE_ID , ROLE_NAME FROM ROLE_INFO WHERE ROLE_STATE =1");
		try {
			log.debug( "--getAllRoleList-->sql:"+sql.toString());
			return DataBaseUtil.SUNECM.queryMapList(sql.toString());
		} catch (Exception e) {
			log.error( "角色管理->获取全部角色对象列表失败->" + e.toString());
			throw new DBRuntimeException("RoleManageDAOIMP===>getAllRoleList:"
					+ e.toString());
		}
	}

	/**
	 * 根据输入信息查询角色列表
	 * 
	 * @param roleName
	 *            角色名称
	 * @param roleState
	 *            角色状态
	 * @param start
	 * @param limit
	 * @return 角色信息列表
	 */
	public List<RoleInfoBean> searchRoleInfoList(String roleName,
			String roleState, int start, int limit) {
		log.info(
				"--searchRoleInfoList(start)-->roleName:"
						+ roleName + ";roleState:" + roleState + ";start:"
						+ start + ";limit:" + limit);
		List<RoleInfoBean> roleInfo = null;
		StringBuffer sql = new StringBuffer();

		sql
				.append("SELECT ROLE_ID , ROLE_NAME , ROLE_STATE , ROLE_DES FROM ROLE_INFO WHERE 1=1");
		List list=new ArrayList();
		// 过滤角色名称
		if (roleName != null && !"".equals(roleName)) {
			sql.append(" AND ROLE_NAME LIKE  '%'||?||'%' ");
			list.add(roleName);
		}
		// 过滤用户状态
		if (roleState != null && !"".equals(roleState)) {
			sql.append(" AND ROLE_NAME LIKE  '%").append(roleName).append("%' ");
//			list.add(roleName);
		}
		log.debug(
				"--searchRoleInfoList-->sql:"
						+ sql.toString());
		try {
			roleInfo = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(
					sql.toString(), start, limit), RoleInfoBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->根据输入信息查询角色列表失败->" + e.toString());
			throw new DBRuntimeException(
					"RoleManageDAOIMP===>searchRoleInfoList:" + e.toString());
		}
		log.info( "--searchRoleInfoList(over)-->roleInfo:"+roleInfo);
		return roleInfo;
	}

	/**
	 * 根据输入信息查询角色列表(取全部记录)
	 * 
	 * @param roleName
	 *            角色名称
	 * @param roleState
	 *            角色状态
	 * @return 角色信息列表
	 */
	public List<RoleInfoBean> searchRoleInfoAllList(String roleName,
			String roleState) {
		log.info(
				"--searchRoleInfoAllList(start)-->roleName:"
						+ roleName + ";roleState:" + roleState);
		List<RoleInfoBean> roleInfo = null;
		StringBuffer sql = new StringBuffer();

		sql
				.append("SELECT ROLE_ID , ROLE_NAME , ROLE_STATE , ROLE_DES FROM ROLE_INFO WHERE 1=1");
		List list=new ArrayList();
		// 过滤角色名称
		if (roleName != null && !"".equals(roleName)) {
			sql.append(" AND ROLE_NAME LIKE  '%").append(roleName).append("%' ");
//			list.add(roleName);
		}
		// 过滤用户状态
		if (roleState != null && !"".equals(roleState)) {
			sql.append(" AND ROLE_STATE =? ");
			list.add(roleState);
		}
		log.debug(
				"--searchRoleInfoAllList-->sql:"
						+ sql.toString());
		try {
			roleInfo = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					RoleInfoBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->根据输入信息查询角色列表(取全部记录)失败->"
					+ e.toString(),e);
			throw new DBRuntimeException(
					"RoleManageDAOIMP===>searchRoleInfoAllList:" + e.toString());
		}
		log.info( "--searchRoleInfoAllList(over)-->roleInfo:"+roleInfo);
		return roleInfo;
	}

	/**
	 * 根据输入的信息创建新的角色
	 * 
	 * @param role
	 *            角色对象
	 * @return 是否成功
	 */
	public boolean addRole(RoleInfoBean role) {
		log.info(
				"--addRole(start)-->role:"
						+ role);
		StringBuffer sql = new StringBuffer();

		sql
				.append("INSERT INTO ROLE_INFO(ROLE_ID,ROLE_NAME,ROLE_STATE,ROLE_DES)VALUES(?,?,?,?)");
		log.debug(
				"--addRole-->sql:"
						+ sql.toString());
		List list=new ArrayList();
		list.add(incrementer.nextStringValue());
		list.add(role.getRole_name());
		list.add(role.getRole_state());
		list.add(role.getRole_des());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->创建新的角色失败->" + e.toString());
			throw new DBRuntimeException("RoleManageDAOIMP===>addRole:"
					+ e.toString());
		}
		log.info( "--addRole(over)");
		return true;
	}

	/**
	 * 修改角色信息
	 * 
	 * @param role
	 *            角色对象
	 * @return 是否成功
	 */
	public boolean modifyRole(RoleInfoBean role) {
		log.info(
				"--modifyRole(start)-->role:"
						+ role);
		StringBuffer sql = new StringBuffer();

		sql.append("UPDATE ROLE_INFO SET ROLE_NAME =?,ROLE_DES = ? WHERE ROLE_ID = ?");
		List list=new ArrayList();
		list.add(role.getRole_name());
		list.add(role.getRole_des());
		list.add(role.getRole_id());
		log.debug(
				"--modifyRole-->sql:"
						+ sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->修改角色信息失败->" + e.toString(),e);
			throw new DBRuntimeException("RoleManageDAOIMP===>modifyRole:"
					+ e.toString());
		}
		log.info( "--modifyRole(over)");
		return true;
	}

	/**
	 * 启用、禁用角色
	 * 
	 * @param roleIDs
	 *            角色ID集合
	 * @param roleState
	 *            角色状态
	 * @return 是否成功
	 */
	public boolean modifyRoleState(String roleIDs, String roleState) {
		log.info(
				"--modifyRoleState(start)-->roleIDs:"
						+ roleIDs + ";roleState:" + roleState);
		StringBuffer role_id_string = new StringBuffer();
		String[] role_id = roleIDs.split(",");
		String[] params=new String[role_id.length+1];
		params[0]=roleState;
		for (int i = 0; i < role_id.length; i++) {
			role_id_string.append("?,");
			if(i>0){
				params[i]=role_id[i-1];
			}
		}
		params[role_id.length]=role_id[role_id.length-1];
		
		if(role_id_string.length()>0){
			role_id_string.deleteCharAt(role_id_string.length()-1);
		}
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE ROLE_INFO SET ").append("ROLE_STATE =? where ROLE_ID IN (" + role_id_string.toString() + ")");
		log.debug(
				"--modifyRoleState-->sql:"
						+ sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(),params);
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->修改角色状态失败->" + e.toString(),e);
			throw new DBRuntimeException("RoleManageDAOIMP===>modifyRoleState:"
					+ e.toString());
		}
		log.info(
				"--modifyRoleState(over)");
		return true;
	}

	/**
	 * 获取角色已有的权限
	 * 
	 * @param roleIDs
	 * @return
	 */
	public List<NodeBean> getExistsComponents(String roleIDs) {
		log.info(
				"--getExistsComponents(start)-->roleIDs:"
						+ roleIDs);
		List<NodeBean> permissionInfos = null;
		StringBuffer role_id_string = new StringBuffer();
		String[] role_id = roleIDs.split(",");
		for (int i = 0; i < role_id.length; i++) {
			role_id_string.append("?,");
		}
		if(role_id_string.length()>0){
			role_id_string.deleteCharAt(role_id_string.length()-1);
		}
		StringBuffer sql = new StringBuffer();
		sql
				.append(
						"SELECT T.PERMISSION_CODE,T.PERMISSION_NAME,T.PERMISSION_TYPE FROM SYS_PERMISSION T WHERE T.PERMISSION_CODE IN (SELECT T1.PERMISSION_CODE FROM ROLE_PERMISSION_MAP T1 WHERE T1.ROLE_ID IN (").append(role_id_string).append("))");
		log.debug(
				"--getExistsComponents-->sql:"
						+ sql.toString());
		try {
			permissionInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					PermissionInfoBean.class,role_id);
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->获取角色已有的权限失败->" + e.toString(),e);
			throw new DBRuntimeException(
					"RoleManageDAOIMP===>getExistsComponents:" + e.toString());
		}
		log.info(
				"--getExistsComponents(over)-->permissionInfos:"
						+ permissionInfos);
		return permissionInfos;
	}

	/**
	 * 获取角色没有有的权限
	 * 
	 * @param roleIDs
	 * @return
	 */
	public List<NodeBean> getNotExistsComponents(String roleIDs) {
		log.info(
				"--getNotExistsComponents(start)-->roleIDs:"
						+ roleIDs);
		List<NodeBean> permissionInfos = null;
		StringBuffer role_id_string = new StringBuffer();
		String[] role_id = roleIDs.split(",");
		String[] params=new String[role_id.length*2];
		for (int i = 0; i < role_id.length; i++) {
			role_id_string.append("?,");
			params[i]=role_id[i];
		}
		for (int i = 0; i < role_id.length; i++) {
			params[i+role_id.length]=role_id[i];
		}
		if(role_id_string.length()>0){
			role_id_string.deleteCharAt(role_id_string.length()-1);
		}
		StringBuffer sql = new StringBuffer();
		
		sql.append("SELECT T.PERMISSION_CODE,T.PERMISSION_NAME,T.PERMISSION_TYPE FROM SYS_PERMISSION T WHERE T.PERMISSION_CODE NOT IN (SELECT T1.PERMISSION_CODE FROM ROLE_PERMISSION_MAP T1 WHERE T1.ROLE_ID IN (").append(role_id_string).append("))");
		sql.append(" UNION ");
		sql.append("SELECT T.PERMISSION_CODE,T.PERMISSION_NAME,T.PERMISSION_TYPE FROM SYS_PERMISSION T WHERE T.PERMISSION_CODE IN(SELECT DISTINCT  T1.PERMISSION_TYPE FROM SYS_PERMISSION T1 WHERE T1.PERMISSION_CODE NOT IN (SELECT T2.PERMISSION_CODE FROM ROLE_PERMISSION_MAP T2 WHERE T2.ROLE_ID IN(").append(role_id_string).append("))").append(" AND  T1.PERMISSION_TYPE IS NOT NULL)");
		log.debug(
				"--getNotExistsComponents-->sql:"
						+ sql.toString()+params);
		try {
			permissionInfos = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					PermissionInfoBean.class,params);
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->获取角色没有有的权限失败->" + e.toString(),e);
			throw new DBRuntimeException(
					"RoleManageDAOIMP===>getNotExistsComponents:"
							+ e.toString());
		}
		log.info(
				"--getNotExistsComponents(over)-->permissionInfos:"
						+ permissionInfos);
		return permissionInfos;
	}

	/**
	 * 修改角色的权限
	 * 
	 * @param roleIDs
	 * @param componentIDs
	 */
	public boolean updateRoleComponents(String roleIDs, String componentIDs) {
		log.info(
				"--updateRoleComponents(start)-->roleIDs:"
						+ roleIDs + ";componentIDs:" + componentIDs);
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM ROLE_PERMISSION_MAP WHERE ROLE_ID IN(");
		sql.append(roleIDs).append(")");
		log.debug(
				"--updateRoleComponents-->sql:"
						+ sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString());
			if ("".equals(componentIDs.trim()))
				return true;
			sql = new StringBuffer("");
			String[] componentID = componentIDs.split(",");
			for (int i = 0; i < componentID.length; i++) {
				String[] role_id = roleIDs.split(",");
				for (int j = 0; j < role_id.length; j++) {
					sql
							.append(
									"INSERT INTO ROLE_PERMISSION_MAP(ROLE_ID,PERMISSION_CODE)VALUES(")
							.append(role_id[j]).append(",'").append(
									componentID[i]).append("')");
					log.debug(
							"--updateRoleComponents-->sql:"
									+ sql.toString());
					DataBaseUtil.SUNECM.update(sql.toString());
					sql = new StringBuffer("");
				}
			}
		} catch (Exception e) {
			log.error( "角色管理->修改角色的权限失败->" + e.toString(),e);
			throw new DBRuntimeException(
					"RoleManageDAOIMP===>updateRoleComponents:" + e.toString());
		}
		log.info( "--updateRoleComponents(over)");
		return true;
	}

	/**
	 * 获取角色对内容对象操作权限
	 * 
	 * @param role_ids
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<RoleCmodelRelBean> getConferList(String role_id, int start,
			int limit) {
		log.info(
				"--getConferList(start)-->role_id:"
						+ role_id + ";start:" + start + ";limit:" + limit);
		List<RoleCmodelRelBean> beanList = null;

		StringBuffer sql = new StringBuffer();

		sql
				.append(
						"SELECT C.MODEL_CODE,C.MODEL_NAME,U.PERMISSION_CODE,U.ROLE_ID FROM CONTENT_MODEL_SET C LEFT OUTER JOIN ROLE_MODEL_REL U ON(U.MODEL_CODE=C.MODEL_CODE) AND U.ROLE_ID =? WHERE C.MODEL_TYPE = '0' ");
		List list=new ArrayList();
		list.add(role_id);
		log.debug(
				"--getConferList-->sql:"
						+ sql.toString());
		try {
			beanList = DataBaseUtil.SUNECM.queryBeanList(pageTool.getPageSql(
					sql.toString(), start, limit), RoleCmodelRelBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->获取角色对内容对象操作权限失败->" + e.toString(),e);
			throw new DBRuntimeException("RoleManageDAOIMP===>getConferList:"
					+ e.toString());
		}
		log.info( "--getConferList-->beanList:"+beanList);
		return beanList;
	}

	/**
	 * 获取角色对内容对象操作权限(取全部记录)
	 * 
	 * @param role_ids
	 * @return
	 */
	public List<RoleCmodelRelBean> getConferAllList(String role_id) {
		List<RoleCmodelRelBean> beanList = null;
		log.info(
				"--getConferAllList(start)-->role_id:"
						+ role_id);
		StringBuffer sql = new StringBuffer();

		sql
				.append(
						"SELECT C.MODEL_CODE,C.MODEL_NAME,U.PERMISSION_CODE,U.ROLE_ID FROM CONTENT_MODEL_SET C LEFT OUTER JOIN ROLE_MODEL_REL U ON(U.MODEL_CODE=C.MODEL_CODE) AND U.ROLE_ID = ? WHERE C.MODEL_TYPE = '0' ");
		List list=new ArrayList();
		list.add(role_id);
		try {
			log.debug(
					"--getConferAllList-->sql:"
							+ sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					RoleCmodelRelBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error( "角色管理->获取角色对内容对象操作权限(取全部记录)失败->"
					+ e.toString());
			throw new DBRuntimeException(
					"RoleManageDAOIMP===>getConferAllList:" + e.toString());
		}
		log.info(
				"--getConferAllList(over)-->beanList:"+beanList);
		return beanList;
	}

	/**
	 * 配置内容对象操作权限
	 * 
	 * @param role_ids
	 * @param model_codes
	 * @param permission_code
	 * @return
	 */
	public boolean configConfer(String role_id, String model_codes,
			String permission_code) {
		log.info(
				"--configConfer(start)-->role_id:"
						+ role_id + ";model_codes:" + model_codes
						+ ";permission_code:" + permission_code);
		StringBuffer sql = new StringBuffer();

		StringBuffer model_code_string = new StringBuffer();
		String[] models = model_codes.trim().split(",");

		for (int i = 0; i < models.length; i++) {
			if (i > 0)
				model_code_string.append(",");
			model_code_string.append("'").append(models[i]).append("'");
		}
		Collection<String> sqls = new ArrayList<String>();
		sql.append("DELETE FROM ROLE_MODEL_REL WHERE ROLE_ID = ").append(
				role_id);
		sql.append(" AND MODEL_CODE IN (").append(model_code_string)
				.append(")");
		log.debug(
				"--configConfer-->sql:"
						+ sql.toString());
		sqls.add(sql.toString());
		sql = new StringBuffer();

		for (int j = 0; j < models.length; j++) {
			sql = new StringBuffer();
			sql
					.append(
							"INSERT INTO ROLE_MODEL_REL (ROLE_ID,MODEL_CODE,PERMISSION_CODE)VALUES(")
					.append(role_id).append(",'").append(models[j]).append(
							"','").append(permission_code).append("')");
			sqls.add(sql.toString());
			log.debug(
					"--configConfer-->sql:"
							+ sql.toString());
		}
		try {
			DataBaseUtil.SUNECM.exceTrans(sqls);
		} catch (Exception e) {
			log.error( "角色管理->配置内容对象操作权限失败->" + e.toString(),e);
			throw new DBRuntimeException("RoleManageDAOIMP===>configConfer:"
					+ e.toString());
		}
		log.info("--configConfer(over)-->配置成功!");
		return true;
	}

	public int checkRoleName(String roleName) {
		log.info(
				"--checkRoleName(start)-->roleName:"
						+ roleName);
		if (roleName == null || roleName.equals("")) {
			return 0;
		}
		int count = 0;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT count(*) FROM ROLE_INFO where ROLE_NAME =? ");
		List list=new ArrayList();
		list.add(roleName);
		try {
			log.debug(
					"--checkRoleName-->sql:"
							+ sql.toString());
			count = DataBaseUtil.SUNECM.queryInt(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "角色管理->校验角色名是否存在失败:" + e.toString(),e);
			throw new DBRuntimeException("TokenManageDAOIMP===>checkBindIp:"
					+ e.getMessage());
		}
		log.info( "--checkRoleName(over)-->count:"+count);
		return count;
	}

}
