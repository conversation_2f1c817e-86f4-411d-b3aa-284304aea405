package com.sunyard.ecm.server.bean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public  class GroupSequence {
	public String groupName;
	public GroupSequence superSeq;// 父级别的Group
	public List<GroupSequence> childSeqs = new ArrayList<GroupSequence>();// 子级别的Group列表
	private final static  Logger log = LoggerFactory.getLogger(GroupSequence.class);

	public GroupSequence(String groupName, GroupSequence superSeq) {
		this.groupName = groupName;
		this.superSeq = superSeq;
		if (this.superSeq != null) {
			this.superSeq.childSeqs.add(this);
		}
	}


//	public static void main(String[] args) {
//		List<String> list = new ArrayList<String>();
//		list.add("local,zh");
//		list.add("fff,zh");
//		list.add("fffff,zh");
//		list.add("zh,");
////
//		List<GroupSequence> slist = configGroupSequence("",list);
//		for (GroupSequence gs : slist) {
//			System.out.println(gs.groupName + " > " + (gs.superSeq != null ? gs.superSeq.groupName : "NULL"));
//		}
//	}

	public static List<GroupSequence> configGroupSequence(String modelCode,List<String> list) {
		// List<String> list = new ArrayList<String>();
		// list.add("local,zh");
		// list.add("fff,zh");
		// list.add("fffff,zh");
		// list.add("zh,");
		List<GroupSequence> slist = new ArrayList<GroupSequence>();
		if(list==null||list.size()==0){
			log.warn("list is null");
			return  slist;
		}
		log.info("configGroupSequence");
		for (String str : list) {
			GroupSequence gs = findByGroup(slist, str.split(",")[0]);
			if (gs == null) {
				GroupSequence parentGs = findByGroup(slist, str.split(",")[1]);
				if (parentGs != null) {
					gs = new GroupSequence(str.split(",")[0], parentGs);
					slist.add(gs);
				} else {
					parentGs = new GroupSequence(str.split(",")[1], null);
					gs = new GroupSequence(str.split(",")[0], parentGs);
					slist.add(gs);
					slist.add(parentGs);
				}
			}
		}

		for (GroupSequence gs : slist) {
			log.info("begin reset,modelCode:"+modelCode+",gname:"+gs.groupName + " > " + (gs.superSeq != null ? gs.superSeq.groupName : "NULL"));
		}
		return slist;
	}

	
	private static GroupSequence findByGroup(List<GroupSequence> slist, String s) {
		for (GroupSequence gs : slist) {
			if (gs.groupName.equals(s)) {
				return gs;
			}
		}

		return null;
	}
}