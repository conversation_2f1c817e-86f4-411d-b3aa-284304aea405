package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * <p>
 * Title:xstream 内容存储服务器信息信息
 * </p>
 * <p>
 * Description:
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@XStreamAlias("NodeInfo")
public class NodeInfo {
	@XStreamAsAttribute
	private String SERVER_ID;// 节点ID SERVER_ID
	@XStreamAsAttribute
	private String SERVER_NAME; // 节点名称 SERVER_NAME
	@XStreamAsAttribute
	private String SERVER_IP; // 节点IP地址 SERVER_IP
	@XStreamAsAttribute
	private String HTTP_PORT; // 节点http端口号 HTTP_PORT
	@XStreamAsAttribute
	private String SOCKET_PORT; // 节点socket端口号SOCKET_PORT
	@XStreamAsAttribute
	private String STATE; // 服务器状态STATE
	@XStreamAsAttribute
	private String IS_DBCONN;// 数据库连接ISDB_CONN
	@XStreamAsAttribute
	private String GROUP_ID; // 所属服务器组GROUP_ID
	@XStreamAsAttribute
	private String WEIGHT;// 权重WEIGHT
	@XStreamAsAttribute
	private String REMARK;// 备注REMARK
	@XStreamAsAttribute
	private String GROUP_NAME;// 所属服务器组名字
	@XStreamAsAttribute
	private String GROUP_IP;// 所属服务器组ip
	@XStreamAsAttribute
	private String GROUP_HTTP_PORT;// 所属服务器组http端口GHPORT
	@XStreamAsAttribute
	private String GROUP_SOCKETPORT;// 所属服务器组socket端口
	@XStreamAsAttribute
	private String GROUP_STATE;// 所属服务器组状态
	@XStreamAsAttribute
	private String GROUP_OS; // 所属服务器组操作系统
	@XStreamAsAttribute
	private String GROUP_REMARK;// 所属服务器组备注
	@XStreamAsAttribute
	private String DEPLOY_MODE;// 所属服务器组集群方式DEPLOY_MODE
	@XStreamAsAttribute
	private String IS_ECM_DB;// 该节点是否是连接单独数据库
	@XStreamAsAttribute
	private String HTTPS_PORT;
	@XStreamAsAttribute
	private String GROUP_HTTPS_PORT;
	@XStreamAsAttribute
	private String TRANS_PROTOCOL;
	@XStreamAsAttribute
	private String GTRANS_PROTOCOL;

	public String getGTRANS_PROTOCOL() {
		return GTRANS_PROTOCOL;
	}

	public void setGTRANS_PROTOCOL(String gTRANS_PROTOCOL) {
		GTRANS_PROTOCOL = gTRANS_PROTOCOL;
	}

	public String getTRANS_PROTOCOL() {
		return TRANS_PROTOCOL;
	}

	public void setTRANS_PROTOCOL(String tRANS_PROTOCOL) {
		TRANS_PROTOCOL = tRANS_PROTOCOL;
	}

	public String getHTTPS_PORT() {
		return HTTPS_PORT;
	}

	public void setHTTPS_PORT(String hTTPS_PORT) {
		HTTPS_PORT = hTTPS_PORT;
	}

	public String getGROUP_HTTPS_PORT() {
		return GROUP_HTTPS_PORT;
	}

	public void setGROUP_HTTPS_PORT(String gROUP_HTTPS_PORT) {
		GROUP_HTTPS_PORT = gROUP_HTTPS_PORT;
	}

	public String getServer_id() {
		return SERVER_ID;
	}

	public void setServer_id(String serverId) {
		SERVER_ID = serverId;
	}

	public String getServer_name() {
		return SERVER_NAME;
	}

	public void setServer_name(String serverName) {
		SERVER_NAME = serverName;
	}

	public String getServer_ip() {
		return SERVER_IP;
	}

	public void setServer_ip(String serverIp) {
		SERVER_IP = serverIp;
	}

	public String getHttp_port() {
		return HTTP_PORT;
	}

	public void setHttp_port(String httpPort) {
		HTTP_PORT = httpPort;
	}

	public String getSocket_port() {
		return SOCKET_PORT;
	}

	public void setSocket_port(String socketPort) {
		SOCKET_PORT = socketPort;
	}

	public String getState() {
		return STATE;
	}

	public void setState(String state) {
		this.STATE = state;
	}

	public String getIs_dbconn() {
		return IS_DBCONN;
	}

	public void setIs_dbconn(String isDbconn) {
		IS_DBCONN = isDbconn;
	}

	public String getGroup_id() {
		if ("-1".equals(GROUP_ID)) {
			return null;
		}
		return GROUP_ID;
	}

	public void setGroup_id(String groupId) {
		GROUP_ID = groupId;
	}

	public String getWetght() {
		return WEIGHT;
	}

	public void setWetght(String wetght) {
		this.WEIGHT = wetght;
	}

	public String getRemark() {
		return REMARK;
	}

	public void setRemark(String remark) {
		this.REMARK = remark;
	}

	public String getGroup_name() {
		return GROUP_NAME;
	}

	public void setGroup_name(String groupName) {
		GROUP_NAME = groupName;
	}

	public String getGroup_ip() {
		return GROUP_IP;
	}

	public void setGroup_ip(String groupIp) {
		GROUP_IP = groupIp;
	}

	public String getGroup_http_port() {
		return GROUP_HTTP_PORT;
	}

	public void setGroup_http_port(String groupHttpPort) {
		GROUP_HTTP_PORT = groupHttpPort;
	}

	public String getGroup_socketport() {
		return GROUP_SOCKETPORT;
	}

	public void setGroup_socketport(String groupSocketport) {
		GROUP_SOCKETPORT = groupSocketport;
	}

	public String getGroup_state() {
		return GROUP_STATE;
	}

	public void setGroup_state(String groupState) {
		GROUP_STATE = groupState;
	}

	public String getGroup_os() {
		return GROUP_OS;
	}

	public void setGroup_os(String groupOs) {
		GROUP_OS = groupOs;
	}

	public String getGroup_remark() {
		return GROUP_REMARK;
	}

	public void setGroup_remark(String groupRemark) {
		GROUP_REMARK = groupRemark;
	}

	public String getDeploy_mode() {
		return DEPLOY_MODE;
	}

	public void setDeploy_mode(String deployMode) {
		DEPLOY_MODE = deployMode;
	}

	public boolean isUA() {
		return "-1".equals(GROUP_ID);
	}

	public String getIS_ECM_DB() {
		return IS_ECM_DB;
	}

	public void setIS_ECM_DB(String iSECMDB) {
		IS_ECM_DB = iSECMDB;
	}

	@Override
	public String toString() {
		return "NodeInfo [DEPLOY_MODE=" + DEPLOY_MODE + ", GROUP_HTTP_PORT="
				+ GROUP_HTTP_PORT + ", GROUP_ID=" + GROUP_ID + ", GROUP_IP="
				+ GROUP_IP + ", GROUP_NAME=" + GROUP_NAME + ", GROUP_OS="
				+ GROUP_OS + ", GROUP_REMARK=" + GROUP_REMARK
				+ ", GROUP_SOCKETPORT=" + GROUP_SOCKETPORT + ", GROUP_STATE="
				+ GROUP_STATE + ", HTTP_PORT=" + HTTP_PORT + ", IS_DBCONN="
				+ IS_DBCONN + ", REMARK=" + REMARK + ", SERVER_ID=" + SERVER_ID
				+ ", SERVER_IP=" + SERVER_IP + ", SERVER_NAME=" + SERVER_NAME
				+ ", SOCKET_PORT=" + SOCKET_PORT + ", STATE=" + STATE
				+ ", WEIGHT=" + WEIGHT + ", IS_ECM_DB=" +IS_ECM_DB+"]";
	}

}