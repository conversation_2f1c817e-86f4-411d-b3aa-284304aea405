package com.sunyard.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map.Entry;
import java.util.Properties;

/**
 * 用于读取配置文件
 * 
 * <AUTHOR>
 *
 */
public class LoadProperties {

	private static Properties pro;
	private static InputStream in;
	private final static Logger log = LoggerFactory.getLogger(LoadProperties.class);

	public static List<String> getKeyWords() {
		in = null;
		pro = new Properties();
		try {
			ClassLoader classloader = LoadProperties.class.getClassLoader();
			if (classloader == null) {
				log.error("classloader is null");
				throw new IOException("classloader is null");
			}
			in = classloader.getResourceAsStream("pram.properties");
			pro.load(in);
			List<String> values = new ArrayList<String>();
			for (Entry<Object, Object> entry : pro.entrySet()) {
				if (entry.getValue() != null) {
					values.add(entry.getValue().toString());
				}
			}
			return values;
		} catch (IOException e) {
			log.error("loading pram.properties error", e);
			return null;
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
					log.error("IOException e:", e);
				}
				in = null;
			}
		}
	}

}
