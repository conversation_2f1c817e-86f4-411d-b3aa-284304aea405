<?xml version="1.0" encoding="UTF-8" ?>
<!-- debug 为false时 不打印日志级别更改信息 -->
<configuration debug="false">
	<springProperty name="LOG_DIR" source="logback.file" scope="context" defaultValue="/aos/logback" />
	<springProperty name="LOG_LEVEL" scope="context" source="logback.level" defaultValue="INFO"/>
	<springProperty name="LOG_MAXFILESIZE" scope="context" source="logback.filesize" defaultValue="100MB"/>
	<springProperty name="LOG_FILEMAXDAY" scope="context" source="logback.filemaxday" defaultValue="7"/>
	<springProperty name="LOG_CHARSET" scope="context" source="logback.charset" defaultValue="utf8"/>


	<!-- 控制台日志输出格式 -->
	<property name="CONSOLE_LOG_PATTERN" value="%red(%d{yyyy-MM-dd HH:mm:ss}) %yellow([%X{userid}]) %green([%thread]) %highlight(%-5level) %logger{36} - %msg%n"/>
	<!-- 日志文件日志输出格式 -->
	<property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{userid}] [%X{traceId}/%X{spanId}] [%thread] %-5level %logger{36} - %msg%n"/>

	<!-- 控制台输出日志 -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>${CONSOLE_LOG_PATTERN}</pattern>
			<charset>${LOG_CHARSET}</charset>
		</encoder>
	</appender>

	<!-- 异步输出 -->
	<appender name ="ASYNC" class= "ch.qos.logback.classic.AsyncAppender">
		<!-- 不丢失日志.默认的,如果队列的80%已满,则会丢弃TRACT、DEBUG、INFO级别的日志 -->
		<discardingThreshold>0</discardingThreshold>
		<!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
		<queueSize>512</queueSize>
		<!-- 添加附加的appender,最多只能添加一个 -->
		<appender-ref ref ="STDOUT"/>
		<includeCallerData>true</includeCallerData>
	</appender>

	<!-- 按照登录用户的userId（机构号、用户号）分文件打印日志 -->
	  <appender name="SIFT" class="ch.qos.logback.classic.sift.SiftingAppender">
		<discriminator>
			<key>userid</key>
			<defaultValue>systemDefault</defaultValue>
		</discriminator>
		<sift>
			<appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
				<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
					<fileNamePattern>${LOG_DIR}/RunLogs/${userid}/%d{yyyyMMdd}_%i.log</fileNamePattern>
					<!-- each file should be at most 100MB, keep 60 days worth of history, but at most 20GB -->
					<maxFileSize>${LOG_MAXFILESIZE}</maxFileSize>
					<maxHistory>${LOG_FILEMAXDAY}</maxHistory>
					<totalSizeCap>5GB</totalSizeCap>
				</rollingPolicy>
				<!-- <append>false</append> -->
				<encoder>
					<pattern>${FILE_LOG_PATTERN}</pattern>
				</encoder>
			</appender>
		</sift>
	 </appender>

	<appender name="ecm" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_DIR}/ECM/log4jEcm.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<!--<MaxBackupIndex>2</MaxBackupIndex>-->
			<fileNamePattern>${LOG_DIR}/ECM/log4jEcm.%d{yyyy-MM-dd}-%i.log</fileNamePattern>
			<maxFileSize>${LOG_MAXFILESIZE}</maxFileSize>
			<!-- 日志在磁盘上保留天数 -->
			<maxHistory>${LOG_FILEMAXDAY}</maxHistory>
			<totalSizeCap>5GB</totalSizeCap>
		</rollingPolicy>
		<!-- <append>false</append> -->
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{yyyy-MM-dd HH:mm:ss} [%p]-[%c-%L] %m%n</pattern>
			<charset>${LOG_CHARSET}</charset>
		</encoder>
	</appender>
	<appender name="sunds" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>${LOG_DIR}/SunDS/logSunDS.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/SunDS/logSunDS.%d{yyyyMMdd}_%i.log</fileNamePattern>
			<!-- each file should be at most 100MB, keep 60 days worth of history, but at most 20GB -->
			<maxFileSize>${LOG_MAXFILESIZE}</maxFileSize>
			<maxHistory>${LOG_FILEMAXDAY}</maxHistory>
			<totalSizeCap>5GB</totalSizeCap>
		</rollingPolicy>
		<!-- <append>false</append> -->
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${FILE_LOG_PATTERN}</pattern>
			<charset>${LOG_CHARSET}</charset>
		</encoder>
	</appender>
	<appender name="rootlog" class="ch.qos.logback.classic.sift.SiftingAppender">
		<discriminator>
			<key>requestNo</key>
			<defaultValue>systemDefault</defaultValue>
		</discriminator>
		<sift>
			<appender name="rootlog_2" class="ch.qos.logback.core.rolling.RollingFileAppender">
				<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
					<fileNamePattern>${LOG_DIR}/logAll.%d{yyyyMMdd}_%i.log</fileNamePattern>
					<!-- each file should be at most 100MB, keep 60 days worth of history, but at most 20GB -->
					<maxFileSize>${LOG_MAXFILESIZE}</maxFileSize>
					<maxHistory>${LOG_FILEMAXDAY}</maxHistory>
					<totalSizeCap>5GB</totalSizeCap>
				</rollingPolicy>
				<!-- <append>false</append> -->
				<encoder>
					<pattern>${FILE_LOG_PATTERN} </pattern>
				</encoder>
			</appender>
		</sift>
	</appender>
	<appender name="ecm" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${LOG_DIR}/logEcm.%d{yyyyMMdd}_%i.log</fileNamePattern>
			<!-- each file should be at most 100MB, keep 60 days worth of history, but at most 20GB -->
			<maxFileSize>${LOG_MAXFILESIZE}</maxFileSize>
			<maxHistory>${LOG_FILEMAXDAY}</maxHistory>
			<totalSizeCap>5GB</totalSizeCap>
		</rollingPolicy>
		<!-- <append>false</append> -->
		<encoder>
			<pattern>${FILE_LOG_PATTERN}</pattern>
		</encoder>
	</appender>

	<appender name="logSay" class="ch.qos.logback.core.ConsoleAppender">
		<Target>System.out</Target>
		<encoder>
			<pattern>${CONSOLE_LOG_PATTERN}</pattern>
		</encoder>
	</appender>

	<!-- 添加以下配置来打印MyBatis的SQL语句 -->
	<logger name="com.sunyard.aos.unify.mapper" level="DEBUG"/>
	<logger name="com.sunyard.aos.unify.dao" level="DEBUG"/>
	<logger name="com.sunyard.ars" level="DEBUG"/>

	<logger name="java.sql.Connection" level="ERROR" />
	<logger name="java.sql.Statement" level="ERROR" />
	<logger name="java.sql.PreparedStatement" level="ERROR" />
	<logger name="java.sql.ResultSet" level="ERROR" />
	<logger name="org.quartz.core.QuartzSchedulerThread" level="ERROR" />
	<logger name="com.mchange.v2" level="ERROR" />
	<logger name="org.apache.zookeeper" level="ERROR" />
	<logger name="kafka.producer" level="ERROR" />
	<logger name="kafka.consumer" level="ERROR" />
	<logger name="org.apache.thrift" level="ERROR" />
	<!--屏蔽这个日志登录耗时太多-->
	<logger name="com.sunyard.aos.unify.dao.IFFieldMapper" level="OFF" />
    <logger name="com.sunyard.aos.unify.dao.MenuTreeMapper" level="OFF" />
    <logger name="com.sunyard.aos.unify.controller.ExternalDataController" level="OFF" />
    <logger name="com.sunyard.cop.IF.controller.BaseController" level="${LOG_LEVEL}" />
	<logger name="com.sunyard.util" level="debug">
		<appender-ref ref="rootlog"/>
	</logger>
	<logger name="com.sunyard.client" level="debug">
		<appender-ref ref="rootlog"/>
	</logger>
	<logger name="com.sunyard.cop" level="debug">
		<appender-ref ref="sunds"/>
	</logger>
	<logger name="org.quartz.core.QuartzSchedulerThread" level="ERROR"/>
	<logger name="com.sunyard.ecm" level="debug">
		<appender-ref ref="ecm"/>
	</logger>
	<logger name="com.mchange.v2" level="ERROR"/>
	<logger name="org.springframework" level="ERROR"/>
	<logger name="org.apache" level="ERROR"/>
	<logger name="com.sunyard.exception" level="debug">
		<appender-ref ref="rootlog"/>
	</logger>
	<logger name="io.lettuce.core" level="ERROR"/>
	<logger name="com.alibaba.cloud.nacos" level="ERROR"/>
	<logger name="com.alibaba.nacos" level="ERROR"/>
	<logger name="org.mybatis.spring.mapper.ClassPathMapperScanner" level="ERROR"/>
	<logger name="org.mybatis.spring.SqlSessionFactoryBean" level="ERROR"/>
	<logger name="io.netty" level="ERROR"/>
	<logger name="org.redisson.cluster.ClusterConnectionManager" level="INFO" />
	<logger name="org.redisson.command.RedisExecutor" level="INFO" />
	<!--	<logger name="ch.qos.logback" level="OFF"/>-->

	<!-- 添加以下配置来打印MyBatis的SQL语句 -->
    <logger name="com.sunyard.ars" level="DEBUG"/>

	<logger name="com.sunyard.aos.unify.mapper" level="DEBUG"/>
	<logger name="com.sunyard.aos.unify.dao" level="DEBUG"/>

	<root level="${LOG_LEVEL}">
<!--		<appender-ref ref="STDOUT" />-->
		<appender-ref ref="ASYNC"/>
		<appender-ref ref="SIFT" />
		<appender-ref ref="rootlog"/>
	</root>
</configuration>
