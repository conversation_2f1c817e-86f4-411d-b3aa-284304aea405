/*
 * Translated default messages for bootstrap-select.
 * Locale: Z<PERSON> (Chinese)
 * Region: CN (China)
 */
(function ($) {
  $.fn.selectpicker.defaults = {
    noneSelectedText: '',
    noneResultsText: '没有找到匹配项',
    countSelectedText: '选中{1}中的{0}项',
    maxOptionsText: ['超出限制 (最多选择{n}项)', '组选择超出限制(最多选择{n}组)'],
    multipleSeparator: ', ',
    selectAllText: '全选',
    deselectAllText: '取消全选'
  };
})(jQuery);
