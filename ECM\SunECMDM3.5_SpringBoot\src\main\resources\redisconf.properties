#最大连接数
maxTotal=1024
#最大空闲连接数
maxIdle=1024
#获取连接时的最大等待毫秒数(如果设置为阻塞时BlockWhenExhausted),如果超时就抛异常, 小于零:阻塞不确定的时间
maxWaitMillis=1000
#在获取连接的时候检查有效性, 默认false
testOnBorrow=true
#在获取返回结果的时候检查有效性, 默认false
testOnReturn=true
#redis连接模式(normal（默认）、sentinel)
redisConnModel=normal
#redis普通模式连接地址
redisConnIpPort=127.0.0.1:6379
#redis哨兵地址和端口（多个用逗号隔开）
sentinelsIpPort=127.0.0.1:26379
#redis主节点名称
masterName=redisMst
#redis连接密码
password=123456
#消息队列大小(默认1000)
msgqueue=1000
#从消息队列取消息的线程大小(默认5个)
takeMsgThreadNum=20
