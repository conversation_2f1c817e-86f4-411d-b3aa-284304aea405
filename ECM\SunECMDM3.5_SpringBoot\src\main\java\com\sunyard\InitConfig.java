package com.sunyard;

import org.apache.cxf.transport.servlet.CXFServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

@Configuration
public class InitConfig {

    @Bean(name = "multipartResolver")
    public CommonsMultipartResolver getCommonsMultipartResolver() {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        multipartResolver.setMaxUploadSize(209715200);
        multipartResolver.setMaxInMemorySize(1048576);
        multipartResolver.setDefaultEncoding("utf-8");
        return multipartResolver;
    }

    @Bean
    public ServletRegistrationBean servletRegistration() {
        ServletRegistrationBean servletRegistrationBean = new ServletRegistrationBean(new CXFServlet(), "/webservices/*");
        servletRegistrationBean.setName("webService");
        return servletRegistrationBean;
    }

//    @Override
//    public void addViewControllers(ViewControllerRegistry registry) {
//
//        //默认地址（可以是页面或后台请求接口）
//        registry.addViewController("/").setViewName("forward:/index.jsp");
//        //设置过滤优先级最高
//        registry.setOrder(Ordered.HIGHEST_PRECEDENCE);
//    }
//
//    @Autowired
//    private Person person;

//    @Bean
//    public void getPersonName(){
//        String name = person.getName();
//        System.out.println("=================="+name);
//    }

//    @Autowired
//    private Bus bus;
//
//    @Bean
//    public SunEcmAccessImpl sunEcmAccessImpl(){
//        return new SunEcmAccessImpl();
//    }
//
//    @Bean
//    public Endpoint endpoint() {
//        EndpointImpl endpoint = new EndpointImpl(bus, sunEcmAccessImpl());
//        endpoint.publish("/WsInterface");
//        return endpoint;
//    }
}
