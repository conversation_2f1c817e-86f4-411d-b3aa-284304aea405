package com.sunyard.console.logmanage.action;


import java.net.URLDecoder;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONObject;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import com.sunyard.console.configmanager.wsserviceutil.WsBeanInterface;
import com.sunyard.console.contentservermanage.dao.ContentServerGroupManageDAO;
import com.sunyard.console.logmanage.bean.LogRuleBean;
import com.sunyard.console.logmanage.dao.LogRuleManageDAO;
import com.sunyard.console.monitor.statusManage.dao.StatusManageDao;
import com.sunyard.console.threadpoool.IssueUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>Title: 日志策略管理Action</p>
 * <p>Description: 处理日志策略信息管理中的跳转</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class LogRuleManageAction extends BaseAction {
	/**
	 *
	 */
	private static final long serialVersionUID = 1L;
	@Autowired
	LogRuleManageDAO lrdao;
	@Autowired
	ContentServerGroupManageDAO contentServerGroupManageDao;
	@Autowired
	StatusManageDao smdao;
	@Autowired
	WsBeanInterface wsclient;
	private String log_id;		//日志策略ID
	private String class_path;	//包路径
	private String level;		//日志级别
	private String save_path;	//保存路径
	private String log_size;	//日志大小
	private int start;
	private int limit;
	private String optionFlag;	//操作标识
	private String c_calss_path;
	private String value;		//远程校验字段值
	private final static  Logger log = LoggerFactory.getLogger(LogRuleManageAction.class);

	public String getValue() {
		return value;
	}
	public void setValue(String value) {
		this.value = value;
	}
	public StatusManageDao getSmdao() {
		return smdao;
	}
	public void setSmdao(StatusManageDao smdao) {
		this.smdao = smdao;
	}
	public ContentServerGroupManageDAO getCsgmdao() {
		return contentServerGroupManageDao;
	}
	public void setCsgmdao(ContentServerGroupManageDAO contentServerGroupManageDao) {
		this.contentServerGroupManageDao = contentServerGroupManageDao;
	}
	public WsBeanInterface getWsclient() {
		return wsclient;
	}
	public void setWsclient(WsBeanInterface wsclient) {
		this.wsclient = wsclient;
	}

	public String getLog_id() {
		return log_id;
	}
	public void setLog_id(String log_id) {
		this.log_id = log_id;
	}
	public String getC_calss_path() {
		return c_calss_path;
	}
	public void setC_calss_path(String c_calss_path) {
		this.c_calss_path = c_calss_path;
	}
	public LogRuleManageDAO getLrdao() {
		return lrdao;
	}
	public void setLrdao(LogRuleManageDAO lrdao) {
		this.lrdao = lrdao;
	}
	public String getClass_path() {
		return class_path;
	}
	public void setClass_path(String class_path) {
		this.class_path = class_path;
	}
	public String getLevel() {
		return level;
	}
	public void setLevel(String level) {
		this.level = level;
	}
	public String getSave_path() {
		return save_path;
	}
	public void setSave_path(String save_path) {
		this.save_path = save_path;
	}
	public String getLog_size() {
		return log_size;
	}
	public void setLog_size(String log_size) {
		this.log_size = log_size;
	}
	public int getStart() {
		return start;
	}
	public void setStart(int start) {
		this.start = start;
	}
	public int getLimit() {
		return limit;
	}
	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getOptionFlag() {
		return optionFlag;
	}
	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	/**
	 * 查询日志策略列表
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/logManage/getLogRuleListAction.action")
	public String getLogRuleList(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page = modelJson.getInt("page");
		int limit = modelJson.getInt("limit");
		log.info("--getLogRuleList(start)-->page:" + page + ";limit:" + limit);
		String jsonStr = null;
		int start = (page - 1) * limit;

		try{
			List<LogRuleBean> logRuleList = lrdao.searchLogRuleList(start+1, limit);
			log.debug( "--getLogRuleList-->logRuleList:"+logRuleList);
			List<LogRuleBean> logRuleAllList = lrdao.searchAllLogRuleList();
			log.debug( "--getLogRuleList-->logRuleAllList:"+logRuleAllList);
			jsonStr = new JSONUtil().createJsonDataByColl(logRuleList,logRuleAllList.size(),new LogRuleBean());
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取日志策略信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "日志策略管理->日志策略查询失败"+e.toString(),e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getLogRuleList(over)");
		return null;
	}

	/**
	 * 增加、修改日志策略
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/logManage/configLogRuleAction.action", method = RequestMethod.POST)
	public String configLogRule(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		//String log_id = modelJson.getString("log_id");
		String optionFlag = modelJson.getString("optionFlag");
		String class_path = modelJson.getString("c_calss_path");
		String level = modelJson.getString("level");
		String log_size = modelJson.getString("log_size");
		String save_path = modelJson.getString("save_path");
		String c_calss_path = modelJson.getString("c_calss_path");
		log.info("--configLogRule(start)");
		LogRuleBean logRule = new LogRuleBean();
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();

		logRule.setClass_path(class_path);
		logRule.setLevel(level);
		logRule.setLog_size(log_size);
		logRule.setSave_path(save_path);
		log.debug( "--configLogRule-->logRule:"+logRule);
		try{
			if(optionFlag.equals("create1")){
				log_id = lrdao.addLogRule(logRule);
				logRule.setId(log_id);
			}else if(optionFlag.equals("modify1")){
				String log_id = modelJson.getString("log_id");
				logRule.setClass_path(c_calss_path);
				logRule.setId(log_id);
				lrdao.modifyRule(logRule);
			}
			//创建日志策略线程
			//ConsoleThreadPool.getThreadPool().submit(new Thread( new SendLogRuleThread(logRule,optionFlag)));
			IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("message", "配置日志策略成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--configLogRule(over)-->配置日志策略成功！");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "配置日志策略失败!!");
			jsonStr = jsonResp.toString();
			log.error( "日志策略管理->配置日志策略失败->"+e.toString(),e);
		}
		this.outJsonString(jsonStr);
		log.info( "--configLogRule(over)");
		return null;
	}
	/**
	 * 校验路径名称是否存在
	 *
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/logManage/checkSavePathAction.action")
	public String checkSavePath(String value) {
		log.info("--checkSavePath(start)-->value:" + value);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		int count = 0;
		try {
			if(value!=null){
				value=value.toLowerCase();
			}
			count = lrdao.checkSavePath(value);
			log.debug( "--checkSavePath-->count:"+count);
		} catch (Exception e) {
			count = -1;
			// 记录日志
			log.error( "日志策略管理->校验该路径名称是否存在失败!" + e.toString(),e);
		}
		if (count == 0) {
			jsonResp.put("valid", true);
			jsonResp.put("reason", true);
		} else if (count > 0) {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "路径名称已经被使用!!");
		} else {
			jsonResp.put("valid", false);
			jsonResp.put("reason", "检验失败!!");
		}
		jsonResp.put("success", true);
		jsonStr = jsonResp.toString();
		this.outJsonString(jsonStr);
		log.info( "--checkSavePath(over)-->value:"+value);
		return null;

	}
}
