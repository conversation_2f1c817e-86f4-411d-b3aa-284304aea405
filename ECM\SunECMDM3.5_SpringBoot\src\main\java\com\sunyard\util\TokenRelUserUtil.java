package com.sunyard.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.sunyard.ecm.server.bean.BatchBean;
import com.sunyard.ecm.server.bean.HeightQuery;
import com.sunyard.ecm.server.bean.MigrateBatchBean;
import com.sunyard.ecm.server.dao.SunECMDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.common.ClientConfiguration;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.ws.utils.XMLUtil;

public class TokenRelUserUtil {
	private static final String DATE_FORMAT = "yyyyMMddHHmmssSSS";
	private static Logger log = LoggerFactory.getLogger(TokenRelUserUtil.class);
	/**
	 * 有效期
	 */
	private ThreadLocal<TokenInfoBean> threadBatchBean = new ThreadLocal<TokenInfoBean>();
	private static final String TOKEN = "TOKEN";
	private Map<String, TokenInfoBean> tokenMap = new ConcurrentHashMap<String, TokenInfoBean>();
	private SunECMDao sunECMDao;
	private ExecutorService clearPool = Executors.newSingleThreadExecutor();
	private Thread SCHEDULE_CLEAR_TOKEN_TASK;
	/**
	 * 清理任务执行间隔
	 */
	private final static long interval = 10 * 60 * 1000;
	public static final String insertSql = "insert into token_user_rel(token_value,user_info,expired_time)values(?,?,?)";
	public static final String params_sql = "select PARAM_VALUE from PARAM_TABLE WHERE PARAM_NAME=?";

	/**
	 * 获取过期时间
	 * 
	 * @return
	 */
	public String getExpireTime() {
		Object[] params = { "token_expire_time" };
		List<Map<String, String>> val = sunECMDao.searchSql(TokenRelUserUtil.params_sql, params);
		long token_expire_time = 1800000L;// 默认令牌有效期半个小时
		if (!val.isEmpty()) {
			token_expire_time = Long.parseLong(val.get(0).get("PARAM_VALUE")) * 1000;
		}
		long expire_time = System.currentTimeMillis() + token_expire_time;
		SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT);

		String date = sf.format(new Date(expire_time));
		return date;
	}

	/**
	 * 将字符串转成日期，然后返回日期的long值
	 * 
	 * @param date
	 * @return
	 */
	public long StringDate2Long(String date) {
		try {
			SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT);
			Date time = sf.parse(date);
			return time.getTime();
		} catch (ParseException e) {
			throw new RuntimeException(e);
		}
	}

	public BatchBean reqXmlInfo2BatchBean(String xml) {
		BatchBean batchBean = XMLUtil.xml2Bean(xml, BatchBean.class);
		if(!"true".equals(ClientConfiguration.get("openBreakPoint"))){
			batchBean.setBreakPoint(false);
			log.info("breakpoint is close");
		}
		TokenInfoBean tokenBean = removeThreadTokenBean();
		if (tokenBean == null)
			return batchBean;
		batchBean.setUser(tokenBean.getUser());
		batchBean.setPassWord(tokenBean.getPassword());
		return batchBean;
	}

	public MigrateBatchBean reqXmlInfo2MigrateBatchBean(String xml) {
		MigrateBatchBean migrateBean = MigrateBatchBean.toBean(xml);
		TokenInfoBean tokenBean = removeThreadTokenBean();
		if (tokenBean == null)
			return migrateBean;
		migrateBean.setUser(tokenBean.getUser());
		migrateBean.setPassWord(tokenBean.getPassword());
		return migrateBean;
	}

	public HeightQuery reqXml2HeightQueryBean(String xml) {
		HeightQuery heightQuery = XMLUtil.xml2Bean(xml, HeightQuery.class);

		TokenInfoBean tokenBean = removeThreadTokenBean();
		if (tokenBean == null)
			return heightQuery;
		heightQuery.setUserName(tokenBean.getUser());
		heightQuery.setPASSWORD(tokenBean.getPassword());
		return heightQuery;
	}

	public void setSunECMDao(SunECMDao sunECMDao) {
		this.sunECMDao = sunECMDao;
	}

	public void init() {
		SCHEDULE_CLEAR_TOKEN_TASK = new Thread(new Runnable() {
			public void run() {
				while (true) {
					TokenRelUserUtil.this.clear();
					try {
						Thread.sleep(interval);
					} catch (InterruptedException e) {
						break;
					}
				}
			}

		});
		SCHEDULE_CLEAR_TOKEN_TASK.setName("SCHEDULE_CLEAR_TOKEN_TASK");
		SCHEDULE_CLEAR_TOKEN_TASK.setDaemon(true);
		SCHEDULE_CLEAR_TOKEN_TASK.start();
	}

	public void destroy() {
		SCHEDULE_CLEAR_TOKEN_TASK.interrupt();
		TokenRelUserUtil.this.clearPool.shutdownNow();
	}

	public TokenRelUserUtil() {
		super();
	}

	private void clear() {
		Thread t = new Thread(new Runnable() {
			public void run() {
				log.info("begin clear expired token");
				Set<String> keys = new HashSet<String>();
				keys.addAll(TokenRelUserUtil.this.tokenMap.keySet());
				long currentTime = System.currentTimeMillis();
				Iterator<String> iterator = keys.iterator();
				while (iterator.hasNext()) {
					String token = iterator.next();
					if (currentTime >= TokenRelUserUtil.this.tokenMap.get(token).getExpiredTime()) {
						TokenRelUserUtil.this.tokenMap.remove(token);
					}
				}
				String clearSql = "delete from token_user_rel where expired_time <= ?";
				try {
					SimpleDateFormat sf = new SimpleDateFormat(DATE_FORMAT);
					String dbres = TokenRelUserUtil.this.sunECMDao.deleteSql(clearSql,
							new Object[] { sf.format(new Date(currentTime)) });
					if (!TransOptionKey.SUCCESS.equals(dbres)) {
						TokenRelUserUtil.log.warn("delete expired token fail!:{}", dbres);
					}
				} catch (Exception e) {
					TokenRelUserUtil.log.error(e.getMessage(), e);
				}
				log.info(" clear expired token end");
			}
		});
		clearPool.execute(t);
	}

	public void tokenMessage2NormalMessage(Map<String, String> map) throws SunECMException {
		String token = map.get(TOKEN);
		if (map.get("USERNAME") != null) {
			return;
		} else if (!StringUtil.stringIsNull(token)) {

			TokenInfoBean tokenbean = getTokenBean(token);
			map.put("USERNAME", tokenbean.getUser());
			map.put("PASSWORD", tokenbean.getPassword());

			threadBatchBean.set(tokenbean);
		}
	}

	/**
	 * 移除并返回线程存储对象
	 * 
	 * @return 移除的存储对象
	 */
	public TokenInfoBean removeThreadTokenBean() {
		TokenInfoBean tokenBean = threadBatchBean.get();
		threadBatchBean.remove();
		return tokenBean;
	}

	public void putToken(String token, TokenInfoBean tokenBean) {
		tokenMap.put(token, tokenBean);

	}

	/**
	 * 加密用户信息
	 * 
	 * @param username
	 * @param password
	 * @return
	 * @throws SunECMException
	 */
	public String enCodeUserInfo(String username, String password) throws SunECMException {
		String user_info;
		try {
			user_info = URLEnCode.ebotongEncrypto(username + TransOptionKey.SPLITSYM + password);
		} catch (Exception e) {
			throw new SunECMException(SunECMExceptionStatus.URLENCODE_FAIL, e);
		}
		return user_info;
	}

	/**
	 * 解密用户信息
	 * 
	 * @param user_info
	 * @return
	 * @throws SunECMException
	 */

	private TokenInfoBean deCodeUserInfo(String user_info) throws SunECMException {
		String[] userinfo;
		try {
			// user_info =
			userinfo = URLEnCode.ebotongDecrypto(user_info).split(TransOptionKey.SPLITSYM);
		} catch (Exception e) {
			throw new SunECMException(SunECMExceptionStatus.URLENCODE_FAIL, e);
		}
		return new TokenInfoBean(userinfo[0], userinfo[1], 0);
	}

	private TokenInfoBean getTokenBean(String token) throws SunECMException {
		TokenInfoBean tokenbean = tokenMap.get(token);
		if (tokenbean == null) {
			String sql = "select USER_INFO,EXPIRED_TIME from token_user_rel where token_value=?";
			List<Map<String, String>> res = sunECMDao.searchSql(sql, new Object[] { token });
			if (!res.isEmpty()) {
				tokenbean = deCodeUserInfo(res.get(0).get("USER_INFO"));
				tokenbean.setExpiredTime(StringDate2Long(res.get(0).get("EXPIRED_TIME")));
				tokenMap.put(token, tokenbean);
			} else {
				throw new SunECMException(SunECMExceptionStatus.USER_WITHOUT_LOGIN, "无效的令牌");
			}
		}
		long t = System.currentTimeMillis();
		if (t >= tokenbean.getExpiredTime()) {
			tokenMap.remove(token);
			throw new SunECMException(SunECMExceptionStatus.USER_WITHOUT_LOGIN, "无效的令牌");
		}
		return tokenbean;
	}

	public static class TokenInfoBean {
		private String user;
		private String password;
		private long expiredTime;

		public TokenInfoBean(String user, String password, long expiredTime) {
			super();
			this.user = user;
			this.password = password;
			this.expiredTime = expiredTime;
		}

		public String getUser() {
			return user;
		}

		public String getPassword() {
			return password;
		}

		public long getExpiredTime() {
			return expiredTime;
		}

		private void setExpiredTime(long expiredTime) {
			this.expiredTime = expiredTime;
		}

	}
}
