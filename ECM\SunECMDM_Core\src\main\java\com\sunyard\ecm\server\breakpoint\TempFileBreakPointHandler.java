package com.sunyard.ecm.server.breakpoint;

import com.sunyard.common.Configuration;
import com.sunyard.ecm.server.bean.BatchBean;
import com.sunyard.ecm.server.bean.BatchFileBean;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.daoinf.IBreakPointDao;
import com.sunyard.ecm.server.util.EcmUtil;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.util.FileUtil;
import com.sunyard.ws.utils.XMLUtil;
import org.apache.cxf.common.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class TempFileBreakPointHandler implements IBreakPointDao {
	private final static  Logger log = LoggerFactory.getLogger(TempFileBreakPointHandler.class);
	private String filePath;

	public String getBreakPointPath() {
		if (StringUtils.isEmpty(filePath)) {
			filePath = Configuration.get("breakPoint", "/breakPoint/");
		}
		if (!filePath.endsWith("/")) {
			filePath = filePath + "/";
		}
		return filePath;
	}

	/**
	 * 如果服务端提供断点，那么在出错时需要保存该批次文件断点信息时调用。
	 * <p>
	 * 因为在外面没有做断点判断，所以需要在此方法中做判断。
	 * <p>
	 * 
	 * @param contentID
	 * @throws SunECMException
	 */
	public void saveBreakpointInfo(BatchBean batch) throws SunECMException {
		String contentID = batch.getIndex_Object().getContentID();
		if (!batch.isBreakPoint()) {
			log.debug("无需断点：" + contentID);
			return;
		}
		List<BatchFileBean> batchFileBeans = batch.getDocument_Objects();
		List<FileBean> files = new ArrayList<FileBean>();
		for (BatchFileBean batchFileBean : batchFileBeans) {
			List<FileBean> fileBeans = batchFileBean.getFiles();
			for (FileBean filebean : fileBeans) {
				if (filebean.getSaveName() != null) {// 表示文件已经上传到服务器
					files.add(filebean);
				}
			}
		}
		if (files.size() == 0) {
			log.debug("批次没有对应的上传文件信息");
			return;
		}
		FileUtil.writeXML(XMLUtil.list2Xml(files), filePath, contentID
				+ "_BREAKPOINT");
		log.debug("生成断点信息，保存在：" + filePath + contentID + "_BREAKPOINT.xml");
	}

	public void removeBreakPointInfo(String modelCode, String contentID) {
		String breakPointName = filePath + contentID + "_BREAKPOINT.xml";
		File file = new File(breakPointName);
		if (file.exists() && file.delete()) {
			log.info("删除断点文件成功：" + contentID);
		}
	}

	public List<FileBean> getBreakPoint(String modelCode, String contentId)
			throws SunECMException {
		String xml = getBreakPointInfoAsXML(modelCode, contentId);
		if (EcmUtil.isBlank(xml)) {
			return null;
		}
		List<FileBean> list = XMLUtil.xml2list(xml, FileBean.class);
		return list;
	}

	private String getBreakPointInfoAsXML(String modelCode, String contentID)
			throws SunECMException {
		String breakPointMsg = null;
		try {
			File file = new File(filePath + contentID + "_BREAKPOINT.xml");
			if (file.exists()) {
				breakPointMsg = FileUtil.readXml(filePath + contentID
						+ "_BREAKPOINT.xml");
			}
		} catch (IOException e) {
			log.error("--getBreakPointMsg-->IOException"
					+ SunECMExceptionStatus.FILE_NOT_FOUND + e.toString());
			throw new SunECMException(SunECMExceptionStatus.FILE_NOT_FOUND,
					e.toString());
		}
		return breakPointMsg;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

}
