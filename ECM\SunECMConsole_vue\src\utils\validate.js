/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path)
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  // const valid_map = ['admin', 'editor']
  // console.log(str)
  // alert('['+str+']');
  // return valid_map.indexOf(str.trim()) >= 0
  return true;
}

export function checkEnglish(a) {
  let reg = /^[a-zA-Z]+[a-zA-Z0-9_]*$/;
  if (!reg.test(a)) {
    return false;
  } else {
    return true;
  }
}