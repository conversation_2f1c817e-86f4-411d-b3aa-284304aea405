package com.sunyard.util;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


/**
 * 字符串工具类
 * 
 * <AUTHOR>
 * 
 */
public class StringUtil {
	/**
	 * 将参数类型转换为数据库定义的类型
	 * 
	 * @param typeCode
	 * @param value
	 * @return
	 */
	public static void convertToAttType(String typeCode, String value,
			StringBuffer valueBuffer) {
		// 4代表整形类型
		if (typeCode.equals("4")) {
			if (value == null || value.equals(""))
				valueBuffer.append("null").append(",");
			else
				valueBuffer.append(value).append(",");
		}
		// 12代表可变字符串类型
		if (typeCode.equals("12")) {
			valueBuffer.append("'").append(value).append("',");
		}
		// 3代表浮点类型
		if (typeCode.equals("3")) {
			if ("".equals(value))
				valueBuffer.append("null").append(",");
			else
				valueBuffer.append(value).append(",");
		}
		// 1代表字符串类型
		if (typeCode.equals("1")) {
			valueBuffer.append("'").append(value).append("',");
		}
	}

	/**
	 * 将参数类型转换为数据库定义的类型
	 * 
	 * @param typeCode
	 * @param value
	 * @return
	 */
	public static void convertToAttTypeWithOutDot(String typeCode,
			String value, StringBuffer valueBuffer) {
		// 4代表整形类型
		if (typeCode.equals("4")) {
			if ("".equals(value))
				valueBuffer.append("null").append("");
			else
				valueBuffer.append(value).append("");
		}
		// 12代表可变字符串类型
		if (typeCode.equals("12")) {
			valueBuffer.append("'").append(value).append("'");
		}
		// 3代表浮点类型
		if (typeCode.equals("3")) {
			if ("".equals(value))
				valueBuffer.append("null").append("");
			else
				valueBuffer.append(value).append("");
		}
		// 1代表字符串类型
		if (typeCode.equals("1")) {
			valueBuffer.append("'").append(value).append("'");
		}
	}

	/**
	 * 将customMap中与attrMap相同key删掉
	 * 
	 * @param customMap
	 * @param attrSet
	 */
	public static Map<String, String> removeSYSATTR(
			Map<String, String> customMap, String[] attrSet) {
		Map<String,String> copy=customMap;
		if (attrSet == null || copy == null) {
			return null;
		}
		for (String attrCode : attrSet) {
			if (copy.containsKey(attrCode)) {
				copy.remove(attrCode);
			}
		}
		return copy;
	}
	/**
	 * 校验str是否为空，为空返回true ，不为空返回false
	 * @param str
	 * @return
	 */
	public  static boolean stringIsNull(String str) {
		if (str == null || str.equals("") || str.equals("null") || str.equals("NULL")) {
			return true;
		} else {
			return false;
		}
	}
//	public static void main(String[] args) {
//		Map<String,String> m=new HashMap<String, String>();
////		m.put("a", "aa");
////		m.put("aa", "aa");
//
//Set<String> s =new HashSet<String>();
////s.add("a");
////s.add("b");
//		removeSYSATTR(null, null);
//		System.out.println(m+"----"+s);
//		
//		
//	}
}