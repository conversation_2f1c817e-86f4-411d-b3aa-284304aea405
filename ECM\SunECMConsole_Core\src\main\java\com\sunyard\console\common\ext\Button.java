package com.sunyard.console.common.ext;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: 按钮类</p>
 * <p>Description: 创建动态按钮</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class Button {
	/**
	 * 按钮文本
	 */
	public static final String TEXT = "text";
	/**
	 * 是否隐藏
	 */
	public static final String HIDDEN = "hidden";
	/**
	 * 按钮触发事件
	 */
	public static final String HANDLER = "handler";
	
	HashMap<String, Object> button = new HashMap<String,Object>();
	
	/**
	 * 设置属性
	 * @param key
	 * @param value
	 */
	public void setProperties(String key , String value){
		button.put(key, value);
	}
	public void setProperties(String key , int value){
		button.put(key, value);
	}
	public void setProperties(String key , boolean value){
		button.put(key, value);
	}
	public void setProperties_obj(String key , String value){
		button.put(key, "#"+value+"#");
	}
	public void setPropertiesObject(String key , Object value){
		button.put(key, value);
	}
	/**
	 * 获取某个属性
	 * @param key
	 * @return
	 */
	public Object getProperties(String key){
		return button.get(key);
	}
	public void removeProperties(String key){
		button.remove(key);
	}
	public Map<String,Object> getButton(){
		if(button != null && button.get("xtype") == null){
			button.put("xtype", "button");
		}
		return button;
	}
	
}
