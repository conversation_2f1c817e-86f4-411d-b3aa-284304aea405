<template>
  <div>
    <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="配置文档对象" :visible.sync="modelDocDialogFormVisible" width="1200px">
     <div class="edit_dev">
      <el-transfer
        style="text-align: left; display: inline-block"
        v-model="choiceDataList"
        filterable
        :left-default-checked="[1]"
        :right-default-checked="[2]"
        :titles="['未关联文档', '已关联文档']"
        :button-texts="['删除', '添加']"
        :format="{
          noChecked: '${total}',
          hasChecked: '${checked}/${total}',
        }"
        @change="handleChange"
        :data="notChoiceDataList"
      >
        <span slot-scope="{ option }"
          >{{ option.key }} - {{ option.label }}</span
        >
      </el-transfer>
    </div>  
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="hide">取消</el-button>
        <el-button size="mini" type="primary" @click="commitModelObjPartRel()"
          >完成</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>


<style>
.transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
}
</style>

<script>
import {
  getAllDocList,
  updateContentObjectPartRel,
} from "@/api/contentObjectManage";
import elDragDialog from "@/directive/el-drag-dialog";

export default {
  name: "model-attr-rel",
  directives: { elDragDialog },
  props: {
    modelCode: {
      required: false,
      type: String,
    },
  },
  data: function () {
    return {
      notChoiceDataList: [],
      value: [1],
      choiceDataList: [],
      modelDocDialogFormVisible: false,
      renderFunc(h, option) {
        return (
          <span>
            {option.key} - {option.label}
          </span>
        );
      },
    };
  },
  created() {},
  methods: {
    show() {
      this.modelDocDialogFormVisible = true;
    },
    hide() {
      this.modelDocDialogFormVisible = false;
    },
    commitModelObjPartRel() {
      let add = false;
      if (this.choiceDataList.length > 1) {
        alert("只能配置一个文档对象");
        return;
      } else if (this.choiceDataList.length == 1) {
        //存在文档关系
        add = true;
      }
      updateContentObjectPartRel({
        modeCode: this.modelCode,
        partCode: this.choiceDataList[0],
        add: add,
      }).then(() => {
        this.hide();
        this.$emit("getAllObj");

        this.$notify({
          title: "Success",
          message: "Created Successfully",
          type: "success",
          duration: 2000,
        });
      });

      // this.$emit("getAllObj");
    },

    getAllDocList(modelcode) {
      this.notChoiceDataList = [];
      this.choiceDataList = [];
      getAllDocList(this.modelCode).then((response) => {
        let allattrsTree = response.msg;
        allattrsTree.map((item) => {
          this.notChoiceDataList.push({
            key: item.id,
            label: item.text,
          });
          if (item.child) {
            this.choiceDataList.push(item.id);
          }
        });
        this.show();

        setTimeout(() => {
          // this.listLoading = false;
        }, 1 * 100);
      });
    },
  },
};
</script>
<style scoped>
.edit_dev >>> .el-transfer-panel {
     width:350px;
   }
</style>