package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

//内容模型模版信息参数部分

/**
 * <p>
 * Title:xstream 内容模型模版中文档或者索引的属性信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 *  
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("AttributeBean")
public class AttributeBean {
	@XStreamAsAttribute
	private String ATTRIBUTE_CODE;		//属性代码ATTRIBUTE_CODE
	@XStreamAsAttribute
	private String ATTRIBUTE_NAME;		//属性名 ATTRIBUTE_NAME\
	@XStreamAsAttribute
	private String APP_TYPE_CODE ;  // APP_TYPE_NAME属性类型 代码
	@XStreamAsAttribute
	private String APP_TYPE_NAME;		//APP_TYPE_NAME属性类型 中文名
	@XStreamAsAttribute
	private String ATTRIBUTE_LENGTH;	//属性长度ATTRIBUTE_LENGTH
	@XStreamAsAttribute
	private String ATTRIBUTE_DEFAULT;	//属性默认值ATTRIBUTE_DEFAULT
	@XStreamAsAttribute
	private String ATTRIBUTE_ISNULL;	//是否为空(0不可以为空 1可以为空)ATTRIBUTE_ISNULL
	@XStreamAsAttribute
	private String ATTRIBUTE_VALUE_MAPPING;	//值域范围ATTRIBUTE_VALUE_MAPPING
	@XStreamAsAttribute
	private String ATTRIBUTE_STATE;	//	属性状态 0 未使用,1使用 当状态为1时不能进行删除及修改操作.ATTRIBUTE_STATE
	@XStreamAsAttribute
	private String ATTRIBUTE_MODEL_TYPE;//属性类别 1:索引对象 2:文档对象 3:索引和文档 4:自定义ATTRIBUTE_MOTEL_TYPE
	@XStreamAsAttribute
	private String ATTRIBUTE_OTHERNAME;//属性别名
	@XStreamAsAttribute
	private String UPDATE_LEVEL;//修改级别
	public String getAttribute_code() {
		return ATTRIBUTE_CODE;
	}
	public void setAttribute_code(String attributeCode) {
		ATTRIBUTE_CODE = attributeCode;
	}
	public String getAttribute_name() {
		return ATTRIBUTE_NAME;
	}
	public void setAttribute_name(String attributeName) {
		ATTRIBUTE_NAME = attributeName;
	}
	public String getApp_type_name() {
		return APP_TYPE_NAME;
	}
	public void setApp_type_name(String appTypeName) {
		APP_TYPE_NAME = appTypeName;
	}
	public String getAttribute_length() {
		return ATTRIBUTE_LENGTH;
	}
	public void setAttribute_length(String attributeLength) {
		ATTRIBUTE_LENGTH = attributeLength;
	}
	public String getAttribute_default() {
		return ATTRIBUTE_DEFAULT;
	}
	public void setAttribute_default(String attributeDefault) {
		ATTRIBUTE_DEFAULT = attributeDefault;
	}
	public String getAttribute_isnull() {
		return ATTRIBUTE_ISNULL;
	}
	public void setAttribute_isnull(String attributeIsnull) {
		ATTRIBUTE_ISNULL = attributeIsnull;
	}
	public String getAttribute_value_mapping() {
		return ATTRIBUTE_VALUE_MAPPING;
	}
	public void setAttribute_value_mapping(String attributeValueMapping) {
		ATTRIBUTE_VALUE_MAPPING = attributeValueMapping;
	}
	public String getAttribute_state() {
		return ATTRIBUTE_STATE;
	}
	public void setAttribute_state(String attributeState) {
		ATTRIBUTE_STATE = attributeState;
	}
	public String getAttribute_model_type() {
		return ATTRIBUTE_MODEL_TYPE;
	}
	public void setAttribute_model_type(String attributeModelType) {
		ATTRIBUTE_MODEL_TYPE = attributeModelType;
	}
	public String getApp_type_code() {
		return APP_TYPE_CODE;
	}
	public void setApp_type_code(String appTypeCode) {
		APP_TYPE_CODE = appTypeCode;
	}
	public String getATTRIBUTE_OTHERNAME() {
		return ATTRIBUTE_OTHERNAME;
	}
	public void setATTRIBUTE_OTHERNAME(String aTTRIBUTE_OTHERNAME) {
		ATTRIBUTE_OTHERNAME = aTTRIBUTE_OTHERNAME;
	}
	public String getUPDATE_LEVEL() {
		return UPDATE_LEVEL;
	}
	public void setUPDATE_LEVEL(String uPDATE_LEVEL) {
		UPDATE_LEVEL = uPDATE_LEVEL;
	}
	@Override
	public String toString() {
		return "AttributeBean [APP_TYPE_CODE=" + APP_TYPE_CODE
				+ ", APP_TYPE_NAME=" + APP_TYPE_NAME + ", ATTRIBUTE_CODE="
				+ ATTRIBUTE_CODE + ", ATTRIBUTE_DEFAULT=" + ATTRIBUTE_DEFAULT
				+ ", ATTRIBUTE_ISNULL=" + ATTRIBUTE_ISNULL
				+ ", ATTRIBUTE_LENGTH=" + ATTRIBUTE_LENGTH
				+ ", ATTRIBUTE_MODEL_TYPE=" + ATTRIBUTE_MODEL_TYPE
				+ ", ATTRIBUTE_NAME=" + ATTRIBUTE_NAME + ", ATTRIBUTE_STATE="
				+ ATTRIBUTE_STATE + ", ATTRIBUTE_VALUE_MAPPING="
				+ ATTRIBUTE_VALUE_MAPPING 
				+ ", UPDATELEVEL=" + UPDATE_LEVEL
				+ ", ATTRIBUTE_OTHERNAME=" + ATTRIBUTE_OTHERNAME	
				+ "]";
	}

	/**
	 * 类型改变后转换值
	 * @param value
	 * 		转换对象值
	 * <AUTHOR>
	 * @return
	 * 		返回转换后的值
	 */
	public Object change(String value){
		if("4".equals(APP_TYPE_CODE)){//整形
			if(value == null || "".equals(value) || "null".equalsIgnoreCase(value)){
//				return SQLBean.NULL;
				return 0;
			}else{
				return Integer.parseInt(value);
			}
		}else if("12".equals(APP_TYPE_CODE)){//字符串
			if(value == null || "".equals(value) || "null".equalsIgnoreCase(value)){
//				return SQLBean.NULL;
				return "";
			}else{
				return new String(value);
			}
		} else {
			if (value == null || "".equals(value) || "null".equalsIgnoreCase(value)) {
				return "";
			} else
				return String.valueOf(value);
		}
	}
}

