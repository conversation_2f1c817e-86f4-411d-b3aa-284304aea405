package com.sunyard.util;

import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.commons.io.IOUtils;

public class SslUtils {

	public static void trustAllHttpsCertificates() throws Exception {
		TrustManager[] trustAllCerts = new TrustManager[1];
		TrustManager tm = new miTM();
		trustAllCerts[0] = tm;
		SSLContext sc = SSLContext.getInstance("SSL");
		sc.init(null, trustAllCerts, null);
		HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
	}

	static class miTM implements TrustManager,X509TrustManager {
		public X509Certificate[] getAcceptedIssuers() {
			return null;
		}

		public boolean isServerTrusted(X509Certificate[] certs) {
			return true;
		}

		public boolean isClientTrusted(X509Certificate[] certs) {
			return true;
		}

		public void checkServerTrusted(X509Certificate[] certs, String authType)
				throws CertificateException {
			return;
		}

		public void checkClientTrusted(X509Certificate[] certs, String authType)
				throws CertificateException {
			return;
		}
	}

	/**
	 * 忽略HTTPS请求的SSL证书，必须在openConnection之前调用
	 * @throws Exception
	 */
	public static void ignoreSsl() throws Exception{
		HostnameVerifier hv = new HostnameVerifier() {
			public boolean verify(String urlHostName, SSLSession session) {
				return true;
			}
		};
		trustAllHttpsCertificates();
		HttpsURLConnection.setDefaultHostnameVerifier(hv);
	}
	
//	public static void main(String[] args) {
//		URL url;
//		try {
////			url = new URL("https://***********:8443/SunECMDM/servlet/getFile?s/lV3bcibQjND5lMLOCM3p+5ogZFH9uyKRrtvEOy5ATi3a7u18wax87gN4DWjch/x/GYsj1xgxIS5rf3EVxjrHyMlrIUdjLwNKc/2/k9R6E9fvExWJ9Q5N5jcDqhS9xC7SQD4sEsTnZjqwcYyBE1D31ofGJ2HVUUmcQ0ie/fnI3qMmB2GEQ3us7RAX/3YqwJV4Uz9wkzUx23CnEniwTlztXp4bK0xUIimrfwhHX8hFaxJDzIK9VCvAvnrN3kVW5/YkbALc5hV/0L68LxvBkktUeXtULOdngzgzvQ1L1gR4nGRGsQrEWm62ChYHR+Yto7PbUueimFeNqmLUjc6o2BKg==");
//			
//			SslUtils.ignoreSsl();	//在openConnection前调用该方法
//		URL url2 = new URL("https://***********:8443/SunECMDM/servlet/getFile?s/lV3bcibQjND5lMLOCM3p+5ogZFH9uyKRrtvEOy5ATi3a7u18wax87gN4DWjch/x/GYsj1xgxIS5rf3EVxjrHyMlrIUdjLwNKc/2/k9R6E9fvExWJ9Q5N5jcDqhS9xC7SQD4sEsTnZjqwcYyBE1D31ofGJ2HVUUmcQ0ie/fnI07lX5s8MjJXM7RAX/3YqwJV4Uz9wkzUx23CnEniwTlztXp4bK0xUIimrfwhHX8hFaxJDzIK9VCvAvnrN3kVW5/YkbALc5hV/0L68LxvBkktUeXtULOdngzgzvQ1L1gR4nGRGsQrEWm62ChYHR+Yto7PbUueimFeNqmLUjc6o2BKg==");
//			
//			//打开建立连接
//			URLConnection con = url2.openConnection();
//			con.connect();
//			//百度响应的数据
//			InputStream iis = con.getInputStream();
//			
//			OutputStream out = new FileOutputStream("E://ss14File.jpg");
//			
//			IOUtils.copy(iis, out);
//			
//			IOUtils.closeQuietly(iis);
//			IOUtils.closeQuietly(out);
//			
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
//
//	}
}