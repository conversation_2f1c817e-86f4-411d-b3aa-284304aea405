package com.sunyard.ecm.server;

import java.util.ArrayList;
import java.util.List;

import com.sunyard.ecm.common.trans.GetSocketPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.common.Configuration;
import com.sunyard.ecm.common.trans.socket.Server;
import com.sunyard.ecm.server.dm.MemoryBatchInfoCache;
import com.sunyard.ecm.server.ua.MemDMConnectionCache;
import com.sunyard.ecm.util.net.JSSESocketFactory;
import com.sunyard.ecm.util.net.ServerSocketFactory;
import com.syd.common.serviceinit.ServiceInterface;

/**
 * 服务端socket启动初始化类
 * 
 * <AUTHOR>
 * 
 */
public class SocketInitial implements ServiceInterface {
	private final static Logger log = LoggerFactory.getLogger(SocketInitial.class);// log4j获取日志
//	private int defaultPort = 8021;// 默认socket端口8021
	private int defaultSSLPort = 8022;
	private boolean defaultwasCluster = false;
	private boolean defaultSocketEnable = true;
	private boolean defaultSSLEnable = false;
	private int defaultPoolSize = 150;
	private List<Server> servers = new ArrayList<Server>();
	private GetSocketPort socketPort;
	/**
	 * 从配置文件获取socket端口
	 */
	public SocketInitial(GetSocketPort socketPort) {
		this.socketPort=socketPort;
		boolean wasCluster = Configuration.getBoolean("wasCluster",
				defaultwasCluster);
		if (!wasCluster) {
			if (Configuration.getBoolean("socketEnable", defaultSocketEnable)) {
				try {
					Server socket = new Server(
							ServerSocketFactory.getDefault(),
							socketPort.getPort(),
							Configuration.getInt("socketMaxThreadPool",
									defaultPoolSize));
					socket.setServerName("NormalSocketServer");
					socket.setConnectionType("SOCKET");
					servers.add(socket);
					log.info("Init socket success");
				} catch (Exception e) {
					log.error("Failed to start socket server", e);
				}
			}
			if (Configuration.getBoolean("sslSocketEnable", defaultSSLEnable)) {
				try {
					ServerSocketFactory jsseSocketFactory = new JSSESocketFactory();
					jsseSocketFactory.setAttribute("sslProtocol",
							Configuration.get("sslProtocol"));
					jsseSocketFactory.setAttribute("keystoreFile",
							Configuration.get("keystoreFile"));
					jsseSocketFactory.setAttribute("keystorePass",
							Configuration.get("keystorePass"));
					Server sslsocket = new Server(jsseSocketFactory,
							Configuration.getInt("sslSocketPort",
									defaultSSLPort), Configuration.getInt(
									"socketMaxThreadPool", defaultPoolSize));
					sslsocket.setServerName("SSLSocketServer");
					sslsocket.setConnectionType("SSL SOCKET");
					servers.add(sslsocket);
					log.info("Init ssl socket success");
				} catch (Exception e) {
					log.error("Failed to start ssl socket server", e);
				}
			}
		}

	}

	/**
	 * 关闭监听服务和线程池
	 */
	public void cleanup() {
		for (Server server : servers) {
			server.stop();
		}
		// TODO 暂时这么写
		MemoryBatchInfoCache.getInstance().clear();
		MemDMConnectionCache.getInstance().clear();
	}

	public String getServiceName() {
		return "SocketInitial";
	}

	public boolean start() {
		for (Server server : servers) {
			Thread sokcetserver = new Thread(server);
			sokcetserver.setName(server.getServerName());
			sokcetserver.start();
		}
		return true;
	}



	public void setSocketPort(GetSocketPort socketPort) {
		this.socketPort = socketPort;
	}
	
	public GetSocketPort getSocketPort() {
		return socketPort;
	}


	
}