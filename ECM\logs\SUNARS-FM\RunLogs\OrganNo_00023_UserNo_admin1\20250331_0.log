2025-03-31 10:56:54.761 [OrganNo_00023_UserNo_admin1] [a08d8c690e97ab69/3dab17c055d9beb1] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 10:56:54.902 [OrganNo_00023_UserNo_admin1] [a08d8c690e97ab69/3e69eaf183b4d9ce] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 10:56:54.902 [OrganNo_00023_UserNo_admin1] [a08d8c690e97ab69/3e69eaf183b4d9ce] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 10:56:54.937 [OrganNo_00023_UserNo_admin1] [a08d8c690e97ab69/3e69eaf183b4d9ce] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 10:56:54.939 [OrganNo_00023_UserNo_admin1] [a08d8c690e97ab69/3e69eaf183b4d9ce] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 10:56:54.940 [OrganNo_00023_UserNo_admin1] [a08d8c690e97ab69/3e69eaf183b4d9ce] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 10:56:54.970 [OrganNo_00023_UserNo_admin1] [a08d8c690e97ab69/3e69eaf183b4d9ce] [http-nio-9009-exec-2] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 10:56:55.285 [OrganNo_00023_UserNo_admin1] [a08d8c690e97ab69/3dab17c055d9beb1] [http-nio-9009-exec-2] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 10:56:55.459 [OrganNo_00023_UserNo_admin1] [d1d9fca120bab2cb/72525a2aaf6ffb8b] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-31 10:56:55.538 [OrganNo_00023_UserNo_admin1] [d1d9fca120bab2cb/7f87e8d090c79ecc] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE IN ('23', '24')
2025-03-31 10:56:55.540 [OrganNo_00023_UserNo_admin1] [d1d9fca120bab2cb/7f87e8d090c79ecc] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-03-31 10:56:55.577 [OrganNo_00023_UserNo_admin1] [d1d9fca120bab2cb/7f87e8d090c79ecc] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 10:56:55.580 [OrganNo_00023_UserNo_admin1] [d1d9fca120bab2cb/7f87e8d090c79ecc] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 10:56:55.584 [OrganNo_00023_UserNo_admin1] [d1d9fca120bab2cb/7f87e8d090c79ecc] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-03-31 10:56:55.618 [OrganNo_00023_UserNo_admin1] [d1d9fca120bab2cb/7f87e8d090c79ecc] [http-nio-9009-exec-1] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-03-31 10:56:55.703 [OrganNo_00023_UserNo_admin1] [d1d9fca120bab2cb/72525a2aaf6ffb8b] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 10:57:27.671 [OrganNo_00023_UserNo_admin1] [87585474b49ca690/7f85aae6412c3062] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 10:57:27.678 [OrganNo_00023_UserNo_admin1] [87585474b49ca690/a0b24b935138495b] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 10:57:27.679 [OrganNo_00023_UserNo_admin1] [87585474b49ca690/a0b24b935138495b] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 10:57:27.709 [OrganNo_00023_UserNo_admin1] [87585474b49ca690/a0b24b935138495b] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 10:57:27.710 [OrganNo_00023_UserNo_admin1] [87585474b49ca690/a0b24b935138495b] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 10:57:27.711 [OrganNo_00023_UserNo_admin1] [87585474b49ca690/a0b24b935138495b] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 10:57:27.742 [OrganNo_00023_UserNo_admin1] [87585474b49ca690/a0b24b935138495b] [http-nio-9009-exec-4] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 10:57:27.827 [OrganNo_00023_UserNo_admin1] [87585474b49ca690/7f85aae6412c3062] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 10:57:27.922 [OrganNo_00023_UserNo_admin1] [afe53fc280abfd10/0124a6996716ee2c] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-31 10:57:27.959 [OrganNo_00023_UserNo_admin1] [afe53fc280abfd10/e55a40a69b89f163] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE IN ('23', '24')
2025-03-31 10:57:27.961 [OrganNo_00023_UserNo_admin1] [afe53fc280abfd10/e55a40a69b89f163] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-03-31 10:57:27.993 [OrganNo_00023_UserNo_admin1] [afe53fc280abfd10/e55a40a69b89f163] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 10:57:27.995 [OrganNo_00023_UserNo_admin1] [afe53fc280abfd10/e55a40a69b89f163] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 10:57:27.995 [OrganNo_00023_UserNo_admin1] [afe53fc280abfd10/e55a40a69b89f163] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-03-31 10:57:28.030 [OrganNo_00023_UserNo_admin1] [afe53fc280abfd10/e55a40a69b89f163] [http-nio-9009-exec-10] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-03-31 10:57:28.107 [OrganNo_00023_UserNo_admin1] [afe53fc280abfd10/0124a6996716ee2c] [http-nio-9009-exec-10] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 10:57:43.560 [OrganNo_00023_UserNo_admin1] [f098195c7ae52fd9/4fb13984c36f65d8] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 10:57:43.567 [OrganNo_00023_UserNo_admin1] [f098195c7ae52fd9/3ddd9e2373a0cde1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 10:57:43.568 [OrganNo_00023_UserNo_admin1] [f098195c7ae52fd9/3ddd9e2373a0cde1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 10:57:43.599 [OrganNo_00023_UserNo_admin1] [f098195c7ae52fd9/3ddd9e2373a0cde1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 10:57:43.600 [OrganNo_00023_UserNo_admin1] [f098195c7ae52fd9/3ddd9e2373a0cde1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 10:57:43.600 [OrganNo_00023_UserNo_admin1] [f098195c7ae52fd9/3ddd9e2373a0cde1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 10:57:43.630 [OrganNo_00023_UserNo_admin1] [f098195c7ae52fd9/3ddd9e2373a0cde1] [http-nio-9009-exec-7] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 10:57:43.713 [OrganNo_00023_UserNo_admin1] [f098195c7ae52fd9/4fb13984c36f65d8] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 10:57:43.808 [OrganNo_00023_UserNo_admin1] [aeb3ae6aa985ec61/d0154f9346a05601] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-03-31 10:57:43.837 [OrganNo_00023_UserNo_admin1] [aeb3ae6aa985ec61/49061dc822a07bd7] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-03-31 10:57:43.838 [OrganNo_00023_UserNo_admin1] [aeb3ae6aa985ec61/49061dc822a07bd7] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-03-31 10:57:43.869 [OrganNo_00023_UserNo_admin1] [aeb3ae6aa985ec61/49061dc822a07bd7] [http-nio-9009-exec-8] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 10:57:43.945 [OrganNo_00023_UserNo_admin1] [aeb3ae6aa985ec61/d0154f9346a05601] [http-nio-9009-exec-8] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 10:58:52.418 [OrganNo_00023_UserNo_admin1] [396d392c45e66627/120aa79b48ce092d] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 10:58:52.484 [OrganNo_00023_UserNo_admin1] [396d392c45e66627/24bea548680ee8db] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 10:58:52.485 [OrganNo_00023_UserNo_admin1] [396d392c45e66627/24bea548680ee8db] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 10:58:52.515 [OrganNo_00023_UserNo_admin1] [396d392c45e66627/24bea548680ee8db] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 10:58:52.515 [OrganNo_00023_UserNo_admin1] [396d392c45e66627/24bea548680ee8db] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 10:58:52.516 [OrganNo_00023_UserNo_admin1] [396d392c45e66627/24bea548680ee8db] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 10:58:52.546 [OrganNo_00023_UserNo_admin1] [396d392c45e66627/24bea548680ee8db] [http-nio-9009-exec-9] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 10:58:52.626 [OrganNo_00023_UserNo_admin1] [396d392c45e66627/120aa79b48ce092d] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 10:58:52.720 [OrganNo_00023_UserNo_admin1] [9ea0652c7c87f90a/e502e79e78a7e9fd] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-31 10:58:52.746 [OrganNo_00023_UserNo_admin1] [9ea0652c7c87f90a/07695a191c28778d] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE IN ('23', '24')
2025-03-31 10:58:52.748 [OrganNo_00023_UserNo_admin1] [9ea0652c7c87f90a/07695a191c28778d] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-03-31 10:58:52.782 [OrganNo_00023_UserNo_admin1] [9ea0652c7c87f90a/07695a191c28778d] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 10:58:52.783 [OrganNo_00023_UserNo_admin1] [9ea0652c7c87f90a/07695a191c28778d] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 10:58:52.784 [OrganNo_00023_UserNo_admin1] [9ea0652c7c87f90a/07695a191c28778d] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-03-31 10:58:52.817 [OrganNo_00023_UserNo_admin1] [9ea0652c7c87f90a/07695a191c28778d] [http-nio-9009-exec-11] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-03-31 10:58:52.892 [OrganNo_00023_UserNo_admin1] [9ea0652c7c87f90a/e502e79e78a7e9fd] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 10:59:40.536 [OrganNo_00023_UserNo_admin1] [381621bb4812473d/6df4ecd3eb2d1a7b] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 10:59:40.543 [OrganNo_00023_UserNo_admin1] [381621bb4812473d/a4ebe0b59f14dd4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 10:59:40.544 [OrganNo_00023_UserNo_admin1] [381621bb4812473d/a4ebe0b59f14dd4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 10:59:40.576 [OrganNo_00023_UserNo_admin1] [381621bb4812473d/a4ebe0b59f14dd4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 10:59:40.577 [OrganNo_00023_UserNo_admin1] [381621bb4812473d/a4ebe0b59f14dd4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 10:59:40.577 [OrganNo_00023_UserNo_admin1] [381621bb4812473d/a4ebe0b59f14dd4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 10:59:40.608 [OrganNo_00023_UserNo_admin1] [381621bb4812473d/a4ebe0b59f14dd4a] [http-nio-9009-exec-13] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 10:59:40.689 [OrganNo_00023_UserNo_admin1] [381621bb4812473d/6df4ecd3eb2d1a7b] [http-nio-9009-exec-13] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 10:59:40.767 [OrganNo_00023_UserNo_admin1] [2e7904dec4995df8/df8dac9a6c823050] [http-nio-9009-exec-14] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-31 10:59:40.793 [OrganNo_00023_UserNo_admin1] [2e7904dec4995df8/ac7180504556653d] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE IN ('23', '24')
2025-03-31 10:59:40.794 [OrganNo_00023_UserNo_admin1] [2e7904dec4995df8/ac7180504556653d] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-03-31 10:59:40.826 [OrganNo_00023_UserNo_admin1] [2e7904dec4995df8/ac7180504556653d] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 10:59:40.828 [OrganNo_00023_UserNo_admin1] [2e7904dec4995df8/ac7180504556653d] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 10:59:40.828 [OrganNo_00023_UserNo_admin1] [2e7904dec4995df8/ac7180504556653d] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-03-31 10:59:40.861 [OrganNo_00023_UserNo_admin1] [2e7904dec4995df8/ac7180504556653d] [http-nio-9009-exec-14] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-03-31 10:59:40.935 [OrganNo_00023_UserNo_admin1] [2e7904dec4995df8/df8dac9a6c823050] [http-nio-9009-exec-14] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:01:44.425 [OrganNo_00023_UserNo_admin1] [96832450e5cfb29e/7a43393c231f296d] [http-nio-9009-exec-30] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:01:44.495 [OrganNo_00023_UserNo_admin1] [96832450e5cfb29e/f0e265ebe1c6eccb] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:01:44.496 [OrganNo_00023_UserNo_admin1] [96832450e5cfb29e/f0e265ebe1c6eccb] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:01:44.526 [OrganNo_00023_UserNo_admin1] [96832450e5cfb29e/f0e265ebe1c6eccb] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:01:44.527 [OrganNo_00023_UserNo_admin1] [96832450e5cfb29e/f0e265ebe1c6eccb] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:01:44.528 [OrganNo_00023_UserNo_admin1] [96832450e5cfb29e/f0e265ebe1c6eccb] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:01:44.557 [OrganNo_00023_UserNo_admin1] [96832450e5cfb29e/f0e265ebe1c6eccb] [http-nio-9009-exec-30] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:01:44.638 [OrganNo_00023_UserNo_admin1] [96832450e5cfb29e/7a43393c231f296d] [http-nio-9009-exec-30] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:01:44.735 [OrganNo_00023_UserNo_admin1] [c6648d71f9aa3c72/44669dca1c673838] [http-nio-9009-exec-17] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-03-31 11:01:44.756 [OrganNo_00023_UserNo_admin1] [c6648d71f9aa3c72/fa71dedd655d712e] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND EXISTS (SELECT 1 FROM sm_organ_parent_tb sopt WHERE sopt.parent_organ = ? AND sopt.organ_no = SITE_NO)
2025-03-31 11:01:44.758 [OrganNo_00023_UserNo_admin1] [c6648d71f9aa3c72/fa71dedd655d712e] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), 00023(String)
2025-03-31 11:01:44.788 [OrganNo_00023_UserNo_admin1] [c6648d71f9aa3c72/fa71dedd655d712e] [http-nio-9009-exec-17] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-03-31 11:01:44.868 [OrganNo_00023_UserNo_admin1] [c6648d71f9aa3c72/44669dca1c673838] [http-nio-9009-exec-17] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:01:45.551 [OrganNo_00023_UserNo_admin1] [8625cf0ad5591bef/606e1f66b260e0f1] [http-nio-9009-exec-19] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:01:45.558 [OrganNo_00023_UserNo_admin1] [8625cf0ad5591bef/e19fc481b82ef4c0] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:01:45.559 [OrganNo_00023_UserNo_admin1] [8625cf0ad5591bef/e19fc481b82ef4c0] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:01:45.589 [OrganNo_00023_UserNo_admin1] [8625cf0ad5591bef/e19fc481b82ef4c0] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:01:45.590 [OrganNo_00023_UserNo_admin1] [8625cf0ad5591bef/e19fc481b82ef4c0] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:01:45.590 [OrganNo_00023_UserNo_admin1] [8625cf0ad5591bef/e19fc481b82ef4c0] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:01:45.620 [OrganNo_00023_UserNo_admin1] [8625cf0ad5591bef/e19fc481b82ef4c0] [http-nio-9009-exec-19] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:01:45.692 [OrganNo_00023_UserNo_admin1] [8625cf0ad5591bef/606e1f66b260e0f1] [http-nio-9009-exec-19] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:01:45.753 [OrganNo_00023_UserNo_admin1] [1cca3bd41c53a69e/132e2b3bddbe7518] [http-nio-9009-exec-20] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_4"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1",
		"isCenter":"1"
	}
}
2025-03-31 11:01:45.776 [OrganNo_00023_UserNo_admin1] [1cca3bd41c53a69e/8b3a86f5ef99452d] [http-nio-9009-exec-20] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE = ?
2025-03-31 11:01:45.778 [OrganNo_00023_UserNo_admin1] [1cca3bd41c53a69e/8b3a86f5ef99452d] [http-nio-9009-exec-20] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 14(String)
2025-03-31 11:01:45.810 [OrganNo_00023_UserNo_admin1] [1cca3bd41c53a69e/8b3a86f5ef99452d] [http-nio-9009-exec-20] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 11:01:45.811 [OrganNo_00023_UserNo_admin1] [1cca3bd41c53a69e/8b3a86f5ef99452d] [http-nio-9009-exec-20] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 11:01:45.812 [OrganNo_00023_UserNo_admin1] [1cca3bd41c53a69e/8b3a86f5ef99452d] [http-nio-9009-exec-20] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 14(String), 15(Integer)
2025-03-31 11:01:45.844 [OrganNo_00023_UserNo_admin1] [1cca3bd41c53a69e/8b3a86f5ef99452d] [http-nio-9009-exec-20] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 1
2025-03-31 11:01:45.924 [OrganNo_00023_UserNo_admin1] [1cca3bd41c53a69e/132e2b3bddbe7518] [http-nio-9009-exec-20] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314183802129311",
				"applicationState":"FM_STATE_APP_4",
				"baleNo":"2025031400023002",
				"businessDate":"20250314",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400314",
				"endBusiDate":"20250314",
				"id":"8972b5df4f9a4606b5ce7ea1cf318625",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"19"
			}
		],
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:01:46.005 [OrganNo_00023_UserNo_admin1] [32c34db72c1117ab/2bc5539e9fe29855] [http-nio-9009-exec-21] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"packageState":"FM_STATE_PACK_1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-03-31 11:01:46.036 [OrganNo_00023_UserNo_admin1] [32c34db72c1117ab/521a1ae19f045bed] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_PACKAGE WHERE PACKAGE_STATE = ? AND EXISTS (SELECT 1 FROM sm_organ_parent_tb sopt WHERE sopt.parent_organ = ? AND sopt.organ_no = SITENO)
2025-03-31 11:01:46.037 [OrganNo_00023_UserNo_admin1] [32c34db72c1117ab/521a1ae19f045bed] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - ==> Parameters: 1(String), 00023(String)
2025-03-31 11:01:46.069 [OrganNo_00023_UserNo_admin1] [32c34db72c1117ab/521a1ae19f045bed] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - <==      Total: 1
2025-03-31 11:01:46.070 [OrganNo_00023_UserNo_admin1] [32c34db72c1117ab/521a1ae19f045bed] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.P.selectPackagesByConfig - ==>  Preparing: select PACKAGE_NO "PACKAGE_NO", ID "ID", PACKAGE_STATE "PACKAGE_STATE", USER_NO "USER_NO", USER_NAME "USER_NAME", REGISTER_DATE "REGISTER_DATE", ORGAN_NO "ORGAN_NO", ORGAN_NAME "ORGAN_NAME",BELONG_YEAR "BELONG_YEAR", CODE_NO "CODE_NO", KEEP_YEAR "KEEP_YEAR", PACK_TYPE "PACK_TYPE", TODAY_NUM "TODAY_NUM", YEAR_NUM "YEAR_NUM", TRUNK_NO "TRUNK_NO", FRAMESEQ "FRAMESEQ",TOTAL_COUNT "TOTAL_COUNT", DESTROYDATE "DESTROYDATE", BEGINBUSIDATE "BEGINBUSIDATE", ENDBUSIDATE "ENDBUSIDATE", SITENO "SITENO", SITENAME "SITENAME", CODENAME "CODENAME", PLACEFILE "PLACEFILE", ALLTELLERS "ALLTELLERS", AREANO "AREANO", WAREHOUSENO "WAREHOUSENO", VOUCHER_COUNT "VOUCHER_COUNT" from FM_PACKAGE WHERE PACKAGE_STATE = ? AND EXISTS (select 1 from sm_organ_parent_tb sopt where sopt.parent_organ = ? and sopt.organ_no = SITENO ) order by SITENO,BEGINBUSIDATE,TODAY_NUM DESC LIMIT ?
2025-03-31 11:01:46.071 [OrganNo_00023_UserNo_admin1] [32c34db72c1117ab/521a1ae19f045bed] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.P.selectPackagesByConfig - ==> Parameters: 1(String), 00023(String), 15(Integer)
2025-03-31 11:01:46.103 [OrganNo_00023_UserNo_admin1] [32c34db72c1117ab/521a1ae19f045bed] [http-nio-9009-exec-21] DEBUG c.s.a.f.d.P.selectPackagesByConfig - <==      Total: 1
2025-03-31 11:01:46.190 [OrganNo_00023_UserNo_admin1] [32c34db72c1117ab/2bc5539e9fe29855] [http-nio-9009-exec-21] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"alltellers":"20250314:6045330",
				"beginbusidate":"20250314",
				"belongYear":"2025",
				"codeNo":"0",
				"codename":"凭证",
				"endbusidate":"20250314",
				"id":"9a9a376037cc4123b79e3670d7583da2",
				"keepYear":"15",
				"packageNo":"0202500023D1500001",
				"packageState":"FM_STATE_PACK_1",
				"placefile":"凭证",
				"registerDate":"20250314",
				"siteno":"00023",
				"todayNum":1,
				"totalCount":1,
				"userName":"系统超级管理员",
				"userNo":"admin",
				"yearNum":1
			}
		],
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:01:55.298 [OrganNo_00023_UserNo_admin1] [9194e3c0c2d0197d/0e563fd3a5ef13e0] [http-nio-9009-exec-22] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-03-31 11:01:55.317 [OrganNo_00023_UserNo_admin1] [9194e3c0c2d0197d/fdff7d2281a4a404] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - ==>  Preparing: SELECT count(0) FROM FM_WAREHOUSE
2025-03-31 11:01:55.318 [OrganNo_00023_UserNo_admin1] [9194e3c0c2d0197d/fdff7d2281a4a404] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - ==> Parameters: 
2025-03-31 11:01:55.348 [OrganNo_00023_UserNo_admin1] [9194e3c0c2d0197d/fdff7d2281a4a404] [http-nio-9009-exec-22] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - <==      Total: 1
2025-03-31 11:01:55.434 [OrganNo_00023_UserNo_admin1] [9194e3c0c2d0197d/0e563fd3a5ef13e0] [http-nio-9009-exec-22] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-03-31 11:01:58.737 [OrganNo_00023_UserNo_admin1] [d14216b1970e1b12/28d974e3e3030257] [http-nio-9009-exec-26] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:01:58.745 [OrganNo_00023_UserNo_admin1] [d14216b1970e1b12/0567ab5566a0b1fc] [http-nio-9009-exec-26] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:01:58.745 [OrganNo_00023_UserNo_admin1] [d14216b1970e1b12/0567ab5566a0b1fc] [http-nio-9009-exec-26] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:01:58.775 [OrganNo_00023_UserNo_admin1] [d14216b1970e1b12/0567ab5566a0b1fc] [http-nio-9009-exec-26] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:01:58.776 [OrganNo_00023_UserNo_admin1] [d14216b1970e1b12/0567ab5566a0b1fc] [http-nio-9009-exec-26] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:01:58.776 [OrganNo_00023_UserNo_admin1] [d14216b1970e1b12/0567ab5566a0b1fc] [http-nio-9009-exec-26] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:01:58.807 [OrganNo_00023_UserNo_admin1] [d14216b1970e1b12/0567ab5566a0b1fc] [http-nio-9009-exec-26] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:01:58.876 [OrganNo_00023_UserNo_admin1] [d14216b1970e1b12/28d974e3e3030257] [http-nio-9009-exec-26] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:01:58.956 [OrganNo_00023_UserNo_admin1] [538e570f64cbba09/8152a067da6bb758] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"initFlowList"
	}
}
2025-03-31 11:01:59.016 [OrganNo_00023_UserNo_admin1] [538e570f64cbba09/8152a067da6bb758] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flows":[
			{
				"defaultNode":"701",
				"flowNodeList":[
					{
						"flowId":"888",
						"id":"5",
						"nextFlowNodeList":[
							
						],
						"purview":"FM_APP_BORROW_REQUEST",
						"text":"已作废",
						"value":"-000"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"4",
						"nextFlowNodeList":[
							{
								"flowNodeId":"4",
								"text":"确认办结",
								"value":"003"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"审批退回待处理",
						"value":"003"
					},
					{
						"canBeClose":"true",
						"flowId":"888",
						"id":"3",
						"nextFlowNodeList":[
							{
								"flowNodeId":"3",
								"text":"确认办结",
								"value":"000"
							}
						],
						"purview":"FM_APP_BORROW_REQUEST,FM_APP_BORROW_RESPONSE",
						"text":"借阅确认待办结",
						"value":"002"
					},
					{
						"flowId":"888",
						"id":"2",
						"nextFlowNodeList":[
							{
								"flowNodeId":"2",
								"text":"退回借阅审批",
								"value":"701"
							},
							{
								"flowNodeId":"2",
								"text":"借阅确认",
								"value":"002"
							}
						],
						"purview":"FM_APP_BORROW_RESPONSE",
						"text":"审批通过待确认",
						"value":"001"
					},
					{
						"flowId":"888",
						"id":"1",
						"nextFlowNodeList":[
							{
								"flowNodeId":"1",
								"text":"退回申请人",
								"value":"003"
							},
							{
								"flowNodeId":"1",
								"text":"审批通过",
								"value":"001"
							}
						],
						"purview":"FM_APP_BORROW_AUDIT_1",
						"text":"待审批",
						"value":"701"
					}
				],
				"id":"888",
				"value":"7"
			}
		]
	},
	"retMsg":""
}
2025-03-31 11:01:59.111 [OrganNo_00023_UserNo_admin1] [8adafaa43f638aa7/a126992f2c52dc90] [http-nio-9009-exec-24] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"searchType":[
			"request"
		],
		"pageSize":15,
		"currentPage":1
	}
}
2025-03-31 11:01:59.141 [OrganNo_00023_UserNo_admin1] [8adafaa43f638aa7/bacbbcf36fb14165] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BORROW_REQUEST WHERE USER_NO = ? AND ((REQUEST_STATE = '003' AND FLOW = '7') OR (REQUEST_STATE = '002' AND FLOW = '7') OR (REQUEST_STATE = '001' AND FLOW = '7') OR (REQUEST_STATE = '701' AND FLOW = '7'))
2025-03-31 11:01:59.142 [OrganNo_00023_UserNo_admin1] [8adafaa43f638aa7/bacbbcf36fb14165] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - ==> Parameters: admin1(String)
2025-03-31 11:01:59.172 [OrganNo_00023_UserNo_admin1] [8adafaa43f638aa7/bacbbcf36fb14165] [http-nio-9009-exec-24] DEBUG c.s.a.f.d.B.selectBorrowRequestSelective_COUNT - <==      Total: 1
2025-03-31 11:01:59.250 [OrganNo_00023_UserNo_admin1] [8adafaa43f638aa7/a126992f2c52dc90] [http-nio-9009-exec-24] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:00.582 [OrganNo_00023_UserNo_admin1] [0670c901c97341c8/29ffea0c3d91a5ba] [http-nio-9009-exec-27] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"getServerTime"
	}
}
2025-03-31 11:02:00.641 [OrganNo_00023_UserNo_admin1] [0670c901c97341c8/29ffea0c3d91a5ba] [http-nio-9009-exec-27] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"myServerTime":"20250331",
		"serverTime":1743390120588
	},
	"retMsg":""
}
2025-03-31 11:02:06.800 [OrganNo_00023_UserNo_admin1] [a341dbd729d407f5/73910863941b935e] [http-nio-9009-exec-29] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:02:06.808 [OrganNo_00023_UserNo_admin1] [a341dbd729d407f5/faaec64e7b1be063] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:02:06.809 [OrganNo_00023_UserNo_admin1] [a341dbd729d407f5/faaec64e7b1be063] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:02:06.839 [OrganNo_00023_UserNo_admin1] [a341dbd729d407f5/faaec64e7b1be063] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:06.840 [OrganNo_00023_UserNo_admin1] [a341dbd729d407f5/faaec64e7b1be063] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:02:06.841 [OrganNo_00023_UserNo_admin1] [a341dbd729d407f5/faaec64e7b1be063] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:02:06.871 [OrganNo_00023_UserNo_admin1] [a341dbd729d407f5/faaec64e7b1be063] [http-nio-9009-exec-29] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:02:06.956 [OrganNo_00023_UserNo_admin1] [a341dbd729d407f5/73910863941b935e] [http-nio-9009-exec-29] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:07.033 [OrganNo_00023_UserNo_admin1] [74b9b3559851d92c/619d0507be5cfba1] [http-nio-9009-exec-16] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"appLocationQuery",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-03-31 11:02:07.081 [OrganNo_00023_UserNo_admin1] [74b9b3559851d92c/1c772022aaf39c77] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery_COUNT - ==>  Preparing: SELECT count(0) FROM (SELECT t1.application_no, t1.business_date, t1.site_no, t1.site_name, t1.teller_no, t1.teller_name, t1.code_no, t1.code_name, t1.warrant_amount, t1.user_no, t1.user_name, t1.register_date, t1.application_state, t1.remark, t1.monitor, t2.package_no, t2.package_state, '' tmp1, '' tmp2, '' tmp3, '' pru_no, '' pru_name, '' prd, t3.trunk_no, t3.trunk_state, t3.app_count, t3.trunk_user_no, t3.trunk_user_name, t3.trunk_date, t3.frame_user_no tru_no, t3.frame_user_name tru_name, t3.frame_date trd, '' area_no, t3.ware_house_no, t3.batch_no, t1.ogan_no, t1.ogan_name, t1.APP_INEXNO, t1.END_BUSI_DATE, t1.FRAMESEQ, t1.app_type, t4.BALE_NO, t4.SEND_USER_NO, t4.SEND_USER_NAME, t4.SEND_DATE, t4.RECEIVE_DATE, t4.RECEIVE_USER_NO, t4.RECEIVE_USER_NAME FROM fm_application t1 LEFT JOIN fm_package t2 ON t1.package_no = t2.package_no LEFT JOIN fm_trunk t3 ON t1.trunk_no = t3.trunk_no LEFT JOIN FM_BALE_TB t4 ON t1.BALE_NO = t4.BALE_NO WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = t1.site_no)) row_
2025-03-31 11:02:07.081 [OrganNo_00023_UserNo_admin1] [74b9b3559851d92c/1c772022aaf39c77] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery_COUNT - ==> Parameters: admin1(String)
2025-03-31 11:02:07.115 [OrganNo_00023_UserNo_admin1] [74b9b3559851d92c/1c772022aaf39c77] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery_COUNT - <==      Total: 1
2025-03-31 11:02:07.116 [OrganNo_00023_UserNo_admin1] [74b9b3559851d92c/1c772022aaf39c77] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery - ==>  Preparing: select * from ( select t1.application_no,t1.business_date,t1.site_no,t1.site_name, t1.teller_no,t1.teller_name,t1.code_no,t1.code_name,t1.warrant_amount, t1.user_no,t1.user_name,t1.register_date,t1.application_state,t1.remark,t1.monitor, t2.package_no,t2.package_state,'' tmp1,'' tmp2,'' tmp3, '' pru_no,'' pru_name,'' prd, t3.trunk_no,t3.trunk_state,t3.app_count,t3.trunk_user_no,t3.trunk_user_name, t3.trunk_date,t3.frame_user_no tru_no,t3.frame_user_name tru_name, t3.frame_date trd,'' area_no,t3.ware_house_no,t3.batch_no,t1.ogan_no,t1.ogan_name,t1.APP_INEXNO,t1.END_BUSI_DATE,t1.FRAMESEQ,t1.app_type ,t4.BALE_NO,t4.SEND_USER_NO,t4.SEND_USER_NAME,t4.SEND_DATE,t4.RECEIVE_DATE,t4.RECEIVE_USER_NO,t4.RECEIVE_USER_NAME from fm_application t1 left join fm_package t2 on t1.package_no = t2.package_no left join fm_trunk t3 on t1.trunk_no = t3.trunk_no LEFT JOIN FM_BALE_TB t4 ON t1.BALE_NO = t4.BALE_NO WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = t1.site_no ) ) row_ order by row_.business_date DESC LIMIT ?
2025-03-31 11:02:07.117 [OrganNo_00023_UserNo_admin1] [74b9b3559851d92c/1c772022aaf39c77] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery - ==> Parameters: admin1(String), 15(Integer)
2025-03-31 11:02:07.153 [OrganNo_00023_UserNo_admin1] [74b9b3559851d92c/1c772022aaf39c77] [http-nio-9009-exec-16] DEBUG c.s.a.f.d.A.selectApplicationLocationQuery - <==      Total: 4
2025-03-31 11:02:07.234 [OrganNo_00023_UserNo_admin1] [74b9b3559851d92c/619d0507be5cfba1] [http-nio-9009-exec-16] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"app_type":"1",
				"application_state":"23",
				"teller_no":"2966437",
				"user_no":"admin",
				"site_no":"00023",
				"app_inexno":"1",
				"ogan_no":"00023",
				"ogan_name":"00023-中国银行四川省分行",
				"warrant_amount":"6",
				"site_name":"中国银行四川省分行",
				"user_name":"系统超级管理员",
				"teller_name":"李静",
				"business_date":"20250320",
				"code_name":"差错单",
				"application_no":"20250320111254186478",
				"end_busi_date":"20250320",
				"register_date":"20250320",
				"code_no":"1"
			},
			{
				"app_type":"1",
				"application_state":"14",
				"teller_no":"2966437",
				"user_no":"admin",
				"site_no":"00023",
				"app_inexno":"1",
				"send_date":"20250314",
				"ogan_no":"00023",
				"ogan_name":"00023-中国银行四川省分行",
				"bale_no":"2025031400023002",
				"warrant_amount":"19",
				"site_name":"中国银行四川省分行",
				"receive_user_no":"admin",
				"receive_user_name":"系统超级管理员",
				"user_name":"系统超级管理员",
				"teller_name":"李静",
				"business_date":"20250314",
				"code_name":"凭证",
				"application_no":"20250314183802129311",
				"end_busi_date":"20250314",
				"send_user_no":"admin",
				"register_date":"20250314",
				"code_no":"0",
				"send_user_name":"系统超级管理员",
				"receive_date":"20250314"
			},
			{
				"app_type":"1",
				"application_state":"13",
				"teller_no":"6045330",
				"user_no":"admin",
				"package_state":"1",
				"site_no":"00023",
				"app_inexno":"1",
				"send_date":"20250314",
				"ogan_no":"00023",
				"ogan_name":"00023-中国银行四川省分行",
				"bale_no":"2025031400023001",
				"warrant_amount":"10",
				"site_name":"中国银行四川省分行",
				"receive_user_no":"admin",
				"receive_user_name":"系统超级管理员",
				"user_name":"系统超级管理员",
				"teller_name":"李虹",
				"business_date":"20250314",
				"code_name":"凭证",
				"application_no":"20250314115427438151",
				"end_busi_date":"20250314",
				"send_user_no":"admin",
				"package_no":"0202500023D1500001",
				"register_date":"20250314",
				"code_no":"0",
				"send_user_name":"系统超级管理员",
				"receive_date":"20250314"
			},
			{
				"app_type":"1",
				"application_state":"23",
				"teller_no":"2966437",
				"user_no":"admin",
				"site_no":"00023",
				"app_inexno":"1",
				"ogan_no":"00023",
				"ogan_name":"00023-中国银行四川省分行",
				"warrant_amount":"18",
				"site_name":"中国银行四川省分行",
				"user_name":"系统超级管理员",
				"teller_name":"李静",
				"business_date":"20250313",
				"code_name":"凭证",
				"application_no":"20250314153732058149",
				"end_busi_date":"20250313",
				"register_date":"20250314",
				"code_no":"0"
			}
		],
		"totalCount":4,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:02:11.632 [OrganNo_00023_UserNo_admin1] [7da3dcbcaabd83b0/a98e00626ac7def4] [http-nio-9009-exec-52] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-03-31 11:02:11.658 [OrganNo_00023_UserNo_admin1] [7da3dcbcaabd83b0/04e32e5e328377c8] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_HOUSEKEYEXCHANGE_TB WHERE (SENDUSER IS NULL OR SENDUSER IN (SELECT USER_NO FROM SM_USERS_TB WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SM_USERS_TB.ORGAN_NO))) AND (RECIEVEUSER IS NULL OR RECIEVEUSER IN (SELECT USER_NO FROM SM_USERS_TB WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SM_USERS_TB.ORGAN_NO))) AND (GUARDER IS NULL OR GUARDER IN (SELECT USER_NO FROM SM_USERS_TB WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SM_USERS_TB.ORGAN_NO)))
2025-03-31 11:02:11.660 [OrganNo_00023_UserNo_admin1] [7da3dcbcaabd83b0/04e32e5e328377c8] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - ==> Parameters: admin1(String), admin1(String), admin1(String)
2025-03-31 11:02:11.695 [OrganNo_00023_UserNo_admin1] [7da3dcbcaabd83b0/04e32e5e328377c8] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:11.779 [OrganNo_00023_UserNo_admin1] [7da3dcbcaabd83b0/a98e00626ac7def4] [http-nio-9009-exec-52] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功!"
}
2025-03-31 11:02:15.990 [OrganNo_00023_UserNo_admin1] [54718bf0f1a79ce3/58a824a15895acc2] [http-nio-9009-exec-34] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:02:15.998 [OrganNo_00023_UserNo_admin1] [54718bf0f1a79ce3/752e72a3fb051d59] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:02:15.999 [OrganNo_00023_UserNo_admin1] [54718bf0f1a79ce3/752e72a3fb051d59] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:02:16.029 [OrganNo_00023_UserNo_admin1] [54718bf0f1a79ce3/752e72a3fb051d59] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:16.029 [OrganNo_00023_UserNo_admin1] [54718bf0f1a79ce3/752e72a3fb051d59] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:02:16.030 [OrganNo_00023_UserNo_admin1] [54718bf0f1a79ce3/752e72a3fb051d59] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:02:16.060 [OrganNo_00023_UserNo_admin1] [54718bf0f1a79ce3/752e72a3fb051d59] [http-nio-9009-exec-34] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:02:16.141 [OrganNo_00023_UserNo_admin1] [54718bf0f1a79ce3/58a824a15895acc2] [http-nio-9009-exec-34] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:20.020 [OrganNo_00023_UserNo_admin1] [257b4f42e6fbf167/b8be2cf23e168bab] [http-nio-9009-exec-38] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:02:20.027 [OrganNo_00023_UserNo_admin1] [257b4f42e6fbf167/4dd0774cfbf33d90] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:02:20.027 [OrganNo_00023_UserNo_admin1] [257b4f42e6fbf167/4dd0774cfbf33d90] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:02:20.057 [OrganNo_00023_UserNo_admin1] [257b4f42e6fbf167/4dd0774cfbf33d90] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:20.057 [OrganNo_00023_UserNo_admin1] [257b4f42e6fbf167/4dd0774cfbf33d90] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:02:20.058 [OrganNo_00023_UserNo_admin1] [257b4f42e6fbf167/4dd0774cfbf33d90] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:02:20.088 [OrganNo_00023_UserNo_admin1] [257b4f42e6fbf167/4dd0774cfbf33d90] [http-nio-9009-exec-38] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:02:20.173 [OrganNo_00023_UserNo_admin1] [257b4f42e6fbf167/b8be2cf23e168bab] [http-nio-9009-exec-38] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:20.248 [OrganNo_00023_UserNo_admin1] [86f5285c3d3cc134/6b5ca7b36d58ed61] [http-nio-9009-exec-36] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15,
		"queryType":"1"
	}
}
2025-03-31 11:02:20.267 [OrganNo_00023_UserNo_admin1] [86f5285c3d3cc134/78c2788a3a88a3f8] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_PACKAGE WHERE EXISTS (SELECT 1 FROM sm_organ_parent_tb sopt WHERE sopt.parent_organ = ? AND sopt.organ_no = SITENO)
2025-03-31 11:02:20.268 [OrganNo_00023_UserNo_admin1] [86f5285c3d3cc134/78c2788a3a88a3f8] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - ==> Parameters: 00023(String)
2025-03-31 11:02:20.298 [OrganNo_00023_UserNo_admin1] [86f5285c3d3cc134/78c2788a3a88a3f8] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.P.selectPackagesByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:20.299 [OrganNo_00023_UserNo_admin1] [86f5285c3d3cc134/78c2788a3a88a3f8] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.P.selectPackagesByConfig - ==>  Preparing: select PACKAGE_NO "PACKAGE_NO", ID "ID", PACKAGE_STATE "PACKAGE_STATE", USER_NO "USER_NO", USER_NAME "USER_NAME", REGISTER_DATE "REGISTER_DATE", ORGAN_NO "ORGAN_NO", ORGAN_NAME "ORGAN_NAME",BELONG_YEAR "BELONG_YEAR", CODE_NO "CODE_NO", KEEP_YEAR "KEEP_YEAR", PACK_TYPE "PACK_TYPE", TODAY_NUM "TODAY_NUM", YEAR_NUM "YEAR_NUM", TRUNK_NO "TRUNK_NO", FRAMESEQ "FRAMESEQ",TOTAL_COUNT "TOTAL_COUNT", DESTROYDATE "DESTROYDATE", BEGINBUSIDATE "BEGINBUSIDATE", ENDBUSIDATE "ENDBUSIDATE", SITENO "SITENO", SITENAME "SITENAME", CODENAME "CODENAME", PLACEFILE "PLACEFILE", ALLTELLERS "ALLTELLERS", AREANO "AREANO", WAREHOUSENO "WAREHOUSENO", VOUCHER_COUNT "VOUCHER_COUNT" from FM_PACKAGE WHERE EXISTS (select 1 from sm_organ_parent_tb sopt where sopt.parent_organ = ? and sopt.organ_no = SITENO ) order by SITENO,BEGINBUSIDATE,TODAY_NUM DESC LIMIT ?
2025-03-31 11:02:20.300 [OrganNo_00023_UserNo_admin1] [86f5285c3d3cc134/78c2788a3a88a3f8] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.P.selectPackagesByConfig - ==> Parameters: 00023(String), 15(Integer)
2025-03-31 11:02:20.331 [OrganNo_00023_UserNo_admin1] [86f5285c3d3cc134/78c2788a3a88a3f8] [http-nio-9009-exec-36] DEBUG c.s.a.f.d.P.selectPackagesByConfig - <==      Total: 1
2025-03-31 11:02:20.417 [OrganNo_00023_UserNo_admin1] [86f5285c3d3cc134/6b5ca7b36d58ed61] [http-nio-9009-exec-36] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"alltellers":"20250314:6045330",
				"beginbusidate":"20250314",
				"belongYear":"2025",
				"codeNo":"0",
				"codename":"凭证",
				"endbusidate":"20250314",
				"id":"9a9a376037cc4123b79e3670d7583da2",
				"keepYear":"15",
				"packageNo":"0202500023D1500001",
				"packageState":"FM_STATE_PACK_1",
				"placefile":"凭证",
				"registerDate":"20250314",
				"siteno":"00023",
				"todayNum":1,
				"totalCount":1,
				"userName":"系统超级管理员",
				"userNo":"admin",
				"yearNum":1
			}
		],
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:02:27.480 [OrganNo_00023_UserNo_admin1] [f07fdc5f0940c579/b2fc965b00711712] [http-nio-9009-exec-39] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:02:27.487 [OrganNo_00023_UserNo_admin1] [f07fdc5f0940c579/5c8138a7e96bfb85] [http-nio-9009-exec-39] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:02:27.488 [OrganNo_00023_UserNo_admin1] [f07fdc5f0940c579/5c8138a7e96bfb85] [http-nio-9009-exec-39] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:02:27.518 [OrganNo_00023_UserNo_admin1] [f07fdc5f0940c579/5c8138a7e96bfb85] [http-nio-9009-exec-39] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:27.518 [OrganNo_00023_UserNo_admin1] [f07fdc5f0940c579/5c8138a7e96bfb85] [http-nio-9009-exec-39] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:02:27.518 [OrganNo_00023_UserNo_admin1] [f07fdc5f0940c579/5c8138a7e96bfb85] [http-nio-9009-exec-39] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:02:27.548 [OrganNo_00023_UserNo_admin1] [f07fdc5f0940c579/5c8138a7e96bfb85] [http-nio-9009-exec-39] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:02:27.622 [OrganNo_00023_UserNo_admin1] [f07fdc5f0940c579/b2fc965b00711712] [http-nio-9009-exec-39] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:27.701 [OrganNo_00023_UserNo_admin1] [9bcd2ae3bc2026dd/91426438db5ccbba] [http-nio-9009-exec-25] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-03-31 11:02:27.721 [OrganNo_00023_UserNo_admin1] [9bcd2ae3bc2026dd/87162ea2477fffea] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-03-31 11:02:27.721 [OrganNo_00023_UserNo_admin1] [9bcd2ae3bc2026dd/87162ea2477fffea] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-03-31 11:02:27.754 [OrganNo_00023_UserNo_admin1] [9bcd2ae3bc2026dd/87162ea2477fffea] [http-nio-9009-exec-25] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 11:02:27.827 [OrganNo_00023_UserNo_admin1] [9bcd2ae3bc2026dd/91426438db5ccbba] [http-nio-9009-exec-25] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:02:35.129 [OrganNo_00023_UserNo_admin1] [a7d3ea5b058a85a2/5fbfaac8a24c3491] [http-nio-9009-exec-43] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:02:35.137 [OrganNo_00023_UserNo_admin1] [a7d3ea5b058a85a2/1ca9dd6c2e5844e3] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:02:35.138 [OrganNo_00023_UserNo_admin1] [a7d3ea5b058a85a2/1ca9dd6c2e5844e3] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:02:35.168 [OrganNo_00023_UserNo_admin1] [a7d3ea5b058a85a2/1ca9dd6c2e5844e3] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:35.169 [OrganNo_00023_UserNo_admin1] [a7d3ea5b058a85a2/1ca9dd6c2e5844e3] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:02:35.169 [OrganNo_00023_UserNo_admin1] [a7d3ea5b058a85a2/1ca9dd6c2e5844e3] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:02:35.200 [OrganNo_00023_UserNo_admin1] [a7d3ea5b058a85a2/1ca9dd6c2e5844e3] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:02:35.272 [OrganNo_00023_UserNo_admin1] [a7d3ea5b058a85a2/5fbfaac8a24c3491] [http-nio-9009-exec-43] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:35.364 [OrganNo_00023_UserNo_admin1] [c0093d78554a9999/0a75883771ca057a] [http-nio-9009-exec-68] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-03-31 11:02:35.392 [OrganNo_00023_UserNo_admin1] [c0093d78554a9999/feefb8de816ceefa] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE = ?
2025-03-31 11:02:35.392 [OrganNo_00023_UserNo_admin1] [c0093d78554a9999/feefb8de816ceefa] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-03-31 11:02:35.424 [OrganNo_00023_UserNo_admin1] [c0093d78554a9999/feefb8de816ceefa] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 11:02:35.425 [OrganNo_00023_UserNo_admin1] [c0093d78554a9999/feefb8de816ceefa] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE = ? order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 11:02:35.426 [OrganNo_00023_UserNo_admin1] [c0093d78554a9999/feefb8de816ceefa] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 23(String), 15(Integer)
2025-03-31 11:02:35.459 [OrganNo_00023_UserNo_admin1] [c0093d78554a9999/feefb8de816ceefa] [http-nio-9009-exec-68] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-03-31 11:02:35.534 [OrganNo_00023_UserNo_admin1] [c0093d78554a9999/0a75883771ca057a] [http-nio-9009-exec-68] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:02:35.612 [OrganNo_00023_UserNo_admin1] [7887bf14a46676f7/bcb22039fd3c9737] [http-nio-9009-exec-44] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-03-31 11:02:35.624 [OrganNo_00023_UserNo_admin1] [7887bf14a46676f7/b38407f92f9d15f5] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND EXISTS (SELECT 1 FROM sm_organ_parent_tb sopt WHERE sopt.parent_organ = ? AND sopt.organ_no = SITE_NO)
2025-03-31 11:02:35.624 [OrganNo_00023_UserNo_admin1] [7887bf14a46676f7/b38407f92f9d15f5] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), 00023(String)
2025-03-31 11:02:35.655 [OrganNo_00023_UserNo_admin1] [7887bf14a46676f7/b38407f92f9d15f5] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-03-31 11:02:35.735 [OrganNo_00023_UserNo_admin1] [7887bf14a46676f7/bcb22039fd3c9737] [http-nio-9009-exec-44] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:45.725 [OrganNo_00023_UserNo_admin1] [7a8179f13cc48453/3ab1e424c89d1a76] [http-nio-9009-exec-46] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:02:45.732 [OrganNo_00023_UserNo_admin1] [7a8179f13cc48453/8e9f8172254bb9c9] [http-nio-9009-exec-46] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:02:45.733 [OrganNo_00023_UserNo_admin1] [7a8179f13cc48453/8e9f8172254bb9c9] [http-nio-9009-exec-46] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:02:45.765 [OrganNo_00023_UserNo_admin1] [7a8179f13cc48453/8e9f8172254bb9c9] [http-nio-9009-exec-46] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:45.766 [OrganNo_00023_UserNo_admin1] [7a8179f13cc48453/8e9f8172254bb9c9] [http-nio-9009-exec-46] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:02:45.766 [OrganNo_00023_UserNo_admin1] [7a8179f13cc48453/8e9f8172254bb9c9] [http-nio-9009-exec-46] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:02:45.796 [OrganNo_00023_UserNo_admin1] [7a8179f13cc48453/8e9f8172254bb9c9] [http-nio-9009-exec-46] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:02:45.881 [OrganNo_00023_UserNo_admin1] [7a8179f13cc48453/3ab1e424c89d1a76] [http-nio-9009-exec-46] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:45.958 [OrganNo_00023_UserNo_admin1] [db01939b2983eea2/12202bb42afcdc3f] [http-nio-9009-exec-47] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"baleState":"1"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1"
	}
}
2025-03-31 11:02:45.971 [OrganNo_00023_UserNo_admin1] [db01939b2983eea2/96d2d3d19c6f605a] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==>  Preparing: SELECT count(0) FROM FM_BALE_TB WHERE BALE_STATE = ? AND EXISTS (SELECT 1 FROM sm_organ_parent_tb sopt WHERE sopt.parent_organ = ? AND sopt.organ_no = SITE_NO)
2025-03-31 11:02:45.971 [OrganNo_00023_UserNo_admin1] [db01939b2983eea2/96d2d3d19c6f605a] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - ==> Parameters: 1(String), 00023(String)
2025-03-31 11:02:46.001 [OrganNo_00023_UserNo_admin1] [db01939b2983eea2/96d2d3d19c6f605a] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.B.selectBaleOfOneUse_COUNT - <==      Total: 1
2025-03-31 11:02:46.081 [OrganNo_00023_UserNo_admin1] [db01939b2983eea2/12202bb42afcdc3f] [http-nio-9009-exec-47] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:53.130 [OrganNo_00023_UserNo_admin1] [e1196b56c5f91d5f/c19f2f2d1c03f418] [http-nio-9009-exec-49] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:02:53.141 [OrganNo_00023_UserNo_admin1] [e1196b56c5f91d5f/8d79ff52ae0650b1] [http-nio-9009-exec-49] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:02:53.142 [OrganNo_00023_UserNo_admin1] [e1196b56c5f91d5f/8d79ff52ae0650b1] [http-nio-9009-exec-49] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:02:53.173 [OrganNo_00023_UserNo_admin1] [e1196b56c5f91d5f/8d79ff52ae0650b1] [http-nio-9009-exec-49] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:53.173 [OrganNo_00023_UserNo_admin1] [e1196b56c5f91d5f/8d79ff52ae0650b1] [http-nio-9009-exec-49] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:02:53.174 [OrganNo_00023_UserNo_admin1] [e1196b56c5f91d5f/8d79ff52ae0650b1] [http-nio-9009-exec-49] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:02:53.204 [OrganNo_00023_UserNo_admin1] [e1196b56c5f91d5f/8d79ff52ae0650b1] [http-nio-9009-exec-49] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:02:53.288 [OrganNo_00023_UserNo_admin1] [e1196b56c5f91d5f/c19f2f2d1c03f418] [http-nio-9009-exec-49] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:02:53.367 [OrganNo_00023_UserNo_admin1] [fc08484c2a80be4b/095c19a1d5bc41a9] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-03-31 11:02:53.379 [OrganNo_00023_UserNo_admin1] [fc08484c2a80be4b/ca7a8fc9baac786e] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - ==>  Preparing: SELECT count(0) FROM FM_WAREHOUSE
2025-03-31 11:02:53.380 [OrganNo_00023_UserNo_admin1] [fc08484c2a80be4b/ca7a8fc9baac786e] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - ==> Parameters: 
2025-03-31 11:02:53.410 [OrganNo_00023_UserNo_admin1] [fc08484c2a80be4b/ca7a8fc9baac786e] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - <==      Total: 1
2025-03-31 11:02:53.735 [OrganNo_00023_UserNo_admin1] [fc08484c2a80be4b/095c19a1d5bc41a9] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-03-31 11:02:53.831 [OrganNo_00023_UserNo_admin1] [aa4e459a23752164/9dfe3ae0f6a179ea] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-03-31 11:02:53.851 [OrganNo_00023_UserNo_admin1] [aa4e459a23752164/48bdc5bb4cd0b2a8] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_FRAME_TB
2025-03-31 11:02:53.851 [OrganNo_00023_UserNo_admin1] [aa4e459a23752164/48bdc5bb4cd0b2a8] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - ==> Parameters: 
2025-03-31 11:02:53.881 [OrganNo_00023_UserNo_admin1] [aa4e459a23752164/48bdc5bb4cd0b2a8] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.H.selectByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:53.955 [OrganNo_00023_UserNo_admin1] [aa4e459a23752164/9dfe3ae0f6a179ea] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:02:54.048 [OrganNo_00023_UserNo_admin1] [596f7883c685a9b0/d99a17f72f02ca13] [http-nio-9009-exec-53] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_WAREHOUSE"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1
	}
}
2025-03-31 11:02:54.089 [OrganNo_00023_UserNo_admin1] [596f7883c685a9b0/17c7677e55a5127f] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.T.selectTrunkByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_TRUNK WHERE TRUNK_STATE = ? AND EXISTS (SELECT 1 FROM sm_organ_parent_tb sopt WHERE sopt.parent_organ = ? AND sopt.organ_no = FM_TRUNK.ORGAN_NO)
2025-03-31 11:02:54.090 [OrganNo_00023_UserNo_admin1] [596f7883c685a9b0/17c7677e55a5127f] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.T.selectTrunkByConfig_COUNT - ==> Parameters: 1(String), 00023(String)
2025-03-31 11:02:54.120 [OrganNo_00023_UserNo_admin1] [596f7883c685a9b0/17c7677e55a5127f] [http-nio-9009-exec-53] DEBUG c.s.a.f.d.T.selectTrunkByConfig_COUNT - <==      Total: 1
2025-03-31 11:02:54.202 [OrganNo_00023_UserNo_admin1] [596f7883c685a9b0/d99a17f72f02ca13] [http-nio-9009-exec-53] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:03:12.825 [OrganNo_00023_UserNo_admin1] [ef63993e30795b53/a2cd2dd51559213a] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:03:12.831 [OrganNo_00023_UserNo_admin1] [ef63993e30795b53/5090b21c8f2fa5e4] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:03:12.832 [OrganNo_00023_UserNo_admin1] [ef63993e30795b53/5090b21c8f2fa5e4] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:03:12.861 [OrganNo_00023_UserNo_admin1] [ef63993e30795b53/5090b21c8f2fa5e4] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:03:12.862 [OrganNo_00023_UserNo_admin1] [ef63993e30795b53/5090b21c8f2fa5e4] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:03:12.863 [OrganNo_00023_UserNo_admin1] [ef63993e30795b53/5090b21c8f2fa5e4] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:03:12.892 [OrganNo_00023_UserNo_admin1] [ef63993e30795b53/5090b21c8f2fa5e4] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:03:12.981 [OrganNo_00023_UserNo_admin1] [ef63993e30795b53/a2cd2dd51559213a] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:03:13.050 [OrganNo_00023_UserNo_admin1] [515c8135cb672843/69071825710bb51e] [http-nio-9009-exec-55] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-31 11:03:13.073 [OrganNo_00023_UserNo_admin1] [515c8135cb672843/b217370c62be2b0c] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE IN ('23', '24')
2025-03-31 11:03:13.074 [OrganNo_00023_UserNo_admin1] [515c8135cb672843/b217370c62be2b0c] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-03-31 11:03:13.105 [OrganNo_00023_UserNo_admin1] [515c8135cb672843/b217370c62be2b0c] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 11:03:13.106 [OrganNo_00023_UserNo_admin1] [515c8135cb672843/b217370c62be2b0c] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 11:03:13.106 [OrganNo_00023_UserNo_admin1] [515c8135cb672843/b217370c62be2b0c] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-03-31 11:03:13.138 [OrganNo_00023_UserNo_admin1] [515c8135cb672843/b217370c62be2b0c] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-03-31 11:03:13.221 [OrganNo_00023_UserNo_admin1] [515c8135cb672843/69071825710bb51e] [http-nio-9009-exec-55] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:23:44.696 [OrganNo_00023_UserNo_admin1] [26c1abf19fb5d1f3/38a7f1cf07b337fd] [http-nio-9009-exec-56] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:23:45.301 [OrganNo_00023_UserNo_admin1] [26c1abf19fb5d1f3/b5588fb67e4f35ff] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:23:45.302 [OrganNo_00023_UserNo_admin1] [26c1abf19fb5d1f3/b5588fb67e4f35ff] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:23:45.332 [OrganNo_00023_UserNo_admin1] [26c1abf19fb5d1f3/b5588fb67e4f35ff] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:23:45.333 [OrganNo_00023_UserNo_admin1] [26c1abf19fb5d1f3/b5588fb67e4f35ff] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:23:45.333 [OrganNo_00023_UserNo_admin1] [26c1abf19fb5d1f3/b5588fb67e4f35ff] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:23:45.362 [OrganNo_00023_UserNo_admin1] [26c1abf19fb5d1f3/b5588fb67e4f35ff] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:23:45.439 [OrganNo_00023_UserNo_admin1] [26c1abf19fb5d1f3/38a7f1cf07b337fd] [http-nio-9009-exec-56] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:23:45.518 [OrganNo_00023_UserNo_admin1] [6de1eb311fddd51a/97cc169464f6062e] [http-nio-9009-exec-80] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"applicationState":"FM_STATE_APP_13"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"0"
	}
}
2025-03-31 11:23:45.540 [OrganNo_00023_UserNo_admin1] [6de1eb311fddd51a/d64a1348993cdf74] [http-nio-9009-exec-80] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE USER_NO = ? AND APPLICATION_STATE = ?
2025-03-31 11:23:45.541 [OrganNo_00023_UserNo_admin1] [6de1eb311fddd51a/d64a1348993cdf74] [http-nio-9009-exec-80] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String), 23(String)
2025-03-31 11:23:45.570 [OrganNo_00023_UserNo_admin1] [6de1eb311fddd51a/d64a1348993cdf74] [http-nio-9009-exec-80] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 11:23:45.641 [OrganNo_00023_UserNo_admin1] [6de1eb311fddd51a/97cc169464f6062e] [http-nio-9009-exec-80] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:24:59.735 [OrganNo_00023_UserNo_admin1] [9690d71050ea8863/5e4baf4b3c0bb29d] [http-nio-9009-exec-60] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:24:59.800 [OrganNo_00023_UserNo_admin1] [9690d71050ea8863/9401b2dce6010109] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:24:59.801 [OrganNo_00023_UserNo_admin1] [9690d71050ea8863/9401b2dce6010109] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:24:59.831 [OrganNo_00023_UserNo_admin1] [9690d71050ea8863/9401b2dce6010109] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:24:59.832 [OrganNo_00023_UserNo_admin1] [9690d71050ea8863/9401b2dce6010109] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:24:59.832 [OrganNo_00023_UserNo_admin1] [9690d71050ea8863/9401b2dce6010109] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:24:59.862 [OrganNo_00023_UserNo_admin1] [9690d71050ea8863/9401b2dce6010109] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:24:59.946 [OrganNo_00023_UserNo_admin1] [9690d71050ea8863/5e4baf4b3c0bb29d] [http-nio-9009-exec-60] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:25:00.025 [OrganNo_00023_UserNo_admin1] [06755e1e3d26589e/0bff020973781c36] [http-nio-9009-exec-61] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-31 11:25:00.048 [OrganNo_00023_UserNo_admin1] [06755e1e3d26589e/4dc809922328791f] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE IN ('23', '24')
2025-03-31 11:25:00.049 [OrganNo_00023_UserNo_admin1] [06755e1e3d26589e/4dc809922328791f] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-03-31 11:25:00.080 [OrganNo_00023_UserNo_admin1] [06755e1e3d26589e/4dc809922328791f] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 11:25:00.080 [OrganNo_00023_UserNo_admin1] [06755e1e3d26589e/4dc809922328791f] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 11:25:00.081 [OrganNo_00023_UserNo_admin1] [06755e1e3d26589e/4dc809922328791f] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-03-31 11:25:00.113 [OrganNo_00023_UserNo_admin1] [06755e1e3d26589e/4dc809922328791f] [http-nio-9009-exec-61] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-03-31 11:25:00.195 [OrganNo_00023_UserNo_admin1] [06755e1e3d26589e/0bff020973781c36] [http-nio-9009-exec-61] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-03-31 11:27:32.926 [OrganNo_00023_UserNo_admin1] [7a32830a82c31fc1/6447e2263d064556] [http-nio-9009-exec-37] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-03-31 11:27:32.994 [OrganNo_00023_UserNo_admin1] [7a32830a82c31fc1/bfcaeb8e7b6ac853] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==>  Preparing: SELECT count(0) FROM FM_CODE
2025-03-31 11:27:32.995 [OrganNo_00023_UserNo_admin1] [7a32830a82c31fc1/bfcaeb8e7b6ac853] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - ==> Parameters: 
2025-03-31 11:27:33.025 [OrganNo_00023_UserNo_admin1] [7a32830a82c31fc1/bfcaeb8e7b6ac853] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig_COUNT - <==      Total: 1
2025-03-31 11:27:33.026 [OrganNo_00023_UserNo_admin1] [7a32830a82c31fc1/bfcaeb8e7b6ac853] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==>  Preparing: select ID "ID", CODE_NO "CODE_NO", CODE_NAME "CODE_NAME", OCR_TYPE "OCR_TYPE", KEEP_YEARS "KEEP_YEARS", IS_DEFAULT "IS_DEFAULT", HEADING "HEADING", PLACEFILE "PLACEFILE" from FM_CODE
2025-03-31 11:27:33.026 [OrganNo_00023_UserNo_admin1] [7a32830a82c31fc1/bfcaeb8e7b6ac853] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - ==> Parameters: 
2025-03-31 11:27:33.055 [OrganNo_00023_UserNo_admin1] [7a32830a82c31fc1/bfcaeb8e7b6ac853] [http-nio-9009-exec-37] DEBUG c.s.a.f.d.C.selectCodeDefByConfig - <==      Total: 2
2025-03-31 11:27:33.133 [OrganNo_00023_UserNo_admin1] [7a32830a82c31fc1/6447e2263d064556] [http-nio-9009-exec-37] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"凭证",
				"codeNo":"0",
				"heading":"凭证",
				"id":"95ce8dc1d66e42d4be784f40bf28f370",
				"isDefault":"1",
				"keepYears":"15",
				"ocrType":"0",
				"placefile":"凭证"
			},
			{
				"codeName":"差错单",
				"codeNo":"1",
				"heading":"差错单",
				"id":"5e0fadddbb614a4bb2a49a3479fc5665",
				"isDefault":"0",
				"keepYears":"30",
				"ocrType":"1",
				"placefile":"差错单"
			}
		],
		"totalCount":2,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-03-31 11:27:33.211 [OrganNo_00023_UserNo_admin1] [6f91548ba76e090a/22dd7de67a3b1cee] [http-nio-9009-exec-86] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1,
		"isCenter":"1",
		"queryType":"1"
	}
}
2025-03-31 11:27:33.232 [OrganNo_00023_UserNo_admin1] [6f91548ba76e090a/d901047c45c929d0] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==>  Preparing: SELECT count(0) FROM FM_APPLICATION WHERE EXISTS (SELECT 1 FROM sm_organ_tb sot, sm_organ_parent_tb sopt WHERE EXISTS (SELECT 1 FROM sm_users_tb sut WHERE sut.user_no = ? AND sot.organ_no = sut.organ_no) AND sot.organ_no = sopt.parent_organ AND sopt.organ_no = SITE_NO) AND APPLICATION_STATE IN ('23', '24')
2025-03-31 11:27:33.233 [OrganNo_00023_UserNo_admin1] [6f91548ba76e090a/d901047c45c929d0] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - ==> Parameters: admin1(String)
2025-03-31 11:27:33.264 [OrganNo_00023_UserNo_admin1] [6f91548ba76e090a/d901047c45c929d0] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications_COUNT - <==      Total: 1
2025-03-31 11:27:33.265 [OrganNo_00023_UserNo_admin1] [6f91548ba76e090a/d901047c45c929d0] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications - ==>  Preparing: select ID "ID",APPLICATION_NO "APPLICATION_NO",BUSINESS_DATE "BUSINESS_DATE",SITE_NO "SITE_NO",SITE_NAME "SITE_NAME", TELLER_NO "TELLER_NO",TELLER_NAME "TELLER_NAME",CODE_NO "CODE_NO",CODE_NAME "CODE_NAME",WARRANT_AMOUNT "WARRANT_AMOUNT", USER_NO "USER_NO",USER_NAME "USER_NAME",REGISTER_DATE "REGISTER_DATE",APPLICATION_STATE "APPLICATION_STATE", PACKAGE_NO "PACKAGE_NO",TRUNK_NO "TRUNK_NO",REMARK "REMARK",MONITOR "MONITOR",DESTROY_DATE "DESTROY_DATE", SCAN_FLAG "SCAN_FLAG",OGAN_NO "OGAN_NO",APP_TYPE "APP_TYPE",FRAMESEQ "FRAMESEQ",APP_INEXNO "APP_INEXNO", OGAN_NAME "OGAN_NAME",END_BUSI_DATE "END_BUSI_DATE",BUSINESS_ID "BUSINESS_ID", ACT_VOUCHER_NUM "ACT_VOUCHER_NUM",IS_DEL "IS_DEL",INPUT_WORKER "INPUT_WORKER",BALE_NO "BALE_NO",BATCHID "BATCHID" from FM_APPLICATION WHERE EXISTS (select 1 from sm_organ_tb sot, sm_organ_parent_tb sopt where exists (select 1 from sm_users_tb sut where sut.user_no = ? and sot.organ_no = sut.organ_no) and sot.organ_no = sopt.parent_organ and sopt.organ_no = SITE_NO ) and APPLICATION_STATE in ('23','24') order by BUSINESS_DATE,SITE_NO,TELLER_NO LIMIT ?
2025-03-31 11:27:33.265 [OrganNo_00023_UserNo_admin1] [6f91548ba76e090a/d901047c45c929d0] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications - ==> Parameters: admin1(String), 15(Integer)
2025-03-31 11:27:33.301 [OrganNo_00023_UserNo_admin1] [6f91548ba76e090a/d901047c45c929d0] [http-nio-9009-exec-86] DEBUG c.s.a.f.d.A.selectFmApplications - <==      Total: 2
2025-03-31 11:27:33.382 [OrganNo_00023_UserNo_admin1] [6f91548ba76e090a/22dd7de67a3b1cee] [http-nio-9009-exec-86] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250314153732058149",
				"applicationState":"FM_STATE_APP_13",
				"businessDate":"20250313",
				"codeName":"凭证",
				"codeNo":"0",
				"destroyDate":"20400313",
				"endBusiDate":"20250313",
				"id":"c18bfe1d77bb416aae4ae2a888d0cb0e",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250314",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"18"
			},
			{
				"actVoucherNum":0,
				"appIndex":"1",
				"appType":"1",
				"applicationNo":"20250320111254186478",
				"applicationState":"FM_STATE_APP_13",
				"batchId":"20250320111254186478",
				"businessDate":"20250320",
				"codeName":"差错单",
				"codeNo":"1",
				"destroyDate":"20550320",
				"endBusiDate":"20250320",
				"id":"36e5bc54292b4b2c929f4f26aa8cba48",
				"isDel":"0",
				"oganName":"00023-中国银行四川省分行",
				"oganNo":"00023",
				"registerDate":"20250320",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"tellerName":"李静",
				"tellerNo":"2966437",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"6"
			}
		],
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
