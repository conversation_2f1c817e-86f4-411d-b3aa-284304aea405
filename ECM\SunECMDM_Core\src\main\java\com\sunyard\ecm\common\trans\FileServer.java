package com.sunyard.ecm.common.trans;

import com.sunyard.exception.SunECMException;

import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

public interface FileServer {
	/**
	 * 接收文件前需要对客户端传送的信息做处理
	 * 
	 * @param map
	 * @param content
	 * @return
	 */
	public String prepareRecieveFile(Map<String, String> map,
			Connection connection) throws SunECMException;

	public String httpPrepareRecieveFile(Map<String, String> map, Connection connection) throws SunECMException;
	/**
	 * 接收文件前需要对客户端传送的信息做处理
	 * 
	 * @param map
	 * @param content
	 * @return
	 */
	public String migratePrepareRecieveFile(Map<String, String> map,
			Connection connection) throws SunECMException;

	public InputStream getFileInputStream(Map<String, String> paramsMap)
			throws IOException, SunECMException;
}