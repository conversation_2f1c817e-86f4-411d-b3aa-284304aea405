package com.sunyard.console.configmanager.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * 分表的索引
 * <AUTHOR>
 */
@XStreamAlias("TableIndex")
public class TableIndexInfo {
	@XStreamAsAttribute
	private String MODEL_CODE;
	@XStreamAsAttribute
	private String ATTRIBUTE_CODE;
	@XStreamAsAttribute
	private String INDEX_NAME;
	@XStreamAsAttribute
	private String VERSION;
	@XStreamAsAttribute
	private String HAS_DELETE;
	@XStreamAsAttribute
	private String INDEX_STATE;
	@XStreamAsAttribute
	private String INDEX_TYPE;
	public String getModel_code() {
		return MODEL_CODE;
	}
	public void setModel_code(String modelCode) {
		MODEL_CODE = modelCode;
	}
	public String getAttribute_code() {
		return ATTRIBUTE_CODE;
	}
	public void setAttribute_code(String attributeCode) {
		ATTRIBUTE_CODE = attributeCode;
	}
	public String getIndex_name() {
		return INDEX_NAME;
	}
	public void setIndex_name(String indexName) {
		INDEX_NAME = indexName;
	}
	public String getVersion() {
		return VERSION;
	}
	public void setVersion(String version) {
		this.VERSION = version;
	}
	public String getHas_delete() {
		return HAS_DELETE;
	}
	public void setHas_delete(String hasDelete) {
		HAS_DELETE = hasDelete;
	}
	public String getIndex_state() {
		return INDEX_STATE;
	}
	public void setIndex_state(String indexState) {
		INDEX_STATE = indexState;
	}
	public String getIndex_type() {
		return INDEX_TYPE;
	}
	public void setIndex_type(String indexType) {
		INDEX_TYPE = indexType;
	}
}
