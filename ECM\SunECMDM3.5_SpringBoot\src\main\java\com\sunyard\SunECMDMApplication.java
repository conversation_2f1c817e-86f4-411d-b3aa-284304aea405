package com.sunyard;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ImportResource;

@SpringBootApplication
@ImportResource(value = {"classpath:conf/applicationContext.xml"})
@ServletComponentScan
public class SunECMDMApplication {
//    public SunECMDMApplication(ServiceLocator serviceLocator){}

    public static void main(String[] args) {
        SpringApplication.run(SunECMDMApplication.class, args);
    }

}
