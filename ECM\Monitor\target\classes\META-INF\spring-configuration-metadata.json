{"groups": [{"name": "monit", "type": "com.sunyard.monitor.util.MonitConfig", "sourceType": "com.sunyard.monitor.util.MonitConfig"}], "properties": [{"name": "monit.cpu", "type": "java.lang.String", "sourceType": "com.sunyard.monitor.util.MonitConfig"}, {"name": "monit.harddrive", "type": "java.lang.String", "sourceType": "com.sunyard.monitor.util.MonitConfig"}, {"name": "monit.ip", "type": "java.lang.String", "sourceType": "com.sunyard.monitor.util.MonitConfig"}, {"name": "monit.linux", "type": "java.lang.String", "sourceType": "com.sunyard.monitor.util.MonitConfig"}, {"name": "monit.mac", "type": "java.lang.String", "sourceType": "com.sunyard.monitor.util.MonitConfig"}, {"name": "monit.memory", "type": "java.lang.String", "sourceType": "com.sunyard.monitor.util.MonitConfig"}, {"name": "monit.netflow", "type": "java.lang.String", "sourceType": "com.sunyard.monitor.util.MonitConfig"}, {"name": "monit.port", "type": "java.lang.String", "sourceType": "com.sunyard.monitor.util.MonitConfig"}], "hints": []}