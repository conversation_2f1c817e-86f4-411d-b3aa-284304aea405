package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * <p>
 * Title: 配置参数bean
 * </p>
 * <p>
 * Description: 配置参数信息
 * </p>
 * <p>
 * Copyright: Copyright (c) 2019
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@XStreamAlias("AllParamMsgBean")
public class AllParamMsgBean {
	@XStreamAsAttribute
	private String PAR_ID;//参数ID
	@XStreamAsAttribute
	private String PAR_KEY;//参数键
	@XStreamAsAttribute
	private String PAR_VAL;//参数值
	@XStreamAsAttribute
	private String PAR_ALL;//全局参数
	@XStreamAsAttribute
	private String PAR_GROUP;//组ID
	@XStreamAsAttribute
	private String PAR_SERVER;//服务器ID
	@XStreamAsAttribute
	private String PAR_MODEL;//索引模型ID
	@XStreamAsAttribute
	private String PAR_STATE;//参数状态
	
	public String getPAR_ID() {
		return PAR_ID;
	}
	public void setPAR_ID(String pAR_ID) {
		PAR_ID = pAR_ID;
	}
	public String getPAR_KEY() {
		return PAR_KEY;
	}
	public void setPAR_KEY(String pAR_KEY) {
		PAR_KEY = pAR_KEY;
	}
	public String getPAR_VAL() {
		return PAR_VAL;
	}
	public void setPAR_VAL(String pAR_VAL) {
		PAR_VAL = pAR_VAL;
	}
	public String getPAR_ALL() {
		return PAR_ALL;
	}
	public void setPAR_ALL(String pAR_ALL) {
		PAR_ALL = pAR_ALL;
	}
	public String getPAR_GROUP() {
		return PAR_GROUP;
	}
	public void setPAR_GROUP(String pAR_GROUP) {
		PAR_GROUP = pAR_GROUP;
	}
	public String getPAR_SERVER() {
		return PAR_SERVER;
	}
	public void setPAR_SERVER(String pAR_SERVER) {
		PAR_SERVER = pAR_SERVER;
	}
	public String getPAR_MODEL() {
		return PAR_MODEL;
	}
	public void setPAR_MODEL(String pAR_MODEL) {
		PAR_MODEL = pAR_MODEL;
	}
	public String getPAR_STATE() {
		return PAR_STATE;
	}
	public void setPAR_STATE(String pAR_STATE) {
		PAR_STATE = pAR_STATE;
	}
	@Override
	public String toString() {
		return "AllParamMsgBean [PAR_ID=" + PAR_ID + ", PAR_KEY=" + PAR_KEY + ", PAR_VAL=" + PAR_VAL + ", PAR_ALL="
				+ PAR_ALL + ", PAR_GROUP=" + PAR_GROUP + ", PAR_SERVER=" + PAR_SERVER + ", PAR_MODEL=" + PAR_MODEL
				+ ", PAR_STATE=" + PAR_STATE + "]";
	}
	
	
	
}
