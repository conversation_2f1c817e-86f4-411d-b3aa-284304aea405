package com.sunyard.ecm.server;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.annotation.WebInitParam;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.common.Configuration;
@WebFilter(urlPatterns = "/servlet/*", filterName = "filter",initParams = {
		@WebInitParam(name="charset",value="UTF-8"),
		@WebInitParam(name="contentType",value="text/html;charset=UTF-8")
})
public class MyCharsetFilter implements Filter {
	private  final static  Logger log = LoggerFactory.getLogger(MyCharsetFilter.class);
	public void init(FilterConfig filterConfig) throws ServletException {
		// TODO Auto-generated method stub
		
	}

	public void doFilter(ServletRequest request1, ServletResponse response1, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest request = (HttpServletRequest) request1;
		HttpServletResponse response = (HttpServletResponse) response1;
		String uri = request.getRequestURI();
		setResponse(request,response);
		if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
			response.setStatus(200);
			return;
		}
		log.info("uri为"+uri);
		if(uri.contains("getFile")){
			if(Configuration.getBoolean("isReferer", false)){
				String referer = request.getHeader("Referer");
				log.info("referer值为"+referer);
				if(referer !=null && !referer.equals("")){
					String whiteList = Configuration.get("whiteList");
					String[] white = whiteList.split(",");
					for(int i = 0; i<white.length; i++){
						if(referer.contains(white[i])){
							log.info("referer验证通过");
							chain.doFilter(request, response);
							break;
						}else{
							if(i==white.length-1){
								errorMsg(request, response,"没有权限访问该地址，来源不正确，不在白名单内");
							}
						}
					}
				} else{
					errorMsg(request, response,"没有权限访问该地址，来源为空");
				}
			}else{
				chain.doFilter(request, response);
			}
		}else{
			chain.doFilter(request, response);
		}
	}
	
	public void setResponse(HttpServletRequest request, HttpServletResponse response) {
		response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));

		response.setHeader("Access-Control-Allow-Methods", "*");
		response.setHeader("Access-Control-Allow-Credentials", "true");
		response.setHeader("Access-Control-Allow-Headers",
				"Accept, Content-Type, Content-Length, Accept-Encoding, X-Token, X-CSRF-Token, Authorization");

	}
	 private void errorMsg(HttpServletRequest request,
				HttpServletResponse response, String msg) {
			response.setContentType("text/plain");
			response.setCharacterEncoding("utf-8");
			response.setStatus(404);
			try {
				StringBuffer url = request.getRequestURL();
				log.error("ulr下载失败:url="+url.toString()+"msg="+msg);
				response.getWriter().write("File DownLoad Error");
			} catch (IOException e) {
				log.error("出错",e);
			}
		}
	public void destroy() {
		// TODO Auto-generated method stub
		
	}


}
