import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function addAttribute(data) {
  const url = '/contentModelManage/addAttributeInfoAction'+EndUrl.EndUrl
  data.optionFlag = "create1";
  data.attributeName = encodeURI(data.attribute_name);
  data.attributeDefault = encodeURI(data.attribute_default)

  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}
export function updateAttribte(data) {
  const url = '/contentModelManage/addAttributeInfoAction'+EndUrl.EndUrl
    data.optionFlag = "update1";
    data.attributeName = encodeURI(data.attribute_name);
    data.attributeDefault = encodeURI(data.attribute_default)
  // console.log(data)
  return request({
    url: url,
    method: 'post',
    params: {
      data: data
    }

  })
}
export function getAttrTypeList() {
  return request({
    url: '/contentModelManage/getAttributeTypeAction'+EndUrl.EndUrl,
    method: 'get'
  })
}
export function getAttrList(data) {
  data.showSystemAttr = 0;
  return request({
    url: '/contentModelManage/attributeInfoAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: data }
  })
}

export function deleteAttr(data) {
  return request({
    url: '/contentModelManage/delAttributeInfoAction'+EndUrl.EndUrl,
    method: 'post',
    params: { attributeCode: data.attribute_code }
  })
}

export function getDesensitiveRuleList() {
  return request({
    url: '/contentModelManage/getDesensitiveRuleAction'+EndUrl.EndUrl,
    method: 'get'
  })
}