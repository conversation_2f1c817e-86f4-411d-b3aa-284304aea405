package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

@XStreamAlias("IndexInfoBean")
public class IndexInfoBean {
	@XStreamAsAttribute
	private String modelCode;
	@XStreamAsAttribute
	private String attributeCode;
	@XStreamAsAttribute
	private String indexName;
	@XStreamAsAttribute
	private String version;
	@XStreamAsAttribute
	private String hasDelete;
	@XStreamAsAttribute
	private String indexState;
	@XStreamAsAttribute
	private String indexType;

	public IndexInfoBean(String modelCode, String attributeCode, String indexName, String version, String hasDelete,
			String indexState, String indexType) {
		this.modelCode = modelCode;
		this.attributeCode = attributeCode;
		this.indexName = indexName;
		this.version = version;
		this.hasDelete = hasDelete;
		this.indexState = indexState;
		this.indexType = indexType;
	}

	public IndexInfoBean() {
	}

	public String getModelCode() {
		return modelCode;
	}

	public void setModelCode(String modelCode) {
		this.modelCode = modelCode;
	}

	public String getAttributeCode() {
		return attributeCode;
	}

	public void setAttributeCode(String attributeCode) {
		this.attributeCode = attributeCode;
	}

	public String getIndexName() {
		return indexName;
	}

	public void setIndexName(String indexName) {
		this.indexName = indexName;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getHasDelete() {
		return hasDelete;
	}

	public void setHasDelete(String hasDelete) {
		this.hasDelete = hasDelete;
	}

	public String getIndexState() {
		return indexState;
	}

	public void setIndexState(String indexState) {
		this.indexState = indexState;
	}

	public String getIndexType() {
		return indexType;
	}

	public void setIndexType(String indexType) {
		this.indexType = indexType;
	}

}
