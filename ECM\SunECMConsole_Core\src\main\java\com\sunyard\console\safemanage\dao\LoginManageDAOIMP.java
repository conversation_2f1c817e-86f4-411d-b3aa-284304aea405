package com.sunyard.console.safemanage.dao;

import com.sunyard.console.common.config.LoadConfigFile;
import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.util.DateUtil;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.safemanage.bean.PermissionInfoBean;
import com.sunyard.console.safemanage.bean.UserInfoBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* <p>Title: 用户登入,登出管理接口类</p>
 * <p>Description: 定义用户登入,登出数据库处理方法</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Repository("lmdao")
public class LoginManageDAOIMP implements LoginManageDAO {
	private  final static  Logger log = LoggerFactory.getLogger(LoginManageDAOIMP.class);
	/**
	 * 用户登录
	 * @param login_id
	 * @param password
	 * @return
	 */
	public UserInfoBean login(String login_id, String password) {
		log.info( "--login(start)-->用户登录，userName:" + login_id + ";password:" + "****************");
		StringBuffer sql = new StringBuffer();
		UserInfoBean user = null;          
		try{
			Object[] parmas = new Object[1];
			parmas[0] =login_id;
			sql.append("SELECT T.* FROM USER_INFO T WHERE USER_STATE =1 AND LOGIN_ID=?");
			log.debug( "--login-->用户登录sql-->" + sql.toString()+","+login_id+"]");
			user = (UserInfoBean) DataBaseUtil.SUNECM.queryBean(sql.toString(), UserInfoBean.class,parmas);
		}catch(Exception e){
			log.error( "安全管理->用户登录失败->",e);
			throw new DBRuntimeException(
					"LoginManageDAOIMP===>login:" + e.toString());
		}
		log.info( "--login(start)-->用户登录,user:"+user);
		return user;
	}
	
	/**
	 * 获取登陆用户权限
	 * @param login_id
	 * @return
	 */
	
	public List<PermissionInfoBean> getMenuAndButtonPermission(String login_id) {
		log.info( "--getMenuAndButtonPermission(start)-->login_id:" + login_id);
		StringBuffer sql = new StringBuffer();
		List<PermissionInfoBean> pers = null;
		try{
			sql.append("select distinct (t.PERMISSION_CODE), t.PERMISSION_NAME, t.PERMISSION_TYPE");
			sql.append(" from SYS_PERMISSION t");
			sql.append(" where t.PERMISSION_CODE in");
			sql.append(" (select t.PERMISSION_CODE");
			sql.append(" from USER_PERMISSION_MAP t");
			sql.append(" where t.LOGIN_ID = '").append(login_id).append("'");
			sql.append(" union");
			sql.append(" select t.PERMISSION_CODE");
			sql.append(" from ROLE_PERMISSION_MAP t,ROLE_INFO ri");
			sql.append(" where t.ROLE_ID in (select t1.ROLE_ID");
			sql.append(" from USER_ROLE_MAP t1");
			sql.append(" where t1.LOGIN_ID = '").append(login_id).append("') and t.ROLE_ID = ri.ROLE_ID and ri.ROLE_STATE = 1) ORDER BY t.PERMISSION_CODE");
			log.debug( "--getMenuAndButtonPermission-->login_id:" + login_id + ";sql:" + sql.toString());
			pers = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), PermissionInfoBean.class);
		}catch(Exception e){
			log.error("安全管理->获取登录用户权限列表失败->"+e.toString());
			throw new DBRuntimeException(
					"LoginManageDAOIMP===>getMenuAndButtonPermission:" + e.toString());
		}
		log.info( "--getMenuAndButtonPermission(over)-->pers:"+pers);
		return pers;
	}
	/**
	 *是否是系统用户:首先校验用户信息，然后校验角色信息
	 */
	public boolean checkSystemUser(String loginId) {

		try {
			//校验用户是否对内容模型有操作权限
			StringBuffer sqlforUser = new StringBuffer("SELECT U.MODEL_CODE ,U.PERMISSION_CODE  FROM USER_CMODEL_REL U   WHERE  U.LOGIN_ID = ")
			.append("'").append(loginId).append("'");
			List<Map<String, String>> permissionCode = DataBaseUtil.SUNECM.queryMapList(sqlforUser.toString());
			for (Map<String, String> map : permissionCode) {
				if(getPermissionByCode(map.get("PERMISSION_CODE"))){
					return false;
				}
			}
			//校验角色中该用户是否拥有模型操作权限
			StringBuffer	 sqlforRole =new StringBuffer("SELECT R.MODEL_CODE  ,R.PERMISSION_CODE  FROM USER_CMODEL_REL U ,ROLE_MODEL_REL R ,USER_ROLE_MAP M ,ROLE_INFO INFO " +
					" WHERE U.LOGIN_ID = M.LOGIN_ID AND M.ROLE_ID = R.ROLE_ID AND R.ROLE_ID = INFO.ROLE_ID AND INFO.ROLE_STATE  = 1 AND U.LOGIN_ID = ")
				.append("'").append(loginId).append("'");
			permissionCode = DataBaseUtil.SUNECM.queryMapList(sqlforRole.toString());
			for (Map<String, String> map : permissionCode) {
				if(getPermissionByCode(map.get("PERMISSION_CODE"))){
					return false;
				}
			}
			
		} catch (Exception e) {
			log.error("校验系统用户出错"+loginId, e);
		}
		return true;
	}
	/**
	 * 根据位数返回值权限的值， * 操作权限  依次为增0、删1、改2、查3、打印（保留位）4、批注5
	 * @param code
	 * @return
	 */
	public boolean getPermissionByCode(String permission_code) {
		char[] permisions = permission_code.toCharArray();
		for (int i = 0; i < 4; i++) {
			if (permisions[i] == '1') {
				return true;
			}
		}
		return false;
	}

	public Map<String, String> sysUserLoginCheck(UserInfoBean user) {
		Map<String, String> map = new HashMap<String, String>();
		map.put("result", "success");
		if (user == null) {
			return map;
		}
		int max_login_day = LoadConfigFile.getConfigBean().getLogout_max_day();// 必须修改天数
		int logout_remind_day = LoadConfigFile.getConfigBean().getLogout_remind_day();// 提醒天数
		if (max_login_day == 0 && logout_remind_day == 0) {
			// 密码过期时间为0，表示密码永远有效
			return map;

		} else {
			// 判断用户有没有业务模型操作权限，如果没有权限（是系统用户）
			// ，则对用户的密码有过期要求，max_login_day为密码修改最长时间
			if (!checkSystemUser(user.getLogin_id())) {
				// 不是系统用户
				return map;
			} else {
				// 属于系统用户，则需要判断用户密码是否过期
				try {
					// 最新的密码修改时间
					String modifyDate = user.getPsw_mdf_date();
					String errorMSG = "";
					// 密码过期提醒时间
					SimpleDateFormat sm = new SimpleDateFormat("yyyyMMddHHmm");
					String now = sm.format(new Date());

					// 当前时间-修改时间=差的天数
					Map<String, Long> day_map = DateUtil.dateDiff(modifyDate,
							now, "yyyyMMddHHmm");
					long day = day_map.get("day");
					long hour = day_map.get("hour");
					long min = day_map.get("min");

					// now-modify=modify_now_day,这个天数如果大于最长时间，则对用户进行注销操作
					if (day >= max_login_day) {
						errorMSG = "密码超过【" + day + "天" + hour + "小时" + min
								+ "分钟】未修改,请修改密码!";
						log.warn(errorMSG);
						map.put("result", "false");
						map.put("msg", errorMSG);
						return map;
					}
					// end-当前时间=剩余提醒天数
					day_map = DateUtil.dateDiff(now, DateUtil.getMDrqzhsti12(
							modifyDate, max_login_day), "yyyyMMddHHmm");
					day = day_map.get("day");
					hour = day_map.get("hour");
					min = day_map.get("min");

					if (day < logout_remind_day) {
						// 需要对用户密码过期提醒
						errorMSG = "密码将于【" + day + "天" + hour + "小时" + min
								+ "分钟】后过期，请及时修改!";
						log.info(errorMSG);
						map.put("result", "success");
						map.put("msg", errorMSG);
						return map;
					}
				} catch (ParseException e) {
					log.error("", e);
				}
			}
		}
		return map;
	}
//	public static void main(String[] args) {
//		while (true) {
//
//			// 属于系统用户，则需要判断用户密码是否过期
//			try {
//				try {
//					Thread.sleep(15*1000);
//				} catch (InterruptedException e) {
//					log.error(e);
//				}
//				// 最新的密码修改时间
//				String modifyDate ="201504141042";
//				String errorMSG = "";
//				// 密码过期提醒时间
//				SimpleDateFormat sm = new SimpleDateFormat("yyyyMMddHHmm");
//				String now = sm.format(new Date());
//
//				// 当前时间-修改时间=差的天数
//				Map<String, Long> day_map = DateUtil.dateDiff(modifyDate,
//						now, "yyyyMMddHHmm");
//				long day = day_map.get("day");
//				long hour = day_map.get("hour");
//				long min = day_map.get("min");
//				log.info(  "{{"+day + "天" + hour + "小时" + min
//						+ "分钟】");
//				// now-modify=modify_now_day,这个天数如果大于最长时间，则对用户进行注销操作
//				if (day >= 30) {
//					errorMSG = "密码超过【" + day + "天" + hour + "小时" + min
//							+ "分钟】未修改,请修改密码!";
//					log.error(errorMSG);
//					continue;
//				}
//				// end-当前时间=剩余提醒天数
//				day_map = DateUtil.dateDiff(now, DateUtil.getMDrqzhsti12(
//						modifyDate, 30), "yyyyMMddHHmm");
//				day = day_map.get("day");
//				hour = day_map.get("hour");
//				min = day_map.get("min");
//				log.info(  day + "天" + hour + "小时" + min
//							+ "分钟】");
//				if (day < 10) {
//					// 需要对用户密码过期提醒
//					errorMSG = "密码将于【" + day + "天" + hour + "小时" + min
//							+ "分钟】后过期，请及时修改!";
//					log.error(errorMSG);
//				}
//			} catch (ParseException e) {
//				log.error("", e);
//			}
//		
//		}}
}
