import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getCachingStrategyList(data) {
  return request({
    url: '/lifeManage/getScheduleListVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: data }
  })
}

export function addCaching(data) {
  const url = '/lifeManage/addCachingVueAction'+EndUrl.EndUrl;
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}


export function updateCaching(data) {
  const url = '/lifeManage/updateCachingVueAction'+EndUrl.EndUrl;
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function stopCaching(data) {
  return request({
    url: '/lifeManage/stopCachingVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'task_ids': data.task_id
    }
  })
}

export function startCaching(data) {
  return request({
    url: '/lifeManage/startCachingVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'task_ids': data.task_id
    }
  })
}

export function getUnbindTask(data) {
  return request({
    url: '/lifeManage/getUnbindTaskListVueAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: data }
  })
}
