import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getContentServerGroup() {
  const url = '/monitorManage/getAllGroupsAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post'
  })
}

export function getRelServers(data) {
  const url = '/monitorManage/getRelServersAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getRelContentObject(data) {
  const url = '/monitorManage/getRelContentsAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getServerInfo(data) {
  const url = '/monitorManage/getServerInfoAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getServer(data) {
  const url = '/monitorManage/getServerAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getNetFlow(data) {
  const url = '/monitorManage/getNetFlowAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getInitAction(data) {
  const url = '/monitorManage/getInitAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getRealTimeData(data) {
  const url = '/monitorManage/getRealTimeDataAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getSocketConnNum(data) {
  const url = '/monitorManage/getSocketConnNumAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getSchedulerNumofMonth(data) {
  const url = '/monitorManage/getSchedulerNumofMonthAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getTCPSocketStatus(data) {
  const url = '/monitorManage/getTCPSocketStatusAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getTaskCountNum(data) {
  const url = '/monitorManage/getTaskCountNumAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getSocketPoolStatus(data) {
  const url = '/monitorManage/getSocketPoolStatusAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getMigrateThreadPool(data) {
  const url = '/monitorManage/getMigrateThreadPoolAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getMigrateBlockQueue(data) {
  const url = '/monitorManage/getMigrateBlockQueueAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getTaskAction(data) {
  let option = data.option;
  let url;
  if(option == 'UPLOAD'){
    url =  '/monitorManage/getUploadFileNumAction'+EndUrl.EndUrl
  }else if(option == 'UPDATE'){
    url =  '/monitorManage/getUpDateFileNumAction'+EndUrl.EndUrl
  }else if(option == 'HEIGQUERY'){
    url =  '/monitorManage/getHeighQFileNumAction'+EndUrl.EndUrl
  }else if(option == 'QUERY'){
    url =  '/monitorManage/getQueryFileNumAction'+EndUrl.EndUrl
  }else if(option == 'GETFILE'){
    url =  '/monitorManage/getDownloadFileNumAction'+EndUrl.EndUrl
  }else if(option == 'MIGRATE'){
    url =  '/monitorManage/getMigrateFileNumAction'+EndUrl.EndUrl
  }
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getTaskDayAction(data) {
  const url = '/monitorManage/getTaskOfDayAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getTimeAction(data) {
  let option = data.option;
  let url;
  if(option == 'UPLOAD'){
    url =  '/monitorManage/getUploadTimeCostAction'+EndUrl.EndUrl
  }else if(option == 'UPDATE'){
    url =  '/monitorManage/getUpdateTimeCostAction'+EndUrl.EndUrl
  }else if(option == 'HEIGQUERY'){
    url =  '/monitorManage/getHeighQTimeCostAction'+EndUrl.EndUrl
  }else if(option == 'QUERY'){
    url =  '/monitorManage/getQueryTimeCostAction'+EndUrl.EndUrl
  }else if(option == 'GETFILE'){
    url =  '/monitorManage/getDownloadTimeCostAction'+EndUrl.EndUrl
  }else if(option == 'MIGRATE'){
    url =  '/monitorManage/getMigrateTimeCostAction'+EndUrl.EndUrl
  }
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getCountFileAction(data) {
  let option = data.option;
  let url;
  if(option == 'UPLOAD'){
    url =  '/monitorManage/getUploadFileAction'+EndUrl.EndUrl
  }else if(option == 'UPDATE'){
    url =  '/monitorManage/getUpdateFileAction'+EndUrl.EndUrl
  }
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getCountFileSizeAction(data) {
  let option = data.option;
  let url;
  if(option == 'UPLOAD'){
    url =  '/monitorManage/getUploadFileSizeAction'+EndUrl.EndUrl
  }else if(option == 'UPDATE'){
    url =  '/monitorManage/getUpdateFileSizeAction'+EndUrl.EndUrl
  }
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}
