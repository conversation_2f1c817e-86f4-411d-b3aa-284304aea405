<!--表单 弹出框 表单+按钮-->
<!-- dialog begin  -->
<div class="dialog-wrap ars-dialog-form-title">
    <div class="top-title list-contTitle" v-html="dialog.dialog_title">
        <!-- {{ dialog.dialog_title }} -->
    </div>
    <!--列表区 begin-->
    <div class="dialog-content padding-all" style="top:30px">
        <form :id="dialog.form_id" class="ars-dialog-form" :class="dialog.form_class">
            <!--form 表单 begin-->
            <component-form v-for="item in dialog.form_data" v-bind:key="item.id" v-bind:message="item">
            </component-form>
            <!--form 表单 end-->
        </form>
    </div>
    <!--列表区 end-->

    <!--按钮 begin-->
    <div class="dialog-footer cont-btn" v-if="info==false">
        <component-button v-for="item in dialog.buttons" v-bind:key="item.id" v-bind:message="item"></component-button>
    </div>
    <div class="dialog-footer cont-btn" v-else>
        <div class="footer-right" style="padding:0 5px;" v-html="info">
        </div>
        <div class="footer-left">
            <component-button v-for="item in dialog.buttons" v-bind:key="item.id" v-bind:message="item">
            </component-button>
        </div>
    </div>
    <!--按钮 end-->
</div>
<!-- dialog end  -->

<script>
    new Vue({
        el: ".ars-dialog-form-title",//页面整体 ID
        data: {
            'dialog': common_dialog.dialog_form_title,
            'info': commonBlank(common_dialog.dialog_form_title.info) ? false : common_dialog.dialog_form_title.info,
        },
        methods: {
            /**
             * 将字符串转换为函数
             * @param item:函数字符串*/
            callFn: function (item) {
                if (item === 'dialogClose()') {
                    // 优先调用common_dialog中的dialogClose方法，避免误关父弹框
                    if (common_dialog && typeof common_dialog.dialogClose === 'function') {
                        common_dialog.dialogClose();
                    } else {
                        // 如果common_dialog中没有dialogClose方法，则调用本地方法
                        var reg1 = /^\w+/g;
                        var reg2 = /\(((.|)+?)\)/; //取小括号中的参数
                        var fn = item.match(reg1)[0];
                        var args = item.match(reg2)[1];
                        if (commonBlank(args)) { //函数无参数
                            this[fn].apply(this); //调用函数
                        } else { //函数有参数
                            this[fn].apply(this, args.split(',')); //调用函数
                        }
                    }
                } else {
                    if (!commonBlank(this.dialog.callFn)) {
                        this.dialog.callFn(item);//则执行common_dialog中的相应函数
                    } else {
                        common_dialog.callFn(item);//则执行common_dialog中的相应函数
                    }
                }
            },
            /**
             * 弹出框：关闭*/
            dialogClose: function () {
                BJUI.dialog('closeCurrent', '');//关闭当前弹出框
            }
        }
    });
</script>