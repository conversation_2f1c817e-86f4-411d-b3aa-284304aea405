<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.XxlJobGroupDao">
	
	<resultMap id="XxlJobGroup" type="com.xxl.job.admin.core.model.XxlJobGroup" >
		<result column="id" property="id" />
	    <result column="app_name" property="appName" />
	    <result column="title" property="title" />
	    <result column="order_" property="order" />
		<result column="address_type" property="addressType" />
		<result column="address_list" property="addressList" />
	</resultMap>

	<sql id="Base_Column_List">
		t.id,
		t.app_name,
		t.title,
		t.order_,
		t.address_type,
		t.address_list
	</sql>

	<select id="findAll" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_GROUP t
		order BY t.order_ ASC
	</select>

	<select id="findByAddressType" parameterType="java.lang.Integer" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_GROUP t
		WHERE t.address_type = #{addressType}
		order BY t.order_ ASC
	</select>

	<select id="findById" parameterType="java.lang.Integer" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_GROUP t
		WHERE t.id = #{id}
		order BY t.id ASC
	</select>



	<insert id="save" parameterType="com.xxl.job.admin.core.model.XxlJobGroup" useGeneratedKeys="false" keyProperty="id" >
		INSERT INTO QRTZ_TRIGGER_GROUP ( app_name, title, order_, address_type, address_list)
		values ( #{appName,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{order}, #{addressType,jdbcType=VARCHAR}, #{addressList,jdbcType=VARCHAR})
	</insert>

	<update id="update" parameterType="com.xxl.job.admin.core.model.XxlJobGroup" >
		UPDATE QRTZ_TRIGGER_GROUP
		SET app_name = #{appName},
			title = #{title,},
			order_ = #{order},
			address_type = #{addressType},
			address_list = #{addressList,jdbcType=VARCHAR}
		WHERE id = #{id}
	</update>

	<delete id="remove" parameterType="java.lang.Integer" >
		DELETE FROM QRTZ_TRIGGER_GROUP
		WHERE id = #{id}
	</delete>

	<select id="load" parameterType="java.lang.Integer" resultMap="XxlJobGroup">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_TRIGGER_GROUP t
		WHERE t.id = #{id}
	</select>

</mapper>