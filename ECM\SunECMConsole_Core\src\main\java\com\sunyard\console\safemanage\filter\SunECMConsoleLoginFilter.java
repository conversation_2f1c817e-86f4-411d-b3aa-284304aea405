package com.sunyard.console.safemanage.filter;

import com.sunyard.console.common.config.ReadConfig;
import com.sunyard.console.common.util.RedisUtil;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.monitor.monitorCenter.singleton.LazySingleton;
import com.sunyard.console.safemanage.bean.UserInfoBean;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

/**
 * 自定义过滤器
 *
 * <AUTHOR>
 */
@WebFilter(urlPatterns = "*.action", filterName = "LoginFilter")
public class SunECMConsoleLoginFilter implements Filter {
	final static Logger log = Logger.getLogger(SunECMConsoleLoginFilter.class);

	public void destroy() {

	}

	private void outJsonString(String str, HttpServletResponse response) {
		response.setContentType("text/javascript;charset=UTF-8");
		try {
			PrintWriter out = response.getWriter();
			out.write(str);
			out.flush();
		} catch (IOException e) {
			log.error("error", e);
		}
	}

	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest request = (HttpServletRequest) req;
		HttpServletResponse response = (HttpServletResponse) res;
		String uri = request.getRequestURI();

//		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));

		response.setHeader("Access-Control-Allow-Methods", "*");
		response.setHeader("Access-Control-Allow-Credentials", "true");
		response.setHeader("Access-Control-Allow-Headers",
				"Accept, Content-Type, Content-Length, Accept-Encoding, X-Token, X-CSRF-Token, Authorization");

		try {
			if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
				response.setStatus(200);
				return;
			}

			log.debug("访问URL权限过滤,URI=" + uri);
			String token = request.getHeader("X-Token");
			// 未登陆直接退出
			if (uri.contains("/safeManage/loginAction.action") || uri.contains("/safeManage/loginOutAction.action")) {
				try {
					chain.doFilter(req, res);
				} catch (IllegalStateException e) {
					log.error(e.toString());
				}
			} else {
				SessionManage sm = new SessionManage();
				UserInfoBean user = sm.getUserSession(token, request);
				if (user==null|| StringUtil.stringIsNull(user.getLogin_id())) {
					JSONObject jsonResp = new JSONObject();
					jsonResp.put("code", 50008);// TODO mock
					outJsonString(jsonResp.toString(), response);
					return;

				} else {
					try {
						chain.doFilter(req, res);
					} catch (IllegalStateException e) {
						log.error(e.toString());
					}

				}

			}
		} catch (Exception e) {
			log.error("", e);
			// TODO: handle exception
		}
	}
//	public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain)
//			throws IOException, ServletException {
//		HttpServletRequest request = (HttpServletRequest) req;
//		HttpServletResponse response = (HttpServletResponse) res;
//		String uri = request.getRequestURI();
//
////		response.setHeader("Access-Control-Allow-Origin", "*");
//	    response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
//
//		response.setHeader("Access-Control-Allow-Methods", "*");
//		response.setHeader("Access-Control-Allow-Credentials", "true");
//		response.setHeader("Access-Control-Allow-Headers",
//				"Accept, Content-Type, Content-Length, Accept-Encoding, X-Token, X-CSRF-Token, Authorization");
//
//		if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
//			response.setStatus(200);
//			return;
//		}
//
//		log.debug("访问URL权限过滤,URI=" + uri);
//		String token = request.getHeader("X-Token");
//		// 未登陆直接退出
//		if (uri.contains("/safeManage/loginAction.action") || uri.contains("/safeManage/loginOutAction.action")) {
//			try {
//				chain.doFilter(req, res);
//			} catch (IllegalStateException e) {
//				log.error(e.toString());
//			}
//		} else {
//			
//
//			 HttpSession	session=request.getSession();
//			log.debug("当前SESSION ID是[" + session.getId() + "]创建时间是[" + session.getCreationTime() +"]");
//			UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//			if(user!=null) {
//				chain.doFilter(req, res);
//
//			}else {
//
//				JSONObject jsonResp = new JSONObject();
//				jsonResp.put("code", 50008);// TODO mock
//				PrintWriter out = response.getWriter();
//				out.write(jsonResp.toString());
//				out.flush();
//				return;
//			
//			}
//			if (!checkTokenFromRedis(token)) {
//				JSONObject jsonResp = new JSONObject();
//				jsonResp.put("code", 50008);// TODO mock
//				PrintWriter out = response.getWriter();
//				out.write(jsonResp.toString());
//				out.flush();
//				return;
//			} else {
//				try {
//					chain.doFilter(req, res);
//				} catch (IllegalStateException e) {
//					log.error(e.toString());
//				}
//			}
//
//		}
//	}

	public void init(FilterConfig arg0) throws ServletException {

	}

	/*
	 * token : 用户的唯一标识 根据token操作redis数据
	 */
	/**
	 * 
	 * @param token
	 * @return true表示有，false表示沒有token
	 */
	public static boolean checkTokenFromRedis(String token) {
		if (StringUtil.stringIsNull(token)) {
			log.error("no token");
			return false;
		}
		log.info("checkredis[" + token + "]");
		RedisUtil rediUtil = new RedisUtil();
		Map<String, String> map = rediUtil.getHashMap(token);
		if (map == null || map.size() == 0) {
			log.info("no token");
			return false;
		} else {
			log.info("剩余时间:" + rediUtil.getRedisttl(token));
			rediUtil.setRedisTimeout(token, ReadConfig.getConsoleConfigBean().getMaxLoginSessionInterval());
			return true;
		}
	}

//	public boolean checkPermission(HttpServletRequest request, HttpServletResponse response) {
//		String jsonStr = null;
//		String uri = request.getRequestURI();
//		String option = getOption(uri);
//		HttpSession session = request.getSession();
//		Map persMap = (Map) session.getAttribute("menuAndButtonPermissions");
//		log.debug("option:" + option);
//		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//		if (user == null) {
//			log.error("没有用户");
//			JSONObject jsonResp = new JSONObject();
//			jsonResp.put("success", "false");
//			jsonResp.put("message", "没有用户");
//			jsonStr = jsonResp.toString();
//			this.outJsonString(jsonStr, response);
//			return false;
//		} else if (persMap == null || persMap.size() == 0
//				|| !StringUtil.stringIsNull(option) && !persMap.containsKey(option)) {
//			log.error("user=" + user.getLogin_id() + "]没有权限" + option + "]");
//			JSONObject jsonResp = new JSONObject();
//			jsonResp.put("success", "false");
//			jsonResp.put("message", "没有权限");
//			jsonStr = jsonResp.toString();
//			this.outJsonString(jsonStr, response);
//			return false;
//		}
//		return true;
//	}

	public String getOption(String uri) {
		String[] urls = uri.split("/");
		String option;
		if (urls != null && urls.length > 0) {
			option = urls[urls.length - 1];
			log.debug("option=" + option);
			Map<String, String> permissionMap = LazySingleton.getUsePermission();
			if (permissionMap != null) {
				return permissionMap.get(option);
			}
		}
		return null;
	}

//	public void setSessionTime(HttpSession session) {
//		Map<String, UserInfoBean> userSessionMap = LazySingleton.getUserSessionMap();
//		UserInfoBean user = (UserInfoBean) session.getAttribute("loginUser");
//		if (user != null) {
//			String loginId = user.getLogin_id();
//			UserInfoBean sessionUser = userSessionMap.get(loginId);
//			if (sessionUser != null) {
//				String last = sessionUser.getPsw_mdf_date();
//				log.debug("loginId:" + loginId + ",lastTime=" + last);
//				try {
//					last = DateUtil.getMDrqzhsti14(last,
//							ReadConfig.getConsoleConfigBean().getMaxLoginSessionInterval());
//				} catch (ParseException e1) {
//					log.error("" + last, e1);
//					last = DateUtil.getMDrqzhsti14();
//				}
//				log.debug("loginout time:" + last);
//				boolean flag = DateUtil.compareTime(last, DateUtil.getMDrqzhsti14());
//				if (flag) {
//					// 表示该用户已经失效
//					log.info("remove loginId:" + loginId + ",lastTime=" + last);
//					userSessionMap.remove(loginId);
//				} else {
//					log.debug("reset lastTime");
//					sessionUser.setPsw_mdf_date(DateUtil.getMDrqzhsti14());
//				}
//			}
//		}
//	}

//	public boolean IsReLogin(HttpSession session) {
//		UserInfoBean userBean = (UserInfoBean) session.getAttribute("loginUser");
//		if (userBean == null) {
//			return false;
//		}
//		String loginId = userBean.getLogin_id();
//		String udep = userBean.getUser_department();
//
//		Map<String, UserInfoBean> userSessionMap = LazySingleton.getUserSessionMap();
//		UserInfoBean sessionUser = userSessionMap.get(loginId);
//		if (sessionUser == null) {
//			return false;
//		}
//		if (!udep.equals(sessionUser.getUser_department())) {
//			log.error("loginid=" + loginId + ",udep:" + udep + ",用户已经在其他地方登录!");
//			session.removeAttribute("loginUser");
//			session.removeAttribute("loginDateTime");
//			session.removeAttribute("menuAndButtonPermissions");
//			session.removeAttribute("passwordNeedModify");
//			return true;
//		}
//		return false;
//
//	}
}
