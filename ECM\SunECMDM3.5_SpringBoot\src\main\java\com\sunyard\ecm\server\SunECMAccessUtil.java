package com.sunyard.ecm.server;

import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.bean.SQLBean;
import com.sunyard.ecm.server.dao.SunECMDao;
import com.sunyard.exception.SunECMException;
import com.sunyard.util.SpringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

public class SunECMAccessUtil {
	final static Logger log = LoggerFactory.getLogger(SunECMAccessUtil.class);
	private SunECMDao ecmDao = (SunECMDao) SpringUtil.getSpringBean("sunECMDao");

	public void getBusinessStartDate() {

	}

	/**
	 * * 带版本控制查询文件SQL：--- SELECT A.* FROM ( SELECT * FROM PART_V_1 WHERE
	 * CONTENT_ID = '201701_11_DFA87BF6-D99B-EF6E-B9DF-9F8A17491AA2-21' AND
	 * GROUP_ID = '1' AND VERSION <= 3 AND CONTENT_STATUS=1 ) A INNER JOIN (
	 * SELECT MAX(VERSION) VERSION, FILE_NO FROM ( SELECT * FROM PART_V_1 WHERE
	 * CONTENT_ID = '201701_11_DFA87BF6-D99B-EF6E-B9DF-9F8A17491AA2-21' AND
	 * GROUP_ID = '1' AND VERSION <= 3 )X GROUP BY FILE_NO) B ON A.VERSION =
	 * B.VERSION AND A.FILE_NO = B.FILE_NO ----
	 * 
	 * @param contentId
	 * @param version
	 * @param groupId
	 * @param tableName
	 * @return
	 */
	public List<Map<String, String>> getFile_V(String contentId, String version, String groupId, String tableName) {
		log.debug("生成查询版本控制文档信息SQL语句");
		StringBuffer sql = new StringBuffer();
		StringBuffer subQueries = new StringBuffer();
		subQueries.append("SELECT * FROM ").append(tableName).append(" WHERE CONTENT_ID = '").append(contentId).append("' AND GROUP_ID = '").append(groupId).append("' AND VERSION <= ")
				.append(version).append(" ");
		StringBuffer filtersql = new StringBuffer();
		sql.append("SELECT A.SAVE_NAME,A.FILE_PATH,A.OPTION_TYPE,A.VOLUME_ID,A.CONTENT_STATUS,A.FILE_NO,A.VERSION FROM ( ").append(subQueries.toString()).append(filtersql.toString())
				.append(" AND CONTENT_STATUS=1 ) A INNER JOIN ( SELECT MAX(VERSION) VERSION, FILE_NO FROM ( ").append(subQueries.toString())
				.append(" )X GROUP BY FILE_NO) B ON A.VERSION = B.VERSION AND A.FILE_NO = B.FILE_NO");

		log.debug("SQL : " + sql.toString());
		return ecmDao.searchSql(sql.toString(), null);
	}

	/**
	 * 查询无版本控制文件信息 * 无版本控制查询文件 sql :-----SELECT A.* FROM ( SELECT * FROM
	 * PART_V_1 WHERE CONTENT_ID =
	 * '201701_11_DFA87BF6-D99B-EF6E-B9DF-9F8A17491AA2-21' AND GROUP_ID = '1'
	 * AND CONTENT_STATUS=1 ) A INNER JOIN ( SELECT MAX(VERSION) VERSION,
	 * FILE_NO FROM (SELECT * FROM PART_V_1 WHERE CONTENT_ID =
	 * '201701_11_DFA87BF6-D99B-EF6E-B9DF-9F8A17491AA2-21' AND GROUP_ID = '1' )X
	 * GROUP BY FILE_NO) B ON A.VERSION = B.VERSION AND A.FILE_NO =
	 * B.FILE_NO------
	 * 
	 * @param batchBean
	 *            查询条件
	 * @return
	 * @throws SunECMException
	 */
	public List<Map<String, String>> getFile_NV(String contentId, String version, String groupId, String tableName) throws SunECMException {
		log.debug("生成查询无版本控制文档SQL");
		StringBuffer sql = new StringBuffer();
		StringBuffer subQueries = new StringBuffer();
		subQueries.append("SELECT * FROM ").append(tableName).append(" WHERE CONTENT_ID = '").append(contentId).append("' AND GROUP_ID = '").append(groupId).append("' ");
		StringBuffer filtersql = new StringBuffer();
		sql.append("SELECT A.SAVE_NAME,A.FILE_PATH,A.OPTION_TYPE,A.VOLUME_ID,A.CONTENT_STATUS,A.FILE_NO ,A.VERSION FROM ( ").append(subQueries.toString()).append(filtersql.toString())
				.append(" AND CONTENT_STATUS=1 ) A INNER JOIN ( SELECT MAX(VERSION) VERSION, FILE_NO FROM ( ").append(subQueries.toString())
				.append(" )X GROUP BY FILE_NO) B ON A.VERSION = B.VERSION AND A.FILE_NO = B.FILE_NO");

		// 去掉服务器的选择
		log.debug("SQL : " + sql.toString());
		return ecmDao.searchSql(sql.toString(), null);
	}

	/**
	 * 组装删除指定文件的SQL DELETE FROM doc_v_1 where FILE_NO='' AND GROUP_ID='' AND
	 * VERSION=''
	 */
	public void createDelFileNotExistSQL(FileBean fileBean, String tableName, List<SQLBean> sqlList) {
		log.debug("create SQL");
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM ").append(tableName).append(" WHERE FILE_NO=? AND GROUP_ID=? AND VERSION=? ");
		Object[] objects = new Object[3];
		objects[0] = fileBean.getFileNO();
		objects[1] = fileBean.getGroupID();
		objects[2] = fileBean.getVersion();
		SQLBean indexsqlbean = new SQLBean();
		indexsqlbean.setParams(objects);
		indexsqlbean.setSql(sql.toString());
		sqlList.add(indexsqlbean);
		log.debug("SQL::[" + sql + "]parameters[" + objects[0] + "," + objects[1] + "," + objects[2] + "]");
	}
	/**
	 * 组装删除指定文件的SQL DELETE FROM MIGRATE_ERROR_DETAIL WHERE CONTENT_ID=? AND GROUP_ID=? AND VERSION=? AND FILE_TABLE_NAME=? 
	 * VERSION=''
	 */
	public void createDelMigrateErrorDetailSQL(FileBean fileBean, String fileTableName, List<SQLBean> sqlList) {
		log.debug("create SQL");
		StringBuffer sql = new StringBuffer();
		sql.append("DELETE FROM MIGRATE_ERROR_DETAIL WHERE CONTENT_ID=? AND GROUP_ID=? AND VERSION=? AND FILE_TABLE_NAME=? ");
		Object[] objects = new Object[4];
		objects[0] = fileBean.getContentID();
		objects[1] = fileBean.getGroupID();
		objects[2] = fileBean.getVersion();
		objects[3] = fileTableName;
		SQLBean indexsqlbean = new SQLBean();
		indexsqlbean.setParams(objects);
		indexsqlbean.setSql(sql.toString());
		sqlList.add(indexsqlbean);
		log.debug("SQL::[" + sql + "]parameters[" + objects[0] + "," + objects[1] + "," + objects[2] +objects[3]+ "]");
	}

	/**
	 * 执行数据库
	 * 
	 * @param sqlList
	 */
	public void executeDBUpdate(List<SQLBean> sqlList) {
		log.info("begin to execute SQL::{}", sqlList);
		this.ecmDao.updateSqlBeans(sqlList);
	}
}
