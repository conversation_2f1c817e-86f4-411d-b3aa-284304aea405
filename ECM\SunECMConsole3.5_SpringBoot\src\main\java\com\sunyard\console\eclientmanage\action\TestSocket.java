package com.sunyard.console.eclientmanage.action;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TestSocket {
	private  final static  Logger log = LoggerFactory.getLogger(TestSocket.class);

//	public static void main(String[] args) {
//		String model_code = "doc,part";
//
//		log.info("要导出的模型code[" + model_code + "]");
//		if (StringUtil.stringIsNull(model_code)) {
//		}
//		String[] modelCodes = model_code.split(",");
//
//		List<String> modelCodeList = new ArrayList<String>();
//
//		Collections.addAll(modelCodeList, modelCodes);
//		System.out.println(modelCodeList);
//	}

	public static void a() {

		String msg = "SAVE_TIME=0;SUPER_GROUPID=144;";
		String t = msg.substring(msg.indexOf("SUPER_GROUPID"));
		String e;
		if (t.indexOf(";") == -1) {
			e = t.substring(t.indexOf("=") + 1);
		} else {
			e = t.substring(t.indexOf("=") + 1, t.indexOf(";"));
		}

	}
}
