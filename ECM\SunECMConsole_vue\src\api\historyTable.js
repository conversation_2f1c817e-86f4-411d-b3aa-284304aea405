import request from '@/utils/request'
import EndUrl from './publicUrl.js'


export function getHistoryList(data) {
  const url = '/contentModelManage/getHistoryTableAction'+EndUrl.EndUrl
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getObjectName() {
  return request({
    url: '/contentModelManage/getObjectNameAction'+EndUrl.EndUrl,
    method: 'get',
    params: {'model_code':''}
  })
}

export function addHistory(data) {
  const url = '/contentModelManage/addHistoryTableAction'+EndUrl.EndUrl
  data.model_code = encodeURI(data.model_code);
  data.end_time = encodeURI(data.end_time);
  data.begin_time = encodeURI(data.begin_time)
  return request({
    url: url,
    method: 'post',
    params: { data: data }
  })
}

export function getEndTime(data) {
  return request({
    url : '/contentModelManage/getObjTableEndTimeAction'+EndUrl.EndUrl,
    method: 'post',
    params: { 'model_code': data.model_code}
  })
}

