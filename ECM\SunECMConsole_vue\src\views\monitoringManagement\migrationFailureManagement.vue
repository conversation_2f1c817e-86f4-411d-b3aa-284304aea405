<template>
  <div class="app-container">
    <div>
      <label-wrap>批次号:</label-wrap>
      <el-input
        v-model="listQuery.content_id"
        placeholder="批次号"
        style="width: 200px"
      />
      <label-wrap>  模型代码:</label-wrap>
      <el-input
        v-model="listQuery.model_code"
        placeholder="模型代码"
        style="width: 200px"
      />
      <label-wrap>  失败时间:</label-wrap>
      <el-date-picker v-model="listQuery.fail_time" type="date" placeholder="失败时间"  value-format="yyyy-MM-dd"  style="width: 200px"></el-date-picker>
    </div>
    <div align="center" style="margin-top: 30px;margin-bottom: 30px">
      <el-button v-if="this.hasPerm('queryFailMigrate')" type="primary"
        icon="el-icon-search"
        size="mini"
        plain
        round  @click="handleFilter">查询</el-button>
      <el-button type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain
        round  @click="reset">重置</el-button>
    </div>


    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="批次号" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.content_id }}</span>
        </template>
      </el-table-column>

      <el-table-column label="模型代码" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.model_code }}</span>
        </template>
      </el-table-column>
      <el-table-column label="版本号" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.version}}</span>
        </template>
      </el-table-column>
      <el-table-column label="迁移失败时间" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.last_time}}</span>
        </template>
      </el-table-column>
      <el-table-column label="失败信息" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.error_case}}</span>
        </template>
      </el-table-column>
      <el-table-column label="批次表名" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.table_name}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="200"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="{ row }">
          <el-button v-if="hasPerm('reMigrate')" type="primary" size="mini" @click="migrate(row)">
            重新迁移
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
  import Pagination from '@/components/Pagination'
  import {getMigrateFailList,reMigrateFaileBatch,getNoFileMigrateFailList} from "@/api/migrateFail";



  export default {
    components: { Pagination},
    data() {
      return {
        tableKey: 0,
        list: null,
        total: 0,
        listLoading: true,
        listQuery: {
          start:0,
          page: 1,
          limit: 20,
          content_id: '',
          model_code: '',
          fail_time: ''
        },
      }
    },
    created() {
      this.getList()
    },
    methods: {
      getList() {
        this.listLoading = true
        getMigrateFailList(this.listQuery).then(response => {
          this.list = response.root
          this.total = Number(response.totalProperty)
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
      getNoFileMigrateFailList() {
        this.listLoading = true
        getNoFileMigrateFailList(this.listQuery).then(response => {
          this.list = response.root
          this.total = Number(response.totalProperty)
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
      handleFilter() {
        this.listQuery.page = 1
        this.getNoFileMigrateFailList()
      },
      migrate(row) {
        reMigrateFaileBatch(row).then(response => {
          this.$notify({
            title: 'Success',
            message: 'Created Successfully',
            type: 'success',
            duration: 2000
          })
          this.getList()
        })
      },
      reset() {
        this.listQuery.content_id=""
        this.listQuery.model_code=""
        this.listQuery.fail_time=""
      }
    }
  }
</script>
