<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.JobRelationDao">
	
	<resultMap id="JobRelation" type="com.xxl.job.admin.core.model.JobRelation" >
		<result column="id" property="id" />
		<result column="JOB_ID" property="jobId" />
	    <result column="CHILDREN_JOB_ID" property="childrenJob" />
	</resultMap>
	
	<resultMap id="JobRelationWithDesc" type="com.xxl.job.admin.core.model.JobRelation" >
		<result column="ID" property="id" />
		<result column="JOB_ID" property="jobId" />
		<result column="CHILDREN_JOB_ID" property="childrenJob" />
		<association property="jobInfo" javaType = "com.xxl.job.admin.core.model.XxlJobInfo">
			<result  column="JOB_DESC" property="jobDesc" />
		</association>
		<association property="childrenJobInfo" javaType = "com.xxl.job.admin.core.model.XxlJobInfo">
			<result  column="CHILDREN_JOB_DESC" property="jobDesc" />
		</association>
	</resultMap>
	

	<sql id="Base_Column_List">
		t.id,
		t.JOB_ID,
		t.CHILDREN_JOB_ID
	</sql>
	
	<select id="pageList" parameterType="java.util.HashMap" resultMap="JobRelation">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_JOB_RELATION t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobId gt 0">
				AND t.JOB_ID = #{parentJobId}
			</if>
		</trim>
		ORDER BY JOB_ID DESC
	</select>




	<select id="jobQueryList" parameterType="java.util.HashMap" resultMap="JobRelation">
		SELECT <include refid="Base_Column_List" /> FROM QRTZ_JOB_RELATION t
		START WITH t.CHILDREN_JOB_ID=#{jobId}
		CONNECT BY PRIOR    t.JOB_ID=t.CHILDREN_JOB_ID ORDER BY t.CHILDREN_JOB_ID
	</select>





	<select id="pageListWithDesc" parameterType="java.util.HashMap" resultMap="JobRelationWithDesc">
		SELECT T.ID, T.JOB_ID, T.CHILDREN_JOB_ID, T1.JOB_DESC,T2.JOB_DESC  CHILDREN_JOB_DESC
 	 	FROM QRTZ_JOB_RELATION T, QRTZ_TRIGGER_INFO T1, QRTZ_TRIGGER_INFO T2
 		WHERE T.JOB_ID = T1.ID AND T.CHILDREN_JOB_ID = T2.ID
		<if test="jobId &gt; 0"> AND T.JOB_ID = #{jobId} </if>
		ORDER BY T.JOB_ID DESC

	</select>


	<select id="loadById" parameterType="java.util.HashMap" resultMap="JobRelation">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_JOB_RELATION t
		WHERE ID = #{id}
		ORDER BY JOB_ID DESC
	</select>

	<select id="loadJByJobId" parameterType="java.util.HashMap" resultMap="JobRelation">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_JOB_RELATION t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobId gt 0">
				AND t.JOB_ID = #{jobId,jdbcType=INTEGER}
			</if>
		</trim>
		ORDER BY JOB_ID DESC
	</select>



	<select id="getAllJobRelation" parameterType="java.util.HashMap" resultMap="JobRelation">
		SELECT <include refid="Base_Column_List" />
		FROM QRTZ_JOB_RELATION t
		ORDER BY JOB_ID DESC
	</select>






	<select id="pageListCount" parameterType="java.util.HashMap" resultType="int">
		SELECT count(1)
		FROM QRTZ_JOB_RELATION t
		<trim prefix="WHERE" prefixOverrides="AND | OR" >
			<if test="jobId gt 0">
				AND t.JOB_ID = #{jobId}
			</if>
		</trim>
	</select>

	<insert id="insertRelations" parameterType="java.util.List">
		INSERT INTO QRTZ_JOB_RELATION (
		JOB_ID, CHILDREN_JOB_ID
		) VALUES
		<foreach collection="list" item="jobRelation" separator="," >
			(#{jobRelation.jobId,jdbcType=INTEGER},#{jobRelation.childrenJob,jdbcType=INTEGER})
		</foreach>
	</insert>


	<insert id="insertRelation" parameterType="com.xxl.job.admin.core.model.XxlJobInfo" useGeneratedKeys="false" keyProperty="id" >
	INSERT INTO QRTZ_JOB_RELATION (
			JOB_ID, CHILDREN_JOB_ID
		) VALUES
			(#{jobId,jdbcType=INTEGER},#{childrenJob,jdbcType=INTEGER})
	</insert>

	<update id="update" parameterType="com.xxl.job.admin.core.model.JobRelation" >
		UPDATE QRTZ_JOB_RELATION
		SET 
			CHILDREN_JOB_ID = #{childrenJob,jdbcType=INTEGER}
		WHERE id = #{id,jdbcType=INTEGER}
	</update>
	
	<delete id="delete" parameterType="java.util.HashMap">
		DELETE
		FROM QRTZ_JOB_RELATION
		WHERE id = #{id,jdbcType=INTEGER}
	</delete>

	
	<select id="getMaxId" resultType="Integer">
		SELECT  nvl(MAX(id),0) 
		FROM QRTZ_JOB_RELATION
	</select>

</mapper>