@echo off
if {%1}=={} goto LGetDate 

set dt=%1%
goto END

:LGetDate
rem GET YESTERDAY DATE
set dt=%date:~0,10%
#set dt=2023-07-04
rem date format is "YYYY-MM-DD"
rem set /P dt="Input Date: "
set dy=%dt:~0,4%
set dm=%dt:~5,2%
set dd=%dt:~8,2%


if %dm%%dd%==0101 goto L01
if %dm%%dd%==0201 goto L02
if %dm%%dd%==0301 goto L07
if %dm%%dd%==0401 goto L02
if %dm%%dd%==0501 goto L04
if %dm%%dd%==0601 goto L02
if %dm%%dd%==0701 goto L04
if %dm%%dd%==0801 goto L02
if %dm%%dd%==0901 goto L02
if %dm%%dd%==1001 goto L05
if %dm%%dd%==1101 goto L03
if %dm%%dd%==1201 goto L06
if %dd%==02 goto L10
if %dd%==03 goto L10
if %dd%==04 goto L10
if %dd%==05 goto L10
if %dd%==06 goto L10
if %dd%==07 goto L10
if %dd%==08 goto L10
if %dd%==09 goto L10
if %dd%==10 goto L11
set /A dd=dd-1
set dt=%dy%-%dm%-%dd%
goto END
:L10
set /A dd=%dd:~1,1%-1
set dt=%dy%-%dm%-0%dd%
goto END
:L11
set dt=%dy%-%dm%-09
goto END
:L02
set /A dm=%dm:~1,1%-1
set dt=%dy%-0%dm%-31
goto END
:L04
set /A dm=dm-1
set dt=%dy%-0%dm%-30
goto END
:L05
set dt=%dy%-09-30
goto END
:L03
set dt=%dy%-10-31
goto END
:L06
set dt=%dy%-11-30
goto END
:L01
set /A dy=dy-1
set dt=%dy%-12-31
goto END
:L07
set /A "dd=dy%%4"
if not %dd%==0 goto L08
set /A "dd=dy%%100"
if not %dd%==0 goto L09
set /A "dd=dy%%400"
if %dd%==0 goto L09
:L08
set dt=%dy%-02-28
goto END
:L09
set dt=%dy%-02-29
goto END
:END


echo open 21.96.8.22
echo user ocrftp
echo 3EDC#ert
echo bi
echo prompt off
echo lcd D:\data\%dt:~0,4%%dt:~5,2%%dt:~8,2%

#echo cd ../..
#echo cd ocrxcpt
echo cd Date

echo cd dcds 
echo mget 0100006D.k01.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%0
echo mget 0186310D.k14.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.v91.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.v92.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.v93.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.v94.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vg1.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vg2.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vg3.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vg4.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vh1.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vh2.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vh3.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vh4.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vt1.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vt2.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vt3.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.vt4.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.xa1.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.xa2.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.xa3.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.xa4.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0100006D.k07.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%0
echo mget 0186310D.dm1.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.w02.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.w03.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.w04.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.v81.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.v82.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.v83.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.v84.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 8186310D.1SR.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 8186310D.1RR.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget US86310D.RLM.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget 0186310D.dwd.gz.%dt:~0,4%%dt:~5,2%%dt:~8,2%1
echo mget T20501.00000230.bd4.%dt:~0,4%%dt:~5,2%%dt:~8,2%1.gz
echo mget T20501.00000230.bd6.%dt:~0,4%%dt:~5,2%%dt:~8,2%1.gz
echo mget T20501.00000230.bb1.%dt:~0,4%%dt:~5,2%%dt:~8,2%1.gz

echo bi
echo cd ..
echo cd %dt:~0,4%%dt:~5,2%%dt:~8,2%
echo asc
echo mget *0090*
echo mget *0050*
echo mget CRDD1020_%dt:~0,4%%dt:~5,2%%dt:~8,2%.txt   
echo mget DEPD1010_%dt:~0,4%%dt:~5,2%%dt:~8,2%.txt   
echo mget DEPD1020_%dt:~0,4%%dt:~5,2%%dt:~8,2%.txt  
echo mget DEPD2050_%dt:~0,4%%dt:~5,2%%dt:~8,2%.txt
echo mget DEPD9950_%dt:~0,4%%dt:~5,2%%dt:~8,2%.txt
echo mget ACAD0020_%dt:~0,4%%dt:~5,2%%dt:~8,2%.txt
echo mget *.AB6

echo bye

