package com.sunyard.client.conn;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.ws.client.WSAccessClient;
import com.sunyard.ws.comm.Base64Coder;
import com.sunyard.ws.internalapi.SunEcmAccess;

/**
 * 客户端WEBSERVICE连接
 * 
 * <AUTHOR>
 *
 */
public class WSConn implements TransConn {
	private final static Logger log = LoggerFactory.getLogger(WSConn.class);
	private static SunEcmAccess access;
	private static WSAccessClient wsAccessClient = new WSAccessClient();
	private String returnMsg = "";
	
	private String getReturnMsg() {
		return returnMsg;
	}

	private void setReturnMsg(String returnMsg) {
		this.returnMsg = returnMsg;
	}

	public WSConn(String ip, int httpPort, String serverName){
		String url = "http://"+ip+":"+httpPort+"/"+serverName+"/webservices/WsInterface";
		log.info( "--WSConn-->WSConn-->建立WEBSERVICE连接:"+ url);
		WSConn.access = wsAccessClient.getAccessClient(url, 5*60*1000);
	}
	
	public void destroy() {
		;
	}

	public String receiveMsg() {
		return getReturnMsg();
	}

	public boolean sendFileData(String filePath, String contentID,
			String transType) throws IOException {
		log.info( "WEBSERVICE发送文件,文件路径:"+filePath+",内容ID:"+contentID);

		// 获取文件后缀
		int i = filePath.lastIndexOf('.'); 
		String format = ""; 
		if ((i >-1) && (i < (filePath.length()))) { 
			format = filePath.substring(i + 1, filePath.length()); 
		} 
		// 判断文件是否存在
		File file = new File(filePath);
		if (!file.exists()) {
			log.debug( "文件不存在,文件路径:"+filePath);
			return false;
		}
		long fileSize = file.length();
		StringBuffer sbuf = new StringBuffer();
		sbuf.append("FILENAME=" + file.getName() 
				+ ",FILESIZE=" + Long.toString(fileSize) 
				+ ",FILEPATH=" + filePath
				+ ",CONTENTID = " + contentID
				+ ",FORMAT=" + format
				+ ",FILE=");
		try {
			byte[] fileBytes = getBytesFromFile(file);
			char[] fileChars = Base64Coder.encode(fileBytes);
			sbuf.append(fileChars);
			access.receiveFileFromClient(sbuf.toString());
			return true;
		} catch (IOException e) {
			log.error(
					"-->WSConn-->sendFileData-->SunECMException-->"
							+ e.toString());
			throw new IOException(e.toString());
		}
	}
	
	private static byte[] getBytesFromFile(File file) throws IOException {
		InputStream is =null;
		try{
		 is = new FileInputStream(file);
		// 获取文件大小
		long length = file.length();
		if (length > Integer.MAX_VALUE) {
			// 文件太大，无法读取
			is.close();
			throw new IOException("File is to large " + file.getName());
		}
		// 创建一个数据来保存文件数据
		byte[] bytes = new byte[(int) length];
		// 读取数据到byte数组中
		int offset = 0;
		int numRead = 0;
		while (offset < bytes.length
		&& (numRead = is.read(bytes, offset, bytes.length - offset)) >= 0) {
			offset += numRead;
		}
		// 确保所有数据均被读取
		if (offset < bytes.length) {
			is.close();
			throw new IOException("Could not completely read file "
					+ file.getName());
			}
		return bytes;
		} finally {
			if (is != null) {
				is.close();
			}
		}
	}

	public void sendMsg(String msg) {
		log.info( "WEBSERVICE发送消息");
		String returnMsg = access.receiveMsgFromClient(msg);
		setReturnMsg(returnMsg);
	}

}