package com.sunyard.ecm.server.bean;



import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * 索引信息
 *
 */
@XStreamAlias("OfflineCountBean")
public class OfflineCountBean {
	@XStreamAsAttribute
	private String startDate;
	/** 离线失败数 **/
	@XStreamAsAttribute
	private String offlineFailCount;
	/** 离线成功数 **/
	@XStreamAsAttribute
	private String offlineSuccessCount;
	/** 离线失败数 **/
	@XStreamAsAttribute
	private String offlineTotalCount;
	/** 离线放弃数 **/
	@XStreamAsAttribute
	private String offlineabandonCount;
	/** 批次号 **/
	@XStreamAsAttribute
	private String contentId;
	/** 迁移状态 **/
	@XStreamAsAttribute
	private String migrationStatus;
	/** 组id **/
	@XStreamAsAttribute
	private String groupId;
	/** modelCode **/
	@XStreamAsAttribute
	private String modelCode;

	public String getMigrationStatus() {
		return migrationStatus;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getModelCode() {
		return modelCode;
	}

	public void setModelCode(String modelCode) {
		this.modelCode = modelCode;
	}

	public void setMigrationStatus(String migrationStatus) {
		this.migrationStatus = migrationStatus;
	}

	public String getOfflineabandonCount() {
		return offlineabandonCount;
	}

	public void setOfflineabandonCount(String offlineabandonCount) {
		this.offlineabandonCount = offlineabandonCount;
	}

	public String getStartDate() {
		return startDate;
	}

	public String getContentId() {
		return contentId;
	}

	public void setContentId(String contentId) {
		this.contentId = contentId;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getOfflineFailCount() {
		return offlineFailCount;
	}

	public void setOfflineFailCount(String offlineFailCount) {
		this.offlineFailCount = offlineFailCount;
	}

	public String getOfflineSuccessCount() {
		return offlineSuccessCount;
	}

	public void setOfflineSuccessCount(String offlineSuccessCount) {
		this.offlineSuccessCount = offlineSuccessCount;
	}

	public String getOfflineTotalCount() {
		return offlineTotalCount;
	}

	public void setOfflineTotalCount(String offlineTotalCount) {
		this.offlineTotalCount = offlineTotalCount;
	}

}
