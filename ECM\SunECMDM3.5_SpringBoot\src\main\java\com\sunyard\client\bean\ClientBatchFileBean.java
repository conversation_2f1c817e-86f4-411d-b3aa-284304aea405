package com.sunyard.client.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * 批次信息
 * <AUTHOR>
 *
 */
@XStreamAlias("BatchFileBean")
public class ClientBatchFileBean {
	/** 文档部件名 **/
	@XStreamAsAttribute
	private String FILE_PART_NAME;
	/** 文档部件版本信息 **/
	@XStreamAsAttribute
	private String VERSION;
	/** 文档部件过滤条件 **/
	private HashMap<String, String> FILTERS;
	/** 文件 **/
	private List<ClientFileBean> files;
	
	public HashMap<String, String> getFilters() {
		return FILTERS;
	}

	public void setFilters(HashMap<String, String> filters) {
		FILTERS = filters;
	}
	
	/**
	 * 添加过滤条件
	 * 
	 * @param key 筛选条件
	 * @param value 筛选值
	 */
	public void addFilter(String key, String value) {
		if(FILTERS == null) {
			FILTERS = new HashMap<String, String>();
		}
		this.FILTERS.put(key, value);
	}

	public String getFilePartName() {
		return FILE_PART_NAME;
	}

	/**
	 * 文档部件英文名 
	 * @param filePartName 文档部件英文名
	 */
	public void setFilePartName(String filePartName) {
		this.FILE_PART_NAME = filePartName;
	}

	public String getVersion() {
		return VERSION;
	}

	/**
	 * 设定文档部件版本
	 * @param version 文档部件版本
	 */
	public void setVersion(String version) {
		this.VERSION = version;
	}

	public List<ClientFileBean> getFiles() {
		return files;
	}

	/**
	 * 设定文件队列
	 * @param files 文档队列List<ClientFileBean>
	 */
	public void setFiles(List<ClientFileBean> files) {
		this.files = files;
	}

	/**
	 * 添加文件
	 * @param file 文件信息ClientFileBean
	 */
	public void addFile(ClientFileBean file) {
		if(files == null) {
			files = new ArrayList<ClientFileBean>();
		}
		this.files.add(file);
	}
	
	/**
	 * 需要保留的文件
	 * @param fileNOs
	 */
	public void retainFile(String[] fileNOs) {
		if(files == null) {
			files = new ArrayList<ClientFileBean>();
		}
		for (String fileNO : fileNOs) {
			ClientFileBean fileBean = new ClientFileBean();
			fileBean.setFileNO(fileNO);
			this.files.add(fileBean);
		}
	}
	
	/**
	 * 替换文件
	 * @param fileNO
	 * @param fileName
	 */
	public void replaceFile(String fileNO, String fileName) {
		if(files == null) {
			files = new ArrayList<ClientFileBean>();
		}
		ClientFileBean fileBean = new ClientFileBean();
		fileBean.setFileNO(fileNO);
		fileBean.setFileName(fileName);
		this.files.add(fileBean);
	}
	
	/**
	 * 改变一个文件的属性
	 * @param file 文件信息ClientFileBean
	 */
	public void updateFileAtt(ClientFileBean file) {
		if(files == null) {
			files = new ArrayList<ClientFileBean>();
		}
		this.files.add(file);
	}
}