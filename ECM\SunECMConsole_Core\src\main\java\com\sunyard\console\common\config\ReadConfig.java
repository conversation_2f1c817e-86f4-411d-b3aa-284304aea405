package com.sunyard.console.common.config;

import com.sunyard.console.common.config.bean.ConsoleConfigBean;
import com.sunyard.console.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <p>
 * Title: 读取配置文件
 * </p>
 * <p>
 * Description: 读取配置文件consoleConfig.properties
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ReadConfig {

	private final static Logger log = LoggerFactory.getLogger(ConsoleConfigBean.class);
	private static List<String> list = new ArrayList<String>();
	private static String open_weak_ps_dictionary = "false";

	/**
	 * 读取transConfig.properties配置文件
	 * 
	 * @return
	 * @throws Exception
	 */
	public static ConsoleConfigBean getConsoleConfigBean() {
		ConsoleConfigBean bean = new ConsoleConfigBean();
		Properties props = new Properties();
		InputStream in = null;
		try {
			ClassLoader classLoader = ReadConfig.class.getClassLoader();
			if (classLoader == null) {
				throw new IOException("classloader is null");
			} else {
				in = classLoader.getResourceAsStream("consoleConfig.properties");
				props.load(in);
			}
		} catch (IOException e) {
			log.error("error", e);
			throw new RuntimeException("控制台读取配置文件出错,错误信息==》" + e.getMessage());
		} finally {
			try {
				if (in != null) {
					in.close();
				}
				in = null;
			} catch (IOException e) {
				log.error("读取文件consoleConfig.properties失败", e);
			}
		}

		bean.setToken_able_time(props.getProperty("token_able_time"));
		bean.setServer_DM_Name(props.getProperty("server_DM_Name"));
		bean.setServer_UA_Name(props.getProperty("server_UA_Name"));
		bean.setOtherConsoleUrl(props.getProperty("otherConsoleUrl"));
		bean.setOne_user_can_deleteBatch(props.getProperty("one_user_can_deleteBatch"));
		bean.setOffLineConsoleUrl(props.getProperty("offLineConsoleUrl"));
		bean.setMaxIssueThreadSize(Integer.parseInt(props.getProperty("maxIssueThreadSize")));
		bean.setSeparate_table_type(Integer.parseInt(props.getProperty("separate_table_type")));
		int monitorTime = 1;
		try {
			monitorTime = Integer.parseInt(props.getProperty("monitorTime"));
		} catch (Exception e) {
			log.warn("set bean error=", e);
			monitorTime = 1;
		}
		bean.setMonitorTime(monitorTime);
		// 用户密码修改需求
		String logout_max_day = props.getProperty("logout_max_day");
		if (logout_max_day != null) {
			logout_max_day = logout_max_day.trim();
		} else {
			logout_max_day = "0";
		}
		String logout_remind_day = props.getProperty("logout_remind_day");
		if (logout_remind_day != null) {
			logout_remind_day = logout_remind_day.trim();
		} else {
			logout_remind_day = "0";
		}
		bean.setLogout_max_day(Integer.parseInt(logout_max_day));// 超过n天么有修改密码则转到修改密码页面
		bean.setLogout_remind_day(Integer.parseInt(logout_remind_day));// 超过n天提醒修改密码

		boolean haixiaSSO = false;
		try {
			haixiaSSO = Boolean.parseBoolean(props.getProperty("haixiaSSO"));
		} catch (Exception e) {
			log.error("Exception e:" + e);
			haixiaSSO = false;
		}
		bean.setHaixiaSSO(haixiaSSO);
		int loginErrorCount = 3;
		try {
			loginErrorCount = Integer.parseInt(props.getProperty("loginErrorCount"));
		} catch (Exception e) {
			log.error("loginErrorCount error", e);
		}
		bean.setLoginErrorCount(loginErrorCount);
		int loginErrorInterval = 5;
		try {
			loginErrorInterval = Integer.parseInt(props.getProperty("loginErrorInterval"));
		} catch (Exception e) {
			log.error("loginErrorInterval error", e);
		}
		bean.setLoginErrorInterval(loginErrorInterval);
		int maxLoginSessionInterval = 30;
		try {
			maxLoginSessionInterval = Integer.parseInt(props.getProperty("maxLoginSessionInterval"));
		} catch (Exception e) {
			log.error("maxLoginSessionInterval error", e);
		}
		bean.setMaxLoginSessionInterval(maxLoginSessionInterval);

		// 设置监控地址
		String monitAdress = props.getProperty("monitAddress");
		Map<String, String> monitmap = new HashMap<String, String>();
		if (monitAdress != null && !monitAdress.isEmpty()) {
			String[] monit = monitAdress.split(";");
			for (int i = 0; i < monit.length; i++) {
				String[] ipandport = monit[i].split(":");
				monitmap.put(ipandport[0], ipandport[1]);
			}
			bean.setMonitAddress(monitmap);
		} else {
			log.debug("未设置监控地址，按DM端口设置");
		}
		String sessionType = "1";
		try {
			sessionType = props.getProperty("sessionType");
		} catch (Exception e) {
			log.error("e", e);
		}
		bean.setSessionType(sessionType);

		try {
			bean.setEs_sunecmdmip( props.getProperty("es_sunecmdmip"));
		}catch (Exception e) {
			log.error("set es_sunecmdmip error",e);
		}
	
		try {
	            bean.setDubboIsOn(props.getProperty("dubboIsOn"));
	        } catch (Exception e) {
	            log.error("set dubboIsOn error", e);
	       }			
		
		  return bean;
	}

	/**
	 * 读取weak_pwd_dic.properties配置文件
	 * 
	 * @return
	 * @throws Exception
	 */
	public static void getWeakPWDDictionary() {
		if (list != null && list.size() > 0) {
			return;
		}
		Properties props = new Properties();
		InputStream in = null;
		try {
			ClassLoader classloader = ReadConfig.class.getClassLoader();
			if (classloader == null) {
				throw new IOException("classloader is null");
			} else {
				in = classloader.getResourceAsStream("weak_pwd_dic.properties");
				props.load(in);
				String weak_password_dictionary = props.getProperty("weak_password_dictionary");
				open_weak_ps_dictionary = props.getProperty("open_weak_password_dictionary");
				if(weak_password_dictionary == null){
					return;
				}else{
					list = StringUtil.stringToList(weak_password_dictionary, ",");
				}
			}

		} catch (IOException e) {
			log.error("读取weak_pwd_dic.properties出错", e);
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e) {
					log.error("关闭weak_pwd_dic.properties失败", e);
				}
			}
		}
	}

	/**
	 * 是否弱密码
	 * 
	 * @param pwd
	 * @return 如果是弱密码返回true，如果不是弱密码返回false
	 */
	public static boolean isWeakPassword(String pwd) {
		getWeakPWDDictionary();
		if ("true".equals(open_weak_ps_dictionary)){
			if(list != null && list.size() > 0) {
				if (list.contains(pwd)) {
					return true;
				} else {
					return false;
				}
			}
		}
		return false;
	}
}
