package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

@XStreamAlias("modelPermissionBean")
public class ModelPermissionBean {
	@XStreamAsAttribute
	private String MODEL_CODE;// 内容模型代码
	@XStreamAsAttribute
	private String PERMISSION_CODE;// 操作权限  依次为增0、删1、改2、查3、打印（保留位）4、批注5
	
	public static final String[] permission_Name = {"UPLOAD","DELETE","UPDATE","SEARCH","NULL","ANNO_OPERATION"};
	
	public String getModel_code() {
		return MODEL_CODE;
	}
	public void setModel_code(String modelCode) {
		MODEL_CODE = modelCode;
	}
	public String getPermission_code() {
		return PERMISSION_CODE;
	}
	public void setPermission_code(String permissionCode) {
		PERMISSION_CODE = permissionCode;
	}
}