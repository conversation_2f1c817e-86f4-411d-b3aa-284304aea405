package com.sunyard.ws.utils;

import com.sunyard.console.configmanager.bean.*;
import com.sunyard.console.contentmodelmanage.bean.AttrDesenRuleBean;
import com.sunyard.console.singletonManage.bean.LazySingletonBean;
import com.sunyard.console.tagmanage.bean.TagInfoBean;
import com.sunyard.console.tagmanage.bean.TagRelAttributeBean;
import com.sunyard.console.tagmanage.bean.TagRelModelBean;
import com.sunyard.ecm.server.bean.LifeCycleStrategyBean;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.thoughtworks.xstream.io.xml.XmlFriendlyNameCoder;

import java.util.ArrayList;
import java.util.List;


/**
 * xml报文和bean对象的相互转换
 * 支持javabean，List，嵌套Map，但Map中最好不要用bean，尽量在WS中使用String。
 * 例如Map<String, Map<String, String>>……
 * <AUTHOR>
 * @param <T>
 */
public class XMLUtil<T> {
	//	private static XStream xStream = new XStream(new XppDriver(new
//			XmlFriendlyReplacer("_-", "_")));
	private static XStream xStream = new XStream(new DomDriver("UTF-8", new XmlFriendlyNameCoder("-_", "_")));

	//	private static XStream dexStream = new XStream(new DomDriver());//xstream-1.3.1.jar 适用
	private static XStream dexStream = new XStream(new DomDriver("UTF-8", new XmlFriendlyNameCoder("-_", "_")));//xstream-1.4.3.jar适用
	static
	{
		Class<?>[] classes = new Class[] { TokenBean.class, SQLBean.class, OfflineCountBean.class, LogInfoBean.class,LazySingletonBean.class,LifeCycleStrategyBean.class, NodeInfo.class,TagInfoBean.class,TagRelModelBean.class,TagRelAttributeBean.class,EsSQLBean.class, AttrDesenRuleBean.class};
		dexStream.allowTypes(classes);
		xStream.allowTypes(classes);
	}


	/**
	 * 获取类的别名或者类名
	 * @param type
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private static String getTypeName(Class type){
		String nodeName = "";
		XStreamAlias alias = (XStreamAlias) type.getAnnotation(XStreamAlias.class);
		if(alias != null) nodeName = alias.value();
		else nodeName = type.getSimpleName();

		return nodeName;
	}

	/**
	 * 从普通bean对象转为xml报文
	 * @param t 待转换的bean对象类型
	 * @return
	 */
	public static <T> String bean2XML(T t){
		xStream.processAnnotations(t.getClass());
		return xStream.toXML(t).replaceAll("\\n", "")
				.replace("&lt;", "<").replace("&gt;", ">").replace("&quot;", "\"").replaceAll(",", "<<DH>>");
	}

	/**
	 * 从xml报文转为bean对象
	 * @param xml xml报文
	 * @param type type 类名称。type.class
	 * @category 使用方法 xml2Bean(String xml， type);
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T xml2Bean(String xml, Class type){
		xml=xml.replaceAll("<<DH>>", ",");
		dexStream.processAnnotations(type);
		T t = (T) dexStream.fromXML(xml);
		return t;
	}

	/**
	 * 将List对象转为xml报文
	 * @param list 待转换的list对象
	 * @return
	 */
	public static <T> String list2Xml(List<T> list){
		if (list == null || list.size() == 0) {
			return "";
		}
		StringBuffer sb = new StringBuffer();
		for (T t : list) {
			sb.append(bean2XML(t)).append("");
		}
		return sb.toString();
	}

	/**
	 * 将xml报文转换为list对象
	 * @param xml xml报文
	 * @param type type 类名称。type.class
	 * @category 使用方法 xml2list(String xml， type)，List中的类对象
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> List<T> xml2list(String xml, Class type){
		List<T> list = new ArrayList<T>();
		String nodeName = getTypeName(type);
		if(nodeName.equals("Map")) nodeName = "map";
		String[] xmlStrArray = xml.split("<"+ nodeName);
		for (int i = 1; i < xmlStrArray.length; i++) {
			String str = xmlStrArray[i];
			list.add((T)xml2Bean("<"+ nodeName + str, type));
		}
		return list;
	}

	/**
	 * 为xml结构报文加上头和root节点
	 * <?xml version="1.0" encoding="UTF-8"?>
	 * <root>
	 * 报文内容，通过XMLUtil
	 * </root>
	 * @param str
	 * @return
	 */
	public static <T> String addHeadRootNode(String str){
		StringBuffer sb = new StringBuffer();
		sb.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
		sb.append("<root>").append(str).append("</root>");
		return sb.toString();
	}

	/**
	 * 报文删除头和root节点，便于转为bean对象
	 * <?xml version="1.0" encoding="UTF-8"?>
	 * <root>
	 * 报文内容，通过XMLUtil
	 * </root>
	 * @param str
	 * @return
	 */
	public static <T> String removeHeadRoot(String str){
		str = str.replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "");
		str = str.replaceAll("<root>", "");
		str = str.replaceAll("</root>", "");
		return str;
	}

	/**
	 * 因登陆接口XML报文改造后针对登陆报文剥离头和ROOT及RESULT节点，便于转为bean对象
	 *
	 * <?xml version="1.0" encoding="UTF-8"?>
	 * <root>
	 * 报文内容，通过XMLUtil
	 * </root>
	 * @param str
	 * @return
	 */
	public static <T> String removeHeadRoot(String str, String isLogin){
		if ("isLogin".equals(isLogin)) {
			str = str.replaceAll("<\\?xml version=\"1.0\" encoding=\"UTF-8\"\\?>", "");
			str = str.replaceAll("<ROOT>", "");
			str = str.replaceAll("</ROOT>", "");
			str = str.replaceAll("<MESSAGE>", "");
			str = str.replaceAll("</MESSAGE>", "");
			return str;
		} else {
			return removeHeadRoot(str);
		}
	}

}