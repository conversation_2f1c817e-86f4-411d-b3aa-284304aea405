package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * <p>
 * Title:xstream 生命周期信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("LifeCycleStrategyBean")
public class LifeCycleStrategyBean {
	
	@XStreamAsAttribute
	private String TASK_ID;//任务编号TASK_ID
	
	@XStreamAsAttribute
	private String TASK_NO;// 任务NO  TASK_NO
	
	@XStreamAsAttribute
	private String GROUP_ID;//服务器组编号GROUP_ID
	
	@XStreamAsAttribute
	private String MODEL_CODE;// 模型代码MODEL_CODE
	
	@XStreamAsAttribute
	private String RUN_TYPE;// RUN_TYPE为执行类型RUN_TYPE
	
	@XStreamAsAttribute
	private String BEGIN_TIME;// BEGIN_TIME为开始时间BEGIN_TIME
	
	@XStreamAsAttribute
	private String END_TIME;// END_TIME为结束时间END_TIME

	@XStreamAsAttribute
	private String PARAMETERS;	// 参数PARAMETERS
	
	@XStreamAsAttribute
	private String TASK_STATE;// 任务状态task_state
	@XStreamAsAttribute
	private String TASK_NAME;//任务名称
	@XStreamAsAttribute
	private String TASK_CLASS ;//任务类名
	@XStreamAsAttribute
	private String PARAM_KEY;//参数key
	

	public String getTask_id() {
		return TASK_ID;
	}

	public void setTask_id(String taskId) {
		TASK_ID = taskId;
	}

	public String getTask_no() {
		return TASK_NO;
	}

	public void setTask_no(String taskNo) {
		TASK_NO = taskNo;
	}

	public String getGroup_id() {
		return GROUP_ID;
	}

	public void setGroup_id(String groupId) {
		GROUP_ID = groupId;
	}

	public String getModel_code() {
		return MODEL_CODE;
	}

	public void setModel_code(String modelCode) {
		MODEL_CODE = modelCode;
	}

	public String getRun_type() {
		return RUN_TYPE;
	}

	public void setRun_type(String runType) {
		RUN_TYPE = runType;
	}

	public String getBegin_time() {
		return BEGIN_TIME;
	}

	public void setBegin_time(String beginTime) {
		BEGIN_TIME = beginTime;
	}

	public String getEnd_time() {
		return END_TIME;
	}

	public void setEnd_time(String endTime) {
		END_TIME = endTime;
	}

	public String getParameters() {
		return PARAMETERS;
	}

	public void setParameters(String parameters) {
		this.PARAMETERS = parameters;
	}

	public String getTask_state() {
		return TASK_STATE;
	}

	public void setTask_state(String taskState) {
		TASK_STATE = taskState;
	}

	public String getTask_name() {
		return TASK_NAME;
	}

	public void setTask_name(String taskName) {
		TASK_NAME = taskName;
	}

	public String getTask_class() {
		return TASK_CLASS;
	}

	public void setTask_class(String taskClass) {
		TASK_CLASS = taskClass;
	}

	public String getParam_key() {
		return PARAM_KEY;
	}

	public void setParam_key(String paramKey) {
		PARAM_KEY = paramKey;
	}

	@Override
	public String toString() {
		return "LifeCycleStrategyBean [BEGIN_TIME=" + BEGIN_TIME
				+ ", END_TIME=" + END_TIME + ", GROUP_ID=" + GROUP_ID
				+ ", MODEL_CODE=" + MODEL_CODE + ", PARAM_KEY=" + PARAM_KEY
				+ ", PARAMETERS=" + PARAMETERS + ", RUN_TYPE=" + RUN_TYPE
				+ ", TASK_CLASS=" + TASK_CLASS + ", TASK_ID=" + TASK_ID
				+ ", TASK_NAME=" + TASK_NAME + ", TASK_NO=" + TASK_NO
				+ ", TASK_STATE=" + TASK_STATE + "]";
	}

}