package com.sunyard.console.tagmanage.dao;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.common.util.StringUtil;
import com.sunyard.console.common.util.TagStateUtil;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.tagmanage.bean.TagInfoBean;
import com.sunyard.console.tagmanage.bean.TagRelAttTypeDefineBean;
import com.sunyard.console.tagmanage.bean.TagRelAttributeBean;
import com.sunyard.console.tagmanage.bean.TagRelModelBean;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Title: 标签实现类
 * </p>
 * <p>
 * Description: 参数管理中各类数据库操作的方法
 * </p>
 * <p>
 * Copyright: Copyright (c) 2022
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 */
@Repository("tagManageDao")
public class TagManageDAOIMP implements TagManageDAO {
	/**
	 * 数据库分页对象
	 */
	@Autowired
	private PageTool pageTool;

	private final static  Logger log = Logger.getLogger(TagManageDAOIMP.class);

	public PageTool getPageTool() {
		return pageTool;
	}

	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}

	public List<TagInfoBean> getTag(String tag_code, String tag_name, int start, int limit) {
		log.info( "--getTag(start)-->tag_code:" + tag_code + ";tag_name:" + tag_name);
		List<TagInfoBean> tagInfo = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT TAG_CODE , TAG_NAME , TAG_STATE FROM ES_TAGS WHERE 1=1 ");
		if (tag_code != null && !"".equals(tag_code)) {
			sql.append("AND TAG_CODE like  '%").append(tag_code).append("%' ");
		}
		if (tag_name != null && !"".equals(tag_name)) {
			sql.append("AND TAG_NAME LIKE  '%").append(tag_name).append("%' ");
		}
		try {
			log.debug( "--getTag-->sql:" + sql.toString());
			tagInfo = DataBaseUtil.SUNECM.queryBeanList(pageTool
					.getPageSql(sql.toString(), start, limit),TagInfoBean.class);
			sql = null;
		} catch (Exception e) {

			// 记录日志
			log.error( "标签管理->查询标签失败" + e.toString());
			throw new DBRuntimeException(
					"tagManageDAOIMP===>tagInfo:"
							+ e.toString());
		}
		log.info( "--getTag(over)-->tagInfo:"+tagInfo);
		return tagInfo;
	}

	public List<TagInfoBean> getTag(String tag_code, String tag_name) {
		log.info( "--getTag(start)-->tag_code:" + tag_code + ";tag_name:" + tag_name);
		List<TagInfoBean> tagInfo = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT TAG_CODE , TAG_NAME , TAG_STATE FROM ES_TAGS WHERE 1=1 ");
		if (tag_code != null && !"".equals(tag_code)) {
			sql.append("AND TAG_CODE like  '%").append(tag_code).append("%' ");
		}
		if (tag_name != null && !"".equals(tag_name)) {
			sql.append("AND TAG_NAME LIKE  '%").append(tag_name).append("%' ");
		}
		try {
			log.debug( "--getTag-->sql:" + sql.toString());
			tagInfo = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),TagInfoBean.class);
			sql = null;
		} catch (Exception e) {

			// 记录日志
			log.error( "标签管理->查询标签失败" + e.toString());
			throw new DBRuntimeException(
					"tagManageDAOIMP===>tagInfo:"
							+ e.toString());
		}
		log.info( "--getTag(over)-->tagInfo:"+tagInfo);
		return tagInfo;
	}

	public boolean addTag(TagInfoBean tagBean, List<TagRelAttributeBean> attrList) {
		log.info("--addTag(start)-->tagBean:" + tagBean + ";attrList" + attrList);
		Collection<String> sqls = new ArrayList<String>();
		String tagCode = tagBean.getTag_code();
		String tagName = tagBean.getTag_name();
//		String[] checks = check_ids.split(",");
		StringBuffer sql = new StringBuffer();

		sql.append("INSERT INTO ES_TAGS (TAG_CODE,TAG_NAME,TAG_STATE )VALUES( '");
		sql.append(tagCode).append("','");
		sql.append(tagName).append("','");
		sql.append(TagStateUtil.Not_Synchronized).append("')");//未同步
		log.debug("--addTag-->sql:" + sql.toString());
		sqls.add(sql.toString());

		StringBuffer sql2 = new StringBuffer();
		sql2.append("DELETE FROM ES_TAGS_ATTRS WHERE TAG_CODE ='").append(tagCode).append("'");
		log.debug("--addTag-->sql:" + sql2.toString());
		sqls.add(sql2.toString());
		for(TagRelAttributeBean attr:attrList){
//			boolean isIndex = Arrays.asList(checks).contains(attr.getAttribute_code());

			StringBuffer sql3 = new StringBuffer();
			sql3.append("INSERT INTO ES_TAGS_ATTRS(TAG_CODE,ATTR_CODE,ATTR_TYPE,IS_INDEX,ATTR_SYS_TYPE,IGNORE_ABOVE,STATE )VALUES( '");
			sql3.append(tagCode).append("','");
			sql3.append(attr.getAttribute_code()).append("','");
			sql3.append(attr.getAttribute_type()).append("','");
			sql3.append(attr.getAttribute_isindex()).append("','");
			sql3.append(attr.getAttribute_motel_type()).append("','");
			sql3.append(attr.getAttribute_above()).append("','");
			sql3.append(TagStateUtil.Not_Synchronized).append("')");
			log.debug("--addTag-->sql:" + sql3.toString());
			sqls.add(sql3.toString());
		}

		try {
			DataBaseUtil.SUNECM.exceTrans(sqls);
		} catch (Exception e) {
			log.error("标签管理->新增标签及关联属性失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>addTag:" + e.toString());
		}
		log.info("--addTag(over)");
		return true;
	}

	public boolean updateTag(TagInfoBean tagBean, List<TagRelAttributeBean> attrList) {
		log.info("--updateTag(start)-->tagBean:" + tagBean + ";attrList" + attrList);
		Collection<String> sqls = new ArrayList<String>();
		String tagCode = tagBean.getTag_code();
		String tagName = tagBean.getTag_name();
		String tagState = tagBean.getTag_state();
		String attState = TagStateUtil.Not_Synchronized;//未同步1
		if(tagState.equals(TagStateUtil.Already_Synchronized) || tagState.equals(TagStateUtil.Synchronized_Modify)){//已同步或同步修改
			if(attrList != null && attrList.size()>0){
				tagState = TagStateUtil.Synchronized_Modify;//状态设为同步已修改3
				attState = TagStateUtil.Synchronized_Modify;//状态设为同步已修改3
			}
		}

//		String[] checks = check_ids.split(",");
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE ES_TAGS SET TAG_NAME ='");
		sql.append(tagName).append("',TAG_STATE='");
		sql.append(tagState).append("'");
		sql.append(" WHERE TAG_CODE = '").append(tagCode).append("'");
		log.debug("--updateTag-->sql:" + sql.toString());
		sqls.add(sql.toString());

//		StringBuffer sql2 = new StringBuffer();
//		sql2.append("DELETE FROM ES_TAGS_ATTRS WHERE TAG_CODE ='").append(tagCode).append("'");
//		log.debug("--updateTag-->sql:" + sql2.toString());
//		sqls.add(sql2.toString());
		if(attrList != null) {
			for(TagRelAttributeBean attr:attrList){
	//			boolean isIndex = Arrays.asList(checks).contains(attr.getAttribute_code());
				if(!attr.getAttribute_code().equals("") && attr.getAttribute_code()!= null) {
					StringBuffer sql3 = new StringBuffer();
					sql3.append("INSERT INTO ES_TAGS_ATTRS(TAG_CODE,ATTR_CODE,ATTR_TYPE,IS_INDEX,ATTR_SYS_TYPE,IGNORE_ABOVE,STATE )VALUES( '");
					sql3.append(tagCode).append("','");
					sql3.append(attr.getAttribute_code()).append("','");
					sql3.append(attr.getAttribute_type()).append("','");
					sql3.append(attr.getAttribute_isindex()).append("','");
					sql3.append(attr.getAttribute_motel_type()).append("','");
					sql3.append(attr.getAttribute_above()).append("','");
					sql3.append(attState).append("')");
					log.debug("--updateTag-->sql:" + sql3.toString());
					sqls.add(sql3.toString());
				}
			}
		}

		try {
			DataBaseUtil.SUNECM.exceTrans(sqls);
		} catch (Exception e) {
			log.error("标签管理->修改标签及关联属性失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>updateTag:" + e.toString());
		}
		log.info("--updateTag(over)");
		return true;
	}

	public boolean delTag(String tag_code) {
		log.info( "--delTag(start)-->tag_code:" + tag_code);
		if (tag_code == null || tag_code.equals("")) {
			return false;
		}
		Collection<String> sqls = new ArrayList<String>();
		StringBuffer sql = new StringBuffer();

		sql.append("DELETE FROM ES_TAGS ");
		sql.append("WHERE TAG_CODE = '").append(tag_code).append("'");
		log.debug("--delTag-->sql:" + sql.toString());
		sqls.add(sql.toString());

		StringBuffer sql2 = new StringBuffer();
		sql2.append("DELETE FROM ES_TAGS_ATTRS WHERE TAG_CODE ='").append(tag_code).append("'");
		sqls.add(sql2.toString());
		log.debug("--delTag-->sql:" + sql2.toString());

		StringBuffer sql3 = new StringBuffer();
		sql3.append("DELETE FROM ES_MODEL_REL WHERE TAG_CODE ='").append(tag_code).append("'");
		sqls.add(sql3.toString());
		log.debug("--delTag-->sql:" + sql3.toString());
		try {
			DataBaseUtil.SUNECM.exceTrans(sqls);
		} catch (Exception e) {
			// 记录日志
			log.error( "标签管理->tagManageDAOIMP->delTag失败"
					+ e.toString());
			throw new DBRuntimeException(
					"tagManageDAOIMP===>delTag:"
							+ e.toString());
		}
		log.info( "--delTag(over)");
		return true;
	}

	public List<TagRelAttributeBean> getNotExistAttrsTree(String tag_code) {
		log.info("--getNotExistAttrsTree(start)-->tag_code:" + tag_code);
		List<TagRelAttributeBean> attrs = null;
		StringBuffer sql = new StringBuffer();
		sql.append(
				"SELECT ATTRIBUTE_CODE,ATTRIBUTE_NAME,ATTRIBUTE_TYPE,ATTRIBUTE_UPUSER,ATTRIBUTE_MOTEL_TYPE FROM ATTRIBUTE_SET WHERE (ATTRIBUTE_UPUSER !='SYS_DEFAULT')");

		if(!StringUtil.stringIsNull(tag_code)){
			sql.append(" AND ATTRIBUTE_CODE NOT IN (SELECT ATTR_CODE FROM ES_TAGS_ATTRS WHERE TAG_CODE = '")
					.append(tag_code).append("')");
		}
		log.debug("--getNotExistAttrsTree-->sql:" + sql.toString());
		try {
			attrs = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), TagRelAttributeBean.class);
			sql = null;
		} catch (Exception e) {
			log.error("标签管理->获取标签没有关联的属性失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>getNotExistAttrsTree:" + e.toString());
		}
		log.info("--getNotExistAttrsTree(over)-->attrs:" + attrs);
		return attrs;
	}

	public List<TagRelAttributeBean> getES_Attr(String tag_code) {
		log.info("--getES_Attr(start)-->tag_code:" + tag_code);
		List<TagRelAttributeBean> attrs = null;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT  attr_code  attribute_code, attr_type  attribute_type,  is_index  attribute_isindex ,attr_sys_type attribute_motel_type  FROM es_tags_attrs");
		if(!StringUtil.stringIsNull(tag_code)){//新增
			sql.append(" where TAG_CODE='").append(tag_code).append("'");
		}
		log.info("--getExistAttrsTree-->sql:" + sql.toString());
		try {
			attrs = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), TagRelAttributeBean.class);
			sql = null;
		} catch (Exception e) {
			log.error("标签管理->获取标签关联的属性失败->" , e);
			throw new DBRuntimeException("tagManageDAOIMP===>getExistAttrsTree:" + e.toString());
		}
		log.info("--getES_Attr(over)-->attrs:" + attrs);
		return attrs;
	}

	public List<TagRelAttributeBean> getExistAttrsTree(String tag_code) {
		log.info("--getExistAttrsTree(start)-->tag_code:" + tag_code);
		List<TagRelAttributeBean> attrs = null;
		StringBuffer sql = new StringBuffer();
		if(StringUtil.stringIsNull(tag_code)){//新增
			tag_code = "";
		}
		sql.append("SELECT DISTINCT t1.ATTRIBUTE_CODE,t1.ATTRIBUTE_NAME,t2.ATTR_TYPE AS ATTRIBUTE_TYPE,t2.IS_INDEX AS ATTRIBUTE_ISINDEX,t2.IGNORE_ABOVE AS ATTRIBUTE_ABOVE FROM ATTRIBUTE_SET t1,ES_TAGS_ATTRS t2 WHERE 1=1");
		sql.append(" AND t1.ATTRIBUTE_CODE IN (SELECT ATTR_CODE FROM ES_TAGS_ATTRS WHERE TAG_CODE = '")
		.append(tag_code).append("' AND ATTRIBUTE_CODE NOT IN('CONTENT_ID','FILE_NO','GROUP_ID'))");
		sql.append(" AND t1.ATTRIBUTE_CODE = t2.ATTR_CODE AND t2.TAG_CODE = '").append(tag_code).append("'");

		log.debug("--getExistAttrsTree-->sql:" + sql.toString());
		try {
			attrs = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), TagRelAttributeBean.class);
			sql = null;
		} catch (Exception e) {
			log.error("标签管理->获取标签关联的属性失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>getExistAttrsTree:" + e.toString());
		}
		log.info("--getExistAttrsTree(over)-->attrs:" + attrs);
		return attrs;
	}

	public List<TagRelAttributeBean> getAttrByID(String attr_ids) {
		log.info("--getAttrByID(start)-->attr_ids:" + attr_ids);
		List<TagRelAttributeBean> attrs = null;
		StringBuffer sql = new StringBuffer();
		String[] attrIds = attr_ids.split(",");
		sql.append(
				"SELECT ATTRIBUTE_CODE,ATTRIBUTE_NAME,'keyword' ATTRIBUTE_TYPE,ATTRIBUTE_UPUSER,ATTRIBUTE_MOTEL_TYPE FROM ATTRIBUTE_SET WHERE 1=1");
		sql.append(" AND ATTRIBUTE_CODE IN (");
		for (String attr : attrIds) {
			sql.append("'").append(attr).append("',");
		}
		sql.deleteCharAt(sql.length() - 1).append(" )");
		log.debug("--getExistAttrsTree-->sql:" + sql.toString());
		try {
			attrs = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), TagRelAttributeBean.class);
			sql = null;
		} catch (Exception e) {
			log.error("标签管理->获取属性详情失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>getAttrByID:" + e.toString());
		}
		log.info("--getAttrByID(over)-->attrs:" + attrs);
		return attrs;

	}

	public List<TagRelModelBean> getRelContentObject(String tag_code, int start, int limit) {
		log.info("--getTagModels(start)-->tag_code:" + tag_code);
		StringBuffer sql = new StringBuffer();
		if(StringUtil.stringIsNull(tag_code)){
			tag_code = "";
		}
		List<TagRelModelBean> beanList = null;
		sql.append("SELECT m.MODEL_CODE,m.MODEL_NAME,rel.STATE FROM ES_MODEL_REL rel,CONTENT_MODEL_SET m ");
		sql.append("where rel.MODEL_CODE=m.MODEL_CODE AND rel.TAG_CODE=? ");
		List list=new ArrayList();
		list.add(tag_code);

		try {
			log.debug( "--getTagModels-->sql:" + sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(pageTool
					.getPageSql(sql.toString(), start, limit),
					TagRelModelBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("标签管理->获取标签关联的内容对象失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>getRelContentObject:" + e.toString());
		}
		log.info("--getTagModels(over)-->beanList:" + beanList);
		return beanList;
	}


	public List<TagRelModelBean> getRelContentObject(String tag_code) {
		log.info("--getRelContentObject(start)-->tag_code:" + tag_code);
		StringBuffer sql = new StringBuffer();
		if(StringUtil.stringIsNull(tag_code)){//新增
			tag_code = "";
		}
		List<TagRelModelBean> beanList = null;
		sql.append("SELECT m.MODEL_CODE,m.MODEL_NAME,rel.STATE FROM ES_MODEL_REL rel,CONTENT_MODEL_SET m ");
		sql.append("where rel.MODEL_CODE=m.MODEL_CODE AND rel.TAG_CODE=? ");
		List list=new ArrayList();
		list.add(tag_code);

		try {
			log.debug( "--getRelContentObject-->sql:" + sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					TagRelModelBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("标签管理->获取标签关联的内容对象失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>getRelContentObject:" + e.toString());
		}
		log.info("--getRelContentObject(over)-->beanList:" + beanList);
		return beanList;
	}


	public List<TagRelModelBean> getUnRelContentObject(String tag_code) {
		log.info( "--getUnRelContentObject(start)-->tag_code:" + tag_code);
		List<TagRelModelBean> beanList = null;
		if(StringUtil.stringIsNull(tag_code)){
			tag_code = "";
		}
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT m.MODEL_CODE,m.MODEL_NAME FROM CONTENT_MODEL_SET m");
		sql.append(" where m.MODEL_TYPE = '0' and m.MODEL_CODE not in(");
		sql
				.append(
						"select rel.MODEL_CODE from ES_MODEL_REL rel where rel.TAG_CODE=?)");
		List list=new ArrayList();
		list.add(tag_code);
		try {
			log.debug( "--getUnRelContentObjectTree-->sql:" + sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					TagRelModelBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "标签管理->获取标签未关联的内容对象失败:" + e.toString(), e);

			throw new DBRuntimeException(
					"tagManageDAOIMP===>getUnRelContentObject:"
							+ e.getMessage());
		}
		log.info( "--getUnRelContentObject(over)-->beanList:"+beanList);
		return beanList;
	}

	public List<TagInfoBean> getAll_tagInfoBeans() {
		log.info("--getAll_tagInfoBeans(start)");
		List<TagInfoBean> list = new ArrayList<TagInfoBean>();
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT TAG_CODE,TAG_NAME,TAG_STATE FROM ES_TAGS ");
		log.debug("--getAll_tagInfoBeans-->sql:" + sql.toString());
		try {
			list = DataBaseUtil.SUNECM.queryBeanList(sql.toString(), TagInfoBean.class);
		} catch (Exception e) {
			log.error("查询所有标签" + e.toString(), e);
		}
		log.info("--getAll_tagInfoBeans(over) list：" + list);
		return list;
	}

	public boolean updateTagModels(TagInfoBean tagBean) {
		log.info("--updateTagModels(start)-->tagBean:" + tagBean);
		Collection<String> sqls = new ArrayList<String>();
		String tagCode = tagBean.getTag_code();
		String model_ids = tagBean.getModel_ids();
		String[] modelIDs = model_ids.split(",");
//		StringBuffer sql = new StringBuffer();

//		sql.append("DELETE FROM ES_MODEL_REL WHERE TAG_CODE ='").append(tagCode).append("'");
//		log.debug("--updateTagModels-->sql:" + sql.toString());
//		sqls.add(sql.toString());
		for(String mid:modelIDs){
			StringBuffer sql2 = new StringBuffer();
			sql2.append("INSERT INTO ES_MODEL_REL(TAG_CODE,MODEL_CODE,STATE)VALUES( '");
			sql2.append(tagCode).append("','");
			sql2.append(mid).append("','");
			sql2.append("1").append("')");
			log.debug("--updateTagModels-->sql:" + sql2.toString());
			sqls.add(sql2.toString());
		}

		try {
			DataBaseUtil.SUNECM.exceTrans(sqls);
		} catch (Exception e) {
			log.error("标签管理->修改标签及关联模型失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>updateTagModels:" + e.toString());
		}
		log.info("--updateTagModels(over)");
		return true;
	}

	public boolean modifyModelRelState(String tag_code, String model_code,String state) {
		log.info("--modifyModelRelState(start)-->tag_code:" + tag_code+",model_code:" + model_code+",state:" + state);
		StringBuffer sql = new StringBuffer();
		List list=new ArrayList();
		sql.append("UPDATE ES_MODEL_REL SET ").append("STATE = ? ");
		sql.append(" WHERE TAG_CODE = ? AND MODEL_CODE = ?");
		list.add(state);
		list.add(tag_code);
		list.add(model_code);

		log.debug("--modifyModelRelState-->sql:" + sql.toString());
		try {
			DataBaseUtil.SUNECM.update(sql.toString(), list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("标签管理->修改标签关联模型状态失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>modifyModelRelState:" + e.toString());
		}
		log.info("--modifyModelRelState(over)");
		return true;
	}

	public List<TagRelAttributeBean> getRelAttr(String tag_code,String tag_state) {
		log.info( "--getRelAttr(start)-->tag_code:" + tag_code +",tag_state:" + tag_state);
		List<TagRelAttributeBean> beanList = null;
		if(StringUtil.stringIsNull(tag_code)){
			tag_code = "";
		}
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT e.ATTR_CODE ATTRIBUTE_CODE,e.ATTR_TYPE ATTRIBUTE_TYPE,e.IS_INDEX ATTRIBUTE_ISINDEX,e.ATTR_SYS_TYPE ATTRIBUTE_MOTEL_TYPE,e.STATE,e.IGNORE_ABOVE attribute_above "
						+ "FROM ES_TAGS_ATTRS e WHERE TAG_CODE=?");
		List list=new ArrayList();
		list.add(tag_code);
		if(tag_state.equals(TagStateUtil.Synchronized_Modify)){//同步后修改状态
			//返回新增属性
			sql.append(" AND STATE=?");
			list.add(TagStateUtil.Synchronized_Modify);
		}
		try {
			log.debug( "--getRelAttr-->sql:" + sql.toString());
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					TagRelAttributeBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "标签管理->获取标签关联的属性失败:" + e.toString(), e);

			throw new DBRuntimeException(
					"tagManageDAOIMP===>getRelAttr:"
							+ e.getMessage());
		}
		log.info( "--getRelAttr(over)-->beanList:"+beanList);
		return beanList;
	}

	public List<TagRelModelBean> getTagRelModelBeans(String tag_code) {
		log.info("--getTagRelModelBeans(start)-->tag_code:" + tag_code);
		StringBuffer sql = new StringBuffer();
		if(StringUtil.stringIsNull(tag_code)){
			tag_code = "";
		}
		List<TagRelModelBean> tagRelModelBeans = null;
		sql.append("SELECT MODEL_CODE,TAG_CODE,STATE FROM ES_MODEL_REL where TAG_CODE= ? ");
//		sql.append("where TAG_CODE= "+tag_code);
		List list=new ArrayList();
		list.add(tag_code);
		try {
			log.debug( "--getTagRelModelBeans-->sql:" + sql.toString());
			tagRelModelBeans = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					TagRelModelBean.class,list.toArray());
			sql = null;
		} catch (Exception e) {
			log.error("标签管理->获取标签关联的内容对象失败->" + e.toString(), e);
			throw new DBRuntimeException("tagManageDAOIMP===>getTagRelModelBeans:" + e.toString());
		}
		log.info("--getTagRelModelBeans(over)-->beanList:" + tagRelModelBeans);
		return tagRelModelBeans;
	}

	public int checkTagName(String tag_code, String tag_name) {
		log.info( "--checkTagName(start)-->tag_code:" + tag_code +",tag_name:"+tag_name);
		StringBuffer sql = new StringBuffer();
		int count = 0;
		List list=new ArrayList();
		sql
				.append(
						"select count(1) from ES_TAGS g where g.TAG_NAME=? ");
		list.add(tag_name);
		if (!tag_code.equals("")) {
			sql.append(" AND TAG_CODE!=? ");
			list.add(tag_code);
		}
		try {
			log.debug( "--SunECMConsole-->tagManageDAOIMP-->checkTagName-->sql:" + sql.toString());
			count = DataBaseUtil.SUNECM.queryInt(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "标签管理->校验标签名称唯一性失败:" + e.toString(), e);

			throw new DBRuntimeException(
					"tagManageDAOIMP===>checkTagName:"
							+ e.getMessage());
		}
		log.info( "--checkTagName(over)-->count:"+count);
		return count;
	}

	public int checkTagCode(String tag_code) {
		log.info( "--checkTagCode(start)-->tag_code:" + tag_code);
		StringBuffer sql = new StringBuffer();
		int count = 0;
		List list=new ArrayList();
		sql
				.append(
						"select count(1) from ES_TAGS g where g.TAG_CODE=? ");
		list.add(tag_code);

		try {
			log.debug( "--SunECMConsole-->tagManageDAOIMP-->checkTagCode-->sql:" + sql.toString());
			count = DataBaseUtil.SUNECM.queryInt(sql.toString(),list.toArray());
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "标签管理->校验标签代码唯一性失败:" + e.toString(), e);

			throw new DBRuntimeException(
					"tagManageDAOIMP===>checkTagName:"
							+ e.getMessage());
		}
		log.info( "--checkTagCode(over)-->count:"+count);
		return count;
	}

	public boolean changeSynState(String tag_code) {
		log.info( "--changeSynState(start)-->tag_code:" + tag_code);
		if (tag_code == null || tag_code.equals("")) {
			return false;
		}
		Collection<String> sqls = new ArrayList<String>();
		StringBuffer sql1 = new StringBuffer();

		sql1.append("UPDATE ES_TAGS SET TAG_STATE = '").append(TagStateUtil.Already_Synchronized).append("'");
		sql1.append("WHERE TAG_CODE = '").append(tag_code).append("'");
		log.debug("--changeSynState-->sql:" + sql1.toString());
		sqls.add(sql1.toString());

		StringBuffer sql2 = new StringBuffer();

		sql2.append("UPDATE ES_TAGS_ATTRS SET STATE = '").append(TagStateUtil.Already_Synchronized).append("'");
		sql2.append("WHERE TAG_CODE = '").append(tag_code).append("'");
		log.debug("--changeSynState-->sql:" + sql2.toString());
		sqls.add(sql2.toString());

		try {
			DataBaseUtil.SUNECM.exceTrans(sqls);
		} catch (Exception e) {
			// 记录日志
			log.error( "标签管理->tagManageDAOIMP->changeSynState失败"
					+ e.toString());
			throw new DBRuntimeException(
					"tagManageDAOIMP===>changeSynState:"
							+ e.toString());
		}
		log.info( "--changeSynState(over)");
		return true;
	}

	public String getTagState(String tag_code) {
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT   TAG_STATE FROM   ES_TAGS ");
		sql.append(" WHERE 1=1");
			sql.append(" AND  TAG_CODE='").append(tag_code).append("'");
		String tagState;
		try {
			log.debug( "--getTagState-->sql:" + sql.toString());
			tagState = DataBaseUtil.SUNECM.queryString(sql.toString());
			sql = null;
		} catch (Exception e) {
			log.error( "标签管理->tagManageDAOIMP->查询标签状态失败" + e.toString(), e);
						throw new DBRuntimeException(
								"tagManageDAOIMP===>getTagState:"
										+ e.getMessage());
		}
		log.info( "--getTagState(over)-->tagState:"+tagState);
		return tagState;
	}

	/**
	 * 取得ES属性定义列表
	 *
	 * @return
	 */
	public List<TagRelAttTypeDefineBean> getAttributeTypeList() {
		log.info( "--getAttributeTypeList(start)");
		List<TagRelAttTypeDefineBean> attributeTypeList = null;
		StringBuffer sql = new StringBuffer();
		sql
				.append("SELECT TYPE_ID , TYPE_NAME FROM ES_ATTRIBUTE_TYPE_DEFINE d");

		try {
			log.debug( "--getAttributeTypeList-->sql:" + sql.toString());
			attributeTypeList = DataBaseUtil.SUNECM.queryBeanList(sql
					.toString(), TagRelAttTypeDefineBean.class);
			sql = null;
		} catch (Exception e) {
			// 记录日志
			log.error( "标签管理->获取ES属性类型列表失败" + e.toString());
			throw new DBRuntimeException(
					"tagManageDAOIMP===>getAttributeTypeList:"
							+ e.toString());
		}
		log.info( "--getAttributeTypeList(over)-->attributeTypeList:"+attributeTypeList);
		return attributeTypeList;
	}

}
