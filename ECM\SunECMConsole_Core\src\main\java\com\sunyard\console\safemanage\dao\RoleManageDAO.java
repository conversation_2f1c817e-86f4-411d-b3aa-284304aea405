package com.sunyard.console.safemanage.dao;

import com.sunyard.console.safemanage.bean.NodeBean;
import com.sunyard.console.safemanage.bean.RoleCmodelRelBean;
import com.sunyard.console.safemanage.bean.RoleInfoBean;

import java.util.List;
import java.util.Map;

/**
 * <p>Title: 角色管理数据库操作接口类</p>
 * <p>Description: 定义角色管理中各类数据库操作的方法</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public interface RoleManageDAO {

	/**
	 * 获取全部角色对象列表
	 * @return
	 */
	public List<Map<String,String>> getAllRoleList();
	
	/**
	 * 根据输入信息查询角色列表
	 * @param roleName	角色名称
	 * @param roleState	角色状态
	 * @param start
	 * @param limit
	 * @return			角色信息列表
	 */
	public List<RoleInfoBean> searchRoleInfoList(String roleName , String roleState , int start , int limit);
	
	/**
	 * 根据输入信息查询角色列表(取全部记录)
	 * @param roleName	角色名称
	 * @param roleState	角色状态
	 * @return			角色信息列表
	 */
	public List<RoleInfoBean> searchRoleInfoAllList(String roleName , String roleState);
	
	/**
	 * 根据输入的信息创建新的角色
	 * @param 	role	角色对象
	 * @return			是否成功
	 */
	public boolean addRole(RoleInfoBean role);
	
	/**
	 * 修改角色信息
	 * @param 	role	角色对象
	 * @return 			是否成功
	 */
	public boolean modifyRole(RoleInfoBean role);
	
	/**
	 * 启用、禁用角色
	 * @param roleIDs 	角色ID集合
	 * @param roleState 角色状态
	 * @return 			是否成功
	 */
	public boolean modifyRoleState(String roleIDs , String roleState);
	
	/**
	 * 获取角色已有的权限
	 * @param roleIDs
	 * @return
	 */
	public List<NodeBean> getExistsComponents(String roleIDs);
	
	/**
	 * 获取角色没有有的权限
	 * @param roleIDs
	 * @return
	 */
	public List<NodeBean> getNotExistsComponents(String roleIDs);
	
	/**
	 * 修改角色的权限
	 * @param roleIDs 
	 * @param componentIDs 
	 */
	public boolean updateRoleComponents(String roleIDs, String componentIDs);
	
	/**
	 * 获取角色对内容对象操作权限
	 * @param role_ids
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<RoleCmodelRelBean> getConferList(String role_id,int start,int limit);
	
	/**
	 * 获取角色对内容对象操作权限(取全部记录)
	 * @param role_ids
	 * @return
	 */
	public List<RoleCmodelRelBean> getConferAllList(String role_id);
	
	/**
	 * 配置内容对象操作权限
	 * @param role_ids
	 * @param model_codes
	 * @param permission_code
	 * @return
	 */
	public boolean configConfer(String role_id,String model_codes,String permission_code);
	/**
	 * 角色名唯一校验
	 * @param role_name 角色名
	 * @return
	 */
	public int checkRoleName(String roleName);
}
