package com.sunyard.console.configmanager.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * <p>
 * Title:xstream 内容模型与存储卷关联信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("SGroupModleSetBean")
public class SGroupModleSetBean {
	@XStreamAsAttribute
	private String GROUP_ID;
	@XStreamAsAttribute
	private String MODEL_CODE; //内容模型代码
	@XStreamAsAttribute
	private String VOLUME_ID; //启用的存储卷,多个卷用逗号隔开
	public String getModel_code() {
		return MODEL_CODE;
	}
	public void setModel_code(String modelCode) {
		MODEL_CODE = modelCode;
	}
	public String getVolume_id() {
		return VOLUME_ID;
	}
	public void setVolume_id(String volumeId) {
		VOLUME_ID = volumeId;
	}
	public String getGroup_id() {
		return GROUP_ID;
	}
	public void setGroup_id(String groupId) {
		GROUP_ID = groupId;
	}
	@Override
	public String toString() {
		return "SGroupModleSetBean [GROUP_ID=" + GROUP_ID + ", MODEL_CODE="
				+ MODEL_CODE + ", VOLUME_ID=" + VOLUME_ID + "]";
	}


}
