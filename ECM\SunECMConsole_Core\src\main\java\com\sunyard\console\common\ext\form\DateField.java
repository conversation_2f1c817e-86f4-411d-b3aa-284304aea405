package com.sunyard.console.common.ext.form;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: 日期文本框类</p>
 * <p>Description: 存放动态表单中日期型字段</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class DateField extends Field{

	/**
	 * 日期文本框宽度
	 */
	public static final String WIDTH				= "width";
	/**
	 * 允许选择的最大日期 
	 */
	public static final String MAXVALUE				= "maxValue";
	/**
	 * 当日期大于最大值时的错误提示信息 
	 */
	public static final String MAXTEXT				= "maxText";
	/**
	 * 允许选择的最小时间 
	 */
	public static final String MINVALUE				= "minValue";
	/**
	 * 当日期小于最小值时的错误提示信息 
	 */
	public static final String MINTEXT				= "minText";
	/**
	 * 日期显示格式，默认为“m/d/y”，一般使用“Y-m-d” 
	 * 
	 * Y：四位年份 
	 * m：带前缀0的月份 
	 * d：带前缀0的日期 
	 * y：两位年份 
	 * n：不带前缀0的月份 
	 * j：不带前缀0的日期 
	 * w：星期的数字，0表示星期日，1代表星期一
	 */
	public static final String FORMAT				= "format";
	/**
	 * 是否显示今天按钮，默认为true 
	 */
	public static final String SHOWTODAY			= "showToday";
	/**
	 * 多个日期输入格式组成的字符串，不同的格式之间使用“|”
	 * 进行分割，默认值为'm/d/Y|n/j/Y|n/j/y|m/j/y|n/d/y|m/j/Y|n/d/Y|
	 * m-d-y|m-d-Y|m/d|m-d|md|mdy|mdY|d|Y-m-d' 
	 */
	public static final String ALTFORMATS			= "altFormats";
	/**
	 * 禁止选择的日期组成的数组 
	 */
	public static final String DISABLEDDATES		= "disabledDates";
	/**
	 * 选择禁选日期时显示的提示信息 
	 */
	public static final String DISABLEDDATESTEXT	= "disabledDatesText";
	/**
	 * 禁止选择的星期组成的数组，0代表星期日，1代表星期一 
	 */
	public static final String DISABLEDDAYS		= "disabledDays";
	/**
	 * 选择禁选星期时显示的提示信息 
	 */
	public static final String DISABLEDDAYSTEXT	= "disabledDaysText";
	/**
	 * 当日期值非法时显示的提示信息 
	 */
	public static final String INVALIDTEXT		= "invalidText";
	/**
	 * 标签类型
	 */
	public static final String	XTYPE			= "xtype";
	
	private Map<String,Object> dateField = new HashMap<String,Object>();
	
	public void setProperties(String key , String value){
		dateField.put(key, value);
	}
	public void setProperties(String key , int value){
		dateField.put(key, value);
	}
	public void setProperties(String key , boolean value){
		dateField.put(key, value);
	}
	public void setProperties_obj(String key , String value){
		dateField.put(key, "#"+value+"#");
	}
	
	public Map<String,Object> getDateField(){
		if(dateField != null && dateField.get("xtype") == null){
			dateField.put("xtype", "datefield`");
		}
		return dateField;
	}
	/**
	 * 获取某个属性
	 * @param key
	 * @return
	 */
	public Object getProperties(String key){
		return dateField.get(key);
	}
	


}
