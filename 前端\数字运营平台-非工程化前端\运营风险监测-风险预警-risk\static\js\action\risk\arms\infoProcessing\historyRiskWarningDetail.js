/**
 * 历史预警详情页面
 */
var history_risk_warning_detail = new Vue({
    el: "#history-risk-warning-detail",
    data: {
        title: "", // 页面标题
        modelName: "",
        // 添加导出按钮配置
        buttons: [
            {'type': 'button', 'name': '导出', 'icon': 'sign-out', 'id':'history-risk-warning-export-btn', 'class': 'btn-primary', 'method': 'exportData()'}
        ],
        form: {
            form_id: "history-risk-warning-detail-fm",
            form_class: "ars-form",
            form_data: [],
            buttons: [
                {
                    type: 'button',
                    name: '查询',
                    icon: 'search',
                    id: 'history-risk-warning-detail-query-btn',
                    class: 'ars-btn btn-primary',
                    method: 'queryDetailData()'
                }
            ]
        },
        // 添加表格配置，参考 queryList.js
        table: {
            table_id: 'history-detail-table', // 当前页表格ID
            page_id: 'history-detail-table-page', // 表格页码ID
            table_column: [], // 表格列
            table_data: [], // 表格数据
            current_page: 1 // 当前页码
        },
        variable: {
            modelId: "", // 模型ID
            ishandle: "", // 处理状态
            showFilter: false, // 是否显示筛选区域
            isInit: true, // 是否初始化表格
            searchFields: [], // 搜索字段
            findFields: {}, // 查找字段
            imgFields: [] // 用于存储看图相关字段配置
        },
        modelInfo: {} // 模型信息
    },
    methods: {
        /**
         * 将字符串转换为函数
         */
        callFn:function(item) {
            var reg1 = /^\w+/g;
            var reg2 = /\(((.|)+?)\)/; //取小括号中的参数
            var fn = item.match(reg1)[0];
            var args = item.match(reg2)[1];
            if(commonBlank(args)){ //函数无参数
                this[fn].apply(this); //调用函数
            }else { //函数有参数
                this[fn].apply(this,args.split(',')); //调用函数
            }
        },

        /**
         * 表单显示隐藏
         */
        formShow: function(event) {
            var obj = event.currentTarget;
            $.CurrentNavtab.find(".cont-container").css("background", "#fff");
            if(this.variable.showFilter) {
                $(obj).closest(".cont-header").find("form").slideUp(10, function() {
                    var header_height = $(history_risk_warning_detail.$options.el).find(".cont-header").height();
                    $(history_risk_warning_detail.$options.el).find(".cont-container").css('top', header_height);
                    $(history_risk_warning_detail.$options.el).find(".cont-container").css('height', $(history_risk_warning_detail.$options.el).height() - header_height - 35 + 'px');
                    $(window).trigger(BJUI.eventType.resizeGrid);
                });
                this.variable.showFilter = false;
            } else {
                $(obj).closest(".cont-header").find("form").slideDown(10, function() {
                    var header_height = $(history_risk_warning_detail.$options.el).find(".cont-header").height();
                    $(history_risk_warning_detail.$options.el).find(".cont-container").css('top', header_height);
                    $(history_risk_warning_detail.$options.el).find(".cont-container").css('height', $(history_risk_warning_detail.$options.el).height() - header_height - 35 + 'px');
                    $(window).trigger(BJUI.eventType.resizeGrid);
                });
                this.variable.showFilter = true;
            }
        },

        /**
         * 返回上一页
         */
        goBack: function() {
            BJUI.ajax('doload', {
                url: 'static/html/risk/arms/infoProcessing/historyRiskWarningList.html',
                target: arms_main.main_nav.target
            });
        },

        /**
         * 初始化页面
         */
        initPage: function() {
            // 获取模型ID
            this.variable.modelId = arms_main.variable.show_modelId;

            // 获取处理状态
            this.variable.ishandle = arms_main.variable.base_arms_ishandle;

            // 获取看图字典配置
            this.variable.imgFields = commonFieldData(commonConst("IMAGE_DIC"));

            // 从首页传递的模型信息中获取模型信息
            this.getModelInfo();

            // 添加按钮到页面右下角
            var buttonHtml = '<div class="btn-group" style="position:absolute; bottom:10px; right:15px; z-index:100;">';
            for(var i=0; i<this.buttons.length; i++) {
                var button = this.buttons[i];
                buttonHtml += '<button type="' + button.type + '" id="' + button.id + '" class="btn ' + button.class + '"';
                if(button.method) {
                    buttonHtml += ' onclick="history_risk_warning_detail.callFn(\'' + button.method + '\')"';
                }
                buttonHtml += '><i class="fa fa-' + button.icon + '"></i> ' + button.name + '</button> ';
            }
            buttonHtml += '</div>';

            // 将按钮添加到页面
            $('#history-risk-warning-detail').find('.cont-container').append(buttonHtml);

            // 然后获取表格字段信息
            var self = this;
            setTimeout(function() {
                self.getTableFields();
            }, 100);
        },

        /**
         * 获取模型信息
         */
        getModelInfo: function() {
            // 从arms_main中获取模型信息
            if (arms_main.variable.modelInfoList && arms_main.variable.modelInfoList.length > 0) {
                // 遍历模型列表，查找当前模型ID对应的信息
                for (var i = 0; i < arms_main.variable.modelInfoList.length; i++) {
                    var model = arms_main.variable.modelInfoList[i];
                    if (model.model_id == this.variable.modelId) {
                        // 找到匹配的模型信息
                        this.modelName = model.model_name;

                        // 设置页面标题
                        this.title = model.model_name /* + "--历史预警数据" */;

                        // 保存模型信息到Vue实例
                        this.modelInfo = {
                            "tableName": model.table_name,
                            "modelId": this.variable.modelId,
                            "modelName": model.model_name
                        };
                        return;
                    }
                }
            }
        },

        /**
         * 获取表格字段信息
         */
        getTableFields: function() {
            // 确保已经有模型信息
            if (!this.modelInfo) {
                commonError("模型信息不存在，请先获取模型信息");
                return;
            }

            var msg = {
                "parameterList": [{
                    "model_id": this.variable.modelId,
                    "ishandle": this.variable.ishandle,
                    "is_history": "1",
                    "modelInfo": this.modelInfo // 添加模型信息到请求参数
                }],
                "sysMap": {}
            };

            var response = commonGet(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getHistoryRiskWarningDetailData.do", $.toJSON(msg));
            if(response.retCode == commonConst('HANDLE_SUCCESS')) {
                var fields = response.retMap.exhibitFields || [];

                // 处理看图字段，为imgFields添加mappingField属性
                if(this.variable.imgFields && this.variable.imgFields.length > 0) {
                    // 直接设置默认属性
                    $.each(this.variable.imgFields, function(index, imgField) {
                        // 默认使用value作为mappingField
                        imgField.mappingField = imgField.value;
                        imgField.imageFlag = true;
                    });
                }

                // 设置查询字段
                this.setSearchFields(fields);

                // 设置表格列
                this.setTableColumns(fields);

                // 加载表格数据
                this.loadTableData();
            } else {
                commonError(response.retMsg);
            }
        },

        /**
         * 设置查询字段
         */
        setSearchFields: function(fields) {
            // 清空查询字段
            this.form.form_data = [];
            this.variable.findFields = {};

            // 添加查询字段
            for(var i=0; i<fields.length; i++) {
                var field = fields[i];
                // 确保字段名称存在
                var fieldName = field.NAME || field.name;
                var chName = field.CH_NAME || field.ch_name;
                var isFind = field.IS_FIND || field.is_find;
                
                if(!fieldName || !chName) {
                    console.warn("跳过无效查询字段:", field);
                    continue;
                }
                
                if(isFind === '1') { // 可查询字段
                    this.form.form_data.push({
                        'type': 'input',
                        'name': chName,
                        'id': 'history_filter_' + fieldName,
                        'value': '',
                        'alias': fieldName
                    });
                    this.variable.findFields[fieldName] = true;
                }
            }
        },

        /**
         * 设置表格列
         */
        setTableColumns: function(fields) {
            var self = this;
            
            // 表格列
            var columns = [
                {
                    name: 'num',
                    label: '序号',
                    align: 'center',
                    width: 40,
                    finalWidth: true,
                    render: function(value, data) {
                        return ((self.table.current_page-1)*20)+data.gridIndex + 1;
                    }
                },
                {
                    name: 'OP',
                    label: '操作',
                    align: 'center',
                    width: 80,
                    finalWidth: true,
                    render: function(value, rowData) {
                        var html = '';
                        // 直接添加看图按钮，不做条件判断
                        html += ' <a title="查看影像" data-row-index="' + rowData.gridIndex +
                            '" onclick="history_risk_warning_detail.showImage(this)">看图</a>';
                        return html;
                    }
                }
            ];

            // 添加模型字段
            for(var i=0; i<fields.length; i++) {
                var field = fields[i];
                
                // 确保字段名称存在，尝试多种可能的属性名
                var fieldName = field.NAME || field.name;
                var chName = field.CH_NAME || field.ch_name;
                var align = field.ALIGN || field.align || 'center';
                var width = field.WIDTH || field.width || 80;
                
                // 如果字段名称不存在，则跳过
                if(!fieldName || !chName) {
                    console.warn("跳过无效字段:", field);
                    continue;
                }

                // 排除一些系统字段
                if(fieldName === 'MODEL_ID' || fieldName === 'MODELROW_ID' || fieldName === 'ISHANDLE') {
                    continue;
                }

                // 使用闭包保存当前字段名
                (function(name) {
                    columns.push({
                        name: name,
                        label: chName,
                        align: align,
                        width: width,
                        finalWidth: true,
                        render: function(value) {
                            return value || '';
                        }
                    });
                })(fieldName);
            }

            // 添加检查结果列
            columns.push({
                name: 'CHECK_RESULT',
                label: '检查结果',
                align: 'center',
                width: 80,
                finalWidth: true,
                render: function(value, row) {
                    console.log("检查结果:", row.ISHANDLE);
                    if(row.ISHANDLE === '1') {
                        return '<span class="label label-success">通过</span>';
                    } else if(row.ISHANDLE === '2') {
                        return '<span class="label label-danger">差错</span>';
                    } else {
                        return '';
                    }
                }
            });

            // 设置表格列
            this.table.table_column = columns;
        },

        /**
         * 加载表格数据
         */
        loadTableData: function() {
            // 确保已经有模型信息
            if (!this.modelInfo) {
                commonError("模型信息不存在，请先获取模型信息");
                return;
            }

            var params = {
                "model_id": this.variable.modelId,
                "ishandle": this.variable.ishandle,
                "is_history": "1",
                "modelInfo": this.modelInfo // 添加模型信息
            };

            // 添加查询条件
            this.form.form_data.forEach(function(field) {
                var value = $('#' + field.id).val();
                if(value) {
                    params[field.alias] = value;
                }
            });

            var msg = {
                "parameterList": [params],
                "sysMap": {}
            };

            var response = commonGet(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getHistoryRiskWarningDetailData.do", $.toJSON(msg));
            if(response.retCode == commonConst('HANDLE_SUCCESS')) {
                var tableData = response.retMap.historyDetailData || [];
                
                // 确保每条数据都有gridIndex属性
                for(var i=0; i<tableData.length; i++) {
                    tableData[i].gridIndex = i;
                }

                this.table.table_data = tableData;

                // 初始化表格
                this.initTable();
            } else {
                commonError(response.retMsg);
            }
        },

        /**
         * 初始化表格
         */
        initTable: function() {
            // 判断是初始化还是刷新
            if(this.variable.isInit) {
                commonInitDatagrid(this.table.table_id, {
                    dialogFilterW: 10,
                    showCheckboxcol: false,
                    columns: this.table.table_column,
                    data: this.table.table_data,
                    showPager: true,
                    rowNum: 20
                });
                this.variable.isInit = false;
                
                // 初始化后设置当前页码
                this.table.current_page = 1;
            } else {
                $("#" + this.table.table_id).datagrid('reload', {data: $.toJSON(this.table.table_data)});
            }
        },

        /**
         * 查询明细数据
         */
        queryDetailData: function() {
            this.loadTableData();
        },

        /**
         * 导出数据
         */
        exportData: function() {
            // 确保已经有模型信息
            if (!this.modelInfo) {
                commonError("模型信息不存在，请先获取模型信息");
                return;
            }

            // 导出按钮
            var formatField = 'occur_date#%yyyy-MM-dd,operator_no#@OPERATOR_NO';
            $('#history-risk-warning-export-btn').selectExport('init', {
                targetId: this.table.table_id,                          // 需要导出的datagrid控件id
                columns: this.table.table_column,                       // 需要导出的datagrid控件列信息定义
                menu_id: arms_main.variable.menu_id,
                button_id: 'history-risk-warning-export-btn',           // 导出按钮ID
                allRecordsHandler: function() {                         // 将上次查询出来的条数传给导出插件
                    return history_risk_warning_detail.table.table_data;
                },
                exportTitle: '历史预警信息',
                exportDataUrl: commonConst("SUNDA_RISK") + '/armsExhibitDataController/exportHistoryRiskWarningData',  // 导出数据url
                exportFormatvalue: formatField,
                getParamHandler: function() {
                    // 导出数据时需要传递的参数
                    var params = {
                        "model_id": history_risk_warning_detail.variable.modelId,
                        "ishandle": history_risk_warning_detail.variable.ishandle,
                        "is_history": '1',
                        "modelInfo": history_risk_warning_detail.modelInfo
                    };

                    // 添加查询条件
                    history_risk_warning_detail.form.form_data.forEach(function(field) {
                        var value = $('#' + field.id).val();
                        if(value) {
                            params[field.alias] = value;
                        }
                    });
                    
                    // 构造符合后端要求的参数格式
                    var requestParams = {
                        "parameterList": [params],
                        "sysMap": {}
                    };

                    return '&params=' + encodeURIComponent($.toJSON(requestParams));
                },
                subMenuLabels: '当前页数据,,,全部数据,',
                noExportColumn: '序号,操作',
                exportFileName: '历史预警数据_' + history_risk_warning_detail.modelName + "_" + commonCurrentDateStr() + '.xls',
                direction: 'up',                               // 导出选项弹出方向，支持 向上（up）或 向下（down）
                horizontalDirection: 'right'                   // 导出选项的水平位置，支持右对齐(right),默认不写为左对齐(left)
            });
        },

        /**
         * 查看影像
         * @param element 点击的按钮元素
         */
        showImage: function(element) {
            try {
                // 获取行索引
                var rowIndex = $(element).data("row-index");

                // 直接从Vue实例的表格数据中获取完整的行数据
                var rowData = this.table.table_data[rowIndex];

                if (!rowData) {
                    commonError("获取影像数据失败");
                    return;
                }

                // 调用新的预警看图详情功能
                this.openRiskWarningImageDetail(rowData);
            } catch (err) {
                commonError("查看影像失败: " + err.message);
            }
        },

        /**
         * 打开预警看图详情
         * @param rowData 行数据
         */
        openRiskWarningImageDetail: function(rowData) {
            // 获取模型ID
            var modelId = rowData.MODEL_ID || this.variable.modelId;
            var modelRowId = rowData.MODELROW_ID || rowData.MODEL_ROW_ID;
            var busiDataDate = rowData.BUSI_DATA_DATE || rowData.OCCUR_DATE;

            // 验证必要参数
            if (!modelId) {
                commonError("模型ID不能为空");
                return;
            }
            if (!modelRowId) {
                commonError("模型行ID不能为空");
                return;
            }

            console.log("看图参数：", {
                model_id: modelId,
                model_row_id: modelRowId,
                busi_data_date: busiDataDate
            });

            var msg = {
                "parameterList": [{
                    "model_id": modelId,
                    "model_row_id": modelRowId,
                    "busi_data_date": busiDataDate
                }],
                "sysMap": {}
            };

            var response = commonPost(commonConst("SUNDA_RISK")+"/armsExhibitDataController/getRiskWarningImageDetail.do", $.toJSON(msg));

            console.log("后端响应：", response);

            if (response.retCode == commonConst("HANDLE_SUCCESS")) {
                // 检查是否有图像数据（学习老系统的处理方式）
                var imageData = response.retMap;
                var tmpDataList = imageData.tmpDataList || [];
                var flowList = imageData.flowList || [];

                if (tmpDataList.length === 0 && flowList.length === 0) {
                    // 没有图像数据，显示提示信息（与老系统一致）
                    commonError("未查询到相应的流水信息，无法看图");
                    return;
                }

                this.displayRiskWarningImageDialog(response.retMap);
            } else {
                // console.error("获取看图数据失败：", response.retMsg);
                commonError(response.retMsg);
            }
        },

        /**
         * 处理凭证数据，添加从1开始的递增序号
         * @param dataList 原始数据列表
         * @returns {Array} 处理后的数据列表
         */
        processVoucherData: function(dataList) {
            if (!dataList || dataList.length === 0) {
                return [];
            }

            // 为每条记录添加显示序号，从1开始递增
            return dataList.map(function(item, index) {
                return Object.assign({}, item, {
                    displayIndex: index + 1 // 从1开始的递增序号
                });
            });
        },

        /**
         * 显示预警看图对话框
         * @param data 影像数据
         */
        displayRiskWarningImageDialog: function(data) {
            var flowInfo = data.flowList;
            var npsFlag = flowInfo && flowInfo.length > 0 ? flowInfo[0].NPS_FLAG : null;

            if (!commonBlank(npsFlag) && npsFlag == 1) {
                // 无纸化看图
                np_showFlow(data.flowFields, flowInfo);
                return;
            }

            // 预警看图对话框配置
            common_dialog = {
                'dialog_img': {
                    'title': '预警看图处理',
                    'url': 'static/html/risk/arms/dialog/riskWarningShowImg.html',
                    'dialog_id': 'ars-risk-warning-image-dialog',
                    'table_voucher': {
                        'table_data': this.processVoucherData(data.tmpDataList || []),
                        'table_column': [
                            {name: 'displayIndex', label: '图像序号', align: 'center', width: 50},
                            {name: 'formName', label: '版面名称', align: 'center', width: 100},
                            {name: 'flowId', label: '业务流水', align: 'center', width: 100},
                            {name: 'batchId', label: '批次号', align: 'center', width: 100},
                            {name: 'inccodeinBatch', label: '批内码', align: 'center', width: 80},
                            {name: 'errorFlag', label: '差错标志', align: 'center', width: 80},
                            {name: 'insertDate', label: '录入日期', align: 'center', width: 100},
                            {name: 'fileName', label: '文件名', align: 'center', width: 150}
                        ]
                    },
                    'table_flow': {
                        'table_data': flowInfo || []
                    },
                    'variable': {
                        'image_data': data.tmpDataList || [],
                        'flow_data': flowInfo || [],
                        'batch_data': data.batchList || [],
                        'business_data': data.businessData,
                        'model_info': data.modelInfo,
                        'showOperations': true,
                        'modelId': data.businessData ? (data.businessData.model_id || data.businessData.MODEL_ID) : '',
                        'modelRowId': data.businessData ? (data.businessData.modelrow_id || data.businessData.MODELROW_ID) : '',
                        'tableName': data.tableName || '',
                        'warningData': data.businessData || {},
                        // 添加看图需要的变量
                        'img_batchId': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].batchId : '',
                        'img_inputDate': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].inputDate : '',
                        'img_fileName': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].fileName : '',
                        'img_backFileName': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].backFileName : '',
                        'img_siteNo': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].siteNo : '',
                        'img_contentId': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].contentId : '',
                        'img_imgType': '1',
                        'img_serialNo': data.tmpDataList && data.tmpDataList.length > 0 ? data.tmpDataList[0].serialNo : ''
                    }
                }
            };

            // 构建流水表列配置
            var fl_field_col = "";
            if (data.flowFields && data.flowFields.length > 0) {
                for (var i = 0; i < data.flowFields.length; i++) {
                    fl_field_col += ",{name:'" + data.flowFields[i].FIELD_NAME +
                                   "',label:'" + data.flowFields[i].ELSE_NAME +
                                   "',align:'center',finalWidth:true}";
                }
            } else {
                // 如果没有flowFields，使用默认的列配置
                fl_field_col += ",{name:'OCCUR_DATE',label:'发生日期',align:'center',finalWidth:true}";
                fl_field_col += ",{name:'SITE_NO',label:'机构号',align:'center',finalWidth:true}";
                fl_field_col += ",{name:'OPERATOR_NO',label:'操作员号',align:'center',finalWidth:true}";
                fl_field_col += ",{name:'FLOW_ID',label:'流水号',align:'center',finalWidth:true}";
            }
            common_dialog.dialog_img.table_flow.table_column = "[" + fl_field_col.substring(1) + "]";

            // 打开对话框
            arsCommonDialog({
                title: common_dialog.dialog_img.title,
                id: common_dialog.dialog_img.dialog_id,
                url: common_dialog.dialog_img.url,
                max: true,
                onLoad: function ($dialog) {
                    // 对话框加载完成后的处理
                }
            }, 13);
        }
    },
    mounted: function() {
        this.$nextTick(function() {
            try {
                mainCont.subPageSize($(this.$options.el)); // 重置页面布局
                this.initPage(); // 初始化页面
                // this.exportData(); // 初始化导出功能
                
                // 添加表格分页事件监听
                var self = this;
                $("#history-detail-table").on('afterChange.bjui.datagrid', function() {
                    self.table.current_page = $(this).data('page') || 1;
                });
            } catch (err) {
                commonError(err.name + '：' + err.message, '前台执行异常');
            }
        });
    }
});
