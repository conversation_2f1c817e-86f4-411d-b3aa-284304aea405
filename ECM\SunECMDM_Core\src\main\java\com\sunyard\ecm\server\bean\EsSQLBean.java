package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;

@XStreamAlias("EsSQLBean")
public class EsSQLBean extends SQLBean{
    
	private int AffectedNum =0;
	
	private int IsIndex = 0;//1为索引sql,0为文档sql
	
	public int getAffectedNum() {
		return AffectedNum;
	}

	public int getIsIndex() {
		return IsIndex;
	}

	public void setIsIndex(int isIndex) {
		IsIndex = isIndex;
	}

	public void setAffectedNum(int affectedNum) {
		AffectedNum = affectedNum;
	}

}
