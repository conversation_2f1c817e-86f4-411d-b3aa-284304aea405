package com.sunyard.ecm.common.trans.socket;

import com.sunyard.ecm.util.net.ServerSocketFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class Server implements Runnable {
	private final static Logger log = LoggerFactory.getLogger(Server.class);
	private volatile ExecutorService pool = null;
	private volatile ServerSocket serverSocket = null;
	private volatile boolean isRun = false;
	private ServerSocketFactory serverSocketFactory;
	private String serverName;
	private String connectionType;
	
	private int port;

	public Server(ServerSocketFactory serverSocketFactory, int port,
			int poolSize) throws IOException, InstantiationException {
		this.port = port;
		this.serverSocketFactory = serverSocketFactory;
		this.pool = Executors.newFixedThreadPool(poolSize);
		isRun = true;
	}

	public void run() {
		try {
			serverSocket = serverSocketFactory.createSocket(port);
			log.info(getServerName() + " start listen at " + port);
		} catch (Exception e1) {
			isRun = false;
			return;
		}
		while (isRun) {
			try {
				Socket socket = serverSocketFactory.acceptSocket(serverSocket);
				log.debug("{} Accept a new connect from :{}",getServerName()
						, socket.getRemoteSocketAddress());
				SocketConnection con = new SocketConnection(socket);
				con.setConnectionType(connectionType);
				pool.execute(con);
			} catch (IOException e) {
				if (isRun) {
					log.error("接受新连接失败", e);
				} else {
					log.info("Stop the " + getServerName() + " success.");
				}
			}
		}

	}

	public void stop() {
		this.isRun = false;
		try {
			log.info("stop the socket");
			if (serverSocket != null) {
				serverSocket.close();
			}
		} catch (Exception e) {
			log.error("stop the socket get Exception", e);
		} finally {
			try {
				log.info("shutdown the pool");
				pool.shutdown();
			} catch (RuntimeException e) {
				log.error("stop the pool get Excption", e);
			}
		}
		log.info("Server stop over");
	}

	public boolean isRun() {
		return isRun;
	}

	public String getServerName() {
		return serverName;
	}

	public void setServerName(String serverName) {
		this.serverName = serverName;
	}

	public void setConnectionType(String connectionType) {
		this.connectionType = connectionType;
	}

}