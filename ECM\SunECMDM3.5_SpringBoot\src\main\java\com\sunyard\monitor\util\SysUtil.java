package com.sunyard.monitor.util;

import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 一些系统工具 
 */
public class SysUtil {
	private final static  Logger log = LoggerFactory.getLogger(SysUtil.class);

	/**
	 * 通过命令行获取本地信息
	 *
	 * @param String equ
	 * @return String
	 * @throws IOException
	 * */
	public static String execDos(String order) throws IOException {
		if (StringUtil.isBlank(order)) {
			return null;
		}
		Process pro = Runtime.getRuntime().exec(order);
		if (null == pro) {
			return null;
		}
		Reader r = null;
		String result = "";
		BufferedReader br = null;
		try{
			r = new InputStreamReader(pro.getInputStream());
			br = new BufferedReader(r);
			String str = br.readLine();
			while (null != str) {
				result += str + "\n";
				str = br.readLine();
			}
		}catch(Exception e){
			log.error("",e);
		}finally{
			if(r != null){
				r.close();
				br.close();
			}
		}
		return result;
	}

	/**
	 * 根据提供的节点名称,从XML文件中读取相应的值
	 *
	 * @param String node 节点名称
	 * @return Map<String, String> 返回节点值
	 * */
	@SuppressWarnings("unchecked")
	public static Map<String, String> getXml(String xpath) {
		if (StringUtil.isBlank(xpath)) {
			return null;
		}
		InputStream is = null;
		Map<String, String> map = new HashMap<String, String>();
		SAXReader sax = new SAXReader();
		Document doc = null;
		try {
			try {
				ClassLoader aa = SysUtil.class.getClassLoader();
				if (aa == null) {
					return map;
				}
				is = aa.getResourceAsStream("config-morit.xml");
				doc = sax.read(is);
				List<Element> list = doc.getRootElement().elements(xpath);
				if (null == list || 0 >= list.size()) {
					return null;
				}
				for (Element e : list) {
					if (null == e || StringUtil.isBlank(e.getName())) {
						continue;
					}
					map.put(e.getName(), e.getText());
				}
			}finally{
				if(is != null) is.close();
			}
		} catch (Exception e1) {
			log.error("出错");
		}
		return map;
	}

	/**
	 * 保留指定位数的小数(最后一位四舍五入)
	 *
	 * @param Float f 待处理数
	 * @param int n 保留小数位数
	 * @return float
	 * */
	public static float mathRound(float f, int n) {
		if (-1 >= n) {
			return f;
		}
		String xs = "#0";
		for (int i = 0; i < n; i++) {
			if (0 == i) {
				xs += ".";
			}
			xs += "0";
		}
		DecimalFormat df = new DecimalFormat(xs);
		float result = Float.valueOf(df.format(f));
		return result;
	}

	/**
	 * SNMP传输协议中把十六进制转换成中文
	 * @param String input 十六进制字符串
	 * @return String 返回转换后的字符串
	 * */
	public String exchange(String input) {
		String result;
		StringBuffer sb = new StringBuffer();
		String hexs = input;
		String[] sa = hexs.split(":");
		for (String s : sa) {
			char c = (char) (Integer.parseInt(s, 16));
			sb.append(c);
		}
		result = sb.toString();
		try {
			result = new String(result.getBytes("ISO-8859-1"), "gb2312");
		} catch (UnsupportedEncodingException e) {
			log.error("出错");
		}
		return result;
	}

}