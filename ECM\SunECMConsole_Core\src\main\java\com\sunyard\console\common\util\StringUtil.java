package com.sunyard.console.common.util;

import com.sunyard.console.common.config.LoadConfigFile;
import com.sunyard.console.configmanager.wsserviceutil.CodeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;

public class StringUtil {
	private final static Logger log = LoggerFactory.getLogger(StringUtil.class);

	public static List<String> stringToList(String str, String regex) {
		List<String> list = new ArrayList<String>();
		// str不能为空
		if (!"".equals(str) || null != str) {
			String[] arr = str.split(regex);

			for (int i = 0; i < arr.length; i++) {
				if (!"".equals(arr[i])) {
					list.add(arr[i]);
				}
			}
		}

		return list;
	}

	/**
	 * 将字符串的分割换为新的分割并用单引号引起来
	 * 
	 * @param str
	 *            为 2,1,5,
	 * @param oldRegex
	 *            为,
	 * @param newRegex
	 *            为:
	 * @return 则返回2':'1':'5'
	 */
	public static String stringToString(String str, String oldRegex, String newRegex) {
		if (str == null) {
			return null;
		}
		String[] b = str.split(oldRegex);
		StringBuffer strBuf = new StringBuffer();
		for (int i = 0; i < b.length; i++) {
			if (i != 0) {
				strBuf.append(newRegex);
			}
			strBuf.append("'").append(b[i]).append("'");
		}
		return strBuf.toString();
	}

	/**
	 * 比较两个list，返回第2个比第一个多的元素组成的list
	 * 
	 * @param list
	 *            3 2 8 5 1
	 * @param list2
	 *            1 5 4
	 * @return 4 list类型为list2
	 */
	public static List compareList(List list, List list2) {
		if (list == null) {
			return list2;
		}
		if (list2 == null) {
			return new ArrayList();
		}
		List diffList = new ArrayList();
		diffList.addAll(list2);
		for (int i = 0; i < list.size(); i++) {
			for (int j = 0; j < list2.size(); j++) {
				if (list2.get(j).equals(list.get(i))) {
					diffList.remove(list2.get(j));
					break;
				}
			}
		}
		return diffList;
	}

	/**
	 * 组装DM的WebService URL
	 * 
	 * @param ipAndPort
	 * @return http://ip:port/SunECMDM/webservices/WsInterface
	 */
	public static String assemblyDMUrl(String ipAndPort, String type) {
		// "http://************:8080/SunECMDM/webservices/WsInterface";
		if (stringIsNull(ipAndPort)) {
			log.error("ip is null");
			return null;
		}
		String[] str = ipAndPort.split(":");
		if (str.length != 2) {
			log.error("ip or port is error:" + ipAndPort);
			return null;
		}
		String ip = str[0];
		String port = str[1];
		ip = CodeUtil.changeIp(ip);
		ipAndPort = ip + ":" + port;
		StringBuffer urlBuffer = setUrlHead(ipAndPort, type);
		urlBuffer.append("/").append(LoadConfigFile.getConfigBean().getServer_DM_Name());
		urlBuffer.append("/webservices/WsInterface");
		return urlBuffer.toString();
	}

	/**
	 * 组装DM的WebService URL
	 * 
	 * @param ipAndPort
	 * @return http://ip:port/SunECMDM/webservices/WsInterface
	 */
	public static String assemblyUAUrl(String ipAndPort, String type) {
		// "http://************:8080/SunECMDM/webservices/WsInterface";
		if (stringIsNull(ipAndPort)) {
			log.error("ip is null");
			return null;
		}
		String[] str = ipAndPort.split(":");
		if (str.length != 2) {
			log.error("ip or port is error:" + ipAndPort);
			return null;
		}
		String ip = str[0];
		String port = str[1];
		ip = CodeUtil.changeIp(ip);
		ipAndPort = ip + ":" + port;
		StringBuffer urlBuffer = setUrlHead(ipAndPort, type);
		urlBuffer.append("/").append(LoadConfigFile.getConfigBean().getServer_UA_Name());
		urlBuffer.append("/webservices/WsInterface");
		return urlBuffer.toString();
	}

	public static StringBuffer setUrlHead(String ipAndPort, String type) {
		StringBuffer urlBuffer = new StringBuffer();
		if ("https".equals(type)) {
			urlBuffer.append("https://").append(ipAndPort);
		} else {
			urlBuffer.append("http://").append(ipAndPort);
		}
		return urlBuffer;
	}

	/**
	 * 校验str是否为空，为空返回true ，不为空返回false
	 * 
	 * @param str
	 * @return
	 */
	public static boolean stringIsNull(String str) {
		if (str == null || str.equals("") || str.equals("null") || str.equals("NULL")) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 生成随机数
	 * 
	 * @param length
	 *            随机数个数
	 * @return
	 * @throws NoSuchAlgorithmException
	 */
	public static String getRandomStr(int length) throws NoSuchAlgorithmException {
		String str = "abcdefghijklmnopqrstuvwxyz";
		StringBuffer sb = new StringBuffer();
		int len = str.length();
		int num;
		SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
		for (int i = 0; i < length; i++) {
			num = (int) Math.round(secureRandom.nextDouble() * (len - 1));
			sb.append(str.charAt(num));
		}
		return sb.toString();
	}
}
