package com.sunyard.console.singletonManage.action;

import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.sunyard.console.singletonManage.bean.LazySingletonBean;
import com.sunyard.console.singletonManage.dao.SingletonManageDAO;
import com.sunyard.ecm.server.bean.LifeCycleStrategyBean;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class SingletonManageAction extends BaseAction {
	
	private final static  Logger log = LoggerFactory.getLogger(SingletonManageAction.class);
	@Autowired
	private SingletonManageDAO singletonManageDao;

	@ResponseBody
	@RequestMapping(value = "/singletonManage/getSingletonInfoAction.action", method = RequestMethod.POST)
	public String getSingletonInfoSearch(String data) {
		log.info( "--getSingletonInfoSearch(start)");
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		JSONObject jsonObj = new JSONObject();
		JSONObject modelJson = JSONObject.fromObject(data);
        String ip = (String) modelJson.getOrDefault("server_ip","");
		String port = (String) modelJson.getOrDefault("http_port","");
		try {
			LazySingletonBean lazy = singletonManageDao.getDMInfo(ip,port);
			log.debug( "--getSingletonInfoSearch-->" );
			jsonObj.put("server_ip", lazy.getSERVER_IP());
			jsonObj.put("server_id", lazy.getSERVER_ID());
			jsonObj.put("server_name", lazy.getSERVER_NAME());
			jsonObj.put("server_status", lazy.getSERVER_STATUS());
			jsonObj.put("group_name",lazy.getGROUP_NAME());
			jsonObj.put("group_id",lazy.getGROUP_ID());
			jsonObj.put("group_state",lazy.getGROUP_STATE());
			jsonObj.put("group_os",lazy.getGROUP_OS());
			jsonObj.put("deploy_mode",lazy.getDEPLOY_MODE());
			jsonObj.put("http_port",lazy.getHTTP_PORT());
			jsonObj.put("socket_port",lazy.getSOCKET_PORT());

			List<LifeCycleStrategyBean> lifemap = lazy.getLifeCycle();
			if(lifemap!=null){
				 for(LifeCycleStrategyBean key : lifemap){ 
					JSONObject jso = new JSONObject();
					jso.put("model_code", key.getModel_code());
					jso.put("task_name", key.getTask_name());
					jso.put("run_type", key.getRun_type());
					jso.put("begin_time", key.getBegin_time());
					jso.put("end_time", key.getEnd_time());
					jso.put("task_state", key.getTask_state());	
					jsonArray.add(jso);
				}
				jsonObj.put("life_map", jsonArray);
			}
			jsonResp.put("root", jsonObj);
			jsonResp.put("code", 20000);
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			log.info("--getSingletonInfoSearch(over)");

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "获取服务器缓存信息失败!!!");
			log.error( "内存管理->查询服务器存信息失败->"+e.toString());
		}

		this.outJsonString(jsonResp.toString());
		log.info( "--getSingletonInfoSearch(over)");
		return null;
	}
	
	@ResponseBody
	@RequestMapping(value = "/singletonManage/resetSingletonInfoAction.action", method = RequestMethod.POST)
	public String regetSingletonInfoSearch(String data) {
		log.info( "--getSingletonInfoSearch(start)");
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		JSONObject jsonObj = new JSONObject();
		JSONObject modelJson = JSONObject.fromObject(data);
        String ip = (String) modelJson.getOrDefault("server_ip","");
		String port = (String) modelJson.getOrDefault("http_port","");
		try {
			LazySingletonBean lazy = singletonManageDao.regetDMInfo(ip,port);
			log.debug( "--getSingletonInfoSearch-->" );
			jsonObj.put("server_ip", lazy.getSERVER_IP());
			jsonObj.put("server_id", lazy.getSERVER_ID());
			jsonObj.put("server_name", lazy.getSERVER_NAME());
			jsonObj.put("server_status", lazy.getSERVER_STATUS());
			jsonObj.put("group_name",lazy.getGROUP_NAME());
			jsonObj.put("group_id",lazy.getGROUP_ID());
			jsonObj.put("group_state",lazy.getGROUP_STATE());
			jsonObj.put("group_os",lazy.getGROUP_OS());
			jsonObj.put("deploy_mode",lazy.getDEPLOY_MODE());
			jsonObj.put("http_port",lazy.getHTTP_PORT());
			jsonObj.put("socket_port",lazy.getSOCKET_PORT());

			List<LifeCycleStrategyBean> lifemap = lazy.getLifeCycle();
			if(lifemap!=null){
				 for(LifeCycleStrategyBean key : lifemap){ 
					JSONObject jso = new JSONObject();
					jso.put("model_code", key.getModel_code());
					jso.put("task_name", key.getTask_name());
					jso.put("run_type", key.getRun_type());
					jso.put("begin_time", key.getBegin_time());
					jso.put("end_time", key.getEnd_time());
					jso.put("task_state", key.getTask_state());	
					jsonArray.add(jso);
				}
				jsonObj.put("life_map", jsonArray);
			}
			jsonResp.put("root", jsonObj);
			jsonResp.put("code", 20000);
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			log.info("--getSingletonInfoSearch(over)");

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "获取服务器缓存信息失败!!!");
			log.error( "内存管理->查询服务器存信息失败->"+e.toString());
		}

		this.outJsonString(jsonResp.toString());
		log.info( "--getSingletonInfoSearch(over)");
		return null;
	}
	
	@ResponseBody
	@RequestMapping(value = "/singletonManage/getUASingletonInfoAction.action", method = RequestMethod.POST)
	public String getUASingletonInfoSearch(String data) {
		log.info( "--getUASingletonInfoSearch(start)");
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		JSONObject jsonObj = new JSONObject();
		JSONObject modelJson = JSONObject.fromObject(data);
        String ip = (String) modelJson.getOrDefault("server_ip","");
		String port = (String) modelJson.getOrDefault("http_port","");
		try {
			LazySingletonBean lazy = singletonManageDao.getUAInfo(ip,port);
			log.debug( "--getSingletonInfoSearch-->" );
			jsonObj.put("server_ip", lazy.getSERVER_IP());
			jsonObj.put("server_id", lazy.getSERVER_ID().replaceFirst("UA", ""));
			jsonObj.put("server_name", lazy.getSERVER_NAME());
			jsonObj.put("server_status", lazy.getSERVER_STATUS());
			jsonObj.put("group_name",lazy.getGROUP_NAME());
			jsonObj.put("group_id",lazy.getGROUP_ID());
			jsonObj.put("http_port",lazy.getHTTP_PORT());
			jsonObj.put("socket_port",lazy.getSOCKET_PORT());
			jsonResp.put("root", jsonObj);
			jsonResp.put("code", 20000);
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			log.info("--getSingletonInfoSearch(over)");

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "获取服务器缓存信息失败!!!");
			log.error( "内存管理->查询服务器缓存信息失败->"+e.toString());
		}

		this.outJsonString(jsonResp.toString());
		log.info( "--getUASingletonInfoSearch(over)");
		return null;
	}
	
	@ResponseBody
	@RequestMapping(value = "/singletonManage/resetUASingletonInfoAction.action", method = RequestMethod.POST)
	public String regetUASingletonInfoSearch(String data) {
		log.info( "--regetUASingletonInfoSearch(start)");
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray=new JSONArray();
		JSONObject jsonObj = new JSONObject();
		JSONObject modelJson = JSONObject.fromObject(data);
        String ip = (String) modelJson.getOrDefault("server_ip","");
		String port = (String) modelJson.getOrDefault("http_port","");
		try {
			LazySingletonBean lazy = singletonManageDao.regetUAInfo(ip,port);
			log.debug( "--getSingletonInfoSearch-->" );
			jsonObj.put("server_ip", lazy.getSERVER_IP());
			jsonObj.put("server_id", lazy.getSERVER_ID().replaceFirst("UA", ""));
			jsonObj.put("server_name", lazy.getSERVER_NAME());
			jsonObj.put("server_status", lazy.getSERVER_STATUS());
			jsonObj.put("group_name",lazy.getGROUP_NAME());
			jsonObj.put("group_id",lazy.getGROUP_ID());
			jsonObj.put("http_port",lazy.getHTTP_PORT());
			jsonObj.put("socket_port",lazy.getSOCKET_PORT());

			jsonResp.put("root", jsonObj);
			jsonResp.put("code", 20000);
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			log.info("--getSingletonInfoSearch(over)");

		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "获取统一接入服务器缓存信息失败!!!");
			log.error( "内存管理->查询统一接入服务器缓存信息失败->"+e.toString());
		}

		this.outJsonString(jsonResp.toString());
		log.info( "--regetUASingletonInfoSearch(over)");
		return null;
	}
	

}
