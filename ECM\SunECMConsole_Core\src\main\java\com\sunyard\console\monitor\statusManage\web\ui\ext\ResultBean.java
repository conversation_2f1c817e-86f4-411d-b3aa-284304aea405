package com.sunyard.console.monitor.statusManage.web.ui.ext;

import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;


public class ResultBean {
	/**
	 * 标识结果是成功还是失败
	 * true:  成功
	 * false: 失败
	 */
	private boolean result;
	
	/**
	 * 此属性值无法设置，用来标识Ajax请求处理成功，回调success函数
	 */
	private String success = "true";
	
	/**
	 * 结果提示框的标题
	 */
	private String title;
	
	/**
	 * 结果提示框的信息
	 */
	private String message;
	
	/**
	 * 结果提示框的等级,取值如下
	 * Ext.MessageBox.INFO       提示信息
	 * Ext.MessageBox.WARNING    警告信息
	 * Ext.MessageBox.QUESTION   确定信息
	 * Ext.MessageBox.ERROR      错误信息
	 */
	private String level;
	
	public ResultBean(){
	}
	
	/**
	 * 
	 * @param result 标识结果是否成功，true:结果执行成功，false：结果执行失败
	 * @param title  标题信息
	 * @param msg    显示结果信息
	 */
	public ResultBean(boolean result,String title,String msg){		
		if(StringUtils.isBlank(msg)){
			msg =  result ? title + "成功" : title +"失败";
		}
		this.result = result;
		this.title = title;
		this.message = msg;
		level = result ? ExtConstant.EXT_MESSAGEBOX_INFO : ExtConstant.EXT_MESSAGEBOX_ERROR;
	}
	
	/**
	 * 得到执行结果
	 * @return
	 */
	public boolean isResult() {
		return result;
	}

	/**
	 * 设置执行结果
	 * @param result
	 */
	public void setResult(boolean result) {
		this.result = result;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	/**
	 * 得到执行结果信息
	 * @return
	 */
	public String getMessage() {
		return message;
	}

	/**
	 * 设置执行结果信息
	 * @param message
	 */
	public void setMessage(String message) {
		this.message = message;
	}
	
	public String getLevel() {
		return level;
	}

	public void setLevel(String level) {
		this.level = level;
	}
	
	public String toString() {
		StringBuffer sb = new StringBuffer();
		sb.append("{").append(super.toString()).append("}");
		sb.append("result: [").append(this.result).append("]; ");
		sb.append("title: [").append(this.title).append("]; ");
		sb.append("message: [").append(this.message).append("]; ");
		return sb.toString();
	}
	
	/**
	 * 将对象转换成JSON字符串
	 * @return
	 */
	public String toJSONString(){
		String msg = StringUtils.trimToEmpty(this.message);
		msg = msg.replaceAll("\\[", "(");
		msg = msg.replaceAll("\\]", ")");
		
		JSONObject jsonResp = new JSONObject();
		jsonResp.put("success", this.success);
		jsonResp.put("result", this.result);
		jsonResp.put("title", this.title);
		jsonResp.put("message", msg);
		jsonResp.put("level", this.level);
		return jsonResp.toString(1);
	}
}
