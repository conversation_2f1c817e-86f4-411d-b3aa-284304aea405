package com.sunyard.console.common.accesscontrol;

import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.dom4j.DocumentException;

import com.sunyard.key.FindMAC;
import com.sunyard.key.License;

/**
 * <p>
 * Title: License文件信息
 * </p>
 * <p>
 * Description: 读取License文件配置信息
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0 
 */

public class LicenseFileConstant {
	
	private final static  Logger log = LoggerFactory.getLogger(LicenseFileConstant.class);
	
	/**
	 * 是否用授权
	 */
	private static boolean haslic = false;
	
	private static String maxusers; //最大用户数量         0 表示无限制
	
	private static String expiration; //到期日期              99999999 表示无限制
	
	private static String mac; //授权的MAC地址
	
	/**
	 * 初始化加载系统配置文件
	 */
	private static void init() {
		try {
			Map<String,String> map = License.parseLicense();//获取授权文件
			String info = map.get("SIGNATURE"); //获取授权文件签名
			decoding(info); //解密授权文件签名
		} catch (DocumentException e) {
			throw new RuntimeException("加载授权文件文件错误....");
		}
	}
	/**
	 * 解密授权文件签名
	 * @param info 授权文件签名
	 */
	private static void decoding(String info) {
		try {
			byte[] s = License.decryptInfo(info);
			StringBuffer sb = new StringBuffer();
			for (byte temp : s) {
				sb.append((char)temp);
			}
			String[] str = sb.toString().split("==="); //E8-9A-8F-3B-30-AE===2===20121230
			mac = str[0];
			maxusers = str[1];
			expiration = str[2];
			
		} catch (Exception e) {
			log.error("error");
		}
		
	}
	/**
	 * 获取授权文件最大用户数
	 * @return 最大用户数
	 */
	public static String getMaxUsers(){
		if(maxusers == null){
			init();
		}
		return maxusers;
	}
	
	/**
	 * 获取授权文件的MAC地址 
	 * 格式：E8-9A-8F-3B-30-AE
	 * @return 授权机器MAC地址 
	 */
	public static String getMac(){
		if(mac == null){
			init();
		}
		return mac;
	}
	/**
	 * 获取授权文件到期日期 
	 * 格式：yyyyMMdd
	 * @return 授权文件到期日期 yyyyMMdd
	 */
	public static String getExpiration(){
		if(expiration == null){
			init();
		}
		return expiration;
	}
	/**
	 * 权限校验
	 * @return true 有权限 false 无权限
	 */
	public static boolean CheckLicense(){
		boolean flag = false;
		FindMAC fm = new FindMAC();
		List<String> sysMac = fm.macInfo();
		String mac = LicenseFileConstant.getMac();
		log.info("授权机器MAC地址："+mac);
		mac=replaceSpecialStr(mac);
		for(String m:sysMac){
			log.info("系统MAC地址："+m);
			m=replaceSpecialStr(m);
			log.info("去除:和-之后系统的mac地址:"+m+",授权mac="+mac+"]");
			if(mac.contains(m)){
				haslic = true; 
				flag = true; 
			}
		}
		return flag;
	}
	/***
	 * 去除msg中特殊(-和:)字符，并返回去除后的字符串的大写
	 */
	public static String replaceSpecialStr(String msg){
		 msg=msg.replaceAll("\\:|\\-", "");
		 return msg.toUpperCase();
		
	}
	
//	public static void main(String[] args) {
//		
////		System.out.println(Integer.parseInt(LicenseFileConstant.getMaxUsers())>3);
//		System.out.println(CheckLicense());
////		SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");//设置日期格式 yyyy-MM-dd HH:mm:ss
////		String now = df.format(new Date());
////		System.out.println(now);
////		System.out.println(LicenseFileConstant.getExpiration());
////		System.out.println(Integer.parseInt(LicenseFileConstant.getExpiration())>Integer.parseInt(now));
////		System.out.println(haslic);
////		System.out.println(replaceSpecialStr("a:b-c:d:]"));
//	}
}
