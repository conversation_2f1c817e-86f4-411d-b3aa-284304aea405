package com.xxl.job.admin.service.impl;

import com.github.pagehelper.PageHelper;
import com.xxl.job.admin.core.model.JobParam;
import com.xxl.job.admin.dao.JobParamDao;
import com.xxl.job.admin.service.JobParamService;
import com.xxl.job.core.biz.model.ReturnT;

import org.apache.commons.lang.StringUtils;
import org.quartz.CronExpression;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class JobParamServiceImpl implements JobParamService {

	@Resource
	private JobParamDao jobParamDao;

	@Override
	public Map<String, Object> paramPageList(int start, int length, int jobId, String param_field) {

		// page list
		// page query
		PageHelper.startPage(start/length+1, length);
		List<JobParam> list = jobParamDao.pageList(start, length, jobId, param_field);
		int list_count = jobParamDao.pageListCount(start, length, jobId, param_field);

		// package result
		Map<String, Object> maps = new HashMap<String, Object>();
		maps.put("recordsTotal", list_count); // 总记录数
		maps.put("recordsFiltered", list_count); // 过滤后的总记录数
		maps.put("data", list); // 分页列表
		return maps;
	}

	@Override
    public List<JobParam> getParamById(int jobId) {
		return jobParamDao.pageList(0,0,jobId,""); 
	}
	
	
	
	@Override
	public ReturnT<String> add(List<JobParam> jobParams) {

		int vnt = 0;
		for (int x = 0; x < jobParams.size(); x++) {
			vnt = jobParamDao.save(jobParams.get(x));
		}
		if (vnt > 0) {
			return ReturnT.SUCCESS;
		} else {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "新增参数配置失败");
		}

	}

	@Override
	public ReturnT<String> reschedule(JobParam jobParam) {
		{
			System.out.println(jobParam);

			if (StringUtils.isBlank(jobParam.getParam_field())) {
				return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入“参数字段”");
			}

			if (StringUtils.isBlank(jobParam.getParam_field_ch())) {
				return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入“参数说明”");
			}

			if (StringUtils.isBlank(jobParam.getParam_field())) {
				return new ReturnT<String>(ReturnT.FAIL_CODE, "请输入“参数值”");
			}

			int i = jobParamDao.update(jobParam);

			if (i > 0) {
				return ReturnT.SUCCESS;
			} else {
				return new ReturnT<String>(ReturnT.FAIL_CODE, "新增参数配置失败");
			}
		}
	}

	@Override
	public ReturnT<String> remove(int id) {
		int i = jobParamDao.delete(id);
		if (i > 0) {
			return ReturnT.SUCCESS;
		} else {
			return new ReturnT<String>(ReturnT.FAIL_CODE, "新增参数配置失败");
		}
	}

	@Override
	public ReturnT<String> add(JobParam jobParam) {
		// TODO Auto-generated method stub
		return null;
	}
}
