<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
	"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xxl.job.admin.dao.JobDateDao">

	
	<select id="queryDate" resultType="String">
		SELECT  JOB_DATE
		FROM QRTZ_JOB_DATE
	</select>


	<select id="updateDate" parameterType="java.util.HashMap">
			update QRTZ_JOB_DATE SET JOB_DATE= #{occurDate}
	</select>


	<select id="savaDate" parameterType="java.util.HashMap">
		INSERT INTO QRTZ_JOB_DATE(JOB_DATE)  VALUES(#{occurDate})
	</select>

</mapper>