2025-07-21 16:34:38.730 [OrganNo_00023_UserNo_admin] [0f31a7f26abaa8bb/1b1c2c7384d45187] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"2"
	}
}
2025-07-21 16:34:39.202 [OrganNo_00023_UserNo_admin] [0f31a7f26abaa8bb/1b1c2c7384d45187] [http-nio-9009-exec-1] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:34:39.210 [] [0f31a7f26abaa8bb/1b1c2c7384d45187] [http-nio-9009-exec-1] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:38 操作结束时间: 2025-07-21 16:34:39!总共花费时间: 563 毫秒！
2025-07-21 16:34:43.180 [OrganNo_00023_UserNo_admin] [9a0fa477b8efc7bd/802b8c069e3a53ae] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"getUnscanCodeList"
	}
}
2025-07-21 16:34:43.225 [OrganNo_00023_UserNo_admin] [9a0fa477b8efc7bd/802b8c069e3a53ae] [http-nio-9009-exec-3] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"codeNo":"BG01",
				"codeType":"财务报告",
				"id":"8BAAF15146D3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"中期财务报告:本级及全辖月\\季\\半年财务报告",
				"codeNo":"BG02",
				"codeType":"财务报告",
				"id":"8BAAF15146D4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"下级机构上报的财务报告",
				"codeNo":"BG03",
				"codeType":"财务报告",
				"id":"8BAAF15146D5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"不定期报表",
				"codeNo":"BG04",
				"codeType":"财务报告",
				"id":"8BAAF15146D6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他财务报告",
				"codeNo":"BG05",
				"codeType":"财务报告",
				"id":"8BAAF15146D7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"特殊介质保存的会计资料",
				"codeNo":"JZ01",
				"codeType":"特殊介质",
				"id":"8BAAF15146D8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"codeType":"会计凭证",
				"id":"8BAAF15146D9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"其他会计凭证",
				"codeNo":"PZ02",
				"codeType":"会计凭证",
				"id":"8BAAF15146DAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"挂失登记及补发凭单收据",
				"codeNo":"QT01",
				"codeType":"其他",
				"id":"8BAAF15146DBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"存\\贷款开销户记录",
				"codeNo":"QT02",
				"codeType":"其他",
				"id":"8BAAF15146DCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"有权机关查询\\冻结\\扣划公函资料和登记簿",
				"codeNo":"QT03",
				"codeType":"其他",
				"id":"8BAAF15146DDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"帐销案存记录",
				"codeNo":"QT04",
				"codeType":"其他",
				"id":"8BAAF15146DEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"codeType":"其他",
				"id":"8BAAF15146DFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"codeType":"其他",
				"id":"8BAAF15146E0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案保管登记簿",
				"codeNo":"QT07",
				"codeType":"其他",
				"id":"8BAAF15146E1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"销毁清册及相关审批资料",
				"codeNo":"QT08",
				"codeType":"其他",
				"id":"8BAAF15146E2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"机构变动交接清册",
				"codeNo":"QT09",
				"codeType":"其他",
				"id":"8BAAF15146E3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"重要单证\\有价凭证\\印章的领发\\保管和缴销记录",
				"codeNo":"QT10",
				"codeType":"其他",
				"id":"8BAAF15146E4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计档案移交清册",
				"codeNo":"QT11",
				"codeType":"其他",
				"id":"8BAAF15146E5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计人员交接清册",
				"codeNo":"QT12",
				"codeType":"其他",
				"id":"8BAAF15146E6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计事后监督档案(含差错通知书)",
				"codeNo":"QT13",
				"codeType":"其他",
				"id":"8BAAF15146E7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计专用印章登记簿及印章处置清单",
				"codeNo":"QT14",
				"codeType":"其他",
				"id":"8BAAF15146E8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"codeType":"其他",
				"id":"8BAAF15146E9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行计息和核息清单及保证金利息清单",
				"codeNo":"QT16",
				"codeType":"其他",
				"id":"8BAAF15146EAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行、联行对帐单、确认函和查询查复书",
				"codeNo":"QT17",
				"codeType":"其他",
				"id":"8BAAF15146EBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部对帐记录及其他对帐资料",
				"codeNo":"QT18",
				"codeType":"其他",
				"id":"8BAAF15146ECE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"人民银行及同业对帐单",
				"codeNo":"QT19",
				"codeType":"其他",
				"id":"8BAAF15146EDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"对帐回单",
				"codeNo":"QT20",
				"codeType":"其他",
				"id":"8BAAF15146EEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部资金往来核算资料",
				"codeNo":"QT21",
				"codeType":"其他",
				"id":"8BAAF15146EFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"财会检查档案",
				"codeNo":"QT22",
				"codeType":"其他",
				"id":"8BAAF15146F0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案查阅登记簿和申请单",
				"codeNo":"QT23",
				"codeType":"其他",
				"id":"8BAAF15146F1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计档案拆封申请单",
				"codeNo":"QT24",
				"codeType":"其他",
				"id":"8BAAF15146F2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"计算机应用系统运行日志",
				"codeNo":"QT25",
				"codeType":"其他",
				"id":"8BAAF15146F3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计电算化系统开发的全套文档资料",
				"codeNo":"QT26",
				"codeType":"其他",
				"id":"8BAAF15146F4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计档案",
				"codeNo":"QT27",
				"codeType":"其他",
				"id":"8BAAF15146F5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"投资科目分户帐",
				"codeNo":"ZB01",
				"codeType":"会计账簿",
				"id":"8BAAF15146F6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"资本金、股金及股权明细",
				"codeNo":"ZB02",
				"codeType":"会计账簿",
				"id":"8BAAF15146F7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"明细账、分户帐、卡片账未销卡清单、帐户余额表及其他明细帐",
				"codeNo":"ZB03",
				"codeType":"会计账簿",
				"id":"8BAAF15146F8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"总帐：本级及全辖汇总日计表及其他形式总帐",
				"codeNo":"ZB04",
				"codeType":"会计账簿",
				"id":"8BAAF15146F9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"codeType":"会计账簿",
				"id":"8BAAF15146FAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"已处置固定资产卡片(按报废清理后计算保管期限)",
				"codeNo":"ZB06",
				"codeType":"会计账簿",
				"id":"8BAAF15146FBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计帐簿",
				"codeNo":"ZB07",
				"codeType":"会计账簿",
				"id":"8BAAF15146FCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			}
		]
	},
	"retMsg":""
}
2025-07-21 16:34:43.226 [] [9a0fa477b8efc7bd/802b8c069e3a53ae] [http-nio-9009-exec-3] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:43 操作结束时间: 2025-07-21 16:34:43!总共花费时间: 57 毫秒！
2025-07-21 16:34:43.269 [OrganNo_00023_UserNo_admin] [aa0581308fa3ca48/1446c3c11e486888] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"1"
	}
}
2025-07-21 16:34:43.330 [OrganNo_00023_UserNo_admin] [aa0581308fa3ca48/1446c3c11e486888] [http-nio-9009-exec-4] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"QT15-2025-000023-10-000002",
				"archState":"1",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"dayNum":1,
				"endDate":"20251231",
				"id":"0cc30aedfebc412db94252ea1d501cff",
				"keepYear":"10",
				"registerDate":"20250717",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"5",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:34:43.331 [] [aa0581308fa3ca48/1446c3c11e486888] [http-nio-9009-exec-4] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:43 操作结束时间: 2025-07-21 16:34:43!总共花费时间: 76 毫秒！
2025-07-21 16:34:48.286 [OrganNo_00023_UserNo_admin] [494e236bfe5fd452/2e6e33580f4ec59a] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"transfer",
		"ids":[
			"0cc30aedfebc412db94252ea1d501cff"
		]
	}
}
2025-07-21 16:34:48.345 [OrganNo_00023_UserNo_admin] [494e236bfe5fd452/2e6e33580f4ec59a] [http-nio-9009-exec-6] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"移交成功"
}
2025-07-21 16:34:48.347 [] [494e236bfe5fd452/2e6e33580f4ec59a] [http-nio-9009-exec-6] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 移交!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:48 操作结束时间: 2025-07-21 16:34:48!总共花费时间: 80 毫秒！
2025-07-21 16:34:48.376 [OrganNo_00023_UserNo_admin] [ff896d07065b806d/250cf3b168cfee0e] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"1"
	}
}
2025-07-21 16:34:48.422 [OrganNo_00023_UserNo_admin] [ff896d07065b806d/250cf3b168cfee0e] [http-nio-9009-exec-7] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:34:48.423 [] [ff896d07065b806d/250cf3b168cfee0e] [http-nio-9009-exec-7] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:48 操作结束时间: 2025-07-21 16:34:48!总共花费时间: 58 毫秒！
2025-07-21 16:34:54.819 [OrganNo_00023_UserNo_admin] [8b32bd64881b2a2e/c0aac5eba37e1e18] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"2"
	}
}
2025-07-21 16:34:54.860 [OrganNo_00023_UserNo_admin] [8b32bd64881b2a2e/c0aac5eba37e1e18] [http-nio-9009-exec-9] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"QT15-2025-000023-10-000002",
				"archState":"2",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"dayNum":1,
				"endDate":"20251231",
				"id":"0cc30aedfebc412db94252ea1d501cff",
				"keepYear":"10",
				"registerDate":"20250717",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250721",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"5",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:34:54.861 [] [8b32bd64881b2a2e/c0aac5eba37e1e18] [http-nio-9009-exec-9] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:54 操作结束时间: 2025-07-21 16:34:54!总共花费时间: 51 毫秒！
2025-07-21 16:34:59.947 [OrganNo_00023_UserNo_admin] [ecca16207fc773d1/7927bc35f07e2c26] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"delayReceive",
		"ids":[
			"0cc30aedfebc412db94252ea1d501cff"
		],
		"delayReason":"1123123"
	}
}
2025-07-21 16:34:59.987 [OrganNo_00023_UserNo_admin] [ecca16207fc773d1/7927bc35f07e2c26] [http-nio-9009-exec-11] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"延后接收设置成功"
}
2025-07-21 16:34:59.989 [] [ecca16207fc773d1/7927bc35f07e2c26] [http-nio-9009-exec-11] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 延后接收!请求IP地址: ************** 操作开始时间: 2025-07-21 16:34:59 操作结束时间: 2025-07-21 16:34:59!总共花费时间: 53 毫秒！
2025-07-21 16:35:00.032 [OrganNo_00023_UserNo_admin] [302205a4893c085b/d568f57563b1935a] [http-nio-9009-exec-12] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"2"
	}
}
2025-07-21 16:35:00.076 [OrganNo_00023_UserNo_admin] [302205a4893c085b/d568f57563b1935a] [http-nio-9009-exec-12] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"QT15-2025-000023-10-000002",
				"archState":"2",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"dayNum":1,
				"endDate":"20251231",
				"id":"0cc30aedfebc412db94252ea1d501cff",
				"keepYear":"10",
				"lateReason":"1123123",
				"registerDate":"20250717",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250721",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"5",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:35:00.077 [] [302205a4893c085b/d568f57563b1935a] [http-nio-9009-exec-12] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:00 操作结束时间: 2025-07-21 16:35:00!总共花费时间: 72 毫秒！
2025-07-21 16:35:34.014 [OrganNo_00023_UserNo_admin] [7e15c9c3cd20d2d9/0096601092abf3ab] [http-nio-9009-exec-14] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"back",
		"ids":[
			"0cc30aedfebc412db94252ea1d501cff"
		]
	}
}
2025-07-21 16:35:34.050 [OrganNo_00023_UserNo_admin] [7e15c9c3cd20d2d9/0096601092abf3ab] [http-nio-9009-exec-14] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"回退成功"
}
2025-07-21 16:35:34.052 [] [7e15c9c3cd20d2d9/0096601092abf3ab] [http-nio-9009-exec-14] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 回退!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:34 操作结束时间: 2025-07-21 16:35:34!总共花费时间: 52 毫秒！
2025-07-21 16:35:34.100 [OrganNo_00023_UserNo_admin] [9ccaaa92d6b11c2e/83ac984f483641f5] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"2"
	}
}
2025-07-21 16:35:34.145 [OrganNo_00023_UserNo_admin] [9ccaaa92d6b11c2e/83ac984f483641f5] [http-nio-9009-exec-15] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:35:34.146 [] [9ccaaa92d6b11c2e/83ac984f483641f5] [http-nio-9009-exec-15] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:34 操作结束时间: 2025-07-21 16:35:34!总共花费时间: 74 毫秒！
2025-07-21 16:35:51.266 [OrganNo_00023_UserNo_admin] [df3c034047d2d849/99943dbfae1d0ec3] [http-nio-9009-exec-17] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"getUnscanCodeList"
	}
}
2025-07-21 16:35:51.290 [OrganNo_00023_UserNo_admin] [df3c034047d2d849/99943dbfae1d0ec3] [http-nio-9009-exec-17] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"codeNo":"BG01",
				"codeType":"财务报告",
				"id":"8BAAF15146D3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"中期财务报告:本级及全辖月\\季\\半年财务报告",
				"codeNo":"BG02",
				"codeType":"财务报告",
				"id":"8BAAF15146D4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"下级机构上报的财务报告",
				"codeNo":"BG03",
				"codeType":"财务报告",
				"id":"8BAAF15146D5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"不定期报表",
				"codeNo":"BG04",
				"codeType":"财务报告",
				"id":"8BAAF15146D6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他财务报告",
				"codeNo":"BG05",
				"codeType":"财务报告",
				"id":"8BAAF15146D7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"特殊介质保存的会计资料",
				"codeNo":"JZ01",
				"codeType":"特殊介质",
				"id":"8BAAF15146D8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"codeType":"会计凭证",
				"id":"8BAAF15146D9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"其他会计凭证",
				"codeNo":"PZ02",
				"codeType":"会计凭证",
				"id":"8BAAF15146DAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"挂失登记及补发凭单收据",
				"codeNo":"QT01",
				"codeType":"其他",
				"id":"8BAAF15146DBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"存\\贷款开销户记录",
				"codeNo":"QT02",
				"codeType":"其他",
				"id":"8BAAF15146DCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"有权机关查询\\冻结\\扣划公函资料和登记簿",
				"codeNo":"QT03",
				"codeType":"其他",
				"id":"8BAAF15146DDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"帐销案存记录",
				"codeNo":"QT04",
				"codeType":"其他",
				"id":"8BAAF15146DEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"codeType":"其他",
				"id":"8BAAF15146DFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"codeType":"其他",
				"id":"8BAAF15146E0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案保管登记簿",
				"codeNo":"QT07",
				"codeType":"其他",
				"id":"8BAAF15146E1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"销毁清册及相关审批资料",
				"codeNo":"QT08",
				"codeType":"其他",
				"id":"8BAAF15146E2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"机构变动交接清册",
				"codeNo":"QT09",
				"codeType":"其他",
				"id":"8BAAF15146E3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"重要单证\\有价凭证\\印章的领发\\保管和缴销记录",
				"codeNo":"QT10",
				"codeType":"其他",
				"id":"8BAAF15146E4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计档案移交清册",
				"codeNo":"QT11",
				"codeType":"其他",
				"id":"8BAAF15146E5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计人员交接清册",
				"codeNo":"QT12",
				"codeType":"其他",
				"id":"8BAAF15146E6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计事后监督档案(含差错通知书)",
				"codeNo":"QT13",
				"codeType":"其他",
				"id":"8BAAF15146E7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计专用印章登记簿及印章处置清单",
				"codeNo":"QT14",
				"codeType":"其他",
				"id":"8BAAF15146E8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"codeType":"其他",
				"id":"8BAAF15146E9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行计息和核息清单及保证金利息清单",
				"codeNo":"QT16",
				"codeType":"其他",
				"id":"8BAAF15146EAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行、联行对帐单、确认函和查询查复书",
				"codeNo":"QT17",
				"codeType":"其他",
				"id":"8BAAF15146EBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部对帐记录及其他对帐资料",
				"codeNo":"QT18",
				"codeType":"其他",
				"id":"8BAAF15146ECE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"人民银行及同业对帐单",
				"codeNo":"QT19",
				"codeType":"其他",
				"id":"8BAAF15146EDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"对帐回单",
				"codeNo":"QT20",
				"codeType":"其他",
				"id":"8BAAF15146EEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部资金往来核算资料",
				"codeNo":"QT21",
				"codeType":"其他",
				"id":"8BAAF15146EFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"财会检查档案",
				"codeNo":"QT22",
				"codeType":"其他",
				"id":"8BAAF15146F0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案查阅登记簿和申请单",
				"codeNo":"QT23",
				"codeType":"其他",
				"id":"8BAAF15146F1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计档案拆封申请单",
				"codeNo":"QT24",
				"codeType":"其他",
				"id":"8BAAF15146F2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"计算机应用系统运行日志",
				"codeNo":"QT25",
				"codeType":"其他",
				"id":"8BAAF15146F3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计电算化系统开发的全套文档资料",
				"codeNo":"QT26",
				"codeType":"其他",
				"id":"8BAAF15146F4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计档案",
				"codeNo":"QT27",
				"codeType":"其他",
				"id":"8BAAF15146F5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"投资科目分户帐",
				"codeNo":"ZB01",
				"codeType":"会计账簿",
				"id":"8BAAF15146F6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"资本金、股金及股权明细",
				"codeNo":"ZB02",
				"codeType":"会计账簿",
				"id":"8BAAF15146F7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"明细账、分户帐、卡片账未销卡清单、帐户余额表及其他明细帐",
				"codeNo":"ZB03",
				"codeType":"会计账簿",
				"id":"8BAAF15146F8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"总帐：本级及全辖汇总日计表及其他形式总帐",
				"codeNo":"ZB04",
				"codeType":"会计账簿",
				"id":"8BAAF15146F9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"codeType":"会计账簿",
				"id":"8BAAF15146FAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"已处置固定资产卡片(按报废清理后计算保管期限)",
				"codeNo":"ZB06",
				"codeType":"会计账簿",
				"id":"8BAAF15146FBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计帐簿",
				"codeNo":"ZB07",
				"codeType":"会计账簿",
				"id":"8BAAF15146FCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			}
		]
	},
	"retMsg":""
}
2025-07-21 16:35:51.292 [] [df3c034047d2d849/99943dbfae1d0ec3] [http-nio-9009-exec-17] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:51 操作结束时间: 2025-07-21 16:35:51!总共花费时间: 36 毫秒！
2025-07-21 16:35:51.338 [OrganNo_00023_UserNo_admin] [d6c76e16ded50ea9/240d393fd12e3620] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"1"
	}
}
2025-07-21 16:35:51.383 [OrganNo_00023_UserNo_admin] [d6c76e16ded50ea9/240d393fd12e3620] [http-nio-9009-exec-18] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"QT15-2025-000023-10-000002",
				"archState":"1",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"dayNum":1,
				"endDate":"20251231",
				"id":"0cc30aedfebc412db94252ea1d501cff",
				"keepYear":"10",
				"lateReason":"1123123",
				"registerDate":"20250717",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250721",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"5",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:35:51.384 [] [d6c76e16ded50ea9/240d393fd12e3620] [http-nio-9009-exec-18] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:51 操作结束时间: 2025-07-21 16:35:51!总共花费时间: 65 毫秒！
2025-07-21 16:35:56.186 [OrganNo_00023_UserNo_admin] [a748766ed11f9cea/969f7a70bb1bd8d4] [http-nio-9009-exec-20] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"printCode",
		"ids":[
			"0cc30aedfebc412db94252ea1d501cff"
		]
	}
}
2025-07-21 16:35:56.454 [OrganNo_00023_UserNo_admin] [a748766ed11f9cea/969f7a70bb1bd8d4] [http-nio-9009-exec-20] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"barcodeList":[
			{
				"fileName":"QT15-2025-000023-10-000002.png",
				"src":"/home/<USER>/TempFiles/SunAOS/barCodeImgs/arch/QT15-2025-000023-10-000002/QT15-2025-000023-10-000002.png"
			}
		]
	},
	"retMsg":"打印档案准备完成"
}
2025-07-21 16:35:56.455 [] [a748766ed11f9cea/969f7a70bb1bd8d4] [http-nio-9009-exec-20] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 打印档案!请求IP地址: ************** 操作开始时间: 2025-07-21 16:35:56 操作结束时间: 2025-07-21 16:35:56!总共花费时间: 281 毫秒！
2025-07-21 16:36:53.433 [OrganNo_00023_UserNo_admin] [900c34cf2c4fa648/cf07b6b7e6fad5ce] [http-nio-9009-exec-22] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-21 16:36:53.496 [OrganNo_00023_UserNo_admin] [900c34cf2c4fa648/cf07b6b7e6fad5ce] [http-nio-9009-exec-22] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:36:53.497 [] [900c34cf2c4fa648/cf07b6b7e6fad5ce] [http-nio-9009-exec-22] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:36:53 操作结束时间: 2025-07-21 16:36:53!总共花费时间: 88 毫秒！
2025-07-21 16:36:55.066 [OrganNo_00023_UserNo_admin] [d65e9786b6c2a220/93299d4574b413e2] [http-nio-9009-exec-23] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-21 16:36:55.129 [OrganNo_00023_UserNo_admin] [d65e9786b6c2a220/93299d4574b413e2] [http-nio-9009-exec-23] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT06-2025-000023-99-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"dayNum":1,
				"endDate":"20251231",
				"id":"5eff8c8a5bf84fa994a16675af0dc299",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250715",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"22",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:36:55.130 [] [d65e9786b6c2a220/93299d4574b413e2] [http-nio-9009-exec-23] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:36:55 操作结束时间: 2025-07-21 16:36:55!总共花费时间: 84 毫秒！
2025-07-21 16:36:55.175 [OrganNo_00023_UserNo_admin] [1d3c9f4426e99bde/bd5a3b26abad3402] [http-nio-9009-exec-24] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-21 16:36:55.223 [OrganNo_00023_UserNo_admin] [1d3c9f4426e99bde/bd5a3b26abad3402] [http-nio-9009-exec-24] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"***********",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:36:55.224 [] [1d3c9f4426e99bde/bd5a3b26abad3402] [http-nio-9009-exec-24] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:36:55 操作结束时间: 2025-07-21 16:36:55!总共花费时间: 60 毫秒！
2025-07-21 16:36:55.623 [OrganNo_00023_UserNo_admin] [bd7efc8af42f640d/ebf32bb397beaf3f] [http-nio-9009-exec-25] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"2"
	}
}
2025-07-21 16:36:55.668 [OrganNo_00023_UserNo_admin] [bd7efc8af42f640d/ebf32bb397beaf3f] [http-nio-9009-exec-25] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:36:55.669 [] [bd7efc8af42f640d/ebf32bb397beaf3f] [http-nio-9009-exec-25] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:36:55 操作结束时间: 2025-07-21 16:36:55!总共花费时间: 59 毫秒！
2025-07-21 16:37:00.800 [OrganNo_00023_UserNo_admin] [d7d72e5ba984f851/587f2030bb6bcdcc] [http-nio-9009-exec-27] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":1
	}
}
2025-07-21 16:37:00.833 [OrganNo_00023_UserNo_admin] [d7d72e5ba984f851/587f2030bb6bcdcc] [http-nio-9009-exec-27] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"heading":"会计凭证",
				"id":"95e088a4637cb54501637cbfe58f0000",
				"isDefault":"1",
				"keepYears":"40",
				"ocrType":"1",
				"placefile":"会计凭证"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功！"
}
2025-07-21 16:37:00.835 [] [d7d72e5ba984f851/587f2030bb6bcdcc] [http-nio-9009-exec-27] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 凭证类型定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:00 操作结束时间: 2025-07-21 16:37:00!总共花费时间: 58 毫秒！
2025-07-21 16:37:00.883 [OrganNo_00023_UserNo_admin] [05f5bc3975f89d5d/7641d726cee52f6d] [http-nio-9009-exec-28] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1
	}
}
2025-07-21 16:37:00.942 [OrganNo_00023_UserNo_admin] [05f5bc3975f89d5d/7641d726cee52f6d] [http-nio-9009-exec-28] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"address":"巴中",
				"areaNo":"15356",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc1ae6ab00a8",
				"isDefault":"0",
				"locate":"行内",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ1901",
				"valid":"否",
				"wareHouseName":"巴中分行会计档案库",
				"wareHouseNo":"bz001"
			},
			{
				"address":"德阳旌东支行",
				"areaNo":"15054",
				"full":"否",
				"id":"95e088a463fbda550163fc22b3540070",
				"isDefault":"0",
				"locate":"行内",
				"manager":"段林",
				"managermethod":"机构自管",
				"property":"自属",
				"systemcode":"KJ0201",
				"valid":"否",
				"wareHouseName":"德阳分行旌东档案库",
				"wareHouseNo":"dey01"
			},
			{
				"address":"广安",
				"areaNo":"15349",
				"full":"否",
				"id":"95e088a463fbda550163fc28873f007a",
				"isDefault":"0",
				"locate":"行内",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ1801",
				"valid":"否",
				"wareHouseName":"广安分行库房",
				"wareHouseNo":"ga001"
			},
			{
				"address":"达州",
				"areaNo":"15163",
				"full":"否",
				"id":"95e088a463fbda550163fc21b56f006f",
				"isDefault":"0",
				"locate":"行外",
				"manager":"张渠",
				"managermethod":"机构自管",
				"property":"租赁",
				"systemcode":"ZH0501",
				"valid":"否",
				"wareHouseName":"达州物流港综合档案库",
				"wareHouseNo":"daz01"
			},
			{
				"address":"遂宁市遂州中路799号",
				"areaNo":"15132",
				"full":"否",
				"id":"95e088a463fbda550163fc1040ee0054",
				"isDefault":"0",
				"locate":"行外",
				"manager":"文永红",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ1001",
				"valid":"否",
				"wareHouseName":"遂宁分行会计档案室",
				"wareHouseNo":"sn000"
			},
			{
				"address":"遂宁市遂州中路799号",
				"areaNo":"15132",
				"full":"否",
				"id":"95e088a463fbda550163fc16aa1b005b",
				"isDefault":"0",
				"locate":"行内",
				"manager":"曾广建",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ1003",
				"valid":"否",
				"wareHouseName":"遂宁分行会计档案室01",
				"wareHouseNo":"sn001"
			},
			{
				"address":"绵阳市涪城区建设街1号",
				"areaNo":"15091",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc17a31e00a7",
				"isDefault":"0",
				"locate":"行外",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"ZH0301",
				"valid":"否",
				"wareHouseName":"绵阳分行建设街档案室",
				"wareHouseNo":"my002"
			},
			{
				"address":"绵阳市北川县安昌镇东风路12号",
				"areaNo":"15091",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc159b4000a6",
				"isDefault":"0",
				"locate":"行外",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ0302",
				"valid":"否",
				"wareHouseName":"绵阳分行安昌档案库房",
				"wareHouseNo":"my001"
			},
			{
				"address":"遂宁市遂州中路799号",
				"areaNo":"15132",
				"full":"否",
				"id":"95e088a463fbda550163fc17e860005e",
				"isDefault":"0",
				"locate":"行内",
				"manager":"曾广建",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ1004",
				"valid":"否",
				"wareHouseName":"遂宁分行会计档案室04",
				"wareHouseNo":"sn002"
			},
			{
				"address":"西昌市三岔口西路二号",
				"areaNo":"15312",
				"full":"否",
				"id":"95e088a463fbda550163fc1b275d0068",
				"isDefault":"0",
				"locate":"行内",
				"manager":"刘杰",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ1301",
				"valid":"否",
				"wareHouseName":"凉山分行会计档案库",
				"wareHouseNo":"lsh01"
			},
			{
				"address":"内江市西林大道145号",
				"areaNo":"15238",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc1c269700a9",
				"isDefault":"0",
				"locate":"行内",
				"manager":"伍军",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ0801",
				"valid":"否",
				"wareHouseName":"内江分行综合管理部",
				"wareHouseNo":"nj001"
			},
			{
				"address":"宜宾",
				"areaNo":"15260",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc28b83c00b9",
				"isDefault":"0",
				"locate":"行内",
				"manager":"黄小春",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ0601",
				"valid":"否",
				"wareHouseName":"宜宾会计档案分库",
				"wareHouseNo":"yb001"
			},
			{
				"address":"自贡",
				"areaNo":"15215",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc2544e100b5",
				"isDefault":"0",
				"locate":"行外",
				"managermethod":"机构自管",
				"property":"租赁",
				"systemcode":"ZH0902",
				"valid":"否",
				"wareHouseName":"丽景苑库房",
				"wareHouseNo":"cd004"
			},
			{
				"address":"广元市利州东路501号",
				"areaNo":"15329",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc2416bd00b2",
				"isDefault":"0",
				"locate":"行内",
				"manager":"陶诗辉",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"ZH1702",
				"valid":"否",
				"wareHouseName":"广元分行会计档案分库",
				"wareHouseNo":"gy001"
			},
			{
				"address":"自贡",
				"areaNo":"15215",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc24c57200b4",
				"isDefault":"0",
				"locate":"行内",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"ZH0901",
				"valid":"否",
				"wareHouseName":"自流井支行",
				"wareHouseNo":"cd003"
			},
			{
				"address":"泸州",
				"areaNo":"15276",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc2652d700b6",
				"isDefault":"0",
				"locate":"行内",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ0702",
				"valid":"否",
				"wareHouseName":"泸州江城会计档案分库",
				"wareHouseNo":"lz001"
			},
			{
				"address":"乐山市中区嘉定北路140号",
				"areaNo":"15189",
				"full":"否",
				"id":"95e088a463fbda550163fc1cf8cb006b",
				"isDefault":"0",
				"locate":"行内",
				"manager":"罗颐川",
				"managermethod":"机构自管",
				"property":"自有",
				"systemcode":"KJ0401",
				"valid":"否",
				"wareHouseName":"乐山分行本部档案库房",
				"wareHouseNo":"ls002"
			},
			{
				"address":"资阳雁江区外环路西一段17号一层",
				"areaNo":"15334",
				"full":"否",
				"id":"95e088a563fbd6ab0163fc1d959c00ab",
				"isDefault":"0",
				"locate":"行外",
				"manager":"罗英",
				"managermethod":"机构自管",
				"property":"租赁",
				"systemcode":"ZH1401",
				"valid":"否",
				"wareHouseName":"资阳分行档案室",
				"wareHouseNo":"zy002"
			},
			{
				"areaNo":"00023",
				"full":"否",
				"id":"95e031a775877f1d01758788890c0002",
				"isDefault":"0",
				"manager":"test",
				"valid":"否",
				"wareHouseName":"都江堰",
				"wareHouseNo":"cd001"
			}
		],
		"totalCount":19,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-07-21 16:37:00.942 [] [05f5bc3975f89d5d/7641d726cee52f6d] [http-nio-9009-exec-28] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 库房定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:00 操作结束时间: 2025-07-21 16:37:00!总共花费时间: 84 毫秒！
2025-07-21 16:37:01.005 [OrganNo_00023_UserNo_admin] [d51aec50cd63d0e9/56234b78e2d1f32e] [http-nio-9009-exec-29] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":1,
		"pageSize":15
	}
}
2025-07-21 16:37:01.066 [OrganNo_00023_UserNo_admin] [d51aec50cd63d0e9/56234b78e2d1f32e] [http-nio-9009-exec-29] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"areano":"15356",
				"costotal":1,
				"frameid":"425d6944a2894b588513e68ec38e399b",
				"frameseq":"bz001010101",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":2
			},
			{
				"areano":"15356",
				"costotal":2,
				"frameid":"da732ae7ff16458e841641f293b182d7",
				"frameseq":"bz001010102",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":3,
				"frameid":"7c44e308e9e649b887cedc221e64418a",
				"frameseq":"bz001010103",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":4,
				"frameid":"a00889af70fd4bf6bc424fda67257431",
				"frameseq":"bz001010104",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":5,
				"frameid":"7e9560ef9d3b4234b5831a07fdec0ae6",
				"frameseq":"bz001010105",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":6,
				"frameid":"12a3c96527284b299a7d6cee38099d59",
				"frameseq":"bz001010106",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":1
			},
			{
				"areano":"15356",
				"costotal":7,
				"frameid":"740cfde1ebfe4aaa8a20237dbc5e5300",
				"frameseq":"bz001010107",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":8,
				"frameid":"e307b2ee98a24c6dbc1c3aa0374ccddc",
				"frameseq":"bz001010108",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":9,
				"frameid":"b9b3e792fe0943adaee6e721ef8d7e86",
				"frameseq":"bz001010109",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":10,
				"frameid":"15b90d8cd4ad4b00a4b2d90dc704d319",
				"frameseq":"bz001010110",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":1,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":1,
				"frameid":"f409f4eba5b4468c9d206cd0d1d200fc",
				"frameseq":"bz001010201",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":2,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":2,
				"frameid":"1f55574acc534816ad1b6af93616de02",
				"frameseq":"bz001010202",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":2,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":3,
				"frameid":"0fc8d1cc12e04a04820bf137c11271a7",
				"frameseq":"bz001010203",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":2,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":4,
				"frameid":"ba841bf29ba5434080f018d5ccb813fd",
				"frameseq":"bz001010204",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":2,
				"shelfnum":1,
				"trunknum":0
			},
			{
				"areano":"15356",
				"costotal":5,
				"frameid":"b9a22031a80e4b76bcf6b3d1c0901dd5",
				"frameseq":"bz001010205",
				"frametype":"1",
				"houseid":"bz001",
				"isNull":"EMPITY",
				"layernum":2,
				"shelfnum":1,
				"trunknum":0
			}
		],
		"totalCount":1029,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:37:01.067 [] [d51aec50cd63d0e9/56234b78e2d1f32e] [http-nio-9009-exec-29] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 库房架定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:00 操作结束时间: 2025-07-21 16:37:01!总共花费时间: 94 毫秒！
2025-07-21 16:37:01.129 [OrganNo_00023_UserNo_admin] [46fc288f7a83cf34/d1b8f8260a159f4e] [http-nio-9009-exec-30] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"trunkState":"WAIT_WAREHOUSE"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"pageSize":15,
		"currentPage":1
	}
}
2025-07-21 16:37:01.233 [OrganNo_00023_UserNo_admin] [46fc288f7a83cf34/d1b8f8260a159f4e] [http-nio-9009-exec-30] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"appCount":"22",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a46f85376d016f8eb01ead176b",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行广安武胜支行",
				"organNo":"36027",
				"trunkDate":"20200110",
				"trunkNo":"20201800003",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"章燕",
				"trunkUserNo":"5430040",
				"yearnum":1,
				"yearorganseq":"202036027D3000001"
			},
			{
				"appCount":"40",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a46f84100d016f8475aef101fe",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行广安分行公司金融部",
				"organNo":"15533",
				"trunkDate":"20200108",
				"trunkNo":"20201800002",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"章燕",
				"trunkUserNo":"5430040",
				"yearnum":0,
				"yearorganseq":"202015533D3000001"
			},
			{
				"appCount":"40",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"20510101",
				"id":"95e088a46f41d354016f7a25af087fec",
				"keepYear":"30",
				"maxDate":"20200104",
				"minDate":"20191212",
				"organName":"中国银行广安分行财务运营部",
				"organNo":"15532",
				"trunkDate":"20200106",
				"trunkNo":"20201800001",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"章燕",
				"trunkUserNo":"5430040",
				"yearnum":-2,
				"yearorganseq":"202015532D3000001"
			},
			{
				"appCount":"22",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a56fa17e0a016fa1acff560373",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行雅安名山支行",
				"organNo":"35918",
				"trunkDate":"20200114",
				"trunkNo":"20201600004",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"樊鲁琼",
				"trunkUserNo":"5430064",
				"yearnum":1,
				"yearorganseq":"202035918D3000001"
			},
			{
				"appCount":"28",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a56f853896016f9248bd831c37",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行雅安挺进路支行",
				"organNo":"15324",
				"trunkDate":"20200111",
				"trunkNo":"20201600003",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"樊鲁琼",
				"trunkUserNo":"5430064",
				"yearnum":1,
				"yearorganseq":"202015324D3000001"
			},
			{
				"appCount":"25",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a56f41d471016f82b5d5ec7e11",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行雅安名山支行",
				"organNo":"35918",
				"trunkDate":"20200108",
				"trunkNo":"20201600002",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"樊鲁琼",
				"trunkUserNo":"5430064",
				"yearnum":0,
				"yearorganseq":"202035918D3000001"
			},
			{
				"appCount":"27",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a56f41d471016f786a071e658f",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行雅安名山支行",
				"organNo":"35918",
				"trunkDate":"20200106",
				"trunkNo":"20201600001",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"樊鲁琼",
				"trunkUserNo":"5430064",
				"yearnum":-2,
				"yearorganseq":"202035918D3000001"
			},
			{
				"appCount":"36",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a46f85376d016f9db1735d3275",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行眉山彭山支行",
				"organNo":"15427",
				"trunkDate":"20200113",
				"trunkNo":"20201500004",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"李丹",
				"trunkUserNo":"5430070",
				"yearnum":1,
				"yearorganseq":"202015427D3000001"
			},
			{
				"appCount":"24",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a46f85376d016f9cdc5b4d31e9",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行眉山环湖路支行",
				"organNo":"15127",
				"trunkDate":"20200113",
				"trunkNo":"20201500003",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"李丹",
				"trunkUserNo":"5430070",
				"yearnum":1,
				"yearorganseq":"202015127D3000001"
			},
			{
				"appCount":"38",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a46f85376d016f8908a5fd06fe",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行眉山新区支行",
				"organNo":"15128",
				"trunkDate":"20200109",
				"trunkNo":"20201500002",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"李丹",
				"trunkUserNo":"5430070",
				"yearnum":1,
				"yearorganseq":"202015128D3000001"
			},
			{
				"appCount":"33",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"19000101",
				"id":"95e088a46f41d354016f7948bf057e04",
				"keepYear":"30",
				"maxDate":"19000101",
				"minDate":"19000101",
				"organName":"中国银行眉山眉州大道支行",
				"organNo":"15126",
				"trunkDate":"20200106",
				"trunkNo":"20201500001",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"李丹",
				"trunkUserNo":"5430070",
				"yearnum":-2,
				"yearorganseq":"202015126D3000001"
			},
			{
				"appCount":"25",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"20510101",
				"id":"95e088a46fa17d69016fa1bf0e2f0532",
				"keepYear":"30",
				"maxDate":"20200112",
				"minDate":"20200103",
				"organName":"中国银行资阳十字路口支行",
				"organNo":"15335",
				"trunkDate":"20200114",
				"trunkNo":"20201400010",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"王吉莹",
				"trunkUserNo":"5430008",
				"yearnum":1,
				"yearorganseq":"202015335D3000001"
			},
			{
				"appCount":"15",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"20510101",
				"id":"95e088a46fa17d69016fa1b609ce04d7",
				"keepYear":"30",
				"maxDate":"20200110",
				"minDate":"20191223",
				"organName":"中国银行安岳北街支行",
				"organNo":"15341",
				"trunkDate":"20200114",
				"trunkNo":"20201400009",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"王吉莹",
				"trunkUserNo":"5430008",
				"yearnum":1,
				"yearorganseq":"202015341D3000001"
			},
			{
				"appCount":"15",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"20510101",
				"id":"95e088a46f85376d016f9211bb8d214b",
				"keepYear":"30",
				"maxDate":"20200109",
				"minDate":"20200103",
				"organName":"中国银行安岳支行营业部",
				"organNo":"15529",
				"trunkDate":"20200111",
				"trunkNo":"20201400008",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"王吉莹",
				"trunkUserNo":"5430008",
				"yearnum":1,
				"yearorganseq":"202015529D3000001"
			},
			{
				"appCount":"16",
				"belongYear":"2020",
				"codeNo":"PZ01",
				"codename":"1",
				"destroyDate":"20510101",
				"id":"95e088a46f85376d016f920d54102115",
				"keepYear":"30",
				"maxDate":"20200108",
				"minDate":"20191106",
				"organName":"中国银行资阳分行营业部",
				"organNo":"15528",
				"trunkDate":"20200111",
				"trunkNo":"20201400007",
				"trunkState":"WAIT_WAREHOUSE",
				"trunkType":"1",
				"trunkUserName":"王吉莹",
				"trunkUserNo":"5430008",
				"yearnum":1,
				"yearorganseq":"202015528D3000001"
			}
		],
		"totalCount":470,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:37:01.234 [] [46fc288f7a83cf34/d1b8f8260a159f4e] [http-nio-9009-exec-30] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 装箱操作 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:01 操作结束时间: 2025-07-21 16:37:01!总共花费时间: 132 毫秒！
2025-07-21 16:37:10.802 [OrganNo_00023_UserNo_admin] [7f23913522190469/bebce57c82cba154] [http-nio-9009-exec-32] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-21 16:37:10.859 [OrganNo_00023_UserNo_admin] [7f23913522190469/bebce57c82cba154] [http-nio-9009-exec-32] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT06-2025-000023-99-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"dayNum":1,
				"endDate":"20251231",
				"id":"5eff8c8a5bf84fa994a16675af0dc299",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250715",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"22",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:37:10.860 [] [7f23913522190469/bebce57c82cba154] [http-nio-9009-exec-32] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:10 操作结束时间: 2025-07-21 16:37:10!总共花费时间: 72 毫秒！
2025-07-21 16:37:10.907 [OrganNo_00023_UserNo_admin] [fff13169d7fd6fed/792e99638147dfa3] [http-nio-9009-exec-33] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-21 16:37:10.949 [OrganNo_00023_UserNo_admin] [fff13169d7fd6fed/792e99638147dfa3] [http-nio-9009-exec-33] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"***********",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-21 16:37:10.951 [] [fff13169d7fd6fed/792e99638147dfa3] [http-nio-9009-exec-33] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:10 操作结束时间: 2025-07-21 16:37:10!总共花费时间: 62 毫秒！
2025-07-21 16:37:14.528 [OrganNo_00023_UserNo_admin] [1559ead1ad899ca3/65b91d809a8d270e] [http-nio-9009-exec-35] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"code":"***********"
	}
}
2025-07-21 16:37:14.616 [OrganNo_00023_UserNo_admin] [1559ead1ad899ca3/65b91d809a8d270e] [http-nio-9009-exec-35] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"root":[
			{
				"fileName":"***********.png",
				"src":"/barCodeImgs/trunk/***********.png"
			},
			{
				"fileName":"20250100003.png",
				"src":"/barCodeImgs/trunk/20250100003.png"
			},
			{
				"fileName":"20250100004.png",
				"src":"/barCodeImgs/trunk/20250100004.png"
			},
			{
				"fileName":"20250100005.png",
				"src":"/barCodeImgs/trunk/20250100005.png"
			},
			{
				"fileName":"20250100006.png",
				"src":"/barCodeImgs/trunk/20250100006.png"
			},
			{
				"fileName":"20250100007.png",
				"src":"/barCodeImgs/trunk/20250100007.png"
			},
			{
				"fileName":"20250100008.png",
				"src":"/barCodeImgs/trunk/20250100008.png"
			},
			{
				"fileName":"20250100009.png",
				"src":"/barCodeImgs/trunk/20250100009.png"
			},
			{
				"fileName":"20250100010.png",
				"src":"/barCodeImgs/trunk/20250100010.png"
			},
			{
				"fileName":"20250100011.png",
				"src":"/barCodeImgs/trunk/20250100011.png"
			}
		]
	},
	"retMsg":"打印箱号准备完成"
}
2025-07-21 16:37:14.617 [] [1559ead1ad899ca3/65b91d809a8d270e] [http-nio-9009-exec-35] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:14 操作结束时间: 2025-07-21 16:37:14!总共花费时间: 98 毫秒！
2025-07-21 16:37:20.388 [OrganNo_00023_UserNo_admin] [229b751b44bf12c5/807ebd587b77d683] [http-nio-9009-exec-37] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"trunkNo":"***********"
	}
}
2025-07-21 16:37:20.414 [OrganNo_00023_UserNo_admin] [229b751b44bf12c5/807ebd587b77d683] [http-nio-9009-exec-37] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"list":[
			{
				"archNo":"BG02-2025-000023-10-000001",
				"archState":"4",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"中期财务报告:本级及全辖月\\季\\半年财务报告",
				"codeNo":"BG02",
				"dayNum":1,
				"endDate":"20251231",
				"id":"4d52c4061bf14c2dacc8d8f87a4789e2",
				"insertNo":"111",
				"keepYear":"10",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"trunkNo":"***********",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"222",
				"yearNum":1
			},
			{
				"archNo":"QT14-2025-000023-30-000001",
				"archState":"4",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计专用印章登记簿及印章处置清单",
				"codeNo":"QT14",
				"dayNum":1,
				"endDate":"20251231",
				"id":"7098d47111644e11a58543e260b74a3a",
				"keepYear":"30",
				"receiveDate":"20250716",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250716",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250716",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"trunkNo":"***********",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"33",
				"yearNum":1
			},
			{
				"archNo":"QT15-2025-000023-10-000001",
				"archState":"4",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"dayNum":1,
				"endDate":"20251231",
				"id":"d6b5bb6edb1e4b9fbdd6b409badd9404",
				"keepYear":"10",
				"receiveDate":"20250717",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250715",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250717",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"trunkNo":"***********",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"11",
				"yearNum":1
			}
		]
	},
	"retMsg":""
}
2025-07-21 16:37:20.414 [] [229b751b44bf12c5/807ebd587b77d683] [http-nio-9009-exec-37] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 根据箱号查询档案!请求IP地址: ************** 操作开始时间: 2025-07-21 16:37:20 操作结束时间: 2025-07-21 16:37:20!总共花费时间: 36 毫秒！
