2025-07-22 14:26:58.745 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/37e01f75cc3ab1a5] [http-nio-9007-exec-19] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"error_count":0,
			"isCheck":"1",
			"loginTerminal":"1",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"userNo":"admin"
		}
	],
	"sysMap":{
		"parameterList":[
			{
				"userNo":"admin",
				"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
				"isCheck":"1",
				"loginTerminal":"1"
			}
		],
		"custom_login_url":{
			"userNo":"admin",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg=="
		},
		"code":"",
		"oper_type":"0",
		"loginKind":"user_no"
	}
}
2025-07-22 14:26:58.778 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 柜员：admin登录成功
2025-07-22 14:26:58.779 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.findUserByUserNo - ==>  Preparing: select USER_NO, PASSWORD, ORGAN_NO, USER_NAME, USER_ENABLE, LOGIN_MODE, LOGIN_STATE, LAST_MODI_DATE, UNDERSIGNED, TELLERLVL, USER_STATUS, SINGLE_LOGIN, LAST_LOGIN_TIME, LAST_LOGOUT_TIME, TERMINAL_IP, TERMINAL_MAC, LOGIN_PC_SERVER, LOGIN_MOBILE_SERVER, BANK_NO, EMP_NO,ERROR_COUNT,CUSTOM_ATTR,PHONE, IS_RESIZE from SM_USERS_TB where USER_NO = ?
2025-07-22 14:26:58.779 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.findUserByUserNo - ==> Parameters: admin(String)
2025-07-22 14:26:58.781 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.findUserByUserNo - <==      Total: 1
2025-07-22 14:26:58.781 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==>  Preparing: select o.status as organ_status from sm_users_tb u left join sm_organ_tb o on o.organ_no = u.organ_no where u.user_no =?
2025-07-22 14:26:58.783 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.findUserOrganStatus - ==> Parameters: admin(String)
2025-07-22 14:26:58.784 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.findUserOrganStatus - <==      Total: 1
2025-07-22 14:26:58.784 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==>  Preparing: select distinct a.role_no from sm_user_role_tb a, sm_role_tb b where a.user_no = ? and a.is_open = '1' and a.role_no = b.role_no and b.is_open = '1' and a.bank_no = ? and b.bank_no = ?
2025-07-22 14:26:58.784 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.dao.UserMapper.getUserRole - ==> Parameters: admin(String), SUNYARD(String), SUNYARD(String)
2025-07-22 14:26:58.786 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.dao.UserMapper.getUserRole - <==      Total: 1
2025-07-22 14:26:58.786 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==>  Preparing: select ORGAN_NO, ORGAN_NAME, SHNAME, ORGAN_LEVEL, ORGAN_TYPE, PARENT_ORGAN, LAST_MODI_DATE, BANK_NO from sm_organ_tb where organ_no = ? and bank_no = ?
2025-07-22 14:26:58.786 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - ==> Parameters: 00023(String), SUNYARD(String)
2025-07-22 14:26:58.787 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.dao.OrganMapper.getOrganByNo - <==      Total: 1
2025-07-22 14:26:58.788 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 最大文件上传大小设置为：500 MB
2025-07-22 14:26:58.788 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] INFO  c.s.a.u.modelImpl.LoginServiceImpl - 配置文件中设置文件上传大小为:500.0M
2025-07-22 14:26:58.788 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==>  Preparing: update sm_users_tb set error_count = ? where user_no = ?
2025-07-22 14:26:58.789 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - ==> Parameters: 0(Integer), admin(String)
2025-07-22 14:26:58.791 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.updateStateAndErrorCount - <==    Updates: 1
2025-07-22 14:26:58.792 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==>  Preparing: update sm_users_tb set last_login_time = ?, login_state = ? , login_pc_server = ? ,terminal_ip = ? where user_no = ?
2025-07-22 14:26:58.793 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.updateUserLoginInfo - ==> Parameters: **************(String), 1(String), ************:9007(String), **************(String), admin(String)
2025-07-22 14:26:58.795 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.updateUserLoginInfo - <==    Updates: 1
2025-07-22 14:26:58.796 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.L.insertRecord - ==>  Preparing: insert into sm_loginhistory_tb(user_no,login_time,login_type,login_terminal,login_ip,login_mac,bank_no) values(?, ?, ?, ?, ?, ?, ?)
2025-07-22 14:26:58.796 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.L.insertRecord - ==> Parameters: admin(String), **************(String), 1(String), 1(String), **************(String), (String), SUNYARD(String)
2025-07-22 14:26:58.797 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/7749a0175c62cd9a] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.L.insertRecord - <==    Updates: 1
2025-07-22 14:26:58.804 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/4a0955b0e205d1af] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==>  Preparing: select concat(a.button_id,a.menu_id) as perm_key,a.req_url as req_url from SM_BUTTON_TB a where a.BANK_NO = ?
2025-07-22 14:26:58.804 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/4a0955b0e205d1af] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - ==> Parameters: SUNYARD(String)
2025-07-22 14:26:58.816 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/4a0955b0e205d1af] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.getAllPermissionUrlMapper - <==      Total: 646
2025-07-22 14:26:58.820 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/4a0955b0e205d1af] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==>  Preparing: select menu_id,button_id from sm_right_tb a where a.role_no in ( select t.role_no from sm_user_role_tb t where user_no = ? AND t.BANK_NO = ? )
2025-07-22 14:26:58.821 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/4a0955b0e205d1af] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - ==> Parameters: admin(String), SUNYARD(String)
2025-07-22 14:26:58.827 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/4a0955b0e205d1af] [http-nio-9007-exec-19] DEBUG c.s.a.u.d.U.getMenuButtonMapperByUserNo - <==      Total: 312
2025-07-22 14:26:58.834 [OrganNo_null_UserNo_null] [325d5d88c0abd1a5/37e01f75cc3ab1a5] [http-nio-9007-exec-19] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"maxUploadSize":500.0,
		"loginMsg":"登录成功",
		"loginFlag":"loginSuccess",
		"kkport":"8012",
		"customAttrMap":{
			"theme":"main"
		},
		"isOverFlag":"0",
		"lastLoginTime":"**************",
		"loginUser":{
			"bankNo":"SUNYARD",
			"custom_attr":"{\"theme\":\"main\"}",
			"error_count":0,
			"is_resize":"1",
			"lastLoginTime":"**************",
			"lastLogoutTime":"**************",
			"lastModiDate":"**************",
			"loginMode":"1",
			"loginPCServer":"************:9007",
			"loginState":"1",
			"loginTerminal":"1",
			"loginTime":"**************",
			"loginType":"1",
			"organNo":"00023",
			"password":"qYkoKKQ0ZQHc5xxmUn6aJg==",
			"roleNo":"8",
			"singleLogin":"0",
			"tellerlvl":"1",
			"terminalIp":"**************",
			"userEnable":"1",
			"userName":"系统超级管理员",
			"userNo":"admin",
			"userStatus":"1"
		},
		"includeModule":"01,05,11,17,18,22,23,24,25,26,33",
		"kkIP":"127.0.0.1",
		"loginOrgan":{
			"bankNo":"SUNYARD",
			"lastModiDate":"**************",
			"organLevel":"1",
			"organName":"中国银行四川省分行",
			"organNo":"00023",
			"organType":"1",
			"parentOrgan":"00023",
			"shname":"中国银行四川省分行"
		},
		"iskk":"0",
		"isEncrypt":"0"
	},
	"retMsg":"执行成功"
}
