package com.sunyard.console.safemanage.dao;

import com.sunyard.console.safemanage.bean.PermissionInfoBean;
import com.sunyard.console.safemanage.bean.UserInfoBean;

import java.util.List;
import java.util.Map;


/**
 * <p>Title: 用户登入,登出管理接口类</p>
 * <p>Description: 定义用户登入,登出数据库处理方法</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public interface LoginManageDAO {
	/**
	 * 用户登录
	 * @param login_id
	 * @param password
	 * @return
	 */
	public UserInfoBean login(String login_id,String password);
	
	/**
	 * 获取登陆用户权限
	 * @param login_id
	 * @return
	 */
	public List<PermissionInfoBean> getMenuAndButtonPermission(String login_id);
	/**
	 * 校验用户是不是系统用户
	 * @param login_id
	 * @return 如果是系统用户（没有内容模型操作权限）则返回true，否则（有内容模型操作权限）返回false
	 */
	public boolean checkSystemUser(String login_id);
	/**
	 * 对系统用户的登录校验，需要校验用户密码是否超过有效期，对超过有效期的用户进行注销，并对即将到期的密码进行提醒
	 * @param 用户bean 
	 */
	public Map<String,String> sysUserLoginCheck(UserInfoBean user);
}
