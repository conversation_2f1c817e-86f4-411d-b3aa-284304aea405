package com.sunyard.ecm.server.cache;

import com.sunyard.ecm.server.bean.BatchBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * 存放批次信息的静态变量
 * <AUTHOR>
 */
public class StaticBatchParams {
	private final static  Logger log = LoggerFactory.getLogger(StaticBatchParams.class);
	private volatile static StaticBatchParams unique;
	
	private StaticBatchParams(){};
	
	public static StaticBatchParams getInstance(){
		if(unique == null){
			synchronized (StaticBatchParams.class) {
				if(unique == null){
					unique = new StaticBatchParams();
				}
			}
		}
		return unique;
	}
	
	private Map<String, BatchBean> batchMap = new HashMap<String, BatchBean>();
	
	/**
	 * 添加批次信息
	 * @param contentID 内容ID
	 * @param filePath 文件在客户端上的路径
	 * @param fileID 文件ID
	 * @param format 文件后缀
	 */
	public void addBatch(String contentID, BatchBean batchBean){
		batchMap.put(contentID, batchBean);
	}
	
	/**
	 * 获取保存在服务器缓存中的文件信息列表
	 * @param contentID 内容ID
	 * @return
	 */
	public BatchBean getBatch(String contentID) {
		return batchMap.get(contentID);
	}
	
	/**
	 * 删除批次
	 * @param contentID 内容ID
	 */
	public void removeBatch(String contentID) {
		log.debug( "--StaticBatchParams --> removeBatch: remove contentID is " + contentID + "的索引信息...");
		batchMap.remove(contentID);
	}
}