package com.sunyard.client.conn;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.Closeable;
import java.io.DataOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.Socket;

import com.sunyard.util.TransOptionKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.bean.ClientFileBean;
import com.sunyard.client.common.ClientConfiguration;
import com.sunyard.exception.SunECMException;
import com.sunyard.util.FileTypeJudge;
import com.sunyard.util.FileTypeJudge.FileType;

/**
 * 客户端socket连接
 * 
 * <AUTHOR>
 * 
 */
public class SocketConn implements TransConn ,Closeable {
	private final static  Logger log = LoggerFactory.getLogger(SocketConn.class);
	private Socket socket = null;
	private BufferedReader in = null;
	private PrintWriter out = null;
	private String socketAddrress;
	private DataOutputStream outStream;// Socket输出流

	public SocketConn(Socket socket) throws IOException {
		this.socket = socket;
		try {
			this.socketAddrress=socket.getRemoteSocketAddress().toString();
			socket.setSendBufferSize(ClientConfiguration.getInt("client.socket.sendbuffersize", 65536));
			socket.setReceiveBufferSize(ClientConfiguration.getInt("client.socket.receivebuffersize", 65536));
			socket.setSoTimeout(ClientConfiguration.getInt("client.socket.timeout", 0) * 1000);
			in = new BufferedReader(new InputStreamReader(socket
					.getInputStream(), "UTF-8"));
			out = new PrintWriter(new BufferedWriter(new OutputStreamWriter(
					socket.getOutputStream(),ClientConfiguration.get("client.socket.charset",  "UTF-8"))), false);

			outStream = new DataOutputStream(socket.getOutputStream());
		} catch (IOException e) {
			log.error(
					"--SocketConn-->SocketConn-->IOException-->"
							+ e.toString());
			throw new IOException(e.toString());
		}
	}

	public void sendMsg(String msg) {
		//log.info( "SOCKET发送消息");
		out.println(msg);
		out.flush();
	}

	public String receiveMsg() throws IOException {
		//log.info( "SOCKET接受消息");
		try {
			String msg = in.readLine();
			return msg;
		} catch (IOException e) {
			log.error(
					"-->SocketConn-->receiveMsg-->IOException-->"
							+ e.toString());
			if (" Software caused connection abort: recv failed".equals(e.toString())) {
				return "#######上传批次返回的信息[FAIL<<::>>741]#######";
			} else {
				throw new IOException(e.toString());
			}
			
		}
	}
    
	public boolean sendFileDatabyStream(ClientFileBean fileBean, String contentID,
			String transType) throws SunECMException, IOException {
        InputStream inputStream=fileBean.getInputStream();		
		String format="";
		int transBufferSize = ClientConfiguration.getInt("client.socket.buffersize", 65536);
		int maxbps = 0;
		File tempFile = null;
		int status = 700;
		//long fileSize= Long.parseLong(fileBean.getFilesize());
		long fileSize;
		String filePath=fileBean.getFileName();
		byte[] b = null ;
		try {
			try {
				//从流中获取文件大小
			    FileInputStream fileInputStream =(FileInputStream) inputStream;
			    fileSize=fileInputStream.getChannel().size();
				//从流中获取文件类型
		        b = new byte[28];
		        if ((inputStream.read(b, 0, b.length))!=-1) {
		        	FileType fileType =FileTypeJudge.getType(b);
		        	if (fileType==null) {
		        		log.error("客户端无法识别文件类型");
		    			throw new SunECMException("客户端无法识别文件类型");
					}
				    format=fileType.toString();
				}else {
					log.error("客户端传入文件流不完整");
			throw new SunECMException("客户端传入文件流不完整");
				}
				//发送报文
				filePath=filePath.replaceAll(",", "<<DH>>");
				String str = transType + "FILENAME=" + filePath
						+ ",FILESIZE=" + fileSize
						+ ",BUFFERSIZE=" + transBufferSize + ",FILEPATH="
						+ filePath + ",CONTENTID = " + contentID + ",FORMAT="
						+ format;
				log.debug( "客户端发送文件报文 , socket client send file msg=" + str);
				sendMsg(str);
				String result = receiveMsg();
				if(result==null||result.equals("")){
					log.error( "客户端文件发送前接收返回的报文,return msg 为空 [" + result+"]");
					return false;
				}
				log.debug( "客户端文件发送前接收返回的报文,return msg = " + result);
				String[] content = result.split(TransOptionKey.SPLITSYM);
				
				if (!String.valueOf(TransOptionKey.SERVER_OK)
						.equals(content[0])) {
					status = Integer.parseInt(content[0]);
					//错误信息
					String errorMsg=" server no msg";
					if(content.length>1){
						errorMsg=content[1];
					}
					log.error( "---SunDM-->SocketClientTransImpl-->sendFile-->服务端异常--"
									+ errorMsg);
					throw new SunECMException(status,
							"---SunDM-->SocketClientTransImpl-->sendFile-->服务端异常--"
									+ errorMsg);
				}
				long skipLength=0;
				if (content.length >= 2) {
					maxbps = Integer.valueOf(content[1]);
				}
				if (content.length == 3) {
					skipLength = Integer.valueOf(content[2]);
				}
				fileSize-=skipLength;
				// 分块传输
				long skipedLength=inputStream.skip(skipLength);
				if(skipedLength!=skipLength){
					throw new SunECMException("断点文件与服务端文件大小不一致");
				}
				boolean transFlag = true;
				int off = 0;
                //写入前28字节
				outStream.write(b,0,b.length);
				if (maxbps > 0) { // 需要进行流量控制
					FlowController fin = new FlowController(inputStream,
							maxbps);
					log.debug( "当前速率" + fin.check() / 1000 + "kbps,文件大小" + fin.available() / 1024 + "kb");
					long start = System.currentTimeMillis();
					byte[] buffer = new byte[1024];
					int realLength = 0;
					while ((realLength = fin.read(buffer)) > 0) {
						outStream.write(buffer, 0, realLength);
					}
					fin.close();
					long end = System.currentTimeMillis();
					log.debug( Thread.currentThread().getName() + "读取文件" + filePath
							+ "耗时" + (end - start) / 600 + "秒");
				}
				if (maxbps == 0) { // 不需要进行流量控制
					while (transFlag) {
						int bufSize = transBufferSize;
						if (fileSize < transBufferSize) {
							bufSize = Integer.parseInt(Long.toString(fileSize));
							transFlag = false;
						}
						byte[] buf = new byte[bufSize];
						int l=inputStream.read(buf);
						if(l!=-1){
							outStream.write(buf,0,l);		
						}
						outStream.flush();
						fileSize = fileSize - transBufferSize;
						off += transBufferSize;			
					}
				}
				
				
				String afterXml = receiveMsg();
				if(afterXml==null){
					log.error("返回为空");
					return false;
				}
				log.debug( "客户端文件发送后完成返回的报文,return msg = " + afterXml);
				String []results=afterXml.split(TransOptionKey.SPLITSYM);
				if(!TransOptionKey.SERVER_OK.equals(results[1])){
					log.warn( afterXml);
					throw new SunECMException(afterXml);
				}
				return true;
			} catch (SunECMException e) {
				log.error(
						"-->SocketConn-->sendFileData-->SunECMException-->"
								+ e.toString());
				throw new SunECMException(status, e.toString());
			} finally {
				if (inputStream != null) {
					inputStream.close();	
				}
				if (tempFile != null) {
					tempFile.delete();
				}
			}
		} catch (IOException e) {
			log.error(
					"-->sendFileData-->sendFileData-->IOException-->"
							+ e.toString());
			throw new IOException(e.toString());
		}
	}
	
	
	
	public boolean sendFileData(String filePath, String contentID,
			String transType) throws SunECMException, IOException {
//		log.info( "SOCKET发送文件,文件路径：" +filePath+",内容ID:"+contentID);
		int i = filePath.lastIndexOf('.');
		String format = "";
		if ((i > -1) && (i < (filePath.length()))) {
			format = filePath.substring(i + 1, filePath.length());
		}
		int transBufferSize = ClientConfiguration.getInt("client.socket.buffersize", 65536);
		int maxbps = 0;
		FileInputStream fileInputStream = null;
		File tempFile = null;
		int status = 700;
		try {
			try {
				File file = new File(filePath);
				if (!file.exists()) {
					log.error( "文件不存在,文件路径："+file.getPath());
					return false;
				}
				long fileSize = file.length();
				filePath=filePath.replaceAll(",", "<<DH>>");
				String str = transType + "FILENAME=" + file.getName()
						+ ",FILESIZE=" + Long.toString(fileSize)
						+ ",BUFFERSIZE=" + transBufferSize + ",FILEPATH="
						+ filePath + ",CONTENTID = " + contentID + ",FORMAT="
						+ format;
				log.debug( "客户端发送文件报文 , socket client send file msg=" + str);
				sendMsg(str);
				String result = receiveMsg();
				if(result==null||result.equals("")){
					log.error( "客户端文件发送前接收返回的报文,return msg 为空 [" + result+"]");
					return false;
				}
				log.debug( "客户端文件发送前接收返回的报文,return msg = " + result);
				String[] content = result.split(TransOptionKey.SPLITSYM);
				
				if (!String.valueOf(TransOptionKey.SERVER_OK)
						.equals(content[0])) {
					status = Integer.parseInt(content[0]);
					//错误信息
					String errorMsg=" server no msg";
					if(content.length>1){
						errorMsg=content[1];
					}
					log.error( "---SunDM-->SocketClientTransImpl-->sendFile-->服务端异常--"
									+ errorMsg);
					throw new SunECMException(status,
							"---SunDM-->SocketClientTransImpl-->sendFile-->服务端异常--"
									+ errorMsg);
				}
				long skipLength=0;
				if (content.length >= 2) {
					maxbps = Integer.valueOf(content[1]);
				}
				if (content.length == 3) {
					skipLength = Integer.valueOf(content[2]);
				}
				fileSize-=skipLength;
				// 分块传输
				fileInputStream = new FileInputStream(file);
				long skipedLength=fileInputStream.skip(skipLength);
				if(skipedLength!=skipLength){
					throw new SunECMException("断点文件与服务端文件大小不一致");
				}
				boolean transFlag = true;
				int off = 0;

				if (maxbps > 0) { // 需要进行流量控制
					FlowController fin = new FlowController(fileInputStream,
							maxbps);
					log.debug( "当前速率" + fin.check() / 1000 + "kbps,文件大小" + fin.available() / 1024 + "kb");
					long start = System.currentTimeMillis();
					byte[] buffer = new byte[1024];
					int realLength = 0;
					while ((realLength = fin.read(buffer)) > 0) {
						outStream.write(buffer, 0, realLength);
					}
					fin.close();
					long end = System.currentTimeMillis();
					log.debug( Thread.currentThread().getName() + "读取文件" + file
							+ "耗时" + (end - start) / 600 + "秒");
				}
				if (maxbps == 0) { // 不需要进行流量控制
					while (transFlag) {
						int bufSize = transBufferSize;
						if (fileSize < transBufferSize) {
							bufSize = Integer.parseInt(Long.toString(fileSize));
							transFlag = false;
						}
						byte[] buf = new byte[bufSize];
						int l=fileInputStream.read(buf);
						if(l!=-1){
							outStream.write(buf,0,l);
						}
						outStream.flush();
						fileSize = fileSize - transBufferSize;
						off += transBufferSize;
					}
				}
				String afterXml = receiveMsg();
				if(afterXml==null){
					log.error("返回为空");
					return false;
				}
				log.debug( "客户端文件发送后完成返回的报文,return msg = " + afterXml);
				String []results=afterXml.split(TransOptionKey.SPLITSYM);
				if(!TransOptionKey.SERVER_OK.equals(results[1])){
					log.warn( afterXml);
					throw new SunECMException(afterXml);
				}
				return true;
			} catch (SunECMException e) {
				log.error(
						"-->SocketConn-->sendFileData-->SunECMException-->"
								+ e.toString());
				throw new SunECMException(status, e.toString());
			} finally {
				if (fileInputStream != null) {
					fileInputStream.close();
				}
				if (tempFile != null) {
					tempFile.delete();
				}
			}
		} catch (IOException e) {
			log.error(
					"-->sendFileData-->sendFileData-->IOException-->"
							+ e.toString());
			throw new IOException(e.toString());
		}
	}

	public void destroy() {
		log.info("关闭SOCKET连接"+socketAddrress);
		try {
			in.close();
		} catch (IOException e) {
			log.error("关闭连接出错",e);
		}
		if (outStream != null) {
			try {
				outStream.flush();
				outStream.close();
				if (out != null) {
					out.close();
				}
			} catch (IOException e) {
				log.error("",e);
			}
		}

		try {
			socket.close();
		} catch (IOException e) {
			log.warn("close socket get excption："+socketAddrress, e);
		}
		log.info("关闭SOCKET连接over"+socketAddrress);
	}
	public boolean isClosed(){
		return socket.isClosed();
	}
	public OutputStream getOutputStream() throws IOException{
		return this.socket.getOutputStream();
	}

	public String getSocketAddrress() {
		return socketAddrress;
	}

	public void close() {
		try {
			this.sendMsg(TransOptionKey.DISCONNECT_PROCESS);
			this.receiveMsg();
		} catch (Exception e) {
			log.error("出错",e);
		}
		finally{
			this.destroy();
		}
	}
}