package com.sunyard.console.common.ext;

import com.sunyard.console.common.ext.form.DateField;
import com.sunyard.console.common.ext.form.TextField;

import java.util.HashMap;
import java.util.Map;


/**
 * <p>Title: 表单辅助类</p>
 * <p>Description: 将不同类型文本框加入到表单中</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ExtFormHelp {
	/**
	 * 创建文本输入框
	 * 
	 * @param label	标签名
	 * @param name	文本框名
	 * @return
	 */
	public static TextField createTextField(String label ,String id,String name){
		TextField field = new TextField();
		
		field.setXtype("textfield");
		field.setProperties(TextField.ID, id);
		field.setProperties(TextField.NAME, name);
		field.setProperties(TextField.FIELDLABEL,label);
		field.setProperties(TextField.WIDTH, 100);
		return field;
	}
	/**
	 * 创建日期输入框
	 * @param label
	 * @param name
	 * @return
	 */
	public static DateField createDateField(String label , String name){
		DateField field = new DateField();
		field.setProperties(DateField.XTYPE,"datefield");
		field.setProperties(DateField.NAME, name);
		field.setProperties(DateField.FORMAT,"Ymd");
		field.setProperties(DateField.WIDTH,100);
		field.setProperties(DateField.FIELDLABEL,label);
		
		return field;
	}
	
	public static Map<String,String> createNumberField(String label ,String name){
		Map<String,String> combo = new HashMap<String,String>();
		combo.put("xtype", "numberfield");
		combo.put("fieldLabel", label);
		combo.put("name", name);
		return combo;
	}

}
