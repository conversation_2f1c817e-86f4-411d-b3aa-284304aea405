import request from '@/utils/request'
import EndUrl from './publicUrl.js'

export function getTags(data) {
  const obj = {
    'page': data.page, 
    'limit': data.limit, 
    'tag_name': encodeURI(data.tag_name),
    'tag_code': data.tag_code
  }
  return request({
    url: '/tagManage/getTagsAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function delTag(data) {
  const url = '/tagManage/delTagAction'+EndUrl.EndUrl;
  const obj = {
    'tag_code': data.tag_code
  }
  return request({
    url: url,
    method: 'post',
    params: { data: obj }
  })
}

export function getExistAttrsTree(data) {
  return request({
    url: '/tagManage/getExistAttrsTreeAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'tag_code': data
    }
  })
}

export function getNotExistAttrsTree(data) {
  return request({
    url: '/tagManage/getNotExistAttrsTreeAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'tag_code': data
    }
  })
}

export function updateTagAttrs(data) {
  const obj = {
    'optionFlag': data.optionFlag,
    'tag_code': data.tag_code,
    'tag_name': encodeURI(data.tag_name),
    'attrNames': data.attrNames,
    'attrTypes': data.attrTypes,
    'attrIndexs': data.attrIndexs,
    'attrAboves': data.attrAboves    
  }
  return request({
    url: '/tagManage/updateTagAttrsAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }

  })
}

export function getUnRelContentObjectTree(data) {
  return request({
    url: '/tagManage/getUnRelContentObjectTreeAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'tag_code': data
    }
  })
}

export function getRelContentObjectTree(data) {
  return request({
    url: '/tagManage/getRelContentObjectTreeAction'+EndUrl.EndUrl,
    method: 'post',
    params: {
      'tag_code': data
    }
  })
}

export function updateTagModels(data) {
  const obj = {
    'tag_code': data.tag_code,
    'model_ids': data.model_ids
  }
  return request({
    url: '/tagManage/updateTagModelsAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }

  })
}

export function synTagtoES(data) {
  const url = '/tagManage/createEsIndexToSunECMDM'+EndUrl.EndUrl;
  return request({
    url: url,
    method: 'post',
    params: {       
      'tag_code': data
    }
  })
}

export function getTagModel(data) {
  const obj = {
    'page': data.page, 
    'limit': data.limit, 
    'tag_code': data.tag_code
  }
  return request({
    url: '/tagManage/getTagModelAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function startModelRelState(data) {
  const obj = {
    'tag_code': data.tag_code,
    'model_code': data.model_code,
    'state' : '1'
  }
  return request({
    url: '/tagManage/modifyModelRelStateAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}
export function stopModelRelState(data) {
  const obj = {
    'tag_code': data.tag_code,
    'model_code': data.model_code,
    'state' : '0'
  }
  return request({
    url: '/tagManage/modifyModelRelStateAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function checkTagName(data,dia) {
  const obj = {
    'tag_code': data.tag_code,
    'tag_name': data.tag_name,
    'optionFlag' : dia
  }
  return request({
    url: '/tagManage/checkTagNameAction'+EndUrl.EndUrl,
    method: 'post',
    params: { data: obj }
  })
}

export function searchEsFromDM(data) {
  return request({
    url: '/tagManage/searchEsFromDMAction'+EndUrl.EndUrl,
    method: 'post',
    params: { tag_code: data }
  })
}

export function getESAttrTypeList() {
  return request({
    url: '/tagManage/getESAttrTypeAction'+EndUrl.EndUrl,
    method: 'get'
  })
}