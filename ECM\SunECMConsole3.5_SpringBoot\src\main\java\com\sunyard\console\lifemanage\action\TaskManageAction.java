package com.sunyard.console.lifemanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import javax.servlet.http.HttpSession;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.console.lifemanage.bean.TaskInfoBean;
import com.sunyard.console.lifemanage.dao.TaskManageDAO;
import com.sunyard.console.process.exception.DBRuntimeException;
import com.sunyard.console.safemanage.bean.UserInfoBean;

import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>
 * Title: 任务管理action
 * </p>
 * <p>
 * Description: 用于管理系统任务信息,包括查询，新增，修改，删除等操作的action访问类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class TaskManageAction extends BaseAction {
	/**
	 * 日志对象
	 */
	private  final static Logger log = LoggerFactory.getLogger(TaskManageAction.class);
	/**
	 * 任务主键
	 */
	private int task_no;;
	/**
	 * 任务名称
	 */
	private String task_name;
	/**
	 * 任务类路径
	 */
	private String task_class;
	/**
	 * 任务中使用的参数key，如有多个可以;(分号)隔开(如：天数:days;名字:name)
	 */
	private String param_key;
	/**
	 * 分页显示记录的开始位置
	 */
	private int start;
	/**
	 * 分页显示记录的总条数
	 */
	private int limit;
	/**
	 * 新增或者修改任务标识符
	 */
	private String optionFlag;
	/**
	 * 任务管理数据库操作对象
	 */
	@Autowired
	private TaskManageDAO taskManageDao;
	public int getTask_no() {
		return task_no;
	}

	public void setTask_no(int taskNo) {
		task_no = taskNo;
	}

	public String getTask_name() {
		return task_name;
	}

	public void setTask_name(String taskName) {
		task_name = taskName;
	}

	public String getTask_class() {
		return task_class;
	}

	public void setTask_class(String taskClass) {
		task_class = taskClass;
	}

	public String getParam_key() {
		return param_key;
	}

	public void setParam_key(String paramKey) {
		param_key = paramKey;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	public TaskManageDAO getTaskManageDao() {
		return taskManageDao;
	}

	public void setTaskManageDao(TaskManageDAO taskManageDao) {
		this.taskManageDao = taskManageDao;
	}

	/**
	 * 任务查询action 将结果集组合成jsonStr，返回给页面
	 * 
	 * @return null
	 * @throws UnsupportedEncodingException 
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/getTaskListAction.action", method = RequestMethod.POST)
	public String getTaskList(String data) throws UnsupportedEncodingException {
		 JSONObject modelJson = JSONObject.fromObject(data);
	     String task_name = URLDecoder.decode(modelJson.getString("task_name"),"utf-8");
	     int page_int = 0;
	     String page = modelJson.getString("page");
	     if (page != null) {
			page_int = Integer.parseInt(page);
	     }
	     int limit_int = 0;
	     String limit = modelJson.getString("limit");
	     if (limit != null) {
			limit_int = Integer.parseInt(limit);
	     }
	     start = (page_int-1) * limit_int;
	     String task_no = modelJson.getString("task_no");
		log.info("--getTaskList(start)-->task_name:" + task_name);
		String jsonStr = null;
		task_no = "".equals(task_no) || task_no == null ? "0" : task_no;
		task_name = task_name == null ? null : task_name.trim();
		try {
			List<TaskInfoBean> list = taskManageDao.getTaskList(Integer.valueOf(task_no),
					task_name, start + 1, limit_int);
			List<TaskInfoBean> AllList = taskManageDao.getTaskList(Integer.valueOf(task_no),
					task_name);
			int size = 0;
			if (AllList != null && AllList.size() > 0) {
				size = AllList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(list, size,
					new TaskInfoBean());
			log.debug( "--getTaskList-->size:" + size);
		} catch (DBRuntimeException e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取任务信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "任务管理->查询任务失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getTaskList(over)-->task_name:" + task_name);
		return null;
	}

	/**
	 * 新增任务
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/addTaskListAction.action", method = RequestMethod.POST)
	public String addTask(String task_name, String task_class, String param_key, String optionFlag, int task_no) {
		log.info("--addTask(start)-->task_name:" + task_name);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		HttpSession session = getSession();
		UserInfoBean userBean = (UserInfoBean) session
				.getAttribute("loginUser");
		if (userBean == null) {
			log.warn( "--addTask-->配置任务失败!用户登陆已超时,请重新登陆");
			jsonResp.put("success", false);
			jsonResp.put("message", "配置任务失败!用户登陆已超时,请重新登陆!");
			jsonStr = jsonResp.toString();
			this.outJsonString(jsonStr);
			return null;
		}
		TaskInfoBean bean = new TaskInfoBean();
		task_name = task_name == null ? null : task_name.trim();
		task_class = task_class == null ? null : task_class.trim();
		param_key = param_key == null ? null : param_key.trim();
		optionFlag = optionFlag == null ? "" : optionFlag.trim();
		bean.setTask_no(task_no);
		bean.setTask_name(task_name);
		bean.setTask_class(task_class);
		bean.setParam_key(param_key);
		try {
			log.debug( "--addTask-->optionFlag:" + optionFlag);
			if (optionFlag.equals("create")) {// 新增任务
				if (taskManageDao.addTask(bean)) {
					jsonResp.put("success", true);
					jsonResp.put("message", "新增任务成功!!");
				} else {
					jsonResp.put("success", true);
					jsonResp.put("message", "新增任务失败!!");
				}
			} else {// 修改任务
				if (taskManageDao.updateTask(bean)) {
					jsonResp.put("success", true);
					jsonResp.put("message", "修改任务成功!!");
				} else {
					jsonResp.put("success", true);
					jsonResp.put("message", "修改任务失败!!");
				}
			}
			jsonStr = jsonResp.toString();
			log.debug( "--addTask-->optionFlag:" + optionFlag);
		} catch (DBRuntimeException e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置修改失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "任务管理->配置任务失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--addTask(over)-->task_name:" + task_name);
		return null;
	}

	/**
	 * 删除任务action
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/delTaskAction.action", method = RequestMethod.POST)
	public String delTask(int task_no) {
		log.info( "--delTask(start)-->task_no:" + task_no);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean result = taskManageDao.delTask(task_no);
			log.debug( "--delTask-->result:" + result);
			if (result) {
				jsonResp.put("message", "删除任务成功!!");
			} else {
				jsonResp.put("message", "删除任务失败!!");
			}
			jsonResp.put("success", result);
			jsonStr = jsonResp.toString();
		} catch (DBRuntimeException e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "删除任务失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "任务管理->删除任务失败:" + e.toString(), e);
		}

		this.outJsonString(jsonStr);
		log.info( "--delTask(over)-->task_no:" + task_no);
		return null;
	}

	/**
	 * 校验该任务是否被使用, 删除任务的时候需要判断
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/checkTaskUsedAction.action", method = RequestMethod.POST)
	public String checkTaskUsed(int task_no) {
		log.info( "--checkTaskUsed(start)-->task_no:" + task_no);
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;

		try {
			boolean result = taskManageDao.checkTaskUsed(task_no);
			log.debug( "--checkTaskUsed-->result:" + result);
			if (result) {
				jsonResp.put("message", "该任务正在被使用");
			} else {
				jsonResp.put("message", "该任务未被使用");
			}
			jsonResp.put("success", true);
			jsonStr = jsonResp.toString();
		} catch (DBRuntimeException e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "校验任务是否被使用失败");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error( "任务管理-校验该任务是否被使用失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info( "--checkTaskUsed(over)-->task_no:" + task_no);
		return null;

	}
}
