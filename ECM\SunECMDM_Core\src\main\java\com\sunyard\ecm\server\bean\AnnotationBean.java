package com.sunyard.ecm.server.bean;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/**
 * 批注类
 * <AUTHOR>
 *
 */
@XStreamAlias("Annotation")
public class AnnotationBean {
	@XStreamAsAttribute
	private String ANNOTATION_ID;// 批注ID，36位GUID
	@XStreamAsAttribute
	private String ANNOTATION_FLAG;// 批注操作标识，新增或是修改OptionKey
	@XStreamAsAttribute
	private String ANNOTATION_CONTENT;// 批注内容
	@XStreamAsAttribute
	private String ANNOTATION_USER;// 批注用户
	@XStreamAsAttribute
	private String ANNOTATION_COLOR;// 批注颜色
	@XStreamAsAttribute
	private String ANNOTATION_X1POS;// 批注起始x坐标
	@XStreamAsAttribute
	private String ANNOTATION_Y1POS;// 批注起始y坐标
	@XStreamAsAttribute
	private String ANNOTATION_X2POS;// 批注结束x坐标
	@XStreamAsAttribute
	private String ANNOTATION_Y2POS;// 批注结束y坐标
	@XStreamAsAttribute
	private String S_VERSION;// 版本号

	public String getAnnotation_id() {
		return ANNOTATION_ID;
	}

	public void setAnnotation_id(String annotationId) {
		ANNOTATION_ID = annotationId;
	}

	public String getAnnotation_content() {
		return ANNOTATION_CONTENT;
	}

	public void setAnnotation_content(String annotationContent) {
		ANNOTATION_CONTENT = annotationContent;
	}

	public String getAnnotation_user() {
		return ANNOTATION_USER;
	}

	public void setAnnotation_user(String annotationUser) {
		ANNOTATION_USER = annotationUser;
	}

	public String getAnnotation_color() {
		return ANNOTATION_COLOR;
	}

	public void setAnnotation_color(String annotationColor) {
		ANNOTATION_COLOR = annotationColor;
	}

	public String getAnnotation_x1pos() {
		return ANNOTATION_X1POS;
	}

	public void setAnnotation_x1pos(String annotationX1pos) {
		ANNOTATION_X1POS = annotationX1pos;
	}

	public String getAnnotation_x2pos() {
		return ANNOTATION_X2POS;
	}

	public void setAnnotation_x2pos(String annotationX2pos) {
		ANNOTATION_X2POS = annotationX2pos;
	}

	public String getAnnotation_y1pos() {
		return ANNOTATION_Y1POS;
	}

	public void setAnnotation_y1pos(String annotationY1pos) {
		ANNOTATION_Y1POS = annotationY1pos;
	}

	public String getAnnotation_y2pos() {
		return ANNOTATION_Y2POS;
	}

	public void setAnnotation_y2pos(String annotationY2pos) {
		ANNOTATION_Y2POS = annotationY2pos;
	}

	public String getS_version() {
		return S_VERSION;
	}

	public void setS_version(String sVersion) {
		S_VERSION = sVersion;
	}

	public String getAnnotation_flag() {
		return ANNOTATION_FLAG;
	}

	public void setAnnotation_flag(String annotationFlag) {
		ANNOTATION_FLAG = annotationFlag;
	}
}