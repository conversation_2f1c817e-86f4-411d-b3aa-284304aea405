<?xml version="1.0" encoding="UTF-8"?>
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出-->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数-->
<Configuration status="WARN" monitorInterval="30">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
		    <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="%d{yyyyMMdd HH:mm:ss.SSS} [%t] [%-5p] %c{1}.%M-%L: %m%n" />
        </Console>
         <!--文件会打印出所有信息，这个log每次运行程序会自动清空，由append属性决定，这个也挺有用的，适合临时测试用-->

        <!-- <File name="log" fileName="D:/test.log" append="false">
             <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
             <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>

         </File>-->
		 
       <RollingFile name="RollingFileINFO" filename="c:/log/es.log"
                     filePattern="c:/log/$${date:yyyy-MM}/log-%d{yyyy-MM-dd}-%i.log4j2dm">
		     <!--只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->       
             <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
			 
            <PatternLayout charset="GB2312" pattern="%d{yyyyMMdd HH:mm:ss.SSS} [%t] [%-5p] %c{1}.%M-%L: %m%n" />
            <!-- 按日志大小存储日志 -->
            <!--  <Policies>
                <SizeBasedTriggeringPolicy size="100 MB" />
            </Policies>
            <DefaultRolloverStrategy max="30" /> -->
            
            <!-- 按日志天数存储日志 --> 
            <Policies>
				<TimeBasedTriggeringPolicy modulate="true" interval="1"/>
			</Policies>
	
        </RollingFile>        
    </Appenders>
	
    <Loggers>
    <logger name="org.springframework" level="INFO"></logger>
        <logger name="org.apache.cxf" level="INFO"></logger>
    
	 <Root level="debug" includeLocation="true">
	    <AppenderRef ref="Console" />
	    <AppenderRef ref="RollingFileINFO" /> 
	  </Root>
    </Loggers>
</Configuration>
