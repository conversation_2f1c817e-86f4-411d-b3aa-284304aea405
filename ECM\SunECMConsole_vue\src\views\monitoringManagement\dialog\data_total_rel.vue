<template>
  <div>
    <el-row :gutter="8">
		<el-col :xs="24" :sm="24" :lg="13">
			<div :id="canvas1"  :style="{height:height,width:width1,padding: padding}">
			</div>
		</el-col>
		<el-col :xs="24" :sm="24" :lg="8">
			<div :id="canvas3" :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
	 </el-row>
	 <el-row :gutter="8">
		<el-col :xs="24" :sm="24" :lg="13">
        	<div :id="canvas2" :style="{height:height,width:width1,padding: padding}">
        	</div>
    	</el-col>
		<el-col :xs="24" :sm="24" :lg="8">
        	<div :id="canvas4" :style="{height:height,width:width,padding: padding}">
        	</div>
    	</el-col>
	</el-row>
  </div>
</template>


<style>
/* .transfer-footer {
  margin-left: 20px;
  padding: 6px 5px;
} */
</style>

<script>
import {getInitAction} from '@/api/monitorManage'
import * as echarts from 'echarts'

export default {
  name: "data-total-rel",
  props: {
    listQuery: {
      require: true,
      type: Object,
    },
    canvas1: {
      type: String,
      default: 'chart1'
    },
    canvas2: {
        type: String,
        default: 'chart2'
      },
    canvas3: {
        type: String,
        default: 'chart3'
      },
    canvas4: {
        type: String,
        default: 'chart4'
      },
    width1: {
      type: String,
      default: '600px'
    },
    width: {
      type: String,
      default: '500px'
    },
    height: {
      type: String,
      default: '250px'
    },
    padding: {
      type: String,
    //   default: '16px 16px'
    }
  },
  data() {
    return{
        successTask : [], 
        failTask : [], 
        minTime : [], 
        avgTime : [], 
        maxTime : [], 
        sumFile : [], 
        sumFileSize : [],
		chart1 : null,
        chart2 : null,
        chart3 : null,
        chart4 : null,
        time : null
    }
  },
  
    mounted() {
      this.$nextTick(() => {
        this.showCharts()
      })
    },

	beforeDestroy(){
		clearInterval(this.timer);
		this.timer=null;
	},
  methods: {
    showCharts(){
	  const _this = this;
      this.chart1 = echarts.init(document.getElementById(this.canvas1), 'dark');
      this.chart2 = echarts.init(document.getElementById(this.canvas2), 'dark');
	  this.chart3 = echarts.init(document.getElementById(this.canvas3), 'dark');
      this.chart4 = echarts.init(document.getElementById(this.canvas4), 'dark');

        let option1 = {
				title : {
					textStyle : {
						color : '#e2e9ff',
					},
					x : 'center',
					text : '任务数统计'
				},
				legend : {
					x : 'right',
					textStyle : {
						color : '#e2e9ff'
					},
					data : [ '成功', '失败' ]
				},
				tooltip : {
					trigger : 'axis',
					axisPointer : {
						type : 'line',
					// animation: true
					}
				},
				grid : { //控制图的大小
					top : '20%',
					left : '3%',
					right : '1%',
					bottom : '3%',
					containLabel : true
				},
				xAxis : [ {
					axisLine : {
						lineStyle : {
							color : 'rgba(255,255,255,0.12)'
						}
					},
					axisLabel : {
						margin : 10,
						color : '#e2e9ff',
						textStyle : {
							fontSize : 12
						},
					},
					triggerEvent : {
						componentType : 'xAxis',
						value : '',
						name : 'trigger'
					},
					type : 'category',
					data : [ '批次上传', '批次更新', '高级检索', '批次查询', '文件下载', '批次迁移' ]
				} ],
				yAxis : [ {
					name : '(笔)',
					nameTextStyle : {
						color : "#e2e9ff"
					},
					type : 'value',
					axisLabel : {
						formatter : '{value}',
						color : '#e2e9ff',
					},
					axisLine : {
						show : false
					},
					splitLine : {
						lineStyle : {
							color : 'rgba(255,255,255,0.12)'
						}
					}
				} ],
				series : [
						{
							barWidth : '20%',
							name : '成功',
							type : 'bar',
							stack : '数量',
							// 各事件对应的成功个数
							data : this.successTask,
							itemStyle : {
								normal : {
									color : new echarts.graphic.LinearGradient(0, 0, 0, 1, [ {
												offset : 0,
												color : 'rgba(0,244,255,1)' // 0% 处的颜色
											}, {
												offset : 1,
												color : 'rgba(0,77,167,1)' // 100% 处的颜色
											} ], false),
									barBorderRadius : [ 20, 20, 20, 20 ],
									shadowColor : 'rgba(0,160,221,1)',
									shadowBlur : 4,
								}
							},
						},
						{
							barWidth : '20%',
							name : '失败',
							type : 'bar',
							stack : '数量',
							//各事件对应的失败个数
							data : this.failTask,
							itemStyle : {
								normal : {
									color : new echarts.graphic.LinearGradient(0, 0, 0, 1, [ {
												offset : 0,
												color : 'rgba(238,93,2,1)' // 0% 处的颜色
											}, {
												offset : 1,
												color : 'rgba(237,206,53,1)' // 100% 处的颜色
											} ], false),

									barBorderRadius : [ 20, 20, 20, 20 ],
									shadowColor : 'rgba(0,160,221,1)',
									shadowBlur : 4,
								}
							}
						} ]
		};
		let option2 = {
			title : {
				textStyle : {
					color : '#e2e9ff',
				},
				x : 'center',
				text : '任务耗时统计'
			},
			grid : { //控制图的大小
				//	top:'20%',
				left : '3%',
				right : '1%',
				bottom : '5%',
				containLabel : true
			},
			legend : {
				x : 'right',
				textStyle : {
					color : '#e2e9ff'
				},
				//orient : 'vertical',
				data : [ '最小', '平均', '最大' ]
			},
			tooltip : {
				trigger : 'axis',
				axisPointer : {
					type : 'shadow',
				}
			},
			xAxis : {
				axisLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				axisLabel : {
					margin : 10,
					color : '#e2e9ff',
					textStyle : {
						fontSize : 12
					},
				},
				triggerEvent : {
					componentType : 'xAxis',
					value : '',
					name : 'trigger'
				},
				type : 'category',
				data : [ '批次上传', '批次更新', '高级检索', '批次查询', '文件下载', '批次迁移' ]
			},
			yAxis : {
				name : '(ms)',
				nameTextStyle : {
					color : "#e2e9ff"
				},
				type : 'value',
				axisLabel : {
					formatter : '{value}',
					color : '#e2e9ff',
				},
				axisLine : {
					show : false
				},
				splitLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				}
			},
			series : [
					{
						name : '最小',
						type : 'bar',
						barWidth : '15%',
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0, 0, 0, 1, [ {
											offset : 0,
											color : 'rgba(32,223,94,1)' // 0% 处的颜色
										}, {
											offset : 1,
											color : 'rgba(101,252,155,1)' // 100% 处的颜色
										} ], false),
								barBorderRadius : [ 20, 20, 20, 20 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						data : this.minTime
					},
					{
						name : '平均',
						type : 'bar',
						barWidth : '15%',
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0, 0, 0, 1, [ {
											offset : 0,
											color : 'rgba(238,93,2,1)' // 0% 处的颜色
										}, {
											offset : 1,
											color : 'rgba(237,206,53,1)' // 100% 处的颜色
										} ], false),

								barBorderRadius : [ 20, 20, 20, 20 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						data : this.avgTime
					},
					{
						name : '最大',
						type : 'bar',
						barWidth : '15%',
						itemStyle : {
							normal : {
								color : new echarts.graphic.LinearGradient(0, 0, 0, 1, [ {
											offset : 0,
											color : 'rgba(0,244,255,1)' // 0% 处的颜色
										}, {
											offset : 1,
											color : 'rgba(0,77,167,1)' // 100% 处的颜色
										} ], false),
								barBorderRadius : [ 20, 20, 20, 20 ],
								shadowColor : 'rgba(0,160,221,1)',
								shadowBlur : 4,
							}
						},
						data : this.maxTime
					} ]
		};
		let option3 = {
			color : [ '#61a0a8' ],
			title : {
				text : '文件接收数量',
				x : 'center',
				textStyle : {
					color : '#e2e9ff',
				},
			},
			grid : { //控制图的大小
				top : '20%',
				left : '1%',
				right : '6%',
				bottom : '3%',
				containLabel : true
			},
			tooltip : {
				trigger : 'axis',
				axisPointer : { // 坐标轴指示器，坐标轴触发有效
					type : 'line' // 默认为直线，可选为：'line' | 'shadow'
				}
			},
			xAxis : {
				triggerEvent : {
					componentType : 'xAxis',
					value : '',
					name : 'trigger'
				},
				axisLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				axisLabel : {
					margin : 10,
					color : '#e2e9ff',
					textStyle : {
						fontSize : 12
					},
				},
				type : 'category',
				data : [ '批次上传', '批次更新' ]
			},
			yAxis : {
				name : '(张)',
				nameTextStyle : {
					color : "#e2e9ff"
				},
				type : 'value',
				axisLabel : {
					formatter : '{value}',
					color : '#e2e9ff',
				},
				axisLine : {
					show : false
				},
				splitLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				}
			},
			series : [ {
				//type: "pictorialBar",
				data : this.sumFile,
				barWidth : '11%',
				type : 'bar',
				itemStyle : {
					normal : {
						color : new echarts.graphic.LinearGradient(0, 0, 0, 1, [ {
									offset : 0,
									color : 'rgba(0,244,255,1)' // 0% 处的颜色
								}, {
									offset : 1,
									color : 'rgba(0,77,167,1)' // 100% 处的颜色
								} ], false),
						barBorderRadius : [ 20, 20, 20, 20 ],
						shadowColor : 'rgba(0,160,221,1)',
						shadowBlur : 4,
					}
				},
				label : {
					normal : {
						rich : {
							d : {
								color : '#3CDDCF',
							},
							a : {
								color : '#fff',
								align : 'center',
							},
							b : {
								width : 1,
								height : 30,
								borderWidth : 1,
								borderColor : '#234e6c',
								align : 'left'
							},
						}
					}
				}
			} ]

		};
		let option4 = {
			title : {
				textStyle : {
					color : '#e2e9ff',
				},
				x : 'center',
				text : '文件大小'
			},
			grid : { //控制图的大小
				//	top:'20%',
				left : '1%',
				right : '10%',
				bottom : '5%',
				containLabel : true
			},
			tooltip : {
				trigger : 'axis',
				axisPointer : { // 坐标轴指示器，坐标轴触发有效
					type : 'line' // 默认为直线，可选为：'line' | 'shadow'
				}
			},
			xAxis : {
				triggerEvent : {
					componentType : 'xAxis',
					value : '',
					name : 'trigger'
				},
				nameTextStyle : {
					color : "#e2e9ff"
				},
				type : 'value',
				axisLabel : {
					formatter : '{value}',
					color : '#e2e9ff',
				},
				axisLine : {
					show : false
				},
				splitLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				type : 'category',
				data : [ '上传', '更新' ]
			},
			yAxis : {
				name : '(M)',
				nameTextStyle : {
					color : "#e2e9ff"
				},
				type : 'value',
				axisLabel : {
					formatter : '{value}',
					color : '#e2e9ff',
				},
				axisLine : {
					show : false
				},
				splitLine : {
					lineStyle : {
						color : 'rgba(255,255,255,0.12)'
					}
				},
				type : 'value'
			},
			series : [ {
				itemStyle : {
					normal : {
						color : new echarts.graphic.LinearGradient(0, 0, 0,1, [ {
									offset : 0,
									color : 'rgba(0,244,255,1)' // 0% 处的颜色
								}, {
									offset : 1,
									color : 'rgba(0,77,167,1)' // 100% 处的颜色
								} ], false),
						barBorderRadius : [ 5, 5, 0, 0 ],
						shadowColor : 'rgba(0,160,221,1)',
						shadowBlur : 4,
					}
				},
				data : this.sumFileSize,
				barWidth : '14%',
				type : 'bar'
			} ]
		};
      this.chart1.setOption(option1);
	  this.chart2.setOption(option2);
	  this.chart3.setOption(option3);
	  this.chart4.setOption(option4);

		this.chart1.on('click', function(params) {
			let name = params.value;
			let option;
			switch (name) {
			case '批次上传':
				option = 'UPLOAD'				
				break;
			case '批次更新':
				option = 'UPDATE'				
				break;
			case '高级检索':
				option = 'HEIGQUERY'				
				break;
			case '批次查询':
				option = 'QUERY'				
				break;
			case '文件下载':
				option = 'GETFILE'				
				break;
			case '批次迁移':
				option = 'MIGRATE'				
				break;
			}
		clearInterval(_this.timer);
		_this.timer=null;

		let chartnum = "1"+"_"+option;
		_this.$emit('checkChartNum',chartnum);
		});

		this.chart2.on('click', function(params) {
			let name = params.value;
			let option;
			switch (name) {
			case '批次上传':
				option = 'UPLOAD'				
				break;
			case '批次更新':
				option = 'UPDATE'				
				break;
			case '高级检索':
				option = 'HEIGQUERY'				
				break;
			case '批次查询':
				option = 'QUERY'				
				break;
			case '文件下载':
				option = 'GETFILE'				
				break;
			case '批次迁移':
				option = 'MIGRATE'				
				break;
			}
		clearInterval(_this.timer);
		_this.timer=null;

		let chartnum = "2"+"_"+option;
		_this.$emit('checkChartNum',chartnum);
		});

		this.chart3.on('click', function(params) {
			let name = params.value;
			let option;
			switch (name) {
			case '批次上传':
				option = 'UPLOAD'				
				break;
			case '批次更新':
				option = 'UPDATE'				
				break;
			}
		clearInterval(_this.timer);
		_this.timer=null;

		let chartnum = "3"+"_"+option;
		_this.$emit('checkChartNum',chartnum);
		});

		this.chart4.on('click', function(params) {
			let name = params.value;
			let option;
			switch (name) {
			case '上传':
				option = 'UPLOAD'				
				break;
			case '更新':
				option = 'UPDATE'				
				break;
			}
		clearInterval(_this.timer);
		_this.timer=null;

		let chartnum = "4"+"_"+option;
		_this.$emit('checkChartNum',chartnum);
		});
    },

    showData(){
      let date = new Date();
		let month = date.getMonth() + 1;
		let time = date.getDate();
		month = month < 10 ? "0" + month : month;
		time = time < 10 ? "0" + time : time;
		this.listQuery.date = date.getFullYear()+'' + month +''+ time;
     	this.timer = setInterval(() => { 
			getInitAction(this.listQuery).then(response => {
				this.chart1.hideLoading();
				this.chart2.hideLoading();
				this.chart3.hideLoading();
				this.chart4.hideLoading();
				let upload = response.UPLOAD;
				let update = response.UPDATE;
				let heiguery = response.HEIGQUERY;
				let query = response.QUERY;
				let getfile = response.GETFILE;
				let migrate = response.MIGRATE;
				let option = [upload, update, heiguery, query, getfile,
						migrate];
				this.successTask = [];
				this.failTask = [];
				this.minTime = [];
				this.avgTime = [];
				this.maxTime = [];
				this.sumFile = [];
				this.sumFileSize = [];
				for (let i = 0; i < 6; i++) {
					if (i < 2) {
						this.sumFile.push(option[i].sumFile);
						this.sumFileSize.push(option[i].sumFileSize);
					}
					this.successTask.push(option[i].successTask);
					this.failTask.push(option[i].failTask);
					this.minTime.push(option[i].minTime);
					this.avgTime.push(option[i].avgTime);
					this.maxTime.push(option[i].maxTime);
				}
				this.chart1.setOption({
					series : [ {
						data : this.successTask
					}, {
						data : this.failTask
					} ]
				});
				this.chart2.setOption({
					series : [ {
						data : this.minTime
					}, {
						data : this.avgTime
					}, {
						data : this.maxTime
					} ]
				});
				this.chart3.setOption({
					series : [ {
						data : this.sumFile
					} ]
				});
				this.chart4.setOption({
					series : [ {
						data : this.sumFileSize
					} ]
				})
			})
		},5000);
	}
  }
};
</script>
<style scoped>
/* .edit_dev >>> .el-transfer-panel {
     width:350px;
   } */
</style>