package com.xxl.job.admin.dao;

import com.xxl.job.admin.core.model.JobRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface JobRelationDao  {
    public List<JobRelation> pageListWithDesc(@Param("offset") int offset, @Param("pagesize") int pagesize, @Param("jobId") int jobId, @Param("executorHandler") String executorHandler);
    public int pageListCount(@Param("offset") int offset, @Param("pagesize") int pagesize, @Param("jobId") int jobId, @Param("executorHandler") String executorHandler);

    public int insertRelation(JobRelation jobRelations);

    public JobRelation loadById(@Param("id") int id);

    public int update(JobRelation item);

    public int delete(@Param("id") int id);


    public List<JobRelation> loadJByJobId(@Param("jobId")Integer  jobId);

    List<JobRelation> jobQueryList(@Param("jobId")int jobId);
}
