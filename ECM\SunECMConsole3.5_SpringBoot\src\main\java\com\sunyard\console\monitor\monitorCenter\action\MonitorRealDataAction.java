package com.sunyard.console.monitor.monitorCenter.action;

import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.struts.BaseAction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.common.ReportConstant;
import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.monitor.monitorCenter.dao.MonitorManageDao;
import com.sunyard.msgqueue.msgreport.ReportOpt;
import com.sunyard.redis.RedisClient;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
public class MonitorRealDataAction extends BaseAction {

	private final static Logger log = LoggerFactory.getLogger(MonitorRealDataAction.class);
	@Autowired
	private MonitorManageDao monitorManageDao; // 监控管理DAO
	private String server_id; // 服务器ID
	private String option;
	private String modelName;
	private String[] modelNames;

	private String date;
	private boolean flag;// 是否第一次访问页面标识
	ReportOpt reportOpt = new ReportOpt();

	public MonitorManageDao getMonitorManageDao() {
		return monitorManageDao;
	}

	public void setMonitorManageDao(MonitorManageDao monitorManageDao) {
		this.monitorManageDao = monitorManageDao;
	}

	public String[] getModelNames() {
		return modelNames;
	}

	public void setModelNames(String[] modelNames) {
		this.modelNames = modelNames;
	}

	public boolean getFlag() {
		return flag;
	}

	public void setFlag(boolean flag) {
		this.flag = flag;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getOption() {
		return option;
	}

	public void setOption(String option) {
		this.option = option;
	}

	public String getModelName() {
		return modelName;
	}

	public void setModelName(String modelName) {
		this.modelName = modelName;
	}

	public String getServer_id() {
		return server_id;
	}

	public void setServer_id(String serverId) {
		server_id = serverId;
	}

	/**
	 * 当天相关数据获取
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getInitAction.action", method = RequestMethod.POST)
	public String getInit(String data) {
		log.info("--->开始获取接口服务统计数据--->");
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");
		log.debug("开始获取reDis数据");
		Map<String, Map<String, String>> map = reportOpt.getReportMapForDay(modelName, server_id, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (!map.keySet().contains("UPLOAD")) {
			Map subMap = new HashMap<String, String>();
			subMap.put("successTask", "0");
			subMap.put("failTask", "0");
			subMap.put("minTime", "0");
			subMap.put("avgTime", "0");
			subMap.put("maxTime", "0");
			subMap.put("sumFile", "0");
			subMap.put("sumFileSize", "0");
			map.put("UPLOAD", subMap);
		}
		if (!map.keySet().contains("UPDATE")) {
			Map subMap = new HashMap<String, String>();
			subMap.put("successTask", "0");
			subMap.put("failTask", "0");
			subMap.put("minTime", "0");
			subMap.put("avgTime", "0");
			subMap.put("maxTime", "0");
			subMap.put("sumFile", "0");
			subMap.put("sumFileSize", "0");
			map.put("UPDATE", subMap);
		}
		if (!map.keySet().contains("HEIGQUERY")) {
			Map subMap = new HashMap<String, String>();
			subMap.put("successTask", "0");
			subMap.put("failTask", "0");
			subMap.put("minTime", "0");
			subMap.put("avgTime", "0");
			subMap.put("maxTime", "0");
			map.put("HEIGQUERY", subMap);
		}
		if (!map.keySet().contains("QUERY")) {
			Map subMap = new HashMap<String, String>();
			subMap.put("successTask", "0");
			subMap.put("failTask", "0");
			subMap.put("minTime", "0");
			subMap.put("avgTime", "0");
			subMap.put("maxTime", "0");
			map.put("QUERY", subMap);
		}
		if (!map.keySet().contains("GETFILE")) {
			Map subMap = new HashMap<String, String>();
			subMap.put("successTask", "0");
			subMap.put("failTask", "0");
			subMap.put("minTime", "0");
			subMap.put("avgTime", "0");
			subMap.put("maxTime", "0");
			map.put("GETFILE", subMap);
		}
		if (!map.keySet().contains("MIGRATE")) {
			Map subMap = new HashMap<String, String>();
			subMap.put("successTask", "0");
			subMap.put("failTask", "0");
			subMap.put("minTime", "0");
			subMap.put("avgTime", "0");
			subMap.put("maxTime", "0");
			map.put("MIGRATE", subMap);
		}
		for (String key : map.keySet()) {
			jsonResp.put(key, map.get(key));
		}
		jsonResp.put("code", 20000);
		log.info("--->接口服务统计数据获取完成！--->");
		this.outJsonString(jsonResp.toString());
		return null;

	}

	/**
	 * 获取历史上传文件成功失败数量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getUploadFileNumAction.action")
	public String getUploadFileNum(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月上传接口文件任务数 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.UPLOAD + " 数据类型:" + ReportConstant.TASK);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.UPLOAD, ReportConstant.TASK, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月上传接口文件任务数完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史更新文件成功失败数量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getUpDateFileNumAction.action")
	public String getUpDateFileNum(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月更新接口文件任务数 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.UPDATE + " 数据类型:" + ReportConstant.TASK);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.UPDATE, ReportConstant.TASK, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月更新接口文件任务数完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史高级检索文件成功失败数量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getHeighQFileNumAction.action")
	public String getHeighQFileNum(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月高级查询接口文件任务数 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.HEIGQUERY + " 数据类型:" + ReportConstant.TASK);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.HEIGQUERY, ReportConstant.TASK, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月高级查询接口文件任务数完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史查询文件成功失败数量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getQueryFileNumAction.action")
	public String getQueryFileNum(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月查询接口文件任务数 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.QUERY + " 数据类型:" + ReportConstant.TASK);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.QUERY, ReportConstant.TASK, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月查询接口文件任务数完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史下载文件成功失败数量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getDownloadFileNumAction.action")
	public String getDownloadFileNum(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月文件下载接口文件任务数 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.GETFILE + " 数据类型:" + ReportConstant.TASK);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.GETFILE, ReportConstant.TASK, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月文件下载接口文件任务数完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取迁移接口成功失败数量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getMigrateFileNumAction.action")
	public String getMigrateFileNum(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月迁移接口文件任务数 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.MIGRATE + " 数据类型:" + ReportConstant.TASK);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.MIGRATE, ReportConstant.TASK, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月迁移接口接口文件任务数完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史上传耗时
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getUploadTimeCostAction.action")
	public String getUploadTimeCost(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月上传接口耗时 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.UPLOAD + " 数据类型:" + ReportConstant.TIME);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.UPLOAD, ReportConstant.TIME, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月上传接口耗时完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史更新耗时
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getUpdateTimeCostAction.action")
	public String getUpdateTimeCost(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月更新接口耗时 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.UPDATE + " 数据类型:" + ReportConstant.TIME);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.UPDATE, ReportConstant.TIME, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月更新接口耗时完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史高级检索耗时
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getHeighQTimeCostAction.action")
	public String getHeighQTimeCost(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月更新接口耗时 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.HEIGQUERY + " 数据类型:" + ReportConstant.TIME);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.HEIGQUERY, ReportConstant.TIME, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月更新接口耗时完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史查询耗时
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getQueryTimeCostAction.action")
	public String getQueryTimeCost(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月查詢接口耗时 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.QUERY + " 数据类型:" + ReportConstant.TIME);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.QUERY, ReportConstant.TIME, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月查詢接口耗时完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史下载耗时
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getDownloadTimeCostAction.action")
	public String getDownloadTimeCost(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月文件下载接口耗时 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.GETFILE + " 数据类型:" + ReportConstant.TIME);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.GETFILE, ReportConstant.TIME, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月文件下载接口耗时完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取历史迁移耗时
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getMigrateTimeCostAction.action")
	public String getMigrateTimeCost(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月迁移接口耗时 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.MIGRATE + " 数据类型:" + ReportConstant.TIME);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.MIGRATE, ReportConstant.TIME, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月迁移接口耗时完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取总上传文件大小
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getUploadFileSizeAction.action")
	public String getUploadFileSize(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月上传文件大小 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.UPLOAD + " 数据类型:" + ReportConstant.COUNTFILESIZE);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.UPLOAD, ReportConstant.COUNTFILESIZE, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月上传文件大小完成 ！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取总更新文件大小
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getUpdateFileSizeAction.action")
	public String getUpdateFileSize(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月更新文件大小 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.UPDATE + " 数据类型:" + ReportConstant.COUNTFILESIZE);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.UPDATE, ReportConstant.COUNTFILESIZE, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取月更新文件大小完成 ！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取总上传文件数量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getUploadFileAction.action")
	public String getUploadFile(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月上传文件大小 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.UPLOAD + " 数据类型:" + ReportConstant.COUNTFILE);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.UPLOAD, ReportConstant.COUNTFILE, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取上传文件数量完成 ！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取总更新文件数量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getUpdateFileAction.action")
	public String getUpdateFile(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String date = modelJson.getString("date");		
		log.info("---> 开始获取月更新文件数量 ---> 模型名:" + modelName + " 服务器ID:" + server_id + " 时间:" + date + " 接口:"
				+ ReportConstant.UPDATE + " 数据类型:" + ReportConstant.COUNTFILE);
		Map<String, Map<String, String>> map = reportOpt.getReportMapForMonth(modelName, server_id,
				ReportConstant.UPDATE, ReportConstant.COUNTFILE, date);
		log.debug("获取reDis数据完成");
		JSONObject jsonResp = new JSONObject();
		if (map.size() > 0) {
			for (String key : map.keySet()) {
				jsonResp.put(key, map.get(key));
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取更新文件数量完成 ！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 准实时耗时(服务器组)
	 * 
	 * @throws ParseException
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getRealTimeDataAction.action")
	public String getRealTimeData(String data) throws ParseException {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String option = modelJson.getString("scatterOption");
		boolean flag = modelJson.getBoolean("flag");
		log.info("---> 开始获取接口耗时数据 ---> 模型名:" + modelName + " 服务器:" + server_id + " 接口:" + option);
		JSONObject jsonResp = new JSONObject();
		String[] servers = server_id.split(",");
		RedisClient redisClient = new RedisClient();
		String mm = new SimpleDateFormat("mm").format(new Date());
		SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		Date timer = new Date();
		if (!flag) {
			for (int i = 0; i < servers.length; i++) {
				String key = modelName + "|" + servers[i] + "|" + option + "|" + mm;
				List<String> list = redisClient.getList(key);
				if (list.size() > 0) {
					TreeMap tree = new TreeMap<String, String>();
					for (int j = 0; j < list.size(); j++) {
						String[] arr = list.get(j).split("\\|");
						tree.put(sf.parse(arr[0]).getTime(), arr[2]);
					}
					for (Object treeKey : tree.keySet()) {
						jsonResp.put(servers[i] + "=" + treeKey.toString(), tree.get(treeKey).toString());
					}
				} else {
					jsonResp.put(servers[i] + "=" + timer.getTime(), ",");
				}
			}
		} else {
			log.debug("页面初始化,获取一小时前数据");
			for (int i = 0; i < servers.length; i++) {
				for (int t = Integer.valueOf(mm) + 1; t < 60; t++) {
					Date time = new Date();
					time.setHours(time.getHours() - 1);
					time.setMinutes(t);
					jsonResp.put(servers[i] + "=" + time.getTime(), ",");
					String key;
					if (t <= 9) {
						key = modelName + "|" + servers[i] + "|" + option + "|0" + t;
					} else {
						key = modelName + "|" + servers[i] + "|" + option + "|" + t;
					}
					List<String> list = redisClient.getList(key);
					if (list.size() > 0) {
						TreeMap tree = new TreeMap<String, String>();
						for (int j = 0; j < list.size(); j++) {
							String[] arr = list.get(j).split("\\|");
							tree.put(sf.parse(arr[0]).getTime(), arr[2]);
						}
						for (Object treeKey : tree.keySet()) {
							jsonResp.put(servers[i] + "=" + treeKey.toString(), tree.get(treeKey).toString());
						}
					}
				}
				for (int t = 0; t < Integer.valueOf(mm); t++) {
					Date time = new Date();
					time.setMinutes(t);
					jsonResp.put(servers[i] + "=" + time.getTime(), ",");
					String key;
					if (t <= 9) {
						key = modelName + "|" + servers[i] + "|" + option + "|0" + t;
					} else {
						key = modelName + "|" + servers[i] + "|" + option + "|" + t;
					}
					List<String> list = redisClient.getList(key);
					if (list.size() > 0) {
						TreeMap tree = new TreeMap<String, String>();
						for (int j = 0; j < list.size(); j++) {
							String[] arr = list.get(j).split("\\|");
							tree.put(sf.parse(arr[0]).getTime(), arr[2]);
						}
						for (Object treeKey : tree.keySet()) {
							jsonResp.put(servers[i] + "=" + treeKey.toString(), tree.get(treeKey).toString());
						}
					}
				}
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取接口耗时数据完成 --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取服务器各模型socket连接数
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getSocketConnNumAction.action")
	public String getSocketConnNum(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelNameString = modelJson.getString("modelNames");
		String[] modelNames = modelNameString.split(",");
		String server_id = modelJson.getString("server_id");
		boolean flag = modelJson.getBoolean("flag");

		log.info("---> 开始获取socket连接数 ---> 模型名:" + modelNames + " 服务器:" + server_id);
		JSONObject jsonResp = new JSONObject();
		Map<String, String> map;
		if (flag) {
			log.debug("页面初始化,开始获取一小时前socket连接数");
			for (int i = 0; i < modelNames.length; i++) {
				map = reportOpt.getSocketNumForHour(server_id, modelNames[i]);
				if (map.size() > 0) {
					for (String key : map.keySet()) {
						jsonResp.put(modelNames[i] + "_" + key, map.get(key));
					}
				}
			}
		} else {
			for (int i = 0; i < modelNames.length; i++) {
				map = reportOpt.getSocketNumForSecond(server_id, modelNames[i]);
				if (map.size() > 0) {
					for (String key : map.keySet()) {
						jsonResp.put(modelNames[i] + "_" + key, map.get(key));
					}
				}
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> socket连接数获取完成！ --->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取服务器TCP监控数据
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getTCPSocketStatusAction.action")
	public String getTCPSocketStatus(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = modelJson.getString("server_id");
		boolean flag = modelJson.getBoolean("flag");
		log.info("---> 开始获取TCP状态数据 ---> 服务器:" + server_id);
		ContentServerInfoBean ServerInfo = monitorManageDao.getServerIpById(server_id);
		int socket = ServerInfo.getSocket_port();
		String ip = ServerInfo.getServer_ip();
		Map<String, Map<String, String>> map;
		JSONObject jsonResp = new JSONObject();
		if (flag) {
			log.debug("页面初始化,开始获取前一小时TCP状态数据 ");
			map = reportOpt.getTcpStatusForHour(ip, String.valueOf(socket));
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					int arr[] = new int[9];
					int i = 0;
					for (String sysElem : map.get(key).keySet()) {
						arr[i] = Integer.valueOf(map.get(key).get(sysElem));
						i++;
					}
					jsonResp.put(key, arr);
				}
			}

		} else {
			map = reportOpt.getTcpStatusForSecond(ip, String.valueOf(socket));
			if (map.size() > 0) {
				for (String key : map.keySet()) {
					int arr[] = new int[9];
					int i = 0;
					for (String sysElem : map.get(key).keySet()) {
						arr[i] = Integer.valueOf(map.get(key).get(sysElem));
						i++;
					}
					jsonResp.put(key, arr);
				}
			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取TCP状态数据完成！--->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取一天任务量
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getTaskOfDayAction.action")
	public String getTaskOfDay(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String modelName = modelJson.getString("model_code");
		String server_id = modelJson.getString("server_id");
		String option = modelJson.getString("option");
		String date = modelJson.getString("date");

		log.info("---> 开始获取一天每小时任务数 ---> 服务器:" + server_id + " 模型名:" + modelName + " 接口:" + option + " 时间:" + date);
		Map<String, Map<String, String>> map = reportOpt.getTaskReportForHour(modelName, server_id, option, date);
		JSONObject jsonResp = new JSONObject();
		TreeMap<Integer, Map<String, String>> tree = new TreeMap<Integer, Map<String, String>>();
		Map<String, String> empty = new HashMap<String, String>();
		empty.put("failTask", "0");
		empty.put("successTask", "0");
		for (int i = 0; i < 24; i++) {
			String temp = String.valueOf(i);
			if (i < 10) {
				temp = "0" + String.valueOf(i);
			}
			if (!map.containsKey(temp)) {
				tree.put(i, empty);
			}
		}
		for (Object key : map.keySet()) {
			tree.put(Integer.valueOf(key.toString()), map.get(key));
		}
		for (Object key : tree.keySet()) {
			jsonResp.put(key, tree.get(key));
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取一天每小时任务数完成！--->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取总数(批次总数、迁移清理总数、离线清理总数)
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getTaskCountNumAction.action")
	public String getTaskCountNum(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = modelJson.getString("server_id");
		String modelName = modelJson.getString("model_code");
		String date = modelJson.getString("date");
		log.info("---> 开始获取迁移清理、离线清理及批次总数 ---> 服务器:" + server_id + " 模型名:" + modelName);
		String servers[] = server_id.split(",");
		Map<String, Integer> sum = null;
		Map<String, String> migrateMap = null;
		Map<String, String> clearMap = null;
		Map<String, String> offlineMap = null;
		Map<String, String> offlineClearMap = null;
		JSONObject jsonResp = new JSONObject();
		Map<String, String> tempMap = new HashMap<String, String>();
		tempMap.put("failTask", "0");
		tempMap.put("successTask", "0");
		tempMap.put("maxTime", "0");
		tempMap.put("avgTime", "0");
		tempMap.put("minTime", "0");
		int batchsum = 0;
		int migrateclearsum = 0;
		int offlineclearsum = 0;
		String migrateS[] = new String[servers.length];
		String migrateF[] = new String[servers.length];
		String clearS[] = new String[servers.length];
		String clearF[] = new String[servers.length];
		String offlineS[] = new String[servers.length];
		String offlineF[] = new String[servers.length];
		String offlineClearS[] = new String[servers.length];
		String offlineClearF[] = new String[servers.length];
		// [服務器][迁移、迁移清理、离线、离线清理]
		String maxTime[][] = new String[servers.length][];
		String avgTime[][] = new String[servers.length][];
		String minTime[][] = new String[servers.length][];

		for (int i = 0; i < servers.length; i++) {
			sum = reportOpt.getBatchSum(modelName, servers[i]);
			migrateMap = reportOpt.getContentMigrateReport(date, modelName, servers[i]);
			clearMap = reportOpt.getContentClearReport(date, modelName, servers[i]);
			offlineMap = reportOpt.getContentOfflineReport(date, modelName, servers[i]);
			offlineClearMap = reportOpt.getOfflineClearReport(date, modelName, servers[i]);

			batchsum = batchsum + Integer.valueOf(sum.get("batchsum"));
			migrateclearsum = migrateclearsum + Integer.valueOf(sum.get("migrateclearsum"));
			offlineclearsum = offlineclearsum + Integer.valueOf(sum.get("offlineclearsum"));
			
			if (migrateMap.size() == 0) {
				migrateMap = tempMap;
			}
			if (clearMap.size() == 0) {
				clearMap = tempMap;
			}
			if (offlineMap.size() == 0) {
				offlineMap = tempMap;
			}
			if (offlineClearMap.size() == 0) {
				offlineClearMap = tempMap;
			}
			migrateS[i] = migrateMap.get("successTask");
			migrateF[i] = migrateMap.get("failTask");

			clearS[i] = clearMap.get("successTask");
			clearF[i] = clearMap.get("failTask");

			offlineS[i] = offlineMap.get("successTask");
			offlineF[i] = offlineMap.get("failTask");

			offlineClearS[i] = offlineClearMap.get("successTask");
			offlineClearF[i] = offlineClearMap.get("failTask");
			
			String tempMax[] = new String[4];
			String tempAvg[] = new String[4];
			String tempMin[] = new String[4];
			tempMax[0] = migrateMap.get("maxTime");
			tempMax[1] = clearMap.get("maxTime");
			tempMax[2] = offlineMap.get("maxTime");
			tempMax[3] = offlineClearMap.get("maxTime");

			tempAvg[0] = migrateMap.get("avgTime");
			tempAvg[1] = clearMap.get("avgTime");
			tempAvg[2] = offlineMap.get("avgTime");
			tempAvg[3] = offlineClearMap.get("avgTime");

			tempMin[0] = migrateMap.get("minTime");
			tempMin[1] = clearMap.get("minTime");
			tempMin[2] = offlineMap.get("minTime");
			tempMin[3] = offlineClearMap.get("minTime");

			maxTime[i] = tempMax;
			avgTime[i] = tempAvg;
			minTime[i] = tempMin;
			if (i == servers.length - 1) {
				jsonResp.put("MsuccessTask", migrateS);
				jsonResp.put("MfailTask", migrateF);
				jsonResp.put("MCsuccessTask", clearS);
				jsonResp.put("MCfailTask", clearF);
				jsonResp.put("OsuccessTask", offlineS);
				jsonResp.put("OfailTask", offlineF);
				jsonResp.put("OCsuccessTask", offlineClearS);
				jsonResp.put("OCfailTask", offlineClearF);
				jsonResp.put("batchsum", batchsum);
				jsonResp.put("migrateclearsum", migrateclearsum);
				jsonResp.put("offlineclearsum", offlineclearsum);
				jsonResp.put("maxTime", maxTime);
				jsonResp.put("avgTime", avgTime);
				jsonResp.put("minTime", minTime);

			}
		}
		jsonResp.put("code", 20000);
		log.info("---> 获取总数完成！--->");
		this.outJsonString(jsonResp.toString());
		return null;
	}

	@ResponseBody
	@RequestMapping(value = "/monitorManage/getSchedulerNumofMonthAction.action")
	public String getSchedulerNumofMonth(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = modelJson.getString("server_id");
		String modelName = modelJson.getString("model_code");
		String date = modelJson.getString("date");
		JSONObject jsonResp = new JSONObject();
		String servers[] = server_id.split(",");
		Map<String, Map<String, String>> migrateSum = new HashMap<String, Map<String, String>>();
		Map<String, Map<String, String>> clearSum = new HashMap<String, Map<String, String>>();
		Map<String, Map<String, String>> offlineSum = new HashMap<String, Map<String, String>>();
		Map<String, Map<String, String>> offlineCSum = new HashMap<String, Map<String, String>>();
		Map<String, String> temp = new HashMap<String, String>();
		for (int i = 0; i < servers.length; i++) {
			Map<String, Map<String, String>> migrateM = reportOpt.getContentMigrateMonth(date, modelName, servers[i]);
			Map<String, Map<String, String>> clearM = reportOpt.getContentClearMonth(date, modelName, servers[i]);
			Map<String, Map<String, String>> offlineM = reportOpt.getContentOfflineMonth(date, modelName, servers[i]);
			Map<String, Map<String, String>> offlineCM = reportOpt.getOfflineClearMonth(date, modelName, servers[i]);
			if (migrateM.size() > 0) {
				for (String key : migrateM.keySet()) {
					if (!migrateSum.containsKey(key)) {
						migrateSum.put(key, migrateM.get(key));
					} else {
						temp=new HashMap<String, String>();
						for (String task : migrateSum.get(key).keySet()) {
							temp.put(task, String.valueOf(Integer.valueOf(migrateSum.get(key).get(task))
									+ Integer.valueOf(migrateM.get(key).get(task))));
						}
						migrateSum.put(key, temp);
					}
				}
			}
			if (clearM.size() > 0) {
				for (String key : clearM.keySet()) {
					if (!clearSum.containsKey(key)) {
						clearSum.put(key, clearM.get(key));
					} else {
						temp=new HashMap<String, String>();
						for (String task : clearSum.get(key).keySet()) {
							temp.put(task, String.valueOf(Integer.valueOf(clearSum.get(key).get(task))
									+ Integer.valueOf(clearM.get(key).get(task))));
						}
						clearSum.put(key, temp);
					}
				}
			}
			if (offlineM.size() > 0) {
				for (String key : offlineM.keySet()) {
					if (!offlineSum.containsKey(key)) {
						offlineSum.put(key, offlineM.get(key));
					} else {
						temp=new HashMap<String, String>();
						for (String task : offlineSum.get(key).keySet()) {
							temp.put(task, String.valueOf(Integer.valueOf(offlineSum.get(key).get(task))
									+ Integer.valueOf(offlineM.get(key).get(task))));
						}
						offlineSum.put(key, temp);
					}
				}
			}
			if (offlineCM.size() > 0) {
				for (String key : offlineCM.keySet()) {
					if (!offlineCSum.containsKey(key)) {
						offlineCSum.put(key, offlineCM.get(key));
					} else {
						temp=new HashMap<String, String>();
						for (String task : offlineCSum.get(key).keySet()) {
							temp.put(task, String.valueOf(Integer.valueOf(offlineCSum.get(key).get(task))
									+ Integer.valueOf(offlineCM.get(key).get(task))));
						}
						offlineCSum.put(key, temp);
					}
				}
			}
		}
		jsonResp.put("M", migrateSum);
		jsonResp.put("MC", clearSum);
		jsonResp.put("O", offlineSum);
		jsonResp.put("OC", offlineCSum);
		jsonResp.put("code", 20000);
		this.outJsonString(jsonResp.toString());
		return null;
	}

	/**
	 * 获取指定服务器线程池使用情况
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getSocketPoolStatusAction.action")
	public String getSocketPoolStatus(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = modelJson.getString("server_id");
		log.info("--getSocketPoolStatus(start)-->server_id:" + server_id);
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray = new JSONArray();
 		if (server_id == null || server_id.equals("")) {
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonResp.put("code", 20000);
			jsonResp.put("totalProperty", "0");// 总行数
			this.outJsonString(jsonResp.toString());
			return null;
		}
		Map<String, String> map = reportOpt.getSoccketStartTime(server_id);
		jsonResp.put("result", "success");
		jsonResp.put("code", 20000);
		jsonResp.put("success", "true");
		for (String key : map.keySet()) {
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("server_id", server_id);
			jsonObj.put("socket_Name", key);
			jsonObj.put("socket_Time", map.get(key));
			jsonArray.add(jsonObj);
		}
		jsonResp.put("totalProperty", map.size() + "");// 总行数
		jsonResp.put("root", jsonArray);
		log.debug("jsonResp: " + jsonResp.toString(1));
		this.outJsonString(jsonResp.toString(1));
		log.info("--getSocketPoolStatus(over)-->server_id:" + server_id);
		return null;
	}

	/**
	 * 获取迁移线程池各线程的开始时间
	 * 
	 * @throws SQLException
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getMigrateThreadPoolAction.action")
	public String getMigrateThreadPool(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = modelJson.getString("server_id");
		log.info("--getMigrateThreadPool(start)-->server_id:" + server_id);
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray = new JSONArray();
		if (server_id == null || server_id.equals("")) {
			jsonResp.put("result", "success");
			jsonResp.put("success", "true");
			jsonResp.put("code", 20000);
			jsonResp.put("totalProperty", "0");// 总行数
			this.outJsonString(jsonResp.toString());
			return null;
		}
		Map<String, String> map = reportOpt.getMigrateStartTime(server_id);
		jsonResp.put("result", "success");
		jsonResp.put("code", 20000);
		jsonResp.put("success", "true");
		for (String key : map.keySet()) {
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("server_id", server_id);
			jsonObj.put("thread_Name", key);
			jsonObj.put("thread_Time", map.get(key));
			jsonArray.add(jsonObj);
		}
		jsonResp.put("totalProperty", map.size() + "");// 总行数
		jsonResp.put("root", jsonArray);
		log.debug("jsonResp: " + jsonResp.toString(1));
		this.outJsonString(jsonResp.toString(1));
		log.info("--getMigrateThreadPool(over)-->server_id:" + server_id);
		return null;
	}

	/**
	 * 获取迁移队列批次及队列大小
	 * 
	 * @throws SQLException
	 */
	@ResponseBody
	@RequestMapping(value = "/monitorManage/getMigrateBlockQueueAction.action")
	public String getMigrateBlockQueue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String server_id = modelJson.getString("server_id");
		String option = modelJson.getString("option");
		String sql = "SELECT SERVER_IP FROM CONTENT_SERVER_INFO WHERE SERVER_ID='" + server_id + "'";
		String ip = null;
		try {
			ip = DataBaseUtil.SUNECM.queryString(sql);
		} catch (SQLException e) {
			log.error("获取服务器IP出错：" + e.toString());
			return null;
		}
		log.info("--getMigrateBlockQueue(start)-->server ID:" + server_id + " server IP:" + ip + " level:" + option);
		JSONObject jsonResp = new JSONObject();
		JSONArray jsonArray = new JSONArray();
		Map<String, String> map = new HashMap<String,String>();
		if (ip == null || ip.equals("")) {
			log.error("服务器IP为空！");
			this.outJsonString(jsonResp.toString());
			return null;
		}
		if (option.equals("Low")) {
			map = reportOpt.getLowMigrateQueue(ip);// 低优先级迁移队列
		} else {
			map = reportOpt.getHighMigrateQueue(ip);// 高优先级迁移队列
		}
		Map<String, String> map2 = reportOpt.getMigrateQueueSize(ip);// 迁移队列大小
		jsonResp.put("result", "success");
		jsonResp.put("code", 20000);
		jsonResp.put("success", "true");
		for (String key : map.keySet()) {
			JSONObject jsonObj = new JSONObject();
			jsonObj.put("contentId", key);
			jsonObj.put("modelCode", map.get(key));
			if (map2.containsKey(map.get(key))) {
				jsonObj.put("modelSize", map2.get(map.get(key)));
			}
			jsonArray.add(jsonObj);
		}
		jsonResp.put("totalProperty", map.size() + "");// 总行数
		jsonResp.put("root", jsonArray);
		log.debug("jsonResp: " + jsonResp.toString(1));
		this.outJsonString(jsonResp.toString(1));
		log.info("--getMigrateBlockQueue(over)-->server ID:" + server_id + " server IP:" + ip + " level:" + option);
		return null;

	}
}
