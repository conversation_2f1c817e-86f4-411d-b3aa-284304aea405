package com.sunyard.console.common.util;

/**
 * 内容采集系统全局变量类
 * <AUTHOR>
 *
 */
public abstract class Constant {
	
	private Constant(){
	}
	
	/**
	 * 登录后的用户名称
	 */
	public static final String USER_LOGIN_FLAG = "LOGIN_USER_NAME";
	
	/**
	 * 系统运行模式，生产模式
	 */
	public static final String SUN_SYS_RUN_MODEL_PROD = "PROD";
	
	/**
	 * 系统运行模式，开发模式
	 */
	public static final String SUN_SYS_RUN_MODEL_DEVE = "DEVE";
	
	/**
	 * 接口调用参数：身份标识
	 */
	public static final String SUN_PARAM_UID = "UID";
	
	/**
	 * 接口调用参数：口令标识
	 */
	public static final String SUN_PARAM = "PWD";
	
	/**
	 * 接口调用参数：应用标识
	 */
	//public static final String SUN_PARAM_APPID = "AppID";
	
	/**
	 * 接口调用参数：身份标识
	 */
	public static final String SUN_PARAM_OPER_USER_ID = "UserID";
	
	/**
	 * 接口调用参数：用户名
	 */
	public static final String SUN_PARAM_OPER_USER_NAME = "UserName";
	
	/**
	 * 接口调用参数：用户所在机构ID
	 */
	//public static final String SUN_PARAM_ORGAN_ID = "OrgID";
	
	/**
	 * 接口调用参数：用户所在机构名称
	 */
	//public static final String SUN_PARAM_ORGAN_NAME = "OrgName";
	
	/**
	 * 接口调用参数：用户权限标识
	 */
	public static final String SUN_PARAM_RIGHT = "right";
	
	/**
	 * 接口调用参数：业务信息参数
	 */
	public static final String SUN_PARAM_INFO = "info";
	

	/**
	 * 接口调用参数：业务资料等级
	 */
	public static final String SUN_PARAM_FILE_LEVEL = "fileLevel";
	
	/**
	 * 接口调用参数:历史版本
	 */
	public static final String SUN_PARAM_HISTORYVERSION = "HistoryVersion";
	
	/**
	 * 权限标识位数组
	 */
	public static final String SUN_PERMISSION_RIGHTS = "PERMISSION_RIGHTS";
	
	/**
	 * 内容管理平台属性：元数据对象名
	 */
	public static final String SUN_ECM_OBJECT_NAME = "OBJECT_NAME";
	
	/**
	 * 版本控制标识
	 */
	public static final String SUN_ECM_OBJECT_VERSIONCONTROL = "IS_VERSION";
	
	/**
	 * 元数据别名
	 */
	public static final String SUN_OBJECT_ALIAS = "OBJECT_ALIAS";
	
	/**
	 * 元数据备注
	 */
	public static final String SUN_OBJECT_REMARK = "OBJECT_REMARK";
	
	/**
	 * 一次请求的所有元数据对象名
	 */
	public static final String SUN_ECM_OBJECT_NAMES = "OBJECT_NAMES";
	
	/**
	 * 请求的所有参数信息
	 */
	public static final String SUN_REQUEST_PARAMETERS = "REQUEST_PARAMETERS";
	public static final String SUN_NODE_INFO_MAP ="SUN_NODE_INFO_MAP";
	/**
	 * 业务索引信息
	 */
	public static final String BUSI_ATTR_VALUE_MAP = "BUSI_ATTR_VALUE_MAP";
	
	/**
	 * 业务属性：业务流水号
	 */
	public static final String BUSI_ATTR_SERIAL_NO = "BUSI_SERIAL_NO";
	public static final String SUN_CONTENT_NO ="SUN_CONTENT_NO";
	
	public static final String DOC_INDEX_INFO_MAP = "DOC_INDEX_INFO_MAP";
	/**
	 *  机构、业务、节点、文档类型的XML集合
	 */
	public static final String METADATA_BEAN_OBJECT = "METADATA_BEAN_OBJECT";
	
	/**
	 * 请求参数中以INFO开头命名的参数和流水号之间的对应关系
	 */
	public static final String INFOX_SERIAL_NO_MAPPING = "INFOX_SERIAL_NO_MAPPING";
	
	public static final String NECESSARY_FILETYPE_COLLECTION = "NECESSARY_FILETYPE_COLLECTION";
	
	public static final String SCAN_METADATA_TREE_ID = "tree-metadata-id";
	
	public static final String BATCH_ATTRIBUTE_ACCORDION_ID_PREFIX = "accordion-";
	
	public static final String BATCH_ATTRIBUTE_FORM_ID_PREFIX = "batch-attribute-form-";
	
	public static final String BATCH_ATTRIBUTE_FORM_HAS_VALUE = "hasValueFlag";
	
	/**
	 * 扫描权限、查看权限、删除权限、修改权限、打印权限、批注权限、管理员权限
	 */
	public static final String SUN_PERMISSION_RIGHT_INDEX      = "RIGHT_INDEX_";
	public static final String SUN_PERMISSION_RIGHT_SCAN       = "RIGHT_INDEX_1";        // 扫描权限
	public static final String SUN_PERMISSION_RIGHT_VIEW       = "RIGHT_INDEX_2";        // 查看权限
	public static final String SUN_PERMISSION_RIGHT_DELETE     = "RIGHT_INDEX_3";        // 删除权限
	public static final String SUN_PERMISSION_RIGHT_MODIFY     = "RIGHT_INDEX_4";        // 修改权限
	public static final String SUN_PERMISSION_RIGHT_PRINT      = "RIGHT_INDEX_5";        // 打印权限
	public static final String SUN_PERMISSION_RIGHT_ANNOTATION = "RIGHT_INDEX_6";        // 批注权限
	public static final String SUN_PERMISSION_RIGHT_ADMIN      = "RIGHT_INDEX_7";        // 管理员权限
	
	public static final String SUN_CONTENT_KEY_NAME   = "KEY";
	
	public static final String SUN_CONTENT_KEY_WORD   = "VALUE";
	
	public static final String ENCRYPTED_ORDER   = "com/sunyard";
	

}
