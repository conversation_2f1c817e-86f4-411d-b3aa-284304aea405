package com.sunyard.console.poolmanage.dao;

import com.sequoiadb.base.SequoiadbDatasource;
import com.sequoiadb.datasource.DatasourceOptions;
import com.sequoiadb.net.ConfigOptions;
import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.database.PageTool;
import com.sunyard.console.common.util.Constant;
import com.sunyard.console.poolmanage.bean.PoolInfoBean;
import com.sunyard.console.process.exception.DBRuntimeException;
import edu.emory.mathcs.backport.java.util.Arrays;
import org.jasypt.util.text.BasicTextEncryptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.List;

@Repository("pDAO")
public class PoolManegerDAOIMP implements PoolManegerDAO {
	// 分页对象
	@Autowired
	private PageTool pageTool;
	
	@Resource(name = "cachenodeInfo_nodeID")
	private DataFieldMaxValueIncrementer incrementer_object;// 自增id

	public static SequoiadbDatasource getDs() {
		return ds;
	}

	public static void setDs(SequoiadbDatasource ds) {
		PoolManegerDAOIMP.ds = ds;
	}

	/**
	 * 日志对象
	 */
	private  final static Logger log = LoggerFactory.getLogger(PoolManegerDAOIMP.class);
	/**
	 * 连接池对象
	 */
	private static SequoiadbDatasource ds = null;

	public PageTool getPageTool() {
		return pageTool;
	}

	public void setPageTool(PageTool pageTool) {
		this.pageTool = pageTool;
	}

	public DataFieldMaxValueIncrementer getIncrementer_object() {
		return incrementer_object;
	}

	public void setIncrementer_object(
			DataFieldMaxValueIncrementer incrementer_object) {
		this.incrementer_object = incrementer_object;
	}

	/**
	 * 查询连接池信息
	 * 
	 * @param pool_id
	 * @param start
	 * @param limit
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<PoolInfoBean> getContentpoolList(int pool_id, int start,
			int limit) {
		log.info("--getContentpoolList(start)-->pool_id:" + pool_id);
		List<PoolInfoBean> beanList = null;
		// 拼装封装sql语句
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT URLS,STATE,POOLNAME,PASSWORD,USERNAME,CO_CONNECTTIMEOUT,CO_MAXAUTOCONNECTRETRYTIME,SO_SYNCCOORDINTERVAL,SO_MAXCONNECTIONNUM,SO_MAXIDENUM,SDB_CONNECTION_ID,SO_DELTAINCCOUNT,SO_ABANDONTIME,VALIDATECONNECTION,SO_RECHECKCYCLEPERIOD FROM   CONTENT_POOL_INFO");
		sql.append(" WHERE 1=1");
		if (pool_id != 0) {
			sql.append(" AND  SDB_CONNECTION_ID=").append(pool_id);
		}
		sql.append(" ORDER BY SDB_CONNECTION_ID");
		try {
			// 去sdb数据库分页查询连接池数据
			beanList = DataBaseUtil.SUNECM.queryBeanList(
					pageTool.getPageSql(sql.toString(), start, limit),
					PoolInfoBean.class);
		} catch (SQLException e) {
			log.error("连接池配置->查询连接池信息失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"PoolManegerDAOIMP===>getContentpoolList--hasPage:"
							+ e.getMessage());
		}
		BasicTextEncryptor cryptor = new BasicTextEncryptor();
		cryptor.setPassword(Constant.ENCRYPTED_ORDER);
		for (PoolInfoBean poolInfoBean : beanList) {
			poolInfoBean.setPassword(cryptor.decrypt(poolInfoBean.getPassword()));
			poolInfoBean.setUsername(cryptor.decrypt(poolInfoBean.getUsername()));
		}
		return beanList;
	}

	/**
	 * 查询连接池信息
	 * 
	 * @param pool_id
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<PoolInfoBean> getContentpoolList(int pool_id) {
		log.info("--getContentpoolList(start)-->pool_id:" + pool_id);

		List<PoolInfoBean> beanList = null;
		// 开始拼装sql语句
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT URLS,STATE,POOLNAME,PASSWORD,USERNAME,CO_CONNECTTIMEOUT,CO_MAXAUTOCONNECTRETRYTIME,SO_SYNCCOORDINTERVAL,SO_MAXCONNECTIONNUM,SO_MAXIDENUM,SDB_CONNECTION_ID,SO_DELTAINCCOUNT,SO_ABANDONTIME,VALIDATECONNECTION,SO_RECHECKCYCLEPERIOD FROM   CONTENT_POOL_INFO");
		sql.append(" WHERE 1=1");
		if (pool_id != 0) {
			sql.append(" AND  SDB_CONNECTION_ID=").append(pool_id);
		}
		sql.append(" ORDER BY SDB_CONNECTION_ID");
		try {
			// 去sdb数据库查询连接池信息
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					PoolInfoBean.class);
		} catch (SQLException e) {
			log.error("连接池配置->查询连接池信息失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"PoolManegerDAOIMP===>getContentpoolList--hasPage:"
							+ e.getMessage());
		}
		 BasicTextEncryptor cryptor = new BasicTextEncryptor();
		cryptor.setPassword(Constant.ENCRYPTED_ORDER);
		for (PoolInfoBean poolInfoBean : beanList) {
			poolInfoBean.setPassword(cryptor.decrypt(poolInfoBean.getPassword()));
			poolInfoBean.setUsername(cryptor.decrypt(poolInfoBean.getUsername()));
		}
		return beanList;
	}

	/**
	 * 查询连接池信息(激活的)
	 * 
	 * @param pool_id
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<PoolInfoBean> getContentpoolList() {
		log.info("--getContentpoolList(start)-");

		List<PoolInfoBean> beanList = null;
		StringBuffer sql = new StringBuffer();
		sql.append("SELECT URLS, STATE,POOLNAME,PASSWORD,USERNAME,CO_CONNECTTIMEOUT,CO_MAXAUTOCONNECTRETRYTIME,VALIDATECONNECTION,SO_MAXCONNECTIONNUM,SO_MAXIDENUM,SDB_CONNECTION_ID,SO_DELTAINCCOUNT,SO_ABANDONTIME,SO_SYNCCOORDINTERVAL,SO_RECHECKCYCLEPERIOD FROM   CONTENT_POOL_INFO");
		sql.append(" WHERE STATE=1");
		sql.append(" ORDER BY SDB_CONNECTION_ID");
		try {
			beanList = DataBaseUtil.SUNECM.queryBeanList(sql.toString(),
					PoolInfoBean.class);
		} catch (SQLException e) {
			log.error("连接池配置->查询连接池信息失败:" + e.toString(), e);
			throw new DBRuntimeException(
					"PoolManegerDAOIMP===>getContentpoolList--hasPage:"
							+ e.getMessage());
		}
		 BasicTextEncryptor cryptor = new BasicTextEncryptor();
		cryptor.setPassword(Constant.ENCRYPTED_ORDER);
		for (PoolInfoBean poolInfoBean : beanList) {
			poolInfoBean.setPassword(cryptor.decrypt(poolInfoBean.getPassword()));
			poolInfoBean.setUsername(cryptor.decrypt(poolInfoBean.getUsername()));
		}
		return beanList;
	}

	/**
	 * 添加连接池
	 * 
	 * @param bean
	 * @return
	 */
	@SuppressWarnings("unused")
	public int addContentPool(PoolInfoBean bean) {
		if (bean == null) {
			return 0;
		}
		int sdb_connection_id = incrementer_object.nextIntValue();
		StringBuffer sql = new StringBuffer();
		sql.append("INSERT INTO    CONTENT_POOL_INFO( URLS,STATE,PASSWORD,USERNAME,POOLNAME,CO_CONNECTTIMEOUT,CO_MAXAUTOCONNECTRETRYTIME,VALIDATECONNECTION,SO_MAXCONNECTIONNUM,SO_MAXIDENUM,SDB_CONNECTION_ID,SO_DELTAINCCOUNT,SO_ABANDONTIME,SO_SYNCCOORDINTERVAL,SO_RECHECKCYCLEPERIOD) VALUES('");
		sql.append(bean.getUrls()).append("',").append(bean.getState())
				.append(",'").append(bean.getPassword()).append("','")
				.append(bean.getUsername()).append("','")
				.append(bean.getPoolname()).append("','")
				.append(bean.getCo_connecttimeout()).append("','")
				.append(bean.getCo_maxautoconnectretrytime()).append("',")
				.append(bean.getValidateConnection()).append(",'")
				.append(bean.getSo_maxconnectionnum()).append("','")
				.append(bean.getSo_maxidenum()).append("',");
		sql.append(sdb_connection_id).append(",'")
				.append(bean.getSo_deltainccount()).append("','")
				.append(bean.getSo_abandontime()).append("','")
				.append(bean.getSo_syncCoordInterval()).append("','")
				.append(bean.getSo_recheckcycleperiod()).append("')");

		try {
			log.debug("--addContentPool-->sql:" + sql.toString());
			int result = DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
			if (result > 0) {
				return sdb_connection_id;
			}
		} catch (Exception e) {
			// 记录日志
			log.error("连接池管理->新增连接池失败:" + e.toString(), e);

			throw new DBRuntimeException("PoolManageDAOIMP===>addContentPool:"
					+ e.getMessage());
		}
		log.info("--addContentPool(over)");
		return 0;
	}

	/**
	 * 修改连接池信息
	 * 
	 * @param bean
	 * @return
	 */
	public int updateContentPool(PoolInfoBean bean) {
		if (bean == null) {
			return 0;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE   CONTENT_POOL_INFO SET URLS='")
				.append(bean.getUrls()).append("',STATE=")
				.append(bean.getState()).append(",USERNAME='")
				.append(bean.getUsername()).append("',PASSWORD='")
				.append(bean.getPassword()).append("',POOLNAME='")
				.append(bean.getPoolname()).append("',SO_MAXCONNECTIONNUM='");
		sql.append(bean.getSo_maxconnectionnum()).append("',SO_MAXIDENUM='")
				.append(bean.getSo_maxidenum()).append("',SO_DELTAINCCOUNT='")
				.append(bean.getSo_deltainccount())
				.append("',CO_CONNECTTIMEOUT='")
				.append(bean.getCo_connecttimeout())
				.append("',CO_MAXAUTOCONNECTRETRYTIME='")
				.append(bean.getCo_maxautoconnectretrytime())
				.append("',SO_SYNCCOORDINTERVAL='")
				.append(bean.getSo_syncCoordInterval())
				.append("',SO_ABANDONTIME='").append(bean.getSo_abandontime());
		sql.append("',validateConnection=")
				.append(bean.getValidateConnection())
				.append(",SO_RECHECKCYCLEPERIOD='")
				.append(bean.getSo_recheckcycleperiod());

		sql.append("' WHERE SDB_CONNECTION_ID=").append(
				bean.getSdb_connection_id());
		try {
			log.debug("--updateContentPool->sql:" + sql.toString());
			int result = DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;

			if (result > 0) {
				return bean.getSdb_connection_id();
			}
		} catch (Exception e) {
			// 记录日志
			log.error("连接池管理->更新连接池失败:" + e.toString(), e);

			throw new DBRuntimeException(
					"PoolManageDAOIMP===>updateContentPool:" + e.getMessage());
		}
		log.info("--updateContentPool(over)");

		return 0;
	}

	/**
	 * 禁用连接池
	 * 
	 * @param sdb_connection_ids
	 * @return
	 */
	public boolean stopContentPool(String sdb_connection_ids) {
		log.info("--stopContentPool(start)-->sdb_connection_ids:"
				+ sdb_connection_ids);
		if (sdb_connection_ids == null || "".equals(sdb_connection_ids)) {
			return false;
		}
		String[] ids = sdb_connection_ids.split(",");
		if (ids.length == 0) {
			return false;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE CONTENT_POOL_INFO  s SET    s.STATE = 0 WHERE s.STATE=1 ");
		sql.append("AND (s.SDB_CONNECTION_ID =")
				.append(Integer.parseInt(ids[0])).append(" ");

		for (int i = 1; i < ids.length; i++) {
			sql.append(" OR s.SDB_CONNECTION_ID =")
					.append(Integer.parseInt(ids[i])).append(" ");
		}
		sql.append(")");

		try {
			log.debug("--stopContentPool-->sql:" + sql.toString());
			int result = DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
			if (result > 0) {
				return true;
			}
		} catch (Exception e) {
			log.error("连接池管理->禁用连接池失败:" + e.toString(), e);

			throw new DBRuntimeException("PoolManageDaoImp===>stopPool:"
					+ e.getMessage());
		}
		log.info("--stopContentPool(over)");
		return false;
	}

	/**
	 * 激活连接池
	 * 
	 * @param sdb_connection_ids
	 * @return
	 */
	public boolean startContentPool(String sdb_connection_ids) {
		log.info("--startContentPool(start)-->sdb_connection_ids:"
				+ sdb_connection_ids);
		if (sdb_connection_ids == null || "".equals(sdb_connection_ids)) {
			return false;
		}

		String[] ids = sdb_connection_ids.split(",");
		if (ids.length == 0) {
			return false;
		}
		StringBuffer sql = new StringBuffer();
		sql.append("UPDATE CONTENT_POOL_INFO  s SET    s.STATE = 1 WHERE s.STATE=0 ");
		sql.append("AND (s.SDB_CONNECTION_ID =")
				.append(Integer.parseInt(ids[0])).append(" ");

		for (int i = 1; i < ids.length; i++) {
			sql.append(" OR s.SDB_CONNECTION_ID =")
					.append(Integer.parseInt(ids[i])).append(" ");
		}
		sql.append(")");
		try {
			log.debug("--startContentPool-->sql:" + sql.toString());
			int result = DataBaseUtil.SUNECM.update(sql.toString());
			sql = null;
			if (result > 0) {
				return true;
			}
		} catch (Exception e) {
			log.error("连接池管理->启用连接池失败:" + e.toString(), e);

			throw new DBRuntimeException("poolManageDaoImp===>startPool:"
					+ e.getMessage());
		}
		log.debug("--startContentPool(over)");
		return false;
	}

	/**
	 * 测试连接池
	 * 
	 * @param urls
	 * @param nwOpt
	 * @param dsOpt
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public boolean testContentPool(String urls, String password,
			String username, ConfigOptions nwOpt, DatasourceOptions dsOpt) {

		log.info("--startContentPool(start)-->sdb_connection_ids:" + urls);
		if (urls == null || "".equals(urls)) {
			return false;
		}
		// 将urls字符串转换成集合
		String[] urls1 = urls.split(",");
		List<String> urls2 = Arrays.asList(urls1);
		ds = new SequoiadbDatasource(urls2, username, password, nwOpt, dsOpt);
		try {
			if (ds.getConnection() != null) {
				ds.close();
				return true;
			}

		} catch (Exception e) {
			log.error("连接池管理->更新连接池失败:" + e.toString(), e);

			throw new DBRuntimeException("PoolManageDAOIMP===>testContentPool:"
					+ e.getMessage());

		}
		return false;

	}
}
