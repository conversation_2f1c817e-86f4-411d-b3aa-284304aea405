<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">


<!--	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource" destroy-method="close">-->
<!--		<property name="driverClass" value="org.postgresql.Driver" />-->
<!--		<property name="jdbcUrl" value="***************************************" />-->
<!--		<property name="user" value="postgres" />-->
<!--		<property name="password" value="root" />-->
<!--		<property name="maxPoolSize" value="200" />-->
<!--		<property name="initialPoolSize" value="3" />-->
<!--		<property name="maxIdleTime" value="30"/>-->
<!--		<property name="minPoolSize" value="3" />-->
<!--		<property name="idleConnectionTestPeriod" value="60" />-->
<!--	</bean>-->

	<bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
		<!-- 基本配置，访问数据库的driver、url、user、password -->
		<property name="driverClassName" value="org.postgresql.Driver" />
		<property name="url" value="********************************************" />
		<property name="username" value="sunyard" />
		<property name="password" value="Sa123456" />
		<!-- 配置初始化大小、最大、最小 -->
		<property name="initialSize" value="50" />
		<property name="maxActive" value="200" />
		<property name="minIdle" value="20" />
		<!-- 配置获取连接等待超时的时间，单位是毫秒 -->
		<property name="maxWait" value="60000" />
		<!-- 配置监控统计拦截的filters -->
		<property name="filters" value="stat" />
		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="300000" />
		<!-- 用来检测连接是否有效的sql，要求是一个查询语句。如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会其作用。 -->
		<!-- 查询语句需要根据不同的数据源进行调整设置 -->
<!--		<property name="validationQuery" value="SELECT 1 FROM DUAL" />-->
		<property name="validationQuery" value="SELECT 1 " />
		<!-- 建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。 -->
		<property name="testWhileIdle" value="true" />
		<!-- 申请连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能。 -->
		<property name="testOnBorrow" value="false" />
		<!-- 归还连接时执行validationQuery检测连接是否有效，做了这个配置会降低性能 -->
		<property name="testOnReturn" value="false" />
		<!-- 对于长时间不使用的连接强制关闭 -->
		<property name="removeAbandoned" value="true" />
		<!-- 超过30分钟开始关闭空闲连接 -->
		<property name="removeAbandonedTimeout" value="1800" />
		<!-- 将当前关闭动作记录到日志 -->
		<property name="logAbandoned" value="false" />
		<!-- 设置数据库事务是否自动提交，默认值为true -->
		<property name="defaultAutoCommit" value="true" />
		<!-- 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大。-->
		<property name="poolPreparedStatements" value="true" />
		<!-- 要启用PSCache，必须配置大于0，当大于0时，poolPreparedStatements自动触发修改为true。 -->
		<property name="maxOpenPreparedStatements" value="0" />
		<property name="maxPoolPreparedStatementPerConnectionSize" value="20" />
	</bean>

	<!-- <bean id="directoryIncrementer" class="org.springframework.jdbc.support.incrementer.DB2SequenceMaxValueIncrementer"> <property name="dataSource" ref="dataSource"></property>
		<property name="incrementerName" value="S_DMS_DIRECTORY"></property> </bean> -->

	<bean id="pageTool"
		  class="com.sunyard.util.pageTool.PostgreSQLPageTool">
	</bean>

</beans>
