package com.sunyard.util;

import java.util.HashMap;
import java.util.Map;

public class EasyMap {
	private Map m = new HashMap();

	public static Map map(Object key, Object value) {
		return create().put(key, value).map();
	}

	public static EasyMap create() {
		return new EasyMap();
	}

	public EasyMap put(Object key, Object value) {
		m.put(key, value);
		return this;
	}

	public Map map() {
		return m;
	}

}
