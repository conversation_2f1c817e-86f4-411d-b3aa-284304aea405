package com.sunyard.console.common.ext.data.record;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title: data.record.Creater</p>
 * <p>Description: 创建grid列表数据映射</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class Creater {
	public static final String name		= "name";
	public static final String mapping	= "mapping";
	
	private List<Map<String,Object>> creater = new ArrayList<Map<String,Object>>();
	
	public void addDataColumn(Map<String,Object> m){
		creater.add(m);
	}
	
	public void addDataColumn(String name , String mapping){
		Map<String,Object> m = new HashMap<String,Object>();
		m.put("name", name);
		m.put("mapping", mapping);
		creater.add(m);
	}
	
	public List<Map<String,Object>> getCreater(){
		return creater;
	}
}
