<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context-2.5.xsd
           http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
           http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-2.5.xsd">
    <!-- 表主键自增配置 -->
    <bean id="roleInfo_roleID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_ROLE_INFO"></property>
    </bean>

    <bean id="logRule_logID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_LOG_RULE"></property>
    </bean>

    <bean id="routeStrategy_routeID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_ROUTE_STRATEGY"></property>
    </bean>

    <bean id="objectResourceDir_dirID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_OBJECT_RESOURCE_DIR"></property>
    </bean>

    <bean id="metedataObjectSet_objectID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_METEDATA_OBJECT_SET"></property>
    </bean>

    <bean id="fileBusiType_busitypeID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_FILE_BUSI_TYPE"></property>
    </bean>

    <bean id="cachenodeInfo_nodeID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_CONTENT_SERVER_INFO"></property>
    </bean>

    <bean id="contentServerGroup_groupID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_CONTENT_SERVER_GROUP"></property>
    </bean>

    <bean id="attributeValueRange_valuerangeID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_ATTRIBUTE_VALUE_RANGE"></property>
    </bean>

    <bean id="archivesType_archivestypeID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_ARCHIVES_TYPE"></property>
    </bean>

    <bean id="schedule_no"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="SCHEDULE_NO"></property>
    </bean>

    <bean id="configInfoSynchroID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_Config_Info_Synchro"></property>
    </bean>

    <bean id="volumeID"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_VOLUME_INFO"></property>
    </bean>

    <bean id="unityAccessServerId"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_UNITY_ACCESS_SERVER"></property>
    </bean>
    <bean id="unityAccessServerGroupId"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_UNITY_ACCESS_SERVER_GROUP"></property>
    </bean>
    <bean id="task_no_incrementer"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_DMTASK"></property>
    </bean>
    <bean id="schedule_id_incrementer"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_SCHEDULER"></property>
    </bean>
    <bean id="group_cmodel_rel_id_incrementer"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_SGROUP_CMODEL_REL"></property>
    </bean>
    <bean id="insNo_dms_rel_id_incrementer"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_INSNO_DMS__REL"></property>
    </bean>
    <bean id="param_config_par_id_incrementer"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_PARAM_CONFIG_INFO"></property>
    </bean>
    <bean id="param_show_par_show_id_incrementer"
          class="org.springframework.jdbc.support.incrementer.OracleSequenceMaxValueIncrementer">
        <property name="dataSource" ref="dataSource"></property>
        <property name="incrementerName" value="S_PARAM_SHOW_INFO"></property>
    </bean>
</beans>