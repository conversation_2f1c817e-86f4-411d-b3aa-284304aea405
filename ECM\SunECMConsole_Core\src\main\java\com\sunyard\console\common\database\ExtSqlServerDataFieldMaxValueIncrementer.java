package com.sunyard.console.common.database;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;

import java.util.UUID;

/**
 * Sqlserver模拟SEQUENCE
 * 
 * <AUTHOR>
 *
 */
public class ExtSqlServerDataFieldMaxValueIncrementer implements DataFieldMaxValueIncrementer {
	private JdbcTemplate jdbcTemplate;
	private static String insertSql = "insert into SEQUENCE_CREATTION(UUID) values(?)";
	private static String querySql = "select id from SEQUENCE_CREATTION where UUID=?";
	private static String deleteSql = "delete from SEQUENCE_CREATTION where id=?";

	public void setJdbcTemplate(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	private int nextValue() {
		String uuid = UUID.randomUUID().toString();
		jdbcTemplate.update(insertSql, uuid);
		int i = jdbcTemplate.queryForList(querySql, uuid).size();
		jdbcTemplate.update(deleteSql, i);
		return i;
	}

	public int nextIntValue() throws DataAccessException {
		return nextValue();
	}

	public long nextLongValue() throws DataAccessException {
		return nextValue();
	}

	public String nextStringValue() throws DataAccessException {
		return nextValue() + "";
	}

}
