package com.sunyard.console.common.database;

import com.sunyard.console.contentmodelmanage.bean.IndexInfoBean;
import net.sf.jsqlparser.JSQLParserException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Title: sqlServer数据库查询分页
 * </p>
 * <p>
 * Description: sqlServer数据库查询分页
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ConditionalOnProperty(prefix = "pageTool",name = "Type", havingValue = "sqlServerPageTool")
@Service("pageTool")
public class SqlServerPageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
		start = start - 1;
		if (start < 0) {
			start = 0;
		}
		int end = start + limit;
		String newSql = "";
		try {
			newSql = new SqlServerParse().removeOrderBy(sql);
		} catch (JSQLParserException e) {
			throw new RuntimeException(e);
		}
		String pageSql = "select * from (select  ROW_NUMBER()" + " OVER(order by (select 0)) AS row_num,* FROM " + "("
				+ newSql + ")x ) " + "t where " + "t.row_num >" + start + " and t.row_num<=" + end;
		return pageSql;
	}

	public String getTableSpaceName(String sql, String tableSpaceName) {
		return sql;
	}

	public String getOnlyOneSql(String sql, String tablename) {
		return "SELECT TOP 1 *  FROM  " + tablename;
	}

	public String delIndexSql(IndexInfoBean bean) {
		return "DROP INDEX " + bean.getTable_name() + "_" + bean.getIndex_name();
	}
}
