package com.sunyard.console.safemanage.bean;



/**
 * <p>Title: 角色信息bean</p>
 * <p>Description: 存放角色信息</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR> 
 * @version 1.0
 */
public class RoleInfoBean extends NodeBean{

	private String role_id;		//角色编号
	private String role_name;	//角色名称
	private String role_state;	//角色状态
	private String role_des;	//备注
	
	public String getRole_state() {
		return role_state;
	}
	public void setRole_state(String role_state) {
		this.role_state = role_state;
	}
	public String getRole_des() {
		return role_des;
	}
	public void setRole_des(String role_des) {
		this.role_des = role_des;
	}
	
	public String getRole_id() {
		return role_id;
	}
	public void setRole_id(String role_id) {
		this.role_id = role_id;
	}
	public String getRole_name() {
		return role_name;
	}
	public void setRole_name(String role_name) {
		this.role_name = role_name;
	}
	@Override
	public String getNodeID() {
		return role_id;
	}
	@Override
	public String getNodeName() {
		return role_name;
	}
	@Override
	public String getParentNodeID() {
		return "0";
	}
	@Override
	public String getHasCheckbox() {
		return "false";
	}
	public String toString(){
		StringBuffer sBuffer = new StringBuffer();
		sBuffer.append("role_id:").append(role_id);
		sBuffer.append(";role_name:").append(role_name);
		sBuffer.append(";role_state:").append(role_state);
		sBuffer.append(";role_des:").append(role_des);
		return sBuffer.toString();
	}
}

