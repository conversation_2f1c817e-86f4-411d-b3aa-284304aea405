<template>
  <div class="app-container">
    <div>
      <label-wrap>对象名称:</label-wrap>
      <el-select v-model="listQuery.model_code" placeholder="请选择">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div align="center">
      <el-button v-if="this.hasPerm('historySearch')" type="primary" icon="el-icon-search"
      size="mini" plain round @click="handleFilter">查询</el-button>
      <el-button type="warning" icon="el-icon-delete-solid" size="mini" plain round @click="reset">重置</el-button>
    </div>

    <div style="margin-top: 20px">
      <el-button
        v-if="this.hasPerm('addHistoryTable')"
        type="primary"
        icon="el-icon-plus" size="mini"
        @click="handleCreate"
      >
        新增
      </el-button>
    </div>
    <el-dialog v-el-drag-dialog :close-on-click-modal="false" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :rules="rules"
        :model="temp"
        label-position="left"
        label-width="120px"
        style="width: 400px; margin-left: 50px"
      >
        <el-form-item label="对象名称" prop="model_code">
          <el-select v-model="temp.model_code" placeholder="请选择" @change="findEndTime">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
                :value="item.value"
              >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="begin_time">
          <el-input
            v-model="temp.begin_time"
            placeholder="开始时间"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-input
            v-model="temp.end_time"
            placeholder="结束时间"
            style="width: 200px"
            :disabled="endTimeStatus ? true : false"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="dialogFormVisible = false"> 取消 </el-button>
        <el-button
          type="primary"
          size="mini"
          @click="createData"
        >
          提交
        </el-button>
      </div>
    </el-dialog>


    <el-table
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="对象名称" min-width="25%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.model_code }}</span>
        </template>
      </el-table-column>

      <el-table-column label="开始时间" min-width="25%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.begin_time }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" min-width="25%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.end_time}}</span>
        </template>
      </el-table-column>

      <el-table-column label="表名" min-width="25%" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.table_name }}</span>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.limit"
      @pagination="getList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {addHistory, getHistoryList,getObjectName,getEndTime} from "@/api/historyTable";
import elDragDialog from "@/directive/el-drag-dialog";

  export default {
    components: { Pagination},
    directives: { elDragDialog },
    data() {
      return {
        tableKey: 0,
        list: null,
        options: null,
        lots:null,
        total: 1,
        listLoading: true,
        listQuery: {
          start:0,
          page: 1,
          limit: 20,
          model_code: '',
          begin_time: '',
          end_time: '',
        },
        temp: {
          model_code: '',
          begin_time: '',
          end_time: '',
        },
        dialogFormVisible: false,
        textMap: {
          create: '新增历史表'
        },
        dialogPvVisible: false,
        rules: {
          model_code: [{ required: true, message: '对象名称必输', trigger: 'blur' }],
          begin_time: [{ required: true, message: '开始时间必输,日期早于结束时间', trigger: 'change'},
                    {required: true,len : 8, message: '长度为8位', trigger: 'blur'}],
        },
        attribute_code_status: false,
        endTimeStatus: false
      }
    },
    created() {
      this.getList(),
        this.pickerChange()
    },
    methods: {
      getList() {
        this.listLoading = true
        getHistoryList(this.listQuery).then(response => {
          this.list = response.root
          this.total = Number(response.totalProperty)
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
      resetTemp() {
        this.temp = {
          model_code: '',
          begin_time: '',
          end_time: '',
        }
        this.endTimeStatus = false;
      },
      handleCreate() {
        this.attribute_code_status = false
        this.resetTemp()
        this.dialogStatus = 'create'
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      pickerChange(){
        getObjectName().then(response => {
          this.options = response.root
          for (var i = 0; i < this.options.length; i++) {
            this.options[i].label = this.options[i].id;
            this.options[i].value = this.options[i].id;
          }
          setTimeout(() => {
            this.listLoading = false
          }, 1 * 100)
        })
      },
      createData() {
        this.$refs['dataForm'].validate((valid) => {
          if(valid){
            if(this.temp.begin_time>=this.temp.end_time){
              this.$notify({
                title: 'fail',
                message: '开始时间必须小于结束时间',
                type: 'fail',
                duration: 2000
              })
              return;
            }
            addHistory(this.temp).then(() => {
              this.getList();
              this.dialogFormVisible = false;
              this.$notify({
                title: 'Success',
                message: 'Created Successfully',
                type: 'success',
                duration: 2000
              })
            })
            }
          })
      },
      handleFilter() {
        this.listQuery.page = 1
        this.getList()
      },
      reset() {
        this.listQuery.model_code=""
      },
      findEndTime(){
        getEndTime(this.temp).then((response) => {
          if(response.tableEndTime!=undefined){
            this.temp.end_time = response.tableEndTime;
            this.endTimeStatus = true;
          }else{
            this.temp.end_time = "";
          }
            })
      }
    }
  }
</script>
