package com.sunyard.console.common.ext.grid;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>Title: grid.ColumnModel</p>
 * <p>Description: 创建grid动态列表模板</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ColumnModel {
	/**
	 * 头：列名
	 */
	public static final String HEADER = "header";
	/**
	 * 列宽
	 */
	public static final String WIDTH = "width";
	/**
	 * 数据索引
	 */
	public static final String DATAINDEX = "dataIndex";
	/**
	 * 是否排序
	 */
	public static final String SORTABLE = "sortable";
	
	private Map<String,Object> column = new HashMap<String,Object>();
	
	public void setProperties(String key , String value){
		column.put(key, value);
	}
	public void setProperties(String key , int value){
		column.put(key, value);
	}
	public void setProperties(String key , boolean value){
		column.put(key, value);
	}
	public void setProperties_obj(String key , String value){
		column.put(key, "#"+value+"#");
	}
	
	public Object getProperties(String key){
		return column.get(key);
	}
	
	public void delProperties(String key){
		column.remove(key);
	}
	
	public Map<String,Object> getColumnModel(){
		return column;
	}
}
