<template>
  <div>
    <el-row :gutter="8">
		<el-col :xs="24" :sm="24" :lg="15">
			<div :id="canvas2"  :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
		<el-col :xs="24" :sm="24" :lg="8">
			<div :id="canvas1" :style="{height:height,width:width1,padding: padding}">
			</div>
		</el-col>
	 </el-row>
  </div>
</template>


<style>

</style>

<script>
import {getTaskCountNum} from '@/api/monitorManage'
import * as echarts from 'echarts'

export default {
  name: "scheduler-day-rel",
  props: {
    listQuery: {
      require: true,
      type: Object,
    },
	serverIds:{
	  type:Array,
	  required:true
    },
	serverNames:{
      type:Array,
      required:true
    },
    canvas1: {
      type: String,
      default: 'chart1'
    },
	canvas2: {
      type: String,
      default: 'chart2'
    },
    width: {
      type: String,
      default: '700px'
    },
	width1: {
      type: String,
      default: '400px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
    //   default: '16px 16px'
    }
  },
  data() {
    return{
		min : [], 
		avg : [], 
		max : [],
		data : [],
		maxValue : 0,
		taskEvent : [ '迁移', '迁移清理', '离线', '离线清理' ],
        minTime : [], 
        avgTime : [], 
        maxTime : [], 
		option1:null,
		option2:null,
		chart1 : null,
        chart2 : null,
		dayQuery: {
			server_id: "",
			model_code: "",
			date: ""
      	}
    }
  },
  
    mounted() {
    //   this.$nextTick(() => {
    //     this.showCharts()
    //   })
    },

  methods: {
    showCharts(){
      this.chart1 = echarts.init(document.getElementById(this.canvas1), 'dark');
      this.chart2 = echarts.init(document.getElementById(this.canvas2), 'dark');
        this.option1 = {
			title : [ {
				text : '当天定时任务数据统计',
				left : 'center',
				top : '5%',
				textStyle : {
					fontSize : 14,
				}
			} ],
			tooltip : {},
			grid : { //控制图的大小
				top : '80%',
				left : '10%',
				right : '0%',
				bottom : '4%',
				containLabel : true
			},
			visualMap : {
				type : 'continuous',
				min : 0,
				max : 10,
				calculable : true,
				right : 10,
				bottom : '5%',
				textStyle : {
					color : '#ffffff'
				},
				//orient : 'horizontal',
				inRange : {
					color : [ '#3893e5', '#48c150', '#ef6670' ]
				}
			},
			series : [ {
				type : 'sunburst',
				data : this.data,
				radius : [ 0, '80%' ],
				center : [ '50%', '52%' ],
				label : {
					color : '#fff',
					rotate : 'outer'
				}
			} ]
		};
		this.option2 = {
			timeline : {
				data : this.serverNames,
				axisType : 'category',
				right : '25%',
				bottom : '0%',
				width : '50%',
				symbolSize : [ 2, 3 ],
				height : '1%',
				label : {
					normal : {
						textStyle : {
							color : '#ddd'
						}
					},
					emphasis : {
						textStyle : {
							color : '#fff'
						}
					}
				},
				controlStyle : {
					show : false,
				},
				lineStyle : {
					show : false,
				},
				checkpointStyle : {
					symbolSize : 0,
					borderWidth : 0
				},
				symbolSize : 0
			},

			baseOption : {},
			options : []
		};

      this.chart1.setOption(this.option1);
	  this.chart2.setOption(this.option2);
    },

    showData(){
		this.showCharts();
		this.dayQuery.server_id = this.listQuery.server_id;
		this.dayQuery.model_code = this.listQuery.model_code;
		this.dayQuery.date = this.listQuery.date.replaceAll("-","");
		getTaskCountNum(this.dayQuery).then(response => {	
			this.chart1.hideLoading();
			this.chart2.hideLoading();
			let batchsum = response.batchsum;
			let migrateclearsum = response.migrateclearsum;
			let offlineclearsum = response.offlineclearsum;
			let MfailTask = response.MfailTask;
			let MCfailTask = response.MCfailTask;
			let OfailTask = response.OfailTask;
			let OCfailTask = response.OCfailTask;
			let MsuccessTask = response.MsuccessTask;
			let MCsuccessTask = response.MCsuccessTask;
			let OsuccessTask = response.OsuccessTask;
			let OCsuccessTask = response.OCsuccessTask;
			for (let  i = 0; i < this.serverIds.length; i++) {
				let temp = Number(MfailTask[i])
						+ Number(MsuccessTask[i])
						+ Number(OfailTask[i])
						+ Number(OsuccessTask[i]);
				if (this.maxValue < temp) {
					this.maxValue = temp;
				}
				this.data[i] = {
					name : this.serverNames[i],
					value : Number(MfailTask[i])
							+ Number(MsuccessTask[i])
							+ Number(OfailTask[i])
							+ Number(OsuccessTask[i]),
					children : [
							{
								name : '数据迁移',
								value : Number(MfailTask[i])
										+ Number(MsuccessTask[i]),
								children : [
										{
											name : '迁移成功',
											value : MsuccessTask[i],
											children : [ {
												name : '迁移清理',
												value : Number(MCfailTask[i])
														+ Number(MCsuccessTask[i]),
												children : [
														{
															name : '迁移清理成功',
															value : MCsuccessTask[i]
														},
														{
															name : '迁移清理失败',
															value : MCfailTask[i]
														} ]
											} ]
										},
										{
											name : '迁移失败',
											value : MfailTask[i]
										} ]
							},
							{
								name : '数据离线',
								value : Number(OfailTask[i])
										+ Number(OsuccessTask[i]),
								children : [
										{
											name : '离线成功',
											value : OsuccessTask[i],
											children : [ {
												name : '离线清理',
												value : Number(OCsuccessTask[i])
														+ Number(OCfailTask[i]),
												children : [
														{
															name : '离线清理成功',
															value : OCsuccessTask[i]
														},
														{
															name : '离线清理失败',
															value : OCfailTask[i]
														} ]
											} ]
										},
										{
											name : '离线失败',
											value : OfailTask[i]
										} ]
							} ]
				};
			}
			this.chart1.setOption({
				visualMap : {
					max : this.maxValue
				},
				series : [ {
					data : this.data
				} ]
			});

			for (let n = 0; n < this.serverIds.length; n++) {
				this.option2.options
						.push({
							title : [
									{},
									{
										text : '定时任务耗时统计-'
												+ this.serverNames[n],
										top : '38%',
										left : 'right',
										// subtext : '按月统计',
										// sublink : 'schedulerTaskStatistics2.html?groups='
										// 		+ serversIdString
										// 		+ '&modelName='
										// 		+ modelName,
										
										subtextStyle : {},
										textStyle : {
											fontSize : 14,
											color : '#52FFFF'
										}
									} ],
							legend : {
								orient : 'vertical',
								top : '10%',
								right : '5%',
								height : '25%',
								itemGap : 8,
								textStyle : {
									fontSize : 10,
								},

							},
							tooltip : {
								trigger : 'axis',
								axisPointer : {
									type : 'shadow',
								}
							},
							grid : {
								top : '50%',
								left : '5%',
								right : '0%',
								bottom : '10%',
								height : '45%',
								containLabel : true
							},
							xAxis : {
								type : 'category',
								axisLine : {
									show : false
								},
								axisTick : {
									show : false
								},
								data : this.taskEvent,
								axisLabel : {
									show : true,
									interval : 0,
									textStyle : {
										fontSize : 12
									}
								}
							},
							yAxis : {
								splitNumber : 5, // 控制Y轴数值显示数量
								axisLine : {
									show : false
								// Y轴线不显示
								},
								axisTick : {
									show : false
								// 是否显示坐标轴刻度
								},
								splitLine : {
									show : true,
									lineStyle : {
										width : 1
									// 分割线宽度
									}
								},
								axisLabel : {
									show : true,
									textStyle : {
										fontSize : 12
									}
								}
							},
							series : [
									{
										name : '清理数据',
										center : [
												'center',
												'center' ],
										type : 'pie',
										radius : [
												'16%',
												'26%' ],
										center : [
												'40%',
												'23%' ],
										hoverAnimation : true,
										itemStyle : {
											normal : {
												label : {
													show : true,
													distance : 0.7,
													textStyle : {
														color : '#d9efff',
														fontSize : "10"
													},
													formatter : '{b}：{b|{d}%} \n {a|{c}} 笔',
													rich : {
														a : {
															color : "yellow",
														},
														b : {
															color : 'pink',
														},
													}
												},
											},
										},

										data : [
												{
													value : migrateclearsum,
													name : '迁移清理',
													itemStyle : {},
												},
												{
													value : offlineclearsum,
													name : '离线清理',
													itemStyle : {
														color : '#888f9b',

													}
												},
												{
													value : Number(batchsum)
															- (Number(migrateclearsum) + Number(offlineclearsum)),
													name : '暂存批次',
													itemStyle : {
														color : new echarts.graphic.LinearGradient(0,1,0,0,
																[{
																	offset : 1,
																	color : "#3893e5" // 0% 处的颜色
																},
																{
																	offset : 0,
																	color : "#00ffff" // 100% 处的颜色
																} ],
														false),
													}
												} ]
									},
									{
										name : '总批次数',
										center : [
												'center',
												'center' ],
										type : 'pie',
										radius : [
												'15%',
												'13%' ],
										center : [
												'40%',
												'23%' ],
										hoverAnimation : false,
										label : {
											normal : {
												show : true,
												position : 'center',
												formatter : function(
														argument) {
													let html = '总批次\r\n\r\n'
															+ batchsum
															+ ' 笔';
													return html;
												},
												textStyle : {
													fontSize : 10,
												},

											}
										},
										data : [
												{
													value : batchsum,
													name : '总批次数',
													itemStyle : {
														color : '#954ce9',
													}
												}, ]
									},
									{
										itemStyle : {
											normal : {
												color : new echarts.graphic.LinearGradient(0,0,0,1,
														[{
															offset : 0,
															color : 'rgba(32,223,94,1)' // 0% 处的颜色
														},
														{
															offset : 1,
															color : 'rgba(101,252,155,1)' // 100% 处的颜色
														} ],
												false),
												barBorderRadius : [0,0,0,0],
												shadowColor : 'rgba(0,160,221,1)',
												shadowBlur : 0,
											}
										},
										name : '最小',
										type : 'bar',
										barWidth : '10%',
										data : response.minTime[n]
									},
									{
										itemStyle : {
											normal : {
												color : new echarts.graphic.LinearGradient(0,0,0,1,
														[{
															offset : 0,
															color : 'rgba(238,93,2,1)' // 0% 处的颜色
														},
														{
															offset : 1,
															color : 'rgba(237,206,53,1)' // 100% 处的颜色
														} ],
												false),

												barBorderRadius : [0,0,0,0],
												shadowColor : 'rgba(0,160,221,1)',
												shadowBlur : 0,
											}
										},
										name : '平均',
										type : 'bar',
										barWidth : '10%',
										data : response.avgTime[n]
									},
									{
										itemStyle : {
											normal : {
												color : new echarts.graphic.LinearGradient(0,0,0,1,
														[{
															offset : 0,
															color : 'rgba(0,244,255,1)' // 0% 处的颜色
														},
														{
															offset : 1,
															color : 'rgba(0,77,167,1)' // 100% 处的颜色
														} ],
												false),
												barBorderRadius : [0,0,0,0],
												shadowColor : 'rgba(0,160,221,1)',
												shadowBlur : 0,
											}
										},
										name : '最大',
										type : 'bar',
										barWidth : '10%',
										data : response.maxTime[n]
									} ]
						})
			}
			this.chart2.setOption(this.option2);	
			})
	}
  }
};
</script>
<style scoped>

</style>