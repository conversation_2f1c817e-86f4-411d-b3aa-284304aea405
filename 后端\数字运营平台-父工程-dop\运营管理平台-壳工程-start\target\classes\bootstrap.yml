app:
  hostname: localhost


auth:
  # 是否为壳工程
  shellProject: true

# tomcat相关配置
server:
  port: 9300

mybatis:
  mapper-locations: classpath*:com/sunyard/aos/*.xml,classpath*:com/sunyard/cop/*.xml,classpath*:com/sunyard/aos/common/mapper/*.xml,classpath*:com/sunyard/aos/*/mapper/*.xml,classpath*:com/sunyard/aos/sflow/mapper/*.xml,classpath*:com/sunyard/aos/flow/mapper/*.xml,classpath*:com/sunyard/cop/IF/mybatis/mapping/*.xml

logging:
  config: classpath:aos-logback-spring.xml
logback:
  # 日志文件路径
  file: ./logs/${spring.application.name}
  # 日志切分大小
  filesize: 100MB
  # 日志保留天数
  filemaxday: 7
  # 日志打印级别
  level: INFO
  # 日志编码集
  charset: utf8
  # es服务器地址
  esserver: null


spring:
  application:
    name: aos-start
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        # 是否启用nacos注册中心
        enabled: false
        # 命名控件 如 sunaos-dev
        namespace: b0c4958d-e5fe-47b3-8862-f70f5b617b40
        # 注册中心地址
        server-addr: 127.0.0.1:8848
        # 分组
        group: DEV_GROUP
      config:
        # 是否启用nacos配置中心
        enabled: false
        # nacos注册中心地址
        server-addr: 127.0.0.1:8848
        # 注册中心命名空间
        namespace: b0c4958d-e5fe-47b3-8862-f70f5b617b40
        refresh-enabled: true
        extension-configs[0]:
          data-id: datasource.yml
          group: DEFAULT_GROUP
          refresh: true
        extension-configs[1]:
          data-id: public-config.properties
          group: DEFAULT_GROUP
          refresh: true
        extension-configs[2]:
          data-id: public-application.yml
          group: DEFAULT_GROUP
          refresh: true
        extension-configs[3]:
          data-id: ${spring.application.name}.yml
          group: DEFAULT_GROUP
          refresh: true
    # spring config 配置中心配置
    config:
      enabled: true
      # 注册中心配置文件名
      name: datasource,public-application,public-config
      discovery:
        # 注册中心服务id
        service-id: sunaos-config
        # 是否启用
        enabled: true
      # 重试机制相关配置
      retry:
        # 配置重试次数，默认为6
        max-attempts: 3
        # 间隔乘数，默认1.1
        multiplier: 1.1
        # 初始重试间隔时间，默认1000ms
        initial-interval: 10000
        # 最大间隔时间，默认2000ms
        max-interval: 20000

eureka:
  client:
    enabled: true
    service-url:
      defaultZone: http://127.0.0.1:9100/eureka
    #客户端每隔30秒从Eureka服务上更新一次服务信息
    registry-fetch-interval-seconds: 30
    #需要将我的服务注册到eureka上
    register-with-eureka: true
    #需要检索服务
    fetch-registry: true
  #心跳检测检测与续约时间
  instance:
    #告诉服务端，如果我10s之内没有给你发心跳，就代表我故障了，将我剔除掉，默认90s
    #Eureka服务端在收到最后一次心跳之后等待的时间上限，单位为秒，超过则剔除（客户端告诉服务端按照此规则等待自己）
    lease-expiration-duration-in-seconds: 90
    #每隔2s向服务端发送一次心跳，证明自已依然活着，默认30s
    #Eureka客户端向服务端发送心跳的时间间隔，单位为秒（客户端告诉服务端自己会按照该规则）
    lease-renewal-interval-in-seconds: 30
    # 启用ip配置 这样在注册中心列表中看见的是以ip+端口呈现的
    prefer-ip-address: true
    # 实例名称  最后呈现地址：ip:2002
    instance-id: ${app.hostname}:${server.port}
    #    health-check-url-path: /actuator/health
    ip-address: ${app.hostname}