package com.sunyard.ecm.server.bean;


import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

/****
 * 迁移失败详细信息对象
 * 
 * <AUTHOR>
 *
 */
@XStreamAlias("MigrateFailDetailBean")
public class MigrateFailDetailBean {
	@XStreamAsAttribute
	private String content_id;
	@XStreamAsAttribute
	private String index_table_name;
	@XStreamAsAttribute
	private String file_table_name;
	private String business_start_date;
	@XStreamAsAttribute
	private String index_model_code;
	@XStreamAsAttribute
	private String file_model_code;
	@XStreamAsAttribute
	private String error_case;
	@XStreamAsAttribute
	private String version;
	@XStreamAsAttribute
	private String fail_time;
	@XStreamAsAttribute
	private String group_id;

	public String getIndex_table_name() {
		return index_table_name;
	}

	public void setIndex_table_name(String index_table_name) {
		this.index_table_name = index_table_name;
	}

	public String getFile_table_name() {
		return file_table_name;
	}

	public void setFile_table_name(String file_table_name) {
		this.file_table_name = file_table_name;
	}

	public String getBusiness_start_date() {
		return business_start_date;
	}

	public void setBusiness_start_date(String business_start_date) {
		this.business_start_date = business_start_date;
	}

	public String getIndex_model_code() {
		return index_model_code;
	}

	public void setIndex_model_code(String index_model_code) {
		this.index_model_code = index_model_code;
	}

	public String getFile_model_code() {
		return file_model_code;
	}

	public void setFile_model_code(String file_model_code) {
		this.file_model_code = file_model_code;
	}

	public String getFail_time() {
		return fail_time;
	}

	public void setFail_time(String failTime) {
		fail_time = failTime;
	}

	public String getContent_id() {
		return content_id;
	}

	public void setContent_id(String contentId) {
		content_id = contentId;
	}

	public String getGroup_id() {
		return group_id;
	}

	public void setGroup_id(String groupId) {
		group_id = groupId;
	}

	public String getError_case() {
		return error_case;
	}

	public void setError_case(String errorCase) {
		error_case = errorCase;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getLast_time() {
		return fail_time;
	}

	public void setLast_time(String lastTime) {
		fail_time = lastTime;
	}

}
