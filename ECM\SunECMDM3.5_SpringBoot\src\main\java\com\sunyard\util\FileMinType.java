package com.sunyard.util;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.common.Configuration;

/**
 * 文件类型处理
 * 
 * <AUTHOR>
 * 
 */
public class FileMinType {
	private static final Logger log = LoggerFactory
			.getLogger(FileMinType.class);
	/**
	 * 得到文件类型
	 * 
	 * @param extName
	 * @return
	 */
	public static String getMiniType(String extName) {
		return  Configuration.get(extName, "application/octet-stream");
	}

	/**
	 * 获取文件格式
	 * 
	 * @param file
	 * @param encoding
	 *            UTF-8
	 * @return
	 */
	public static String getFileFormat(File file, String encoding) {
		String fileName = file.getName();
		String[] token = fileName.split("\\.");
		String format = "";
		if (token.length > 1) {
			format = token[1];
		} else {
			format = "";
		}
		return getMiniType(format) + ";charset=" + encoding;
	}

	/**
	 * 获取文件格式
	 * 
	 * @param filePath
	 *            文件路径
	 * @param encoding
	 *            编码格式
	 * @return
	 */
	public static String getFileFormat(String filePath, String encoding) {
		File file = new File(filePath);
		return getFileFormat(file, encoding);
	}

	/**
	 * 根据连接获取文件格式
	 * 
	 * @param c
	 * @return
	 */
	public static String getFileFormatByCooection(URLConnection c) {
		// 网页编码
		String strencoding = null;
		/**
		 * 首先根据header信息，判断页面编码
		 */
		// map存放的是header信息(url页面的头信息)
		Map<String, List<String>> map = c.getHeaderFields();
		Set<String> keys = map.keySet();
		Iterator<String> iterator = keys.iterator();
		// 遍历,查找字符编码
		String key = null;
		String tmp = null;
		while (iterator.hasNext()) {
			key = iterator.next();
			tmp = map.get(key).toString().toLowerCase();
			log.info(key + "=======" + tmp);
			// 获取content-type charset
			if (key != null && key.equals("Content-Type")) {
				strencoding = tmp.substring(1, tmp.lastIndexOf("]"));
				return strencoding;
			}
		}
		return strencoding;
	}

	/**
	 * 根据URL获取流的格式
	 * 
	 * @param url
	 * @return
	 */
	public static String getFileFormatByUrl(URL url) {
		// 网页编码
		String strencoding = null;
		try {
			URLConnection c = url.openConnection();
			// 用URLConnection的connect()方法建立连接
			c.connect();
			strencoding = getFileFormatByCooection(c);
			return strencoding;
		} catch (IOException e) {
			log.error("出错",e);
		}
		return "";
	}

	/**
	 * 根据URL字符串获取文件格式
	 * 
	 * @param urlStr
	 * @return
	 */
	public static String getFileFormatByUrl(String urlStr) {
		try {
			// 定义URL对象
			URL url = new URL(urlStr);
			return getFileFormatByUrl(url);
		} catch (IOException e) {
			log.error("出错",e);
		}
		return "";
	}

//	public static void main(String[] args) {
//		System.out
//				.println(FileMinType
//						.getFileFormatByUrl("http://172.16.3.93:8080/SunDM/servlet/getImage?GUID=B416E3AA-D5CA-C786-9981-B71653336A54&USERNAME=admin"));
//	}
}