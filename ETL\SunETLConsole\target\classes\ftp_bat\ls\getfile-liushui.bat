@echo off
#set dt=2023-07-04
set dt=%date:~0,10%
rem date format is "YYYY-MM-DD"
rem set /P dt="Input Date: "
set dy=%dt:~0,4%
set dm=%dt:~5,2%
set dd=%dt:~8,2%


if %dm%%dd%==0101 goto L01
if %dm%%dd%==0201 goto L02
if %dm%%dd%==0301 goto L07
if %dm%%dd%==0401 goto L02
if %dm%%dd%==0501 goto L04
if %dm%%dd%==0601 goto L02
if %dm%%dd%==0701 goto L04
if %dm%%dd%==0801 goto L02
if %dm%%dd%==0901 goto L02
if %dm%%dd%==1001 goto L05
if %dm%%dd%==1101 goto L03
if %dm%%dd%==1201 goto L06
if %dd%==02 goto L10
if %dd%==03 goto L10
if %dd%==04 goto L10
if %dd%==05 goto L10
if %dd%==06 goto L10
if %dd%==07 goto L10
if %dd%==08 goto L10
if %dd%==09 goto L10
if %dd%==10 goto L11
set /A dd=dd-1
set dt=%dy%%dm%%dd%
goto END
:L10
set /A dd=%dd:~1,1%-1
set dt=%dy%%dm%0%dd%
goto END
:L11
set dt=%dy%%dm%09
goto END
:L02
set /A dm=%dm:~1,1%-1
set dt=%dy%0%dm%31
goto END
:L04
set /A dm=dm-1
set dt=%dy%0%dm%30
goto END
:L05
set dt=%dy%0930
goto END
:L03
set dt=%dy%1031
goto END
:L06
set dt=%dy%1130
goto END
:L01
set /A dy=dy-1
set dt=%dy%1231
goto END
:L07
set /A "dd=dy%%4"
if not %dd%==0 goto L08
set /A "dd=dy%%100"
if not %dd%==0 goto L09
set /A "dd=dy%%400"
if %dd%==0 goto L09
:L08
set dt=%dy%0228
goto END
:L09
set dt=%dy%0229
goto END
:END
echo %dt%
set dtd=%dt%
rem 判断业务日期文件夹是否存在
set windir=D:\data\%dt%
:: ADD 2019-03-01 关闭非输出显示
@echo off
:: END 2019-03-01 
if exist %windir% (
	 
		if exist %windir%\COSD0090_%dt%.txt ( 
			echo COSD0090 yes 
		) else (
				del /Q .\files-liushui.ftp
	call makefile-liushui > files-liushui.ftp
	ftp.exe -n -s:"files-liushui.ftp"
                        echo biaobao is not exist
			if exist %windir%\COSD0090_%dtd%.txt ( 
				echo COSD0090 yes 
			) else ( 
				if exist %windir%\CORD0050_%dtd%.txt ( 
					cd.>%windir%\COSD0090_%dt%.txt
				)
			)
		)
		
	
	
	
) else (
rem 第一次下载文件 
	md %windir%
	del /Q .\files-liushui.ftp
	call makefile-liushui > files-liushui.ftp
	ftp.exe -n -s:"files-liushui.ftp"
)

echo %windir%
if exist %windir%\ALLEND.TXT (
echo ALLEND
) 
else(
set flag=0
for %%a in ( 
	%windir%\0100006D.k01.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\0186310D.k14.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v91.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v92.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v93.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v94.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vg1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vg2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vg3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vg4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vh1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vh2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vh3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vh4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vt1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vt2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
  %windir%\0186310D.vt3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.vt4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xa1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xa2.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xa3.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.xa4.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0100006D.k07.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%0,
	%windir%\0186310D.dm1.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.w02.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.w03.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.w04.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v81.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v82.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v83.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.v84.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\8186310D.1SR.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\8186310D.1RR.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\0186310D.dwd.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
  %windir%\US86310D.RLM.gz.%dt:~0,4%%dt:~4,2%%dt:~6,2%1,
	%windir%\T20501.00000230.bd4.%dt:~0,4%%dt:~4,2%%dt:~6,2%1.gz,
	%windir%\T20501.00000230.bd6.%dt:~0,4%%dt:~4,2%%dt:~6,2%1.gz,
	%windir%\T20501.00000230.bb1.%dt:~0,4%%dt:~4,2%%dt:~6,2%1.gz,
	%windir%\CORD0050_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\COSD0090_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
  %windir%\CRDD1020_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\DEPD1010_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\DEPD1020_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\DEPD2050_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\DEPD9950_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\ACAD0020_%dt:~0,4%%dt:~4,2%%dt:~6,2%.txt,
	%windir%\trace%dt:~0,4%%dt:~4,2%%dt:~6,2%.AB6
) do if not exist %%a ( 
echo %%a is not exist 
set flag=1)
echo %flag%
if %flag% == 1 ( 
	del /Q .\files-liushui.ftp
	call makefile-liushui > files-liushui.ftp
	ftp.exe -n -s:"files-liushui.ftp"
 ) 
 
if %flag% == 0 ( cd.>%windir%\ALLEND.TXT )
)
