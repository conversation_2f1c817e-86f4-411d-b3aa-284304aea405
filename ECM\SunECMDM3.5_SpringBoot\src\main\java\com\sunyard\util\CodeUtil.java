package com.sunyard.util;

import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.Arrays;

import com.sunyard.ecm.server.bean.MigrateBatchBean;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.bean.ClientBatchBean;
import com.sunyard.client.bean.ClientHeightQuery;

/**
 * 对传入的字符串信息进行加密解密操作
 * 
 * <AUTHOR>
 *
 */
public class CodeUtil {
	private final static  Logger log = LoggerFactory.getLogger(CodeUtil.class);
	/**
	 * 对传入的字串进行加密编码
	 * 
	 * @param content 需要加密的字串
	 * @return 经过加密处理的字串
	 * @备注 2013年9月18日增加 字串加密
	 */
	public static String encode(String content) {
		if(content==null){
			log.debug("encode msg,msg is null");
			return null;
		}
		// 对字符进行偏移2位操作，对原内容进行简单的修改
		char[] array = content.toCharArray();
		StringBuffer sbchar= new StringBuffer();
		for (int i = 0; i < array.length; i++) {
			int singlec = (int)array[i];
			array[i] = (char)(singlec + 2);
			sbchar.append(array[i]);
		}
		Arrays.fill(array, ' ');
		byte[] b = Base64.encodeBase64(sbchar.toString().getBytes(), false);
		ByteBuffer bb = ByteBuffer.allocate(b.length);
		bb.put(b).flip();
		Charset cs = Charset.forName("UTF-8");
		return cs.decode(bb).toString();
	}
	
	/**
	 * 对传入的字串进行解密编码
	 * 
	 * @param content 需要解密的字串
	 * @return 经过还原处理的字串
	 * @备注 2013年9月18日增加 字串解密
	 */
	public static String decode(String content) {
		if(content==null){
			log.debug("decode msg,msg is null");
			return null;
		}
		byte[] b = Base64.decodeBase64(content.getBytes());
		ByteBuffer bb = ByteBuffer.allocate(b.length);
		bb.put(b).flip();
		Charset cs = Charset.forName("UTF-8");
		String offsetContent = cs.decode(bb).toString();
		//还原实际字符串内容
		char[] array = offsetContent.toCharArray();
		StringBuffer sbchar = new StringBuffer();
		for (int i = 0; i < array.length; i++) {
			int singlec = (int)array[i];
			array[i] = (char)(singlec - 2);
			sbchar.append(array[i]);
		}
		Arrays.fill(array, ' ');
		return sbchar.toString();
	}
	
	/**
	 * 将客户端传来的ClientBatchBean中的密码进行加密
	 * 
	 * @param bean 客户端组装的Bean
	 */
	public static void encodeInBean(ClientBatchBean bean) {
		if(bean!=null){
			String passWord=bean.getPassWord();
			if(passWord!=null){
				bean.setPassWord(encode(passWord));
			}else {
				log.debug("doing encodeInBean,passWord is null");
			}
		}
	}
	
	/**
	 * 将客户端传来的ClientHeightQuery中的密码进行加密
	 * @param bean 客户端组装的Bean
	 */
	public static void encodeInBean(ClientHeightQuery bean) {
		if(bean!=null){
			String passWord=bean.getPassWord();
			if(passWord!=null){
				bean.setPassWord(encode(passWord));
			}else {
				log.debug("doing encodeInBean,passWord is null");
			}
		}
	}
	
	/**
	 * 将客户端传来的MigrateBatchBean中的密码进行加密
	 * @param bean 客户端组装的Bean
	 */
	public static void encodeInBean(MigrateBatchBean bean) {
		if(bean!=null){
			String passWord=bean.getPassWord();
			if(passWord!=null){
				bean.setPassWord(encode(passWord));
			}else {
				log.debug("doing encodeInBean,passWord is null");
			}
		}
	}
	public static String changeIp(String ip) {
		return ip;
	}
//		if (!LoadConfigFile.getInstance().getConfigBean().isOpenChanageIp()) {
//			log.debug("no need changeip");
//			return ip;
//		}
//		log.debug("changeIp:" + ip);
//		String ip1 = ip;
//		try {
//			InetAddress ia = InetAddress.getByName("c" + ip);
//			ip1 = ia.getHostAddress();
//		} catch (Exception e) {
//			// log.info("sourceIp=" + ip, e);
//		}
//		log.info("ip:" + ip + ",changeTo:" + ip1);
//		return ip1;
//	}
}