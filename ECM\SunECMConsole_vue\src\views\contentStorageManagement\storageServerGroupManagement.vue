<template>
   <div class="app-container">
       <div class="filter-container">
          <el-input v-model="listQuery.group_name"  placeholder="服务器名称"
             style="width: 200px" class="filter-item"
              @keyup.enter.native="handleFilter()"
          />
          <el-button v-if="this.hasPerm('nodeGroupSearch')" v-waves class="filter-item" plain 
          round  type="primary" icon="el-icon-search"
          size="mini"    @click="handleFilter()"
          >
           查询
          </el-button>
          <el-button
            v-waves
            class="filter-item"
            type="warning"
            icon="el-icon-delete-solid"
            size="mini"
            plain 
            round 
            @click="handleclear"
          >
           清空
          </el-button>
          <el-button
            v-if="this.hasPerm('addNodeGroup')"
            class="filter-item"
            style="margin-left: 10px"
            type="primary"
            icon="el-icon-plus"
            size="mini"
            @click="handleCreateGroup"
          >
          添加服务器组
          </el-button>
       </div>

       <el-table :key="tableKey" v-loading="listLoading" :data="contentServerGroupList" border fit
          highlight-current-row style="width: 100%"
       >
         <el-table-column label="服务器组名称" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.group_name }}</span>
            </template>
         </el-table-column>
         <el-table-column label="组id" style="width: 100%" align="center" v-if="false">
            <template slot-scope="{ row }">
            <span>{{ row.group_id }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作系统" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span v-if="row.os==1" >Windows</span>
            <span v-if="row.os==2" >Mac</span>
            <span v-if="row.os==3" >Linux</span>
            <span v-if="row.os==4" >Aix</span>
            </template>
         </el-table-column>
         <el-table-column label="部署方式" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span v-if="row.deploy_mode==0" >ECM均衡负载</span>
            <span v-if="row.deploy_mode==1" >其他集群方式(DR)</span>
            <span v-if="row.deploy_mode==2" >其他集群方式(NAT)</span>
            </template>
         </el-table-column>
         <el-table-column label="IP" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.group_ip }}</span>
            </template>
         </el-table-column>
         <el-table-column label="HTTP端口" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.http_port }}</span>
            </template>
         </el-table-column>
         <el-table-column label="SOCKET端口" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.socket_port }}</span>
            </template>
         </el-table-column>
         <el-table-column label="HTTPS端口" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.https_port }}</span>
            </template>
         </el-table-column>
         <el-table-column label="传输协议" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.trans_protocol }}</span>
            </template>
         </el-table-column>
         <el-table-column label="状态" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span v-if="row.state==0" style="color:red">禁用</span>
            <span v-if="row.state==1" style="color:green">启用</span>
            </template>
         </el-table-column>
         <el-table-column label="是否ECM存储" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span v-if="row.is_ecm_db==0" style="color:red">其他存储</span>
            <span v-if="row.is_ecm_db==1" style="color:green">ECM存储</span>
            </template>
         </el-table-column>
         <el-table-column label="备注" style="width: 100%" align="center">
            <template slot-scope="{ row }">
            <span>{{ row.remark }}</span>
            </template>
         </el-table-column>
         <el-table-column
          label="操作"
          align="left"
          width="300"
          class-name="small-padding fixed-width"
         >
           <template slot-scope="{ row, $index }">
              <el-button v-if="hasPerm('modifyNodeGroup')" type="primary" icon="el-icon-edit" size="mini" @click="handleUpdateGroup(row)">
                修改
              </el-button>
              <el-button
                v-if="row.state == '0' && hasPerm('ableNodeGroup')"
                size="mini"
                type="primary"
                icon="el-icon-open"
                @click="handleActiveGroup(row, $index)"
                            style="margin-bottom: 5px"

              >
               激活
              </el-button>
              <el-button
                v-if="row.state == '1' && hasPerm('disableNodeGroup')"
                size="mini"
                type="danger"
                icon="el-icon-turn-off"
                @click="handleDisableGroup(row, $index)"
                            style="margin-bottom: 5px"

              >
                禁用
              </el-button>
              <el-button
                v-if="hasPerm('nodeMetadata')"
                size="mini"
                type="primary"
                icon="el-icon-edit"
                @click="handleVolume(row, $index)"
                            style="margin-bottom: 5px"

              >
                配置存储卷
              </el-button>
              <el-button
                v-if="hasPerm('relateVolume')"
                size="mini"
                type="primary"
                icon="el-icon-edit"
                @click="handleRelContentObj(row, $index)"
                            style="margin-bottom: 5px"

              >
                关联内容对象
              </el-button>
           </template>
         </el-table-column>
       </el-table>
       <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="listQuery.start"
        :limit.sync="listQuery.limit"
        @pagination="getContentServerGroupList"
       />




       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="关联存储卷" :visible.sync="VolumeDialogVisible " width="1200px">
          <div class="app-container">
              <div class="filter-container">
                  <el-input v-model="volumelistQuery.volume_name"  placeholder="卷名称"
                     style="width: 200px" class="filter-item"
                     @keyup.enter.native="handleVolumeFilter"
                  />
                  <el-button v-waves class="filter-item" type="primary"  icon="el-icon-search" size="mini"
                     @click="handleVolumeFilter"
                  >
                  查询
                  </el-button>
                  <el-button
                     v-waves
                     class="filter-item"
                     type="primary"
                     icon="el-icon-delete-solid"
                     size="mini"
                     @click="handVolumeleclear"
                  >
                  清空
                  </el-button>
                  <el-button
                     class="filter-item"
                     style="margin-left: 10px"
                     type="primary"
                     icon="el-icon-plus"
                     size="mini"
                     @click="handAddVolume"
                  >
                  新增卷
                  </el-button>
              </div>
              <el-table :key="tableKey" v-loading="VolumelistLoading" :data="volumeList" border fit
                 highlight-current-row style="width: 100%"
              >
                 <el-table-column label="卷名称" width="120px" align="center">
                     <template slot-scope="{ row }">
                     <span>{{ row.volume_name }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column label="卷id" width="120px" align="center" v-if="false" >
                     <template slot-scope="{ row }">
                     <span>{{ row.volume_id }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column label="组id" width="120px" align="center" v-if="false" >
                     <template slot-scope="{ row }">
                     <span>{{ row.server_group_id }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column label="存储标识" width="120px" align="center">
                     <template slot-scope="{ row }">
                     <span>{{ row.root_path }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column label="存储路径" width="120px" align="center">
                     <template slot-scope="{ row }">
                     <span>{{ row.save_path }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column label="目录规则" width="120px" align="center">
                     <template slot-scope="{ row }">
                     <span>{{ row.path_rule }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column label="目录层数" width="120px" align="center">
                     <template slot-scope="{ row }">
                     <span>{{ row.path_number }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column label="最大目录树" width="120px" align="center">
                     <template slot-scope="{ row }">
                     <span>{{ row.dir_number }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column label="备注" width="120px" align="center">
                     <template slot-scope="{ row }">
                     <span>{{ row.volume_remark }}</span>
                     </template>
                 </el-table-column>
                 <el-table-column
                  label="操作"
                  align="center"
                  style="width: 100%"
                  class-name="small-padding fixed-width"
                 >
                   <template slot-scope="{ row }">
                     <el-button
                        size="mini"
                        type="primary"
                        icon="el-icon-edit"
                        @click="handleupdateVolume(row)"
                     >
                     修改存储卷
                     </el-button>
                   </template>
                 </el-table-column>
              </el-table>
              <pagination
               v-show="total > 0"
               :total="volumeTotal"
               :page.sync="volumelistQuery.start"
               :limit.sync="volumelistQuery.limit"
               @pagination="getVolumeInfoList"
              />
          </div>
  <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="VolumeDialogVisible = false"> 取消 </el-button>
      </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="新增卷" :visible.sync="VolumedialogFormVisible">
         <el-form
            ref="dataForm"
            :model="VolumeTemp"
            :rules="VolumeRules"
            label-position="left"
            label-width="120px"
            style="width: 400px; margin-left: 80px"
         >
         <el-form-item label="卷名称" prop="volume_name">
            <el-input
            v-model="VolumeTemp.volume_name"
            prop="volume_name"
            />
         </el-form-item>
         <el-form-item label="存储标识" prop="root_path">
            <el-input
            v-model="VolumeTemp.root_path"
            prop="root_path"
            />
         </el-form-item>
         <el-form-item label="存储路径" prop="save_path">
            <el-input
            v-model="VolumeTemp.save_path"
            prop="save_path"
            />
         </el-form-item>
         <el-form-item label="最大目录数" prop="dir_number">
            <el-input
            v-model="VolumeTemp.dir_number"
            prop="dir_number"
            />
         </el-form-item>
         <el-form-item label="目录规则" prop="path_rule">
           <el-select v-model="VolumeTemp.path_rule" placeholder="请选择目录规则">
             <el-option label="一年" value="1"/>
             <el-option label="一月" value="2"/>
             <el-option label="一天" value="3"/>
             <el-option label="无" value="0"/>
           </el-select>
         </el-form-item>
         <el-form-item label="目录层数" prop="path_number">
           <el-select v-model="VolumeTemp.path_number" placeholder="请选择目录层数">
             <el-option label="1层" value="1"/>
             <el-option label="2层" value="2"/>
           </el-select>
         </el-form-item>
         <el-form-item label="备注" prop="volume_remark">
            <el-input
            v-model="VolumeTemp.volume_remark"
            prop="volume_remark"
            />
         </el-form-item>
         </el-form>
         <div slot="footer" class="dialog-footer">
         <el-button size="mini" @click="VolumedialogFormVisible = false"> 取消 </el-button>
             <el-button size="mini" type="primary" v-preventReClick="500" @click="addVolume">

             提交
            </el-button>

         </div>
       </el-dialog>


       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="修改卷" :visible.sync="VolumeUpdatedialogFormVisible">
         <el-form
            ref="dataForm"
            :model="VolumeTemp"
            :rules="VolumeRules"
            label-position="left"
            label-width="120px"
            style="width: 400px; margin-left: 80px"
         >
         <el-form-item label="卷名称" prop="volume_name">
            <el-input
            v-model="VolumeTemp.volume_name"
            prop="volume_name"
            />
         </el-form-item>
         <el-form-item label="存储标识" prop="root_path">
            <el-input
            v-model="VolumeTemp.root_path"
            prop="root_path"
            :disabled = "true"
            />
         </el-form-item>
         <el-form-item label="存储路径" prop="save_path">
            <el-input
            v-model="VolumeTemp.save_path"
            prop="save_path"
            :disabled = "true"
            />
         </el-form-item>
         <el-form-item label="最大目录数" prop="dir_number">
            <el-input
            v-model="VolumeTemp.dir_number"
            prop="dir_number"
            />
         </el-form-item>
         <el-form-item label="目录规则" prop="path_rule">
           <el-select v-model="VolumeTemp.path_rule" placeholder="请选择目录规则">
             <el-option label="一年" value="1"/>
             <el-option label="一月" value="2"/>
             <el-option label="一天" value="3"/>
             <el-option label="无" value="0"/>
           </el-select>
         </el-form-item>
         <el-form-item label="目录层数" prop="path_number">
           <el-select v-model="VolumeTemp.path_number" placeholder="请选择目录层数">
             <el-option label="1层" value="1"/>
             <el-option label="2层" value="2"/>
           </el-select>
         </el-form-item>
         <el-form-item label="备注" prop="volume_remark">
            <el-input
            v-model="VolumeTemp.volume_remark"
            prop="volume_remark"
            />
         </el-form-item>
         </el-form>
         <div slot="footer" class="dialog-footer">
            <el-button size="mini" @click="VolumeUpdatedialogFormVisible = false"> 取消 </el-button>
            <el-button size="mini" type="primary" v-preventReClick="500"  @click="updateVolume" >
             提交
            </el-button>
         </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="新增内存存储服务器组" :visible.sync="addGroupDialogVisiblePage1">
          <el-form
            ref="dataForm"
            :model="Grouptemp"
            :rules="GroupRules"
            label-position="left"
            label-width="120px"
            style="width: 400px; margin-left: 50px"
          >
             <el-form-item label="服务器组名称" prop="group_name">
               <el-input
                  v-model="Grouptemp.group_name"
                  prop="group_name"
               />
             </el-form-item>
             <el-form-item label="操作系统" prop="os">
               <el-select v-model="Grouptemp.os" >
                  <el-option label="Windows" value="1"/>
                  <el-option label="Mac" value="2"/>
                  <el-option label="Linux" value="3"/>
                  <el-option label="Aix" value="4"/>
               </el-select>
             </el-form-item>
             <el-form-item label="部署方式" prop="deploy_mode">
               <el-select v-model="Grouptemp.deploy_mode" >
                  <el-option label="ECM均衡负载" value="0" />
                  <el-option label="其他集群方式(DR)" value="1" />
                  <el-option label="其他集群方式(NAT)" value="2" />
               </el-select>
             </el-form-item>
             <el-form-item label="IP" prop="group_ip" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-input
                  v-model="Grouptemp.group_ip"
                  prop="group_ip"
               />
             </el-form-item>
             <el-form-item label="HTTP端口" prop="http_port" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-input-number
                  v-model.number="Grouptemp.http_port"
                  prop="http_port"
               />
             </el-form-item>
             <el-form-item label="SOCKET端口" prop="socket_port" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-input-number
                  v-model.number="Grouptemp.socket_port"
                  prop="socket_port"
               />
             </el-form-item>
             <el-form-item label="HTTPS端口" prop="https_port" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-input-number
                  v-model.number="Grouptemp.https_port"
                  prop="https_port"
               />
             </el-form-item>
             <el-form-item label="传输协议" prop="trans_protocol" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-select v-model="Grouptemp.trans_protocol" >
                  <el-option label="http" value="http" />
                  <el-option label="https" value="https" />
               </el-select>
             </el-form-item>
             <el-form-item label="状态" prop="state">
               <el-select v-model="Grouptemp.state" >
                  <el-option label="禁用" value="0"/>
                  <el-option label="启用" value="1"/>
               </el-select>
             </el-form-item>
             <el-form-item label="是否ECM存储" prop="is_ecm_db">
               <el-select v-model="Grouptemp.is_ecm_db" >
                  <el-option label="其他存储" value="0"/>
                  <el-option label="ECM存储" value="1"/>
               </el-select>
             </el-form-item>
             <el-form-item label="备注" prop="remark">
               <el-input
                  v-model="Grouptemp.remark"
                  prop="remark"
               />
             </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="addGroupDialogVisiblePage1 = false"> 取消 </el-button>
             <el-button size="mini" type="primary" @click="handleAddGroupNext()">下一步</el-button>
          </div>
       </el-dialog>


       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="配置服务器" :visible.sync="addGroupDialogVisiblePage2" width="1200px">
        <div class="edit_dev"> 
          <el-transfer
            style="text-align: left; display: inline-block"
            v-model="RelContentServerTree"
            filterable
            :left-default-checked="[1]"
            :right-default-checked="[2]"
            :titles="['未分配的服务器', '已有服务器']"
            :button-texts="['到左边', '到右边']"
            :format="{
               noChecked: '${total}',
               hasChecked: '${checked}/${total}',
            }"
            :data="UnRelContentServerTree"
          >
          </el-transfer>
        </div>   
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="addGroupDialogVisiblePage2 = false"> 取消 </el-button>
             <el-button size="mini" type="primary"  @click="backToAddGroupPage1()">
             上一步
             </el-button>
             <el-button size="mini" type="primary"  @click="backToAddGroupPage3()">
             下一步
             </el-button>
          </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="配置服务器权重" :visible.sync="addGroupDialogVisiblePage3" >
           <el-table :key="tableKey"  :data="RelContentServerTree2" border fit
                 highlight-current-row style="width: 100%"
           >
             <el-table-column label="服务器名称" width="250px" align="center">
               <template slot-scope="{ row }">
               <span>{{ row.server_name }}</span>
               </template>
             </el-table-column>
             <el-table-column label="权重%" width="250px" align="center">
             <template slot-scope="{ row }">
               <el-input v-model="row.weight"></el-input>
             </template>
             </el-table-column>
           </el-table>
           <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="addGroupDialogVisiblePage3 = false"> 取消 </el-button>
             <el-button size="mini" type="primary"  @click="backToAddGroupPage2()">
             上一步
             </el-button>
             <el-button size="mini" type="primary"  @click="complete()">
             完成
             </el-button>
           </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="提示" :visible.sync="GroupWeightWarnDialogVisible" >
        权限分配总和必须等于100
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="修改内存存储服务器组" :visible.sync="updateGroupDialogVisiblePage1">
          <el-form
            ref="dataForm"
            :model="Grouptemp"
            :rules="GroupRules"
            label-position="left"
            label-width="120px"
            style="width: 400px; margin-left: 50px"
          >
              <el-form-item label="服务器组名称" prop="group_name">
               <el-input
                  v-model="Grouptemp.group_name"
                  prop="group_name"
               />
             </el-form-item>
             <el-form-item label="操作系统" prop="os">
               <el-select v-model="Grouptemp.os" >
                  <el-option label="Windows" value="1"/>
                  <el-option label="Mac" value="2"/>
                  <el-option label="Linux" value="3"/>
                  <el-option label="Aix" value="4"/>
               </el-select>
             </el-form-item>
             <el-form-item label="部署方式" prop="deploy_mode">
               <el-select v-model="Grouptemp.deploy_mode" >
                  <el-option label="ECM均衡负载" value="0" />
                  <el-option label="其他集群方式(DR)" value="1" />
                  <el-option label="其他集群方式(NAT)" value="2" />
               </el-select>
             </el-form-item>
             <el-form-item label="IP" prop="group_ip" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-input
                  v-model="Grouptemp.group_ip"
                  prop="group_ip"
               />
             </el-form-item>
             <el-form-item label="HTTP端口" prop="http_port" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-input-number
                  v-model.number="Grouptemp.http_port"
                  prop="http_port"
               />
             </el-form-item>
             <el-form-item label="SOCKET端口" prop="socket_port" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-input-number
                  v-model.number="Grouptemp.socket_port"
                  prop="socket_port"
               />
             </el-form-item>
             <el-form-item label="HTTPS端口" prop="https_port" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-input-number
                  v-model.number="Grouptemp.https_port"
                  prop="https_port"
               />
             </el-form-item>
             <el-form-item label="传输方式" prop="trans_protocol" v-if="Grouptemp.deploy_mode =='0'?false:true">
               <el-select v-model="Grouptemp.trans_protocol" >
                  <el-option label="http" value="http" />
                  <el-option label="https" value="https" />
               </el-select>
             </el-form-item>
             <el-form-item label="状态" prop="state">
               <el-select v-model="Grouptemp.state" >
                  <el-option label="禁用" value="0"/>
                  <el-option label="启用" value="1"/>
               </el-select>
             </el-form-item>
             <el-form-item label="是否ECM存储" prop="is_ecm_db">
               <el-select v-model="Grouptemp.is_ecm_db" >
                  <el-option label="其他存储" value="0"/>
                  <el-option label="ECM存储" value="1"/>
               </el-select>
             </el-form-item>
             <el-form-item label="备注" prop="remark">
               <el-input
                  v-model="Grouptemp.remark"
                  prop="remark"
               />
             </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="updateGroupDialogVisiblePage1 = false"> 取消 </el-button>
             <el-button size="mini" type="primary" @click="handleUpdateGroupNext()">下一步</el-button>
          </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="修改配置服务器" :visible.sync="updateGroupDialogVisiblePage2" width="1200px">
        <div class="edit_dev">
          <el-transfer
            style="text-align: left; display: inline-block"
            v-model="RelContentServerTree"
            filterable
            :left-default-checked="[1]"
            :right-default-checked="[2]"
            :titles="['未分配的服务器', '已有服务器']"
            :button-texts="['到左边', '到右边']"
            :format="{
               noChecked: '${total}',
               hasChecked: '${checked}/${total}',
            }"
            :data="UnRelContentServerTree"
          >
          </el-transfer>
         </div>  
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="updateGroupDialogVisiblePage2 = false"> 取消 </el-button>
             <el-button size="mini" type="primary" @click="backToUpdateGroupPage1()">
             上一步
             </el-button>
             <el-button size="mini" type="primary" @click="backToUpdateGroupPage3()">
             下一步
             </el-button>
          </div>
       </el-dialog>


       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="更新服务器权重" :visible.sync="updateGroupDialogVisiblePage3" >
           <el-table :key="tableKey"  :data="RelContentServerTree2" border fit
                 highlight-current-row style="width: 100%"
           >
             <el-table-column label="服务器名称" width="250px" align="center">
               <template slot-scope="{ row }">
               <span>{{ row.server_name }}</span>
               </template>
             </el-table-column>
             <el-table-column label="权重%" width="250px" align="center">
             <template slot-scope="{ row }">
               <el-input v-model="row.weight"></el-input>
             </template>
             </el-table-column>
           </el-table>
           <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="updateGroupDialogVisiblePage3 = false"> 取消 </el-button>
             <el-button size="mini" type="primary" @click="backToUpdateGroupPage2()">
             上一步
             </el-button>
             <el-button size="mini" type="primary" @click="updateComplete()">
             完成
             </el-button>
           </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="警告" :visible.sync="GroupWeightWarnDialogVisible" >
        权限分配总和必须等于100
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="关联内容对象(仅配置拥有内容数据开始时间的内容模型)" :visible.sync="RelContentObjPage1Visible" width="1200px" >
           <div class="edit_dev"> 
            <el-transfer
            style="text-align: left; display: inline-block"
            v-model="RelContentObjTree"
            filterable
            :left-default-checked="[1]"
            :right-default-checked="[2]"
            :titles="['未选择的内容对象', '已选择的内容对象']"
            :button-texts="['到左边', '到右边']"
            :format="{
               noChecked: '${total}',
               hasChecked: '${checked}/${total}',
            }"
            :data="UnRelContentObjTree"
          >
          </el-transfer>
        </div>  
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="RelContentObjPage1Visible = false"> 取消 </el-button>
             <el-button size="mini" type="primary" @click="backToRelContentObjPage2()">
             下一步
             </el-button>
          </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="关联内容对象(关联卷)" :visible.sync="RelContentObjPage2Visible" >
         <div class="app-container">
            <el-table :key="tableKey"  :data="RelContentObjTree2" border fit
                  highlight-current-row style="width: 100%"
            >
            <el-table-column label="内容对象" width="250px" align="center">
                  <template slot-scope="{ row }">
                  <span>{{ row.contentObjId }}</span>
                  </template>
            </el-table-column>
            <el-table-column label="存储卷" width="250px" align="center">
                  <template slot-scope="{ row }">
                  <span v-if="row.VolumnName==''">请选择</span>
                  <span v-if="row.VolumnName!=''">{{ row.VolumnName }}</span>
                  </template>
            </el-table-column>
            <el-table-column
                  label="操作"
                  align="center"
                  style="width: 100%"
                  class-name="small-padding fixed-width"
                 >
                   <template slot-scope="{ row }">
                     <el-button
                        size="mini"
                        type="primary"
                        @click="handRelContent_volumn(row)"
                     >
                     关联卷
                     </el-button>
                   </template>
                 </el-table-column>
            </el-table>
         </div>
         <div slot="footer" class="dialog-footer">
             <el-button size="mini" @click="RelContentObjPage2Visible = false"> 取消 </el-button>
             <el-button size="mini" type="primary" @click="backToRelContentObjPage1()">
             上一步
             </el-button>
             <el-button size="mini" type="primary" @click="RelContentObjDone()">
             完成
             </el-button>
          </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="关联卷"   :visible.sync="RelContentObjRelVolumnPageVisible" >
          <div>
            <span>关联卷:</span>
            <el-select v-model="SelectVolumnId" placeholder="请选择">
               <el-option
                  v-for="(item,index) in RelVolumnList"
                  :key="index"
                  :label="item.text_text"
                  :value="item.id">
               </el-option>
            </el-select>
          </div>
          <div slot="footer" class="dialog-footer">
             <el-button size="mini" type="primary" @click="cancelVolumn()">
             取消
             </el-button>
             <el-button size="mini" type="primary" @click="submitVolumn()">
             提交
             </el-button>
          </div>
       </el-dialog>

       <el-dialog v-el-drag-dialog :close-on-click-modal="false" title="警告" :visible.sync="SelectVolumnIdWarmPageVisible" >
           请选择卷信息
       </el-dialog>
   </div>
</template>

<script>
import {
  getContentServerGroupListAction,
  activeGroup,
  disableGroup,
  getVolumeInfoListAction,
  configVolume,
  getUnRelContentServerTree,
  getAllContentServerList,
  addContentServerGroupAction,
  getRelContentServerTree,
  AllContentServerweightList,
  getUnRelContentObjectTreeAction,
  getRelContentObjectTreeAction,
  getRelVolumnAction,
  addRelGroupAndContentObjectAction,
  checkGroupNameAction,
  checkGroupIPandPortAction,
} from "@/api/contentServerGroupManage";
import waves from "@/directive/waves"; // waves directive
import elDragDialog from "@/directive/el-drag-dialog";
import { parseTime } from "@/utils";
import Pagination from "@/components/Pagination"; // secondary package based on el-pagination
import global from "../../store/global.js";

export default {
  name: "ComplexTable",
  components: { Pagination },
  directives: { waves,elDragDialog },
  data() {
    return {
      tableKey: 0,
      contentServerGroupList: null,
      total: 0,
      listLoading: true,
      listQuery: {
        start: 1,
        limit: 20,
        group_name: "",
      },
      VolumeDialogVisible: false,
      volumeList: null,
      volumeTotal: 0,
      VolumelistLoading: true,
      volumelistQuery: {
        start: 1,
        limit: 10,
        server_group_id: "",
        volume_name: "",
      },
      VolumeRules: {
        volume_name: [
          {
            required: true,
            pattern: global.regexName,
            message: global.regexNameText,
            trigger: "blur",
          },
        ],
        root_path: [
          {
            required: true,
            pattern: /^(\w:)?\/$/,
            message: "建议:Windows输入格式如 C:/;linux输入格式如 / ",
            trigger: "blur",
          },
        ],
        save_path: [
          {
            required: true,
            pattern: /\/$/,
            message: "输入如SunECM/,支持${yyyy}格式",
            trigger: "blur",
          },
        ],
        dir_number: [
          {
            required: true,
            pattern: /^\d{1,3}$/,
            message: "输入1~999",
            trigger: "blur",
          },
        ],
        path_rule: [
          { required: true, message: "请选择目录规则", trigger: "blur" },
        ],
        path_number: [
          { required: true, message: "请选择目录层数", trigger: "blur" },
        ],
      },
      VolumedialogFormVisible: false,
      Volume_server_group_id: "",
      VolumeTemp: {
        optionFlag: "",
        c_volume_id: "",
        volume_remark: "",
        id: undefined,
        importance: 1,
        timestamp: new Date(),
        title: "",
        type: "",
        status: "published",
      },
      VolumeUpdatedialogFormVisible: false,
      addGroupDialogVisiblePage1: false,
      addGroupDialogVisiblePage2: false,
      addGroupDialogVisiblePage3: false,
      UnRelContentServerTree: [],
      RelContentServerTree: [],
      RelContentServerTree2: [],
      Grouptemp: {
        optionFlag: "",
        group_id: "",
        group_name: "",
        group_ip: "",
        http_port: "0",
        socket_port: "0",
        https_port: "0",
        state: "1",
        os: "1",
        remark: "",
        deploy_mode: "0",
        server_ids: "",
        serverId_weight: "",
        is_ecm_db: "1",
        trans_protocol: "",
      },
      GroupRules: {
        group_name: [
          {
            required: true,
            pattern: global.regexName,
            message: global.regexNameText,
            trigger: "blur",
          },
        ],
        os: [{ required: true, message: "操作系统必选", trigger: "blur" }],
        deploy_mode: [
          { required: true, message: "部署方式必选", trigger: "blur" },
        ],
        state: [{ required: true, message: "状态必选", trigger: "blur" }],
        group_ip: [{ required: true, pattern: global.regNoSpecial, message: "输入格式不正确", trigger: "blur" }],
        http_port: [{ required: true, message: "ip必输", trigger: "blur" }],
        socket_port: [{ required: true, message: "ip必输", trigger: "blur" }],
        https_port: [{ required: true, message: "ip必输", trigger: "blur" }],
        trans_protocol: [
          { required: true, message: "传输协议必选", trigger: "blur" },
        ],
      },
      serverMap: new Map([]),
      weightMap: new Map([]),
      relServer: {
        server_id: "",
        server_name: "",
        weight: 0,
      },
      GroupWeightWarnDialogVisible: false,
      updateGroupDialogVisiblePage1: false,
      updateGroupDialogVisiblePage2: false,
      updateGroupDialogVisiblePage3: false,

      RelContentObjdata: {
        group_id: "",
        modelCode_volumeId: "",
        modelCodes: "",
      },
      UnRelContentObjTree: [],
      RelContentObjTree: [],
      RelContentObj_volumeIdMap: new Map([]),
      RelContentObjTree2: [],
      RelVolumnList: [],
      VolumnMap: new Map([]),
      RelContentObjPage1Visible: false,
      RelContentObjPage2Visible: false,
      RelContentObjRelVolumnPageVisible: false,
      SelectVolumnId: "",
      SelectContentObjId: "",
      SelectVolumnIdWarmPageVisible:false
    };
  },
  created() {
    this.getContentServerGroupList();
  },
  methods: {
    getContentServerGroupList() {
      this.listLoading = true;
      getContentServerGroupListAction(this.listQuery).then((response) => {
        this.contentServerGroupList = response.root;
        this.total = Number(response.totalProperty);
        setTimeout(() => {
          this.listLoading = false;
        }, 1 * 100);
      });
    },
    handleclear() {
      this.listQuery.group_name = "";
    },
    handleFilter() {
      this.listQuery.start = 1;
      this.getContentServerGroupList();
    },
    //服务器组激活禁用
    handleActiveGroup(row, index) {
      activeGroup(row).then((response) => {
        this.getContentServerGroupList();
      });
    },
    handleDisableGroup(row, index) {
      disableGroup(row).then((response) => {
        this.getContentServerGroupList();
      });
    },
    //服务器组激活禁用end
    //配置存储卷
    getVolumeInfoList() {
      getVolumeInfoListAction(this.volumelistQuery).then((response) => {
        this.volumeList = response.root;
        this.volumeTotal = Number(response.totalProperty);
        setTimeout(() => {
          this.VolumelistLoading = false;
        }, 1 * 100);
      });
    },
    handleVolume(row, index) {
      this.Volume_server_group_id = row.group_id;
      this.volumelistQuery.server_group_id = row.group_id;
      this.getVolumeInfoList();
      this.VolumelistLoading = true;
      this.VolumeDialogVisible = true;
    },
    handVolumeleclear() {
      this.volumelistQuery.volume_name = "";
    },
    handleVolumeFilter() {
      this.volumelistQuery.start = 1;
      this.getVolumeInfoList();
    },
    resetVolumeTemp() {
      this.VolumeTemp = {
        optionFlag: "",
        c_volume_id: "",
        volume_remark: "",
        id: undefined,
        importance: 1,
        timestamp: new Date(),
        title: "",
        status: "published",
        type: "",
        name: "",
      };
    },
    handAddVolume() {
      this.resetVolumeTemp();
      this.VolumeTemp.server_group_id = this.Volume_server_group_id;
      this.VolumedialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    addVolume() {
      this.VolumeTemp.optionFlag = "create1";
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          configVolume(this.VolumeTemp).then(() => {
            this.getVolumeInfoList();
            this.VolumedialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "新增卷成功",
              type: "success",
              duration: 4000,
            });
          })
        }
      });
    },
    handleupdateVolume(row) {
      this.resetVolumeTemp();
      this.VolumeTemp = Object.assign({}, row); // copy obj
      this.VolumeTemp.server_group_id = this.Volume_server_group_id;
      this.VolumeTemp.c_volume_id = row.volume_id;
      this.VolumeUpdatedialogFormVisible = true;
      this.$nextTick(() => {
        this.$refs["dataForm"].clearValidate();
      });
    },
    updateVolume() {
      this.VolumeTemp.optionFlag = "update1";
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          configVolume(this.VolumeTemp).then(() => {
            this.getVolumeInfoList();
            this.VolumeUpdatedialogFormVisible = false;
            this.$notify({
              title: "Success",
              message: "修改卷成功",
              type: "success",
              duration: 4000,
            });
          })
        }
      });
    },
    //配置存储卷end

    //新增服务器组
    resetGrouptemp() {
      (this.UnRelContentServerTree = []),
        (this.RelContentServerTree = []),
        (this.RelContentServerTree2 = []),
        (this.Grouptemp = {
          optionFlag: "",
          group_id: "0",
          group_name: "",
          group_ip: "",
          http_port: "0",
          socket_port: "0",
          https_port: "0",
          state: "1",
          os: "1",
          remark: "",
          deploy_mode: "0",
          server_ids: "",
          serverId_weight: "",
          is_ecm_db: "1",
          trans_protocol: "",
        });
    },
    handleCreateGroup() {
      this.resetGrouptemp();
      this.addGroupDialogVisiblePage1 = true;
    },
    handleAddGroupNext() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          checkGroupNameAction(this.Grouptemp).then((response) => {
            if (this.Grouptemp.deploy_mode != 0) {
              checkGroupIPandPortAction(this.Grouptemp).then((response) => {
                this.addGroupDialogVisiblePage1 = false;
                this.getUnRelContentServerTree();
                this.addGroupDialogVisiblePage2 = true;
              });
            } else {
              this.addGroupDialogVisiblePage1 = false;
              this.getUnRelContentServerTree();
              this.addGroupDialogVisiblePage2 = true;
            }
          });
        }
      });
    },
    getUnRelContentServerTree() {
      this.UnRelContentServerTree = [];
      this.RelContentServerTree = [];
      this.RelContentServerTree2 = [];
      getUnRelContentServerTree(this.Grouptemp).then((response) => {
        let noRefServerTree = response.root;
        noRefServerTree.map((item) => {
          this.UnRelContentServerTree.push({
            key: item.server_id,
            label: item.server_name,
          });
        });
      });
    },
    backToAddGroupPage1() {
      this.addGroupDialogVisiblePage2 = false;
      this.addGroupDialogVisiblePage1 = true;
    },
    backToAddGroupPage2() {
      this.RelContentServerTree2 = [];
      this.addGroupDialogVisiblePage3 = false;
      this.addGroupDialogVisiblePage2 = true;
    },
    backToAddGroupPage3() {
      this.addGroupDialogVisiblePage2 = false;
      this.addGroupDialogVisiblePage3 = true;
      getAllContentServerList().then((response) => {
        let AllContentServerList = response.root;
        AllContentServerList.map((item) => {
          this.serverMap.set(item.server_id, item.server_name);
        });
        for (var i = 0; i < this.RelContentServerTree.length; i++) {
          var relServer = {
            server_id: "",
            server_name: "",
            weight: 0,
          };
          relServer.server_id = this.RelContentServerTree[i];
          relServer.server_name = this.serverMap.get(
            Number(this.RelContentServerTree[i])
          );
          this.RelContentServerTree2.push(relServer);
        }
      });
    },
    complete() {
      var totalWeight = 0;
      var weights = "";
      var serverids = "";
      for (var i = 0; i < this.RelContentServerTree2.length; i++) {
        serverids = serverids + this.RelContentServerTree2[i].server_id + ",";
        weights =
          weights +
          this.RelContentServerTree2[i].server_id +
          "=" +
          this.RelContentServerTree2[i].weight +
          ",";
        totalWeight =
          parseInt(totalWeight) +
          parseInt(this.RelContentServerTree2[i].weight);
      }
      if (this.RelContentServerTree2.length != 0) {
        if (totalWeight != 100) {
          this.GroupWeightWarnDialogVisible = true;
        } else {
          this.Grouptemp.serverId_weight = weights;
          this.Grouptemp.server_ids = serverids;
          this.Grouptemp.optionFlag = "create1";
          addContentServerGroupAction(this.Grouptemp).then((response) => {
            this.getContentServerGroupList();
            this.addGroupDialogVisiblePage3 = false;
            this.$notify({
              title: "Success",
              message: "新增服务器组成功",
              type: "success",
              duration: 4000,
            });
          });
        }
      } else {
        this.Grouptemp.serverId_weight = weights;
        this.Grouptemp.server_ids = serverids;
        this.Grouptemp.optionFlag = "create1";
        addContentServerGroupAction(this.Grouptemp).then((response) => {
          this.getContentServerGroupList();
          this.addGroupDialogVisiblePage3 = false;
          this.$notify({
            title: "Success",
            message: "新增服务器组成功",
            type: "success",
            duration: 4000,
          });
        });
      }
    },
    //新增服务器组end

    //修改服务器组模块
    handleUpdateGroup(row) {
      this.resetGrouptemp();
      this.updateGroupDialogVisiblePage1 = true;
      this.Grouptemp.group_id = row.group_id;
      this.Grouptemp.group_name = row.group_name;
      this.Grouptemp.group_ip = row.group_ip;
      this.Grouptemp.http_port = row.http_port;
      this.Grouptemp.socket_port = row.socket_port;
      this.Grouptemp.https_port = row.https_port;
      this.Grouptemp.state = row.state;
      this.Grouptemp.os = row.os;
      this.Grouptemp.remark = row.remark;
      this.Grouptemp.deploy_mode = row.deploy_mode;
      this.Grouptemp.is_ecm_db = row.is_ecm_db;
      this.Grouptemp.trans_protocol = row.trans_protocol;
    },
    handleUpdateGroupNext() {
      this.$refs["dataForm"].validate((valid) => {
        if (valid) {
          checkGroupNameAction(this.Grouptemp).then((response) => {
            if (this.Grouptemp.deploy_mode != 0) {
              checkGroupIPandPortAction(this.Grouptemp).then((response) => {
                this.updateGroupDialogVisiblePage1 = false;
                this.getUnRelContentServerTree();
                this.getRelContentServertree();
                this.updateGroupDialogVisiblePage2 = true;
              });
            } else {
              this.updateGroupDialogVisiblePage1 = false;
              this.getUnRelContentServerTree();
              this.getRelContentServertree();
              this.updateGroupDialogVisiblePage2 = true;
            }
          });
        }
      });
    },
    getRelContentServertree() {
      getRelContentServerTree(this.Grouptemp).then((response) => {
        let RefServerTree = response.root;
        RefServerTree.map((item) => {
          this.UnRelContentServerTree.push({
            key: item.server_id,
            label: item.server_name,
          });
          this.RelContentServerTree.push(item.server_id);
        });
      });
    },
    backToUpdateGroupPage1() {
      this.updateGroupDialogVisiblePage1 = true;
      this.updateGroupDialogVisiblePage2 = false;
    },
    backToUpdateGroupPage3() {
      this.updateGroupDialogVisiblePage2 = false;
      getAllContentServerList().then((response) => {
        let AllContentServerList = response.root;
        AllContentServerList.map((item) => {
          this.serverMap.set(item.server_id, item.server_name);
          this.weightMap.set(item.server_id, item.weight);
        });
        for (var i = 0; i < this.RelContentServerTree.length; i++) {
          var relServer = {
            server_id: "",
            server_name: "",
            weight: 0,
          };
          relServer.server_id = this.RelContentServerTree[i];
          relServer.server_name = this.serverMap.get(
            Number(this.RelContentServerTree[i])
          );
          relServer.weight = this.weightMap.get(
            Number(this.RelContentServerTree[i])
          );
          this.RelContentServerTree2.push(relServer);
        }
      });
      this.updateGroupDialogVisiblePage3 = true;
    },
    backToUpdateGroupPage2() {
      this.RelContentServerTree2 = [];
      this.updateGroupDialogVisiblePage3 = false;
      this.updateGroupDialogVisiblePage2 = true;
    },
    updateComplete() {
      var totalWeight = 0;
      var weights = "";
      var serverids = "";
      for (var i = 0; i < this.RelContentServerTree2.length; i++) {
        serverids = serverids + this.RelContentServerTree2[i].server_id + ",";
        weights =
          weights +
          this.RelContentServerTree2[i].server_id +
          "=" +
          this.RelContentServerTree2[i].weight +
          ",";
        totalWeight =
          parseInt(totalWeight) +
          parseInt(this.RelContentServerTree2[i].weight);
      }
      if (this.RelContentServerTree2.length != 0) {
        if (totalWeight != 100) {
          this.GroupWeightWarnDialogVisible = true;
        } else {
          this.Grouptemp.serverId_weight = weights;
          this.Grouptemp.server_ids = serverids;
          this.Grouptemp.optionFlag = "update1";
          addContentServerGroupAction(this.Grouptemp).then((response) => {
            this.getContentServerGroupList();
            this.addGroupDialogVisiblePage3 = false;
            this.$notify({
              title: "Success",
              message: "更新服务器组成功",
              type: "success",
              duration: 4000,
            });
          });
          this.updateGroupDialogVisiblePage3 = false;
        }
      } else {
        this.Grouptemp.serverId_weight = weights;
        this.Grouptemp.server_ids = serverids;
        this.Grouptemp.optionFlag = "update1";
        addContentServerGroupAction(this.Grouptemp).then((response) => {
          this.getContentServerGroupList();
          this.addGroupDialogVisiblePage3 = false;
          this.$notify({
            title: "Success",
            message: "更新服务器组成功",
            type: "success",
            duration: 4000,
          });
        });
        this.updateGroupDialogVisiblePage3 = false;
      }
    },
    //修改服务器组end
    //关联内容对象
    handleRelContentObj(row, index) {
      this.RelContentObjdata = {
        group_id: "",
        modelCode_volumeId: "",
        modelCodes: "",
      };
      this.RelContentObjdata.group_id = row.group_id;
      this.UnRelContentObjTree = [];
      this.RelContentObjTree = [];
      this.getUnRelContentObjectTree(row);
      this.getRelContentObjectTree(row);
      this.getRelVolumn(row);
      this.RelContentObjPage1Visible = true;
    },
    getUnRelContentObjectTree(row) {
      getUnRelContentObjectTreeAction(row).then((response) => {
        let unRelContentObjectTree = response.root;
        unRelContentObjectTree.map((item) => {
          this.UnRelContentObjTree.push({
            key: item.id,
            label: item.text,
          });
        });
      });
    },
    getRelContentObjectTree(row) {
      this.RelContentObj_volumeIdMap = new Map([]);
      getRelContentObjectTreeAction(row).then((response) => {
        let RelContentObjectTreeResult = response.root;
        RelContentObjectTreeResult.map((item) => {
          this.UnRelContentObjTree.push({
            key: item.id,
            label: item.text,
          });
          this.RelContentObjTree.push(item.id);
          this.RelContentObj_volumeIdMap.set(item.id, item.volumnId);
        });
      });
    },
    getRelVolumn(row) {
      this.RelVolumnList = [];
      this.VolumnMap = new Map([]);
      getRelVolumnAction(row).then((response) => {
        this.RelVolumnList = response.root;
        for (var i = 0; i < this.RelVolumnList.length; i++) {
          this.VolumnMap.set(
            this.RelVolumnList[i].id,
            this.RelVolumnList[i].text_text
          );
        }
      });
    },
    backToRelContentObjPage2() {
      this.RelContentObjTree2 = [];
      for (var i = 0; i < this.RelContentObjTree.length; i++) {
        var contentObj_volumn = {
          contentObjId: "",
          VolumnId: "",
          VolumnName: "",
        };
        contentObj_volumn.contentObjId = this.RelContentObjTree[i];
        if (
          this.RelContentObj_volumeIdMap.get(this.RelContentObjTree[i]) != null
        ) {
          contentObj_volumn.VolumnId = this.RelContentObj_volumeIdMap.get(
            this.RelContentObjTree[i]
          );
        }
        if (this.VolumnMap.get(contentObj_volumn.VolumnId) != null) {
          contentObj_volumn.VolumnName = this.VolumnMap.get(contentObj_volumn.VolumnId);
        }
        this.RelContentObjTree2.push(contentObj_volumn);
      }
      this.RelContentObjPage1Visible = false;
      this.RelContentObjPage2Visible = true;
    },
    backToRelContentObjPage1() {
      this.RelContentObjPage1Visible = true;
      this.RelContentObjPage2Visible = false;
    },
    handRelContent_volumn(row) {
      this.SelectVolumnId = "";
      this.SelectContentObjId = "";
      this.SelectContentObjId = row.contentObjId;
      this.RelContentObjPage2Visible = false;
      this.RelContentObjRelVolumnPageVisible = true;
    },
    cancelVolumn() {
      this.SelectVolumnId = "";
      this.SelectContentObjId = "";
      this.RelContentObjPage2Visible = true;
      this.RelContentObjRelVolumnPageVisible = false;
    },
    submitVolumn() {
      for (var i = 0; i < this.RelContentObjTree2.length; i++) {
        if (this.RelContentObjTree2[i].contentObjId == this.SelectContentObjId) {
          if (this.SelectVolumnId != "") {
            this.RelContentObjTree2[i].VolumnId = this.SelectVolumnId;
            this.RelContentObjTree2[i].VolumnName = this.VolumnMap.get(this.SelectVolumnId);
            this.RelContentObjPage2Visible = true;
            this.RelContentObjRelVolumnPageVisible = false;
          }else{
            this.SelectVolumnIdWarmPageVisible = true;
          }
        }
      }
      
    },
    RelContentObjDone() {
      var modelCode_volumeId = "";
      var modelCodes = "";
    if(this.RelContentObjTree2.length!=0){
        for (var i = 0; i < this.RelContentObjTree2.length; i++) {
            if(this.RelContentObjTree2[i].VolumnId !=""){
                for (var i = 0; i < this.RelContentObjTree2.length; i++) {
                    modelCode_volumeId =
                    modelCode_volumeId +
                    this.RelContentObjTree2[i].contentObjId +
                    "=" +
                    this.RelContentObjTree2[i].VolumnId +
                    ",";
                  modelCodes = modelCodes + this.RelContentObjTree2[i].contentObjId + ",";
                }
                this.RelContentObjdata.modelCode_volumeId = modelCode_volumeId;
                this.RelContentObjdata.modelCodes = modelCodes;
                addRelGroupAndContentObjectAction(this.RelContentObjdata).then(
                  (response) => {
                    this.$notify({
                      title: "Success",
                      message: "关联内容对象成功",
                      type: "success",
                      duration: 4000,
                    });
                  }
                );
                this.RelContentObjPage2Visible = false;
          }else{
            this.SelectVolumnIdWarmPageVisible = true;
          }
        }  
      }else{
         for (var i = 0; i < this.RelContentObjTree2.length; i++) {
              modelCode_volumeId =
              modelCode_volumeId +
              this.RelContentObjTree2[i].contentObjId +
              "=" +
              this.RelContentObjTree2[i].VolumnId +
              ",";
               modelCodes = modelCodes + this.RelContentObjTree2[i].contentObjId + ",";
         }
         this.RelContentObjdata.modelCode_volumeId = modelCode_volumeId;
                this.RelContentObjdata.modelCodes = modelCodes;
                addRelGroupAndContentObjectAction(this.RelContentObjdata).then(
                  (response) => {
                    this.$notify({
                      title: "Success",
                      message: "关联内容对象成功",
                      type: "success",
                      duration: 4000,
                    });
                  }
                );
                this.RelContentObjPage2Visible = false;  
      }

    },
  },
};
</script>

<style scoped>
.edit_dev >>> .el-transfer-panel {
     width:350px;
   }
</style>
