package com.sunyard.ecm.server.bean;

import com.sunyard.ecm.server.bean.converter.StringCustomConverter;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamConverter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档部件信息
 * <AUTHOR>
 *
 */
@XStreamAlias("FileBean")
public class FileBean {
	/** 文件版本 **/
	@XStreamAsAttribute
	private String VERSION;
	/** 文件迁移状态1为待迁移，2为已迁移 **/
	@XStreamAsAttribute
	private String MIGRATION_STATUS;
	/** 上传时间 **/
	@XStreamAsAttribute
	private String UPLOAD_TIME;
	/** 上传用户 **/
	@XStreamAsAttribute
	private String UPLOAD_USER;
	/** 文件格式 **/
	@XStreamAsAttribute
	private String FILE_FORMAT;
	/** 文件ID **/
	@XStreamAsAttribute
	private String FILE_NO;
	/** 文件保存名 ,用于判断是否上传完成**/
	@XStreamAsAttribute
	private String SAVE_NAME;
	/** 文件ID **/
	@XStreamAsAttribute
	private String FILE_REPLACE_NO;
	/** 内容ID **/
	@XStreamAsAttribute
	private String CONTENT_ID;
	/** 文件状态 **/
	@XStreamAsAttribute
	private String FILE_STATUS;
	/** 文件上传之前在客户端的路径+名称 **/
	@XStreamAsAttribute
	private String FILE_NAME;
	/** 操作类型：1为追加，2为替换， 3为删除, 4为更新索引 **/
	@XStreamAsAttribute
	private String OPTION_TYPE;
	/** 有效期 **/
	@XStreamAsAttribute
	private String VALID_PERIOD;
	/** 文件大小 **/
	@XStreamAsAttribute
	private String FILE_SIZE;
	/** 物理文件实际大小，FILE_SIZE在大小图模式下是大图的大小 **/
	@XStreamAsAttribute
	private String REAL_FILE_SIZE;
	/** 文件所在服务器ID **/
	@XStreamAsAttribute
	private String SERVER_ID;
	@XStreamAsAttribute
	private String GROUP_ID;
	/** 文件访问的url **/
	@XStreamAsAttribute
	private String URL;
	/** 文件MD5码 **/
	@XStreamAsAttribute
	private String MD5_STR;
	/** 文件随机路径 **/
	@XStreamAsAttribute
	private String FILE_PATH;
	/** 文件存储卷ID **/
	@XStreamAsAttribute
	private String VOLUME_ID;
	/** 用户的其余自定义属性 **/
	@XStreamConverter(StringCustomConverter.class)
	private Map<String, String> otherAtt;
	/** 文件批注 **/
	private List<AnnotationBean> annoList;
	/** 标签属性 **/
	/**
	 * 断点文件生成时，服务端会修改saveName。需添加此字段用以在断点时比较文件名。
	 */
	@XStreamAsAttribute
	private String origName;
	/**
	 * 服务端已接收文件大小
	 */
	@XStreamAsAttribute
	private Long received=null;
	/**
	 * 传输文件所使用的协议
	 */
	@XStreamAsAttribute
	private String PROTOCOL;
	/**
	 * 文件位置
	 */
	@XStreamAsAttribute
	private String LOCATION;
	/**
	 * 加密的字节大小,格式为：加密前字节+"_"+加密后字节
	 */
	@XStreamAsAttribute
	private String ENCODESIZE;
	
	/** 文档标签 **/
	private List<TagBean> tagBeanList;
	
	public List<TagBean> getTagBeanList() {
		return tagBeanList;
	}

	public void setTagBeanList(List<TagBean> tagBeanList) {
		this.tagBeanList = tagBeanList;
	}

	/**
	 *   添加标签
	 * 
	 * @param fangyue
	 *           
	 */
	public void addTagBeanList(TagBean tagBean) {
		if (tagBeanList == null) {
			tagBeanList = new ArrayList<TagBean>();
		}
		this.tagBeanList.add(tagBean);
	}
	
	public String getENCODESIZE() {
		return ENCODESIZE;
	}

	public void setENCODESIZE(String eNCODESIZE) {
		ENCODESIZE = eNCODESIZE;
	}

	public String getProtocol() {
		return PROTOCOL;
	}

	public void setProtocol(String protocol) {
		this.PROTOCOL = protocol;
	}

	public String getLocation() {
		return LOCATION;
	}

	public void setLocation(String location) {
		this.LOCATION = location;
	}

	public Long getReceived() {
		if(received==null){
			return (long)0;
		}
		return received;
	}

	public void setReceived(Long received) {
		this.received = received;
	}

	public String getVersion() {
		return VERSION;
	}

	public void setVersion(String version) {
		this.VERSION = version;
	}

	public String getUploadTime() {
		return UPLOAD_TIME;
	}

	public void setUploadTime(String uploadTime) {
		this.UPLOAD_TIME = uploadTime;
	}

	public String getUploadUser() {
		return UPLOAD_USER;
	}

	public void setUploadUser(String uploadUser) {
		this.UPLOAD_USER = uploadUser;
	}

	public String getFileFormat() {
		return FILE_FORMAT;
	}

	public void setFileFormat(String fileFormat) {
		this.FILE_FORMAT = fileFormat;
	}

	public String getContentID() {
		return CONTENT_ID;
	}

	public void setContentID(String contentID) {
		this.CONTENT_ID = contentID;
	}

	public String getFileStatus() {
		return FILE_STATUS;
	}

	public void setFileStatus(String fileStatus) {
		this.FILE_STATUS = fileStatus;
	}

	public String getFileName() {
		return FILE_NAME;
	}

	public void setFileName(String fileName) {
		this.FILE_NAME = fileName;
	}

	public String getOptionType() {
		return OPTION_TYPE;
	}

	public void setOptionType(String optionType) {
		this.OPTION_TYPE = optionType;
	}

	public String getValidPeriod() {
		return VALID_PERIOD;
	}

	public void setValidPeriod(String validPeriod) {
		this.VALID_PERIOD = validPeriod;
	}

	public String getFilesize() {
		return FILE_SIZE;
	}

	public void setFilesize(String filesize) {
		this.FILE_SIZE = filesize;
	}
	
	public String getRealFilesize() {
		return REAL_FILE_SIZE;
	}

	public void setRealFilesize(String filesize) {
		this.REAL_FILE_SIZE = filesize;
	}

	public String getUrl() {
		return URL;
	}

	public void setUrl(String url) {
		this.URL = url;
	}

	public Map<String, String> getOtherAtt() {
		return otherAtt;
	}

	public void setOtherAtt(Map<String, String> otherAtt) {
		this.otherAtt = otherAtt;
	}
	
	public void addOtherAtt(String key, String value) {
		if(otherAtt == null){
			otherAtt = new HashMap<String, String>();
		}
		otherAtt.put(key, value);
	}

	public String getMd5Str() {
		return MD5_STR;
	}

	public void setMd5Str(String md5Str) {
		this.MD5_STR = md5Str;
	}

	public List<AnnotationBean> getAnnoList() {
		return annoList;
	}

	public void setAnnoList(List<AnnotationBean> annoList) {
		this.annoList = annoList;
	}
	
	public void addAnno(AnnotationBean anno) {
		if(annoList == null){
			annoList = new ArrayList<AnnotationBean>();
		}
		this.annoList.add(anno);
	}

	public String getVolume_id() {
		return VOLUME_ID;
	}

	public void setVolume_id(String volumeId) {
		VOLUME_ID = volumeId;
	}

	public String getFileNO() {
		return FILE_NO;
	}

	public void setFileNO(String fileNO) {
		this.FILE_NO = fileNO;
	}

	public String getSaveName() {
		return SAVE_NAME;
	}

	public void setSaveName(String saveName) {
		this.SAVE_NAME = saveName;
	}

	public String getServerID() {
		return SERVER_ID;
	}

	public void setServerID(String serverID) {
		this.SERVER_ID = serverID;
	}

	public String getGroupID() {
		return GROUP_ID;
	}

	public void setGroupID(String groupID) {
		this.GROUP_ID = groupID;
	}

	public String getMigrationStatus() {
		return MIGRATION_STATUS;
	}

	public void setMigrationStatus(String migrationStatus) {
		this.MIGRATION_STATUS = migrationStatus;
	}

	public String getFilePath() {
		return FILE_PATH;
	}

	public void setFilePath(String filePath) {
		this.FILE_PATH = filePath;
	}

	public String getFileReplaceNO() {
		return FILE_REPLACE_NO;
	}

	public void setFileReplaceNO(String fileReplaceNO) {
		this.FILE_REPLACE_NO = fileReplaceNO;
	}

	public String getOrigName() {
		return origName;
	}

	public void setOrigName(String origName) {
		this.origName = origName;
	}
}