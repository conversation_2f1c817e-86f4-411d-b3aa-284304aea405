package com.sunyard.console.tagmanage.dao;

import com.sunyard.console.tagmanage.bean.TagInfoBean;
import com.sunyard.console.tagmanage.bean.TagRelAttTypeDefineBean;
import com.sunyard.console.tagmanage.bean.TagRelAttributeBean;
import com.sunyard.console.tagmanage.bean.TagRelModelBean;

import java.util.List;

/**
 * <p>
 * Title: 标签接口类
 * </p>
 * <p>
 * Description: 定义标签管理中各类数据库操作的方法
 * </p>
 * <p>
 * Copyright: Copyright (c) 2022
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
public interface TagManageDAO {

	public List<TagInfoBean> getTag(String tag_code, String tag_name, int start, int limit);
	
	public List<TagInfoBean> getTag(String tag_code, String tag_name);

	public boolean addTag(TagInfoBean tagBean, List<TagRelAttributeBean> attrslist);

	public boolean updateTag(TagInfoBean tagBean, List<TagRelAttributeBean> attrslist);

	public boolean delTag(String tag_code);

	public List<TagRelAttributeBean> getNotExistAttrsTree(String tag_code);

	public List<TagRelAttributeBean> getExistAttrsTree(String tag_code);

	public List<TagRelAttributeBean> getAttrByID(String attr_ids);
	
	public List<TagRelModelBean> getRelContentObject(String tag_code);

	public List<TagRelModelBean> getUnRelContentObject(String tag_code);
    
	public List<TagInfoBean> getAll_tagInfoBeans();

	public boolean updateTagModels(TagInfoBean tagBean);

	public List<TagRelModelBean> getRelContentObject(String tag_code, int start, int limit);

	public boolean modifyModelRelState(String tag_code, String model_code, String state);

	public List<TagRelAttributeBean> getRelAttr(String tag_code, String tag_state);
    
	public List<TagRelModelBean> getTagRelModelBeans(String tag_code);

	public int checkTagName(String tag_code, String tag_name);

	public int checkTagCode(String tag_code);

	public boolean changeSynState(String tag_code);
	
	public String getTagState(String tag_code);

	public List<TagRelAttTypeDefineBean> getAttributeTypeList();

}
