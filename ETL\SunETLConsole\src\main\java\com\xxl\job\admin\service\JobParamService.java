package com.xxl.job.admin.service;

import com.xxl.job.admin.core.model.JobParam;
import com.xxl.job.core.biz.model.ReturnT;

import java.util.List;
import java.util.Map;

/**
 * 任务参数
 */
public interface JobParamService {
    //获取参数列表
    public Map<String,Object> paramPageList(int start, int length, int jobId, String param_field);

    
    //获取参数信息
    public List<JobParam> getParamById(int jobId);
  

    /**
     * 参数信息
     * @param jobParam
     * @return
     */
    public ReturnT<String> add(JobParam jobParam);

    /**
     * 修改参数配置
     * @param param
     * @return
     */
    public  ReturnT<String> reschedule(JobParam param);

    /**
     * 删除参数
     * @param id
     * @return
     */
    public ReturnT<String> remove(int id);

    /**
     * 参数信息
     * @param jobParams
     * @return
     */
	ReturnT<String> add(List<JobParam> jobParams);
}
