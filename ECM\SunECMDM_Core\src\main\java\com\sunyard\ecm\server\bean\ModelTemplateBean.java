package com.sunyard.ecm.server.bean;

import com.sunyard.ecm.server.bean.converter.AttributeBeanConverter;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamConverter;

import java.util.List;
import java.util.Map;

//内容模型模版

/**
 * <p>
 * Title:xstream 内容模型模版信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@XStreamAlias("ModelTemplateBean")
public class ModelTemplateBean {
	@XStreamAsAttribute
	private String MODEL_NAME; // 中文名MODEL_NAME
	@XStreamAsAttribute
	private String MODEL_CODE; // 英文名MODEL_CODE
	@XStreamAsAttribute
	private String MODEL_DEFINES_TIME; // 业务模型生成时间(模型定义时间)MODEL_DEFINES_TIME
	@XStreamAsAttribute
	private String MODEL_DEFINES_PERSON; // 建立业务模型用户（模型定义人）model_defines_person
	@XStreamAsAttribute
	private String MODEL_TYPE; // 是否为文档（模型分类0为索引，1为文档）
	@XStreamAsAttribute
	private String F_MODEL_ID; // 父对象ID（父模型编号）
	@XStreamAsAttribute
	private String SEPARATE_TABLE_DAYS; // 批次分表规则（模型表分表天数）SEPARATE_TABLE_DAYS
	@XStreamAsAttribute
	private String VERSION_CONTROL; // 是否版本控制VERSION_CONTROL
	@XStreamAsAttribute
	private String DATA_FROM; // 数据来源
	@XStreamAsAttribute
	private String TOKEN_CHECK; // 是否令牌控制（是否安全验证）TOKEN_CHECK（0为不需要，1为需要）
	@XStreamAsAttribute
	private String TEXT_SEARCH; // 是否全文检索TEXT_SEARCH
	@XStreamAsAttribute
	private String CREATION_DATE_COLUMN; // 在线时间CREATION_DATE_COLUMN
	@XStreamAsAttribute
	private String FINISH_DATE_COLUMN; // 离线时间FINISH_DATE_COLUMN
	@XStreamConverter(AttributeBeanConverter.class)
	private Map<String,AttributeBean> objAttr; // 业务模型属性
	@XStreamAsAttribute
	private List<IndexInfoBean> indexInfoBeans; // 业务模型属性
	@XStreamAsAttribute
	private List<FileObjectBean> LIST ;
	@XStreamAsAttribute
	private String ENCODEBYTESIZE;//ENCODEBYTESIZE 需要加密的字节数
	@XStreamAsAttribute
	private String ENCODE_TYPE;//ENCODETYPE 加密类型
	
	public String getENCODEBYTESIZE() {
		return ENCODEBYTESIZE;
	}
	public void setENCODEBYTESIZE(String eNCODEBYTESIZE) {
		ENCODEBYTESIZE = eNCODEBYTESIZE;
	}
	public String getENCODE_TYPE() {
		return ENCODE_TYPE;
	}
	public void setENCODE_TYPE(String eNCODE_TYPE) {
		ENCODE_TYPE = eNCODE_TYPE;
	}
	public String getModel_name() {
		return MODEL_NAME;
	}
	public void setModel_name(String modelName) {
		MODEL_NAME = modelName;
	}
	public String getModel_code() {
		return MODEL_CODE;
	}
	public void setModel_code(String modelCode) {
		MODEL_CODE = modelCode;
	}
	public String getVersion_control() {
		return VERSION_CONTROL;
	}
	public void setVersion_control(String versionControl) {
		VERSION_CONTROL = versionControl;
	}
	public Map<String, AttributeBean> getObjAttr() {
		return objAttr;
	}
	public void setObjAttr(Map<String, AttributeBean> objAttr) {
		this.objAttr = objAttr;
	}
	public List<FileObjectBean> getList() {
		return LIST;
	}
	public void setList(List<FileObjectBean> list) {
		this.LIST = list;
	}
	public String getModel_defines_time() {
		return MODEL_DEFINES_TIME;
	}
	public void setModel_defines_time(String modelDefinesTime) {
		MODEL_DEFINES_TIME = modelDefinesTime;
	}
	public String getModel_defines_person() {
		return MODEL_DEFINES_PERSON;
	}
	public void setModel_defines_person(String modelDelinesPerson) {
		MODEL_DEFINES_PERSON = modelDelinesPerson;
	}
	public String getModel_type() {
		return MODEL_TYPE;
	}
	public void setModel_type(String modelType) {
		MODEL_TYPE = modelType;
	}
	public String getF_model_id() {
		return F_MODEL_ID;
	}
	public void setF_model_id(String fModelId) {
		F_MODEL_ID = fModelId;
	}
	public String getSeparate_table_days() {
		return SEPARATE_TABLE_DAYS;
	}
	public void setSeparate_table_days(String separateTableDays) {
		SEPARATE_TABLE_DAYS = separateTableDays;
	}
	public String getData_from() {
		return DATA_FROM;
	}
	public void setData_from(String dataFrom) {
		DATA_FROM = dataFrom;
	}
	public String getToken_check() {
		return TOKEN_CHECK;
	}
	public void setToken_check(String tokenCheck) {
		TOKEN_CHECK = tokenCheck;
	}
	public String getText_search() {
		return TEXT_SEARCH;
	}
	public void setText_search(String textSearch) {
		TEXT_SEARCH = textSearch;
	}
	public String getCreation_date_column() {
		return CREATION_DATE_COLUMN;
	}
	public void setCreation_date_column(String creationDateColumn) {
		CREATION_DATE_COLUMN = creationDateColumn;
	}
	public String getFinish_date_column() {
		return FINISH_DATE_COLUMN;
	}
	public void setFinish_date_column(String finishDateColumn) {
		FINISH_DATE_COLUMN = finishDateColumn;
	}
	@Override
	public String toString() {
		return "ModelTemplateBean [MODEL_NAME=" + MODEL_NAME + ", MODEL_CODE=" + MODEL_CODE + ", MODEL_DEFINES_TIME="
				+ MODEL_DEFINES_TIME + ", MODEL_DEFINES_PERSON=" + MODEL_DEFINES_PERSON + ", MODEL_TYPE=" + MODEL_TYPE
				+ ", F_MODEL_ID=" + F_MODEL_ID + ", SEPARATE_TABLE_DAYS=" + SEPARATE_TABLE_DAYS + ", VERSION_CONTROL="
				+ VERSION_CONTROL + ", DATA_FROM=" + DATA_FROM + ", TOKEN_CHECK=" + TOKEN_CHECK + ", TEXT_SEARCH="
				+ TEXT_SEARCH + ", CREATION_DATE_COLUMN=" + CREATION_DATE_COLUMN + ", FINISH_DATE_COLUMN="
				+ FINISH_DATE_COLUMN + ", objAttr=" + objAttr + ", indexInfoBeans=" + indexInfoBeans + ", LIST=" + LIST
				+ ", ENCODEBYTESIZE=" + ENCODEBYTESIZE + ", ENCODE_TYPE=" + ENCODE_TYPE + "]";
	}
	public List<IndexInfoBean> getIndexInfoBeans() {
		return indexInfoBeans;
	}
	public void setIndexInfoBeans(List<IndexInfoBean> indexInfoBeans) {
		this.indexInfoBeans = indexInfoBeans;
	}
	

}