package com.sunyard.console.poolmanage.dao;

import com.sunyard.console.poolmanage.bean.ModelRelSDBConfigBean;

import java.util.List;

/**
 * <p>
 * Title: 属性集管理数据库操作接口类
 * </p>
 * <p>
 * Description: 定义业务参数配置数据库操作的方法
 * </p>
 * <p>
 * Copyright: Copyright (c) 2017
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR> @version 1.0
 */
public interface ContentObjParamConfDao {

	/**
	 * 查询业务参数配置列表
	 * 
	 * @param contentName
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<ModelRelSDBConfigBean> contentObjConfList(String contentName,
			int start, int limit);

	/**
	 * 查询业务参数配置列表（所有）
	 * 
	 * @param contentName
	 * @param start
	 * @param limit
	 * @return
	 */
	public List<ModelRelSDBConfigBean> contentObjConfList(String contentName);

	/**
	 * 业务参数配置(新增)
	 * 
	 * @param modelRelSDBConfigBean
	 * @return
	 */
	public boolean contentObjConfAdd(ModelRelSDBConfigBean modelRelSDBConfigBean);

	/**
	 * 业务参数配置(修改)
	 * 
	 * @param modelRelSDBConfigBean
	 * @return
	 */
	public boolean contentObjConfUpdate(
			ModelRelSDBConfigBean modelRelSDBConfigBean);

}
