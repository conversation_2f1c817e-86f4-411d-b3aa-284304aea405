package com.sunyard.util;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.ref.SoftReference;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.ConcurrentLinkedQueue;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.sunyard.common.Configuration;


public class EncryptUtil {
	private Key key = null;
	private Cipher cipher;
	public final static Logger logger = LoggerFactory.getLogger(EncryptUtil.class);
	private static ConcurrentLinkedQueue<SoftReference<EncryptUtil>> pool=new ConcurrentLinkedQueue<SoftReference<EncryptUtil>>();
	private SoftReference<EncryptUtil> ref;


	/**
	 * 获得单例
	 * */
	public  static EncryptUtil getInstance(String keyFile) {
		EncryptUtil imp = null;
		SoftReference<EncryptUtil> pref = pool.poll();
		if(pref==null){
			imp=new EncryptUtil(keyFile);
			imp.ref=new SoftReference<EncryptUtil>(imp);
		}else{
			imp=pref.get();
			if(imp==null){
			    imp=new EncryptUtil(keyFile);
				pref=new SoftReference<EncryptUtil>(imp);
				imp.ref=pref;
			}
		}
 
		return imp;
	}

	private EncryptUtil(String keyFile) {
		if (key == null) {
			SecretKeySpec keyspec = readInformation(keyFile);
			key = keyspec;
		}
		try {
			String algorithm = Configuration.get("algorithm");
			cipher = Cipher.getInstance(algorithm);
		} catch (NoSuchAlgorithmException e) {
			logger.error("实例化对称密钥对象失败：" + e.getMessage());
			logger.error("NoSuchAlgorithmException：", e);
		} catch (NoSuchPaddingException e) {
			logger.error("实例化加密解密对象失败：" + e.getMessage());
			logger.error("NoSuchPaddingException：", e);
		}
	}

	/**
	 * 加密字符串
	 * 
	 * @param str
	 * @return
	 */
	public  String Encrypt(String str) {
		String encStr = "";
		byte[] encryptArray = new byte[0];
		try {
			cipher.init(1, key);
			byte[] arrayStr = str.getBytes("UTF-8");

			encryptArray = cipher.doFinal(arrayStr);
			encStr = this.byteArrayToString(encryptArray);
		}  catch (Exception e) {
			logger.error("出错:"+str+"]",e);
		}
		pool.offer(this.ref);
		return encStr;
	}

	/**
	 * 解密字符串
	 * 
	 * @param obj
	 * @return
	 */
	public String deEncrypt(String obj) {
		byte[] dencryptArray = new byte[0];
		try {
			String value = new String(obj.getBytes("UTF-8"));
			byte[] dencStr = stringToByteArray(value);
			cipher.init(2, key);

			dencryptArray = cipher.doFinal(dencStr);
		}  catch (Exception e) {
			logger.error("出错["+obj+"]");
		}
		pool.offer(this.ref);
		return new String(dencryptArray);

	}

	public byte[] stringToByteArray(String serializerStr) {
		Base64 decoder = new Base64();
		byte[] strByte = (byte[]) null;
		strByte = decoder.decode(serializerStr.getBytes());
		return strByte;
	}

	public String byteArrayToString(byte[] stream) {
		Base64 enc = new Base64();
		return new String(enc.encode(stream));
	}

	public final SecretKeySpec readInformation(String keyFile) {
		SecretKeySpec keyGenerator = null;
		ObjectInputStream ois =null;
		try {
			 ois = new ObjectInputStream(EncryptUtil.class.getClassLoader().getResourceAsStream(keyFile));
			if (ois != null) {
				keyGenerator = (SecretKeySpec) ois.readObject();
			}
		} catch (ClassNotFoundException e) {
			logger.error("找不到类：" + e.getMessage());
			logger.error("ClassNotFoundException：", e);
		} catch (FileNotFoundException e) {
			logger.error("找不到文件：" + e.getMessage());
			logger.error("FileNotFoundException：", e);
		} catch (IOException e) {
			logger.error("IO流异常：" + e.getMessage());
			logger.error("IOException：", e);
		}finally{
			if(ois!=null){
				try {
					ois.close();
				} catch (IOException e) {
					logger.error("关闭出错");
				}
			}
		}
		return keyGenerator;
	}

	public static void writeInformation(String allpath, Object obj) throws IOException {
		FileOutputStream fos = null;
		ObjectOutputStream oos = null;
		try {
			fos = new FileOutputStream(allpath);
			oos = new ObjectOutputStream(fos);
			oos.writeObject(obj);
			oos.flush();
		} finally {
			if (fos != null) {
				fos.close();
			}
			if (oos != null) {
				oos.close();
			}

		}
	}

//	public static void main(String strs[]) {
//		// EncryptImp encryptImp = new EncryptImp("Key.license");
//		// EncryptImp.getInstance("Key.license").Encrypt("abc");
////		System.out.println(EncryptUtil.getInstance("UK.dvc").Encrypt("abc"));
//		System.out.println(EncryptUtil.getInstance("UK.dvc").deEncrypt("5VroJVmANCNZca4XjSkxUFwoXjs7g0XNBuROZst+nA9ldcVdZKs7Na5a3rMLXtyaoWXTGHhxLAtM&#xd;lkaQ1XWlsMLoDH0xXTPUTt/wZGC/FDiqUJX5Qa8ta1lFmVjgUwhP/GOTZ6AiNELCqJPzxreHN7pi&#xd;yxSLoTWP3Pw6oP0q2CDX/D7rm/VoSyvn7zH+rSDWVER+LrjzCVTBs9685sJRuG3CtYvYEl8VMmWj&#xd;oiUmxWU="));
////		System.out.println(EncryptUtil.getInstance("UK1.dvc").Encrypt("abc"));
////		System.out.println(EncryptUtil.getInstance("UK1.dvc").deEncrypt("DqYDG+Ys8Nc="));
//	}
}