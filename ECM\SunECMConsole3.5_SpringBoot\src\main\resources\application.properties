server.port=8081
server.servlet.context-path=/SunECMConsole
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
#spring.datasource.url=**********************************************************************************************************************************************
#spring.datasource.username=d2367BebGuUsVqUJW0Cz2Q==
#spring.datasource.password=d2367BebGuUsVqUJW0Cz2Q==
#spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver

#spring.datasource.url=*******************************************************************************************************************************************
#spring.datasource.username=sunecm
#spring.datasource.password=sunecm
#spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
#spring.datasource.url=jdbc:dm://172.1.3.130:5236
#spring.datasource.username=WANGY
#spring.datasource.password=Sunyard@123
#spring.datasource.driverClassName=dm.jdbc.driver.DmDriver
#spring.datasource.isEncrypt=true
#spring.datasource.url=*************************************
#spring.datasource.username=HRISI7+zIGo9z/hYW+sjLw==
#spring.datasource.password=1p+xzhh3ZS6JYbm+B5HUwA==
#spring.datasource.driverClassName=oracle.jdbc.driver.OracleDriver

spring.datasource.isEncrypt=false
spring.datasource.username=sunyard
spring.datasource.password=Sa123456
spring.datasource.url=********************************************
spring.datasource.driverClassName=org.postgresql.Driver

#spring.datasource.url=jdbc:dm://172.1.1.220:5236
#spring.datasource.username=SYSDBA
#spring.datasource.password=SYSDBA
#oracle
#spring.datasource.url=*************************************
#spring.datasource.username=sunecm34
#spring.datasource.password=sunecm
#spring.datasource.driverClassName=oracle.jdbc.driver.OracleDriver
#  com.mysql.cj.jdbc.Driver
spring.datasource.initialSize=5
spring.datasource.minIdle=5
spring.datasource.maxActive=20
spring.datasource.maxWait=60000
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.minEvictableIdleTimeMillis=300000
spring.datasource.validationQuery=SELECT 1 FROM DUAL
spring.datasource.testWhileIdle=true
spring.datasource.testOnBorrow=false
spring.datasource.testOnReturn=false
spring.datasource.poolPreparedStatements=true
spring.datasource.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.filters=stat
spring.datasource.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
pageTool.Type=postgrePageTool
monitor=false
#Eureka ????
eureka.client.service-url.defaultZone:http://127.0.0.1:8100/eureka/
spring.application.name=SunECMConsole
#???????????????
jasypt.encryptor.password=123456
dubboIsOn=false
#spring.cloud.nacos.discovery.server-addr=***********:8848
#spring.cloud.nacos.discovery.username=nacos
#spring.cloud.nacos.discovery.password=nacos
#spring.cloud.nacos.discovery.namespace=public
#spring.cloud.nacos.discovery.ip=************
#spring.cloud.nacos.discovery.group=yx



