package com.sunyard.util.ecm3_1;

import java.io.IOException;
import java.net.Socket;
import java.net.UnknownHostException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.sunyard.ecm.server.bean.LifeCycleStrategyBean;
import com.sunyard.ecm.server.bean.NodeInfo;
import com.sunyard.ecm.server.cache.LazySingleton;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.client.conn.HttpConn;
import com.sunyard.exception.SunECMException;
import com.sunyard.exception.SunECMExceptionStatus;
import com.sunyard.initialization.LoadConfigFile;

/**
 * 
 * 对于DM在内容服务器组中的消息获取等配置支持 DM组的工具类
 * 
 * <AUTHOR>
 * 
 */
public class DMInGroupUtil {
	private  final static  Logger log = LoggerFactory.getLogger(DMInGroupUtil.class);
	// 当前内容存储服务器迁移所配置的传输协议
	private final static String agreement = LazySingleton.getInstance().load.getConfigBean().getAgreement();

	/**
	 * 
	 * 通过组名称获取内容存储服务器节点信息
	 * 
	 * @param groupID
	 * @return
	 * @throws SunECMException
	 */
	public static NodeInfo getNodeInfoByGroupID(String groupID) throws SunECMException {
		// log.info(
		// "getNodeInfoByGroupID-->通过组名称获取内容存储服务器节点信息");
		// log.debug(
		// "getNodeInfoByGroupID-->开始获取内容服务器组["
		// + groupID + "]下一个可连接的内容服务器的信息...");
		if (groupID == null) { // 找不到上级服务器,返回null
			return null;
		}
		// 从内存中获取所有服务器信息
		List<NodeInfo> abandonedNodes = new ArrayList<NodeInfo>();
		// 不断循环获取目标内容存储服务器组下可用的服务器
		NodeInfo resultServer = getGroupNodeList(groupID, abandonedNodes);
		if (resultServer == null) {
			log.warn("--DMInGroupUtil-->getNodeInfoByGroupID-->服务器组名称传入[" + groupID
					+ "]不正确,或统一接入未从Console接收到该服务器组的配置信息,或所查询的內容存储服务器组[" + groupID + "]下所有服务器均已停用");
			throw new SunECMException(SunECMExceptionStatus.DMSNAME_ERROR,
					"--DMInGroupUtil-->getNodeInfoByGroupID-->服务器组名称不正确,统一接入未从Console接收到该[" + groupID
							+ "]服务器组的配置信息,所查詢的內容存儲服务器组所有服务器均已停用");
		}
		return resultServer;
	}

	/**
	 * 获取dmsName内容服务器组名下的所有DM信息
	 * 
	 * @param dmsName        服务器组
	 * @param abandonedNodes 连接不上的内容服务器列表
	 * @return
	 * @throws SunECMException
	 */
	private static NodeInfo getGroupNodeList(String groupID, List<NodeInfo> abandonedNodeList) throws SunECMException {

		// 从内存中获取所有服务器信息,Map对象key为serverID
		Map<String, NodeInfo> totalNode = LazySingleton.getInstance().allNodeInfoTable.getAllNodeInfoTable();
		// log.debug( "ECM管理平台内容存储服务器总个数为[" + totalNode.size()
		// + "],从中找到groupID[" + groupID + "]下可连接的内容服务器");
		List<NodeInfo> servers = new ArrayList<NodeInfo>();
		// 将连接不通的内容存储服务器从临时列表中移除
		Map<String, NodeInfo> map = removeAbandonedNode(totalNode, abandonedNodeList);
		Set<String> set = map.keySet();
		int total = 0;
		boolean flag = true; // 校验服务器组名称是否正确
		// 轮询匹配的内容存储服务器信息
		for (Iterator<String> iterator = set.iterator(); iterator.hasNext();) {
			String serverID = (String) iterator.next();
			NodeInfo server = map.get(serverID);
			if (server.getGroup_name() != null && groupID.equals(server.getGroup_id())
					&& "1".equals(server.getState())) {
				servers.add(server);
				total += Integer.valueOf(server.getWetght());
				flag = false;
			}
		}
		if (flag) {
			throw new SunECMException(SunECMExceptionStatus.DMSNAME_ERROR,
					"服务器组名称不正确,未从Console接收到该服务器组的配置信息,所查詢的內容存儲服务器组所有服务器均已停用");
		}
		// 根据随机数和权重均衡负载
		double start = 0;
		double end = 0;
		double randomNumber =new SecureRandom().nextDouble() * total;
		NodeInfo resultServer = null;
		// 计算负载均衡
		for (int i = 0; i < servers.size(); i++) {
			end += Double.valueOf(servers.get(i).getWetght());
			if (randomNumber >= start && randomNumber <= end) {
				resultServer = servers.get(i);
				break;
			}
			start = end;
		}
		if (resultServer != null) {
			log.debug("找到groupID[" + groupID + "]下可连接的内容服务器[" + resultServer.getServer_name() + "]");
		} else {
			log.debug("未找到groupID[" + groupID + "]下可连接的内容服务器");
		}
		return resultServer;
	}

	/**
	 * 将不能连通的内容存储服务器从临时列表中移除
	 * 
	 * @param totalNode
	 * @param abandonedNodeList
	 * @return
	 */
	private static Map<String, NodeInfo> removeAbandonedNode(Map<String, NodeInfo> totalNode,
			List<NodeInfo> abandonedNodeList) {
		for (NodeInfo nodeInfo : abandonedNodeList) {
			totalNode.remove(nodeInfo.getServer_id());
		}
		return totalNode;
	}

	/**
	 * 校验DM是否可以连通
	 * 
	 * @param node 内容存储服务器信息
	 * @return
	 * @throws SunECMException
	 */
	public static boolean isDMOnLive(NodeInfo node) throws SunECMException {
		log.info("--DMInGroupUtil-->isDMOnLive-->校验DM是否可以连通");
		if ("http".equals(agreement)) {
			return isHttpLive(node);
		} else {
			return isSocketLive(node);
		}
	}

	/**
	 * 校验DM是否可以Http连通
	 * 
	 * @param node 内容存储服务器信息
	 * @return
	 * @throws SunECMException
	 */
	public static boolean isHttpLive(NodeInfo node) throws SunECMException {
		log.debug("DM测试连接目标内容存储器[" + node.getServer_name() + "]的http连接,IP为[" + node.getServer_ip() + "],http端口为["
				+ node.getHttp_port() + "]");
		final int connectretrytimes = 5;
		String host = node.getServer_ip();
		int port = Integer.valueOf(node.getHttp_port());
		// 重复连接CONNECTRETRYTIMES
		for (int i = 0; i < connectretrytimes; i++) {
			if (newhttpHost(host, port)) {
				return true;
			}
			// 等待200毫秒再下一次连接
			try {
				Thread.sleep(200);
			} catch (InterruptedException e) {
				log.debug("当前连接次数：" + (i + 1));
			}
			if (i == connectretrytimes - 1) {
				throw new SunECMException(SunECMExceptionStatus.CLIENT_CONNECT_TO_SERVER_ERROR,
						"HTTP连接主机：" + host + " 端口:" + port + "失败");
			}
		}
		return false;
	}

	/**
	 * 校验DM是否可以socket连通
	 * 
	 * @param node 内容存储服务器信息
	 * @return
	 * @throws SunECMException
	 */
	public static boolean isSocketLive(NodeInfo node) throws SunECMException {
		log.debug("DM测试连接目标内容存储器[" + node.getServer_name() + "]的socket连接,IP为[" + node.getServer_ip() + "],socket端口为["
				+ node.getSocket_port() + "]");
		// 尝试重复连接的次数,默认为5次
		final int connectretrytimes = 5;
		String host = node.getServer_ip();
		int port = Integer.valueOf(node.getSocket_port());
		// 重复连接CONNECTRETRYTIMES
		for (int i = 0; i < connectretrytimes; i++) {
			if (newSocketHost(host, port)) {
				return true;
			}
			// 等待200毫秒再尝试下一次连接
			try {
				Thread.sleep(200);
			} catch (InterruptedException e) {
				log.debug("当前连接次数：" + (i + 1));
			}
			if (i == connectretrytimes - 1) {
				throw new SunECMException(SunECMExceptionStatus.CLIENT_CONNECT_TO_SERVER_ERROR,
						"SOCKET连接主机：" + host + " 端口:" + port + "失败");
			}
		}
		return false;
	}

	/**
	 * socekt建立连接
	 * 
	 * @param ip
	 * @param socketPort
	 * @return
	 */
	private static boolean newSocketHost(String ip, int socketPort) {
		log.debug("尝试向目标服务器建立socekt连接,IP地址为[" + ip + "],socket端口为[" + socketPort + "]");
		Socket socket = null;
		try {
			socket = new Socket(ip, socketPort);
		} catch (UnknownHostException e) {
			log.warn("找不到目标服务器,建立socekt连接失败,IP地址为[" + ip + "],socket端口为[" + socketPort + "]");
			return false;
		} catch (IOException e) {
			log.warn("IO异常,建立socekt连接失败,IP地址为[" + ip + "],socket端口为[" + socketPort + "]");
			return false;
		}
		// 如果连接成功则关闭socket
		try {
			socket.close();
		} catch (IOException e) {
			log.error("IO异常,DM测试socekt连接成功后关闭连接失败,IP地址为[" + ip + "],socket端口为[" + socketPort + "]");
		}
		return true;
	}

	/**
	 * HTTP建立连接
	 * 
	 * @return
	 */
	private static boolean newhttpHost(String ip, int httpPort) {
		log.debug("尝试向目标服务器建立http连接,IP地址为[" + ip + "],http端口为[" + httpPort + "]");
		// 获取服务器工程名,配置文件中读取的
		String serverName = LoadConfigFile.getInstance().getConfigBean().getLocalName();
		int connTimeOut = 30000;
		int reqTimeOut = 30000;
		try {
			String url = "http://" + ip + ":" + httpPort + "/" + serverName + "/servlet/httpReqDispacher";
			log.debug("HTTP连接地址：[" + url + "]");
			new HttpConn(url, connTimeOut, reqTimeOut);
		} catch (Exception e) {
			log.warn("建立http连接,IP地址为[" + ip + "],http端口为[" + httpPort + "],连接失败,异常信息为" + e.toString());
			return false;
		}
		log.debug("建立http连接,IP地址为[" + ip + "],http端口为[" + httpPort + "],连接成功...");
		return true;
	}

	/**
	 * 
	 * 根据内容模型获取生命周期配置的上级服务器节点ID
	 * 
	 * @param modeCode 内容模型名
	 * @return
	 * @throws SunECMException
	 */
	public static String getSuperGroupNodeID(String modeCode) {
		log.info("--DMInGroupUtil-->getSuperGroupNodeID-->据内容模型获取生命周期配置的上级服务器节点ID");
		NodeInfo localNode = LazySingleton.getInstance().load.getNodeInfoBean();
		Map<String, LifeCycleStrategyBean> map = LazySingleton.getInstance().lifeCycleStrategy.getLifeMap();
		log.debug("获取[" + modeCode + "]的迁移任务,当前节点为[" + localNode.getGroup_id() + "]");
		Set<String> keyset = map.keySet();
		for (String key : keyset) {
			LifeCycleStrategyBean lifeCycle = map.get(key);
			if (localNode.getGroup_id().equals(lifeCycle.getGroup_id()) && lifeCycle.getModel_code().equals(modeCode)
					&& "1".equals(lifeCycle.getTask_state())
					&& ("403".equals(lifeCycle.getTask_no()) || "2".equals(lifeCycle.getTask_no()))) {
				String params = lifeCycle.getParameters();
				Map<String, Object> paramMap = getParameters(params);
				log.debug("內容模型[" + modeCode + "]的迁移任务,参数配置为[" + lifeCycle.getParameters() + "]");
				String superGroupID = String.valueOf(paramMap.get("SUPER_GROUPID"));
				log.info("上级服务器组为：" + superGroupID);
				return superGroupID;
			}
		}
		log.warn("未从CONSOLE接收到该[" + modeCode + "]内容模型的迁移任务信息,或者迁移任务已停用,或者本节点[" + localNode.getServer_name() + "]["
				+ localNode.getGroup_name() + "]已是迁移的最高链路节点");
		return null;
	}

	/**
	 * 
	 * 根据内容模型获取生命周期配置的上级服务器节点ID
	 * 
	 * @param modeCode 内容模型名
	 * @return
	 * @throws SunECMException
	 */
	public static boolean hasLowerGroupNodeID(String modeCode) {
		log.info("--DMInGroupUtil-->getSuperGroupNodeID-->据内容模型获取生命周期配置的上级服务器节点ID");
		NodeInfo localNode = LazySingleton.getInstance().load.getNodeInfoBean();
		Map<String, LifeCycleStrategyBean> map = LazySingleton.getInstance().lifeCycleStrategy.getLifeMap();
		log.debug("获取[" + modeCode + "]的迁移任务,当前节点为[" + localNode.getGroup_id() + "]");
		Set<String> keyset = map.keySet();
		for (String key : keyset) {
			LifeCycleStrategyBean lifeCycle = map.get(key);
			if (lifeCycle.getModel_code().equals(modeCode) && "1".equals(lifeCycle.getTask_state())
					&& "2".equals(lifeCycle.getTask_no())) {
				String params = lifeCycle.getParameters();
				Map<String, Object> paramMap = getParameters(params);
				String superGroupID = String.valueOf(paramMap.get("SUPER_GROUPID"));
				if (localNode.getGroup_id().equals(superGroupID)) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * 将生命周期的参数转换成Map
	 * 
	 * @param parameters
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private static Map<String, Object> getParameters(String parameters) {
		if (parameters == null || parameters.length() == 0 || "null".equals(parameters)) {
			return new HashMap();
		}
		String[] paras = parameters.split(";");
		Map<String, Object> map = new HashMap<String, Object>();
		for (int i = 0; i < paras.length; i++) {
			String one = paras[i];
			String[] para = one.split("=");
			if (para.length != 2) {
				log.error("BAD PARAMETER :[" + one + "]");
				continue;
			}
			map.put(para[0].trim(), para[1].trim());
		}
		return map;
	}
}