package com.sunyard.util.pageTool;
/**
 * <p>Title: PostgreSQL数据库查询分页</p>
 * <p>Description: PostgreSQL数据库查询分页</p>
 * <p>Copyright: Copyright (c) 2021</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class PostgreSQLPageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
		int offset=start-1;
		if(offset<0){
			offset=0;
		}

		return sql+" limit "+limit+ " offset "+ offset;
	}
	public String getTopSql(String sql, int top) {
		return sql + " limit " + top;
	}

	public String getRandSql(String sql, int top) {
		return sql + " order by random() limit " + top;
	}

	public String getMaxVersionAndGroupInDMDB() {
		return "POSTGRESQL_getMaxVersionAndGroupInDMDB";
	}

	public String getDistinctRandSql(String sql, int top) {
		return getRandSql(sql,top);
	}
	
}
