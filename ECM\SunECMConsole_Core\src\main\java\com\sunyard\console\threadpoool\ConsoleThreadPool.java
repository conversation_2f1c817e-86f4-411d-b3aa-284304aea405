package com.sunyard.console.threadpoool;

import com.sunyard.console.common.config.LoadConfigFile;
import com.sunyard.console.common.database.DataBaseUtil;
import com.sunyard.console.common.util.DateUtil;
import com.sunyard.console.common.util.SpringUtil;
import com.sunyard.ws.client.WSAccessClient;
import com.sunyard.ws.internalapi.SunEcmAccess;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.support.incrementer.DataFieldMaxValueIncrementer;

import java.sql.SQLException;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 当console下发配置信息过多时，开启 海量http线程导致web容器瘫痪，现将 下发线程统一管理
 * 
 * <AUTHOR>
 *
 */
public class ConsoleThreadPool {
	private int maxNumPoolSize;
	private ExecutorService threadPool;
	private static ConsoleThreadPool c = new ConsoleThreadPool();
	private ConcurrentSkipListSet<String> consumerSet;
	private ConcurrentSkipListSet<String> producerSet;
	private static Logger log = LoggerFactory.getLogger(ConsoleThreadPool.class);
	private static 	DataFieldMaxValueIncrementer incrementer =(DataFieldMaxValueIncrementer) SpringUtil.getSpringBean("configInfoSynchroID");
	private ExecutorService getCustomerThreadPool() {
		return threadPool;
	}

	private ConcurrentSkipListSet<String> getProducerSet() {
		return producerSet;
	}

	private ConsoleThreadPool() {
		maxNumPoolSize = LoadConfigFile.getConfigBean().getMaxIssueThreadSize();
		if (threadPool == null) {
			threadPool = getExecutors(maxNumPoolSize);
		}
		consumerSet = new ConcurrentSkipListSet<String>();
		producerSet = new ConcurrentSkipListSet<String>();
		Thread t = new Thread(new IssueInfoSetDemo(consumerSet, producerSet));
		t.setDaemon(true);
		t.start();

	}

	private ExecutorService getExecutors(int count) {
		return new ConsoleThreadPoolExecutor(0, count, 10L, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());
	}

	/**
	 * 将需要下发的机器下发加入待下发列表
	 * 
	 * @param issueInfoBean
	 */

	public static void addIssueInfoToSet(String url) {
		c.getProducerSet().add(url);
	}

	/**
	 * 下发集合监控线程
	 * 
	 * <AUTHOR>
	 *
	 */
	private  class IssueInfoSetDemo implements Runnable {
		private ConcurrentSkipListSet<String> consumerSet;
		private ConcurrentSkipListSet<String> producerSet;

		private IssueInfoSetDemo(ConcurrentSkipListSet<String> consumerSet, ConcurrentSkipListSet<String> producerSet) {
			super();
			this.consumerSet = consumerSet;
			this.producerSet = producerSet;
		}

		public void run() {
			Thread.currentThread().setName("IssueConfDemo");
			while (true) {
				if (consumerSet.isEmpty()) {
					try {
						IssueUtils.lock.lock();
						consumerSet.addAll(producerSet);
						producerSet.clear();
						if (consumerSet.isEmpty())
							IssueUtils.condition.await();
						IssueUtils.lock.unlock();
					} catch (InterruptedException e) {
						log.error(e.getMessage(), e);
						break;
					}
					continue;
				} else {
					log.info("下发列表不为空，开始下发配置信息");
					String url = consumerSet.first();
					consumerSet.remove(url);
					c.getCustomerThreadPool().submit(new IssueThread(url));
				}
			}
		}

	}

	/**
	 * 下发线程
	 * 
	 * <AUTHOR>
	 *
	 */
	private  class IssueThread implements Runnable {
		private String url;
		private long timeout = 300000;

		private IssueThread(String url) {
			this.url = url;
		}

		public void run() {
			IssueConf();
		}

		private void IssueConf() {
			log.debug("开始下发配置,url:{}",url);
			SunEcmAccess wsInterface = new WSAccessClient().getAccessClient(url, timeout);
			log.debug("获取客户端结束");
			boolean flag=false;
			try {
				boolean b = wsInterface.resetConf();
				if (b) {
					log.info("下发配置成功,url:{}", url);
					flag=true;
				} else {
					log.warn("下发配置失败,url:{}", url);
				}
			} catch (Exception e) {
				log.warn(e.getMessage(), e);
				log.warn("下发配置失败,url:{}", url);
			}
			try {
				StringBuffer sql = new StringBuffer();

				sql.append(
						"INSERT INTO CONFIG_INFO_SYNCHRO(CONFIG_ID,CONFIG_TABLE,CONFIG_CODE,CONFIG_TYPE,SYNCHRO_STATUS,NODE_ID,REMARK,MODIFY_TYPE,SYNCHRO_DATE,TARGET_SERVER_TYPE)");
				sql.append("VALUES(?,?,?,?,?,?,?,?,?,?)");
				Object[] params = new Object[10];
				params[0] = ConsoleThreadPool.incrementer.nextIntValue();
				params[1] = "";
				params[2] = ("");
				params[3] = ("9");
				String synchro_status = "0";
				if (flag) {
					synchro_status = "1";
				}
				params[4] = synchro_status;
				String infos = url.split("//")[1];
				String[] infoArray = infos.split(":");
				String ip = infoArray[0];
				String port = infoArray[1].split("/")[0];
				String appName = infoArray[1].split("/")[1];
				String tableName = "unity_access_server".toUpperCase();
				Integer target_server_type = new Integer(2);
				if (appName.equals(LoadConfigFile.getConfigBean().getServer_DM_Name())) {
					tableName = "content_server_info".toUpperCase();
					target_server_type = new Integer(1);
				}
				StringBuffer nodeIdSql = new StringBuffer();
				nodeIdSql.append("select SERVER_ID from ").append(tableName).append("").append(" WHERE SERVER_IP= '")
						.append(ip).append("'").append("AND HTTP_PORT= '").append(port).append("'");
				int nodeId = DataBaseUtil.SUNECM.queryInt(nodeIdSql.toString());
				params[5] = nodeId;
				params[6] = "下发所有配置信息";
				params[7] = "1";
				params[8] = DateUtil.getCurrentDateTime();
				params[9] = target_server_type;
				DataBaseUtil.SUNECM.update(sql.toString(), params);
			} catch (SQLException e) {
				log.warn(e.getMessage(), e);
			}
			
		}
	}
}
