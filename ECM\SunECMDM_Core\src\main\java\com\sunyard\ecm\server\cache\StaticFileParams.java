package com.sunyard.ecm.server.cache;

import com.sunyard.ecm.server.bean.BatchBean;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.exception.SunECMException;
import com.sunyard.initialization.ConfigParams;
import com.sunyard.util.FileUtil;
import com.sunyard.ws.utils.XMLUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 存放文件临时路径的静态变量
 * 
 * <AUTHOR>
 */
public class StaticFileParams {
	private final static  Logger log = LoggerFactory.getLogger(StaticFileParams.class);

	private volatile static StaticFileParams unique;

	private StaticFileParams() {
	};

	public static StaticFileParams getInstance() {
		if (unique == null) {
			synchronized (StaticFileParams.class) {
				if (unique == null) {
					unique = new StaticFileParams();
				}
			}
		}
		return unique;
	}

	private Map<String, List<FileBean>> fileMap = new HashMap<String, List<FileBean>>();

	/**
	 * 添加文件
	 * 
	 * @param contentID
	 *            内容ID
	 * @param fileOldPath
	 *            文件在客户端上的路径
	 * @param fileID
	 *            文件ID
	 */
	public void addFile(String contentID, FileBean fileBean) {
		if (fileMap.get(contentID) == null) {
			List<FileBean> fileBeans = new ArrayList<FileBean>();
			fileBeans.add(fileBean);
			fileMap.put(contentID, fileBeans);
			return;
		}
		fileMap.get(contentID).add(fileBean);
	}

	/**
	 * 删除文件
	 * 
	 * @param contentID
	 *            内容ID
	 * @param fileName
	 *            文件在客户端上的路径和名称
	 * @param fileID
	 *            文件ID
	 * @throws SunECMException
	 */
	public void handleBatch(String contentID) throws SunECMException {
		if (contentID != null) {
			if (!contentID.equals("")) {
				log.debug("handleBatch process --> the contentID is "
						+ contentID);
				BatchBean batchBean = StaticBatchParams.getInstance().getBatch(
						contentID);
				if(batchBean == null) {
					log.debug("dmserver.singleton.StaticFileParams --> handleBatch: 内存中没有批次" + contentID + "的索引信息...");
					return ;
				}
				if (batchBean.isBreakPoint()) {
					/**
					 * 如果要求断点续传，写入断点续传的记录文件中。 最后将批次记录从内存中删除(包括文件信息)
					 * 断点续传文件路径为批次路径，文件名为contentID+"_BREAKPOINT.xml"
					 */
					List<FileBean> files = fileMap.get(contentID);
					if(files == null) {
						log.debug("dmserver.singleton.StaticFileParams --> handleBatch: 内存中没有批次" + contentID + "的文件信息...");
					} else {
						String filePath = ConfigParams.getRelativePath(batchBean
								.getModelCode(), contentID);
						FileUtil.writeXML(XMLUtil.list2Xml(files), filePath,
								contentID + "_BREAKPOINT");
					}
					delteFilesMsg(contentID);
					StaticBatchParams.getInstance().removeBatch(contentID);
				} else {
					/**
					 * 如果没有设置断点续传，则删除整个批次的文件。
					 */
					removeBatchFiles(contentID);
				}
			}
		}
	}

	/**
	 * 获取保存在服务器缓存中的文件信息列表
	 * 
	 * @param contentID
	 *            内容ID
	 * @return
	 */
	public List<FileBean> getFiles(String contentID) {
		return fileMap.get(contentID);
	}
	
	/**
	 * 删除批次
	 * 
	 * @param contentID
	 *            内容ID
	 * @throws SunECMException
	 */
	public void delteFilesMsg(String contentID){
		log.debug("dmserver.singleton.StaticFileParams --> delteFilesMsg: remove 内容ID contentID is " + contentID + "的文件信息...");
		fileMap.remove(contentID);
	}

	/**
	 * 删除批次
	 * 
	 * @param contentID
	 *            内容ID
	 * @throws SunECMException
	 */
	public void removeBatchFiles(String contentID) throws SunECMException {
		List<FileBean> files = fileMap.get(contentID);
		if(files != null) {
			for (FileBean fileBean : files) {
				File file = new File(fileBean.getFileName());
				if (file.exists()) {
					file.delete();
				}
			}
		}
		delteFilesMsg(contentID);
		StaticBatchParams.getInstance().removeBatch(contentID);
	}
}