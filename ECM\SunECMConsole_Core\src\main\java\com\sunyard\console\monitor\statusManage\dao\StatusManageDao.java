package com.sunyard.console.monitor.statusManage.dao;

import com.sunyard.console.monitor.statusManage.bean.ConfigInfoSynchroBean;

import java.util.List;

/**
 * <p>Title: 同步状态管理数据操作接口类</p>
 * <p>Description: 定义同步状态管理中各类数据操作的方法</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */

public interface StatusManageDao {
	
	/**
	 * 查询状同步态信息(节选范围)
	 * @param start 开始
	 * @param limit 结束
	 * @return
	 */
	public List<ConfigInfoSynchroBean> getNodeStatusList(int start, int limit);
	
	/**
	 * 查询同步状态信息(全部)
	 * @return
	 */
	public List<ConfigInfoSynchroBean> getAllNodeStatusList();
	
	/**
	 * 删除同步信息
	 * @param configIDs
	 */
	public void delSyncStatusInfo(String configIDs);
	
	/**
	 * 新增同步信息
	 * @param nodeStatusBean 
	 */
	public void addSyncStatusInfo(ConfigInfoSynchroBean nodeStatusBean);
	
	/**
	 * 查询符合条件的同步状态信息
	 * @param configID	配置ID
	 * @return
	 */
	public ConfigInfoSynchroBean getSingleServerStatus(String configID);
	
	/**
	 * 更改记录的同步状态
	 * @param configID 配置ID
	 */
	public void updateSyncStatus(String configID);
}
