package com.sunyard.util;

import java.io.Serializable;
import java.rmi.dgc.VMID;

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: </p>
 * <AUTHOR> attributable
 * @version 1.0
 */

public class NumberGenerator implements Serializable{
  public NumberGenerator() {
  }

  /**
   * 产生一个唯一的String类型的序列号
   * @return
   */
  public static String getUniqueNumber()
     {
         return (new VMID()).toString();
     }

}