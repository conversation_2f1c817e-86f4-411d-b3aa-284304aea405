package com.sunyard.ecm.server.bean;

import com.sunyard.ecm.server.bean.converter.AttributeBeanConverter;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamConverter;

import java.util.Map;

/**
 * <p>
 * Title:xstream 内容模型模版信息
 * </p>
 * <p>
 * Description: 
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 3.1
 */
@XStreamAlias("FileObjectBean")
public class FileObjectBean {
	@XStreamAsAttribute
	private String  MODLE_CODE ;// 英文名MODEL_CODE
	@XStreamAsAttribute
	private String VERSION_CONTROL; // 是否版本控制VERSION_CONTROL
	@XStreamConverter(AttributeBeanConverter.class)
	private Map<String,AttributeBean> fileAttr; // 文档部件属性
	public String getModle_code() {
		return MODLE_CODE;
	}
	public void setModle_code(String modleCode) {
		MODLE_CODE = modleCode;
	}
	public Map<String, AttributeBean> getFileAttr() {
		return fileAttr;
	}
	public void setFileAttr(Map<String, AttributeBean> fileAttr) {
		this.fileAttr = fileAttr;
	}
	public String getVersion_control() {
		return VERSION_CONTROL;
	}
	public void setVersion_control(String versionControl) {
		VERSION_CONTROL = versionControl;
	}
	@Override
	public String toString() {
		return "FileObjectBean [fileAttr=" + fileAttr + ", MODLE_CODE="
				+ MODLE_CODE + ", VERSION_CONTROL=" + VERSION_CONTROL + "]";
	}
}