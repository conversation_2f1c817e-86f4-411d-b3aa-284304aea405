package com.sunyard.ecm.server.event.impl;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.sunyard.ecm.server.bean.AllModelMsgBean;
import com.sunyard.ecm.server.bean.BatchBean;
import com.sunyard.ecm.server.bean.BatchFileBean;
import com.sunyard.ecm.server.bean.FileBean;
import com.sunyard.ecm.server.cache.LazySingleton;
import com.sunyard.ecm.server.event.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.ecm.server.event.listener.BatchClearListener;
import com.sunyard.ecm.server.event.listener.BatchHandleListener;
import com.sunyard.ecm.server.event.listener.ConnectionListener;
import com.sunyard.ecm.server.event.listener.FileListener;
import com.sunyard.util.DateUtil;
import com.sunyard.util.OptionKey;

public class FileRecordOperListener implements BatchHandleListener, FileListener, ConnectionListener, BatchClearListener, Runnable {
	private static final int sleepTime = 5;
	private final static Logger log = LoggerFactory.getLogger(FileRecordOperListener.class);
	private Thread t;
	private boolean running = true;

	public FileRecordOperListener() {
		super();
		t = new Thread(this, "记录文件路径监听实现");
		t.start();
	}

	/**
	 * 初始化文件列表list
	 */
	public synchronized void initializeFileList() {
		log.info("begin to initializeFileList");
		Map<String, AllModelMsgBean> objMap = LazySingleton.getInstance().modelObjectSet.getMetadataObjectSetBean();
		if (objMap != null && objMap.size() > 0) {
			Iterator<String> it = objMap.keySet().iterator();
			String date;
			AllModelMsgBean bean;
			while (it.hasNext()) {
				String modelCode = it.next();
				bean = objMap.get(modelCode);
				if ("0".equals(bean.getModel_type())) {
					// 索引对象
					date = DateUtil.get8bitDateStr();
					log.debug("modelCode:" + modelCode + ",date:" + date);
					FileRecordUtil.putFileList(modelCode, date);
				}
			}
		} else {
			log.warn("initializeFileList objMap IS NULL");
		}
	}

	public void run() {
		log.info("启动简易事件监听程序");
		initializeFileList();
		while (running) {
			try {
				Thread.sleep(sleepTime * 1000);
			} catch (InterruptedException e) {
				log.error("", e);

			}
		}
		log.info("简易事件监听程序结束");
	}

	public void stop() {
		log.info("stop simple listener");
		running = false;
		try {
			t.join(10000);
		} catch (InterruptedException e) {
			log.error("等待简易监听线程关闭超时：", e);
		}
		log.info("simple listener已关闭");
	}

	/**
	 * 上传结束事件
	 */
	public boolean onUploadEnd(BatchUploadEndEvent e) {
		BatchBean bean = e.getBatchBean();
		String modelCode = bean.getModelCode();
		List<BatchFileBean> fileBeanList = bean.getDocument_Objects();
		for (BatchFileBean fileBean : fileBeanList) {
			List<FileBean> fileList = fileBean.getFiles();
			for (FileBean file : fileList) {
				log.debug("上传一张图片记录");
				FileRecordUtil.writeRecord(modelCode,"uploadFile,"+ DateUtil.get8bitDateStr() + "+" + file.getFileName());
			}
		}

		return false;
	}

	/**
	 * 更新结束事件
	 */
	public boolean onUpdateEnd(BatchUpdateEndEvent e) {
		BatchBean bean = e.getBatchBean();
		List<BatchFileBean> fileBeanList = bean.getDocument_Objects();
		String modelCode = bean.getModelCode();
		// 文件格式shjd_20151020.txt:
		if (fileBeanList != null) {
			for (BatchFileBean fileBean : fileBeanList) {
				List<FileBean> fileList = fileBean.getFiles();
				if (fileList != null) {
					for (FileBean file : fileList) {
						String optionType = file.getOptionType();
						if (OptionKey.U_ADD.equals(optionType)) {
							// 追加一张图片
							log.debug("追加一张图片");
							FileRecordUtil.writeRecord(modelCode, "addFile,"+DateUtil.get8bitDateStr() + "+" + file.getFileName());
						} else if (OptionKey.U_REPLACE.equals(optionType)) {
							// 替换一张图片
							log.debug("替换一张图片");
							FileRecordUtil.writeRecord(modelCode, "replaceFile,"+DateUtil.get8bitDateStr() + "+" + file.getFileName());
						}
					}
				}
			}
		}

		return false;
	}

	/**
	 * 文件清理事件
	 */
	public boolean onDelete(BatchClearEvent e) {
		log.debug("准备写文件删除事件:" + e.getModelCode() + "," + e.getFilePath());
		// 文件清理事件
		// 文件格式shjd_20151020.txt:
		String filePath = e.getFilePath();
		String modelCode = e.getModelCode();

		FileRecordUtil.writeRecord(modelCode,"deleteFile,"+ DateUtil.get8bitDateStr() + "-" + filePath);

		return false;
	}

	public boolean onAccept(ConnectionOpenEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onClose(ConnectionCloseEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onDelete(BatchDeleteEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onUpdateStart(BatchUpdateStartEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onUploadEnd(FileEndEvent event) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onUploadStart(BatchUploadStartEvent e) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onUploadStart(FileStartEvent event) {
		// TODO Auto-generated method stub
		return false;
	}

	public boolean onMigrateStart(BatchMigrateStartEvent e) {
		log.info("begin migrate contentid:"+e.getBatchBean().getIndex_Object().getContentID());
		// 迁移开始代码
		return false;
	}

	public boolean onMigrateEnd(BatchMigrateEndEvent e) {
		// 迁移结束代码
		BatchBean bean = e.getBatchBean();
		String modelCode = bean.getModelCode();
		List<BatchFileBean> fileBeanList = bean.getDocument_Objects();
		for (BatchFileBean fileBean : fileBeanList) {
			List<FileBean> fileList = fileBean.getFiles();
			for (FileBean file : fileList) {
				log.debug("上传一张图片记录");
				FileRecordUtil.writeRecord(modelCode, "migrateFile,"+DateUtil.get8bitDateStr() + "+" + file.getFileName());
			}
		}
		log.info("getDuration:"+e.getDuration());
		log.info("getFileUploadTime:"+e.getFileUploadTime());
		return false;
	}
}
