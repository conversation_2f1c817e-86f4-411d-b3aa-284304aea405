package com.sunyard.util.pageTool;

public interface PageTool {
	/**
	 * 分页工具
	 * 
	 * @param sql
	 * 			原sql语句
	 * @param start
	 * 			从哪条开始
	 * @param limit
	 * 			每次取几条
	 * @return
	 */
	public  String getPageSql(String sql, int start, int limit);
	
	/**
	 * 获取前几条记录
	 * 
	 * @param sql
	 * 			原sql语句
	 * @param top
	 * 			前多少条
	 * @return
	 */
	public String getTopSql(String sql , int top);
	/**
	 * 获取随即多少条记录
	 * @param sql 原sql语句 
	 * @param top 获取条数
	 * @return
	 */
	public String getRandSql(String sql, int top);
	/**
	 * 获取最高版本sql,db2和oracle不一样
	 * @return
	 */
	public String getMaxVersionAndGroupInDMDB();
	/**获取随即多少条记录
	 * @param sql 原sql语句 
	 * @param top 获取条数
	 * @return
	 */
	public String getDistinctRandSql(String string, int top);
}