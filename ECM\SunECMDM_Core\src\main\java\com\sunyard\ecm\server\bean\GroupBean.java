package com.sunyard.ecm.server.bean;

/**
 * 服务器组对象Bean
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class GroupBean {
	private String GROUP_ID;//组ID
	private String GROUP_NAME;//组名
	private String GROUP_IP;//组IP 采用第三方其群服务时 ， 集群服务器组对外的IP
	private String HTTP_PORT;//http端口
	private String SOCKET_PORT;//socket端口
	private String STATE;//启用、停用状态 0:停用 1:启用
	private String OS;//使用系统1:windows 2:mac 3:linux 4:aix
	private String REMARK;//备注
	private String DEPLOY_MODE;//部署方式 0:ECM负载均衡 1:其他集群方式 
	private String BACKUP_GROUP_ID;//
	public String getGroup_id() {
		return GROUP_ID;
	}
	public void setGroup_id(String group_id) {
		this.GROUP_ID = group_id;
	}
	public String getGroup_name() {
		return GROUP_NAME;
	}
	public void setGroup_name(String group_name) {
		this.GROUP_NAME = group_name;
	}
	public String getGroup_ip() {
		return GROUP_IP;
	}
	public void setGroup_ip(String group_ip) {
		this.GROUP_IP = group_ip;
	}
	public String getHttp_port() {
		return HTTP_PORT;
	}
	public void setHttp_port(String http_port) {
		this.HTTP_PORT = http_port;
	}
	public String getSocket_port() {
		return SOCKET_PORT;
	}
	public void setSocket_port(String socket_port) {
		this.SOCKET_PORT = socket_port;
	}
	public String getState() {
		return STATE;
	}
	public void setState(String state) {
		this.STATE = state;
	}
	public String getOs() {
		return OS;
	}
	public void setOs(String os) {
		this.OS = os;
	}
	public String getRemark() {
		return REMARK;
	}
	public void setRemark(String remark) {
		this.REMARK = remark;
	}
	public String getDeploy_mode() {
		return DEPLOY_MODE;
	}
	public void setDeploy_mode(String deploy_mode) {
		this.DEPLOY_MODE = deploy_mode;
	}
	public String getBackup_group_id() {
		return BACKUP_GROUP_ID;
	}
	public void setBackup_group_id(String backup_group_id) {
		this.BACKUP_GROUP_ID = backup_group_id;
	}
}