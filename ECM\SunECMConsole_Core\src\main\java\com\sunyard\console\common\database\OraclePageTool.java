package com.sunyard.console.common.database;


import com.sunyard.console.contentmodelmanage.bean.IndexInfoBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Title: oracle数据库查询分页
 * </p>
 * <p>
 * Description: oracle数据库查询分页
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@ConditionalOnProperty(prefix = "pageTool",name = "Type", havingValue = "oraclePageTool")
@Service("pageTool")
public class OraclePageTool implements PageTool {

	public String getPageSql(String sql, int start, int limit) {
		int rownum = start + limit - 1;
		return "select * from (select page_table.*, rownum  row_num" + " from (" + sql
				+ ") page_table) where row_num between " + start + " and " + rownum;
	}

	public String getTableSpaceName(String sql, String tableSpaceName) {
		return sql + " tablespace " + tableSpaceName;
	}

	public String getOnlyOneSql(String sql, String tableName) {
		return sql + " where rownum=1 ";
	}

	public String delIndexSql(IndexInfoBean bean) {
		return "DROP INDEX " + bean.getTable_name() + "_" + bean.getIndex_name();
	}
}
