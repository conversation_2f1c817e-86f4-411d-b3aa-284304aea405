package com.sunyard.console.common.accesscontrol;

import java.text.SimpleDateFormat;
import java.util.Date;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



/**
 * <p>
 * Title: 权限校验类
 * </p>
 * <p>
 * Description: 判断系统是否有使用权限，是否到期
 * </p>
 * <p>
 * Copyright: Copyright (c) 2012
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@WebListener
public class PermissionCheck implements ServletContextListener {
	 final static Logger log = LoggerFactory.getLogger(PermissionCheck.class);

	
	public void contextInitialized(ServletContextEvent event) {
		log.info("=====系统权限检验初始化=====");
	//	check();
		log.info("=====系统权限校验完毕=====");
	}

	public void contextDestroyed(ServletContextEvent event) {

	}
	/**
	 * 权限检验
	 * @return
	 */
	private void check(){
		SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");//设置日期格式 yyyy-MM-dd HH:mm:ss
		String now = df.format(new Date());
		log.debug("系统当前时间为："+now);
		String expriation = LicenseFileConstant.getExpiration();
		log.debug("系统到期时间为："+expriation);
		if(Integer.parseInt(now)<=Integer.parseInt(expriation)){
			log.debug("系统未到期，可以使用");
		}else{
			log.debug("系统已过期，无法使用");
			throw new RuntimeException("系统已超过使用期限，禁止启动！");
		}
		if(LicenseFileConstant.CheckLicense()){
			log.debug("校验成功，可以使用");
		}else{
			log.debug("校验失败，无法使用");
			throw new RuntimeException("系统未授权，禁止启动！");
		}
	}
}
