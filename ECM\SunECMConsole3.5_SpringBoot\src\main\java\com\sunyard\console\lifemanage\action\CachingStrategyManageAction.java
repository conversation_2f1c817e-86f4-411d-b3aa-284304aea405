package com.sunyard.console.lifemanage.action;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.JSONUtil;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.sunyard.console.contentservermanage.bean.ContentServerInfoBean;
import com.sunyard.console.lifemanage.bean.ScheduleInfoBean;
import com.sunyard.console.lifemanage.bean.TaskInfoBean;
import com.sunyard.console.lifemanage.dao.CachingStrategyManageDAO;
import com.sunyard.console.threadpoool.IssueUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>
 * Title: 策略管理action
 * </p>
 * <p>
 * Description: 用于管理策略信息,包括查询，新增，修改，删除等操作的action访问类
 * </p>
 * <p>
 * Copyright: Copyright (c) 2010
 * </p>
 * <p>
 * Company: sunyard
 * </p>
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class CachingStrategyManageAction extends BaseAction {
	/**
	 * 
	 */
	private static final long serialVersionUID = -532899336413491375L;
	/**
	 * 策略管理数据库接口操作对象
	 */
	@Autowired
	private CachingStrategyManageDAO cachingStrategyManageDao;
	/**
	 * 日志对象
	 */
	private final static  Logger log = LoggerFactory.getLogger(CachingStrategyManageAction.class);
	/**
	 * 策略主键
	 */
	private int task_id;
	/**
	 * 任务主键
	 */
	private int task_no;
	/**
	 * 任务名称
	 */
	private String task_name;
	/**
	 * 服务器组id
	 */
	private int group_id;
	/**
	 * 模型名称
	 */
	private String group_name;
	/**
	 * 内容模型代码
	 */
	private String model_code;
	/**
	 * 模型名称
	 */
	private String model_name;
	/**
	 * 运行类型 即时 0 每天 1 每周 2 每月 3 每年 4
	 */
	private String run_type;
	/**
	 * 开始时间
	 */
	private String begin_time;
	/**
	 * 结束时间
	 */
	private String end_time;
	/**
	 * 其他参数
	 */
	private String parameters;
	/**
	 * 策略状态
	 */
	private int task_state;
	/**
	 * 分页显示记录的开始位置
	 */
	private int start;
	/**
	 * 分页显示记录的总条数
	 */
	private int limit;
	/**
	 * 新增或者修改任务标识符
	 */
	private String optionFlag;
	/**
	 * 需要禁用和启用的策略id组成的字符串
	 */
	private String task_ids;
	/**
	 * 由策略主键和服务器组主键组成的字符串，格式“策略主键：服务器组主键”，多个组合之间用“，”号隔开
	 */
	private String groupIds_taskIds;

	public CachingStrategyManageDAO getCachingStrategyManageDao() {
		return cachingStrategyManageDao;
	}

	public void setCachingStrategyManageDao(CachingStrategyManageDAO cachingStrategyManageDao) {
		this.cachingStrategyManageDao = cachingStrategyManageDao;
	}

	public int getTask_id() {
		return task_id;
	}

	public void setTask_id(int taskId) {
		task_id = taskId;
	}

	public int getTask_no() {
		return task_no;
	}

	public void setTask_no(int taskNo) {
		task_no = taskNo;
	}

	public String getGroupIds_taskIds() {
		return groupIds_taskIds;
	}

	public void setGroupIds_taskIds(String groupIdsTaskIds) {
		groupIds_taskIds = groupIdsTaskIds;
	}

	public String getTask_name() {
		return task_name;
	}

	public void setTask_name(String taskName) {
		task_name = taskName;
	}

	public int getGroup_id() {
		return group_id;
	}

	public void setGroup_id(int groupId) {
		group_id = groupId;
	}

	public String getGroup_name() {
		return group_name;
	}

	public void setGroup_name(String groupName) {
		group_name = groupName;
	}

	public String getModel_code() {
		return model_code;
	}

	public void setModel_code(String modelCode) {
		model_code = modelCode;
	}

	public String getModel_name() {
		return model_name;
	}

	public void setModel_name(String modelName) {
		model_name = modelName;
	}

	public String getRun_type() {
		return run_type;
	}

	public void setRun_type(String runType) {
		run_type = runType;
	}

	public String getBegin_time() {
		return begin_time;
	}

	public void setBegin_time(String beginTime) {
		begin_time = beginTime;
	}

	public String getEnd_time() {
		return end_time;
	}

	public void setEnd_time(String endTime) {
		end_time = endTime;
	}

	public String getParameters() {
		return parameters;
	}

	public void setParameters(String parameters) {
		this.parameters = parameters;
	}

	public int getTask_state() {
		return task_state;
	}

	public void setTask_state(int taskState) {
		task_state = taskState;
	}

	public int getStart() {
		return start;
	}

	public void setStart(int start) {
		this.start = start;
	}

	public int getLimit() {
		return limit;
	}

	public void setLimit(int limit) {
		this.limit = limit;
	}

	public String getOptionFlag() {
		return optionFlag;
	}

	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}

	public String getTask_ids() {
		return task_ids;
	}

	public void setTask_ids(String taskIds) {
		task_ids = taskIds;
	}

	/**
	 * 获取策略列表
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/getScheduleListAction.action", method = RequestMethod.POST)
	public String getScheduleList(int group_id, String model_code, String task_id, int start, int limit, String task_name) {
		log.info("--getScheduleList(start)-->group_id:" + group_id + ";model_code:" + model_code);
		String jsonStr = null;
		task_id = "".equals(task_id) || task_id == null ? "0" : task_id;
		task_name = task_name == null ? null : task_name.trim();
		try {
			List<ScheduleInfoBean> list = cachingStrategyManageDao.getScheduleList(Integer.valueOf(task_id), group_id, model_code,
					start + 1, limit);
			List<ScheduleInfoBean> AllList = cachingStrategyManageDao.getScheduleList(Integer.valueOf(task_id), group_id, model_code);
			int size = 0;
			if (AllList != null && AllList.size() > 0) {
				size = AllList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(list, size, new ScheduleInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取策略信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("缓存策略管理->查询策略失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info("--getScheduleList(over)");
		return null;

	}

	/**
	 * 获取服务器组下某个内容对象未绑定的任务
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/getUnbindTaskListAction.action", method = RequestMethod.POST)
	public String getUnbindTaskList(int group_id, String model_code, String task_no) {
		log.info("--getUnbindTaskList-->group_id:" + group_id + ";model_code:" + model_code);
		String jsonStr = null;
		task_no = "".equals(task_no) || task_no == null ? "0" : task_no;
		try {
			// 查询未有的任务列表
			List<TaskInfoBean> unBindList = cachingStrategyManageDao.getUnbindTaskList(group_id, model_code, Integer.valueOf(task_no));
			// 查询已有的策略列表
			List<ScheduleInfoBean> bindTaskList = cachingStrategyManageDao.getBindTaskList(group_id, model_code);
			// 重新设置未绑定策略， /对【迁移排程】和【离线排程】做互斥操作
			unBindList = resetUnBindList(unBindList, bindTaskList);

			if (unBindList!=null && unBindList.size() > 0) {
				jsonStr = new JSONUtil().createJsonDataByColl(unBindList, unBindList.size(), new TaskInfoBean());
			} else {
				jsonStr = "{'result': 'success','success': 'true','totalProperty': '0','root': []}";
			}
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取未绑定的任务失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->获取服务器组下某个内容对象未绑定的任务失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info("--getUnbindTaskList(over)");
		return null;
	}

	/**
	 * 重新设置未绑定策略，对迁移和离线做互斥处理
	 * 
	 * @param unBindList
	 * @param bindTaskList
	 */
	private List<TaskInfoBean> resetUnBindList(List<TaskInfoBean> unBindList, List<ScheduleInfoBean> bindTaskList) {
		// 对【迁移排程】和【离线排程】做互斥操作
		if (unBindList != null && bindTaskList != null) {
			log.debug("查询未有的任务列表:size:" + unBindList.size());
			log.debug("查询已经有的策略列表:size:" + bindTaskList.size());
			Iterator<TaskInfoBean> unBindIterator = unBindList.iterator();
			TaskInfoBean unBindBean = null;

			for (ScheduleInfoBean schedulerBean : bindTaskList) {
				int taskNo = schedulerBean.getTask_no();
				if (taskNo == 2) {
					// 存在【迁移排程】，则不能配置【离线排程】=3,【离线清理】=4
					while (unBindIterator.hasNext()) {
						unBindBean = unBindIterator.next();
						if (unBindBean.getTask_no() == 3 || unBindBean.getTask_no() == 4) {
							// 去掉【离线排程】和【离线清理】
							log.debug("服务器组:" + group_id + "下的内容模型:" + model_code + "已经配置了迁移任务，去掉内容离线和离线清理");
							unBindIterator.remove();
						}
					}
				}
				if (taskNo == 3 || taskNo == 4) {
					// 存在【离线排程】=3,[离线清理]=4，则不能配置【迁移排程】】
					while (unBindIterator.hasNext()) {
						unBindBean = unBindIterator.next();
						if (unBindBean.getTask_no() == 2) {
							// 去掉【迁移排程】
							log.debug("服务器组:" + group_id + "下的内容模型:" + model_code + "已经配置了离线任务，去掉迁移任务");
							unBindIterator.remove();
							break;

						}
					}
				}
			}
		}
		return unBindList;
	}

	/**
	 * 增加策略
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/addCachingStrategyAction.action", method = RequestMethod.POST)
	public String addCachingStrategy(int group_id, String model_code, String begin_time, String end_time, String group_name,
									 String model_name, String parameters, String run_type, int task_no, int task_state) {
		log.info("--addCachingStrategy(start)-->group_id:" + group_id + ";model_code:" + model_code);
		String jsonStr = null;
		ScheduleInfoBean bean = new ScheduleInfoBean();
		bean.setBegin_time(begin_time);
		bean.setEnd_time(end_time);
		bean.setGroup_id(group_id);
		bean.setGroup_name(group_name);
		bean.setModel_code(model_code);
		bean.setModel_name(model_name);
		bean.setParameters(parameters);
		bean.setRun_type(run_type);
		bean.setTask_no(task_no);
		bean.setTask_state(task_state);
		log.debug("--addCachingStrategy-->bean:" + bean);
		try {
			task_id = cachingStrategyManageDao.addCachingStrategy(bean);
			log.debug("--addCachingStrategy-->task_id:" + task_id);
			if (task_id != 0 && task_state == 1) {
				// 新增成功并且策略状态为启用，下发缓存策略到该服务器组下所有启用的服务器
				/// ConsoleThreadPool.getThreadPool().submit(new Thread(new SendCachingStrategyThread(task_id, 1)));
				IssueUtils.IssueInfoToDM(IssueUtils.getServerListbyTaskId(task_id));
				log.debug("--addCachingStrategy-->SendCachingStrategyThread start");
			}
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", true);
			jsonResp.put("message", "添加策略成功!!");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "添加策略失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->增加策略失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info("--addCachingStrategy(over)-->task_id:" + task_id);
		return null;
	}
	/**
	 * 修改策略
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/updateCachingStrategyAction.action", method = RequestMethod.POST)
	public String updateCachingStrategy(int task_id, String model_code, String begin_time, String end_time, String group_name, int group_id,
										String model_name, String parameters, String run_type, int task_no, int task_state) {
		log.info("--updateCachingStrategy(start)-->task_id:" + task_id + ";model_code:" + model_code);
		String jsonStr = null;
		ScheduleInfoBean bean = new ScheduleInfoBean();
		bean.setBegin_time(begin_time);
		bean.setEnd_time(end_time);
		bean.setGroup_id(group_id);
		bean.setGroup_name(group_name);
		bean.setModel_code(model_code);
		bean.setModel_name(model_name);
		bean.setParameters(parameters);
		bean.setRun_type(run_type);
		bean.setTask_id(task_id);
		bean.setTask_no(task_no);
		bean.setTask_state(task_state);
		log.debug("--updateCachingStrategy-->bean:" + bean);
		try {
			boolean result = cachingStrategyManageDao.updateCachingStrategy(bean);
			log.debug("--updateCachingStrategy-->result:" + result);
			if (result) {
				// 则调用修改策略下发操作
				//ConsoleThreadPool.getThreadPool().submit(new Thread(new SendCachingStrategyThread(task_id, 3)));
				IssueUtils.IssueInfoToDM(IssueUtils.getServerListbyTaskId(task_id));
				log.debug("--updateCachingStrategy-->SendCachingStrategyThread start");
			}
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", true);
			jsonResp.put("message", "修改策略成功!!");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "修改策略失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->修改策略失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info("--updateCachingStrategy(over)-->task_id:" + task_id);
		return null;

	}

	/**
	 * 启用策略
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/startCachingStrategyAction.action", method = RequestMethod.POST)
	public String startCachingStrategy(String task_ids) {

		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		boolean success = false;
	
		try {
			List<ContentServerInfoBean> contentServerInfoBeanlist=new  ArrayList<ContentServerInfoBean>();
			if (task_ids == null || task_ids.equals("")) {
				success = false;

			} else {
				log.info("--startCachingStrategy(start)-->task_ids:" + task_ids);
				boolean result = cachingStrategyManageDao.startCachingStrategy(task_ids);
				log.debug("--startCachingStrategy-->result:" + result);
				success = result;
				String[] ids = task_ids.split(",");
				if (ids != null && ids.length > 0) {
					for (int i = 0; i < ids.length; i++) {
					/*	ConsoleThreadPool.getThreadPool()
								.submit(new Thread(new SendCachingStrategyThread(Integer.parseInt(ids[i]), 4)));*/
						contentServerInfoBeanlist.addAll(IssueUtils.getServerListbyTaskId(Integer.parseInt(ids[i])));
						log.debug("--startCachingStrategy-->SendCachingStrategyThread start");

					}
				}
			}
			IssueUtils.IssueInfoToDM(contentServerInfoBeanlist);
			if (success) {
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("message", "启用策略成功!!");
				jsonStr = jsonResp.toString();

			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "启用策略失败!!");
				jsonStr = jsonResp.toString();
			}
			log.debug("--startCachingStrategy-->success:" + success);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "启用策略失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->启用策略失败:" + e.toString(), e);

		}

		this.outJsonString(jsonStr);
		log.info("--startCachingStrategy(over)-->task_ids:" + task_ids);
		return null;

	}

	/**
	 * 禁用策略
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/stopCachingStrategyAction.action", method = RequestMethod.POST)
	public String stopCachingStrategy(String task_ids) {

		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		boolean success = false;
		List<ContentServerInfoBean> contentServerInfoBeanlist=new  ArrayList<ContentServerInfoBean>();
		try {
			if (task_ids == null || task_ids.equals("")) {
				success = false;

			} else {
				log.info("--stopCachingStrategy(start)-->task_ids:" + task_ids);
				boolean result = cachingStrategyManageDao.stopCachingStrategy(task_ids);
				log.debug("--stopCachingStrategy-->result:" + result);
				success = result;
				String[] ids = task_ids.split(",");
				if (ids != null && ids.length > 0) {
					for (int i = 0; i < ids.length; i++) {
						/*ConsoleThreadPool.getThreadPool()
								.submit(new Thread(new SendCachingStrategyThread(Integer.parseInt(ids[i]), 5)));*/
						contentServerInfoBeanlist.addAll(IssueUtils.getServerListbyTaskId(Integer.parseInt(ids[i])));
						log.debug("--stopCachingStrategy-->SendCachingStrategyThread start");
					}
				}
			}
			IssueUtils.IssueInfoToDM(contentServerInfoBeanlist);
			if (success) {
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("message", "禁用策略成功!!");
				jsonStr = jsonResp.toString();

			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "禁用策略失败!!");
				jsonStr = jsonResp.toString();
			}
			log.debug("--stopCachingStrategy-->success:" + success);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "禁用策略失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->禁用策略失败:", e);

		}

		this.outJsonString(jsonStr);
		log.info("--stopCachingStrategy(over)-->task_ids:" + task_ids);
		return null;

	}
	
	/**
	 * 获取策略列表
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/getScheduleListVueAction.action")
	public String getScheduleListVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		String group_id = (String) modelJson.getOrDefault("group_id", "0");
		String task_id = (String) modelJson.getOrDefault("task_id", "0");
		String model_code = (String) modelJson.getOrDefault("model_code", "");
		String task_name = (String) modelJson.getOrDefault("task_name", "");
		log.info("--getScheduleListVue(start)-->group_id:" + group_id + ";model_code:" + model_code);
		String jsonStr = null;
		start = (page_int-1) * limit_int;
		task_id = "".equals(task_id) || task_id == null ? "0" : task_id;
		group_id = "".equals(group_id) || group_id == null ? "0" : group_id;
		task_name = task_name == null ? null : task_name.trim();
		try {
			List<ScheduleInfoBean> list = cachingStrategyManageDao.getScheduleList(Integer.valueOf(task_id), Integer.valueOf(group_id), model_code,
					start + 1, limit_int);
			List<ScheduleInfoBean> AllList = cachingStrategyManageDao.getScheduleList(Integer.valueOf(task_id), Integer.valueOf(group_id), model_code);
			int size = 0;
			if (AllList != null && AllList.size() > 0) {
				size = AllList.size();
			}
			jsonStr = new JSONUtil().createJsonDataByColl(list, size, new ScheduleInfoBean());
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", "false");
			jsonResp.put("message", "获取策略信息失败!!");
			jsonStr = jsonResp.toString();
			// 记录日志
			log.error("缓存策略管理->查询策略失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info("--getScheduleListVue(over)");
		return null;
	}
	
	/**
	 * 增加策略
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/addCachingVueAction.action", method = RequestMethod.POST)
	public String addCachingVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		
		int group_id = modelJson.getInt("group_id");
		int task_no = modelJson.getInt("task_no");
		int task_state = modelJson.getInt("task_state");
		String model_code = (String) modelJson.getOrDefault("model_code", "");
		String model_name = (String) modelJson.getOrDefault("model_name", "");
		String begin_time = (String) modelJson.getOrDefault("begin_time", "");
		String end_time = (String) modelJson.getOrDefault("end_time", "");
		String group_name = (String) modelJson.getOrDefault("group_name", "");
		String parameters = (String) modelJson.getOrDefault("parameters", "");
		String run_type = (String) modelJson.getOrDefault("run_type", "");
		
		log.info("--addCachingVue(start)-->group_id:" + group_id + ";model_code:" + model_code);
		String jsonStr = null;
		ScheduleInfoBean bean = new ScheduleInfoBean();
		bean.setBegin_time(begin_time);
		bean.setEnd_time(end_time);
		bean.setGroup_id(group_id);
		bean.setGroup_name(group_name);
		bean.setModel_code(model_code);
		bean.setModel_name(model_name);
		bean.setParameters(parameters);
		bean.setRun_type(run_type);
		bean.setTask_no(task_no);
		bean.setTask_state(task_state);
		log.debug("--addCachingVue-->bean:" + bean);
		try {
			task_id = cachingStrategyManageDao.addCachingStrategy(bean);
			log.debug("--addCachingStrategy-->task_id:" + task_id);
			if (task_id != 0 && task_state == 1) {
				// 新增成功并且策略状态为启用，下发缓存策略到该服务器组下所有启用的服务器
				/// ConsoleThreadPool.getThreadPool().submit(new Thread(new SendCachingStrategyThread(task_id, 1)));
				IssueUtils.IssueInfoToDM(IssueUtils.getServerListbyTaskId(task_id));
				log.debug("--addCachingStrategy-->SendCachingStrategyThread start");
			}
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "添加策略成功!!");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "添加策略失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->增加策略失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info("--addCachingVue(over)-->task_id:" + task_id);
		return null;
	}
	
	/**
	 * 修改策略
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/updateCachingVueAction.action", method = RequestMethod.POST)
	public String updateCachingVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);

		int group_id = modelJson.getInt("group_id");
		int task_id = modelJson.getInt("task_id");
		int task_no = modelJson.getInt("task_no");
		int task_state = modelJson.getInt("task_state");
		String model_code = (String) modelJson.getOrDefault("model_code", "");
		String model_name = (String) modelJson.getOrDefault("model_name", "");
		String begin_time = (String) modelJson.getOrDefault("begin_time", "");
		String end_time = (String) modelJson.getOrDefault("end_time", "");
		String group_name = (String) modelJson.getOrDefault("group_name", "");
		String parameters = (String) modelJson.getOrDefault("parameters", "");
		String run_type = (String) modelJson.getOrDefault("run_type", "");
		
		log.info("--updateCachingStrategy(start)-->task_id:" + task_id + ";model_code:" + model_code);
		String jsonStr = null;
		ScheduleInfoBean bean = new ScheduleInfoBean();
		bean.setBegin_time(begin_time);
		bean.setEnd_time(end_time);
		bean.setGroup_id(group_id);
		bean.setGroup_name(group_name);
		bean.setModel_code(model_code);
		bean.setModel_name(model_name);
		bean.setParameters(parameters);
		bean.setRun_type(run_type);
		bean.setTask_id(task_id);
		bean.setTask_no(task_no);
		bean.setTask_state(task_state);
		log.debug("--updateCachingStrategy-->bean:" + bean);
		try {
			boolean result = cachingStrategyManageDao.updateCachingStrategy(bean);
			log.debug("--updateCachingStrategy-->result:" + result);
			if (result) {
				// 则调用修改策略下发操作
				//ConsoleThreadPool.getThreadPool().submit(new Thread(new SendCachingStrategyThread(task_id, 3)));
				IssueUtils.IssueInfoToDM(IssueUtils.getServerListbyTaskId(task_id));
				log.debug("--updateCachingStrategy-->SendCachingStrategyThread start");
			}
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "修改策略成功!!");
			jsonStr = jsonResp.toString();
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "修改策略失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->修改策略失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info("--updateCachingStrategy(over)-->task_id:" + task_id);
		return null;
	}

	/**
	 * 启用策略
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/startCachingVueAction.action", method = RequestMethod.POST)
	public String startCachingVue(String task_ids) {
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		boolean success = false;
		try {
			List<ContentServerInfoBean> contentServerInfoBeanlist=new  ArrayList<ContentServerInfoBean>();
			if (task_ids == null || task_ids.equals("")) {
				success = false;
			} else {
				log.info("--startCachingStrategy(start)-->task_ids:" + task_ids);
				boolean result = cachingStrategyManageDao.startCachingStrategy(task_ids);
				log.debug("--startCachingStrategy-->result:" + result);
				success = result;
				String[] ids = task_ids.split(",");
				if (ids != null && ids.length > 0) {
					for (int i = 0; i < ids.length; i++) {
					/*	ConsoleThreadPool.getThreadPool()
								.submit(new Thread(new SendCachingStrategyThread(Integer.parseInt(ids[i]), 4)));*/
						contentServerInfoBeanlist.addAll(IssueUtils.getServerListbyTaskId(Integer.parseInt(ids[i])));
						log.debug("--startCachingStrategy-->SendCachingStrategyThread start");
					}
				}
			}
			IssueUtils.IssueInfoToDM(contentServerInfoBeanlist);
			if (success) {
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("message", "启用策略成功!!");
				jsonStr = jsonResp.toString();
			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "启用策略失败!!");
				jsonStr = jsonResp.toString();
			}
			log.debug("--startCachingStrategy-->success:" + success);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "启用策略失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->启用策略失败:" + e.toString(), e);

		}
		this.outJsonString(jsonStr);
		log.info("--startCachingStrategy(over)-->task_ids:" + task_ids);
		return null;
	}

	/**
	 * 禁用策略
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/stopCachingVueAction.action", method = RequestMethod.POST)
	public String stopCachingVue(String task_ids) {
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		boolean success = false;
		List<ContentServerInfoBean> contentServerInfoBeanlist=new  ArrayList<ContentServerInfoBean>();
		try {
			if (task_ids == null || task_ids.equals("")) {
				success = false;
			} else {
				log.info("--stopCachingStrategy(start)-->task_ids:" + task_ids);
				boolean result = cachingStrategyManageDao.stopCachingStrategy(task_ids);
				log.debug("--stopCachingStrategy-->result:" + result);
				success = result;
				String[] ids = task_ids.split(",");
				if (ids != null && ids.length > 0) {
					for (int i = 0; i < ids.length; i++) {
						/*ConsoleThreadPool.getThreadPool()
								.submit(new Thread(new SendCachingStrategyThread(Integer.parseInt(ids[i]), 5)));*/
						contentServerInfoBeanlist.addAll(IssueUtils.getServerListbyTaskId(Integer.parseInt(ids[i])));
						log.debug("--stopCachingStrategy-->SendCachingStrategyThread start");
					}
				}
			}
			IssueUtils.IssueInfoToDM(contentServerInfoBeanlist);
			if (success) {
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("message", "禁用策略成功!!");
				jsonStr = jsonResp.toString();

			} else {
				jsonResp.put("success", false);
				jsonResp.put("message", "禁用策略失败!!");
				jsonStr = jsonResp.toString();
			}
			log.debug("--stopCachingStrategy-->success:" + success);
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "禁用策略失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->禁用策略失败:", e);
		}
		this.outJsonString(jsonStr);
		log.info("--stopCachingStrategy(over)-->task_ids:" + task_ids);
		return null;
	}
	
	/**
	 * 获取服务器组下某个内容对象未绑定的任务
	 * 
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/lifeManage/getUnbindTaskListVueAction.action", method = RequestMethod.POST)
	public String getUnbindTaskListVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);

		int group_id = modelJson.getInt("group_id");
		String model_code = (String) modelJson.getOrDefault("model_code", "");
		String task_no = (String) modelJson.getOrDefault("task_no", "");
		
		log.info("--getUnbindTaskList-->group_id:" + group_id + ";model_code:" + model_code);
		String jsonStr = null;
		task_no = "".equals(task_no) || task_no == null ? "0" : task_no;
		try {
			// 查询未有的任务列表
			List<TaskInfoBean> unBindList = cachingStrategyManageDao.getUnbindTaskList(group_id, model_code, Integer.valueOf(task_no));
			// 查询已有的策略列表
			List<ScheduleInfoBean> bindTaskList = cachingStrategyManageDao.getBindTaskList(group_id, model_code);
			// 重新设置未绑定策略， /对【迁移排程】和【离线排程】做互斥操作
			unBindList = resetUnBindList(unBindList, bindTaskList);

			if (unBindList!=null && unBindList.size() > 0) {
				jsonStr = new JSONUtil().createJsonDataByColl(unBindList, unBindList.size(), new TaskInfoBean());
			} else {
				JSONObject jsonResp=new JSONObject();
				jsonResp.put("result", "success");
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("totalProperty","0");//总行数
				jsonResp.put("root","");//总行数
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			JSONObject jsonResp = new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取未绑定的任务失败!!");
			jsonStr = jsonResp.toString();
			log.error("缓存策略管理->获取服务器组下某个内容对象未绑定的任务失败:" + e.toString(), e);
		}
		this.outJsonString(jsonStr);
		log.info("--getUnbindTaskList(over)");
		return null;
	}
}
