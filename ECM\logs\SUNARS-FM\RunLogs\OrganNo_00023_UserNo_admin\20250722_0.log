2025-07-22 10:55:32.221 [OrganNo_00023_UserNo_admin] [d158a8be3471f2ae/d0eec9f99d918b16] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:55:32.262 [OrganNo_00023_UserNo_admin] [d158a8be3471f2ae/53fc267172a96718] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 10:55:32.263 [OrganNo_00023_UserNo_admin] [d158a8be3471f2ae/53fc267172a96718] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 2(String)
2025-07-22 10:55:32.267 [OrganNo_00023_UserNo_admin] [d158a8be3471f2ae/53fc267172a96718] [http-nio-9009-exec-40] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:55:32.278 [OrganNo_00023_UserNo_admin] [d158a8be3471f2ae/d0eec9f99d918b16] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:04.646 [OrganNo_00023_UserNo_admin] [96a72c41d6932fdd/41f540dbd7abfaed] [http-nio-9009-exec-41] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:57:04.662 [OrganNo_00023_UserNo_admin] [96a72c41d6932fdd/605359f103d3c048] [http-nio-9009-exec-41] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 10:57:04.663 [OrganNo_00023_UserNo_admin] [96a72c41d6932fdd/605359f103d3c048] [http-nio-9009-exec-41] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 2(String)
2025-07-22 10:57:04.666 [OrganNo_00023_UserNo_admin] [96a72c41d6932fdd/605359f103d3c048] [http-nio-9009-exec-41] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:04.686 [OrganNo_00023_UserNo_admin] [96a72c41d6932fdd/41f540dbd7abfaed] [http-nio-9009-exec-41] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:05.331 [OrganNo_00023_UserNo_admin] [acf3c932a7781f5f/1d61e108484c07fa] [http-nio-9009-exec-42] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:57:05.345 [OrganNo_00023_UserNo_admin] [acf3c932a7781f5f/d3ea3285642cfb26] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 10:57:05.346 [OrganNo_00023_UserNo_admin] [acf3c932a7781f5f/d3ea3285642cfb26] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 2(String)
2025-07-22 10:57:05.348 [OrganNo_00023_UserNo_admin] [acf3c932a7781f5f/d3ea3285642cfb26] [http-nio-9009-exec-42] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:05.360 [OrganNo_00023_UserNo_admin] [acf3c932a7781f5f/1d61e108484c07fa] [http-nio-9009-exec-42] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:05.484 [OrganNo_00023_UserNo_admin] [2107d14b4cf32670/ee5601fcfc20de12] [http-nio-9009-exec-43] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:57:05.496 [OrganNo_00023_UserNo_admin] [2107d14b4cf32670/2af8e9b3fbfed76a] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 10:57:05.496 [OrganNo_00023_UserNo_admin] [2107d14b4cf32670/2af8e9b3fbfed76a] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 2(String)
2025-07-22 10:57:05.498 [OrganNo_00023_UserNo_admin] [2107d14b4cf32670/2af8e9b3fbfed76a] [http-nio-9009-exec-43] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:05.514 [OrganNo_00023_UserNo_admin] [2107d14b4cf32670/ee5601fcfc20de12] [http-nio-9009-exec-43] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:06.087 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/a8f30f5a9f39c6f6] [http-nio-9009-exec-44] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-22 10:57:06.101 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/b581fa9b8345d4fe] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM FM_ARCH WHERE ARCH_STATE = ?
2025-07-22 10:57:06.102 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/b581fa9b8345d4fe] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - ==> Parameters: 3(String)
2025-07-22 10:57:06.104 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/b581fa9b8345d4fe] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:06.105 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/b581fa9b8345d4fe] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.F.selectByConditions - ==>  Preparing: SELECT ID, ARCH_NO, ARCH_STATE, ARCH_TYPE, SITE_NO, SITE_NAME, START_DATE, END_DATE, YEAR_NUM, DAY_NUM, BUSINESS_DATE, WARRANT_AMOUNT, REGISTER_DATE, USER_NO, USER_NAME, REMARK, CODE_NO, CODE_NAME, TELLER_NO, TELLER_NAME, TRUNK_NO, TRANSFER_DATE, BELONG_YEAR, KEEP_YEAR, INSERT_NO, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, LATE_REASON FROM FM_ARCH WHERE ARCH_STATE = ? ORDER BY ARCH_NO DESC LIMIT ?
2025-07-22 10:57:06.105 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/b581fa9b8345d4fe] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.F.selectByConditions - ==> Parameters: 3(String), 15(Integer)
2025-07-22 10:57:06.107 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/b581fa9b8345d4fe] [http-nio-9009-exec-44] DEBUG c.s.a.f.d.F.selectByConditions - <==      Total: 3
2025-07-22 10:57:06.130 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/a8f30f5a9f39c6f6] [http-nio-9009-exec-44] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT06-2025-000023-99-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"dayNum":1,
				"endDate":"20251231",
				"id":"5eff8c8a5bf84fa994a16675af0dc299",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250715",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"22",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:06.175 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/aa2af5631b1895ca] [http-nio-9009-exec-45] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-22 10:57:06.188 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 10:57:06.188 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 1(String)
2025-07-22 10:57:06.190 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:06.190 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.U.selectByConditions - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE TRUNK_STATE = ? ORDER BY TRUNK_NO DESC LIMIT ?
2025-07-22 10:57:06.190 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.U.selectByConditions - ==> Parameters: 1(String), 15(Integer)
2025-07-22 10:57:06.192 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.U.selectByConditions - <==      Total: 1
2025-07-22 10:57:06.192 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==>  Preparing: SELECT CODE_NO as codeNo, CODE_NAME as codeName, KEEY_YEARS as keepYears FROM fm_unscan_code WHERE CODE_NO = ?
2025-07-22 10:57:06.192 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==> Parameters: BG01(String)
2025-07-22 10:57:06.193 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/62c991251dfb4ba2] [http-nio-9009-exec-45] DEBUG c.s.a.f.d.F.selectUnscanCodes - <==      Total: 1
2025-07-22 10:57:06.206 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/aa2af5631b1895ca] [http-nio-9009-exec-45] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:12.127 [OrganNo_00023_UserNo_admin] [e69bff158e1232a5/110257a8f96249dd] [http-nio-9009-exec-47] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"getUnscanCodeList"
	}
}
2025-07-22 10:57:12.128 [OrganNo_00023_UserNo_admin] [e69bff158e1232a5/5025b1f226e21b8c] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.U.selectAllEnabled - ==>  Preparing: SELECT id, code_no, code_type, code_name, keey_years, is_default FROM fm_unscan_code ORDER BY code_no ASC
2025-07-22 10:57:12.129 [OrganNo_00023_UserNo_admin] [e69bff158e1232a5/5025b1f226e21b8c] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.U.selectAllEnabled - ==> Parameters: 
2025-07-22 10:57:12.134 [OrganNo_00023_UserNo_admin] [e69bff158e1232a5/5025b1f226e21b8c] [http-nio-9009-exec-47] DEBUG c.s.a.f.d.U.selectAllEnabled - <==      Total: 42
2025-07-22 10:57:12.151 [OrganNo_00023_UserNo_admin] [e69bff158e1232a5/110257a8f96249dd] [http-nio-9009-exec-47] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"codeNo":"BG01",
				"codeType":"财务报告",
				"id":"8BAAF15146D3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"中期财务报告:本级及全辖月\\季\\半年财务报告",
				"codeNo":"BG02",
				"codeType":"财务报告",
				"id":"8BAAF15146D4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"下级机构上报的财务报告",
				"codeNo":"BG03",
				"codeType":"财务报告",
				"id":"8BAAF15146D5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"不定期报表",
				"codeNo":"BG04",
				"codeType":"财务报告",
				"id":"8BAAF15146D6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他财务报告",
				"codeNo":"BG05",
				"codeType":"财务报告",
				"id":"8BAAF15146D7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"特殊介质保存的会计资料",
				"codeNo":"JZ01",
				"codeType":"特殊介质",
				"id":"8BAAF15146D8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"codeType":"会计凭证",
				"id":"8BAAF15146D9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"其他会计凭证",
				"codeNo":"PZ02",
				"codeType":"会计凭证",
				"id":"8BAAF15146DAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"挂失登记及补发凭单收据",
				"codeNo":"QT01",
				"codeType":"其他",
				"id":"8BAAF15146DBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"存\\贷款开销户记录",
				"codeNo":"QT02",
				"codeType":"其他",
				"id":"8BAAF15146DCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"有权机关查询\\冻结\\扣划公函资料和登记簿",
				"codeNo":"QT03",
				"codeType":"其他",
				"id":"8BAAF15146DDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"帐销案存记录",
				"codeNo":"QT04",
				"codeType":"其他",
				"id":"8BAAF15146DEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"codeType":"其他",
				"id":"8BAAF15146DFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"codeType":"其他",
				"id":"8BAAF15146E0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案保管登记簿",
				"codeNo":"QT07",
				"codeType":"其他",
				"id":"8BAAF15146E1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"销毁清册及相关审批资料",
				"codeNo":"QT08",
				"codeType":"其他",
				"id":"8BAAF15146E2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"机构变动交接清册",
				"codeNo":"QT09",
				"codeType":"其他",
				"id":"8BAAF15146E3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"重要单证\\有价凭证\\印章的领发\\保管和缴销记录",
				"codeNo":"QT10",
				"codeType":"其他",
				"id":"8BAAF15146E4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计档案移交清册",
				"codeNo":"QT11",
				"codeType":"其他",
				"id":"8BAAF15146E5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计人员交接清册",
				"codeNo":"QT12",
				"codeType":"其他",
				"id":"8BAAF15146E6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计事后监督档案(含差错通知书)",
				"codeNo":"QT13",
				"codeType":"其他",
				"id":"8BAAF15146E7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计专用印章登记簿及印章处置清单",
				"codeNo":"QT14",
				"codeType":"其他",
				"id":"8BAAF15146E8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"codeType":"其他",
				"id":"8BAAF15146E9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行计息和核息清单及保证金利息清单",
				"codeNo":"QT16",
				"codeType":"其他",
				"id":"8BAAF15146EAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行、联行对帐单、确认函和查询查复书",
				"codeNo":"QT17",
				"codeType":"其他",
				"id":"8BAAF15146EBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部对帐记录及其他对帐资料",
				"codeNo":"QT18",
				"codeType":"其他",
				"id":"8BAAF15146ECE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"人民银行及同业对帐单",
				"codeNo":"QT19",
				"codeType":"其他",
				"id":"8BAAF15146EDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"对帐回单",
				"codeNo":"QT20",
				"codeType":"其他",
				"id":"8BAAF15146EEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部资金往来核算资料",
				"codeNo":"QT21",
				"codeType":"其他",
				"id":"8BAAF15146EFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"财会检查档案",
				"codeNo":"QT22",
				"codeType":"其他",
				"id":"8BAAF15146F0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案查阅登记簿和申请单",
				"codeNo":"QT23",
				"codeType":"其他",
				"id":"8BAAF15146F1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计档案拆封申请单",
				"codeNo":"QT24",
				"codeType":"其他",
				"id":"8BAAF15146F2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"计算机应用系统运行日志",
				"codeNo":"QT25",
				"codeType":"其他",
				"id":"8BAAF15146F3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计电算化系统开发的全套文档资料",
				"codeNo":"QT26",
				"codeType":"其他",
				"id":"8BAAF15146F4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计档案",
				"codeNo":"QT27",
				"codeType":"其他",
				"id":"8BAAF15146F5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"投资科目分户帐",
				"codeNo":"ZB01",
				"codeType":"会计账簿",
				"id":"8BAAF15146F6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"资本金、股金及股权明细",
				"codeNo":"ZB02",
				"codeType":"会计账簿",
				"id":"8BAAF15146F7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"明细账、分户帐、卡片账未销卡清单、帐户余额表及其他明细帐",
				"codeNo":"ZB03",
				"codeType":"会计账簿",
				"id":"8BAAF15146F8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"总帐：本级及全辖汇总日计表及其他形式总帐",
				"codeNo":"ZB04",
				"codeType":"会计账簿",
				"id":"8BAAF15146F9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"codeType":"会计账簿",
				"id":"8BAAF15146FAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"已处置固定资产卡片(按报废清理后计算保管期限)",
				"codeNo":"ZB06",
				"codeType":"会计账簿",
				"id":"8BAAF15146FBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计帐簿",
				"codeNo":"ZB07",
				"codeType":"会计账簿",
				"id":"8BAAF15146FCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			}
		]
	},
	"retMsg":""
}
2025-07-22 10:57:12.181 [OrganNo_00023_UserNo_admin] [3aaa499fbc47c09b/7d9909ec710bcfbe] [http-nio-9009-exec-48] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"queryAreas"
	}
}
2025-07-22 10:57:12.183 [OrganNo_00023_UserNo_admin] [3aaa499fbc47c09b/46c0f75cbd61440f] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.F.selectByParentOrganNo - ==>  Preparing: SELECT AREA_PARENT_ORGAN_NO, AREA_CODE, AREA_NAME, AREA_DEALNO FROM FM_AREA WHERE AREA_PARENT_ORGAN_NO = ? ORDER BY AREA_CODE ASC
2025-07-22 10:57:12.183 [OrganNo_00023_UserNo_admin] [3aaa499fbc47c09b/46c0f75cbd61440f] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.F.selectByParentOrganNo - ==> Parameters: 00023(String)
2025-07-22 10:57:12.187 [OrganNo_00023_UserNo_admin] [3aaa499fbc47c09b/46c0f75cbd61440f] [http-nio-9009-exec-48] DEBUG c.s.a.f.d.F.selectByParentOrganNo - <==      Total: 1
2025-07-22 10:57:12.212 [OrganNo_00023_UserNo_admin] [3aaa499fbc47c09b/7d9909ec710bcfbe] [http-nio-9009-exec-48] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"area_name":"成都地区",
				"area_dealno":"a0",
				"area_code":"01"
			}
		]
	},
	"retMsg":"查询区域数据成功"
}
2025-07-22 10:57:25.104 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/f9b15a555a7eb1c6] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"packToTrunk",
		"archIds":"5eff8c8a5bf84fa994a16675af0dc299",
		"trunkType":"1",
		"belongYear":"2025",
		"codeNo":"BG01",
		"areaCode":"01",
		"lockCode":"20250722"
	}
}
2025-07-22 10:57:25.106 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.selectByLockNo - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE LOCK_NO = ?
2025-07-22 10:57:25.107 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.selectByLockNo - ==> Parameters: 20250722(String)
2025-07-22 10:57:25.110 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.selectByLockNo - <==      Total: 0
2025-07-22 10:57:25.114 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.FmArchMapper.selectByIds - ==>  Preparing: SELECT ID, ARCH_NO, ARCH_STATE, ARCH_TYPE, SITE_NO, SITE_NAME, START_DATE, END_DATE, YEAR_NUM, DAY_NUM, BUSINESS_DATE, WARRANT_AMOUNT, REGISTER_DATE, USER_NO, USER_NAME, REMARK, CODE_NO, CODE_NAME, TELLER_NO, TELLER_NAME, TRUNK_NO, TRANSFER_DATE, BELONG_YEAR, KEEP_YEAR, INSERT_NO, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, LATE_REASON FROM FM_ARCH WHERE ID IN ( ? )
2025-07-22 10:57:25.115 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.FmArchMapper.selectByIds - ==> Parameters: 5eff8c8a5bf84fa994a16675af0dc299(String)
2025-07-22 10:57:25.117 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.FmArchMapper.selectByIds - <==      Total: 1
2025-07-22 10:57:25.119 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectByAreaCode - ==>  Preparing: SELECT AREA_PARENT_ORGAN_NO, AREA_CODE, AREA_NAME, AREA_DEALNO FROM FM_AREA WHERE AREA_CODE = ?
2025-07-22 10:57:25.120 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectByAreaCode - ==> Parameters: 01(String)
2025-07-22 10:57:25.121 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectByAreaCode - <==      Total: 1
2025-07-22 10:57:25.122 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.FmArchMapper.selectByIds - ==>  Preparing: SELECT ID, ARCH_NO, ARCH_STATE, ARCH_TYPE, SITE_NO, SITE_NAME, START_DATE, END_DATE, YEAR_NUM, DAY_NUM, BUSINESS_DATE, WARRANT_AMOUNT, REGISTER_DATE, USER_NO, USER_NAME, REMARK, CODE_NO, CODE_NAME, TELLER_NO, TELLER_NAME, TRUNK_NO, TRANSFER_DATE, BELONG_YEAR, KEEP_YEAR, INSERT_NO, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, LATE_REASON FROM FM_ARCH WHERE ID IN ( ? )
2025-07-22 10:57:25.122 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.FmArchMapper.selectByIds - ==> Parameters: 5eff8c8a5bf84fa994a16675af0dc299(String)
2025-07-22 10:57:25.123 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.FmArchMapper.selectByIds - <==      Total: 1
2025-07-22 10:57:25.124 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectOrganDealno - ==>  Preparing: SELECT DEALNO FROM SM_ORGAN_TB WHERE ORGAN_NO = ?
2025-07-22 10:57:25.124 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectOrganDealno - ==> Parameters: 00023(String)
2025-07-22 10:57:25.125 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectOrganDealno - <==      Total: 1
2025-07-22 10:57:25.126 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==>  Preparing: SELECT CODE_NO as codeNo, CODE_NAME as codeName, KEEY_YEARS as keepYears FROM fm_unscan_code WHERE CODE_NO = ?
2025-07-22 10:57:25.127 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==> Parameters: BG01(String)
2025-07-22 10:57:25.129 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectUnscanCodes - <==      Total: 1
2025-07-22 10:57:25.129 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectByAreaCode - ==>  Preparing: SELECT AREA_PARENT_ORGAN_NO, AREA_CODE, AREA_NAME, AREA_DEALNO FROM FM_AREA WHERE AREA_CODE = ?
2025-07-22 10:57:25.129 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectByAreaCode - ==> Parameters: 01(String)
2025-07-22 10:57:25.131 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.selectByAreaCode - <==      Total: 1
2025-07-22 10:57:25.156 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.selectMaxTrunkNoByHead - ==>  Preparing: SELECT MAX(TRUNK_NO) FROM fm_unscan_trunk WHERE TRUNK_NO LIKE CONCAT(?, '%') AND TRUNK_STATE != '3'
2025-07-22 10:57:25.157 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.selectMaxTrunkNoByHead - ==> Parameters: 202501(String)
2025-07-22 10:57:25.160 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.selectMaxTrunkNoByHead - <==      Total: 1
2025-07-22 10:57:25.164 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.countByYearAndArea - ==>  Preparing: SELECT COUNT(*) FROM fm_unscan_trunk WHERE BELONG_YEAR = ? AND AREA_CODE = ? AND TRUNK_STATE != '3'
2025-07-22 10:57:25.164 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.countByYearAndArea - ==> Parameters: 2025(String), 01(String)
2025-07-22 10:57:25.168 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.countByYearAndArea - <==      Total: 1
2025-07-22 10:57:25.169 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.UnscanTrunkMapper.insert - ==>  Preparing: INSERT INTO fm_unscan_trunk ( ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-22 10:57:25.172 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.UnscanTrunkMapper.insert - ==> Parameters: 76481a989e3d45c795418c4efe3d8f35(String), 20250100006(String), 1(String), 1(String), 20250722(String), BG01(String), null, 2025(String), null, 5.0(Double), 00023(String), 中国银行四川省分行(String), admin(String), 系统超级管理员(String), null, null, null, null, null, null, null, null, null, null, 01(String), 成都地区(String), 0(String), 1(String), null, null, 20250722(String)
2025-07-22 10:57:25.176 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.dao.UnscanTrunkMapper.insert - <==    Updates: 1
2025-07-22 10:57:25.177 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.FmArchMapper.updateTrunkNo - ==>  Preparing: UPDATE FM_ARCH SET TRUNK_NO = ? WHERE ID IN ( ? )
2025-07-22 10:57:25.177 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.FmArchMapper.updateTrunkNo - ==> Parameters: 20250100006(String), 5eff8c8a5bf84fa994a16675af0dc299(String)
2025-07-22 10:57:25.179 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.FmArchMapper.updateTrunkNo - <==    Updates: 1
2025-07-22 10:57:25.180 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.updateArchState - ==>  Preparing: UPDATE FM_ARCH SET ARCH_STATE = ? WHERE ID IN ( ? )
2025-07-22 10:57:25.180 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.updateArchState - ==> Parameters: 4(String), 5eff8c8a5bf84fa994a16675af0dc299(String)
2025-07-22 10:57:25.181 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.F.updateArchState - <==    Updates: 1
2025-07-22 10:57:25.181 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.insert - ==>  Preparing: INSERT INTO fm_unscan_trunk_log ( ID, TRUNK_NO, USER_NO, USER_NAME, CREATE_DATE, CREATE_TIME, ORGAN_NO, ORGAN_NAME, DEAL_TYPE, LOCK_NO, REMARK ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-22 10:57:25.182 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.insert - ==> Parameters: b9f78cbdc9484245a8d8a1f3212af98a(String), 20250100006(String), admin(String), 系统超级管理员(String), 20250722(String), 105725(String), 00023(String), null, PACK(String), null, 档案装箱(String)
2025-07-22 10:57:25.183 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/3f5d9dc4002afb68] [http-nio-9009-exec-50] DEBUG c.s.a.f.d.U.insert - <==    Updates: 1
2025-07-22 10:57:25.200 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/f9b15a555a7eb1c6] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"箱20250100006已经成功登记"
}
2025-07-22 10:57:25.229 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/bcc7da6a8a815ad6] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-22 10:57:25.245 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/dc3f75384fccb4fb] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM FM_ARCH WHERE ARCH_STATE = ?
2025-07-22 10:57:25.245 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/dc3f75384fccb4fb] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - ==> Parameters: 3(String)
2025-07-22 10:57:25.248 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/dc3f75384fccb4fb] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:25.248 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/dc3f75384fccb4fb] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.F.selectByConditions - ==>  Preparing: SELECT ID, ARCH_NO, ARCH_STATE, ARCH_TYPE, SITE_NO, SITE_NAME, START_DATE, END_DATE, YEAR_NUM, DAY_NUM, BUSINESS_DATE, WARRANT_AMOUNT, REGISTER_DATE, USER_NO, USER_NAME, REMARK, CODE_NO, CODE_NAME, TELLER_NO, TELLER_NAME, TRUNK_NO, TRANSFER_DATE, BELONG_YEAR, KEEP_YEAR, INSERT_NO, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, LATE_REASON FROM FM_ARCH WHERE ARCH_STATE = ? ORDER BY ARCH_NO DESC LIMIT ?
2025-07-22 10:57:25.248 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/dc3f75384fccb4fb] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.F.selectByConditions - ==> Parameters: 3(String), 15(Integer)
2025-07-22 10:57:25.251 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/dc3f75384fccb4fb] [http-nio-9009-exec-51] DEBUG c.s.a.f.d.F.selectByConditions - <==      Total: 2
2025-07-22 10:57:25.274 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/bcc7da6a8a815ad6] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:25.929 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/609966ee88081105] [http-nio-9009-exec-52] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-22 10:57:25.945 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 10:57:25.945 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 1(String)
2025-07-22 10:57:25.948 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:25.948 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.U.selectByConditions - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE TRUNK_STATE = ? ORDER BY TRUNK_NO DESC LIMIT ?
2025-07-22 10:57:25.948 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.U.selectByConditions - ==> Parameters: 1(String), 15(Integer)
2025-07-22 10:57:25.951 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.U.selectByConditions - <==      Total: 2
2025-07-22 10:57:25.951 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==>  Preparing: SELECT CODE_NO as codeNo, CODE_NAME as codeName, KEEY_YEARS as keepYears FROM fm_unscan_code WHERE CODE_NO = ?
2025-07-22 10:57:25.951 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==> Parameters: BG01(String)
2025-07-22 10:57:25.952 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.F.selectUnscanCodes - <==      Total: 1
2025-07-22 10:57:25.953 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==>  Preparing: SELECT CODE_NO as codeNo, CODE_NAME as codeName, KEEY_YEARS as keepYears FROM fm_unscan_code WHERE CODE_NO = ?
2025-07-22 10:57:25.953 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==> Parameters: BG01(String)
2025-07-22 10:57:25.954 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/1ccd4ca59347b03e] [http-nio-9009-exec-52] DEBUG c.s.a.f.d.F.selectUnscanCodes - <==      Total: 1
2025-07-22 10:57:25.975 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/609966ee88081105] [http-nio-9009-exec-52] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"1",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"20250722",
				"id":"76481a989e3d45c795418c4efe3d8f35",
				"moveFlag":"0",
				"registerDate":"20250722",
				"belongYear":"2025",
				"trunkNo":"20250100006",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":5.0
			},
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:30.510 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/38b88257a32070e9] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"areaNo":"00023"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":0
	}
}
2025-07-22 10:57:30.521 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/f77b56349331529d] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - ==>  Preparing: SELECT count(0) FROM FM_WAREHOUSE WHERE AREA_NO = ?
2025-07-22 10:57:30.522 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/f77b56349331529d] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - ==> Parameters: 00023(String)
2025-07-22 10:57:30.526 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/f77b56349331529d] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.W.selectWareHouses_COUNT - <==      Total: 1
2025-07-22 10:57:30.526 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/f77b56349331529d] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.W.selectWareHouses - ==>  Preparing: select ID "ID", AREA_NO "AREA_NO", WARE_HOUSE_NO "WARE_HOUSE_NO", WARE_HOUSE_NAME "WARE_HOUSE_NAME", IS_DEFAULT "IS_DEFAULT", ADDRESS "ADDRESS", LOCATE "LOCATE", SYSTEMCODE "SYSTEMCODE", PROPERTY "PROPERTY", MANAGERMETHOD "MANAGERMETHOD", MANAGER "MANAGER", TEL "TEL", ADLEVER "ADLEVER", VALID "VALID", "full" "FULL", BEGINDAY "BEGINDAY", TIMELIMITE "TIMELIMITE", INSTRUCTION "INSTRUCTION", HOUSEAREA "HOUSEAREA", WORKAREA "WORKAREA", BEARLEVEL "BEARLEVEL" from FM_WAREHOUSE WHERE AREA_NO = ?
2025-07-22 10:57:30.526 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/f77b56349331529d] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.W.selectWareHouses - ==> Parameters: 00023(String)
2025-07-22 10:57:30.528 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/f77b56349331529d] [http-nio-9009-exec-54] DEBUG c.s.a.f.d.W.selectWareHouses - <==      Total: 1
2025-07-22 10:57:30.545 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/38b88257a32070e9] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"areaNo":"00023",
				"full":"否",
				"id":"95e031a775877f1d01758788890c0002",
				"isDefault":"0",
				"manager":"test",
				"valid":"否",
				"wareHouseName":"都江堰",
				"wareHouseNo":"cd001"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-07-22 10:57:30.575 [OrganNo_00023_UserNo_admin] [691a0583d22027b2/0b23140913c76262] [http-nio-9009-exec-55] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		
	}
}
2025-07-22 10:57:30.577 [OrganNo_00023_UserNo_admin] [691a0583d22027b2/f42f912f1d48e7b0] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.F.selectByParentOrganNo - ==>  Preparing: SELECT AREA_PARENT_ORGAN_NO, AREA_CODE, AREA_NAME, AREA_DEALNO FROM FM_AREA WHERE AREA_PARENT_ORGAN_NO = ? ORDER BY AREA_CODE ASC
2025-07-22 10:57:30.577 [OrganNo_00023_UserNo_admin] [691a0583d22027b2/f42f912f1d48e7b0] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.F.selectByParentOrganNo - ==> Parameters: 00023(String)
2025-07-22 10:57:30.579 [OrganNo_00023_UserNo_admin] [691a0583d22027b2/f42f912f1d48e7b0] [http-nio-9009-exec-55] DEBUG c.s.a.f.d.F.selectByParentOrganNo - <==      Total: 1
2025-07-22 10:57:30.588 [OrganNo_00023_UserNo_admin] [691a0583d22027b2/0b23140913c76262] [http-nio-9009-exec-55] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"area_name":"成都地区",
				"area_dealno":"a0",
				"area_code":"01"
			}
		]
	},
	"retMsg":"查询区域数据成功"
}
2025-07-22 10:57:32.116 [OrganNo_00023_UserNo_admin] [b7132dce012bade6/e79819f4177a9842] [http-nio-9009-exec-56] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"trunkNos":"20250100006"
	}
}
2025-07-22 10:57:32.118 [OrganNo_00023_UserNo_admin] [b7132dce012bade6/80a90ecbcbb42daf] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.U.selectByTrunkNo - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE TRUNK_NO = ?
2025-07-22 10:57:32.118 [OrganNo_00023_UserNo_admin] [b7132dce012bade6/80a90ecbcbb42daf] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.U.selectByTrunkNo - ==> Parameters: 20250100006(String)
2025-07-22 10:57:32.121 [OrganNo_00023_UserNo_admin] [b7132dce012bade6/80a90ecbcbb42daf] [http-nio-9009-exec-56] DEBUG c.s.a.f.d.U.selectByTrunkNo - <==      Total: 1
2025-07-22 10:57:32.146 [OrganNo_00023_UserNo_admin] [b7132dce012bade6/e79819f4177a9842] [http-nio-9009-exec-56] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"minDate":"20250722",
		"trunkCount":1,
		"maxDate":"20250722"
	},
	"retMsg":"获取打印信息成功"
}
2025-07-22 10:57:37.417 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c63a1a30e8236f26] [http-nio-9009-exec-58] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"id":"76481a989e3d45c795418c4efe3d8f35"
		}
	],
	"sysMap":{
		"oper_type":"transfer",
		"transfer":"01-成都地区",
		"receive":"cd001-都江堰"
	}
}
2025-07-22 10:57:37.420 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.selectById - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE ID = ?
2025-07-22 10:57:37.420 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.selectById - ==> Parameters: 76481a989e3d45c795418c4efe3d8f35(String)
2025-07-22 10:57:37.423 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.selectById - <==      Total: 1
2025-07-22 10:57:37.424 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.transferTrunks - ==>  Preparing: UPDATE fm_unscan_trunk SET TRUNK_STATE = '2', TRANSFER_DATE = ?, TRANSFER_ORGAN_NO = ?, TRANSFER_ORGAN_NAME = ?, TRANSFER_USER_NO = ?, TRANSFER_USER_NAME = ?, RECEIVE_WAIT_ORGAN_NO = ?, RECEIVE_WAIT_ORGAN_NAME = ? WHERE ID IN ( ? )
2025-07-22 10:57:37.425 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.transferTrunks - ==> Parameters: 20250722(String), 01(String), 成都地区(String), admin(String), 系统超级管理员(String), cd001(String), 都江堰(String), 76481a989e3d45c795418c4efe3d8f35(String)
2025-07-22 10:57:37.426 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.transferTrunks - <==    Updates: 1
2025-07-22 10:57:37.427 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.selectById - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE ID = ?
2025-07-22 10:57:37.427 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.selectById - ==> Parameters: 76481a989e3d45c795418c4efe3d8f35(String)
2025-07-22 10:57:37.428 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.selectById - <==      Total: 1
2025-07-22 10:57:37.428 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.insert - ==>  Preparing: INSERT INTO fm_unscan_trunk_log ( ID, TRUNK_NO, USER_NO, USER_NAME, CREATE_DATE, CREATE_TIME, ORGAN_NO, ORGAN_NAME, DEAL_TYPE, LOCK_NO, REMARK ) VALUES ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-07-22 10:57:37.430 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.insert - ==> Parameters: cba7766bdb4c4b49906a470ef4f10d30(String), 20250100006(String), admin(String), 系统超级管理员(String), 20250722(String), 105737(String), 00023(String), null, TRANSFER(String), null, 移交箱子到成都地区(String)
2025-07-22 10:57:37.431 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c94530eef63b99b0] [http-nio-9009-exec-58] DEBUG c.s.a.f.d.U.insert - <==    Updates: 1
2025-07-22 10:57:37.445 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c63a1a30e8236f26] [http-nio-9009-exec-58] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"移交成功"
}
2025-07-22 10:57:37.475 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/afb6df92aab372dd] [http-nio-9009-exec-59] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-22 10:57:37.488 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 10:57:37.488 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 1(String)
2025-07-22 10:57:37.491 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:37.492 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.U.selectByConditions - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE TRUNK_STATE = ? ORDER BY TRUNK_NO DESC LIMIT ?
2025-07-22 10:57:37.492 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.U.selectByConditions - ==> Parameters: 1(String), 15(Integer)
2025-07-22 10:57:37.493 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.U.selectByConditions - <==      Total: 1
2025-07-22 10:57:37.494 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==>  Preparing: SELECT CODE_NO as codeNo, CODE_NAME as codeName, KEEY_YEARS as keepYears FROM fm_unscan_code WHERE CODE_NO = ?
2025-07-22 10:57:37.494 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==> Parameters: BG01(String)
2025-07-22 10:57:37.495 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/e03404b597daa7c7] [http-nio-9009-exec-59] DEBUG c.s.a.f.d.F.selectUnscanCodes - <==      Total: 1
2025-07-22 10:57:37.507 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/afb6df92aab372dd] [http-nio-9009-exec-59] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:39.404 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/87e369fcef868616] [http-nio-9009-exec-60] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:57:39.418 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 10:57:39.418 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 2(String)
2025-07-22 10:57:39.421 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 10:57:39.421 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.U.selectByConditions - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE TRUNK_STATE = ? ORDER BY TRUNK_NO DESC LIMIT ?
2025-07-22 10:57:39.422 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.U.selectByConditions - ==> Parameters: 2(String), 15(Integer)
2025-07-22 10:57:39.423 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.U.selectByConditions - <==      Total: 1
2025-07-22 10:57:39.424 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==>  Preparing: SELECT CODE_NO as codeNo, CODE_NAME as codeName, KEEY_YEARS as keepYears FROM fm_unscan_code WHERE CODE_NO = ?
2025-07-22 10:57:39.424 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==> Parameters: BG01(String)
2025-07-22 10:57:39.425 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/49124593be791aea] [http-nio-9009-exec-60] DEBUG c.s.a.f.d.F.selectUnscanCodes - <==      Total: 1
2025-07-22 10:57:39.433 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/87e369fcef868616] [http-nio-9009-exec-60] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"1",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"2",
				"transferDate":"20250722",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"transferOrganNo":"01",
				"lockNo":"20250722",
				"id":"76481a989e3d45c795418c4efe3d8f35",
				"moveFlag":"0",
				"registerDate":"20250722",
				"belongYear":"2025",
				"receiveWaitOrganNo":"cd001",
				"trunkNo":"20250100006",
				"userName":"系统超级管理员",
				"transferUserNo":"admin",
				"areaCode":"01",
				"codeNo":"BG01",
				"transferUserName":"系统超级管理员",
				"yearNum":5.0,
				"transferOrganName":"成都地区",
				"receiveWaitOrganName":"都江堰"
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 14:27:14.866 [OrganNo_00023_UserNo_admin] [cace257215f71602/ac8645125a0bd3da] [http-nio-9009-exec-62] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-22 14:27:14.904 [OrganNo_00023_UserNo_admin] [cace257215f71602/04b83cfb3a374316] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM FM_ARCH WHERE ARCH_STATE = ?
2025-07-22 14:27:14.905 [OrganNo_00023_UserNo_admin] [cace257215f71602/04b83cfb3a374316] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - ==> Parameters: 3(String)
2025-07-22 14:27:14.911 [OrganNo_00023_UserNo_admin] [cace257215f71602/04b83cfb3a374316] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.F.selectByConditions_COUNT - <==      Total: 1
2025-07-22 14:27:14.911 [OrganNo_00023_UserNo_admin] [cace257215f71602/04b83cfb3a374316] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.F.selectByConditions - ==>  Preparing: SELECT ID, ARCH_NO, ARCH_STATE, ARCH_TYPE, SITE_NO, SITE_NAME, START_DATE, END_DATE, YEAR_NUM, DAY_NUM, BUSINESS_DATE, WARRANT_AMOUNT, REGISTER_DATE, USER_NO, USER_NAME, REMARK, CODE_NO, CODE_NAME, TELLER_NO, TELLER_NAME, TRUNK_NO, TRANSFER_DATE, BELONG_YEAR, KEEP_YEAR, INSERT_NO, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, LATE_REASON FROM FM_ARCH WHERE ARCH_STATE = ? ORDER BY ARCH_NO DESC LIMIT ?
2025-07-22 14:27:14.912 [OrganNo_00023_UserNo_admin] [cace257215f71602/04b83cfb3a374316] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.F.selectByConditions - ==> Parameters: 3(String), 15(Integer)
2025-07-22 14:27:14.915 [OrganNo_00023_UserNo_admin] [cace257215f71602/04b83cfb3a374316] [http-nio-9009-exec-62] DEBUG c.s.a.f.d.F.selectByConditions - <==      Total: 2
2025-07-22 14:27:14.947 [OrganNo_00023_UserNo_admin] [cace257215f71602/ac8645125a0bd3da] [http-nio-9009-exec-62] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 14:27:14.979 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/7d2707e2f47d8a1c] [http-nio-9009-exec-63] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-22 14:27:14.992 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==>  Preparing: SELECT count(0) FROM fm_unscan_trunk WHERE TRUNK_STATE = ?
2025-07-22 14:27:14.992 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - ==> Parameters: 1(String)
2025-07-22 14:27:14.996 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.U.selectByConditions_COUNT - <==      Total: 1
2025-07-22 14:27:14.997 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.U.selectByConditions - ==>  Preparing: SELECT ID, TRUNK_NO, TRUNK_STATE, ARCH_COUNT, REGISTER_DATE, CODE_NO, CODE_NAME, BELONG_YEAR, KEEP_YEAR, YEAR_NUM, SITE_NO, SITE_NAME, USER_NO, USER_NAME, TRANSFER_DATE, TRANSFER_ORGAN_NO, TRANSFER_ORGAN_NAME, TRANSFER_USER_NO, TRANSFER_USER_NAME, RECEIVE_DATE, RECEIVE_ORGAN_NO, RECEIVE_ORGAN_NAME, RECEIVE_USER_NO, RECEIVE_USER_NAME, AREA_CODE, AREA_NAME, MOVE_FLAG, TRUNK_TYPE, RECEIVE_WAIT_ORGAN_NO, RECEIVE_WAIT_ORGAN_NAME, LOCK_NO FROM fm_unscan_trunk WHERE TRUNK_STATE = ? ORDER BY TRUNK_NO DESC LIMIT ?
2025-07-22 14:27:14.997 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.U.selectByConditions - ==> Parameters: 1(String), 15(Integer)
2025-07-22 14:27:14.999 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.U.selectByConditions - <==      Total: 1
2025-07-22 14:27:14.999 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==>  Preparing: SELECT CODE_NO as codeNo, CODE_NAME as codeName, KEEY_YEARS as keepYears FROM fm_unscan_code WHERE CODE_NO = ?
2025-07-22 14:27:15.000 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.F.selectUnscanCodes - ==> Parameters: BG01(String)
2025-07-22 14:27:15.001 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/50f04005be4cac01] [http-nio-9009-exec-63] DEBUG c.s.a.f.d.F.selectUnscanCodes - <==      Total: 1
2025-07-22 14:27:15.023 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/7d2707e2f47d8a1c] [http-nio-9009-exec-63] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 14:27:18.327 [OrganNo_00023_UserNo_admin] [782d6d457d883dab/d0e48b213dd1f0b4] [http-nio-9009-exec-65] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"code":"20250100002"
	}
}
2025-07-22 14:27:18.389 [OrganNo_00023_UserNo_admin] [782d6d457d883dab/d0e48b213dd1f0b4] [http-nio-9009-exec-65] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"root":[
			{
				"fileName":"20250100002.png",
				"src":"/barCodeImgs/trunk/20250100002.png"
			},
			{
				"fileName":"20250100003.png",
				"src":"/barCodeImgs/trunk/20250100003.png"
			},
			{
				"fileName":"20250100004.png",
				"src":"/barCodeImgs/trunk/20250100004.png"
			},
			{
				"fileName":"20250100005.png",
				"src":"/barCodeImgs/trunk/20250100005.png"
			},
			{
				"fileName":"20250100006.png",
				"src":"/barCodeImgs/trunk/20250100006.png"
			},
			{
				"fileName":"20250100007.png",
				"src":"/barCodeImgs/trunk/20250100007.png"
			},
			{
				"fileName":"20250100008.png",
				"src":"/barCodeImgs/trunk/20250100008.png"
			},
			{
				"fileName":"20250100009.png",
				"src":"/barCodeImgs/trunk/20250100009.png"
			},
			{
				"fileName":"20250100010.png",
				"src":"/barCodeImgs/trunk/20250100010.png"
			},
			{
				"fileName":"20250100011.png",
				"src":"/barCodeImgs/trunk/20250100011.png"
			}
		]
	},
	"retMsg":"打印箱号准备完成"
}
