<template>
  <div>
	<el-row :gutter="32">
		<el-col :xs="24" :sm="24" :lg="10">
			<div :id="canvas3"  :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
	 </el-row>
  </div>
</template>


<style>
</style>

<script>
import {getSchedulerNumofMonth} from '@/api/monitorManage'
import * as echarts from 'echarts'

export default {
  name: "scheduler-month-rel",
    props: {
	listQuery: {
      require: true,
      type: Object,
    },
    canvas3: {
      type: String,
      default: 'chart3'
    },
    width: {
      type: String,
      default: '1100px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
      default: '16px 16px'
    }
  },
  data() {
    return{
        chart3:null,
        option3:null,
        time:null,
		days : [],
        migrateF : [], 
        migrateS : [],
        clearF:[],
        clearS:[],
        offlineF:[],
        offlineS:[],
        offlineCF:[],
        offlineCS:[],
        container:[],
        line:['迁移','迁移清理','离线','离线清理'],
		// container:[migrateS,migrateF,clearS,clearF,offlineS,offlineF,offlineCS,offlineCF],
        monthQuery: {
			server_id: "",
			model_code: "",
			date: ""
      	}
	}
  },
  
    mounted() {
      this.$nextTick(() => {
		//   this.checkInit();
      })
    },


  methods: {
	getDays() {
        let selYear = this.listQuery.month.split("-")[0];
		let selMonth = this.listQuery.month.split("-")[1];
		this.time = new Date(selYear,selMonth, 0).getDate();
		let temp = [];
		for (let i = 1; i <= this.time; i++) {
			temp.push(i);
		}
		return temp;
	},
    showChart(){
        this.chart3 = echarts.init(document.getElementById(this.canvas3),'dark');
		this.chart3.showLoading({
            text : "数据正在努力加载..."
        });
        this.option3 = {
                timeline : {
                    data : this.line,
                    axisType: 'category',
                    bottom : '3%'
                },

                baseOption : {},
                options : []
        };
        this.chart3.setOption(this.option3);
    },

    showData(){
        this.days = this.getDays();
        this.showChart();
        this.monthQuery.server_id = this.listQuery.server_id;
		this.monthQuery.model_code = this.listQuery.model_code;
		this.monthQuery.date = this.listQuery.month.replaceAll("-","");
        this.container = [this.migrateS,this.migrateF,this.clearS,this.clearF,this.offlineS,this.offlineF,this.offlineCS,this.offlineCF]
        getSchedulerNumofMonth(this.monthQuery).then(response => {
            this.chart3.hideLoading(); 
            let scheduler=['M','MC','O','OC'];
            for(let s=0;s<scheduler.length;s++){
			    for (let i = 1; i <= this.time; i++) {
                    if (i <= 9) {
                        i = '0' + i;
                    }
                        if (response[''+scheduler[s]][''+i]!= "undefined"&&response[''+scheduler[s]][''+i]!= null) {
                            this.container[s*2].push(response[''+scheduler[s]][''+i].successTask);
                        this.container[s*2+1].push(response[''+scheduler[s]][''+i].failTask);
                    } else {
                        this.container[s*2].push(0);
                        this.container[s*2+1].push(0);
                    } 
            }
            }
            for(let t=0;t<this.line.length;t++){
                this.option3.options.push({
                    title : {
                        textStyle : {
                            color : '#e2e9ff',
                        },
                        x : 'center',
                        text : '服务器组定时任务数据月度统计'
                    },
                    legend : {
                        textStyle : {
                            color : '#e2e9ff'
                        },
                        x : 'right',
                        data : [ '成功', '失败' ]
                    },
                    tooltip : {
                        trigger : 'axis',
                        axisPointer : {
                            type : 'shadow',
                        }
                    },
                    grid : { //控制图的大小
                        left : '3%',
                        right : '5%',
                        bottom : '17%',
                        containLabel : true
                    },
                    xAxis : [ {
                        name : 'Day',
                        axisLine : {
                            lineStyle : {
                                color : 'rgba(255,255,255,0.12)'
                            }
                        },
                        axisLabel : {
                            margin : 10,
                            color : '#e2e9ff',
                            textStyle : {
                                fontSize : 12
                            },
                        },
                        axisLine : {
                            lineStyle : {
                                color : 'rgba(255,255,255,0.12)'
                            }
                        },
                        axisLabel : {
                            margin : 10,
                            color : '#e2e9ff',
                            textStyle : {
                                fontSize : 12
                            },
                        },
                        type : 'category',
                        data : this.days
                    } ],
                    yAxis : [ {
                        name : '(笔)',
                        nameTextStyle : {
                            color : "#e2e9ff"
                        },
                        type : 'value',
                        axisLabel : {
                            formatter : '{value}',
                            color : '#e2e9ff',
                        },
                        axisLine : {
                            show : false
                        },
                        splitLine : {
                            lineStyle : {
                                color : 'rgba(255,255,255,0.12)'
                            }
                        },
                        type : 'value'
                    } ],
                    series : [
                            {
                                name : '成功',
                                type : 'bar',
                                barWidth : '35%',
                                stack : '数量',
                                itemStyle : {
                                    normal : {
                                        color : new echarts.graphic.LinearGradient(0,0,0,1,
                                                [{
                                                    offset : 0,
                                                    color : 'rgba(0,244,255,1)' // 0% 处的颜色
                                                },
                                                {
                                                    offset : 1,
                                                    color : 'rgba(0,77,167,1)' // 100% 处的颜色
                                                } ], false),
                                        barBorderRadius : [ 30, 30, 30, 30 ],
                                        shadowColor : 'rgba(0,160,221,1)',
                                        shadowBlur : 4,
                                    }
                                },
                                data : this.container[t*2]
                            },
                            {
                                name : '失败',
                                type : 'bar',
                                barWidth : '35%',
                                stack : '数量',
                                itemStyle : {
                                    normal : {
                                        color : new echarts.graphic.LinearGradient(0,0,0,1,
                                                [{
                                                    offset : 0,
                                                    color : 'rgba(238,93,2,1)' // 0% 处的颜色
                                                },
                                                {
                                                    offset : 1,
                                                    color : 'rgba(237,206,53,1)' // 100% 处的颜色
                                                } ], false),
                                        barBorderRadius : [ 20, 20, 20,20 ],
                                        shadowColor : 'rgba(0,160,221,1)',
                                        shadowBlur : 4,
                                    }
                                },
                                data : this.container[t*2+1]
                            } ]
                })
            }
            this.chart3.setOption(this.option3);
        })

    }
  }
};
</script>
<style scoped>

</style>