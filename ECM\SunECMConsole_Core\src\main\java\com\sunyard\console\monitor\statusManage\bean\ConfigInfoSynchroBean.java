package com.sunyard.console.monitor.statusManage.bean;

/**
 * <p>Title: 同步信息bean</p>
 * <p>Description: 对应数据库中CONFIG_INFO_SYNCHRO表</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ConfigInfoSynchroBean {
	
	private int config_id;
	private String config_type; // 配置信息类型
	private String config_table; // 配置信息所属的表
	private String synchro_status; // 同步是否成功
	private String config_code; // 所属的配置信息表中对应的唯一编号
	private String remark;    //备注
	private String modify_type; // 修改类型
	private int node_id; // 节点编号
	private String server_name; // 节点名
	private String server_ip; // 节点IP
	private String synchro_date;    //同步时间
	private int target_server_type;// 1：内容存储服务器 2：统一接入
	
	
	public ConfigInfoSynchroBean() {
	}
	
	
	public ConfigInfoSynchroBean(String configType, String configTable,
                                 String synchroStatus, String configCode, String remark,
                                 String modifyType, int nodeId) {
		config_type = configType;
		config_table = configTable;
		synchro_status = synchroStatus;
		config_code = configCode;
		this.remark = remark;
		modify_type = modifyType;
		node_id = nodeId;
	}


	public int getConfig_id() {
		return config_id;
	}
	public void setConfig_id(int configId) {
		config_id = configId;
	}
	public String getConfig_type() {
		return config_type;
	}
	public void setConfig_type(String configType) {
		config_type = configType;
	}
	
	public int getTarget_server_type() {
		return target_server_type;
	}


	public void setTarget_server_type(int targetServerType) {
		target_server_type = targetServerType;
	}


	public String getConfig_table() {
		return config_table;
	}
	public void setConfig_table(String configTable) {
		config_table = configTable;
	}
	public String getSynchro_status() {
		return synchro_status;
	}
	public void setSynchro_status(String synchroStatus) {
		synchro_status = synchroStatus;
	}
	public String getConfig_code() {
		return config_code;
	}
	public void setConfig_code(String configCode) {
		config_code = configCode;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getModify_type() {
		return modify_type;
	}
	public void setModify_type(String modifyType) {
		modify_type = modifyType;
	}
	public int getNode_id() {
		return node_id;
	}
	public void setNode_id(int nodeId) {
		node_id = nodeId;
	}
	public String getServer_name() {
		return server_name;
	}
	public void setServer_name(String serverName) {
		server_name = serverName;
	}
	public String getServer_ip() {
		return server_ip;
	}
	public void setServer_ip(String serverIp) {
		server_ip = serverIp;
	}
	public String getSynchro_date() {
		return synchro_date;
	}
	public void setSynchro_date(String synchroDate) {
		synchro_date = synchroDate;
	}
	
	public String toString(){
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append("config_id:").append(config_id);
		sBuilder.append(";config_type:").append(config_type);
		sBuilder.append(";config_table:").append(config_table);
		sBuilder.append(";synchro_status:").append(synchro_status);
		sBuilder.append(";config_code:").append(config_code);
		sBuilder.append(";remark:").append(remark);
		sBuilder.append(";modify_type:").append(modify_type);
		sBuilder.append(";node_id:").append(node_id);
		sBuilder.append(";server_name:").append(server_name);
		sBuilder.append(";server_ip:").append(server_ip);
		sBuilder.append(";synchro_date:").append(synchro_date);
		sBuilder.append(";target_server_type:").append(target_server_type);
		return sBuilder.toString();
	}
}
