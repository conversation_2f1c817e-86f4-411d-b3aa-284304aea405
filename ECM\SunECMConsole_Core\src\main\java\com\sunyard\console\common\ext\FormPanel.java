package com.sunyard.console.common.ext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>Title:  表单类</p>
 * <p>Description: 存放创建的动态表单</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
public class FormPanel {
	
//	private List<Map<String,?>>			items		= null;	//一个元素或元素数组 
//	private List<Map<String,String>>	buttons		= null;	//一个按钮配置对象的数组，按钮将被添加到表单页脚中 
//	private String						buttonAlign	= null;	//按钮的对齐方式，可选值有left、center、right，默认为center 
//	private int							labelWidth	= 0;	//表单标签的宽度 
//	private String						labelAlign	= null;	//表单标签的对齐方式，可选值有left、top、right，默认为left 
//	private String						labelSeparator	= null;	//字段标签与字段之间的分隔符，默认为':' 
//	private int							minButtonWidth	= 0;	//按钮的最小宽度，默认为75 
	
	private Map<String,Object> formPanel = new HashMap<String,Object>();
	
	public List<Map<String, Object>> getItems() {
		return formPanel.get("items") == null ? null : (List<Map<String, Object>>)formPanel.get("items");
	}
	/**
	 * formPanel的组件
	 * @param items
	 */
	public void setItems(List<Map<String, Object>> items) {
		formPanel.put("items", items);
	}
	/**
	 * formPanel的组件
	 * @param items
	 */
	public void addItems(Map<String, ?> items) {
		if(formPanel.get("items") == null){
			setItems(new ArrayList<Map<String, Object>>());
		}
		((List<Map<String, ?>>)formPanel.get("items")).add(items);
	}
	

	public List<Map<String, String>> getButtons() {
		return formPanel.get("buttons") == null ? null : (List<Map<String, String>>)formPanel.get("buttons");
	}
	/**
	 * 设置按钮
	 * @param buttons
	 */
	public void setButtons(List<Map<String, String>> buttons) {
		formPanel.put("buttons", buttons);
	}
	public void addButton(Map<String, Object> button) {
		if(formPanel.get("buttons") == null){
			setPropertiesObject("buttons", (new ArrayList<Map<String, Object>>()));
		}
		((List<Map<String, Object>>)formPanel.get("buttons")).add(button);
	}
	
	public String getButtonAlign() {
		return formPanel.get("buttonAlign") == null ? null : String.valueOf(formPanel.get("buttonAlign"));
	}
	/**
	 * 按钮居中方式
	 * @return
	 */
	public void setButtonAlign(String buttonAlign) {
		formPanel.put("buttonAlign", buttonAlign);
	}

	public String getLabelWidth() {
		return formPanel.get("labelWidth") == null ? null : String.valueOf(formPanel.get("labelWidth"));
	}

	public void setLabelWidth(int labelWidth) {
		formPanel.put("labelWidth", labelWidth);
	}

	public String getLabelAlign() {
		return formPanel.get("labelAlign") == null ? null : String.valueOf(formPanel.get("labelAlign"));
	}

	public void setLabelAlign(String labelAlign) {
		formPanel.put("labelAlign", labelAlign);
	}

	public String getLabelSeparator() {
		return formPanel.get("labelSeparator") == null ? null : String.valueOf(formPanel.get("labelSeparator"));
	}

	public void setLabelSeparator(String labelSeparator) {
		formPanel.put("labelSeparator", labelSeparator);
	}

	public String getMinButtonWidth() {
		return formPanel.get("minButtonWidth") == null ? null : String.valueOf(formPanel.get("minButtonWidth"));
	}

	public void setMinButtonWidth(int minButtonWidth) {
		formPanel.put("minButtonWidth", minButtonWidth);
	}

	public Map<String, Object> getFormPanel() {
		return formPanel;
	}

	public void setFormPanel(Map<String, Object> formPanel) {
		this.formPanel = formPanel;
	}
	
	/**
	 * 设置其他属性
	 * @param key
	 * @param value
	 */
	public void setProperties(String key , String value){
		formPanel.put(key, value);
	}
	public void setProperties(String key , int value){
		formPanel.put(key, value);
	}
	public void setProperties(String key , boolean value){
		formPanel.put(key, value);
	}
	public void setProperties_obj(String key , String value){
		formPanel.put(key, "#"+value+"#");
	}
	public void setPropertiesObject(String key , Object value){
		formPanel.put(key, value);
	}
	/**
	 * 获取某个属性
	 * @param key
	 * @return
	 */
	public Object getProperties(String key){
		return formPanel.get(key);
	}
	
	public void removeProperties(String key){
		formPanel.remove(key);
	}

}
