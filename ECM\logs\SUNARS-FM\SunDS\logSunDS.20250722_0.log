2025-07-22 10:55:32.221 [OrganNo_00023_UserNo_admin] [d158a8be3471f2ae/d0eec9f99d918b16] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:55:32.278 [OrganNo_00023_UserNo_admin] [d158a8be3471f2ae/d0eec9f99d918b16] [http-nio-9009-exec-40] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:55:32.279 [] [d158a8be3471f2ae/d0eec9f99d918b16] [http-nio-9009-exec-40] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:55:32 操作结束时间: 2025-07-22 10:55:32!总共花费时间: 69 毫秒！
2025-07-22 10:57:04.646 [OrganNo_00023_UserNo_admin] [96a72c41d6932fdd/41f540dbd7abfaed] [http-nio-9009-exec-41] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:57:04.686 [OrganNo_00023_UserNo_admin] [96a72c41d6932fdd/41f540dbd7abfaed] [http-nio-9009-exec-41] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:04.687 [] [96a72c41d6932fdd/41f540dbd7abfaed] [http-nio-9009-exec-41] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:04 操作结束时间: 2025-07-22 10:57:04!总共花费时间: 52 毫秒！
2025-07-22 10:57:05.331 [OrganNo_00023_UserNo_admin] [acf3c932a7781f5f/1d61e108484c07fa] [http-nio-9009-exec-42] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:57:05.360 [OrganNo_00023_UserNo_admin] [acf3c932a7781f5f/1d61e108484c07fa] [http-nio-9009-exec-42] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:05.361 [] [acf3c932a7781f5f/1d61e108484c07fa] [http-nio-9009-exec-42] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:05 操作结束时间: 2025-07-22 10:57:05!总共花费时间: 50 毫秒！
2025-07-22 10:57:05.484 [OrganNo_00023_UserNo_admin] [2107d14b4cf32670/ee5601fcfc20de12] [http-nio-9009-exec-43] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:57:05.514 [OrganNo_00023_UserNo_admin] [2107d14b4cf32670/ee5601fcfc20de12] [http-nio-9009-exec-43] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			
		],
		"pageSize":15,
		"totalCount":0,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:05.515 [] [2107d14b4cf32670/ee5601fcfc20de12] [http-nio-9009-exec-43] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:05 操作结束时间: 2025-07-22 10:57:05!总共花费时间: 46 毫秒！
2025-07-22 10:57:06.087 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/a8f30f5a9f39c6f6] [http-nio-9009-exec-44] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-22 10:57:06.130 [OrganNo_00023_UserNo_admin] [22f0d8a929b3ad17/a8f30f5a9f39c6f6] [http-nio-9009-exec-44] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT06-2025-000023-99-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"dayNum":1,
				"endDate":"20251231",
				"id":"5eff8c8a5bf84fa994a16675af0dc299",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250715",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"22",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":3,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:06.131 [] [22f0d8a929b3ad17/a8f30f5a9f39c6f6] [http-nio-9009-exec-44] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:06 操作结束时间: 2025-07-22 10:57:06!总共花费时间: 61 毫秒！
2025-07-22 10:57:06.175 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/aa2af5631b1895ca] [http-nio-9009-exec-45] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-22 10:57:06.206 [OrganNo_00023_UserNo_admin] [69d48209af0593e0/aa2af5631b1895ca] [http-nio-9009-exec-45] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:06.206 [] [69d48209af0593e0/aa2af5631b1895ca] [http-nio-9009-exec-45] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:06 操作结束时间: 2025-07-22 10:57:06!总共花费时间: 53 毫秒！
2025-07-22 10:57:12.127 [OrganNo_00023_UserNo_admin] [e69bff158e1232a5/110257a8f96249dd] [http-nio-9009-exec-47] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"getUnscanCodeList"
	}
}
2025-07-22 10:57:12.151 [OrganNo_00023_UserNo_admin] [e69bff158e1232a5/110257a8f96249dd] [http-nio-9009-exec-47] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"codeNo":"BG01",
				"codeType":"财务报告",
				"id":"8BAAF15146D3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"中期财务报告:本级及全辖月\\季\\半年财务报告",
				"codeNo":"BG02",
				"codeType":"财务报告",
				"id":"8BAAF15146D4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"下级机构上报的财务报告",
				"codeNo":"BG03",
				"codeType":"财务报告",
				"id":"8BAAF15146D5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"不定期报表",
				"codeNo":"BG04",
				"codeType":"财务报告",
				"id":"8BAAF15146D6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他财务报告",
				"codeNo":"BG05",
				"codeType":"财务报告",
				"id":"8BAAF15146D7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"特殊介质保存的会计资料",
				"codeNo":"JZ01",
				"codeType":"特殊介质",
				"id":"8BAAF15146D8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"会计凭证",
				"codeNo":"PZ01",
				"codeType":"会计凭证",
				"id":"8BAAF15146D9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"其他会计凭证",
				"codeNo":"PZ02",
				"codeType":"会计凭证",
				"id":"8BAAF15146DAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"挂失登记及补发凭单收据",
				"codeNo":"QT01",
				"codeType":"其他",
				"id":"8BAAF15146DBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"存\\贷款开销户记录",
				"codeNo":"QT02",
				"codeType":"其他",
				"id":"8BAAF15146DCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"有权机关查询\\冻结\\扣划公函资料和登记簿",
				"codeNo":"QT03",
				"codeType":"其他",
				"id":"8BAAF15146DDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"帐销案存记录",
				"codeNo":"QT04",
				"codeType":"其他",
				"id":"8BAAF15146DEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"codeType":"其他",
				"id":"8BAAF15146DFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"长期不动户清单\\长期未解付汇款清单\\长期未解付银行汇票清单",
				"codeNo":"QT06",
				"codeType":"其他",
				"id":"8BAAF15146E0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案保管登记簿",
				"codeNo":"QT07",
				"codeType":"其他",
				"id":"8BAAF15146E1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"销毁清册及相关审批资料",
				"codeNo":"QT08",
				"codeType":"其他",
				"id":"8BAAF15146E2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"机构变动交接清册",
				"codeNo":"QT09",
				"codeType":"其他",
				"id":"8BAAF15146E3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"重要单证\\有价凭证\\印章的领发\\保管和缴销记录",
				"codeNo":"QT10",
				"codeType":"其他",
				"id":"8BAAF15146E4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计档案移交清册",
				"codeNo":"QT11",
				"codeType":"其他",
				"id":"8BAAF15146E5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计人员交接清册",
				"codeNo":"QT12",
				"codeType":"其他",
				"id":"8BAAF15146E6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计事后监督档案(含差错通知书)",
				"codeNo":"QT13",
				"codeType":"其他",
				"id":"8BAAF15146E7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"会计专用印章登记簿及印章处置清单",
				"codeNo":"QT14",
				"codeType":"其他",
				"id":"8BAAF15146E8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"密押代号使用保管登记簿",
				"codeNo":"QT15",
				"codeType":"其他",
				"id":"8BAAF15146E9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行计息和核息清单及保证金利息清单",
				"codeNo":"QT16",
				"codeType":"其他",
				"id":"8BAAF15146EAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"代理行、联行对帐单、确认函和查询查复书",
				"codeNo":"QT17",
				"codeType":"其他",
				"id":"8BAAF15146EBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部对帐记录及其他对帐资料",
				"codeNo":"QT18",
				"codeType":"其他",
				"id":"8BAAF15146ECE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"人民银行及同业对帐单",
				"codeNo":"QT19",
				"codeType":"其他",
				"id":"8BAAF15146EDE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"对帐回单",
				"codeNo":"QT20",
				"codeType":"其他",
				"id":"8BAAF15146EEE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"内部资金往来核算资料",
				"codeNo":"QT21",
				"codeType":"其他",
				"id":"8BAAF15146EFE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"财会检查档案",
				"codeNo":"QT22",
				"codeType":"其他",
				"id":"8BAAF15146F0E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"会计档案查阅登记簿和申请单",
				"codeNo":"QT23",
				"codeType":"其他",
				"id":"8BAAF15146F1E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计档案拆封申请单",
				"codeNo":"QT24",
				"codeType":"其他",
				"id":"8BAAF15146F2E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"计算机应用系统运行日志",
				"codeNo":"QT25",
				"codeType":"其他",
				"id":"8BAAF15146F3E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"会计电算化系统开发的全套文档资料",
				"codeNo":"QT26",
				"codeType":"其他",
				"id":"8BAAF15146F4E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计档案",
				"codeNo":"QT27",
				"codeType":"其他",
				"id":"8BAAF15146F5E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			},
			{
				"codeName":"投资科目分户帐",
				"codeNo":"ZB01",
				"codeType":"会计账簿",
				"id":"8BAAF15146F6E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"资本金、股金及股权明细",
				"codeNo":"ZB02",
				"codeType":"会计账簿",
				"id":"8BAAF15146F7E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"99"
			},
			{
				"codeName":"明细账、分户帐、卡片账未销卡清单、帐户余额表及其他明细帐",
				"codeNo":"ZB03",
				"codeType":"会计账簿",
				"id":"8BAAF15146F8E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"总帐：本级及全辖汇总日计表及其他形式总帐",
				"codeNo":"ZB04",
				"codeType":"会计账簿",
				"id":"8BAAF15146F9E67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"codeType":"会计账簿",
				"id":"8BAAF15146FAE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"30"
			},
			{
				"codeName":"已处置固定资产卡片(按报废清理后计算保管期限)",
				"codeNo":"ZB06",
				"codeType":"会计账簿",
				"id":"8BAAF15146FBE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"10"
			},
			{
				"codeName":"其他会计帐簿",
				"codeNo":"ZB07",
				"codeType":"会计账簿",
				"id":"8BAAF15146FCE67CE05329086015E6F5",
				"isDefault":"0",
				"keepYears":"未定"
			}
		]
	},
	"retMsg":""
}
2025-07-22 10:57:12.152 [] [e69bff158e1232a5/110257a8f96249dd] [http-nio-9009-exec-47] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 获取档案分类代码!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:12 操作结束时间: 2025-07-22 10:57:12!总共花费时间: 34 毫秒！
2025-07-22 10:57:12.181 [OrganNo_00023_UserNo_admin] [3aaa499fbc47c09b/7d9909ec710bcfbe] [http-nio-9009-exec-48] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"queryAreas"
	}
}
2025-07-22 10:57:12.212 [OrganNo_00023_UserNo_admin] [3aaa499fbc47c09b/7d9909ec710bcfbe] [http-nio-9009-exec-48] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"area_name":"成都地区",
				"area_dealno":"a0",
				"area_code":"01"
			}
		]
	},
	"retMsg":"查询区域数据成功"
}
2025-07-22 10:57:12.212 [] [3aaa499fbc47c09b/7d9909ec710bcfbe] [http-nio-9009-exec-48] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询区域数据!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:12 操作结束时间: 2025-07-22 10:57:12!总共花费时间: 45 毫秒！
2025-07-22 10:57:25.104 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/f9b15a555a7eb1c6] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"packToTrunk",
		"archIds":"5eff8c8a5bf84fa994a16675af0dc299",
		"trunkType":"1",
		"belongYear":"2025",
		"codeNo":"BG01",
		"areaCode":"01",
		"lockCode":"20250722"
	}
}
2025-07-22 10:57:25.200 [OrganNo_00023_UserNo_admin] [de8ff6ac91558f9e/f9b15a555a7eb1c6] [http-nio-9009-exec-50] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"箱20250100006已经成功登记"
}
2025-07-22 10:57:25.200 [] [de8ff6ac91558f9e/f9b15a555a7eb1c6] [http-nio-9009-exec-50] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 档案装箱!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:25 操作结束时间: 2025-07-22 10:57:25!总共花费时间: 106 毫秒！
2025-07-22 10:57:25.229 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/bcc7da6a8a815ad6] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-22 10:57:25.274 [OrganNo_00023_UserNo_admin] [38d590616d1a0563/bcc7da6a8a815ad6] [http-nio-9009-exec-51] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:25.275 [] [38d590616d1a0563/bcc7da6a8a815ad6] [http-nio-9009-exec-51] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:25 操作结束时间: 2025-07-22 10:57:25!总共花费时间: 56 毫秒！
2025-07-22 10:57:25.929 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/609966ee88081105] [http-nio-9009-exec-52] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-22 10:57:25.975 [OrganNo_00023_UserNo_admin] [fff6801544bb835a/609966ee88081105] [http-nio-9009-exec-52] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"1",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"20250722",
				"id":"76481a989e3d45c795418c4efe3d8f35",
				"moveFlag":"0",
				"registerDate":"20250722",
				"belongYear":"2025",
				"trunkNo":"20250100006",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":5.0
			},
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:25.976 [] [fff6801544bb835a/609966ee88081105] [http-nio-9009-exec-52] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:25 操作结束时间: 2025-07-22 10:57:25!总共花费时间: 56 毫秒！
2025-07-22 10:57:30.510 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/38b88257a32070e9] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"areaNo":"00023"
		}
	],
	"sysMap":{
		"oper_type":"OP004",
		"currentPage":-1,
		"pageSize":0
	}
}
2025-07-22 10:57:30.545 [OrganNo_00023_UserNo_admin] [0dce280faf42c0b6/38b88257a32070e9] [http-nio-9009-exec-54] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"areaNo":"00023",
				"full":"否",
				"id":"95e031a775877f1d01758788890c0002",
				"isDefault":"0",
				"manager":"test",
				"valid":"否",
				"wareHouseName":"都江堰",
				"wareHouseNo":"cd001"
			}
		],
		"totalCount":1,
		"currentPage":-1
	},
	"retMsg":"查询成功!"
}
2025-07-22 10:57:30.545 [] [0dce280faf42c0b6/38b88257a32070e9] [http-nio-9009-exec-54] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 库房定义 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:30 操作结束时间: 2025-07-22 10:57:30!总共花费时间: 46 毫秒！
2025-07-22 10:57:30.575 [OrganNo_00023_UserNo_admin] [691a0583d22027b2/0b23140913c76262] [http-nio-9009-exec-55] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		
	}
}
2025-07-22 10:57:30.588 [OrganNo_00023_UserNo_admin] [691a0583d22027b2/0b23140913c76262] [http-nio-9009-exec-55] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"area_name":"成都地区",
				"area_dealno":"a0",
				"area_code":"01"
			}
		]
	},
	"retMsg":"查询区域数据成功"
}
2025-07-22 10:57:30.589 [] [691a0583d22027b2/0b23140913c76262] [http-nio-9009-exec-55] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询区域数据!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:30 操作结束时间: 2025-07-22 10:57:30!总共花费时间: 32 毫秒！
2025-07-22 10:57:32.116 [OrganNo_00023_UserNo_admin] [b7132dce012bade6/e79819f4177a9842] [http-nio-9009-exec-56] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"trunkNos":"20250100006"
	}
}
2025-07-22 10:57:32.146 [OrganNo_00023_UserNo_admin] [b7132dce012bade6/e79819f4177a9842] [http-nio-9009-exec-56] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"minDate":"20250722",
		"trunkCount":1,
		"maxDate":"20250722"
	},
	"retMsg":"获取打印信息成功"
}
2025-07-22 10:57:32.146 [] [b7132dce012bade6/e79819f4177a9842] [http-nio-9009-exec-56] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 获取打印信息!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:32 操作结束时间: 2025-07-22 10:57:32!总共花费时间: 41 毫秒！
2025-07-22 10:57:37.417 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c63a1a30e8236f26] [http-nio-9009-exec-58] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"id":"76481a989e3d45c795418c4efe3d8f35"
		}
	],
	"sysMap":{
		"oper_type":"transfer",
		"transfer":"01-成都地区",
		"receive":"cd001-都江堰"
	}
}
2025-07-22 10:57:37.445 [OrganNo_00023_UserNo_admin] [760f678ea8c08b9c/c63a1a30e8236f26] [http-nio-9009-exec-58] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		
	},
	"retMsg":"移交成功"
}
2025-07-22 10:57:37.446 [] [760f678ea8c08b9c/c63a1a30e8236f26] [http-nio-9009-exec-58] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 移交!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:37 操作结束时间: 2025-07-22 10:57:37!总共花费时间: 46 毫秒！
2025-07-22 10:57:37.475 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/afb6df92aab372dd] [http-nio-9009-exec-59] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-22 10:57:37.507 [OrganNo_00023_UserNo_admin] [2f7866e6617d611f/afb6df92aab372dd] [http-nio-9009-exec-59] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:37.508 [] [2f7866e6617d611f/afb6df92aab372dd] [http-nio-9009-exec-59] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:37 操作结束时间: 2025-07-22 10:57:37!总共花费时间: 47 毫秒！
2025-07-22 10:57:39.404 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/87e369fcef868616] [http-nio-9009-exec-60] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"trunkState":"2",
		"isCenter":"1"
	}
}
2025-07-22 10:57:39.433 [OrganNo_00023_UserNo_admin] [4e225bbb534a0216/87e369fcef868616] [http-nio-9009-exec-60] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"1",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"2",
				"transferDate":"20250722",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"transferOrganNo":"01",
				"lockNo":"20250722",
				"id":"76481a989e3d45c795418c4efe3d8f35",
				"moveFlag":"0",
				"registerDate":"20250722",
				"belongYear":"2025",
				"receiveWaitOrganNo":"cd001",
				"trunkNo":"20250100006",
				"userName":"系统超级管理员",
				"transferUserNo":"admin",
				"areaCode":"01",
				"codeNo":"BG01",
				"transferUserName":"系统超级管理员",
				"yearNum":5.0,
				"transferOrganName":"成都地区",
				"receiveWaitOrganName":"都江堰"
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 10:57:39.433 [] [4e225bbb534a0216/87e369fcef868616] [http-nio-9009-exec-60] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 10:57:39 操作结束时间: 2025-07-22 10:57:39!总共花费时间: 51 毫秒！
2025-07-22 14:27:14.866 [OrganNo_00023_UserNo_admin] [cace257215f71602/ac8645125a0bd3da] [http-nio-9009-exec-62] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"archState":"3"
	}
}
2025-07-22 14:27:14.947 [OrganNo_00023_UserNo_admin] [cace257215f71602/ac8645125a0bd3da] [http-nio-9009-exec-62] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archNo":"ZB05-2025-000023-30-000001",
				"archState":"3",
				"archType":"1",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"辅助帐簿",
				"codeNo":"ZB05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"fc334db54a17440699b8660725da50b1",
				"insertNo":"1",
				"keepYear":"30",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"测试",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"孟国军",
				"tellerNo":"5274534",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			},
			{
				"archNo":"QT05-2025-000023-99-000001",
				"archState":"3",
				"archType":"2",
				"belongYear":"2025",
				"businessDate":"2025",
				"codeName":"会计系统柜员资料",
				"codeNo":"QT05",
				"dayNum":1,
				"endDate":"20251231",
				"id":"b20989d729734a67859234eaf564b47e",
				"insertNo":"3",
				"keepYear":"99",
				"receiveDate":"20250715",
				"receiveOrganName":"中国银行四川省分行",
				"receiveOrganNo":"00023",
				"receiveUserName":"系统超级管理员",
				"receiveUserNo":"admin",
				"registerDate":"20250714",
				"remark":"1",
				"siteName":"中国银行四川省分行",
				"siteNo":"00023",
				"startDate":"20250101",
				"tellerName":"伍益民",
				"tellerNo":"5430000",
				"transferDate":"20250715",
				"transferOrganName":"中国银行四川省分行",
				"transferOrganNo":"00023",
				"transferUserName":"系统超级管理员",
				"transferUserNo":"admin",
				"userName":"系统超级管理员",
				"userNo":"admin",
				"warrantAmount":"1",
				"yearNum":1
			}
		],
		"pageSize":15,
		"totalCount":2,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 14:27:14.948 [] [cace257215f71602/ac8645125a0bd3da] [http-nio-9009-exec-62] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 14:27:14 操作结束时间: 2025-07-22 14:27:14!总共花费时间: 89 毫秒！
2025-07-22 14:27:14.979 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/7d2707e2f47d8a1c] [http-nio-9009-exec-63] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"oper_type":"query",
		"pageNum":1,
		"pageSize":15,
		"queryType":"1",
		"trunkState":"1",
		"isCenter":"1"
	}
}
2025-07-22 14:27:15.023 [OrganNo_00023_UserNo_admin] [b0cf6ad1e4458e7f/7d2707e2f47d8a1c] [http-nio-9009-exec-63] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"retList":[
			{
				"archCount":"3",
				"keepYear":"99",
				"userNo":"admin",
				"siteName":"中国银行四川省分行",
				"trunkState":"1",
				"trunkType":"1",
				"siteNo":"00023",
				"areaName":"成都地区",
				"codeName":"本级及全辖年度财务报告(含决算正\\附表\\附注和决算文字分析)",
				"lockNo":"222222",
				"id":"c7112fbf14ab445daa4c57a672200171",
				"moveFlag":"0",
				"registerDate":"20250716",
				"belongYear":"2025",
				"trunkNo":"20250100002",
				"userName":"系统超级管理员",
				"areaCode":"01",
				"codeNo":"BG01",
				"yearNum":9.0
			}
		],
		"pageSize":15,
		"totalCount":1,
		"currentPage":1
	},
	"retMsg":"查询成功"
}
2025-07-22 14:27:15.024 [] [b0cf6ad1e4458e7f/7d2707e2f47d8a1c] [http-nio-9009-exec-63] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 查询!请求IP地址: ************** 操作开始时间: 2025-07-22 14:27:14 操作结束时间: 2025-07-22 14:27:15!总共花费时间: 55 毫秒！
2025-07-22 14:27:18.327 [OrganNo_00023_UserNo_admin] [782d6d457d883dab/d0e48b213dd1f0b4] [http-nio-9009-exec-65] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"code":"20250100002"
	}
}
2025-07-22 14:27:18.389 [OrganNo_00023_UserNo_admin] [782d6d457d883dab/d0e48b213dd1f0b4] [http-nio-9009-exec-65] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"root":[
			{
				"fileName":"20250100002.png",
				"src":"/barCodeImgs/trunk/20250100002.png"
			},
			{
				"fileName":"20250100003.png",
				"src":"/barCodeImgs/trunk/20250100003.png"
			},
			{
				"fileName":"20250100004.png",
				"src":"/barCodeImgs/trunk/20250100004.png"
			},
			{
				"fileName":"20250100005.png",
				"src":"/barCodeImgs/trunk/20250100005.png"
			},
			{
				"fileName":"20250100006.png",
				"src":"/barCodeImgs/trunk/20250100006.png"
			},
			{
				"fileName":"20250100007.png",
				"src":"/barCodeImgs/trunk/20250100007.png"
			},
			{
				"fileName":"20250100008.png",
				"src":"/barCodeImgs/trunk/20250100008.png"
			},
			{
				"fileName":"20250100009.png",
				"src":"/barCodeImgs/trunk/20250100009.png"
			},
			{
				"fileName":"20250100010.png",
				"src":"/barCodeImgs/trunk/20250100010.png"
			},
			{
				"fileName":"20250100011.png",
				"src":"/barCodeImgs/trunk/20250100011.png"
			}
		]
	},
	"retMsg":"打印箱号准备完成"
}
2025-07-22 14:27:18.390 [] [782d6d457d883dab/d0e48b213dd1f0b4] [http-nio-9009-exec-65] INFO  c.s.c.I.spring.aop.ArchivesLogAspect - 操作模块: 非扫描档案箱子管理 操作方法: 批量打印箱号条形码!请求IP地址: ************** 操作开始时间: 2025-07-22 14:27:18 操作结束时间: 2025-07-22 14:27:18!总共花费时间: 70 毫秒！
