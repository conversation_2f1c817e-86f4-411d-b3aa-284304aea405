package com.sunyard.console.safemanage.action;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Map;

import com.sunyard.console.common.struts.BaseAction;
import com.sunyard.console.common.util.DateUtil;
import com.sunyard.console.common.util.JSONUtil;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONObject;

import com.sunyard.console.common.config.ReadConfig;
//import com.sunyard.console.haixiaSSO.UapExecEngine;
import com.sunyard.console.monitor.monitorCenter.singleton.LazySingleton;
import com.sunyard.console.safemanage.bean.NodeBean;
import com.sunyard.console.safemanage.bean.UserCmodelRelBean;
import com.sunyard.console.safemanage.bean.UserInfoBean;
import com.sunyard.console.safemanage.dao.LoginManageDAO;
import com.sunyard.console.safemanage.dao.UserManageDAO;
import com.sunyard.console.threadpoool.IssueUtils;
import com.sunyard.util.StringUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <p>Title: 用户管理Action</p>
 * <p>Description: 处理用户信息管理中的跳转</p>
 * <p>Copyright: Copyright (c) 2012</p>
 * <p>Company: sunyard</p>
 * <AUTHOR>
 * @version 1.0
 */
@Controller
public class UserManageAction extends BaseAction {
	
	private static final long serialVersionUID = 1L;
	
	public final static int UPDATE_PASSWORD_SUCCESS = 0;
	public final static int OLD_PASSWORD_FAIL = 1; //旧密码错误
	public final static int UPDATE_PASSWORD_FAIL = 2; 
	public final static int WEAK_PASSWORD_FAIL = 3; //弱密码
	public final static int UPDATE_USER_FAIL = 4; //用户名错误，或用户被禁用
	@Autowired
	private UserManageDAO umdao;
	@Autowired
	private LoginManageDAO lmdao;//用户登录dao

	public void setUmdao(UserManageDAO umdao) {
		this.umdao = umdao;
	}
	public void setLmdao(LoginManageDAO lmdao) {
		this.lmdao = lmdao;
	}


	private String user_id;			//用户ID
	private String user_name;		//用户名称
	private String user_password;	//用户密码
	private String role_id;			//角色ID
	private String user_post;		//职位	
	private String user_department;	//部门
	private String user_state;		//用户状态
	private String ldap_code;		//ldap代码		
	private String user_city;		//所在城市		
	private int start;
	private int limit;
	private String optionFlag;		//操作类型
	private String c_user_id;
	private String user_ids;
	private String password;
	private String componentIDs;
	private String role_ids;
	private String model_codes;
	private String permission_code;
	private String haixiaSSOUser;//是否是海峡银行用户管理
	private final static  Logger log = LoggerFactory.getLogger(UserManageAction.class);
	
	public String getModel_codes() {
		return model_codes;
	}
	public void setModel_codes(String model_codes) {
		this.model_codes = model_codes;
	}
	public String getPermission_code() {
		return permission_code;
	}
	public void setPermission_code(String permission_code) {
		this.permission_code = permission_code;
	}
	public String getRole_ids() {
		return role_ids;
	}
	public void setRole_ids(String role_ids) {
		this.role_ids = role_ids;
	}
	public String getComponentIDs() {
		return componentIDs;
	}
	public void setComponentIDs(String componentIDs) {
		this.componentIDs = componentIDs;
	}
	public String getPassword() {
		return password;
	}
	public void setPassword(String password) {
		this.password = password;
	}
	public String getUser_ids() {
		return user_ids;
	}
	public void setUser_ids(String user_ids) {
		this.user_ids = user_ids;
	}
	public String getC_user_id() {
		return c_user_id;
	}
	public void setC_user_id(String c_user_id) {
		this.c_user_id = c_user_id;
	}
	public String getOptionFlag() {
		return optionFlag;
	}
	public void setOptionFlag(String optionFlag) {
		this.optionFlag = optionFlag;
	}
	public String getUser_id() {
		return user_id;
	}
	public void setUser_id(String user_id) {
		this.user_id = user_id;
	}
	public String getUser_name() {
		return user_name;
	}
	public void setUser_name(String user_name) {
		this.user_name = user_name;
	}
	public String getUser_password() {
		return user_password;
	}
	public void setUser_password(String user_password) {
		this.user_password = user_password;
	}
	public String getRole_id() {
		return role_id;
	}
	public void setRole_id(String role_id) {
		this.role_id = role_id;
	}
	public String getUser_post() {
		return user_post;
	}
	public void setUser_post(String user_post) {
		this.user_post = user_post;
	}
	public String getUser_department() {
		return user_department;
	}
	public void setUser_department(String user_department) {
		this.user_department = user_department;
	}
	public String getUser_state() {
		return user_state;
	}
	public void setUser_state(String user_state) {
		this.user_state = user_state;
	}
	public String getLdap_code() {
		return ldap_code;
	}
	public void setLdap_code(String ldap_code) {
		this.ldap_code = ldap_code;
	}
	public String getUser_city() {
		return user_city;
	}
	public void setUser_city(String user_city) {
		this.user_city = user_city;
	}
	public int getStart() {
		return start;
	}
	public void setStart(int start) {
		this.start = start;
	}
	public int getLimit() {
		return limit;
	}
	public void setLimit(int limit) {
		this.limit = limit;
	}
	
	public String getHaixiaSSOUser() {
		return haixiaSSOUser;
	}
	public void setHaixiaSSOUser(String haixiaSSOUser) {
		this.haixiaSSOUser = haixiaSSOUser;
	}
	
	
	
	
	/**
	 * 按条件查询用户列表
	 * @return 
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/userInfoAction.action", method = RequestMethod.POST)
	public String userInfoSearch(String user_id, String user_name, String user_state, String role_id, int start, int limit) {
		log.info("--userInfoSearch(start)-->user_id:" + user_id + ";user_name:" + user_name + ";user_state:" + user_state + "role_id:" + role_id);
		String jsonStr = null;
		try{
			
			List<UserInfoBean> userInfoList = umdao.searchUserInfoList(user_id, user_name, user_state, role_id,start+1,limit);
			List<UserInfoBean> userInfoAllList = umdao.searchAllUserInfoList(user_id, user_name, user_state, role_id);
			
			jsonStr = new JSONUtil().createJsonDataByColl(userInfoList,userInfoAllList.size(),new UserInfoBean());
			log.debug( "--userInfoSearch-->按条件查询列表成功！");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取用户信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->按条件查询用户列表失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--userInfoSearch(over)-->user_id:"+user_id);
		return null;
	}
	
	/**
	 * 增加、修改用户信息
	 * @return 
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/configUserAction.action", method = RequestMethod.POST)
	public String configUserInfo(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);

		String user_id = (String) modelJson.getOrDefault("user_id", "");
		String haixiaSSOUser = (String) modelJson.getOrDefault("haixiaSSOUser", "");
		String user_password = (String) modelJson.getOrDefault("user_password", "");
		String user_password2 = (String) modelJson.getOrDefault("user_password2", "");
		String user_city = (String) modelJson.getOrDefault("user_city", "");
		String user_department = (String) modelJson.getOrDefault("user_department", "");
		String user_name = (String) modelJson.getOrDefault("user_name", "");
		String user_post = (String) modelJson.getOrDefault("user_post", "");
		String ldap_code = (String) modelJson.getOrDefault("ldap_code", "");
		String optionFlag = (String) modelJson.getOrDefault("optionFlag", "");	
		user_city = "undefined".equals(user_city) || user_city == null ? "" : user_city;
		user_department = "undefined".equals(user_department) || user_department == null ? "" : user_department;
		user_post = "undefined".equals(user_post) || user_post == null ? "" : user_post;
		ldap_code = "undefined".equals(ldap_code) || ldap_code == null ? "" : ldap_code;

		log.info("--configUserInfo(start)-->user_id:" + user_id);
		UserInfoBean user = new UserInfoBean();
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		String message="配置用户成功!!";
		boolean ssosuccess = true;
		String result;
		boolean weakPassword=false;
		try {
			user_id = URLDecoder.decode(user_id, "utf-8");
			haixiaSSOUser = URLDecoder.decode(haixiaSSOUser, "utf-8");
			user_city = URLDecoder.decode(user_city, "utf-8");
			user_department = URLDecoder.decode(user_department, "utf-8");
			user_post = URLDecoder.decode(user_post, "utf-8");
			user_name = URLDecoder.decode(user_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode userManage fields error, user_id=" + user_id
					+ ", haixiaSSOUser=" + haixiaSSOUser
					+ ", user_city=" + user_city
					+ ", user_department=" + user_department
					+ ", user_post=" + user_post
					+ ", user_name=" + user_name,e1);
		}
		try {
			if(!user_password.equals(user_password2)){
				jsonResp.put("success", false);
				jsonResp.put("message", "两次输入的密码不一样!");
				jsonStr = jsonResp.toString();
			}else{
//				if ("true".equals(haixiaSSOUser)) {
//				UapExecEngine uap = null;
//					// 更新用户sso信息
//					uap = new UapExecEngine("38010006", user_id);
//					Map<String, String> map = uap.doSSOUser(uap);
//					result = map.get("result");
//					if (result == null || "false".equals(result)) {
//						// 更新用户出错
//						message = map.get("message");
//						ssosuccess = false;
//					}
//				}
				if (ssosuccess || !"true".equals(haixiaSSOUser)) {
					user.setLogin_id(user_id);
					user.setUser_password(user_password);
					user.setUser_city(user_city);
					user.setUser_department(user_department);
					user.setUser_name(user_name);
					user.setUser_post(user_post);
					user.setLdap_code(ldap_code);
					if (optionFlag.equals("create1")) {
						if (ReadConfig.isWeakPassword(user_password)) {
							weakPassword=true;
							message="新密码太简单!!";
							log.error("新密码太简单["+user_password+"]");
						} else {
							umdao.addUser(user);
						}
					}
					else if (optionFlag.equals("update1")) {
						user.setLogin_id(user_id);
						umdao.modifyUser(user);
						IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
						IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
					}
				}
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("ssosuccess", ssosuccess);
				jsonResp.put("message", message);
				jsonStr = jsonResp.toString();
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "配置用户失败!!");
			jsonStr = jsonResp.toString();
			log.error("用户管理->配置用户信息失败->", e);
		}
		this.outJsonString(jsonStr);
		log.info("--configUserInfo(over)-->user_id:" + user_id);
		return null;
	}
	
	/**
	 * 启用、禁用用户
	 * @return 
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/modifyUserStateAction.action", method = RequestMethod.POST)
	public String modifyUserState(String user_ids, String user_state) {
		log.info( "--modifyUserState(start)-->user_ids:"+user_ids+";user_state:"+user_state);
		String jsonStr = null;
		JSONObject jsonResp = new JSONObject();
		try{
			if(user_ids.equals("admin")&& user_state.equals("0")){
				jsonResp.put("success", false);
				jsonResp.put("message", "不允许禁用admin用户!");
				log.error("用户管理->禁用用户失败->不允许禁用admin用户");
			}else{
				umdao.modifyUserState(user_ids, user_state);
				jsonResp.put("success", true);
				if(Integer.valueOf(user_state)==1){
					jsonResp.put("message", "启用用户成功!!");
					jsonResp.put("code", 20000);//TODO mock
				}else{
					jsonResp.put("message", "禁用用户成功!!");
					jsonResp.put("code", 20000);
				}
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
				jsonStr = jsonResp.toString();
				log.debug( "--modifyUserState-->启用/禁用用户成功!!");
			}
		}catch(Exception e){
			jsonResp.put("success", false);
			if(Integer.valueOf(user_state)==1){
				jsonResp.put("message", "启用用户失败!!");
				log.error( "用户管理->启用用户失败->"+e.toString());
			}else{
				jsonResp.put("message", "禁用用户失败!!");
				log.error( "用户管理->禁用用户失败->",e);
			}
			jsonStr = jsonResp.toString();
		}
		this.outJsonString(jsonStr);
		log.info( "--modifyUserState(over)-->user_ids:"+user_ids);
		return null;
	}
	/**
	 * 重置密码
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/resetUserPwdAction.action", method = RequestMethod.POST)
	public String resetUserPwd(String user_id, String password) {
		log.info("--resetUserPwd(start)-->user_id:"+user_id+";password:***");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try{
			umdao.resetUserPwd(user_id, password);
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("message", "重置密码成功!!");
			jsonStr = jsonResp.toString();
			log.info("--resetUserPwd-->重置密码成功!!");
			IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
			IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "重置密码失败!!");
			jsonStr = jsonResp.toString();
			log.error("用户管理->重置密码失败->",e);
		}
		this.outJsonString(jsonStr);
		return null;
	}
	
	/**
	 * 修改密码
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/modifyUserPwdAction.action", method = RequestMethod.POST)
	public String modifyUserPwd(String user_id, String ps) {
		log.info("旧密码:***;新密码:***" + ",user_id=" + user_id + "]");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		String msg;
		try {
			int errorCount=ReadConfig.getConsoleConfigBean().getLoginErrorCount();
			int errorInterval=ReadConfig.getConsoleConfigBean().getLoginErrorInterval();
			if(!checkUserLoginError(user_id)){
				msg="旧密码错误超过"+errorCount+"次,"+errorInterval+"分钟后再尝试";
				log.warn(msg);
				jsonResp.put("success", false);
				jsonResp.put("message",msg);
				jsonStr = jsonResp.toString();
				this.outJsonString(jsonStr);
				return null;
			}
			String nowCount="0";
			
			if (ReadConfig.isWeakPassword(password)) {
				ps = "weak";
			} else {
				// 旧密码进行校验
				UserInfoBean user = lmdao.login(user_id, ps);
				if (user == null) {
					ps = "usererror";
				} else {
					String dbPassword = user.getPassword();
					if (!DigestUtils.md5Hex(ps).equals(dbPassword)) {
						log.error("旧密码错误");
						ps = "old";
						user=setUserLoginErrorTime(user_id);
						if(user!=null){
							nowCount=user.getUser_post();
						}
					}
				}
			}
			switch (umdao.mofifyUserPwd(ps, password, user_id)) {
			case UPDATE_PASSWORD_SUCCESS:
				LazySingleton.getUserModifyPWDErrorMap().remove(user_id);
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("message", "修改密码成功!!");
				jsonStr = jsonResp.toString();
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
				log.info("修改密码成功!!");
				break;
			case OLD_PASSWORD_FAIL:
				msg="旧密码错误,可以尝试"+errorCount+"次,已错"+nowCount+"次";
				jsonResp.put("success", false);
				jsonResp.put("message", msg);
				jsonStr = jsonResp.toString();
				log.error(msg);
				break;
			case WEAK_PASSWORD_FAIL:
				jsonResp.put("success", false);
				jsonResp.put("message", "新密码太简单!!");
				jsonStr = jsonResp.toString();
				log.error("新密码太简单!!");
				break;
			case UPDATE_USER_FAIL:
				jsonResp.put("success", false);
				jsonResp.put("message", "用户名错误,或用户被禁用!!");
				jsonStr = jsonResp.toString();
				log.error("用户名错误,或用户被禁用!!");
				break;
			case UPDATE_PASSWORD_FAIL:
				jsonResp.put("success", false);
				jsonResp.put("message", "修改密码失败!!");
				jsonStr = jsonResp.toString();
				log.error("修改密码失败!!");
				break;
			}
		} catch (Exception e) {
			jsonResp.put("success", false);
			jsonResp.put("message", "修改密码失败!!");
			jsonStr = jsonResp.toString();
			log.error("修改密码出错", e);
		}

		this.outJsonString(jsonStr);
		log.info("--modifyUserPwd(over)");
		return null;
	}
	public UserInfoBean setUserLoginErrorTime(String loginid) {
		Map<String, UserInfoBean> userModifyPWDErrorMap = LazySingleton.getUserModifyPWDErrorMap();
		UserInfoBean user = userModifyPWDErrorMap.get(loginid);
		if (user == null) {
			user = new UserInfoBean();
			user.setLogin_id(loginid);
			// 错误登录次数为1
			user.setUser_post("1");
			user.setPsw_mdf_date(DateUtil.getMDrqzhsti14());
		} else {
			int errorTime = 1;
			try {
				errorTime = Integer.parseInt(user.getUser_post());
				errorTime++;
			} catch (Exception e) {
				log.error("转换出错" + user.getUser_post() + ",重置为1", e);
				errorTime = 1;
			}
			user.setUser_post(Integer.toString(errorTime));
			if("".equals(user.getPsw_mdf_date())){
				//失败次数重新计算
				user.setPsw_mdf_date(DateUtil.getMDrqzhsti14());
			}
		}
		userModifyPWDErrorMap.put(loginid, user);
		return user;
	}
	/**
	 * 校验用户错误密码到期时间
	 * @param loginid
	 * @return true表示到期了，可以重新登录；false表示未到期（5分钟），无法登录
	 */
	public boolean checkUserLoginError(String loginid) {
		Map<String, UserInfoBean> userModifyPWDErrorMap = LazySingleton.getUserModifyPWDErrorMap();
		UserInfoBean user = userModifyPWDErrorMap.get(loginid);
		if (user == null) {
			return true;
		} else {
			String errorEndTime = user.getPsw_mdf_date();
			try {
				errorEndTime = DateUtil.getMDrqzhsti14(errorEndTime, ReadConfig.getConsoleConfigBean().getLoginErrorInterval());
			} catch (ParseException e1) {
				log.error("" + errorEndTime, e1);
				errorEndTime = DateUtil.getMDrqzhsti14();
			}
			String time = user.getUser_post();
			boolean flag = compareDate(errorEndTime);
			if (flag) {
				user.setPsw_mdf_date("");
				user.setUser_post("0");
			}
			if (Integer.parseInt(time) < ReadConfig.getConsoleConfigBean().getLoginErrorCount()) {
				// 如果密码错误次数不到4次，则可以再尝试登录
				return true;
			}
			return flag;
		}
	}
	/**
	 * 日期和当前日期比较，如果当前日期大于last返回true，否则返回false
	 * 
	 * @param last
	 * @return 如果当前日期大于last返回true，否则返回false
	 */
	public boolean compareDate(String last) {
		boolean flag = false;
		Calendar lastCal = Calendar.getInstance();
		Calendar nowCal = Calendar.getInstance();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
		try {
			lastCal.setTime(sdf.parse(last));
			flag = nowCal.compareTo(lastCal) >= 0;
		} catch (Exception e) {
			log.error("计算时间出错", e);
			return flag;
		}
		return flag;

	}
	/**
	 * 获取用户已有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/safeManage/getUserExistComponents.action")
	public String getUserExistComponents(@RequestParam("user_ids") String user_ids) {
		log.info("--getUserExistComponents(start)-->user_ids:" + user_ids);
		String jsonStr  = null;
		try{
			List<NodeBean> PermissionInfos = umdao.getExistsComponents(user_ids);
			jsonStr = NodeBean.getTreeString(PermissionInfos);
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->获取用户已有权限失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getUserExistComponents(over)-->user_ids:"+user_ids);
		return null;
	}
	
	/**
	 * 获取用户未有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping("/safeManage/getUserNotExistComponents.action")
	public String getUserNotExistComponents(@RequestParam("user_ids") String user_ids) {
		log.info("--getUserNotExistComponents(start)-->user_ids:" + user_ids);
		String jsonStr  = null;
		try{
			List<NodeBean> PermissionInfos = umdao.getNotExistsComponents(user_ids);
			jsonStr = NodeBean.getTreeString(PermissionInfos);
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->获取用户未有权限失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getUserNotExistComponents(over)-->user_ids:"+user_ids);
		return null;
	}
	
	/**
	 * 修改用户权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/updateUserComponents.action", method = RequestMethod.POST)
	public String updateUserComponents(String user_ids, String componentIDs) {
		log.info( "--updateUserComponents(start)-->user_ids:"+user_ids+";componentIDs:"+componentIDs);
		JSONObject	 jsonResp	= new JSONObject();
		String		 jsonStr	= null;
		
		try{
			umdao.updateUserComponents(user_ids, componentIDs);
			jsonResp.put("success", true);
			jsonResp.put("message", "修改权限成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--updateUserComponents-->修改权限成功!!user_ids:" + user_ids);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "修改权限失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->修改用户权限失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--updateUserComponents(over)-->user_ids:"+user_ids);
		return null;
	}
	
	/**
	 * 获取用户已有角色
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getUserExistRoles.action", method = RequestMethod.POST)
	public String getUserExistRoles(String user_ids) {
		log.info( "--getUserExistRoles(start)-->user_ids:"+user_ids);
		String jsonStr  = null;
		JSONObject jsonResp=new JSONObject();
		try{
			List<NodeBean> roles = umdao.getUserExistRoles(user_ids);
			jsonStr = NodeBean.getTreeString(roles);
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonStr);
			log.debug( "--getUserExistRoles-->获取角色信息成功!!");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取角色信息失败!!");
			log.error( "用户管理->获取用户已有角色失败->",e);
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getUserExistRoles(over)-->user_ids:"+user_ids);
		return null;
	}
	
	/**
	 * 获取用户未有角色
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getUserNotExistRoles.action", method = RequestMethod.POST)
	public String getUserNotExistRoles(String user_ids) {
		log.info("--getUserNotExistRoles(start)-->user_ids");
		String jsonStr  = null;
		JSONObject jsonResp=new JSONObject();
		try{
			List<NodeBean> roles = umdao.getUserNotExistRoles(user_ids);
			jsonStr = NodeBean.getTreeString(roles);
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonStr);
			log.debug( "--getUserNotExistRoles-->获取用户未有角色成功！");
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取角色信息失败!!");
			log.error( "用户管理->获取用户未有角色失败->",e);
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getUserNotExistRoles(over)-->user_ids");
		return null;
	}
	
	/**
	 * 修改用户的角色
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/updateUserRoles.action", method = RequestMethod.POST)
	public String updateUserRoles(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		String user_ids = (String) modelJson.getOrDefault("user_ids", "");
		String role_ids = (String) modelJson.getOrDefault("role_ids", "");
		log.info("--updateUserRoles(start)-->user_ids:" + user_ids + ";role_ids:" + role_ids);
		JSONObject jsonResp = new JSONObject();
		String		 jsonStr	= null;
		try{
			umdao.updateUserRoles(user_ids, role_ids);
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "修改用户绑定角色成功!!");
			jsonStr = jsonResp.toString();
			IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
			IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			log.debug( "--updateUserRoles-->修改角色成功!!user_ids:" + user_ids);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "修改角色失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->修改用户角色信息失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--updateUserRoles(over)-->user_ids:"+user_ids);
		return null;
	}
	/**
	 * 获取用户内容对象操作权限列表
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getUserConferAction.action", method = RequestMethod.POST)
	public String getConfer(String user_id, int start, int limit) {
		log.info("--getConfer(start)-->user_id:" + user_id);
		String		 jsonStr	= null;
		try{
			List<UserCmodelRelBean> conferList = umdao.getConferList(user_id, start+1, limit);
			List<UserCmodelRelBean> conferAllList = umdao.getConferAllList(user_id);
			jsonStr = new JSONUtil().createJsonDataByColl(conferList,conferAllList.size(),new UserCmodelRelBean());
			log.debug( "--getConfer-->获取用户内容对象操作权限信息成功!!");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取用户内容对象操作权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->获取用户内容对象操作权限列表失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getConfer(over)-->user_id:"+user_id);
		return null;
	}
	
	/**
	 * 配置用户内容对象操作权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/configUserConferAction.action", method = RequestMethod.POST)
	public String configConfer(String user_id, String model_codes, String permission_code) {
		log.info("--configConfer(start)-->user_id:" + user_id + ";model_codes:" + model_codes + ";permission_code:" + permission_code);
		JSONObject jsonResp = new JSONObject();
		String		 jsonStr	= null;
		
		try{
			umdao.configConfer(user_id, model_codes, permission_code);
			String[] modelCodes=model_codes.split(",");
			IssueUtils.IssueContentModelsInfo(modelCodes);
			jsonResp.put("success", true);
			jsonResp.put("message", "授予权限成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--configConfer-->授予权限成功!!user_id:" + user_id);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "授予权限失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->配置用户内容对象操作权限失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--configConfer(over)-->user_id:"+user_id);
		return null;
		
	}
	/**
	 * 校验haixiasso
	 * @return
	 */
//	@ResponseBody
//	@RequestMapping(value = "/safeManage/checkSSOUserAction.action", method = RequestMethod.POST)
//	public String checkSSOUser(String user_id) {
//		log.info("--checkSSOUser(start)-->user_id:" + user_id);
//		JSONObject jsonResp = new JSONObject();
//		String jsonStr = null;
//		boolean ssosuccess = true;
//		String message = "";
//		try {
//			// 查询用户sso信息
//			UapExecEngine uap = new UapExecEngine("38010007", user_id);
//			Map<String, String> map = uap.doSSOUser(uap);
//			
//			String result = map.get("result");
//			if ("false".equals(result)) {
//				// 无法新增用户
//				ssosuccess = false;
//				message = map.get("message");
//			}
//			
//			jsonResp.put("success", true);
//			jsonResp.put("ssosuccess", ssosuccess);
//			jsonResp.put("message", message);
//			jsonStr = jsonResp.toString();
//		} catch (Exception e) {
//			jsonResp.put("success", false);
//			jsonResp.put("message", "授予权限失败!!");
//			jsonStr = jsonResp.toString();
//			log.error("Exception:",e);
//		}
//		this.outJsonString(jsonStr);
//		log.info("--configConfer(over)-->user_id:" + user_id);
//		return null;
//
//	}
		
	/**
	 * 按条件查询用户列表
	 * @return 
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/userInfoVueAction.action")
	public String userInfoSearchVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		String user_id = (String) modelJson.getOrDefault("user_id", "");
		String user_name = (String) modelJson.getOrDefault("user_name", "");
		String user_state = (String) modelJson.getOrDefault("user_state", "");
		String role_id=String.valueOf(modelJson.getOrDefault("role_id", ""));
		if(StringUtil.stringIsNull(role_id)){
			role_id = "";
		}else{
			role_id = String.valueOf(modelJson.getInt("role_id"));
		}		
		start = (page_int-1) * limit_int;
		try {
			user_id = URLDecoder.decode(user_id, "utf-8");
			user_name = URLDecoder.decode(user_name, "utf-8");
		} catch (UnsupportedEncodingException e1) {
			log.error("decode userManage fields error, user_id=" + user_id
					+  ", user_name=" + user_name,e1);
		}
		log.info("--userInfoSearch(start)-->user_id:" + user_id + ";user_name:" + user_name + ";user_state:" + user_state + "role_id:" + role_id);
		String jsonStr = null;
		start = (page_int-1) * limit_int;
		try{
			
			List<UserInfoBean> userInfoList = umdao.searchUserInfoList(user_id, user_name, user_state, role_id,start+1,limit_int);
			List<UserInfoBean> userInfoAllList = umdao.searchAllUserInfoList(user_id, user_name, user_state, role_id);
			
			jsonStr = new JSONUtil().createJsonDataByColl(userInfoList,userInfoAllList.size(),new UserInfoBean());
			log.debug( "--userInfoSearch-->按条件查询列表成功！");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取用户信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->按条件查询用户列表失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--userInfoSearch(over)-->user_id:"+user_id);
		return null;
	}
	
	/**
	 * 重置密码
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/resetUserPwdVueAction.action", method = RequestMethod.POST)
	public String resetUserPwdVue(String user_id, String password, String password2) {
		log.info("--resetUserPwd(start)-->user_id:"+user_id+";password:***");
		JSONObject jsonResp = new JSONObject();
		String jsonStr = null;
		try{
			if(!password.equals(password2)){
				jsonResp.put("success", false);
				jsonResp.put("message", "两次输入的密码不一样!");
				jsonStr = jsonResp.toString();
			}else{
				umdao.resetUserPwd(user_id, password);
				jsonResp.put("success", true);
				jsonResp.put("code", 20000);
				jsonResp.put("message", "重置密码成功!!");
				jsonStr = jsonResp.toString();
				log.info("--resetUserPwd-->重置密码成功!!");
				IssueUtils.IssueInfoToDM(IssueUtils.getAllAliveDMInfo());
				IssueUtils.IssueInfoToUA(IssueUtils.getAliveUA());
			}
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "重置密码失败!!");
			jsonStr = jsonResp.toString();
			log.error("用户管理->重置密码失败->",e);
		}
		this.outJsonString(jsonStr);
		return null;
	}
	
	/**
	 * 获取用户内容对象操作权限列表
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getUserConferVueAction.action", method = RequestMethod.POST)
	public String getConferVue(String data) {
		JSONObject modelJson = JSONObject.fromObject(data);
		int page_int = 0;
		String page = modelJson.getString("page");
		if (page != null) {
			page_int = Integer.parseInt(page);
		}
		int limit_int = 0;
		String limit = modelJson.getString("limit");
		if (limit != null) {
			limit_int = Integer.parseInt(limit);
		}
		String user_id = (String) modelJson.getOrDefault("user_id", "");
		log.info("--getConfer(start)-->user_id:" + user_id);
		String		 jsonStr	= null;
		start = (page_int-1) * limit_int;
		try{
			List<UserCmodelRelBean> conferList = umdao.getConferList(user_id, start+1, limit_int);
			List<UserCmodelRelBean> conferAllList = umdao.getConferAllList(user_id);
			jsonStr = new JSONUtil().createJsonDataByColl(conferList,conferAllList.size(),new UserCmodelRelBean());
			log.debug( "--getConfer-->获取用户内容对象操作权限信息成功!!");
		}catch(Exception e){
			JSONObject jsonResp=new JSONObject();
			jsonResp.put("success", false);
			jsonResp.put("message", "获取用户内容对象操作权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->获取用户内容对象操作权限列表失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getConfer(over)-->user_id:"+user_id);
		return null;
	}
	
	/**
	 * 配置用户内容对象操作权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/configUserConferVueAction.action", method = RequestMethod.POST)
	public String configConferVue(String user_id, String model_codes, String permission_code) {
		log.info("--configConfer(start)-->user_id:" + user_id + ";model_codes:" + model_codes + ";permission_code:" + permission_code);
		JSONObject jsonResp = new JSONObject();
		String		 jsonStr	= null;	
		try{
			umdao.configConfer(user_id, model_codes, permission_code);
			String[] modelCodes=model_codes.split(",");
			IssueUtils.IssueContentModelsInfo(modelCodes);
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);//TODO mock
			jsonResp.put("message", "授予权限成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--configConfer-->授予权限成功!!user_id:" + user_id);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "授予权限失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->配置用户内容对象操作权限失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--configConfer(over)-->user_id:"+user_id);
		return null;	
	}
	
	/**
	 * 获取用户已有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getUserExistComponentsVueAction.action", method = RequestMethod.POST)
	public String getUserExistComponentsVue(@RequestParam("user_ids") String user_ids) {
		log.info("--getUserExistComponentsVue(start)-->user_ids:" + user_ids);
		String jsonStr  = null;
		JSONObject jsonResp = new JSONObject();
		try{
			List<NodeBean> PermissionInfos = umdao.getExistsComponents(user_ids);
			jsonStr = NodeBean.getTreeString(PermissionInfos);
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonStr);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			log.error( "用户管理->获取用户已有权限失败->",e);
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getUserExistComponentsVue(over)-->user_ids:"+user_ids);
		return null;
	}
	
	/**
	 * 获取用户未有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getUserNotExistComponentsVueAction.action", method = RequestMethod.POST)
	public String getUserNotExistComponentsVue(@RequestParam("user_ids") String user_ids) {
		log.info("--getUserNotExistComponentsVue(start)-->user_ids:" + user_ids);
		String jsonStr  = null;
		JSONObject jsonResp = new JSONObject();
		try{
			List<NodeBean> PermissionInfos = umdao.getNotExistsComponents(user_ids);
			jsonStr = NodeBean.getTreeString(PermissionInfos);
			jsonResp.put("code", 20000);
			jsonResp.put("msg", jsonStr);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			log.error( "用户管理->获取用户未有权限失败->",e);
		}
		this.outJsonString(jsonResp.toString());
		log.info( "--getUserNotExistComponentsVue(over)-->user_ids:"+user_ids);
		return null;
	}
	
	/**
	 * 获取用户所有权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/getUserAllComponentsVueAction.action", method = RequestMethod.POST)
	public String getUserAllComponentsVue(@RequestParam("user_ids") String user_ids) {
		log.info("--getUserAllComponentsVue(start)-->user_ids:" + user_ids);
		String jsonStr  = null;
		JSONObject jsonResp = new JSONObject();
		try{
			List<NodeBean> existList = umdao.getExistsComponents(user_ids);
			List<NodeBean> notexistList = umdao.getNotExistsComponents(user_ids);
			
			List<NodeBean> allList = new ArrayList<NodeBean>();
			if (notexistList != null && notexistList.size() > 0) {
				allList.addAll(notexistList);
			}
			if (existList != null && existList.size() > 0) {
				allList.addAll(existList);
			}
			if (allList != null && allList.size() > 0) {
				jsonStr = NodeBean.getTreeString(allList);
				jsonResp.put("code", 20000);// TODO mock
				jsonResp.put("msg", jsonStr);
			}
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "获取权限信息失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->获取用户所有权限失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--getUserAllComponentsVue(over)-->user_ids:"+user_ids);
		return null;
	}
	
	/**
	 * 修改用户权限
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value = "/safeManage/updateUserComponentsVueAction.action", method = RequestMethod.POST)
	public String updateUserComponentsVue(String user_ids, String componentIDs) {
		log.info( "--updateUserComponents(start)-->user_ids:"+user_ids+";componentIDs:"+componentIDs);
		JSONObject	 jsonResp	= new JSONObject();
		String		 jsonStr	= null;
		try{
			componentIDs = andComponent(componentIDs);	
			umdao.updateUserComponents(user_ids, componentIDs);
			jsonResp.put("success", true);
			jsonResp.put("code", 20000);
			jsonResp.put("message", "修改权限成功!!");
			jsonStr = jsonResp.toString();
			log.debug( "--updateUserComponents-->修改权限成功!!user_ids:" + user_ids);
		}catch(Exception e){
			jsonResp.put("success", false);
			jsonResp.put("message", "修改权限失败!!");
			jsonStr = jsonResp.toString();
			log.error( "用户管理->修改用户权限失败->",e);
		}
		this.outJsonString(jsonStr);
		log.info( "--updateUserComponents(over)-->user_ids:"+user_ids);
		return null;
	}
	
	public boolean containRepeatChar(String str,String str1){
		String[] arr1 = str.split(",");	
		String[] arr2 = str1.split(",");
		for (int i=0;i<arr2.length;i++){
			for (int j=0;j<arr1.length;j++){
				if(arr1[j].equals(arr2[i])){
					return true;
				}
			}
		}
		return false;
	}
	
	public String andComponent(String s){
		String a1 = "addAttribute,attributeSearch,delAttribute,modifyAttribute";
		String a10 = "scheduleSearch";
		String a11 = "ableServer,addGroup,addServer,disableServer,editServer,serverSearch";
		String a12 = "addLog,modifyLog";
		String a13 = "logDown,logSearch";
		String a14 = "addBatch,batchSearch,delBatch,modifyBatch";
		String a16 = "queryFailMigrate,reMigrate";
		String a17 = "manuallyDel,manuallySyn";
		String a18 = "addInSno,delInSno,queryInSno,updateInSno";
		String a2 = "addSepTable,hbaseSQLExport,objectCreate,objectDelete,objectExport,objectImport,objectPermission,objectUpdate";
		String a21 = "reNearLine";
		String a23 = "ableConParam,addConParam,addParamShow,conParamSearch,delParamShow,disableConParam,modifyConParam,modifyParamShow,paramShowSearch";
		String a26 = "tagSearch,addTag,modifyTag,deleteTag,relateModel.synToES";
		String a3 = "addHistoryTable,historySearch";
		String a4 = "ableUser,addUser,bindRole,disableUser,modifyUser,resetPwd,userMetadata,userPermission,userSearch";
		String a5 = "ableRole,addRole,disableRole,editRole,roleMetadata,rolePermission,roleSearch";
		String a6 = "addtoken,delToken";
		String a7 = "ableNode,addNode,disableNode,editNode,nodeSearch";
		String a8 = "ableNodeGroup,addNodeGroup,disableNodeGroup,modifyNodeGroup,nodeGroupSearch,nodeMetadata,relateVolume";
		String a9 = "ableStrategy,addStrategy,disableStrategy,strategySearch,updateStrategy";
		if(containRepeatChar(a1,s)){
			s+=",1";
		}if(containRepeatChar(a10,s)){
			s+=",10";
		}if(containRepeatChar(a11,s)){
			s+=",11";
		}if(containRepeatChar(a12,s)){
			s+=",12";
		}if(containRepeatChar(a13,s)){
			s+=",13";
		}if(containRepeatChar(a14,s)){
			s+=",14";
		}if(containRepeatChar(a16,s)){
			s+=",16";
		}if(containRepeatChar(a17,s)){
			s+=",17";
		}if(containRepeatChar(a18,s)){
			s+=",18";
		}if(containRepeatChar(a2,s)){
			s+=",2";
		}if(containRepeatChar(a21,s)){
			s+=",21";
		}if(containRepeatChar(a23,s)){
			s+=",23";
		}if(containRepeatChar(a26,s)){
			s+=",26";
		}if(containRepeatChar(a3,s)){
			s+=",3";
		}if(containRepeatChar(a4,s)){
			s+=",4";
		}if(containRepeatChar(a5,s)){
			s+=",5";
		}if(containRepeatChar(a6,s)){
			s+=",6";
		}if(containRepeatChar(a7,s)){
			s+=",7";
		}if(containRepeatChar(a8,s)){
			s+=",8";
		}if(containRepeatChar(a9,s)){
			s+=",9";
		}
		return s;
	}
	
}
