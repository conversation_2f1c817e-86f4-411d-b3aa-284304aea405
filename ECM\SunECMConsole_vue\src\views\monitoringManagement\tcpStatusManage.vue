<template>
  <div class="app-container">
    <div class="filter-container">
       <el-select v-model="listQuery.group_id" placeholder="请选择存储服务器组" @change='getServers()'>
        <el-option
          v-for="item in storeGroups"
          :key="item.id"
          :label="item.text_text"
          :value="item.id">
        </el-option>
      </el-select>
       <el-select v-model="listQuery.server_id" placeholder="请选择存储服务器">
        <el-option
          v-for="item in storeServers"
          :key="item.id"
          :label="item.text"
          :value="item.id">
        </el-option>
      </el-select>

      <el-button
        v-waves
        class="filter-item"
        type="primary"
        icon="el-icon-search"
        size="mini"
        plain 
        round 
        @click="handleFilter"
      >
        查询
      </el-button>
      <el-button
        v-waves
        class="filter-item"
        type="warning"
        icon="el-icon-delete-solid"
        size="mini"
        plain 
        round 
        @click="handleclear"
      >
        清空
      </el-button>
     </div> 
   <el-button type="text" @click="queryBeforeOneHour()">{{btnText}}</el-button>
	<el-row :gutter="32">
		<el-col :xs="24" :sm="24" :lg="10">
			<div :id="canvas"  :style="{height:height,width:width,padding: padding}">
			</div>
		</el-col>
	 </el-row>
  </div>

</template>

<script>
import {getContentServerGroup,getRelServers,getTCPSocketStatus} from '@/api/monitorManage'
import waves from '@/directive/waves' // waves directive
import elDragDialog from "@/directive/el-drag-dialog";

import { parseTime } from '@/utils'
import Pagination from '@/components/Pagination' // secondary package based on el-pagination
import * as echarts from 'echarts'

export default {
    name: 'ComplexTable',
    components: { Pagination },
    directives: { waves,elDragDialog },
    filters: {
        statusFilter(status) {
        const statusMap = {
            published: 'success',
            draft: 'info',
            deleted: 'danger'
        }
        return statusMap[status]
    }
  },

  props: {
    canvas: {
      type: String,
      default: 'chart1'
    },
    width: {
      type: String,
      default: '1100px'
    },
    height: {
      type: String,
      default: '500px'
    },
    padding: {
      type: String,
      default: '16px 16px'
    }
  },
  data() {
    return{
      event1:null,
      btnText : "显示当前数据",
      flag : true,//是否第一次访问
      storeGroups :  [],
      storeServers :  [],
      sysElems : [ 'CLOSE_WAIT', 'SYN_RCVD', 'CLOSEING', 'ESTABLISHED',
				'SYN_SENT', 'FIN_WAIT_1', 'FIN_WAIT_2', 'LAST_ACK', 'TIME_WAIT' ],
      listQuery: {
        importance: undefined,
        title: undefined,
        type: undefined,
        group_id:"",
        server_id:"",
      },
		  chart : null,
      tcpText:'TCP状态监控(前一小时数据)'
    }
  },
  
    mounted() {
      this.$nextTick(() => {
        this.showCharts()
      })
    },

    created() {
        this.getGroups()
    },

    beforeDestroy(){
      if(this.event1!=null){
        clearInterval(this.event1);
      }
    },

  methods: {
    handleclear() {
      this.listQuery.group_id= "";
      this.listQuery.server_id= "";
    },

    handleFilter() {
        this.objs = [];
        this.setChartParams();
        this.flag = true;
        this.showData()
    },

    getGroups(){
        getContentServerGroup().then(response => {
        this.storeGroups = response.root;
      })
    },

    getServers(){
      getRelServers(this.listQuery).then(response => {
          this.storeServers = response.root;
        })
    },

    queryBeforeOneHour() {
			if (this.btnText == '显示当前数据') {
				this.btnText = '显示前一小时数据';
        this.tcpText = 'TCP状态监控(当前数据)'
				this.init = 1;
				this.flag = false;
			} else {
        this.btnText = '显示当前数据';
        this.tcpText = 'TCP状态监控(前一小时数据)'
				this.init = 0;
				this.flag = true;
			}
			for (let i = 0; i < this.sysElems.length; i++) {
				this.objs[i].data = [];
			}
        this.showCharts();
        this.showData()
		},

  setChartParams() {
			for (let i = 0; i < this.sysElems.length; i++) {
				this.objs[i] = {
					type : 'line',
					name : this.sysElems[i],
					//symbol:'star',//拐点样式
					symbolSize : 2,
					itemStyle : {
						normal : {
							lineStyle : {
								width : 1,//折线宽度
							//color:"#FF0000"//折线颜色
							}
						}
					},
					data : []
				}
			}
		},

    showCharts(){
      this.chart = echarts.init(document.getElementById(this.canvas), 'dark');
      let option = {
          title : {
            top : '3%',
            text : this.tcpText,
            left : 'center'
          },
          tooltip : {
            axisPointer : {
              show : true,
              type : 'cross',
              lineStyle : {
                type : 'dashed',
                width : 1
              }
            }
          },
          legend : {
            orient : 'vertical',
            x : 'right',
            data : this.sysElems
          },
          grid : {
            top : '10%',
            left : '5%',
            right : '5%',
            bottom : '10%',
            containLabel : true
          },
          xAxis : {
            type : 'time',
            scale : true,
            splitLine : {
              show : false
            }
          },
          yAxis : {
            type : 'value'
          },
          series : this.objs
		};
      this.chart.setOption(option);
    },

    timerEvent(){
			let map = new Map();
      this.listQuery.flag = this.flag;
			getTCPSocketStatus(this.listQuery).then(response => {
          for ( let key in response) {
            if(key != "code"){
              if (map.hasOwnProperty(key)) {
                continue;
              } else {
                let elems = response[key];
                map[key] = response[key];
                for (let elem = 0; elem < elems.length; elem++) {
                  let point = [Number(key),elems[elem] ];
                  this.objs[elem].data.push(point);
                  if (this.objs[elem].data.length > 700) {
                    this.objs[elem].data.shift();
                  }
                }
              }
            }
          }
          this.chart.setOption({
            series : this.objs
          });
          this.flag = false;
			})
	  },
    showData(){
        clearInterval(this.event1);
        this.chart.hideLoading();
        this.timerEvent();
        this.event1 = setInterval(this.timerEvent, 5000);
	  }
  }
}
</script>

<style scoped>

</style>
