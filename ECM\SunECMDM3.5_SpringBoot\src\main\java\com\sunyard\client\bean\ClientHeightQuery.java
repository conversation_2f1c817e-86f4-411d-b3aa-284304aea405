package com.sunyard.client.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sunyard.client.bean.converter.ClientStringCustomConverter;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamAsAttribute;
import com.thoughtworks.xstream.annotations.XStreamConverter;
import com.thoughtworks.xstream.annotations.XStreamOmitField;

/**
 * 高级搜索的返回结果bean对象
 * <AUTHOR>
 *
 */
@XStreamAlias("HeightQuery")
public class ClientHeightQuery {
	@XStreamAsAttribute
	private boolean IS_UNITED_ACCESS; // 是否由统一接入服务器转发
	@XStreamAsAttribute
	private String USER_NAME;// 用户名
	@XStreamAsAttribute
	private int COUNT;// 查询数据总数
	@XStreamAsAttribute
	private String TABLE_NAME;// 查询对象表
	@XStreamAsAttribute
	private int LIMIT;// 每一页的数据行数
	@XStreamAsAttribute
	private int PAGE;// 页码
	
	@XStreamAsAttribute
	private String MODEL_CODE; // 内容模型代码
	@XStreamConverter(ClientStringCustomConverter.class)
	private Map<String, String> customAtt; // 自定义属性条件
	
	private List<String> filters;// 过滤条件
	@XStreamAsAttribute
	private String PASSWORD; // 密码
	private List<ClientBatchIndexBean> indexBeans;// 
	private String NEED_OFFLINE;//是否需要查询离线
	
	
	/**
	 * 令牌
	 */
	@XStreamOmitField
	private String token;
	
	
	public String getToken() {
		return token;
	}


	public void setToken(String token) {
		this.token = token;
	}
	
	public int getCount() {
		return COUNT;
	}

	public String getNEED_OFFLINE() {
		return NEED_OFFLINE;
	}

	public void setNEED_OFFLINE(String nEED_OFFLINE) {
		NEED_OFFLINE = nEED_OFFLINE;
	}

	public String getPassWord() {
		return PASSWORD;
	}

	public void setPassWord(String passWord) {
		this.PASSWORD = passWord;
	}

	/**
	 * 查询数据总数
	 * @param count 查询数据总数
	 */
	public void setCount(int count) {
		this.COUNT = count;
	}

	public int getLimit() {
		return LIMIT;
	}

	/**
	 * 每一页的数据行数
	 * @param limit 每一页的数据行数
	 */
	public void setLimit(int limit) {
		this.LIMIT = limit;
	}

	public String getModelCode() {
		return MODEL_CODE;
	}

	/**
	 * 内容模型代码
	 * @param modelCode 内容模型代码
	 */
	public void setModelCode(String modelCode) {
		this.MODEL_CODE = modelCode;
	}

	public int getPage() {
		return PAGE;
	}

	/**
	 * 页数
	 * @param page 页数
	 */
	public void setPage(int page) {
		this.PAGE = page;
	}

	public Map<String, String> getCustomAtt() {
		return customAtt;
	}

	/**
	 * 自定义属性条件
	 * @param customAtt 自定义属性条件
	 */
	public void setCustomAtt(Map<String, String> customAtt) {
		this.customAtt = customAtt;
	}
	
	/**
	 * 添加自定义属性条件
	 * @param key 属性字段
	 * @param value 属性值
	 */
	public void addCustomAtt(String key, String value) {
		if(customAtt == null) {
			customAtt = new HashMap<String, String>();
		}
		customAtt.put(key, value);
	}

	public List<String> getFilters() {
		return filters;
	}

	/**
	 * 设定过滤条件队列
	 * @param filters 过滤条件队列
	 */
	public void setFilters(List<String> filters) {
		this.filters = filters;
	}
	
	/**
	 * 添加 
	 * @param filter 过滤条件
	 */
	public void addfilters(String filter) {
		if(filters == null) {
			filters = new ArrayList<String>();
		}
		filters.add(filter);
	}

	public String getUserName() {
		return USER_NAME;
	}
	/**
	 * 设定用户名
	 * @param userName
	 */
	public void setUserName(String userName) {
		this.USER_NAME = userName;
	}
	
	public String getTableName() {
		return TABLE_NAME;
	}

	public void setTableName(String tableName) {
		this.TABLE_NAME = tableName;
	}
	
	public List<ClientBatchIndexBean> getIndexBeans() {
		return indexBeans;
	}

	public void setIndexBeans(List<ClientBatchIndexBean> indexBeans) {
		this.indexBeans = indexBeans;
	}
	
	public void addIndexBeans(ClientBatchIndexBean indexBean) {
		if (indexBeans == null) {
			indexBeans = new ArrayList<ClientBatchIndexBean>();
		}
		indexBeans.add(indexBean);
	}
	
	public boolean isUnitedAccess() {
		return IS_UNITED_ACCESS;
	}

	public void setUnitedAccess(boolean isUnitedAccess) {
		this.IS_UNITED_ACCESS = isUnitedAccess;
	}
	
}