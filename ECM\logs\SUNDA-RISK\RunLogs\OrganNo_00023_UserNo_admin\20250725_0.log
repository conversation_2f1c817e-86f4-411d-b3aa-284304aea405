2025-07-25 01:09:44.791 [OrganNo_00023_UserNo_admin] [0fc533711bb57fb5/d9401ea9bcec2886] [http-nio-9060-exec-40] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			
		}
	],
	"sysMap":{
		"oper_type":"getAllModelInfos"
	}
}
2025-07-25 01:09:44.809 [OrganNo_00023_UserNo_admin] [0fc533711bb57fb5/05ff5aba1ada865c] [http-nio-9060-exec-40] DEBUG c.s.a.r.d.a.A.getAllModelInfoDao - ==>  Preparing: SELECT a.id as MODEL_ID, a.show_name as MODEL_NAME, a.privname as MODEL_PRIV, b.table_name as TABLE_NAME, a.model_check_way as MODEL_CHECK_WAY, a.model_data_check_way as MODEL_DATA_CHECK_WAY, a.relating_id as RELATING_MODEL_ID, a.model_type as MODEL_TYPE, CASE WHEN EXISTS ( SELECT 1 FROM MC_MODEL_TB m WHERE m.relating_id = a.id ) THEN 1 ELSE 0 END as IS_RELATE_MODEL FROM MC_MODEL_TB a, MC_TABLE_TB b WHERE a.table_id = b.id AND a.status = 1 AND (a.model_type = 0 OR a.model_type = 1) ORDER BY a.name
2025-07-25 01:09:44.810 [OrganNo_00023_UserNo_admin] [0fc533711bb57fb5/05ff5aba1ada865c] [http-nio-9060-exec-40] DEBUG c.s.a.r.d.a.A.getAllModelInfoDao - ==> Parameters: 
2025-07-25 01:09:44.816 [OrganNo_00023_UserNo_admin] [0fc533711bb57fb5/05ff5aba1ada865c] [http-nio-9060-exec-40] DEBUG c.s.a.r.d.a.A.getAllModelInfoDao - <==      Total: 52
2025-07-25 01:09:44.831 [OrganNo_00023_UserNo_admin] [0fc533711bb57fb5/d9401ea9bcec2886] [http-nio-9060-exec-40] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"modelList":[
			{
				"relating_model_id":1,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":2,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1001-错账冲正交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":1,
				"model_check_way":"0",
				"table_name":"ZD_1001_CUOZCZJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":3,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":4,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1002-账户冻结解冻交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":3,
				"model_check_way":"0",
				"table_name":"ZD_1002_ZHANGHDJJD",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":5,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":6,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1003-账户挂失、解挂交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":5,
				"model_check_way":"0",
				"table_name":"ZD_1003_ZHANGHGSJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":7,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":8,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1004-特殊汇率交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":7,
				"model_check_way":"0",
				"table_name":"ZD_1004_TESHL",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":9,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":10,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1005-强制支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":9,
				"model_check_way":"0",
				"table_name":"ZD_1005_QIANGZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":11,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":12,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1006-非当日起息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":11,
				"model_check_way":"0",
				"table_name":"ZD_1006_FEIDRQX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":13,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":14,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1007-存、贷款调整交易、手工强制结息交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":13,
				"model_check_way":"0",
				"table_name":"ZD_1007_CUNDKTZSGQZJX",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":15,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":16,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1008-存款账户利率维护/变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":15,
				"model_check_way":"0",
				"table_name":"ZD_1008_CUNKZHLLWH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":17,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":18,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1009-存款、贷款账户换机构交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":17,
				"model_check_way":"0",
				"table_name":"ZD_1009_CUNKDKZHHJG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":19,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":20,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1010-重空状态变更交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":19,
				"model_check_way":"0",
				"table_name":"ZD_1010_ZHONGKZTBG",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":21,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":22,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1011-5111人民币长期不动户支取（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":21,
				"model_check_way":"0",
				"table_name":"ZD_1011_CHANGQWJFHKJK",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":23,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":24,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1012-大额手续费收入及支出检查（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":23,
				"model_check_way":"0",
				"table_name":"ZD_1012_SHOUXFSRJC",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":25,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":26,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1013-741挂账、842销账、8429支取交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":25,
				"model_check_way":"0",
				"table_name":"ZD_1013_GUAZXZZQ",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":27,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":28,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1014-单位大额交易（明细）",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":27,
				"model_check_way":"0",
				"table_name":"ZD_1014_DANWDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":29,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":30,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1015-不同客户新开立借记卡预留相同手机号（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":29,
				"model_check_way":"0",
				"table_name":"YJ_1001_XINKDZHSJXT",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":31,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":32,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1016-验资（增资）账户冻结情况检查（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":31,
				"model_check_way":"0",
				"table_name":"YJ_1002_YANZZZZH",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":33,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易",
				"is_relate_model":0,
				"model_type":"0",
				"model_id":34,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1017-非营业时间（19时后）交易（明细）",
				"is_relate_model":1,
				"model_type":"0",
				"model_id":33,
				"model_check_way":"0",
				"table_name":"YJ_1003_FEIYYSJJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":36,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1018-经集中核准的人民币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":35,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1018-经集中核准的人民币大额交易(明细)  ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":36,
				"model_check_way":"0",
				"table_name":"HZ_1001_RMBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":38,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":37,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"1"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1019-经集中核准的外币大额交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":38,
				"model_check_way":"0",
				"table_name":"HZ_1002_WBDEJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":40,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1020-经集中核准的司法扣划交易 ",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":39,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBI",
				"model_name":"1020-经集中核准的司法扣划交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":40,
				"model_check_way":"0",
				"table_name":"HZ_1003_SFKHJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":42,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":41,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1021-经集中核准的司法冻结、解冻交易(明细)",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":42,
				"model_check_way":"0",
				"table_name":"HZ_1004_SFDJJDJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":44,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":43,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1022-经集中核准的挂失、解挂交易(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":44,
				"model_check_way":"0",
				"table_name":"HZ_1005_GSJGJY",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":46,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":45,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1023-存款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":46,
				"model_check_way":"0",
				"table_name":"CSAR7010",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":48,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":47,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1024-贷款利率调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":48,
				"model_check_way":"0",
				"table_name":"CSAR7020",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":50,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":49,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1025-特殊业务调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":50,
				"model_check_way":"0",
				"table_name":"CSAR7040",
				"model_data_check_way":"0"
			},
			{
				"relating_model_id":52,
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型",
				"is_relate_model":0,
				"model_type":"1",
				"model_id":51,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			},
			{
				"model_priv":"ARMS_BRASEXHIBIT",
				"model_name":"1026-GLIF调整成功模型(明细) ",
				"is_relate_model":1,
				"model_type":"1",
				"model_id":52,
				"model_check_way":"0",
				"table_name":"CSAR7050",
				"model_data_check_way":"0"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-25 01:09:49.769 [OrganNo_00023_UserNo_admin] [ac32d7a8ed4507c5/055deced8fe95ce4] [http-nio-9060-exec-42] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"end_date":"20250724",
			"model_id":"",
			"site_no":"",
			"start_date":"20240428"
		}
	],
	"sysMap":{
		
	}
}
2025-07-25 01:09:49.792 [OrganNo_00023_UserNo_admin] [ac32d7a8ed4507c5/be99534b4cb2fb8f] [http-nio-9060-exec-42] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - ==>  Preparing: SELECT COALESCE(main.NOT_DEALED_COUNT, 0) as NOT_DEALED_COUNT, COALESCE(main.HAVE_DEALED_COUNT, 0) as HAVE_DEALED_COUNT, COALESCE(main.SUPERVISE_PASS_COUNT, 0) as SUPERVISE_PASS_COUNT, COALESCE(main.SUPERVISE_SLIP_COUNT, 0) as SUPERVISE_SLIP_COUNT, COALESCE(back.BACKCOUNT, 0) as BACKCOUNT, main.MODEL_ID FROM ( SELECT sum(a.NOT_DEALED_COUNT) as NOT_DEALED_COUNT, sum(a.HAVE_DEALED_COUNT) as HAVE_DEALED_COUNT, sum(a.SUPERVISE_PASS_COUNT) as SUPERVISE_PASS_COUNT, sum(a.SUPERVISE_SLIP_COUNT) as SUPERVISE_SLIP_COUNT, a.MODEL_ID FROM SUPERVISE_STATISTIC_TB a, SM_USER_ORGAN_TB b WHERE 1=1 AND a.OCCUR_DATE >= ? AND a.OCCUR_DATE <= ? AND a.site_no = b.organ_no AND b.user_no = ? AND a.user_no = b.user_no GROUP BY a.MODEL_ID ) main LEFT JOIN ( SELECT COUNT(*) as BACKCOUNT, CAST(model_id AS NUMERIC) as MODEL_ID FROM mc_model_zjd WHERE state = '3' AND user_no_s = ? AND business_date >= ? AND business_date <= ? GROUP BY model_id ) back ON main.MODEL_ID = back.MODEL_ID ORDER BY main.MODEL_ID
2025-07-25 01:09:49.793 [OrganNo_00023_UserNo_admin] [ac32d7a8ed4507c5/be99534b4cb2fb8f] [http-nio-9060-exec-42] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - ==> Parameters: 20240428(String), 20250724(String), admin(String), admin(String), 20240428(String), 20250724(String)
2025-07-25 01:09:49.813 [OrganNo_00023_UserNo_admin] [ac32d7a8ed4507c5/be99534b4cb2fb8f] [http-nio-9060-exec-42] DEBUG c.s.a.r.d.a.A.getRiskWarningStatistics - <==      Total: 3
2025-07-25 01:09:49.844 [OrganNo_00023_UserNo_admin] [ac32d7a8ed4507c5/055deced8fe95ce4] [http-nio-9060-exec-42] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"riskWarningStats":[
			{
				"MODEL_ID":4,
				"SUPERVISE_SLIP_COUNT":2,
				"HAVE_DEALED_COUNT":8,
				"NOT_DEALED_COUNT":3,
				"BACKCOUNT":2,
				"SUPERVISE_PASS_COUNT":6
			},
			{
				"MODEL_ID":5,
				"SUPERVISE_SLIP_COUNT":2,
				"HAVE_DEALED_COUNT":10,
				"NOT_DEALED_COUNT":5,
				"BACKCOUNT":1,
				"SUPERVISE_PASS_COUNT":8
			},
			{
				"MODEL_ID":6,
				"SUPERVISE_SLIP_COUNT":8,
				"HAVE_DEALED_COUNT":23,
				"NOT_DEALED_COUNT":12,
				"BACKCOUNT":3,
				"SUPERVISE_PASS_COUNT":15
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-25 01:09:51.474 [OrganNo_00023_UserNo_admin] [ecd6d1403fba65d9/cb631364b2606100] [http-nio-9060-exec-43] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"modelInfo":{
				"modelName":"1003-账户挂失、解挂交易",
				"modelId":"6",
				"modelType":"1",
				"tableName":"ZD_1003_ZHANGHGSJG"
			},
			"model_id":"6"
		}
	],
	"sysMap":{
		
	}
}
2025-07-25 01:09:51.476 [OrganNo_00023_UserNo_admin] [ecd6d1403fba65d9/76a9c1ef6c47ed7f] [http-nio-9060-exec-43] DEBUG c.s.a.r.d.a.A.getExhibitFields - ==>  Preparing: SELECT a.ID, a.NAME, a.CH_NAME, a.TYPE, c.ROWNO, c.FORMAT, c.ISFIND, c.ISDROPDOWN, c.ISIMPORTANT, c.RELATE_ID, a.TABLE_TYPE FROM MC_FIELD_TB a, MC_EXHIBIT_FIELD_TB c WHERE a.ID = c.TABLE_FIELD_ID AND c.MODEL_ID = ? AND a.TABLE_TYPE IN(3,5) ORDER BY c.ROWNO
2025-07-25 01:09:51.476 [OrganNo_00023_UserNo_admin] [ecd6d1403fba65d9/76a9c1ef6c47ed7f] [http-nio-9060-exec-43] DEBUG c.s.a.r.d.a.A.getExhibitFields - ==> Parameters: 6(String)
2025-07-25 01:09:51.481 [OrganNo_00023_UserNo_admin] [ecd6d1403fba65d9/76a9c1ef6c47ed7f] [http-nio-9060-exec-43] DEBUG c.s.a.r.d.a.A.getExhibitFields - <==      Total: 27
2025-07-25 01:09:51.483 [OrganNo_00023_UserNo_admin] [ecd6d1403fba65d9/76a9c1ef6c47ed7f] [http-nio-9060-exec-43] DEBUG c.s.a.r.d.a.A.getRiskWarningDetailData - ==>  Preparing: SELECT OCCUR_DATE , OCCUR_TIME , SITE_NO , SITE_NAME , OPERATOR_NO , OPERATOR_NAME , FK_SITE_NO , ACC_SITENAME , TX_CODE , TX_NAME , FLOW_ID , LOSS_ACCOUNT_NO , FORMER_LOSS_ACC , CUSTOMER_NAME , ID_TYPE , ID_NO , VOUH_TYPE , VOUH_NO , PRODUCT_CODE , PRE_VOUCH_STATE , POST_VOUCH_STATE , AUTH_TELLER_NO , HZ_TELLER_NAME , HZ_SITE_NO , HZ_SITE_NAME , TRAN_SITE_HANDLE , FIELD_CONTENT_CN ,flow_id,ishandle,modelrow_id FROM ZD_1003_ZHANGHGSJG WHERE 1=1 AND MODEL_ID = ? AND trim(USER_NO) = ? AND IMAGE_STATE = '1' ORDER BY occur_date, site_no, OPERATOR_NO
2025-07-25 01:09:51.484 [OrganNo_00023_UserNo_admin] [ecd6d1403fba65d9/76a9c1ef6c47ed7f] [http-nio-9060-exec-43] DEBUG c.s.a.r.d.a.A.getRiskWarningDetailData - ==> Parameters: 6(String), admin(String)
2025-07-25 01:09:51.487 [OrganNo_00023_UserNo_admin] [ecd6d1403fba65d9/76a9c1ef6c47ed7f] [http-nio-9060-exec-43] DEBUG c.s.a.r.d.a.A.getRiskWarningDetailData - <==      Total: 2
2025-07-25 01:09:51.518 [OrganNo_00023_UserNo_admin] [ecd6d1403fba65d9/cb631364b2606100] [http-nio-9060-exec-43] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"detailData":[
			{
				"LOSS_ACCOUNT_NO":"6216633100000485739",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行郫都红光支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":86,
				"FK_SITE_NO":"35905",
				"TX_CODE":"037241              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"15:34:53            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行郫都红光支行",
				"TX_NAME":"领非预制卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			},
			{
				"LOSS_ACCOUNT_NO":"6217563100016683998",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行珙县支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":112,
				"FK_SITE_NO":"15263",
				"TX_CODE":"037223              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"10:48:35            ",
				"ISHANDLE":"1",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行珙县支行",
				"TX_NAME":"销卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			}
		],
		"exhibitFields":[
			{
				"field_id":366,
				"rowno":1,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"业务日期",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_DATE",
				"is_find":"0",
				"id":366,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":319,
				"rowno":2,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易时间",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_TIME",
				"is_find":"0",
				"id":319,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":83,
				"rowno":3,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"机构号",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NO",
				"is_find":"0",
				"id":83,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":326,
				"rowno":4,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NAME",
				"is_find":"0",
				"id":326,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":367,
				"rowno":5,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NO",
				"is_find":"0",
				"id":367,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":320,
				"rowno":6,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NAME",
				"is_find":"0",
				"id":320,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":394,
				"rowno":7,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属(发卡)机构",
				"type":"INT",
				"isfind":"0",
				"name":"FK_SITE_NO",
				"is_find":"0",
				"id":394,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":399,
				"rowno":8,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属机构名",
				"type":"INT",
				"isfind":"0",
				"name":"ACC_SITENAME",
				"is_find":"0",
				"id":399,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":327,
				"rowno":9,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易代码",
				"type":"INT",
				"isfind":"0",
				"name":"TX_CODE",
				"is_find":"0",
				"id":327,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":372,
				"rowno":10,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易名称",
				"type":"INT",
				"isfind":"0",
				"name":"TX_NAME",
				"is_find":"0",
				"id":372,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":315,
				"rowno":11,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易流水",
				"type":"INT",
				"isfind":"0",
				"name":"FLOW_ID",
				"is_find":"0",
				"id":315,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":406,
				"rowno":12,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账(卡)号",
				"type":"INT",
				"isfind":"0",
				"name":"LOSS_ACCOUNT_NO",
				"is_find":"0",
				"id":406,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":412,
				"rowno":13,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账号（旧）",
				"type":"INT",
				"isfind":"0",
				"name":"FORMER_LOSS_ACC",
				"is_find":"0",
				"id":412,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":314,
				"rowno":14,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"客户名称",
				"type":"INT",
				"isfind":"0",
				"name":"CUSTOMER_NAME",
				"is_find":"0",
				"id":314,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":421,
				"rowno":15,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件类型",
				"type":"INT",
				"isfind":"0",
				"name":"ID_TYPE",
				"is_find":"0",
				"id":421,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":417,
				"rowno":16,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件号",
				"type":"INT",
				"isfind":"0",
				"name":"ID_NO",
				"is_find":"0",
				"id":417,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":341,
				"rowno":17,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证种类",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_TYPE",
				"is_find":"0",
				"id":341,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":328,
				"rowno":18,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证代号",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_NO",
				"is_find":"0",
				"id":328,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":436,
				"rowno":19,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"产品代码/产品码",
				"type":"INT",
				"isfind":"0",
				"name":"PRODUCT_CODE",
				"is_find":"0",
				"id":436,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":376,
				"rowno":20,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易前凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"PRE_VOUCH_STATE",
				"is_find":"0",
				"id":376,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":381,
				"rowno":21,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易后凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"POST_VOUCH_STATE",
				"is_find":"0",
				"id":381,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":368,
				"rowno":22,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"复核柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"AUTH_TELLER_NO",
				"is_find":"0",
				"id":368,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":400,
				"rowno":23,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_TELLER_NAME",
				"is_find":"0",
				"id":400,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":410,
				"rowno":24,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构/核准机构号",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NO",
				"is_find":"0",
				"id":410,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":404,
				"rowno":25,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NAME",
				"is_find":"0",
				"id":404,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":397,
				"rowno":26,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"是否跨网点办理",
				"type":"INT",
				"isfind":"0",
				"name":"TRAN_SITE_HANDLE",
				"is_find":"0",
				"id":397,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":477,
				"rowno":27,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"联网核查结果",
				"type":"INT",
				"isfind":"0",
				"name":"FIELD_CONTENT_CN",
				"is_find":"0",
				"id":477,
				"field_type":1,
				"table_type":"5"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-25 01:09:51.550 [OrganNo_00023_UserNo_admin] [69a0f64ec175478a/d64bcbbe941ceb60] [http-nio-9060-exec-44] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"is_history":"0",
			"ishandle":"0",
			"modelInfo":{
				"modelName":"1003-账户挂失、解挂交易",
				"modelId":"6",
				"modelType":"1",
				"tableName":"ZD_1003_ZHANGHGSJG"
			},
			"model_id":"6"
		}
	],
	"sysMap":{
		
	}
}
2025-07-25 01:09:51.552 [OrganNo_00023_UserNo_admin] [69a0f64ec175478a/8ca07a0499ca1ae1] [http-nio-9060-exec-44] DEBUG c.s.a.r.d.a.A.getExhibitFields - ==>  Preparing: SELECT a.ID, a.NAME, a.CH_NAME, a.TYPE, c.ROWNO, c.FORMAT, c.ISFIND, c.ISDROPDOWN, c.ISIMPORTANT, c.RELATE_ID, a.TABLE_TYPE FROM MC_FIELD_TB a, MC_EXHIBIT_FIELD_TB c WHERE a.ID = c.TABLE_FIELD_ID AND c.MODEL_ID = ? AND a.TABLE_TYPE IN(3,5) ORDER BY c.ROWNO
2025-07-25 01:09:51.552 [OrganNo_00023_UserNo_admin] [69a0f64ec175478a/8ca07a0499ca1ae1] [http-nio-9060-exec-44] DEBUG c.s.a.r.d.a.A.getExhibitFields - ==> Parameters: 6(String)
2025-07-25 01:09:51.557 [OrganNo_00023_UserNo_admin] [69a0f64ec175478a/8ca07a0499ca1ae1] [http-nio-9060-exec-44] DEBUG c.s.a.r.d.a.A.getExhibitFields - <==      Total: 27
2025-07-25 01:09:51.559 [OrganNo_00023_UserNo_admin] [69a0f64ec175478a/8ca07a0499ca1ae1] [http-nio-9060-exec-44] DEBUG c.s.a.r.d.a.A.getRiskWarningDetailData - ==>  Preparing: SELECT OCCUR_DATE , OCCUR_TIME , SITE_NO , SITE_NAME , OPERATOR_NO , OPERATOR_NAME , FK_SITE_NO , ACC_SITENAME , TX_CODE , TX_NAME , FLOW_ID , LOSS_ACCOUNT_NO , FORMER_LOSS_ACC , CUSTOMER_NAME , ID_TYPE , ID_NO , VOUH_TYPE , VOUH_NO , PRODUCT_CODE , PRE_VOUCH_STATE , POST_VOUCH_STATE , AUTH_TELLER_NO , HZ_TELLER_NAME , HZ_SITE_NO , HZ_SITE_NAME , TRAN_SITE_HANDLE , FIELD_CONTENT_CN ,flow_id,ishandle,modelrow_id FROM ZD_1003_ZHANGHGSJG WHERE 1=1 AND MODEL_ID = ? AND ISHANDLE = ? AND trim(USER_NO) = ? AND IMAGE_STATE = '1' ORDER BY occur_date, site_no, OPERATOR_NO
2025-07-25 01:09:51.560 [OrganNo_00023_UserNo_admin] [69a0f64ec175478a/8ca07a0499ca1ae1] [http-nio-9060-exec-44] DEBUG c.s.a.r.d.a.A.getRiskWarningDetailData - ==> Parameters: 6(String), 0(String), admin(String)
2025-07-25 01:09:51.564 [OrganNo_00023_UserNo_admin] [69a0f64ec175478a/8ca07a0499ca1ae1] [http-nio-9060-exec-44] DEBUG c.s.a.r.d.a.A.getRiskWarningDetailData - <==      Total: 1
2025-07-25 01:09:51.596 [OrganNo_00023_UserNo_admin] [69a0f64ec175478a/d64bcbbe941ceb60] [http-nio-9060-exec-44] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"detailData":[
			{
				"LOSS_ACCOUNT_NO":"6216633100000485739",
				"ID_NO":"",
				"PRE_VOUCH_STATE":"",
				"TRAN_SITE_HANDLE":"否",
				"FIELD_CONTENT_CN":"请查表核实",
				"ID_TYPE":"",
				"SITE_NAME":"中国银行郫都红光支行",
				"PRODUCT_CODE":"",
				"SITE_NO":"14902",
				"MODELROW_ID":86,
				"FK_SITE_NO":"35905",
				"TX_CODE":"037241              ",
				"OPERATOR_NO":"1488420",
				"OCCUR_TIME":"15:34:53            ",
				"ISHANDLE":"0",
				"VOUH_NO":"",
				"VOUH_TYPE":"",
				"HZ_TELLER_NAME":"",
				"FLOW_ID":"*********",
				"POST_VOUCH_STATE":"",
				"HZ_SITE_NO":"",
				"ACC_SITENAME":"中国银行郫都红光支行",
				"TX_NAME":"领非预制卡",
				"CUSTOMER_NAME":"",
				"OCCUR_DATE":"********",
				"AUTH_TELLER_NO":"",
				"HZ_SITE_NAME":"",
				"OPERATOR_NAME":"",
				"FORMER_LOSS_ACC":""
			}
		],
		"exhibitFields":[
			{
				"field_id":366,
				"rowno":1,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"业务日期",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_DATE",
				"is_find":"0",
				"id":366,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":319,
				"rowno":2,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易时间",
				"type":"INT",
				"isfind":"0",
				"name":"OCCUR_TIME",
				"is_find":"0",
				"id":319,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":83,
				"rowno":3,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"机构号",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NO",
				"is_find":"0",
				"id":83,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":326,
				"rowno":4,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"SITE_NAME",
				"is_find":"0",
				"id":326,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":367,
				"rowno":5,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NO",
				"is_find":"0",
				"id":367,
				"field_type":0,
				"table_type":"3"
			},
			{
				"field_id":320,
				"rowno":6,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"OPERATOR_NAME",
				"is_find":"0",
				"id":320,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":394,
				"rowno":7,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属(发卡)机构",
				"type":"INT",
				"isfind":"0",
				"name":"FK_SITE_NO",
				"is_find":"0",
				"id":394,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":399,
				"rowno":8,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"账户所属机构名",
				"type":"INT",
				"isfind":"0",
				"name":"ACC_SITENAME",
				"is_find":"0",
				"id":399,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":327,
				"rowno":9,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易代码",
				"type":"INT",
				"isfind":"0",
				"name":"TX_CODE",
				"is_find":"0",
				"id":327,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":372,
				"rowno":10,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易名称",
				"type":"INT",
				"isfind":"0",
				"name":"TX_NAME",
				"is_find":"0",
				"id":372,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":315,
				"rowno":11,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易流水",
				"type":"INT",
				"isfind":"0",
				"name":"FLOW_ID",
				"is_find":"0",
				"id":315,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":406,
				"rowno":12,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账(卡)号",
				"type":"INT",
				"isfind":"0",
				"name":"LOSS_ACCOUNT_NO",
				"is_find":"0",
				"id":406,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":412,
				"rowno":13,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"挂失账号（旧）",
				"type":"INT",
				"isfind":"0",
				"name":"FORMER_LOSS_ACC",
				"is_find":"0",
				"id":412,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":314,
				"rowno":14,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"客户名称",
				"type":"INT",
				"isfind":"0",
				"name":"CUSTOMER_NAME",
				"is_find":"0",
				"id":314,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":421,
				"rowno":15,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件类型",
				"type":"INT",
				"isfind":"0",
				"name":"ID_TYPE",
				"is_find":"0",
				"id":421,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":417,
				"rowno":16,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"证件号",
				"type":"INT",
				"isfind":"0",
				"name":"ID_NO",
				"is_find":"0",
				"id":417,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":341,
				"rowno":17,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证种类",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_TYPE",
				"is_find":"0",
				"id":341,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":328,
				"rowno":18,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"凭证代号",
				"type":"INT",
				"isfind":"0",
				"name":"VOUH_NO",
				"is_find":"0",
				"id":328,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":436,
				"rowno":19,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"产品代码/产品码",
				"type":"INT",
				"isfind":"0",
				"name":"PRODUCT_CODE",
				"is_find":"0",
				"id":436,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":376,
				"rowno":20,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易前凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"PRE_VOUCH_STATE",
				"is_find":"0",
				"id":376,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":381,
				"rowno":21,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"交易后凭证状态",
				"type":"INT",
				"isfind":"0",
				"name":"POST_VOUCH_STATE",
				"is_find":"0",
				"id":381,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":368,
				"rowno":22,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"复核柜员号",
				"type":"INT",
				"isfind":"0",
				"name":"AUTH_TELLER_NO",
				"is_find":"0",
				"id":368,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":400,
				"rowno":23,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准柜员名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_TELLER_NAME",
				"is_find":"0",
				"id":400,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":410,
				"rowno":24,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构/核准机构号",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NO",
				"is_find":"0",
				"id":410,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":404,
				"rowno":25,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"核准机构名称",
				"type":"INT",
				"isfind":"0",
				"name":"HZ_SITE_NAME",
				"is_find":"0",
				"id":404,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":397,
				"rowno":26,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"是否跨网点办理",
				"type":"INT",
				"isfind":"0",
				"name":"TRAN_SITE_HANDLE",
				"is_find":"0",
				"id":397,
				"field_type":1,
				"table_type":"5"
			},
			{
				"field_id":477,
				"rowno":27,
				"isimportant":"0",
				"isdropdown":"0",
				"format":"0",
				"relate_id":0,
				"ch_name":"联网核查结果",
				"type":"INT",
				"isfind":"0",
				"name":"FIELD_CONTENT_CN",
				"is_find":"0",
				"id":477,
				"field_type":1,
				"table_type":"5"
			}
		]
	},
	"retMsg":"查询成功"
}
2025-07-25 01:09:52.947 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/fd87aeb6a5f76e1b] [http-nio-9060-exec-45] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		{
			"busi_data_date":"********",
			"model_id":"6",
			"model_row_id":"86"
		}
	],
	"sysMap":{
		
	}
}
2025-07-25 01:09:52.949 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] DEBUG c.s.a.r.d.a.A.getModelInfoById - ==>  Preparing: SELECT a.id as MODEL_ID, a.show_name as MODEL_NAME, a.privname as MODEL_PRIV, b.table_name as TABLE_NAME, a.model_check_way as MODEL_CHECK_WAY, a.model_data_check_way as MODEL_DATA_CHECK_WAY, a.relating_id as RELATING_MODEL_ID, a.model_type as MODEL_TYPE FROM MC_MODEL_TB a, MC_TABLE_TB b WHERE a.table_id = b.id AND a.id = ?
2025-07-25 01:09:52.949 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] DEBUG c.s.a.r.d.a.A.getModelInfoById - ==> Parameters: 6(String)
2025-07-25 01:09:52.951 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] DEBUG c.s.a.r.d.a.A.getModelInfoById - <==      Total: 1
2025-07-25 01:09:52.952 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] INFO  c.s.a.r.s.i.a.ArmsExhibitDataServiceImpl - 模型信息：{relating_model_id=5, model_priv=ARMS_BRASEXHIBIT, model_name=1003-账户挂失、解挂交易, model_type=1, model_id=6, model_check_way=0, table_name=ZD_1003_ZHANGHGSJG, model_data_check_way=0}
2025-07-25 01:09:52.952 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] DEBUG c.s.a.r.d.a.A.getRiskWarningDataForSlip - ==>  Preparing: SELECT * FROM ZD_1003_ZHANGHGSJG WHERE MODEL_ID = ? AND MODELROW_ID = ?
2025-07-25 01:09:52.952 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] DEBUG c.s.a.r.d.a.A.getRiskWarningDataForSlip - ==> Parameters: 6(String), 86(String)
2025-07-25 01:09:52.954 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] DEBUG c.s.a.r.d.a.A.getRiskWarningDataForSlip - <==      Total: 1
2025-07-25 01:09:52.954 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] INFO  c.s.a.r.s.i.a.ArmsExhibitDataServiceImpl - 业务数据字段：[resupervise_date, acc_sitename, form_type, former_loss_acc, hz_site_name, user_no, resupervise_time, site_no, resupervise_user, id_no, model_name, balance, resupervise_content, flow_id, ishandle, relate_modelid, create_date, data_file_name, hz_site_no, alert_time, create_time, fk_site_no, operator_no, occur_date, site_name, relate_modelrowid, auth_teller_no, busi_data_date, image_state, start_dealtime, br_cas_code, tran_site_handle, error_flag, alert_user, serial_no, field_content_cn, vouh_type, model_level, resupervise_username, tx_code, alert_username, loss_type, product_code, modelrow_id, loss_account_no, zbcl_flag, occur_time, alert_content, post_vouch_state, alert_date, id_type, pre_vouch_state, tx_name, hz_teller_name, form_id, model_id, zone_no, list_flag, operator_name, data_file_id, field1, model_lock, end_dealtime, field6, customer_name, field3, field2, vouh_no, field5, field4]
2025-07-25 01:09:52.954 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/98cad41b30c13b2d] [http-nio-9060-exec-45] INFO  c.s.a.r.s.i.a.ArmsExhibitDataServiceImpl - 业务数据内容：{resupervise_date=null, acc_sitename=中国银行郫都红光支行, form_type=null, former_loss_acc=null, hz_site_name=null, user_no=admin, resupervise_time=null, site_no=14902, resupervise_user=null, id_no=null, model_name=1003-账户挂失、解挂交易, balance=null, resupervise_content=null, flow_id=*********, ishandle=0, relate_modelid=null, create_date=********, data_file_name=00002, hz_site_no=null, alert_time=********094656, create_time=null, fk_site_no=35905, operator_no=1488420, occur_date=********, site_name=中国银行郫都红光支行, relate_modelrowid=null, auth_teller_no=null, busi_data_date=null, image_state=1, start_dealtime=null, br_cas_code=null, tran_site_handle=否, error_flag=null, alert_user=admin               , serial_no=null, field_content_cn=请查表核实, vouh_type=null, model_level=1, resupervise_username=null, tx_code=037241              , alert_username=系统超级管理员                             , loss_type=null, product_code=null, modelrow_id=86, loss_account_no=6216633100000485739, zbcl_flag=null, occur_time=15:34:53            , alert_content=正常业务，无需处理, post_vouch_state=null, alert_date=********, id_type=null, pre_vouch_state=null, tx_name=领非预制卡, hz_teller_name=null, form_id=null, model_id=6, zone_no=5100, list_flag=0, operator_name=null, data_file_id=null, field1=0, model_lock=0, end_dealtime=null, field6=null, customer_name=null, field3=null, field2=null, vouh_no=null, field5=null, field4=null}
2025-07-25 01:09:53.006 [OrganNo_00023_UserNo_admin] [aef05474f20b443b/fd87aeb6a5f76e1b] [http-nio-9060-exec-45] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"flowFields":[
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"FLOW_ID",
				"ELSE_NAME":"流水号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"ACCOUNT",
				"ELSE_NAME":"借方账号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CUSTOMER_ID",
				"ELSE_NAME":"客户号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"LOAN_NO",
				"ELSE_NAME":"借据编号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CLIENT_NAME",
				"ELSE_NAME":"借方户名"
			},
			{
				"FIELD_FLOAT":"2",
				"FIELD_NAME":"AMOUNT",
				"ELSE_NAME":"交易金额"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OPP_ACCT",
				"ELSE_NAME":"贷方账号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OPP_CLIENT_NAME",
				"ELSE_NAME":"贷方户名"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CD_FLAG",
				"ELSE_NAME":"借贷标志"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"SITE_NO",
				"ELSE_NAME":"交易机构"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OPERATOR_NO",
				"ELSE_NAME":"柜员ID"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"GRANT_NO",
				"ELSE_NAME":"授权柜员ID"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OCCUR_DATE",
				"ELSE_NAME":"业务日期"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"OCCUR_TIME",
				"ELSE_NAME":"业务发生时间"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"TX_CODE",
				"ELSE_NAME":"交易代码"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CURRENCY_TYPE",
				"ELSE_NAME":"货币代号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"CHECK_FLAG",
				"ELSE_NAME":"勾对标志"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"LSERIAL_NO",
				"ELSE_NAME":"勾对图像序号"
			},
			{
				"FIELD_FLOAT":"0",
				"FIELD_NAME":"SEQ_ID",
				"ELSE_NAME":"流水序号"
			}
		],
		"modelInfo":{
			"relating_model_id":5,
			"model_priv":"ARMS_BRASEXHIBIT",
			"model_name":"1003-账户挂失、解挂交易",
			"model_type":"1",
			"model_id":6,
			"model_check_way":"0",
			"table_name":"ZD_1003_ZHANGHGSJG",
			"model_data_check_way":"0"
		},
		"businessData":{
			"acc_sitename":"中国银行郫都红光支行",
			"user_no":"admin",
			"site_no":"14902",
			"model_name":"1003-账户挂失、解挂交易",
			"flow_id":"*********",
			"ishandle":"0",
			"create_date":"********",
			"data_file_name":"00002",
			"alert_time":"********094656",
			"fk_site_no":"35905",
			"operator_no":"1488420",
			"occur_date":"********",
			"site_name":"中国银行郫都红光支行",
			"image_state":"1",
			"tran_site_handle":"否",
			"alert_user":"admin               ",
			"field_content_cn":"请查表核实",
			"model_level":"1",
			"tx_code":"037241              ",
			"alert_username":"系统超级管理员                             ",
			"modelrow_id":86,
			"loss_account_no":"6216633100000485739",
			"occur_time":"15:34:53            ",
			"alert_content":"正常业务，无需处理",
			"alert_date":"********",
			"tx_name":"领非预制卡",
			"model_id":6,
			"zone_no":"5100",
			"list_flag":"0",
			"field1":"0",
			"model_lock":"0"
		},
		"batchList":[
			{
				"batchId":"********150114621932",
				"inputDate":"********",
				"occurDate":"********",
				"operatorNo":"1488420 - 童心",
				"siteNo":"14902 - 中国银行成都实业街支行",
				"contentId":"202507_791_60D2C5A1-36CF-BF2F-5B4E-4E1ED284EEF9-1"
			}
		],
		"tableName":"ZD_1003_ZHANGHGSJG",
		"tmpDataList":[
			{
				"serialNo":"mkbANH3xsDT12bM71Y8",
				"batchId":"********150114621932",
				"inccodeinBatch":3,
				"copyInccodein":0,
				"copyRec":0,
				"isFrontPage":"0",
				"psLevel":"1",
				"primaryInccodein":0,
				"formName":"转账凭证",
				"checkFlag":"1",
				"errorFlag":"0",
				"isAudit":"0",
				"selfDelete":"0",
				"processState":"201000",
				"flowId":"*********",
				"imageSize":0,
				"backImageSize":0,
				"patchFlag":0,
				"fileName":"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-A.jpg",
				"backFileName":"00004-A89C811E-ACE4-414b-AC5D-B4203CB08B23-B.jpg",
				"ocrWorker":"admin",
				"ocrDate":"********",
				"ocrTime":"15:58:53",
				"superviseDeal":"0",
				"dataSourceId":"0",
				"insertDate":"********"
			}
		],
		"flowList":[
			{
				"amount":0.0,
				"tx_code":"021031",
				"operator_no":"1488420",
				"lserial_no":"mkbANH3xsDT12bM71Y8",
				"occur_date":"********",
				"site_no":"14902",
				"flow_id":"*********",
				"check_flag":"1",
				"seq_id":414051
			},
			{
				"amount":0.0,
				"tx_code":"001055",
				"operator_no":"1488420",
				"lserial_no":"mkbANH3xsDT12bM71Y8",
				"occur_date":"********",
				"site_no":"14902",
				"flow_id":"*********",
				"check_flag":"1",
				"seq_id":414052
			}
		]
	},
	"retMsg":"获取预警看图详情成功"
}
2025-07-25 01:09:53.068 [OrganNo_00023_UserNo_admin] [7213e21219fc9f62/d92275c275357b5c] [http-nio-9060-exec-46] INFO  c.s.cop.IF.controller.BaseController - 前台发送数据:  {
	"parameterList":[
		
	],
	"sysMap":{
		"modelRowId":86
	}
}
2025-07-25 01:09:53.070 [OrganNo_00023_UserNo_admin] [7213e21219fc9f62/31f0dce5a4c7afde] [http-nio-9060-exec-46] DEBUG c.s.a.r.d.a.A.queryByModelRowId - ==>  Preparing: SELECT modelrow_id, model_flag FROM task_mount WHERE modelrow_id = ?
2025-07-25 01:09:53.071 [OrganNo_00023_UserNo_admin] [7213e21219fc9f62/31f0dce5a4c7afde] [http-nio-9060-exec-46] DEBUG c.s.a.r.d.a.A.queryByModelRowId - ==> Parameters: 86(String)
2025-07-25 01:09:53.073 [OrganNo_00023_UserNo_admin] [7213e21219fc9f62/31f0dce5a4c7afde] [http-nio-9060-exec-46] DEBUG c.s.a.r.d.a.A.queryByModelRowId - <==      Total: 0
2025-07-25 01:09:53.084 [OrganNo_00023_UserNo_admin] [7213e21219fc9f62/d92275c275357b5c] [http-nio-9060-exec-46] INFO  c.s.cop.IF.controller.BaseController - 后台返回数据:  {
	"retCode":"200",
	"retMap":{
		"suspendInfo":{
			"isSuspended":false
		}
	},
	"retMsg":"获取挂起状态成功"
}
